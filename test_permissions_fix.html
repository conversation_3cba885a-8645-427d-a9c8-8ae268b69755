<!DOCTYPE html>
<html>
<head>
    <title>权限修复测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-panel { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .property-test { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .property-name { font-weight: bold; color: #495057; }
        .property-value { color: #28a745; }
        .property-error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>🔧 权限对象修复测试</h1>
    
    <div class="test-panel">
        <h3>📊 测试状态</h3>
        <div id="test-status" class="status info">准备开始测试...</div>
        
        <div style="margin-top: 15px;">
            <button onclick="testPermissionsObject()">测试权限对象</button>
            <button onclick="testCanCoAuthoringProperty()">测试 canCoAuthoring 属性</button>
            <button onclick="testAllProperties()">测试所有属性</button>
            <button onclick="clearTestLog()">清除日志</button>
        </div>
    </div>
    
    <div class="test-panel">
        <h3>📝 测试结果</h3>
        <div id="test-results"></div>
    </div>
    
    <div class="test-panel">
        <h3>📋 测试日志</h3>
        <div id="test-log" class="log-area"></div>
    </div>

    <script>
        // 测试日志系统
        const testLog = {
            container: null,
            
            init() {
                this.container = document.getElementById('test-log');
                this.log('测试系统初始化完成', 'info');
            },
            
            log(message, type = 'info') {
                if (!this.container) return;
                
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.style.marginBottom = '2px';
                
                let color = '#000';
                let prefix = '[INFO]';
                
                switch(type) {
                    case 'success':
                        color = '#28a745';
                        prefix = '[SUCCESS]';
                        break;
                    case 'error':
                        color = '#dc3545';
                        prefix = '[ERROR]';
                        break;
                    case 'warning':
                        color = '#ffc107';
                        prefix = '[WARN]';
                        break;
                }
                
                logEntry.innerHTML = `<span style="color: #6c757d;">${timestamp}</span> <span style="color: ${color}; font-weight: bold;">${prefix}</span> ${message}`;
                this.container.appendChild(logEntry);
                this.container.scrollTop = this.container.scrollHeight;
                
                console.log(`${prefix} ${message}`);
            }
        };
        
        // 更新测试状态
        function updateTestStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            testLog.log(`状态: ${message}`, type);
        }
        
        // 显示测试结果
        function showTestResult(propertyName, value, success) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'property-test';
            
            const nameSpan = document.createElement('span');
            nameSpan.className = 'property-name';
            nameSpan.textContent = propertyName + ': ';
            
            const valueSpan = document.createElement('span');
            valueSpan.className = success ? 'property-value' : 'property-error';
            valueSpan.textContent = value;
            
            resultDiv.appendChild(nameSpan);
            resultDiv.appendChild(valueSpan);
            resultsDiv.appendChild(resultDiv);
        }
        
        // 测试权限对象创建
        function testPermissionsObject() {
            testLog.log('开始测试权限对象创建...', 'info');
            updateTestStatus('正在测试权限对象...', 'info');
            
            try {
                if (!window.AscCommon || !window.AscCommon.asc_CAscEditorPermissions) {
                    throw new Error('AscCommon.asc_CAscEditorPermissions 类不可用');
                }
                
                const permissions = new window.AscCommon.asc_CAscEditorPermissions();
                testLog.log('✓ 权限对象创建成功', 'success');
                
                // 测试基本属性
                showTestResult('对象类型', typeof permissions, true);
                showTestResult('构造函数', permissions.constructor.name, true);
                
                updateTestStatus('权限对象创建成功', 'success');
                return permissions;
                
            } catch (error) {
                testLog.log(`✗ 权限对象创建失败: ${error.message}`, 'error');
                updateTestStatus('权限对象创建失败', 'error');
                return null;
            }
        }
        
        // 测试 canCoAuthoring 属性
        function testCanCoAuthoringProperty() {
            testLog.log('开始测试 canCoAuthoring 属性...', 'info');
            
            const permissions = testPermissionsObject();
            if (!permissions) return;
            
            try {
                // 测试属性是否存在
                const hasProperty = 'canCoAuthoring' in permissions;
                showTestResult('canCoAuthoring 属性存在', hasProperty, hasProperty);
                
                if (hasProperty) {
                    const value = permissions.canCoAuthoring;
                    showTestResult('canCoAuthoring 值', value, value === true);
                    testLog.log(`✓ canCoAuthoring = ${value}`, 'success');
                } else {
                    testLog.log('✗ canCoAuthoring 属性不存在', 'error');
                }
                
                // 测试 getter 方法
                if (typeof permissions.asc_getCanCoAuthoring === 'function') {
                    const getterValue = permissions.asc_getCanCoAuthoring();
                    showTestResult('asc_getCanCoAuthoring()', getterValue, getterValue === true);
                    testLog.log(`✓ asc_getCanCoAuthoring() = ${getterValue}`, 'success');
                } else {
                    testLog.log('✗ asc_getCanCoAuthoring 方法不存在', 'error');
                }
                
                // 测试 setter 方法
                if (typeof permissions.setCanCoAuthoring === 'function') {
                    permissions.setCanCoAuthoring(true);
                    const newValue = permissions.canCoAuthoring;
                    showTestResult('setCanCoAuthoring(true)', newValue, newValue === true);
                    testLog.log(`✓ setCanCoAuthoring 方法工作正常`, 'success');
                } else {
                    testLog.log('✗ setCanCoAuthoring 方法不存在', 'error');
                }
                
                updateTestStatus('canCoAuthoring 属性测试完成', 'success');
                
            } catch (error) {
                testLog.log(`✗ canCoAuthoring 测试失败: ${error.message}`, 'error');
                updateTestStatus('canCoAuthoring 测试失败', 'error');
            }
        }
        
        // 测试所有属性
        function testAllProperties() {
            testLog.log('开始测试所有权限属性...', 'info');
            
            const permissions = testPermissionsObject();
            if (!permissions) return;
            
            const propertiesToTest = [
                { name: 'canCoAuthoring', expected: true },
                { name: 'canEdit', expected: true },
                { name: 'canDownload', expected: true },
                { name: 'canReaderMode', expected: true },
                { name: 'canBranding', expected: false },
                { name: 'customization', expected: false },
                { name: 'isAutosaveEnable', expected: true },
                { name: 'isAnalyticsEnable', expected: false },
                { name: 'licenseType', expected: -1 }, // 默认错误状态
                { name: 'rights', expected: 0 } // 默认无权限
            ];
            
            let successCount = 0;
            let totalCount = propertiesToTest.length;
            
            propertiesToTest.forEach(prop => {
                try {
                    const hasProperty = prop.name in permissions;
                    const value = permissions[prop.name];
                    
                    if (hasProperty) {
                        showTestResult(prop.name, value, true);
                        testLog.log(`✓ ${prop.name} = ${value}`, 'success');
                        successCount++;
                    } else {
                        showTestResult(prop.name, 'undefined', false);
                        testLog.log(`✗ ${prop.name} 属性不存在`, 'error');
                    }
                } catch (error) {
                    showTestResult(prop.name, `Error: ${error.message}`, false);
                    testLog.log(`✗ ${prop.name} 测试异常: ${error.message}`, 'error');
                }
            });
            
            const successRate = (successCount / totalCount * 100).toFixed(1);
            testLog.log(`测试完成: ${successCount}/${totalCount} 成功 (${successRate}%)`, 'info');
            
            if (successCount === totalCount) {
                updateTestStatus('所有属性测试通过', 'success');
            } else if (successCount > totalCount / 2) {
                updateTestStatus(`部分属性测试通过 (${successRate}%)`, 'warning');
            } else {
                updateTestStatus('大部分属性测试失败', 'error');
            }
        }
        
        // 清除测试日志
        function clearTestLog() {
            if (testLog.container) {
                testLog.container.innerHTML = '';
                document.getElementById('test-results').innerHTML = '';
                testLog.log('测试日志已清除', 'info');
            }
        }
        
        // 页面加载完成
        window.addEventListener('load', function() {
            testLog.init();
            updateTestStatus('页面已加载，准备测试', 'info');
            
            // 检查 OnlyOffice 组件
            if (window.AscCommon) {
                testLog.log('✓ AscCommon 对象已加载', 'success');
                if (window.AscCommon.asc_CAscEditorPermissions) {
                    testLog.log('✓ asc_CAscEditorPermissions 类已加载', 'success');
                    updateTestStatus('OnlyOffice 组件已就绪，可以开始测试', 'success');
                } else {
                    testLog.log('✗ asc_CAscEditorPermissions 类未找到', 'error');
                    updateTestStatus('权限类未加载', 'error');
                }
            } else {
                testLog.log('✗ AscCommon 对象未找到', 'error');
                updateTestStatus('OnlyOffice 组件未加载', 'error');
            }
        });
        
        // 拦截控制台错误
        window.addEventListener('error', function(event) {
            testLog.log(`JavaScript错误: ${event.message} (${event.filename}:${event.lineno})`, 'error');
        });
        
        testLog.log('权限测试脚本初始化完成', 'info');
    </script>
    
    <!-- 这里应该包含修改后的 OnlyOffice JS 文件 -->
    <!-- 
    <script src="sdkjs/common/apiCommon.js"></script>
    <script src="sdkjs/common/apiBase.js"></script>
    -->
</body>
</html>
