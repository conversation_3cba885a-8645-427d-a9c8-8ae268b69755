# OnlyOffice 精简预览版

这是一个精简版的 OnlyOffice 预览系统，只包含文档预览所需的核心组件。

## 📁 目录结构

```
onlyoffice-preview/
├── sdkjs/                  # 核心 SDK
│   ├── common/            # 通用组件
│   ├── word/              # 文档编辑器核心
│   ├── cell/              # 电子表格核心
│   └── slide/             # 演示文稿核心
├── web-apps/              # Web 应用
│   ├── apps/              # 应用程序
│   │   ├── api/           # API 接口
│   │   └── viewer/        # 预览器
│   └── common/            # 共享组件
├── preview/               # 预览相关文件
│   ├── index.html         # 主页面
│   ├── preview.js         # 预览脚本
│   └── preview.css        # 预览样式
└── examples/              # 示例文件
    ├── document.docx
    ├── spreadsheet.xlsx
    └── presentation.pptx
```

## 🚀 快速开始

### 1. 基本使用

```html
<!DOCTYPE html>
<html>
<head>
    <title>文档预览</title>
    <link rel="stylesheet" href="preview/preview.css">
</head>
<body>
    <div id="preview-container"></div>
    
    <script src="sdkjs/common/apiCommon.js"></script>
    <script src="web-apps/apps/api/documents/api.js"></script>
    <script src="preview/preview.js"></script>
    
    <script>
        // 初始化预览器
        const previewer = new OnlyOfficePreview({
            container: 'preview-container',
            mode: 'view'
        });
        
        // 加载文档
        previewer.loadDocument({
            url: 'examples/document.docx',
            fileType: 'docx',
            title: '示例文档'
        });
    </script>
</body>
</html>
```

### 2. 内嵌预览

```javascript
// 创建内嵌预览
const embedPreview = new OnlyOfficePreview({
    container: 'embed-container',
    mode: 'embed',
    width: '100%',
    height: '600px',
    toolbar: false,
    statusbar: false
});
```

## ⚙️ 配置选项

### 预览模式配置

```javascript
const config = {
    // 容器设置
    container: 'preview-container',
    
    // 预览模式
    mode: 'view',  // 'view' | 'embed' | 'readonly'
    
    // 尺寸设置
    width: '100%',
    height: '600px',
    
    // UI 控制
    toolbar: false,
    statusbar: false,
    leftMenu: false,
    rightMenu: false,
    
    // 功能控制
    download: true,
    print: true,
    zoom: true,
    
    // 主题设置
    theme: 'light',  // 'light' | 'dark'
    
    // 语言设置
    lang: 'zh'
};
```

## 📋 支持的文件格式

### 文档格式
- `.docx` - Word 文档
- `.doc` - Word 97-2003 文档
- `.odt` - OpenDocument 文本
- `.rtf` - 富文本格式
- `.txt` - 纯文本

### 电子表格格式
- `.xlsx` - Excel 工作簿
- `.xls` - Excel 97-2003 工作簿
- `.ods` - OpenDocument 电子表格
- `.csv` - 逗号分隔值

### 演示文稿格式
- `.pptx` - PowerPoint 演示文稿
- `.ppt` - PowerPoint 97-2003 演示文稿
- `.odp` - OpenDocument 演示文稿

### PDF 格式
- `.pdf` - PDF 文档（只读预览）

## 🔧 自定义配置

### 1. 自定义主题

```css
/* 自定义预览主题 */
.onlyoffice-preview {
    --primary-color: #4a90e2;
    --background-color: #ffffff;
    --text-color: #333333;
    --border-color: #e1e1e1;
}

.onlyoffice-preview.dark-theme {
    --background-color: #2d2d2d;
    --text-color: #ffffff;
    --border-color: #404040;
}
```

### 2. 事件处理

```javascript
previewer.on('documentReady', function() {
    console.log('文档加载完成');
});

previewer.on('error', function(error) {
    console.error('预览错误:', error);
});

previewer.on('download', function(url) {
    console.log('下载文档:', url);
});
```

## 📦 构建和部署

### 开发环境

```bash
# 启动开发服务器
python -m http.server 8080

# 或使用 Node.js
npx http-server -p 8080
```

### 生产部署

1. 将整个 `onlyoffice-preview` 文件夹上传到服务器
2. 配置 Web 服务器（Apache/Nginx）
3. 确保支持 CORS（如果需要跨域访问）

## 🔒 安全注意事项

1. **文件上传**: 验证文件类型和大小
2. **XSS 防护**: 对用户输入进行转义
3. **CORS 配置**: 正确配置跨域访问
4. **文件访问**: 限制文件访问权限

## 📄 许可证

本项目基于 AGPL v3 许可证开源。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请查看文档或提交 Issue。
