/**
 * OnlyOffice 精简预览器
 * 专门用于文档预览的轻量级解决方案
 */

(function(window) {
    'use strict';

    /**
     * OnlyOffice 预览器类
     */
    function OnlyOfficePreview(options) {
        this.options = this._mergeOptions(options);
        this.container = null;
        this.editor = null;
        this.isReady = false;
        this.events = {};
        
        this._init();
    }

    OnlyOfficePreview.prototype = {
        /**
         * 默认配置
         */
        _defaultOptions: {
            container: 'preview-container',
            mode: 'view',
            width: '100%',
            height: '600px',
            toolbar: false,
            statusbar: false,
            leftMenu: false,
            rightMenu: false,
            download: true,
            print: true,
            zoom: true,
            theme: 'light',
            lang: 'zh',
            autoResize: true
        },

        /**
         * 合并配置选项
         */
        _mergeOptions: function(options) {
            var merged = {};
            for (var key in this._defaultOptions) {
                merged[key] = this._defaultOptions[key];
            }
            if (options) {
                for (var key in options) {
                    merged[key] = options[key];
                }
            }
            return merged;
        },

        /**
         * 初始化预览器
         */
        _init: function() {
            console.log('[OnlyOffice Preview] 初始化预览器...');
            
            this._setupContainer();
            this._loadDependencies();
            this._bindEvents();
            
            console.log('[OnlyOffice Preview] 预览器初始化完成');
        },

        /**
         * 设置容器
         */
        _setupContainer: function() {
            var containerId = this.options.container;
            this.container = typeof containerId === 'string' ? 
                document.getElementById(containerId) : containerId;
            
            if (!this.container) {
                throw new Error('预览容器未找到: ' + containerId);
            }

            // 设置容器样式
            this.container.className = 'onlyoffice-preview ' + this.options.theme + '-theme';
            this.container.style.width = this.options.width;
            this.container.style.height = this.options.height;
            this.container.style.position = 'relative';
            this.container.style.overflow = 'hidden';

            // 创建编辑器容器
            var editorContainer = document.createElement('div');
            editorContainer.id = 'preview-editor-' + Date.now();
            editorContainer.style.width = '100%';
            editorContainer.style.height = '100%';
            this.container.appendChild(editorContainer);
            
            this.editorContainerId = editorContainer.id;
        },

        /**
         * 加载依赖项
         */
        _loadDependencies: function() {
            // 检查 OnlyOffice API 是否已加载
            if (typeof window.DocsAPI === 'undefined') {
                console.warn('[OnlyOffice Preview] DocsAPI 未加载，请确保已包含 OnlyOffice API 文件');
            }
        },

        /**
         * 绑定事件
         */
        _bindEvents: function() {
            var self = this;
            
            // 窗口大小调整
            if (this.options.autoResize) {
                window.addEventListener('resize', function() {
                    self._handleResize();
                });
            }
        },

        /**
         * 处理窗口大小调整
         */
        _handleResize: function() {
            if (this.editor && typeof this.editor.Resize === 'function') {
                setTimeout(() => {
                    this.editor.Resize();
                }, 100);
            }
        },

        /**
         * 加载文档
         */
        loadDocument: function(documentConfig) {
            console.log('[OnlyOffice Preview] 加载文档:', documentConfig);
            
            if (!documentConfig || !documentConfig.url) {
                throw new Error('文档配置无效：缺少 URL');
            }

            var config = this._createEditorConfig(documentConfig);
            this._createEditor(config);
        },

        /**
         * 创建编辑器配置
         */
        _createEditorConfig: function(documentConfig) {
            return {
                document: {
                    title: documentConfig.title || '文档预览',
                    url: documentConfig.url,
                    fileType: documentConfig.fileType || this._getFileType(documentConfig.url),
                    permissions: {
                        edit: false,
                        download: this.options.download,
                        print: this.options.print,
                        review: false,
                        comment: false,
                        chat: false,
                        copy: true,
                        fillForms: false
                    }
                },
                editorConfig: {
                    mode: 'view',
                    lang: this.options.lang,
                    customization: {
                        toolbar: this.options.toolbar,
                        statusBar: this.options.statusbar,
                        leftMenu: this.options.leftMenu,
                        rightMenu: this.options.rightMenu,
                        hideRightMenu: !this.options.rightMenu,
                        autosave: false,
                        forcesave: false,
                        compactToolbar: true,
                        compactHeader: true,
                        toolbarNoTabs: true,
                        zoom: this.options.zoom ? 100 : -1,
                        help: false,
                        about: false,
                        chat: false,
                        comments: false
                    },
                    coEditing: {
                        mode: 'strict',
                        change: false
                    }
                },
                events: {
                    onAppReady: this._onAppReady.bind(this),
                    onDocumentReady: this._onDocumentReady.bind(this),
                    onError: this._onError.bind(this),
                    onWarning: this._onWarning.bind(this)
                }
            };
        },

        /**
         * 创建编辑器实例
         */
        _createEditor: function(config) {
            if (typeof window.DocsAPI === 'undefined') {
                throw new Error('OnlyOffice DocsAPI 未加载');
            }

            console.log('[OnlyOffice Preview] 创建编辑器实例');
            this.editor = new window.DocsAPI.DocEditor(this.editorContainerId, config);
        },

        /**
         * 获取文件类型
         */
        _getFileType: function(url) {
            var extension = url.split('.').pop().toLowerCase();
            var typeMap = {
                'docx': 'docx', 'doc': 'doc', 'odt': 'odt', 'rtf': 'rtf', 'txt': 'txt',
                'xlsx': 'xlsx', 'xls': 'xls', 'ods': 'ods', 'csv': 'csv',
                'pptx': 'pptx', 'ppt': 'ppt', 'odp': 'odp',
                'pdf': 'pdf'
            };
            return typeMap[extension] || 'docx';
        },

        /**
         * 应用就绪事件
         */
        _onAppReady: function() {
            console.log('[OnlyOffice Preview] 应用就绪');
            this.trigger('appReady');
        },

        /**
         * 文档就绪事件
         */
        _onDocumentReady: function() {
            console.log('[OnlyOffice Preview] 文档就绪');
            this.isReady = true;
            this.trigger('documentReady');
        },

        /**
         * 错误事件
         */
        _onError: function(event) {
            console.error('[OnlyOffice Preview] 错误:', event);
            this.trigger('error', event);
        },

        /**
         * 警告事件
         */
        _onWarning: function(event) {
            console.warn('[OnlyOffice Preview] 警告:', event);
            this.trigger('warning', event);
        },

        /**
         * 事件绑定
         */
        on: function(event, callback) {
            if (!this.events[event]) {
                this.events[event] = [];
            }
            this.events[event].push(callback);
        },

        /**
         * 触发事件
         */
        trigger: function(event) {
            var args = Array.prototype.slice.call(arguments, 1);
            if (this.events[event]) {
                this.events[event].forEach(function(callback) {
                    callback.apply(null, args);
                });
            }
        },

        /**
         * 销毁预览器
         */
        destroy: function() {
            console.log('[OnlyOffice Preview] 销毁预览器');
            
            if (this.editor && typeof this.editor.destroyEditor === 'function') {
                this.editor.destroyEditor();
            }
            
            if (this.container) {
                this.container.innerHTML = '';
            }
            
            this.editor = null;
            this.container = null;
            this.events = {};
            this.isReady = false;
        },

        /**
         * 获取编辑器实例
         */
        getEditor: function() {
            return this.editor;
        },

        /**
         * 检查是否就绪
         */
        isDocumentReady: function() {
            return this.isReady;
        }
    };

    // 导出到全局
    window.OnlyOfficePreview = OnlyOfficePreview;

    console.log('[OnlyOffice Preview] 预览器类已加载');

})(window);
