/**
 * OnlyOffice 预览器样式
 * 专门为文档预览优化的轻量级样式
 */

/* 基础样式重置 */
.onlyoffice-preview * {
    box-sizing: border-box;
}

/* 预览容器 */
.onlyoffice-preview {
    --primary-color: #4a90e2;
    --background-color: #ffffff;
    --text-color: #333333;
    --border-color: #e1e1e1;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --hover-color: #f5f5f5;
    
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

/* 暗色主题 */
.onlyoffice-preview.dark-theme {
    --background-color: #2d2d2d;
    --text-color: #ffffff;
    --border-color: #404040;
    --shadow-color: rgba(255, 255, 255, 0.1);
    --hover-color: #3a3a3a;
}

/* 编辑器容器 */
.onlyoffice-preview > div {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    outline: none !important;
}

/* 加载状态 */
.onlyoffice-preview.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-color);
}

.onlyoffice-preview.loading::before {
    content: '';
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: preview-spin 1s linear infinite;
}

@keyframes preview-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 错误状态 */
.onlyoffice-preview.error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    background-color: var(--background-color);
}

.onlyoffice-preview.error .error-icon {
    font-size: 48px;
    color: #e74c3c;
    margin-bottom: 16px;
}

.onlyoffice-preview.error .error-message {
    font-size: 16px;
    color: var(--text-color);
    margin-bottom: 8px;
}

.onlyoffice-preview.error .error-details {
    font-size: 14px;
    color: #7f8c8d;
}

/* 内嵌模式样式 */
.onlyoffice-preview.embed-mode {
    border: none;
    border-radius: 0;
}

/* 全屏模式样式 */
.onlyoffice-preview.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    border: none !important;
    border-radius: 0 !important;
}

/* 工具栏隐藏时的样式调整 */
.onlyoffice-preview .asc-window.toolbar-hidden {
    top: 0 !important;
}

/* 状态栏隐藏时的样式调整 */
.onlyoffice-preview .asc-window.statusbar-hidden {
    bottom: 0 !important;
}

/* 菜单隐藏时的样式调整 */
.onlyoffice-preview .asc-window.leftmenu-hidden {
    left: 0 !important;
}

.onlyoffice-preview .asc-window.rightmenu-hidden {
    right: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .onlyoffice-preview {
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
}

@media (max-width: 480px) {
    .onlyoffice-preview {
        border: none;
    }
    
    .onlyoffice-preview.error {
        padding: 16px;
    }
    
    .onlyoffice-preview.error .error-icon {
        font-size: 36px;
        margin-bottom: 12px;
    }
    
    .onlyoffice-preview.error .error-message {
        font-size: 14px;
    }
    
    .onlyoffice-preview.error .error-details {
        font-size: 12px;
    }
}

/* 打印样式 */
@media print {
    .onlyoffice-preview {
        border: none !important;
        box-shadow: none !important;
        background: white !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .onlyoffice-preview {
        --border-color: #000000;
        --text-color: #000000;
        --background-color: #ffffff;
    }
    
    .onlyoffice-preview.dark-theme {
        --border-color: #ffffff;
        --text-color: #ffffff;
        --background-color: #000000;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .onlyoffice-preview.loading::before {
        animation: none;
    }
    
    .onlyoffice-preview * {
        transition: none !important;
        animation: none !important;
    }
}

/* 自定义滚动条 */
.onlyoffice-preview ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.onlyoffice-preview ::-webkit-scrollbar-track {
    background: var(--background-color);
}

.onlyoffice-preview ::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

.onlyoffice-preview ::-webkit-scrollbar-thumb:hover {
    background: var(--text-color);
}

/* Firefox 滚动条 */
.onlyoffice-preview {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) var(--background-color);
}

/* 焦点样式 */
.onlyoffice-preview:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

/* 选择文本样式 */
.onlyoffice-preview ::selection {
    background-color: var(--primary-color);
    color: white;
}

/* 工具提示样式 */
.onlyoffice-preview .tooltip {
    background-color: var(--text-color);
    color: var(--background-color);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    z-index: 10000;
}

/* 加载遮罩 */
.onlyoffice-preview .loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.onlyoffice-preview.dark-theme .loading-mask {
    background-color: rgba(45, 45, 45, 0.8);
}

/* 预览特定的隐藏元素 */
.onlyoffice-preview .asc-window-toolbar,
.onlyoffice-preview .asc-window-status-bar,
.onlyoffice-preview .asc-window-left-panel,
.onlyoffice-preview .asc-window-right-panel {
    display: none !important;
}

/* 确保文档内容区域占满整个容器 */
.onlyoffice-preview .asc-window-content {
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
}
