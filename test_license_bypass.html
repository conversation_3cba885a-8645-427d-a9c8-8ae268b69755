<!DOCTYPE html>
<html>
<head>
    <title>许可证屏蔽测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-panel { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .test-result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .test-name { font-weight: bold; color: #495057; }
        .test-value { color: #28a745; }
        .test-error { color: #dc3545; }
    </style>
</head>
<body>
    <h1>🔐 许可证屏蔽测试</h1>
    
    <div class="test-panel">
        <h3>📊 测试状态</h3>
        <div id="test-status" class="status info">准备开始测试...</div>
        
        <div style="margin-top: 15px;">
            <button onclick="testLicenseConstants()">测试许可证常量</button>
            <button onclick="testMockPermissions()">测试模拟权限</button>
            <button onclick="simulateOnEditorPermissions()">模拟 onEditorPermissions</button>
            <button onclick="clearTestLog()">清除日志</button>
        </div>
    </div>
    
    <div class="test-panel">
        <h3>📝 测试结果</h3>
        <div id="test-results"></div>
    </div>
    
    <div class="test-panel">
        <h3>📋 测试日志</h3>
        <div id="test-log" class="log-area"></div>
    </div>

    <script>
        // 测试日志系统
        const testLog = {
            container: null,
            
            init() {
                this.container = document.getElementById('test-log');
                this.log('许可证测试系统初始化完成', 'info');
            },
            
            log(message, type = 'info') {
                if (!this.container) return;
                
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.style.marginBottom = '2px';
                
                let color = '#000';
                let prefix = '[INFO]';
                
                switch(type) {
                    case 'success':
                        color = '#28a745';
                        prefix = '[SUCCESS]';
                        break;
                    case 'error':
                        color = '#dc3545';
                        prefix = '[ERROR]';
                        break;
                    case 'warning':
                        color = '#ffc107';
                        prefix = '[WARN]';
                        break;
                    case 'bypass':
                        color = '#17a2b8';
                        prefix = '[BYPASS]';
                        break;
                }
                
                logEntry.innerHTML = `<span style="color: #6c757d;">${timestamp}</span> <span style="color: ${color}; font-weight: bold;">${prefix}</span> ${message}`;
                this.container.appendChild(logEntry);
                this.container.scrollTop = this.container.scrollHeight;
                
                console.log(`${prefix} ${message}`);
            }
        };
        
        // 更新测试状态
        function updateTestStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            testLog.log(`状态: ${message}`, type);
        }
        
        // 显示测试结果
        function showTestResult(testName, value, success) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result';
            
            const nameSpan = document.createElement('span');
            nameSpan.className = 'test-name';
            nameSpan.textContent = testName + ': ';
            
            const valueSpan = document.createElement('span');
            valueSpan.className = success ? 'test-value' : 'test-error';
            valueSpan.textContent = value;
            
            resultDiv.appendChild(nameSpan);
            resultDiv.appendChild(valueSpan);
            resultsDiv.appendChild(resultDiv);
        }
        
        // 测试许可证常量
        function testLicenseConstants() {
            testLog.log('开始测试许可证常量...', 'info');
            updateTestStatus('正在测试许可证常量...', 'info');
            
            try {
                // 检查 Asc 对象
                if (typeof window.Asc === 'undefined') {
                    throw new Error('Asc 对象未定义');
                }
                
                // 检查许可证结果常量
                const licenseResults = [
                    'Success', 'Error', 'Expired', 'ExpiredTrial', 'ExpiredLimited', 'SuccessLimit'
                ];
                
                let constantsFound = 0;
                licenseResults.forEach(result => {
                    const constant = `c_oLicenseResult.${result}`;
                    if (window.Asc.c_oLicenseResult && typeof window.Asc.c_oLicenseResult[result] !== 'undefined') {
                        const value = window.Asc.c_oLicenseResult[result];
                        showTestResult(constant, value, true);
                        testLog.log(`✓ ${constant} = ${value}`, 'success');
                        constantsFound++;
                    } else {
                        showTestResult(constant, 'undefined', false);
                        testLog.log(`✗ ${constant} 未定义`, 'error');
                    }
                });
                
                // 检查权限常量
                if (window.Asc.c_oRights) {
                    const rights = ['None', 'View', 'Edit', 'Review', 'Comment'];
                    rights.forEach(right => {
                        const constant = `c_oRights.${right}`;
                        if (typeof window.Asc.c_oRights[right] !== 'undefined') {
                            const value = window.Asc.c_oRights[right];
                            showTestResult(constant, value, true);
                            testLog.log(`✓ ${constant} = ${value}`, 'success');
                            constantsFound++;
                        }
                    });
                }
                
                if (constantsFound > 0) {
                    updateTestStatus(`许可证常量测试完成 (${constantsFound} 个常量)`, 'success');
                } else {
                    updateTestStatus('许可证常量测试失败', 'error');
                }
                
            } catch (error) {
                testLog.log(`✗ 许可证常量测试异常: ${error.message}`, 'error');
                updateTestStatus('许可证常量测试异常', 'error');
            }
        }
        
        // 测试模拟权限对象
        function testMockPermissions() {
            testLog.log('开始测试模拟权限对象...', 'info');
            
            try {
                // 创建模拟权限对象
                const mockPermissions = {
                    asc_getLicenseType: function() { return 0; }, // Success
                    asc_getCanCoAuthoring: function() { return true; },
                    asc_getCanBranding: function() { return true; },
                    asc_getCustomization: function() { return true; },
                    asc_getRights: function() { return 1; }, // Edit
                    asc_getBuildVersion: function() { return '1.0.0'; }
                };
                
                // 测试各个方法
                const tests = [
                    { name: 'getLicenseType', method: 'asc_getLicenseType', expected: 0 },
                    { name: 'getCanCoAuthoring', method: 'asc_getCanCoAuthoring', expected: true },
                    { name: 'getCanBranding', method: 'asc_getCanBranding', expected: true },
                    { name: 'getCustomization', method: 'asc_getCustomization', expected: true },
                    { name: 'getRights', method: 'asc_getRights', expected: 1 },
                    { name: 'getBuildVersion', method: 'asc_getBuildVersion', expected: '1.0.0' }
                ];
                
                let successCount = 0;
                tests.forEach(test => {
                    try {
                        const result = mockPermissions[test.method]();
                        const success = result === test.expected;
                        showTestResult(test.name, result, success);
                        
                        if (success) {
                            testLog.log(`✓ ${test.name} = ${result}`, 'success');
                            successCount++;
                        } else {
                            testLog.log(`✗ ${test.name} = ${result} (期望: ${test.expected})`, 'error');
                        }
                    } catch (error) {
                        showTestResult(test.name, `Error: ${error.message}`, false);
                        testLog.log(`✗ ${test.name} 测试异常: ${error.message}`, 'error');
                    }
                });
                
                const successRate = (successCount / tests.length * 100).toFixed(1);
                testLog.log(`模拟权限测试完成: ${successCount}/${tests.length} 成功 (${successRate}%)`, 'info');
                
                if (successCount === tests.length) {
                    updateTestStatus('模拟权限测试全部通过', 'success');
                } else {
                    updateTestStatus(`模拟权限测试部分通过 (${successRate}%)`, 'warning');
                }
                
            } catch (error) {
                testLog.log(`✗ 模拟权限测试异常: ${error.message}`, 'error');
                updateTestStatus('模拟权限测试异常', 'error');
            }
        }
        
        // 模拟 onEditorPermissions 调用
        function simulateOnEditorPermissions() {
            testLog.log('开始模拟 onEditorPermissions 调用...', 'info');
            
            try {
                // 模拟不同的许可证状态
                const licenseStates = [
                    { name: 'Success', value: 0 },
                    { name: 'Error', value: 1 },
                    { name: 'Expired', value: 2 },
                    { name: 'ExpiredTrial', value: 3 }
                ];
                
                licenseStates.forEach(state => {
                    testLog.log(`测试许可证状态: ${state.name} (${state.value})`, 'info');
                    
                    // 创建模拟参数对象
                    const mockParams = {
                        asc_getLicenseType: function() { return state.value; },
                        asc_getCanCoAuthoring: function() { return true; },
                        asc_getRights: function() { return 1; }
                    };
                    
                    // 模拟 onEditorPermissions 逻辑
                    const originalLicType = mockParams.asc_getLicenseType();
                    testLog.log(`原始许可证类型: ${originalLicType}`, 'info');
                    
                    // 应用屏蔽逻辑
                    mockParams.asc_getLicenseType = function() { return 0; }; // 强制成功
                    const bypassedLicType = mockParams.asc_getLicenseType();
                    
                    testLog.log(`屏蔽后许可证类型: ${bypassedLicType}`, 'bypass');
                    
                    // 验证屏蔽效果
                    if (bypassedLicType === 0) {
                        showTestResult(`${state.name} 屏蔽`, '成功', true);
                        testLog.log(`✓ ${state.name} 状态成功屏蔽为 Success`, 'success');
                    } else {
                        showTestResult(`${state.name} 屏蔽`, '失败', false);
                        testLog.log(`✗ ${state.name} 状态屏蔽失败`, 'error');
                    }
                });
                
                updateTestStatus('onEditorPermissions 模拟测试完成', 'success');
                
            } catch (error) {
                testLog.log(`✗ onEditorPermissions 模拟测试异常: ${error.message}`, 'error');
                updateTestStatus('onEditorPermissions 模拟测试异常', 'error');
            }
        }
        
        // 清除测试日志
        function clearTestLog() {
            if (testLog.container) {
                testLog.container.innerHTML = '';
                document.getElementById('test-results').innerHTML = '';
                testLog.log('测试日志已清除', 'info');
            }
        }
        
        // 拦截控制台日志
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('[AUTH BYPASS]')) {
                testLog.log(message.replace('[AUTH BYPASS]', ''), 'bypass');
            }
            originalConsoleLog.apply(console, args);
        };
        
        // 页面加载完成
        window.addEventListener('load', function() {
            testLog.init();
            updateTestStatus('页面已加载，准备测试许可证屏蔽', 'info');
            
            // 检查 OnlyOffice 组件
            if (window.Asc) {
                testLog.log('✓ Asc 对象已加载', 'success');
                if (window.Asc.c_oLicenseResult) {
                    testLog.log('✓ 许可证结果常量已加载', 'success');
                    updateTestStatus('OnlyOffice 许可证组件已就绪，可以开始测试', 'success');
                } else {
                    testLog.log('✗ 许可证结果常量未找到', 'error');
                    updateTestStatus('许可证常量未加载', 'warning');
                }
            } else {
                testLog.log('✗ Asc 对象未找到', 'error');
                updateTestStatus('OnlyOffice 组件未加载', 'warning');
            }
        });
        
        // 拦截错误
        window.addEventListener('error', function(event) {
            testLog.log(`JavaScript错误: ${event.message} (${event.filename}:${event.lineno})`, 'error');
        });
        
        testLog.log('许可证屏蔽测试脚本初始化完成', 'info');
    </script>
    
    <!-- 这里应该包含 OnlyOffice API 文件 -->
    <!-- 
    <script src="web-apps/apps/api/documents/api.js"></script>
    -->
</body>
</html>
