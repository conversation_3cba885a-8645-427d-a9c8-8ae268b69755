<!DOCTYPE html>
<html>
<head>
    <title>授权屏蔽调试页面</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .debug-panel { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .timestamp { color: #6c757d; }
        .bypass-log { color: #28a745; font-weight: bold; }
        .error-log { color: #dc3545; }
    </style>
</head>
<body>
    <h1>🔧 OnlyOffice 授权屏蔽调试</h1>
    
    <div class="debug-panel">
        <h3>📊 状态监控</h3>
        <div id="status-display" class="status info">正在初始化...</div>
        
        <div style="margin-top: 15px;">
            <button onclick="checkAuthBypass()">检查授权屏蔽</button>
            <button onclick="testOfflineMode()">测试离线模式</button>
            <button onclick="forceReload()">强制重新加载</button>
            <button onclick="clearDebugLog()">清除日志</button>
        </div>
    </div>
    
    <div class="debug-panel">
        <h3>📝 调试日志</h3>
        <div id="debug-log" class="log-area"></div>
    </div>
    
    <div class="debug-panel">
        <h3>🔍 系统信息</h3>
        <div id="system-info">
            <p><strong>用户代理:</strong> <span id="user-agent"></span></p>
            <p><strong>页面URL:</strong> <span id="page-url"></span></p>
            <p><strong>加载时间:</strong> <span id="load-time"></span></p>
        </div>
    </div>

    <script>
        // 调试日志系统
        const debugLog = {
            container: null,
            
            init() {
                this.container = document.getElementById('debug-log');
                this.log('调试系统初始化完成', 'info');
            },
            
            log(message, type = 'info') {
                if (!this.container) return;
                
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.style.marginBottom = '2px';
                
                let className = '';
                let prefix = '';
                
                switch(type) {
                    case 'bypass':
                        className = 'bypass-log';
                        prefix = '[BYPASS]';
                        break;
                    case 'error':
                        className = 'error-log';
                        prefix = '[ERROR]';
                        break;
                    case 'warning':
                        prefix = '[WARN]';
                        break;
                    default:
                        prefix = '[INFO]';
                }
                
                logEntry.innerHTML = `<span class="timestamp">${timestamp}</span> <span class="${className}">${prefix}</span> ${message}`;
                this.container.appendChild(logEntry);
                this.container.scrollTop = this.container.scrollHeight;
                
                // 同时输出到控制台
                console.log(`${prefix} ${message}`);
            }
        };
        
        // 状态更新
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            debugLog.log(`状态更新: ${message}`, type);
        }
        
        // 检查授权屏蔽
        function checkAuthBypass() {
            debugLog.log('开始检查授权屏蔽状态...', 'info');
            
            let bypassDetected = false;
            
            // 检查全局对象
            if (window.AscCommon) {
                debugLog.log('✓ AscCommon 对象已加载', 'bypass');
                bypassDetected = true;
            } else {
                debugLog.log('✗ AscCommon 对象未找到', 'error');
            }
            
            // 检查权限类
            if (window.AscCommon && window.AscCommon.asc_CAscEditorPermissions) {
                debugLog.log('✓ 权限类已加载', 'bypass');
                
                try {
                    const permissions = new window.AscCommon.asc_CAscEditorPermissions();
                    debugLog.log(`✓ 权限对象创建成功 - 许可证类型: ${permissions.asc_getLicenseType()}`, 'bypass');
                } catch (e) {
                    debugLog.log(`✗ 权限对象创建失败: ${e.message}`, 'error');
                }
            }
            
            if (bypassDetected) {
                updateStatus('授权屏蔽检测成功 - 系统运行在离线模式', 'success');
            } else {
                updateStatus('授权屏蔽检测失败 - 可能需要检查文件加载', 'error');
            }
        }
        
        // 测试离线模式
        function testOfflineMode() {
            debugLog.log('测试离线模式功能...', 'info');
            
            // 模拟权限检查
            try {
                if (window.AscCommon && window.AscCommon.asc_CAscEditorPermissions) {
                    const permissions = new window.AscCommon.asc_CAscEditorPermissions();
                    
                    // 设置完整权限
                    permissions.setLicenseType(0);
                    permissions.setCanBranding(true);
                    permissions.setCustomization(true);
                    permissions.setRights(1);
                    
                    debugLog.log('✓ 离线权限设置成功', 'bypass');
                    debugLog.log(`  - 许可证类型: ${permissions.asc_getLicenseType()}`, 'info');
                    debugLog.log(`  - 品牌权限: ${permissions.asc_getCanBranding()}`, 'info');
                    debugLog.log(`  - 自定义权限: ${permissions.asc_getCustomization()}`, 'info');
                    debugLog.log(`  - 用户权限: ${permissions.asc_getRights()}`, 'info');
                    
                    updateStatus('离线模式测试成功 - 所有权限已启用', 'success');
                } else {
                    debugLog.log('✗ 权限类不可用', 'error');
                    updateStatus('离线模式测试失败 - 权限类未加载', 'error');
                }
            } catch (error) {
                debugLog.log(`✗ 离线模式测试异常: ${error.message}`, 'error');
                updateStatus('离线模式测试异常', 'error');
            }
        }
        
        // 强制重新加载
        function forceReload() {
            debugLog.log('执行强制重新加载...', 'warning');
            setTimeout(() => {
                location.reload(true);
            }, 1000);
        }
        
        // 清除日志
        function clearDebugLog() {
            if (debugLog.container) {
                debugLog.container.innerHTML = '';
                debugLog.log('日志已清除', 'info');
            }
        }
        
        // 拦截控制台日志
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('[AUTH BYPASS]')) {
                debugLog.log(message.replace('[AUTH BYPASS]', ''), 'bypass');
            }
            originalConsoleLog.apply(console, args);
        };
        
        // 拦截错误
        window.addEventListener('error', function(event) {
            debugLog.log(`JavaScript错误: ${event.message} (${event.filename}:${event.lineno})`, 'error');
        });
        
        // 页面加载完成
        window.addEventListener('load', function() {
            debugLog.init();
            
            // 填充系统信息
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('page-url').textContent = location.href;
            document.getElementById('load-time').textContent = new Date().toLocaleString();
            
            debugLog.log('页面加载完成', 'info');
            updateStatus('页面已加载，等待OnlyOffice组件...', 'info');
            
            // 定期检查组件加载状态
            let checkCount = 0;
            const checkInterval = setInterval(() => {
                checkCount++;
                
                if (window.AscCommon) {
                    clearInterval(checkInterval);
                    debugLog.log('OnlyOffice组件检测成功', 'bypass');
                    updateStatus('OnlyOffice组件已加载，授权屏蔽生效', 'success');
                    
                    // 自动执行检查
                    setTimeout(checkAuthBypass, 500);
                } else if (checkCount > 20) { // 10秒超时
                    clearInterval(checkInterval);
                    debugLog.log('OnlyOffice组件加载超时', 'error');
                    updateStatus('OnlyOffice组件加载超时，请检查文件路径', 'error');
                }
            }, 500);
        });
        
        debugLog.log('调试脚本初始化完成', 'info');
    </script>
</body>
</html>
