/**
 * OnlyOffice 预览模式配置
 * 专门用于文档预览，屏蔽所有编辑功能和授权检查
 */

// 预览模式的完整配置
const PREVIEW_MODE_CONFIG = {
    // 文档权限配置 - 纯预览模式
    document: {
        permissions: {
            edit: false,                    // 禁用编辑
            download: true,                 // 允许下载
            print: true,                    // 允许打印
            review: false,                  // 禁用审阅
            comment: false,                 // 禁用评论
            chat: false,                    // 禁用聊天
            protect: false,                 // 禁用保护
            copy: true,                     // 允许复制
            fillForms: false,               // 禁用表单填写
            modifyFilter: false,            // 禁用过滤器修改
            modifyContentControl: false,    // 禁用内容控件修改
            reader: true                    // 启用阅读器模式
        }
    },
    
    // 编辑器配置
    editorConfig: {
        mode: 'view',                       // 明确设置为查看模式
        lang: 'zh',
        
        // 自定义配置 - 隐藏所有编辑相关UI
        customization: {
            help: false,                    // 隐藏帮助
            about: false,                   // 隐藏关于
            toolbar: false,                 // 隐藏工具栏
            leftMenu: false,                // 隐藏左侧菜单
            rightMenu: false,               // 隐藏右侧菜单
            statusBar: false,               // 隐藏状态栏
            autosave: false,                // 禁用自动保存
            forcesave: false,               // 禁用强制保存
            compactToolbar: true,           // 紧凑工具栏
            compactHeader: true,            // 紧凑标题栏
            toolbarNoTabs: true,            // 无标签工具栏
            toolbarHideFileName: false,     // 显示文件名
            hideRightMenu: true,            // 隐藏右侧菜单
            chat: false,                    // 禁用聊天
            comments: false,                // 禁用评论
            zoom: 100,                      // 默认缩放
            
            // 功能配置
            features: {
                spellcheck: {
                    change: false           // 禁用拼写检查
                }
            },
            
            // 匿名用户配置
            anonymous: {
                request: false,
                label: 'Viewer'
            },
            
            // 审阅配置
            review: {
                hideReviewDisplay: true,    // 隐藏审阅显示
                reviewDisplay: 'original',  // 显示原始文档
                showReviewChanges: false,   // 不显示审阅更改
                trackChanges: false         // 禁用跟踪更改
            },
            
            // 布局配置
            layout: {
                toolbar: false,             // 隐藏工具栏
                leftMenu: false,            // 隐藏左侧菜单
                rightMenu: false,           // 隐藏右侧菜单
                statusBar: false            // 隐藏状态栏
            }
        },
        
        // 协作编辑配置 - 离线模式
        coEditing: {
            mode: 'strict',                 // 严格模式（离线）
            change: false                   // 不允许更改协作模式
        }
    }
};

// 预览模式的事件处理器
const PREVIEW_MODE_EVENTS = {
    // 应用就绪事件
    onAppReady: function() {
        console.log('[PREVIEW MODE] OnlyOffice 预览模式已就绪');
    },
    
    // 文档就绪事件
    onDocumentReady: function() {
        console.log('[PREVIEW MODE] 文档预览已就绪');
        
        // 确保处于查看模式
        if (window.Asc && window.Asc.asc_docs_api) {
            const api = window.Asc.asc_docs_api();
            if (api && typeof api.asc_setViewMode === 'function') {
                api.asc_setViewMode(true);
                console.log('[PREVIEW MODE] 强制启用查看模式');
            }
        }
    },
    
    // 权限获取事件 - 屏蔽授权检查
    onGetEditorPermissions: function(permissions) {
        console.log('[PREVIEW MODE] 权限获取事件被触发，使用预览模式权限');
        
        // 返回预览模式权限
        return {
            asc_getLicenseType: function() { return 0; },           // 成功许可证
            asc_getCanCoAuthoring: function() { return false; },    // 禁用协作
            asc_getCanEdit: function() { return false; },           // 禁用编辑
            asc_getCanDownload: function() { return true; },        // 允许下载
            asc_getCanBranding: function() { return true; },        // 允许品牌
            asc_getCustomization: function() { return true; },      // 允许自定义
            asc_getRights: function() { return 0; },                // 查看权限
            asc_getBuildVersion: function() { return '1.0.0'; }     // 版本号
        };
    },
    
    // 错误处理
    onError: function(event) {
        console.error('[PREVIEW MODE] 预览模式错误:', event);
        
        // 如果是授权相关错误，忽略并继续
        if (event && (
            event.type === 'license' || 
            event.type === 'auth' || 
            event.message && event.message.includes('license')
        )) {
            console.log('[PREVIEW MODE] 忽略授权相关错误，继续预览模式');
            return false; // 阻止错误传播
        }
    },
    
    // 警告处理
    onWarning: function(event) {
        console.warn('[PREVIEW MODE] 预览模式警告:', event);
        
        // 如果是授权相关警告，忽略
        if (event && (
            event.type === 'license' || 
            event.type === 'auth'
        )) {
            console.log('[PREVIEW MODE] 忽略授权相关警告');
            return false; // 阻止警告显示
        }
    }
};

// 创建预览模式配置的工具函数
function createPreviewConfig(documentConfig) {
    return {
        document: {
            ...documentConfig,
            permissions: PREVIEW_MODE_CONFIG.document.permissions
        },
        editorConfig: PREVIEW_MODE_CONFIG.editorConfig,
        events: {
            ...PREVIEW_MODE_EVENTS,
            // 允许传入自定义事件处理器
            ...(documentConfig.events || {})
        }
    };
}

// 预览模式初始化函数
function initPreviewMode() {
    console.log('[PREVIEW MODE] 初始化预览模式...');
    
    // 屏蔽授权检查
    if (window.Asc && window.Asc.asc_docs_api) {
        const originalDocEditor = window.DocsAPI.DocEditor;
        
        window.DocsAPI.DocEditor = function(containerId, config) {
            console.log('[PREVIEW MODE] 拦截 DocEditor 创建，应用预览模式配置');
            
            // 应用预览模式配置
            const previewConfig = createPreviewConfig(config);
            
            // 创建编辑器实例
            return new originalDocEditor(containerId, previewConfig);
        };
        
        console.log('[PREVIEW MODE] DocEditor 已被预览模式包装');
    }
}

// 导出配置和函数
if (typeof module !== 'undefined' && module.exports) {
    // Node.js 环境
    module.exports = {
        PREVIEW_MODE_CONFIG,
        PREVIEW_MODE_EVENTS,
        createPreviewConfig,
        initPreviewMode
    };
} else {
    // 浏览器环境
    window.PreviewModeConfig = {
        PREVIEW_MODE_CONFIG,
        PREVIEW_MODE_EVENTS,
        createPreviewConfig,
        initPreviewMode
    };
}

console.log('[PREVIEW MODE] 预览模式配置已加载');
