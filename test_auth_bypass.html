<!DOCTYPE html>
<html>
<head>
    <title>OnlyOffice 授权屏蔽测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #editor { width: 100%; height: 600px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>OnlyOffice 授权屏蔽测试</h1>
    
    <div class="info">
        <h3>测试说明</h3>
        <p>此页面用于测试 OnlyOffice 编辑器的授权屏蔽功能。如果屏蔽成功，编辑器应该能够在不进行服务器授权的情况下正常加载。</p>
    </div>
    
    <div id="status-container">
        <div class="status info" id="init-status">正在初始化编辑器...</div>
    </div>
    
    <button onclick="testPermissions()">测试权限获取</button>
    <button onclick="testOfflineMode()">测试离线模式</button>
    <button onclick="clearLogs()">清除日志</button>
    
    <div id="editor"></div>
    
    <div id="logs" style="margin-top: 20px;">
        <h3>日志输出</h3>
        <div id="log-content" style="background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script>
        // 日志记录函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContent = document.getElementById('log-content');
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
            console.log(`[AUTH BYPASS TEST] ${message}`);
        }

        function clearLogs() {
            document.getElementById('log-content').innerHTML = '';
        }

        function updateStatus(message, type = 'info') {
            const statusContainer = document.getElementById('status-container');
            statusContainer.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 重写 console.log 来捕获授权屏蔽的日志
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('[AUTH BYPASS]')) {
                log(message, 'success');
            }
            originalConsoleLog.apply(console, args);
        };

        // 测试权限获取
        function testPermissions() {
            log('开始测试权限获取...');
            
            if (window.Asc && window.Asc.asc_docs_api) {
                try {
                    // 尝试获取编辑器权限
                    const api = window.Asc.asc_docs_api();
                    if (api && typeof api.asc_getEditorPermissions === 'function') {
                        log('调用 asc_getEditorPermissions()');
                        api.asc_getEditorPermissions();
                        log('权限获取调用成功', 'success');
                    } else {
                        log('API 对象或权限方法不可用', 'error');
                    }
                } catch (error) {
                    log(`权限获取失败: ${error.message}`, 'error');
                }
            } else {
                log('OnlyOffice API 未加载', 'error');
            }
        }

        // 测试离线模式
        function testOfflineMode() {
            log('测试离线模式功能...');
            
            if (window.AscCommon && window.AscCommon.asc_CAscEditorPermissions) {
                try {
                    const permissions = new window.AscCommon.asc_CAscEditorPermissions();
                    permissions.setLicenseType(0);
                    permissions.setCanBranding(true);
                    permissions.setCustomization(true);
                    permissions.setRights(1);
                    
                    log('成功创建权限对象', 'success');
                    log(`许可证类型: ${permissions.asc_getLicenseType()}`);
                    log(`品牌权限: ${permissions.asc_getCanBranding()}`);
                    log(`自定义权限: ${permissions.asc_getCustomization()}`);
                    log(`用户权限: ${permissions.asc_getRights()}`);
                } catch (error) {
                    log(`离线模式测试失败: ${error.message}`, 'error');
                }
            } else {
                log('权限类未加载', 'error');
            }
        }

        // 监听编辑器事件
        window.addEventListener('load', function() {
            log('页面加载完成');
            updateStatus('页面已加载，等待 OnlyOffice API...', 'info');
            
            // 检查 API 是否可用
            const checkAPI = setInterval(function() {
                if (window.Asc) {
                    clearInterval(checkAPI);
                    log('OnlyOffice API 已加载', 'success');
                    updateStatus('OnlyOffice API 已加载，授权屏蔽应该生效', 'success');
                    
                    // 自动测试权限
                    setTimeout(testPermissions, 1000);
                }
            }, 500);
            
            // 超时检查
            setTimeout(function() {
                if (!window.Asc) {
                    log('OnlyOffice API 加载超时', 'error');
                    updateStatus('OnlyOffice API 加载失败或超时', 'error');
                }
            }, 10000);
        });

        // 监听授权相关事件
        if (window.addEventListener) {
            window.addEventListener('message', function(event) {
                if (event.data && typeof event.data === 'string') {
                    if (event.data.includes('auth') || event.data.includes('permission')) {
                        log(`收到消息: ${event.data}`);
                    }
                }
            });
        }
    </script>
    
    <!-- 这里应该包含 OnlyOffice 的 JS 文件 -->
    <!-- 
    <script src="sdkjs/common/apiCommon.js"></script>
    <script src="sdkjs/common/apiBase.js"></script>
    <script src="sdkjs/common/docscoapi.js"></script>
    -->
    
    <script>
        log('测试页面初始化完成');
        log('请确保已包含修改后的 OnlyOffice JS 文件');
    </script>
</body>
</html>
