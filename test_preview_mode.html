<!DOCTYPE html>
<html>
<head>
    <title>预览模式测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .test-panel { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .config-display { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 4px; }
        .config-item { margin: 5px 0; }
        .config-key { font-weight: bold; color: #495057; }
        .config-value { color: #28a745; }
        .config-false { color: #dc3545; }
        #editor-container { width: 100%; height: 400px; border: 1px solid #ccc; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>📖 OnlyOffice 预览模式测试</h1>
    
    <div class="test-panel">
        <h3>📊 测试状态</h3>
        <div id="test-status" class="status info">准备开始预览模式测试...</div>
        
        <div style="margin-top: 15px;">
            <button onclick="testPreviewConfig()">测试预览配置</button>
            <button onclick="testPermissions()">测试权限设置</button>
            <button onclick="simulateDocEditor()">模拟文档编辑器</button>
            <button onclick="clearTestLog()">清除日志</button>
        </div>
    </div>
    
    <div class="test-panel">
        <h3>⚙️ 预览模式配置</h3>
        <div id="config-display" class="config-display">
            <div class="config-item">
                <span class="config-key">模式:</span> 
                <span class="config-value">view (预览模式)</span>
            </div>
            <div class="config-item">
                <span class="config-key">编辑权限:</span> 
                <span class="config-false">false (禁用)</span>
            </div>
            <div class="config-item">
                <span class="config-key">协作编辑:</span> 
                <span class="config-false">false (禁用)</span>
            </div>
            <div class="config-item">
                <span class="config-key">下载权限:</span> 
                <span class="config-value">true (允许)</span>
            </div>
            <div class="config-item">
                <span class="config-key">打印权限:</span> 
                <span class="config-value">true (允许)</span>
            </div>
            <div class="config-item">
                <span class="config-key">工具栏:</span> 
                <span class="config-false">false (隐藏)</span>
            </div>
            <div class="config-item">
                <span class="config-key">协作模式:</span> 
                <span class="config-value">strict (离线)</span>
            </div>
        </div>
    </div>
    
    <div class="test-panel">
        <h3>📋 测试日志</h3>
        <div id="test-log" class="log-area"></div>
    </div>
    
    <div class="test-panel">
        <h3>🖼️ 编辑器预览</h3>
        <div id="editor-container"></div>
    </div>

    <script src="preview_mode_config.js"></script>
    <script>
        // 测试日志系统
        const testLog = {
            container: null,
            
            init() {
                this.container = document.getElementById('test-log');
                this.log('预览模式测试系统初始化完成', 'info');
            },
            
            log(message, type = 'info') {
                if (!this.container) return;
                
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.style.marginBottom = '2px';
                
                let color = '#000';
                let prefix = '[INFO]';
                
                switch(type) {
                    case 'success':
                        color = '#28a745';
                        prefix = '[SUCCESS]';
                        break;
                    case 'error':
                        color = '#dc3545';
                        prefix = '[ERROR]';
                        break;
                    case 'warning':
                        color = '#ffc107';
                        prefix = '[WARN]';
                        break;
                    case 'preview':
                        color = '#17a2b8';
                        prefix = '[PREVIEW]';
                        break;
                }
                
                logEntry.innerHTML = `<span style="color: #6c757d;">${timestamp}</span> <span style="color: ${color}; font-weight: bold;">${prefix}</span> ${message}`;
                this.container.appendChild(logEntry);
                this.container.scrollTop = this.container.scrollHeight;
                
                console.log(`${prefix} ${message}`);
            }
        };
        
        // 更新测试状态
        function updateTestStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            testLog.log(`状态: ${message}`, type);
        }
        
        // 测试预览配置
        function testPreviewConfig() {
            testLog.log('开始测试预览模式配置...', 'preview');
            updateTestStatus('正在测试预览配置...', 'info');
            
            try {
                // 检查预览配置是否加载
                if (typeof window.PreviewModeConfig === 'undefined') {
                    throw new Error('预览模式配置未加载');
                }
                
                const config = window.PreviewModeConfig.PREVIEW_MODE_CONFIG;
                testLog.log('✓ 预览模式配置已加载', 'success');
                
                // 验证文档权限
                const permissions = config.document.permissions;
                testLog.log(`编辑权限: ${permissions.edit}`, permissions.edit ? 'error' : 'success');
                testLog.log(`下载权限: ${permissions.download}`, permissions.download ? 'success' : 'error');
                testLog.log(`协作权限: ${permissions.chat}`, permissions.chat ? 'error' : 'success');
                
                // 验证编辑器配置
                const editorConfig = config.editorConfig;
                testLog.log(`编辑器模式: ${editorConfig.mode}`, editorConfig.mode === 'view' ? 'success' : 'error');
                testLog.log(`工具栏显示: ${editorConfig.customization.toolbar}`, editorConfig.customization.toolbar ? 'error' : 'success');
                testLog.log(`协作模式: ${editorConfig.coEditing.mode}`, editorConfig.coEditing.mode === 'strict' ? 'success' : 'warning');
                
                updateTestStatus('预览配置测试完成', 'success');
                
            } catch (error) {
                testLog.log(`✗ 预览配置测试失败: ${error.message}`, 'error');
                updateTestStatus('预览配置测试失败', 'error');
            }
        }
        
        // 测试权限设置
        function testPermissions() {
            testLog.log('开始测试权限设置...', 'preview');
            
            try {
                // 创建模拟的权限对象
                const mockPermissions = window.PreviewModeConfig.PREVIEW_MODE_EVENTS.onGetEditorPermissions();
                
                testLog.log('✓ 权限对象创建成功', 'success');
                
                // 测试各项权限
                const tests = [
                    { name: '许可证类型', method: 'asc_getLicenseType', expected: 0 },
                    { name: '协作编辑', method: 'asc_getCanCoAuthoring', expected: false },
                    { name: '编辑权限', method: 'asc_getCanEdit', expected: false },
                    { name: '下载权限', method: 'asc_getCanDownload', expected: true },
                    { name: '品牌权限', method: 'asc_getCanBranding', expected: true },
                    { name: '用户权限', method: 'asc_getRights', expected: 0 }
                ];
                
                let successCount = 0;
                tests.forEach(test => {
                    try {
                        const result = mockPermissions[test.method]();
                        const success = result === test.expected;
                        
                        if (success) {
                            testLog.log(`✓ ${test.name}: ${result}`, 'success');
                            successCount++;
                        } else {
                            testLog.log(`✗ ${test.name}: ${result} (期望: ${test.expected})`, 'error');
                        }
                    } catch (error) {
                        testLog.log(`✗ ${test.name} 测试异常: ${error.message}`, 'error');
                    }
                });
                
                const successRate = (successCount / tests.length * 100).toFixed(1);
                testLog.log(`权限测试完成: ${successCount}/${tests.length} 成功 (${successRate}%)`, 'info');
                
                if (successCount === tests.length) {
                    updateTestStatus('权限测试全部通过', 'success');
                } else {
                    updateTestStatus(`权限测试部分通过 (${successRate}%)`, 'warning');
                }
                
            } catch (error) {
                testLog.log(`✗ 权限测试异常: ${error.message}`, 'error');
                updateTestStatus('权限测试异常', 'error');
            }
        }
        
        // 模拟文档编辑器
        function simulateDocEditor() {
            testLog.log('开始模拟文档编辑器创建...', 'preview');
            
            try {
                // 模拟文档配置
                const documentConfig = {
                    document: {
                        title: 'test.docx',
                        url: 'test.docx',
                        fileType: 'docx'
                    },
                    events: {
                        onAppReady: function() {
                            testLog.log('模拟编辑器应用就绪', 'preview');
                        },
                        onDocumentReady: function() {
                            testLog.log('模拟文档加载完成', 'preview');
                        }
                    }
                };
                
                // 使用预览配置创建配置
                const previewConfig = window.PreviewModeConfig.createPreviewConfig(documentConfig);
                
                testLog.log('✓ 预览配置创建成功', 'success');
                testLog.log(`文档模式: ${previewConfig.editorConfig.mode}`, 'preview');
                testLog.log(`编辑权限: ${previewConfig.document.permissions.edit}`, 'preview');
                testLog.log(`协作模式: ${previewConfig.editorConfig.coEditing.mode}`, 'preview');
                
                // 模拟事件触发
                if (previewConfig.events.onAppReady) {
                    previewConfig.events.onAppReady();
                }
                
                if (previewConfig.events.onDocumentReady) {
                    previewConfig.events.onDocumentReady();
                }
                
                updateTestStatus('文档编辑器模拟成功', 'success');
                
            } catch (error) {
                testLog.log(`✗ 文档编辑器模拟失败: ${error.message}`, 'error');
                updateTestStatus('文档编辑器模拟失败', 'error');
            }
        }
        
        // 清除测试日志
        function clearTestLog() {
            if (testLog.container) {
                testLog.container.innerHTML = '';
                testLog.log('测试日志已清除', 'info');
            }
        }
        
        // 拦截控制台日志
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            const message = args.join(' ');
            if (message.includes('[PREVIEW MODE]')) {
                testLog.log(message.replace('[PREVIEW MODE]', ''), 'preview');
            }
            originalConsoleLog.apply(console, args);
        };
        
        // 页面加载完成
        window.addEventListener('load', function() {
            testLog.init();
            updateTestStatus('预览模式测试页面已加载', 'info');
            
            // 检查预览配置
            if (window.PreviewModeConfig) {
                testLog.log('✓ 预览模式配置已加载', 'success');
                updateTestStatus('预览模式配置已就绪，可以开始测试', 'success');
                
                // 自动运行基本测试
                setTimeout(() => {
                    testPreviewConfig();
                    setTimeout(() => {
                        testPermissions();
                    }, 1000);
                }, 500);
            } else {
                testLog.log('✗ 预览模式配置未找到', 'error');
                updateTestStatus('预览模式配置加载失败', 'error');
            }
        });
        
        // 拦截错误
        window.addEventListener('error', function(event) {
            testLog.log(`JavaScript错误: ${event.message} (${event.filename}:${event.lineno})`, 'error');
        });
        
        testLog.log('预览模式测试脚本初始化完成', 'info');
    </script>
</body>
</html>
