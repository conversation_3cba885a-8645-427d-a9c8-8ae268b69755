<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OnlyOffice 简单嵌入示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .controls {
            padding: 20px;
            background: #ecf0f1;
            border-bottom: 1px solid #bdc3c7;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .viewer-container {
            height: 600px;
            border: none;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
        }
        
        .viewer-container iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .status {
            padding: 15px 20px;
            background: #d1ecf1;
            color: #0c5460;
            font-size: 14px;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>OnlyOffice 简单嵌入示例</h1>
            <p>演示如何通过 iframe 嵌入 OnlyOffice 文档预览器</p>
        </div>
        
        <div class="controls">
            <div class="form-group">
                <label for="documentUrl">文档 URL:</label>
                <input type="text" id="documentUrl" 
                       placeholder="输入文档的完整 URL，例如: https://example.com/document.docx"
                       value="https://calibre-ebook.com/downloads/demos/demo.docx">
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="documentTitle">文档标题:</label>
                    <input type="text" id="documentTitle" 
                           placeholder="文档标题" 
                           value="演示文档">
                </div>
                
                <div class="form-group">
                    <label for="viewMode">查看模式:</label>
                    <select id="viewMode">
                        <option value="view">只读预览</option>
                        <option value="edit">编辑模式</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="language">语言:</label>
                    <select id="language">
                        <option value="zh">中文</option>
                        <option value="en">English</option>
                        <option value="fr">Français</option>
                        <option value="de">Deutsch</option>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="toolbarPosition">工具栏位置:</label>
                    <select id="toolbarPosition">
                        <option value="top">顶部</option>
                        <option value="bottom">底部</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="allowDownload" checked> 允许下载
                    </label>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="allowPrint" checked> 允许打印
                    </label>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-success" onclick="loadDocument()">加载文档</button>
                <button class="btn" onclick="clearViewer()">清空</button>
            </div>
        </div>
        
        <div id="status" class="status" style="display: none;">
            准备加载文档...
        </div>
        
        <div id="viewerContainer" class="viewer-container">
            <div>
                <h3>📄 OnlyOffice 文档预览器</h3>
                <p>请在上方配置文档信息，然后点击"加载文档"按钮</p>
                <p style="font-size: 12px; color: #999;">
                    支持格式: DOCX, DOC, PDF, XLSX, XLS, PPTX, PPT 等
                </p>
            </div>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
            statusEl.style.display = 'block';
        }
        
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }
        
        function getFileType(url) {
            const extension = url.split('.').pop().toLowerCase();
            const typeMap = {
                'doc': 'doc', 'docx': 'docx', 'pdf': 'pdf',
                'xls': 'xls', 'xlsx': 'xlsx', 'csv': 'csv',
                'ppt': 'ppt', 'pptx': 'pptx',
                'odt': 'odt', 'ods': 'ods', 'odp': 'odp',
                'rtf': 'rtf', 'txt': 'txt'
            };
            return typeMap[extension] || 'docx';
        }
        
        function getEditorType(fileType) {
            const wordTypes = ['doc', 'docx', 'pdf', 'odt', 'rtf', 'txt'];
            const cellTypes = ['xls', 'xlsx', 'csv', 'ods'];
            const slideTypes = ['ppt', 'pptx', 'odp'];
            
            if (wordTypes.includes(fileType)) return 'documenteditor';
            if (cellTypes.includes(fileType)) return 'spreadsheeteditor';
            if (slideTypes.includes(fileType)) return 'presentationeditor';
            return 'documenteditor';
        }
        
        function loadDocument() {
            const documentUrl = document.getElementById('documentUrl').value.trim();
            const documentTitle = document.getElementById('documentTitle').value.trim();
            const viewMode = document.getElementById('viewMode').value;
            const language = document.getElementById('language').value;
            const toolbarPosition = document.getElementById('toolbarPosition').value;
            const allowDownload = document.getElementById('allowDownload').checked;
            const allowPrint = document.getElementById('allowPrint').checked;
            
            if (!documentUrl) {
                showStatus('请输入文档 URL', 'error');
                return;
            }
            
            try {
                new URL(documentUrl); // 验证 URL 格式
            } catch (e) {
                showStatus('请输入有效的 URL 格式', 'error');
                return;
            }
            
            showStatus('正在加载文档...', 'info');
            
            const fileType = getFileType(documentUrl);
            const editorType = getEditorType(fileType);
            
            // 构建 OnlyOffice embed URL
            const embedBaseUrl = 'http://localhost:8080/apps/' + editorType + '/embed/index.html';
            const params = new URLSearchParams({
                url: documentUrl,
                title: documentTitle || '文档预览',
                mode: viewMode,
                lang: language,
                toolbar: toolbarPosition,
                download: allowDownload.toString(),
                print: allowPrint.toString(),
                fileType: fileType
            });
            
            const embedUrl = embedBaseUrl + '?' + params.toString();
            
            // 创建 iframe
            const iframe = document.createElement('iframe');
            iframe.src = embedUrl;
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = 'none';
            iframe.title = documentTitle || '文档预览';
            
            // 监听 iframe 加载事件
            iframe.onload = function() {
                showStatus('文档加载完成', 'success');
                setTimeout(hideStatus, 3000);
            };
            
            iframe.onerror = function() {
                showStatus('文档加载失败，请检查 URL 和网络连接', 'error');
            };
            
            // 替换容器内容
            const container = document.getElementById('viewerContainer');
            container.innerHTML = '';
            container.appendChild(iframe);
            
            console.log('加载文档:', {
                url: documentUrl,
                title: documentTitle,
                mode: viewMode,
                type: fileType,
                editor: editorType,
                embedUrl: embedUrl
            });
        }
        
        function clearViewer() {
            const container = document.getElementById('viewerContainer');
            container.innerHTML = `
                <div>
                    <h3>📄 OnlyOffice 文档预览器</h3>
                    <p>请在上方配置文档信息，然后点击"加载文档"按钮</p>
                    <p style="font-size: 12px; color: #999;">
                        支持格式: DOCX, DOC, PDF, XLSX, XLS, PPTX, PPT 等
                    </p>
                </div>
            `;
            hideStatus();
        }
        
        // 监听来自 OnlyOffice 的消息
        window.addEventListener('message', function(event) {
            console.log('收到 OnlyOffice 消息:', event.data);
            
            // 这里可以处理来自 OnlyOffice 的各种事件
            if (event.data && event.data.type) {
                switch(event.data.type) {
                    case 'documentReady':
                        showStatus('文档已准备就绪', 'success');
                        break;
                    case 'error':
                        showStatus('文档错误: ' + (event.data.message || '未知错误'), 'error');
                        break;
                }
            }
        });
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            showStatus('页面加载完成，请配置文档信息', 'info');
            setTimeout(hideStatus, 3000);
        });
    </script>
</body>
</html>
