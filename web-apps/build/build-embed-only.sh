#!/bin/bash

# OnlyOffice Embed 模式专用打包脚本
# 解决 "Cannot read property 'embed' of undefined" 错误

set -e  # 遇到错误立即退出

echo "🚀 OnlyOffice Embed 模式打包脚本"
echo "=================================="

# 检查当前目录
if [ ! -f "Gruntfile.js" ]; then
    echo "❌ 错误: 请在 web-apps/build 目录下运行此脚本"
    echo "   当前目录: $(pwd)"
    echo "   正确目录: /path/to/office/web-apps/build"
    exit 1
fi

# 检查 Node.js 和 npm
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未找到 npm，请先安装 npm"
    exit 1
fi

echo "✅ 环境检查通过"

# 安装依赖
echo ""
echo "📦 安装 npm 依赖..."
if [ ! -d "node_modules" ]; then
    npm install
else
    echo "   依赖已存在，跳过安装"
fi

# 选择编辑器类型
echo ""
echo "📝 选择要打包的编辑器类型:"
echo "   1) 文档编辑器 (documenteditor) - 推荐"
echo "   2) 表格编辑器 (spreadsheeteditor)"
echo "   3) 演示文稿编辑器 (presentationeditor)"
echo "   4) 全部编辑器"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        EDITOR_TYPE="documenteditor"
        echo "✅ 选择: 文档编辑器"
        ;;
    2)
        EDITOR_TYPE="spreadsheeteditor"
        echo "✅ 选择: 表格编辑器"
        ;;
    3)
        EDITOR_TYPE="presentationeditor"
        echo "✅ 选择: 演示文稿编辑器"
        ;;
    4)
        echo "✅ 选择: 全部编辑器"
        echo ""
        echo "🔨 开始打包全部编辑器..."
        
        echo "   📄 打包文档编辑器..."
        npx grunt init-build-documenteditor deploy-app-embed
        
        echo "   📊 打包表格编辑器..."
        npx grunt init-build-spreadsheeteditor deploy-app-embed
        
        echo "   📈 打包演示文稿编辑器..."
        npx grunt init-build-presentationeditor deploy-app-embed
        
        echo ""
        echo "🎉 全部编辑器 Embed 模式打包完成！"
        echo ""
        echo "📁 输出目录:"
        echo "   - deploy/web-apps/apps/documenteditor/embed/"
        echo "   - deploy/web-apps/apps/spreadsheeteditor/embed/"
        echo "   - deploy/web-apps/apps/presentationeditor/embed/"
        exit 0
        ;;
    *)
        echo "❌ 无效选择，退出"
        exit 1
        ;;
esac

# 检查配置文件
CONFIG_FILE="${EDITOR_TYPE}.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 配置文件 $CONFIG_FILE 不存在"
    exit 1
fi

# 检查配置文件中是否包含 embed 配置
if ! grep -q '"embed"' "$CONFIG_FILE"; then
    echo "❌ 错误: 配置文件 $CONFIG_FILE 中缺少 embed 配置"
    echo "   请检查文件内容是否完整"
    exit 1
fi

echo "✅ 配置文件检查通过: $CONFIG_FILE"

# 开始打包
echo ""
echo "🔨 开始打包 $EDITOR_TYPE Embed 模式..."
echo "   步骤1: 初始化配置..."

# 初始化配置并打包
if npx grunt init-build-${EDITOR_TYPE} deploy-app-embed; then
    echo ""
    echo "🎉 打包成功完成！"
    echo ""
    echo "📁 输出目录: deploy/web-apps/apps/${EDITOR_TYPE}/embed/"
    echo ""
    echo "📋 生成的文件:"
    if [ -d "../deploy/web-apps/apps/${EDITOR_TYPE}/embed" ]; then
        ls -la "../deploy/web-apps/apps/${EDITOR_TYPE}/embed/"
    else
        echo "   ⚠️  输出目录不存在，可能打包失败"
    fi
    echo ""
    echo "🌐 测试方法:"
    echo "   cd ../deploy/web-apps"
    echo "   python -m http.server 8080"
    echo "   打开: http://localhost:8080/apps/${EDITOR_TYPE}/embed/index.html"
else
    echo ""
    echo "❌ 打包失败！"
    echo ""
    echo "🔍 常见解决方案:"
    echo "   1. 检查 Node.js 版本 (建议 14+)"
    echo "   2. 清理并重新安装依赖:"
    echo "      rm -rf node_modules package-lock.json"
    echo "      npm install"
    echo "   3. 检查磁盘空间是否充足"
    echo "   4. 使用完整打包命令:"
    echo "      npx grunt deploy-${EDITOR_TYPE}-component"
    exit 1
fi

echo ""
echo "✨ 脚本执行完成！"
