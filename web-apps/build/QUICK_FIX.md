# 快速修复: "Cannot read property 'embed' of undefined"

## 🚨 问题描述

执行 `grunt deploy-app-embed` 时出现错误：
```
Warning: Cannot read property 'embed' of undefined Use --force to continue.
```

## 🔧 快速解决方案

### 方法1: 使用正确的命令（推荐）

```bash
cd web-apps/build

# ✅ 正确的方法 - 先初始化配置
grunt init-build-documenteditor deploy-app-embed
```

### 方法2: 使用自动化脚本

```bash
cd web-apps/build

# 运行自动化脚本
./build-embed-only.sh
```

### 方法3: 使用完整打包（最安全）

```bash
cd web-apps/build

# 打包所有模式，包含 embed
grunt deploy-documenteditor-component
```

## 🔍 问题原因

`grunt deploy-app-embed` 任务依赖于 `packageFile['embed']` 配置，但是：

1. **配置文件未加载**: `packageFile` 变量为空
2. **缺少初始化步骤**: 没有运行 `init-build-*` 任务来加载配置
3. **任务依赖错误**: `embed-app-init` 直接访问配置，但配置未初始化

## 📋 完整的工作流程

```bash
# 1. 进入构建目录
cd web-apps/build

# 2. 安装依赖
npm install

# 3. 初始化配置并打包 embed
grunt init-build-documenteditor deploy-app-embed

# 4. 验证结果
ls -la ../deploy/web-apps/apps/documenteditor/embed/
```

## 🎯 各编辑器的正确命令

```bash
# 文档编辑器
grunt init-build-documenteditor deploy-app-embed

# 表格编辑器
grunt init-build-spreadsheeteditor deploy-app-embed

# 演示文稿编辑器
grunt init-build-presentationeditor deploy-app-embed
```

## 📁 预期输出

成功后会在以下目录生成文件：
```
deploy/web-apps/apps/documenteditor/embed/
├── index.html
├── index_loader.html
├── app-all.js
├── resources/
│   └── css/
│       └── app-all.css
├── locale/
└── resources/img/
```

## 🧪 测试方法

```bash
# 启动本地服务器
cd deploy/web-apps
python -m http.server 8080

# 访问测试页面
open http://localhost:8080/apps/documenteditor/embed/index.html
```

## ⚡ 一键解决脚本

如果你想要一个完全自动化的解决方案，运行：

```bash
cd web-apps/build
./build-embed-only.sh
```

这个脚本会：
- ✅ 检查环境和依赖
- ✅ 自动安装 npm 包
- ✅ 让你选择编辑器类型
- ✅ 正确执行打包命令
- ✅ 验证输出结果
- ✅ 提供测试方法

## 🔄 如果仍然失败

如果上述方法都不行，尝试完整重置：

```bash
cd web-apps/build

# 清理所有缓存
rm -rf node_modules package-lock.json
rm -rf ../deploy

# 重新安装
npm install

# 使用最安全的完整打包
grunt deploy-documenteditor-component
```

## 📞 总结

**记住**: 永远不要直接运行 `grunt deploy-app-embed`！

**正确的方式**: 
1. 先运行 `init-build-*` 初始化配置
2. 再运行 `deploy-app-embed` 进行打包

或者直接使用完整的组件打包命令 `deploy-*-component`。
