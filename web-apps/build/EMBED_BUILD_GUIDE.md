# OnlyOffice Embed 模式打包指南

## 概述

OnlyOffice 支持单独打包 embed（嵌入式）模式，这是一个轻量化的版本，专门用于第三方系统集成。

## 打包命令

### ⚠️ 重要提示

直接运行 `grunt deploy-app-embed` 会出现 "Cannot read property 'embed' of undefined" 错误。
必须先初始化配置文件！

### 1. 正确的 Embed 打包方法（推荐）

```bash
cd web-apps/build
npm install

# 文档编辑器 embed 模式（推荐）
grunt init-build-documenteditor deploy-app-embed

# 表格编辑器 embed 模式
grunt init-build-spreadsheeteditor deploy-app-embed

# 演示文稿编辑器 embed 模式
grunt init-build-presentationeditor deploy-app-embed
```

### 2. 完整打包（包含所有模式）

```bash
# 打包所有模式（main + mobile + embed + forms）
grunt deploy-documenteditor-component

# 这是最安全的方法，会自动处理所有依赖
```

### 3. 错误的方法（会失败）

```bash
# ❌ 这样会失败
grunt deploy-app-embed

# 错误信息: Cannot read property 'embed' of undefined
```

## 打包配置分析

### Embed 任务定义

在 `Gruntfile.js` 第 698 行：

```javascript
grunt.registerTask('deploy-app-embed', [
    'embed-app-init',      // 初始化 embed 配置
    'clean:prebuild',      // 清理构建前文件
    'terser',              // 压缩 JavaScript
    'less',                // 编译 LESS 样式
    'copy',                // 复制资源文件
    'clean:postbuild'      // 清理构建后文件
]);
```

### Embed 配置详情

在 `documenteditor.json` 的 `embed` 部分：

```json
{
    "embed": {
        "clean": {
            "prebuild": ["../deploy/web-apps/apps/documenteditor/embed"],
            "postbuild": ["../deploy/web-apps/apps/documenteditor/embed/resources/img"]
        },
        "js": {
            "src": [
                "../apps/common/locale.js",
                "../apps/common/Gateway.js", 
                "../apps/common/Analytics.js",
                "../apps/common/embed/lib/util/LocalStorage.js",
                "../apps/common/embed/lib/util/utils.js",
                "../apps/common/embed/lib/view/LoadMask.js",
                "../apps/common/embed/lib/view/modals.js",
                "../apps/common/embed/lib/controller/modals.js",
                "../apps/common/embed/lib/view/SearchBar.js",
                "../apps/documenteditor/embed/js/SearchBar.js",
                "../apps/documenteditor/embed/js/ApplicationView.js",
                "../apps/documenteditor/embed/js/ApplicationController.js",
                "../apps/documenteditor/embed/js/application.js"
            ],
            "dist": "../deploy/web-apps/apps/documenteditor/embed/app-all.js"
        },
        "less": {
            "files": {
                "src": "../apps/documenteditor/embed/resources/less/application.less",
                "dist": "../deploy/web-apps/apps/documenteditor/embed/resources/css/app-all.css"
            }
        },
        "copy": {
            "localization": [...],
            "index-page": {...},
            "images-app": [...]
        }
    }
}
```

## 输出文件结构

打包后的 embed 模式文件位于：

```
deploy/web-apps/apps/documenteditor/embed/
├── index.html              # 主页面
├── index_loader.html       # 加载页面
├── app-all.js             # 合并压缩的 JS 文件
├── resources/
│   └── css/
│       └── app-all.css    # 合并压缩的 CSS 文件
├── locale/                # 本地化文件
│   ├── en.json
│   ├── zh.json
│   └── ...
└── resources/
    └── img/               # 图片资源
```

## Embed 模式特点

### 1. 轻量化设计

- **文件数量少**: 所有 JS 合并为单个 `app-all.js`
- **体积小**: 移除了完整编辑器的复杂功能
- **加载快**: 减少了 HTTP 请求数量

### 2. 核心功能

- 文档预览
- 基础编辑（如果有权限）
- 搜索功能
- 缩放控制
- 简化的工具栏

### 3. 集成友好

- 独立的 HTML 页面
- 支持 iframe 嵌入
- PostMessage 通信
- 参数化配置

## 自定义 Embed 打包

### 1. 修改源文件列表

编辑 `documenteditor.json` 中的 `embed.js.src` 数组：

```json
"src": [
    "../apps/common/locale.js",
    "../apps/common/Gateway.js",
    // 添加或移除你需要的文件
    "your-custom-file.js"
]
```

### 2. 自定义样式

修改 `embed.less.files.src` 指向你的自定义样式文件：

```json
"less": {
    "files": {
        "src": "../apps/documenteditor/embed/resources/less/custom.less",
        "dist": "../deploy/web-apps/apps/documenteditor/embed/resources/css/app-all.css"
    }
}
```

### 3. 添加自定义资源

在 `embed.copy` 中添加新的复制任务：

```json
"copy": {
    "custom-assets": [
        {
            "expand": true,
            "cwd": "../your-custom-path/",
            "src": "**/*",
            "dest": "../deploy/web-apps/apps/documenteditor/embed/custom/"
        }
    ]
}
```

## 环境变量配置

可以通过环境变量自定义打包：

```bash
# 设置公司名称
export COMPANY_NAME="Your Company"

# 设置支持邮箱
export SUPPORT_EMAIL="<EMAIL>"

# 设置帮助链接
export HELP_URL="https://help.yourcompany.com"

# 执行打包
grunt deploy-app-embed
```

## 开发模式 vs 生产模式

### 开发模式

```bash
# 不压缩，保留调试信息
grunt embed-app-init copy less
```

### 生产模式

```bash
# 完整打包，包含压缩和优化
grunt deploy-app-embed
```

## 常见问题

### 1. "Cannot read property 'embed' of undefined" 错误

**问题**: 执行 `grunt deploy-app-embed` 时出现此错误

**原因**: `packageFile` 没有正确加载或缺少 `embed` 配置

**解决方案**:

#### 方法1: 先初始化配置文件
```bash
cd web-apps/build

# 必须先初始化对应的编辑器配置
grunt init-build-documenteditor deploy-app-embed

# 或者其他编辑器
grunt init-build-spreadsheeteditor deploy-app-embed
grunt init-build-presentationeditor deploy-app-embed
```

#### 方法2: 检查配置文件
确保 `documenteditor.json` 文件存在且包含 `embed` 配置：
```bash
# 检查文件是否存在
ls -la documenteditor.json

# 检查是否包含 embed 配置
grep -A 5 '"embed"' documenteditor.json
```

#### 方法3: 使用完整的任务链
```bash
# 使用完整的部署任务（推荐）
grunt deploy-documenteditor-component

# 这会自动处理配置加载和所有模式的打包
```

### 2. 打包失败

**问题**: `npm install` 失败或依赖缺失

**解决方案**:
```bash
cd web-apps/build
rm -rf node_modules package-lock.json
npm install
```

### 3. 路径错误

**问题**: 打包后文件路径不正确

**解决方案**: 检查 `documenteditor.json` 中的相对路径配置

### 4. 自定义文件未包含

**问题**: 添加的自定义文件没有被打包

**解决方案**: 确保在 `embed.js.src` 数组中正确添加了文件路径

## 验证打包结果

打包完成后，可以通过以下方式验证：

```bash
# 启动本地服务器
cd deploy/web-apps
python -m http.server 8080

# 访问 embed 页面
open http://localhost:8080/apps/documenteditor/embed/index.html
```

## 总结

通过 `grunt deploy-app-embed` 命令，您可以：

1. **单独打包** embed 模式，无需完整的编辑器
2. **自定义配置** 包含的文件和资源
3. **优化性能** 通过合并和压缩文件
4. **简化集成** 获得独立的嵌入式组件

这种方式特别适合只需要文档预览功能或轻量级编辑功能的场景。
