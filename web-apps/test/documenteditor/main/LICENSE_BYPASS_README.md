# OnlyOffice 预览器 - 授权绕过说明

## 概述

本文档说明如何在 OnlyOffice 测试版本中绕过授权检查，实现纯预览功能。

## 授权机制分析

### 1. 原始授权流程

```javascript
// 原始代码流程
api.asc_getEditorPermissions(config.licenseUrl, config.customerId);
↓
onEditorPermissions(params) {
    var licType = params.asc_getLicenseType();
    appOptions.canLicense = (licType === Asc.c_oLicenseResult.Success || 
                            licType === Asc.c_oLicenseResult.SuccessLimit);
    appOptions.isEdit = appOptions.canLicense && ... && (config.mode !== 'view');
}
```

### 2. 授权检查的关键点

- **许可证类型检查**: `params.asc_getLicenseType()`
- **许可证服务器**: `config.licenseUrl` 和 `config.customerId`
- **编辑权限**: 基于许可证结果决定是否可编辑

## 绕过方案

### 1. 修改的文件

- `web-apps/test/documenteditor/main/js/ApplicationController.js`

### 2. 具体修改

#### 2.1 绕过许可证服务器调用

```javascript
// 原始代码
api.asc_getEditorPermissions(config.licenseUrl, config.customerId);

// 修改后
// 绕过许可证检查 - 直接调用权限回调
console.log('[PREVIEW MODE] 绕过许可证服务器检查');
// api.asc_getEditorPermissions(config.licenseUrl, config.customerId);

// 直接触发权限回调，模拟成功的许可证验证
setTimeout(function() {
    onEditorPermissions({
        asc_getLicenseType: function() { return Asc.c_oLicenseResult.Success; },
        asc_getCanBranding: function() { return true; }
    });
}, 100);
```

#### 2.2 强制设置预览模式权限

```javascript
// 原始代码
function onEditorPermissions(params) {
    var licType = params.asc_getLicenseType();
    appOptions.canLicense = (licType === Asc.c_oLicenseResult.Success || 
                            licType === Asc.c_oLicenseResult.SuccessLimit);
    appOptions.canBrandingExt = params.asc_getCanBranding();
    appOptions.isEdit = appOptions.canLicense && appOptions.canBrandingExt && 
                       (permissions.edit !== false) && (config.mode !== 'view');
    
    api.asc_setViewMode(!appOptions.isEdit);
}

// 修改后
function onEditorPermissions(params) {
    // 绕过授权检查 - 直接设置为预览模式
    console.log('[PREVIEW MODE] 绕过授权检查，强制设置为预览模式');
    
    // 强制设置权限选项
    appOptions.canLicense     = true;  // 绕过许可证检查
    appOptions.canBrandingExt = true;  // 允许品牌定制
    appOptions.isEdit         = false; // 强制设置为预览模式（不可编辑）
    
    // 强制设置为预览模式
    api.asc_setViewMode(true);  // true = 预览模式，false = 编辑模式
}
```

## 许可证常量说明

### Asc.c_oLicenseResult 值

```javascript
var c_oLicenseResult = {
    Error         : 1,    // 错误
    Expired       : 2,    // 过期
    Success       : 3,    // 成功 ✓
    UnknownUser   : 4,    // 未知用户
    Connections   : 5,    // 连接数限制
    ExpiredTrial  : 6,    // 试用过期
    SuccessLimit  : 7,    // 成功但有限制 ✓
    UsersCount    : 8,    // 用户数限制
    // ... 更多类型
};
```

我们使用 `Success` (3) 来模拟成功的许可证验证。

## 使用方法

### 1. 直接使用修改后的测试版本

```bash
# 启动本地服务器
cd web-apps/test/documenteditor/main/
python -m http.server 8080

# 访问
http://localhost:8080/preview_test.html
```

### 2. 集成到现有项目

```javascript
// 在你的项目中使用修改后的 ApplicationController.js
// 确保设置正确的文档配置

var docConfig = {
    url: 'your-document-url',
    title: 'Document Title',
    fileType: 'docx',
    key: 'unique-document-key'
};

var config = {
    mode: 'view',  // 强制预览模式
    // 不需要 licenseUrl 和 customerId
};
```

## 注意事项

### 1. 法律风险

- 此修改绕过了 OnlyOffice 的商业授权机制
- 仅建议用于学习、测试或开发环境
- 生产环境请购买正版授权

### 2. 功能限制

- 只支持预览功能，无法编辑
- 可能缺少某些高级功能
- 不支持协作编辑

### 3. 技术限制

- 基于测试版本，可能不够稳定
- 需要完整的 OnlyOffice SDK 支持
- 某些文档格式可能不完全支持

## 完整的集成示例

参考 `preview_test.html` 文件，它展示了如何：

1. 创建简洁的预览界面
2. 集成修改后的控制器
3. 处理文档加载逻辑
4. 提供用户友好的操作界面

## 总结

通过以上修改，我们成功绕过了 OnlyOffice 的授权检查，实现了：

- ✅ 无需许可证服务器
- ✅ 无需客户ID验证  
- ✅ 强制预览模式
- ✅ 保留核心预览功能
- ✅ 代码简洁易维护

这为只需要文档预览功能的项目提供了一个轻量级的解决方案。
