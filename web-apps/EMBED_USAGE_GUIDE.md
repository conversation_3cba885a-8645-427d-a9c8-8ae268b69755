# OnlyOffice 内嵌模式使用指南

## 概述

OnlyOffice 内嵌模式是一个轻量化的文档预览和编辑组件，专门设计用于集成到第三方网站或应用程序中。

## 🚀 快速开始

### 1. 基本的 iframe 嵌入

```html
<!DOCTYPE html>
<html>
<head>
    <title>文档预览</title>
</head>
<body>
    <h1>我的文档预览系统</h1>
    
    <!-- 嵌入 OnlyOffice -->
    <iframe 
        src="http://your-server/apps/documenteditor/embed/index.html?url=document-url&mode=view"
        width="100%" 
        height="600px" 
        frameborder="0">
    </iframe>
</body>
</html>
```

### 2. 使用 PostMessage 通信

```html
<!DOCTYPE html>
<html>
<head>
    <title>高级文档预览</title>
</head>
<body>
    <div id="editor-container">
        <iframe 
            id="onlyoffice-editor"
            src="http://your-server/apps/documenteditor/embed/index.html"
            width="100%" 
            height="600px" 
            frameborder="0">
        </iframe>
    </div>

    <script>
        // 监听来自 OnlyOffice 的消息
        window.addEventListener('message', function(event) {
            if (event.origin !== 'http://your-server') return;
            
            console.log('收到消息:', event.data);
            
            switch(event.data.type) {
                case 'documentReady':
                    console.log('文档加载完成');
                    break;
                case 'error':
                    console.error('文档错误:', event.data.message);
                    break;
                case 'requestClose':
                    console.log('请求关闭文档');
                    break;
            }
        });

        // 向 OnlyOffice 发送消息
        function sendToEditor(command, data) {
            var iframe = document.getElementById('onlyoffice-editor');
            iframe.contentWindow.postMessage({
                command: command,
                data: data
            }, 'http://your-server');
        }

        // 示例：初始化文档
        setTimeout(function() {
            sendToEditor('init', {
                config: {
                    document: {
                        title: '我的文档',
                        url: 'https://example.com/document.docx',
                        fileType: 'docx',
                        permissions: {
                            edit: false,  // 只读模式
                            download: true,
                            print: true
                        }
                    },
                    embedded: {
                        toolbarDocked: 'top',
                        saveUrl: 'https://your-api/save',
                        shareUrl: 'https://your-api/share'
                    }
                }
            });
        }, 1000);
    </script>
</body>
</html>
```

## 📋 配置参数

### 文档配置 (document)

```javascript
document: {
    title: '文档标题',
    url: 'https://example.com/document.docx',  // 文档下载链接
    fileType: 'docx',  // 文件类型
    key: 'unique-document-key',  // 唯一标识
    permissions: {
        edit: false,      // 是否可编辑
        download: true,   // 是否可下载
        print: true,      // 是否可打印
        comment: false,   // 是否可评论
        fillForms: false, // 是否可填写表单
        modifyFilter: false, // 是否可修改过滤器
        modifyContentControl: false, // 是否可修改内容控件
        review: false     // 是否可审阅
    }
}
```

### 嵌入配置 (embedded)

```javascript
embedded: {
    toolbarDocked: 'top',        // 工具栏位置: 'top' | 'bottom'
    saveUrl: '/api/save',        // 保存接口
    shareUrl: '/api/share',      // 分享接口
    embedUrl: '/api/embed',      // 嵌入接口
    downloadUrl: '/api/download', // 下载接口
    fullscreenUrl: '/fullscreen', // 全屏链接
    
    // 自定义按钮
    toolbar: {
        file: {
            close: true,    // 显示关闭按钮
            save: false,    // 显示保存按钮
            print: true     // 显示打印按钮
        }
    }
}
```

### 自定义配置 (customization)

```javascript
customization: {
    goback: {
        url: 'https://your-site.com',  // 返回链接
        text: '返回网站',               // 返回按钮文字
        blank: false                   // 是否新窗口打开
    },
    logo: {
        image: 'https://your-site.com/logo.png',
        imageEmbedded: 'https://your-site.com/logo-small.png',
        url: 'https://your-site.com'
    },
    customer: {
        name: '您的公司名称',
        address: '公司地址',
        mail: '<EMAIL>',
        www: 'https://your-company.com'
    }
}
```

## 🔧 高级集成示例

### 1. React 组件

```jsx
import React, { useEffect, useRef } from 'react';

const OnlyOfficeViewer = ({ documentUrl, title, readonly = true }) => {
    const iframeRef = useRef(null);

    useEffect(() => {
        const handleMessage = (event) => {
            if (event.origin !== 'http://your-onlyoffice-server') return;
            
            console.log('OnlyOffice 消息:', event.data);
        };

        window.addEventListener('message', handleMessage);
        
        // 初始化文档
        setTimeout(() => {
            if (iframeRef.current) {
                iframeRef.current.contentWindow.postMessage({
                    command: 'init',
                    data: {
                        config: {
                            document: {
                                title: title,
                                url: documentUrl,
                                fileType: documentUrl.split('.').pop(),
                                permissions: {
                                    edit: !readonly,
                                    download: true,
                                    print: true
                                }
                            }
                        }
                    }
                }, 'http://your-onlyoffice-server');
            }
        }, 1000);

        return () => {
            window.removeEventListener('message', handleMessage);
        };
    }, [documentUrl, title, readonly]);

    return (
        <iframe
            ref={iframeRef}
            src="http://your-onlyoffice-server/apps/documenteditor/embed/index.html"
            width="100%"
            height="600px"
            frameBorder="0"
            title="OnlyOffice Document Viewer"
        />
    );
};

export default OnlyOfficeViewer;
```

### 2. Vue 组件

```vue
<template>
    <iframe
        ref="onlyofficeFrame"
        :src="embedUrl"
        width="100%"
        height="600px"
        frameborder="0"
        @load="onIframeLoad"
    />
</template>

<script>
export default {
    name: 'OnlyOfficeEmbed',
    props: {
        documentUrl: String,
        title: String,
        readonly: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            embedUrl: 'http://your-onlyoffice-server/apps/documenteditor/embed/index.html'
        };
    },
    mounted() {
        window.addEventListener('message', this.handleMessage);
    },
    beforeDestroy() {
        window.removeEventListener('message', this.handleMessage);
    },
    methods: {
        handleMessage(event) {
            if (event.origin !== 'http://your-onlyoffice-server') return;
            console.log('OnlyOffice 消息:', event.data);
        },
        onIframeLoad() {
            setTimeout(() => {
                this.initDocument();
            }, 1000);
        },
        initDocument() {
            const config = {
                document: {
                    title: this.title,
                    url: this.documentUrl,
                    fileType: this.documentUrl.split('.').pop(),
                    permissions: {
                        edit: !this.readonly,
                        download: true,
                        print: true
                    }
                }
            };
            
            this.$refs.onlyofficeFrame.contentWindow.postMessage({
                command: 'init',
                data: { config }
            }, 'http://your-onlyoffice-server');
        }
    }
};
</script>
```

## 🌐 URL 参数方式（简单集成）

如果不想使用 PostMessage，可以直接通过 URL 参数传递配置：

```
http://your-server/apps/documenteditor/embed/index.html?
    url=https://example.com/document.docx&
    title=我的文档&
    mode=view&
    lang=zh&
    toolbar=top&
    download=true&
    print=true
```

支持的 URL 参数：
- `url`: 文档链接
- `title`: 文档标题  
- `mode`: 模式 (`view` | `edit`)
- `lang`: 语言 (`zh` | `en` | `fr` 等)
- `toolbar`: 工具栏位置 (`top` | `bottom`)
- `download`: 是否可下载 (`true` | `false`)
- `print`: 是否可打印 (`true` | `false`)

## 📱 响应式设计

```css
.document-viewer {
    width: 100%;
    height: 80vh;
    min-height: 400px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .document-viewer {
        height: 60vh;
        min-height: 300px;
    }
}
```

## 🔒 安全考虑

1. **域名白名单**: 确保只接受来自可信域名的消息
2. **文档权限**: 合理设置文档的编辑、下载权限
3. **HTTPS**: 生产环境使用 HTTPS
4. **CSP 策略**: 配置内容安全策略

```javascript
// 安全的消息处理
window.addEventListener('message', function(event) {
    // 检查来源域名
    const allowedOrigins = ['https://your-onlyoffice-server.com'];
    if (!allowedOrigins.includes(event.origin)) {
        console.warn('拒绝来自未知域名的消息:', event.origin);
        return;
    }
    
    // 处理消息
    handleOnlyOfficeMessage(event.data);
});
```

这样您就可以轻松地将 OnlyOffice 内嵌到您的网站或应用程序中了！
