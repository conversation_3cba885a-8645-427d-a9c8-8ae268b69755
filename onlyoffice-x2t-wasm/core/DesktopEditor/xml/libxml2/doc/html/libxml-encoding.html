<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module encoding from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module encoding from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-dict.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-dict.html">dict</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-entities.html">entities</a></th><td><a accesskey="n" href="libxml-entities.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>interface for the encoding conversion functions needed for XML basic encoding and iconv() support.  Related specs are rfc2044        (UTF-8 and UTF-16) F. Yergeau Alis Technologies [ISO-10646]    UTF-8 and UTF-16 in Annexes [ISO-8859-1]   ISO Latin-1 characters codes. [UNICODE]      The Unicode Consortium, "The Unicode Standard -- Worldwide Character Encoding -- Version 1.0", Addison- Wesley, Volume 1, 1991, Volume 2, 1992.  UTF-8 is described in Unicode Technical Report #4. [US-ASCII]     Coded Character Set--7-bit American Standard Code for Information Interchange, ANSI X3.4-1986. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#uconv_t">uconv_t</a><br />struct _uconv_t
</pre><pre class="programlisting">Enum <a href="#xmlCharEncoding">xmlCharEncoding</a>
</pre><pre class="programlisting">Structure <a href="#xmlCharEncodingHandler">xmlCharEncodingHandler</a><br />struct _xmlCharEncodingHandler
</pre><pre class="programlisting">Typedef <a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * <a name="xmlCharEncodingHandlerPtr" id="xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>
</pre><pre class="programlisting">int	<a href="#UTF8Toisolat1">UTF8Toisolat1</a>			(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen)</pre>
<pre class="programlisting">int	<a href="#isolat1ToUTF8">isolat1ToUTF8</a>			(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen)</pre>
<pre class="programlisting">int	<a href="#xmlAddEncodingAlias">xmlAddEncodingAlias</a>		(const char * name, <br />					 const char * alias)</pre>
<pre class="programlisting">int	<a href="#xmlCharEncCloseFunc">xmlCharEncCloseFunc</a>		(<a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler)</pre>
<pre class="programlisting">int	<a href="#xmlCharEncFirstLine">xmlCharEncFirstLine</a>		(<a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)</pre>
<pre class="programlisting">int	<a href="#xmlCharEncInFunc">xmlCharEncInFunc</a>		(<a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)</pre>
<pre class="programlisting">int	<a href="#xmlCharEncOutFunc">xmlCharEncOutFunc</a>		(<a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)</pre>
<pre class="programlisting">Function type: <a href="#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a>
int	<a href="#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a>	(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen)
</pre>
<pre class="programlisting">Function type: <a href="#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a>
int	<a href="#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a>	(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen)
</pre>
<pre class="programlisting">void	<a href="#xmlCleanupCharEncodingHandlers">xmlCleanupCharEncodingHandlers</a>	(void)</pre>
<pre class="programlisting">void	<a href="#xmlCleanupEncodingAliases">xmlCleanupEncodingAliases</a>	(void)</pre>
<pre class="programlisting">int	<a href="#xmlDelEncodingAlias">xmlDelEncodingAlias</a>		(const char * alias)</pre>
<pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a>	<a href="#xmlDetectCharEncoding">xmlDetectCharEncoding</a>	(const unsigned char * in, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	<a href="#xmlFindCharEncodingHandler">xmlFindCharEncodingHandler</a>	(const char * name)</pre>
<pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	<a href="#xmlGetCharEncodingHandler">xmlGetCharEncodingHandler</a>	(<a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)</pre>
<pre class="programlisting">const char *	<a href="#xmlGetCharEncodingName">xmlGetCharEncodingName</a>	(<a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)</pre>
<pre class="programlisting">const char *	<a href="#xmlGetEncodingAlias">xmlGetEncodingAlias</a>	(const char * alias)</pre>
<pre class="programlisting">void	<a href="#xmlInitCharEncodingHandlers">xmlInitCharEncodingHandlers</a>	(void)</pre>
<pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	<a href="#xmlNewCharEncodingHandler">xmlNewCharEncodingHandler</a>	(const char * name, <br />							 <a href="libxml-encoding.html#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a> input, <br />							 <a href="libxml-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> output)</pre>
<pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a>	<a href="#xmlParseCharEncoding">xmlParseCharEncoding</a>	(const char * name)</pre>
<pre class="programlisting">void	<a href="#xmlRegisterCharEncodingHandler">xmlRegisterCharEncodingHandler</a>	(<a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler)</pre>
<h2>Description</h2>
<h3><a name="uconv_t" id="uconv_t">Structure uconv_t</a></h3><pre class="programlisting">Structure uconv_t<br />struct _uconv_t {
    UConverter *	uconv	: for conversion between an encoding and
    UConverter *	utf8	: for conversion between UTF-8 and UTF-16
}</pre><h3>Enum <a name="xmlCharEncoding" id="xmlCharEncoding">xmlCharEncoding</a></h3><pre class="programlisting">Enum xmlCharEncoding {
    <a name="XML_CHAR_ENCODING_ERROR" id="XML_CHAR_ENCODING_ERROR">XML_CHAR_ENCODING_ERROR</a> = -1 : No char encoding detected
    <a name="XML_CHAR_ENCODING_NONE" id="XML_CHAR_ENCODING_NONE">XML_CHAR_ENCODING_NONE</a> = 0 : No char encoding detected
    <a name="XML_CHAR_ENCODING_UTF8" id="XML_CHAR_ENCODING_UTF8">XML_CHAR_ENCODING_UTF8</a> = 1 : UTF-8
    <a name="XML_CHAR_ENCODING_UTF16LE" id="XML_CHAR_ENCODING_UTF16LE">XML_CHAR_ENCODING_UTF16LE</a> = 2 : UTF-16 little endian
    <a name="XML_CHAR_ENCODING_UTF16BE" id="XML_CHAR_ENCODING_UTF16BE">XML_CHAR_ENCODING_UTF16BE</a> = 3 : UTF-16 big endian
    <a name="XML_CHAR_ENCODING_UCS4LE" id="XML_CHAR_ENCODING_UCS4LE">XML_CHAR_ENCODING_UCS4LE</a> = 4 : UCS-4 little endian
    <a name="XML_CHAR_ENCODING_UCS4BE" id="XML_CHAR_ENCODING_UCS4BE">XML_CHAR_ENCODING_UCS4BE</a> = 5 : UCS-4 big endian
    <a name="XML_CHAR_ENCODING_EBCDIC" id="XML_CHAR_ENCODING_EBCDIC">XML_CHAR_ENCODING_EBCDIC</a> = 6 : EBCDIC uh!
    <a name="XML_CHAR_ENCODING_UCS4_2143" id="XML_CHAR_ENCODING_UCS4_2143">XML_CHAR_ENCODING_UCS4_2143</a> = 7 : UCS-4 unusual ordering
    <a name="XML_CHAR_ENCODING_UCS4_3412" id="XML_CHAR_ENCODING_UCS4_3412">XML_CHAR_ENCODING_UCS4_3412</a> = 8 : UCS-4 unusual ordering
    <a name="XML_CHAR_ENCODING_UCS2" id="XML_CHAR_ENCODING_UCS2">XML_CHAR_ENCODING_UCS2</a> = 9 : UCS-2
    <a name="XML_CHAR_ENCODING_8859_1" id="XML_CHAR_ENCODING_8859_1">XML_CHAR_ENCODING_8859_1</a> = 10 : ISO-8859-1 ISO Latin 1
    <a name="XML_CHAR_ENCODING_8859_2" id="XML_CHAR_ENCODING_8859_2">XML_CHAR_ENCODING_8859_2</a> = 11 : ISO-8859-2 ISO Latin 2
    <a name="XML_CHAR_ENCODING_8859_3" id="XML_CHAR_ENCODING_8859_3">XML_CHAR_ENCODING_8859_3</a> = 12 : ISO-8859-3
    <a name="XML_CHAR_ENCODING_8859_4" id="XML_CHAR_ENCODING_8859_4">XML_CHAR_ENCODING_8859_4</a> = 13 : ISO-8859-4
    <a name="XML_CHAR_ENCODING_8859_5" id="XML_CHAR_ENCODING_8859_5">XML_CHAR_ENCODING_8859_5</a> = 14 : ISO-8859-5
    <a name="XML_CHAR_ENCODING_8859_6" id="XML_CHAR_ENCODING_8859_6">XML_CHAR_ENCODING_8859_6</a> = 15 : ISO-8859-6
    <a name="XML_CHAR_ENCODING_8859_7" id="XML_CHAR_ENCODING_8859_7">XML_CHAR_ENCODING_8859_7</a> = 16 : ISO-8859-7
    <a name="XML_CHAR_ENCODING_8859_8" id="XML_CHAR_ENCODING_8859_8">XML_CHAR_ENCODING_8859_8</a> = 17 : ISO-8859-8
    <a name="XML_CHAR_ENCODING_8859_9" id="XML_CHAR_ENCODING_8859_9">XML_CHAR_ENCODING_8859_9</a> = 18 : ISO-8859-9
    <a name="XML_CHAR_ENCODING_2022_JP" id="XML_CHAR_ENCODING_2022_JP">XML_CHAR_ENCODING_2022_JP</a> = 19 : ISO-2022-JP
    <a name="XML_CHAR_ENCODING_SHIFT_JIS" id="XML_CHAR_ENCODING_SHIFT_JIS">XML_CHAR_ENCODING_SHIFT_JIS</a> = 20 : Shift_JIS
    <a name="XML_CHAR_ENCODING_EUC_JP" id="XML_CHAR_ENCODING_EUC_JP">XML_CHAR_ENCODING_EUC_JP</a> = 21 : EUC-JP
    <a name="XML_CHAR_ENCODING_ASCII" id="XML_CHAR_ENCODING_ASCII">XML_CHAR_ENCODING_ASCII</a> = 22 : pure ASCII
}
</pre><h3><a name="xmlCharEncodingHandler" id="xmlCharEncodingHandler">Structure xmlCharEncodingHandler</a></h3><pre class="programlisting">Structure xmlCharEncodingHandler<br />struct _xmlCharEncodingHandler {
    char *	name
    <a href="libxml-encoding.html#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a>	input
    <a href="libxml-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a>	output
    iconv_t	iconv_in
    iconv_t	iconv_out
    <a href="libxml-encoding.html#uconv_t">uconv_t</a> *	uconv_in
    <a href="libxml-encoding.html#uconv_t">uconv_t</a> *	uconv_out
}</pre><h3><a name="UTF8Toisolat1" id="UTF8Toisolat1"></a>Function: UTF8Toisolat1</h3><pre class="programlisting">int	UTF8Toisolat1			(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen)<br />
</pre><p>Take a block of UTF-8 chars in and try to convert it to an ISO Latin 1 block of chars out.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of UTF-8 chars</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written if success, -2 if the transcoding fails, or -1 otherwise The value of @inlen after return is the number of octets consumed if the return value is positive, else unpredictable. The value of @outlen after return is the number of octets consumed.</td></tr></tbody></table></div><h3><a name="isolat1ToUTF8" id="isolat1ToUTF8"></a>Function: isolat1ToUTF8</h3><pre class="programlisting">int	isolat1ToUTF8			(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen)<br />
</pre><p>Take a block of ISO Latin 1 chars in and try to convert it to an UTF-8 block of chars out.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of ISO Latin 1 chars</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written if success, or -1 otherwise The value of @inlen after return is the number of octets consumed if the return value is positive, else unpredictable. The value of @outlen after return is the number of octets consumed.</td></tr></tbody></table></div><h3><a name="xmlAddEncodingAlias" id="xmlAddEncodingAlias"></a>Function: xmlAddEncodingAlias</h3><pre class="programlisting">int	xmlAddEncodingAlias		(const char * name, <br />					 const char * alias)<br />
</pre><p>Registers an alias @alias for an encoding named @name. Existing alias will be overwritten.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the encoding name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>alias</tt></i>:</span></td><td>the alias name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlCharEncCloseFunc" id="xmlCharEncCloseFunc"></a>Function: xmlCharEncCloseFunc</h3><pre class="programlisting">int	xmlCharEncCloseFunc		(<a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler)<br />
</pre><p>Generic front-end for encoding handler close function</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>char enconding transformation data structure</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if success, or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlCharEncFirstLine" id="xmlCharEncFirstLine"></a>Function: xmlCharEncFirstLine</h3><pre class="programlisting">int	xmlCharEncFirstLine		(<a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)<br />
</pre><p>Front-end for the encoding handler input function, but handle only the very first line, i.e. limit itself to 45 chars.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>char enconding transformation data structure</td></tr><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> for the output.</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> for the input</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written if success, or -1 general error -2 if the transcoding fails (for *in is not valid utf8 string or the result of transformation can't fit into the encoding we want), or</td></tr></tbody></table></div><h3><a name="xmlCharEncInFunc" id="xmlCharEncInFunc"></a>Function: xmlCharEncInFunc</h3><pre class="programlisting">int	xmlCharEncInFunc		(<a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)<br />
</pre><p>Generic front-end for the encoding handler input function</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>char encoding transformation data structure</td></tr><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> for the output.</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> for the input</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written if success, or -1 general error -2 if the transcoding fails (for *in is not valid utf8 string or the result of transformation can't fit into the encoding we want), or</td></tr></tbody></table></div><h3><a name="xmlCharEncOutFunc" id="xmlCharEncOutFunc"></a>Function: xmlCharEncOutFunc</h3><pre class="programlisting">int	xmlCharEncOutFunc		(<a href="libxml-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)<br />
</pre><p>Generic front-end for the encoding handler output function a first call with @in == NULL has to be made firs to initiate the output in case of non-stateless encoding needing to initiate their state or the output (like the BOM in UTF16). In case of UTF8 sequence conversion errors for the given encoder, the content will be automatically remapped to a CharRef sequence.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>char enconding transformation data structure</td></tr><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> for the output.</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> for the input</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written if success, or -1 general error -2 if the transcoding fails (for *in is not valid utf8 string or the result of transformation can't fit into the encoding we want), or</td></tr></tbody></table></div><h3><a name="xmlCharEncodingInputFunc" id="xmlCharEncodingInputFunc"></a>Function type: xmlCharEncodingInputFunc</h3><pre class="programlisting">Function type: xmlCharEncodingInputFunc
int	xmlCharEncodingInputFunc	(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen)
</pre><p>Take a block of chars in the original encoding and try to convert it to an UTF-8 block of chars out.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the UTF-8 result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of chars in the original encoding</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written, -1 if lack of space, or -2 if the transcoding failed. The value of @inlen after return is the number of octets consumed if the return value is positive, else unpredictiable. The value of @outlen after return is the number of octets consumed.</td></tr></tbody></table></div><br />
<h3><a name="xmlCharEncodingOutputFunc" id="xmlCharEncodingOutputFunc"></a>Function type: xmlCharEncodingOutputFunc</h3><pre class="programlisting">Function type: xmlCharEncodingOutputFunc
int	xmlCharEncodingOutputFunc	(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen)
</pre><p>Take a block of UTF-8 chars in and try to convert it to another encoding. Note: a first call designed to produce heading info is called with in = NULL. If stateful this should also initialize the encoder state.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of UTF-8 chars</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written, -1 if lack of space, or -2 if the transcoding failed. The value of @inlen after return is the number of octets consumed if the return value is positive, else unpredictiable. The value of @outlen after return is the number of octets produced.</td></tr></tbody></table></div><br />
<h3><a name="xmlCleanupCharEncodingHandlers" id="xmlCleanupCharEncodingHandlers"></a>Function: xmlCleanupCharEncodingHandlers</h3><pre class="programlisting">void	xmlCleanupCharEncodingHandlers	(void)<br />
</pre><p>Cleanup the memory allocated for the char encoding support, it unregisters all the encoding handlers and the aliases.</p>
<h3><a name="xmlCleanupEncodingAliases" id="xmlCleanupEncodingAliases"></a>Function: xmlCleanupEncodingAliases</h3><pre class="programlisting">void	xmlCleanupEncodingAliases	(void)<br />
</pre><p>Unregisters all aliases</p>
<h3><a name="xmlDelEncodingAlias" id="xmlDelEncodingAlias"></a>Function: xmlDelEncodingAlias</h3><pre class="programlisting">int	xmlDelEncodingAlias		(const char * alias)<br />
</pre><p>Unregisters an encoding alias @alias</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>alias</tt></i>:</span></td><td>the alias name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlDetectCharEncoding" id="xmlDetectCharEncoding"></a>Function: xmlDetectCharEncoding</h3><pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a>	xmlDetectCharEncoding	(const unsigned char * in, <br />					 int len)<br />
</pre><p>Guess the encoding of the entity using the first bytes of the entity content according to the non-normative appendix F of the XML-1.0 recommendation.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to the first bytes of the XML entity, must be at least 2 bytes long (at least 4 if encoding is UTF4 variant).</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>pointer to the length of the buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>one of the XML_CHAR_ENCODING_... values.</td></tr></tbody></table></div><h3><a name="xmlFindCharEncodingHandler" id="xmlFindCharEncodingHandler"></a>Function: xmlFindCharEncodingHandler</h3><pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	xmlFindCharEncodingHandler	(const char * name)<br />
</pre><p>Search in the registered set the handler able to read/write that encoding.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>a string describing the char encoding.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the handler or NULL if not found</td></tr></tbody></table></div><h3><a name="xmlGetCharEncodingHandler" id="xmlGetCharEncodingHandler"></a>Function: xmlGetCharEncodingHandler</h3><pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	xmlGetCharEncodingHandler	(<a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br />
</pre><p>Search in the registered set the handler able to read/write that encoding.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>an <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> value.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the handler or NULL if not found</td></tr></tbody></table></div><h3><a name="xmlGetCharEncodingName" id="xmlGetCharEncodingName"></a>Function: xmlGetCharEncodingName</h3><pre class="programlisting">const char *	xmlGetCharEncodingName	(<a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br />
</pre><p>The "canonical" name for XML encoding. C.f. http://www.w3.org/TR/REC-xml#charencoding Section 4.3.3 Character Encoding in Entities</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the encoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the canonical name for the given encoding</td></tr></tbody></table></div><h3><a name="xmlGetEncodingAlias" id="xmlGetEncodingAlias"></a>Function: xmlGetEncodingAlias</h3><pre class="programlisting">const char *	xmlGetEncodingAlias	(const char * alias)<br />
</pre><p>Lookup an encoding name for the given alias.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>alias</tt></i>:</span></td><td>the alias name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL if not found, otherwise the original name</td></tr></tbody></table></div><h3><a name="xmlInitCharEncodingHandlers" id="xmlInitCharEncodingHandlers"></a>Function: xmlInitCharEncodingHandlers</h3><pre class="programlisting">void	xmlInitCharEncodingHandlers	(void)<br />
</pre><p>Initialize the char encoding support, it registers the default encoding supported. NOTE: while public, this function usually doesn't need to be called in normal processing.</p>
<h3><a name="xmlNewCharEncodingHandler" id="xmlNewCharEncodingHandler"></a>Function: xmlNewCharEncodingHandler</h3><pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	xmlNewCharEncodingHandler	(const char * name, <br />							 <a href="libxml-encoding.html#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a> input, <br />							 <a href="libxml-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> output)<br />
</pre><p>Create and registers an xmlCharEncodingHandler.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the encoding name, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the <a href="libxml-encoding.html#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a> to read that encoding</td></tr><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the <a href="libxml-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> to write that encoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> created (or NULL in case of error).</td></tr></tbody></table></div><h3><a name="xmlParseCharEncoding" id="xmlParseCharEncoding"></a>Function: xmlParseCharEncoding</h3><pre class="programlisting"><a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a>	xmlParseCharEncoding	(const char * name)<br />
</pre><p>Compare the string to the encoding schemes already known. Note that the comparison is case insensitive accordingly to the section [XML] 4.3.3 Character Encoding in Entities.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the encoding name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>one of the XML_CHAR_ENCODING_... values or <a href="libxml-encoding.html#XML_CHAR_ENCODING_NONE">XML_CHAR_ENCODING_NONE</a> if not recognized.</td></tr></tbody></table></div><h3><a name="xmlRegisterCharEncodingHandler" id="xmlRegisterCharEncodingHandler"></a>Function: xmlRegisterCharEncodingHandler</h3><pre class="programlisting">void	xmlRegisterCharEncodingHandler	(<a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler)<br />
</pre><p>Register the char encoding handler, surprising, isn't it ?</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>the <a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler block</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
