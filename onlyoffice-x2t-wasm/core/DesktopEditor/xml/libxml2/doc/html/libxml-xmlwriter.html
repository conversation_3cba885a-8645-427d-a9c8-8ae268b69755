<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlwriter from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlwriter from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlversion.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlversion.html">xmlversion</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xpath.html">xpath</a></th><td><a accesskey="n" href="libxml-xpath.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>text writing API for XML </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#xmlTextWriterWriteDocType">xmlTextWriterWriteDocType</a></pre><pre class="programlisting">#define <a href="#xmlTextWriterWriteProcessingInstruction">xmlTextWriterWriteProcessingInstruction</a></pre><pre class="programlisting">Structure <a href="#xmlTextWriter">xmlTextWriter</a><br />struct _xmlTextWriter
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlwriter.html#xmlTextWriter">xmlTextWriter</a> * <a name="xmlTextWriterPtr" id="xmlTextWriterPtr">xmlTextWriterPtr</a>
</pre><pre class="programlisting">void	<a href="#xmlFreeTextWriter">xmlFreeTextWriter</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriter">xmlNewTextWriter</a>	(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out)</pre>
<pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterDoc">xmlNewTextWriterDoc</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> * doc, <br />						 int compression)</pre>
<pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterFilename">xmlNewTextWriterFilename</a>	(const char * uri, <br />							 int compression)</pre>
<pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterMemory">xmlNewTextWriterMemory</a>	(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />						 int compression)</pre>
<pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterPushParser">xmlNewTextWriterPushParser</a>	(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />							 int compression)</pre>
<pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterTree">xmlNewTextWriterTree</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />						 int compression)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndAttribute">xmlTextWriterEndAttribute</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndCDATA">xmlTextWriterEndCDATA</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndComment">xmlTextWriterEndComment</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndDTD">xmlTextWriterEndDTD</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndDTDAttlist">xmlTextWriterEndDTDAttlist</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndDTDElement">xmlTextWriterEndDTDElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndDTDEntity">xmlTextWriterEndDTDEntity</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndDocument">xmlTextWriterEndDocument</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndElement">xmlTextWriterEndElement</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterEndPI">xmlTextWriterEndPI</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterFlush">xmlTextWriterFlush</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterFullEndElement">xmlTextWriterFullEndElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterSetIndent">xmlTextWriterSetIndent</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 int indent)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterSetIndentString">xmlTextWriterSetIndentString</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterSetQuoteChar">xmlTextWriterSetQuoteChar</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> quotechar)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartAttribute">xmlTextWriterStartAttribute</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartAttributeNS">xmlTextWriterStartAttributeNS</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartCDATA">xmlTextWriterStartCDATA</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartComment">xmlTextWriterStartComment</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartDTD">xmlTextWriterStartDTD</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartDTDAttlist">xmlTextWriterStartDTDAttlist</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartDTDElement">xmlTextWriterStartDTDElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartDTDEntity">xmlTextWriterStartDTDEntity</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 int pe, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartDocument">xmlTextWriterStartDocument</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * version, <br />					 const char * encoding, <br />					 const char * standalone)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartElement">xmlTextWriterStartElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartElementNS">xmlTextWriterStartElementNS</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterStartPI">xmlTextWriterStartPI</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteAttribute">xmlTextWriterWriteAttribute</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteAttributeNS">xmlTextWriterWriteAttributeNS</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteBase64">xmlTextWriterWriteBase64</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * data, <br />					 int start, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteBinHex">xmlTextWriterWriteBinHex</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * data, <br />					 int start, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteCDATA">xmlTextWriterWriteCDATA</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteComment">xmlTextWriterWriteComment</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteDTD">xmlTextWriterWriteDTD</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * subset)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteDTDAttlist">xmlTextWriterWriteDTDAttlist</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteDTDElement">xmlTextWriterWriteDTDElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteDTDEntity">xmlTextWriterWriteDTDEntity</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 int pe, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ndataid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteDTDExternalEntity">xmlTextWriterWriteDTDExternalEntity</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 int pe, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ndataid)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteDTDExternalEntityContents">xmlTextWriterWriteDTDExternalEntityContents</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ndataid)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteDTDInternalEntity">xmlTextWriterWriteDTDInternalEntity</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 int pe, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteDTDNotation">xmlTextWriterWriteDTDNotation</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteElement">xmlTextWriterWriteElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteElementNS">xmlTextWriterWriteElementNS</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatAttribute">xmlTextWriterWriteFormatAttribute</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatAttributeNS">xmlTextWriterWriteFormatAttributeNS</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />						 const char * format, <br />						 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatCDATA">xmlTextWriterWriteFormatCDATA</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatComment">xmlTextWriterWriteFormatComment</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatDTD">xmlTextWriterWriteFormatDTD</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />					 const char * format, <br />					 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatDTDAttlist">xmlTextWriterWriteFormatDTDAttlist</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatDTDElement">xmlTextWriterWriteFormatDTDElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatDTDInternalEntity">xmlTextWriterWriteFormatDTDInternalEntity</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />							 int pe, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />							 const char * format, <br />							 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatElement">xmlTextWriterWriteFormatElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const char * format, <br />					 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatElementNS">xmlTextWriterWriteFormatElementNS</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />						 const char * format, <br />						 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatPI">xmlTextWriterWriteFormatPI</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const char * format, <br />					 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatRaw">xmlTextWriterWriteFormatRaw</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteFormatString">xmlTextWriterWriteFormatString</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWritePI">xmlTextWriterWritePI</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteRaw">xmlTextWriterWriteRaw</a>		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteRawLen">xmlTextWriterWriteRawLen</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteString">xmlTextWriterWriteString</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatAttribute">xmlTextWriterWriteVFormatAttribute</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatAttributeNS">xmlTextWriterWriteVFormatAttributeNS</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />						 const char * format, <br />						 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatCDATA">xmlTextWriterWriteVFormatCDATA</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatComment">xmlTextWriterWriteVFormatComment</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const char * format, <br />						 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatDTD">xmlTextWriterWriteVFormatDTD</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />					 const char * format, <br />					 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatDTDAttlist">xmlTextWriterWriteVFormatDTDAttlist</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatDTDElement">xmlTextWriterWriteVFormatDTDElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatDTDInternalEntity">xmlTextWriterWriteVFormatDTDInternalEntity</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />							 int pe, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />							 const char * format, <br />							 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatElement">xmlTextWriterWriteVFormatElement</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatElementNS">xmlTextWriterWriteVFormatElementNS</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />						 const char * format, <br />						 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatPI">xmlTextWriterWriteVFormatPI</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const char * format, <br />					 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatRaw">xmlTextWriterWriteVFormatRaw</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 va_list argptr)</pre>
<pre class="programlisting">int	<a href="#xmlTextWriterWriteVFormatString">xmlTextWriterWriteVFormatString</a>	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 va_list argptr)</pre>
<h2>Description</h2>
<h3><a name="xmlTextWriterWriteDocType" id="xmlTextWriterWriteDocType"></a>Macro: xmlTextWriterWriteDocType</h3><pre>#define xmlTextWriterWriteDocType</pre><p>this macro maps to <a href="libxml-xmlwriter.html#xmlTextWriterWriteDTD">xmlTextWriterWriteDTD</a></p>
<h3><a name="xmlTextWriterWriteProcessingInstruction" id="xmlTextWriterWriteProcessingInstruction"></a>Macro: xmlTextWriterWriteProcessingInstruction</h3><pre>#define xmlTextWriterWriteProcessingInstruction</pre><p>This macro maps to <a href="libxml-xmlwriter.html#xmlTextWriterWritePI">xmlTextWriterWritePI</a></p>
<h3><a name="xmlTextWriter" id="xmlTextWriter">Structure xmlTextWriter</a></h3><pre class="programlisting">Structure xmlTextWriter<br />struct _xmlTextWriter {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlFreeTextWriter" id="xmlFreeTextWriter"></a>Function: xmlFreeTextWriter</h3><pre class="programlisting">void	xmlFreeTextWriter		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>Deallocate all the resources associated to the writer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr></tbody></table></div><h3><a name="xmlNewTextWriter" id="xmlNewTextWriter"></a>Function: xmlNewTextWriter</h3><pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriter	(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out)<br />
</pre><p>Create a new <a href="libxml-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure using an <a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> NOTE: the @out parameter will be deallocated when the writer is closed (if the call succeed.)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlNewTextWriterDoc" id="xmlNewTextWriterDoc"></a>Function: xmlNewTextWriterDoc</h3><pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterDoc	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> * doc, <br />						 int compression)<br />
</pre><p>Create a new <a href="libxml-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @*doc as output</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>address of a <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> to hold the new XML document tree</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlNewTextWriterFilename" id="xmlNewTextWriterFilename"></a>Function: xmlNewTextWriterFilename</h3><pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterFilename	(const char * uri, <br />							 int compression)<br />
</pre><p>Create a new <a href="libxml-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @uri as output</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>uri</tt></i>:</span></td><td>the URI of the resource for the output</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlNewTextWriterMemory" id="xmlNewTextWriterMemory"></a>Function: xmlNewTextWriterMemory</h3><pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterMemory	(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />						 int compression)<br />
</pre><p>Create a new <a href="libxml-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @buf as output TODO: handle compression</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td><a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a></td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlNewTextWriterPushParser" id="xmlNewTextWriterPushParser"></a>Function: xmlNewTextWriterPushParser</h3><pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterPushParser	(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />							 int compression)<br />
</pre><p>Create a new <a href="libxml-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @ctxt as output NOTE: the @ctxt context will be freed with the resulting writer (if the call succeeds). TODO: handle compression</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> to hold the new XML document tree</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlNewTextWriterTree" id="xmlNewTextWriterTree"></a>Function: xmlNewTextWriterTree</h3><pre class="programlisting"><a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterTree	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />						 int compression)<br />
</pre><p>Create a new <a href="libxml-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @doc as output starting at @node</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a></td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> or NULL for doc-&gt;children</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndAttribute" id="xmlTextWriterEndAttribute"></a>Function: xmlTextWriterEndAttribute</h3><pre class="programlisting">int	xmlTextWriterEndAttribute	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End the current xml element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndCDATA" id="xmlTextWriterEndCDATA"></a>Function: xmlTextWriterEndCDATA</h3><pre class="programlisting">int	xmlTextWriterEndCDATA		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End an xml CDATA section.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndComment" id="xmlTextWriterEndComment"></a>Function: xmlTextWriterEndComment</h3><pre class="programlisting">int	xmlTextWriterEndComment		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End the current xml coment.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndDTD" id="xmlTextWriterEndDTD"></a>Function: xmlTextWriterEndDTD</h3><pre class="programlisting">int	xmlTextWriterEndDTD		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End an xml DTD.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndDTDAttlist" id="xmlTextWriterEndDTDAttlist"></a>Function: xmlTextWriterEndDTDAttlist</h3><pre class="programlisting">int	xmlTextWriterEndDTDAttlist	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End an xml DTD <a href="libxml-SAX.html#attribute">attribute</a> list.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndDTDElement" id="xmlTextWriterEndDTDElement"></a>Function: xmlTextWriterEndDTDElement</h3><pre class="programlisting">int	xmlTextWriterEndDTDElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End an xml DTD element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndDTDEntity" id="xmlTextWriterEndDTDEntity"></a>Function: xmlTextWriterEndDTDEntity</h3><pre class="programlisting">int	xmlTextWriterEndDTDEntity	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End an xml DTD entity.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndDocument" id="xmlTextWriterEndDocument"></a>Function: xmlTextWriterEndDocument</h3><pre class="programlisting">int	xmlTextWriterEndDocument	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End an xml document. All open elements are closed, and the content is flushed to the output.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndElement" id="xmlTextWriterEndElement"></a>Function: xmlTextWriterEndElement</h3><pre class="programlisting">int	xmlTextWriterEndElement		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End the current xml element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterEndPI" id="xmlTextWriterEndPI"></a>Function: xmlTextWriterEndPI</h3><pre class="programlisting">int	xmlTextWriterEndPI		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End the current xml PI.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterFlush" id="xmlTextWriterFlush"></a>Function: xmlTextWriterFlush</h3><pre class="programlisting">int	xmlTextWriterFlush		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>Flush the output buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterFullEndElement" id="xmlTextWriterFullEndElement"></a>Function: xmlTextWriterFullEndElement</h3><pre class="programlisting">int	xmlTextWriterFullEndElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>End the current xml element. Writes an end tag even if the element is empty</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterSetIndent" id="xmlTextWriterSetIndent"></a>Function: xmlTextWriterSetIndent</h3><pre class="programlisting">int	xmlTextWriterSetIndent		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 int indent)<br />
</pre><p>Set indentation output. indent = 0 do not indentation. indent &gt; 0 do indentation.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>indent</tt></i>:</span></td><td>do indentation?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 on error or 0 otherwise.</td></tr></tbody></table></div><h3><a name="xmlTextWriterSetIndentString" id="xmlTextWriterSetIndentString"></a>Function: xmlTextWriterSetIndentString</h3><pre class="programlisting">int	xmlTextWriterSetIndentString	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>Set string indentation.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 on error or 0 otherwise.</td></tr></tbody></table></div><h3><a name="xmlTextWriterSetQuoteChar" id="xmlTextWriterSetQuoteChar"></a>Function: xmlTextWriterSetQuoteChar</h3><pre class="programlisting">int	xmlTextWriterSetQuoteChar	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> quotechar)<br />
</pre><p>Set the character used for quoting attributes.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>quotechar</tt></i>:</span></td><td>the quote character</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 on error or 0 otherwise.</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartAttribute" id="xmlTextWriterStartAttribute"></a>Function: xmlTextWriterStartAttribute</h3><pre class="programlisting">int	xmlTextWriterStartAttribute	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Start an xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartAttributeNS" id="xmlTextWriterStartAttributeNS"></a>Function: xmlTextWriterStartAttributeNS</h3><pre class="programlisting">int	xmlTextWriterStartAttributeNS	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)<br />
</pre><p>Start an xml <a href="libxml-SAX.html#attribute">attribute</a> with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix or NULL</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartCDATA" id="xmlTextWriterStartCDATA"></a>Function: xmlTextWriterStartCDATA</h3><pre class="programlisting">int	xmlTextWriterStartCDATA		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>Start an xml CDATA section.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartComment" id="xmlTextWriterStartComment"></a>Function: xmlTextWriterStartComment</h3><pre class="programlisting">int	xmlTextWriterStartComment	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br />
</pre><p>Start an xml comment.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartDTD" id="xmlTextWriterStartDTD"></a>Function: xmlTextWriterStartDTD</h3><pre class="programlisting">int	xmlTextWriterStartDTD		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid)<br />
</pre><p>Start an xml DTD.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartDTDAttlist" id="xmlTextWriterStartDTDAttlist"></a>Function: xmlTextWriterStartDTDAttlist</h3><pre class="programlisting">int	xmlTextWriterStartDTDAttlist	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Start an xml DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartDTDElement" id="xmlTextWriterStartDTDElement"></a>Function: xmlTextWriterStartDTDElement</h3><pre class="programlisting">int	xmlTextWriterStartDTDElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Start an xml DTD element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD element</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartDTDEntity" id="xmlTextWriterStartDTDEntity"></a>Function: xmlTextWriterStartDTDEntity</h3><pre class="programlisting">int	xmlTextWriterStartDTDEntity	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 int pe, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Start an xml DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartDocument" id="xmlTextWriterStartDocument"></a>Function: xmlTextWriterStartDocument</h3><pre class="programlisting">int	xmlTextWriterStartDocument	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * version, <br />					 const char * encoding, <br />					 const char * standalone)<br />
</pre><p>Start a new xml document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>version</tt></i>:</span></td><td>the xml version ("1.0") or NULL for default ("1.0")</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding or NULL for default</td></tr><tr><td><span class="term"><i><tt>standalone</tt></i>:</span></td><td>"yes" or "no" or NULL for default</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartElement" id="xmlTextWriterStartElement"></a>Function: xmlTextWriterStartElement</h3><pre class="programlisting">int	xmlTextWriterStartElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Start an xml element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartElementNS" id="xmlTextWriterStartElementNS"></a>Function: xmlTextWriterStartElementNS</h3><pre class="programlisting">int	xmlTextWriterStartElementNS	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)<br />
</pre><p>Start an xml element with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix or NULL</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterStartPI" id="xmlTextWriterStartPI"></a>Function: xmlTextWriterStartPI</h3><pre class="programlisting">int	xmlTextWriterStartPI		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target)<br />
</pre><p>Start an xml PI.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>PI target</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteAttribute" id="xmlTextWriterWriteAttribute"></a>Function: xmlTextWriterWriteAttribute</h3><pre class="programlisting">int	xmlTextWriterWriteAttribute	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write an xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td><a href="libxml-SAX.html#attribute">attribute</a> content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteAttributeNS" id="xmlTextWriterWriteAttributeNS"></a>Function: xmlTextWriterWriteAttributeNS</h3><pre class="programlisting">int	xmlTextWriterWriteAttributeNS	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write an xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml-SAX.html#attribute">attribute</a> local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td><a href="libxml-SAX.html#attribute">attribute</a> content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteBase64" id="xmlTextWriterWriteBase64"></a>Function: xmlTextWriterWriteBase64</h3><pre class="programlisting">int	xmlTextWriterWriteBase64	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * data, <br />					 int start, <br />					 int len)<br />
</pre><p>Write an base64 encoded xml text.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>binary data</td></tr><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the position within the data of the first byte to encode</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of bytes to encode</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteBinHex" id="xmlTextWriterWriteBinHex"></a>Function: xmlTextWriterWriteBinHex</h3><pre class="programlisting">int	xmlTextWriterWriteBinHex	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * data, <br />					 int start, <br />					 int len)<br />
</pre><p>Write a BinHex encoded xml text.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>binary data</td></tr><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the position within the data of the first byte to encode</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of bytes to encode</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteCDATA" id="xmlTextWriterWriteCDATA"></a>Function: xmlTextWriterWriteCDATA</h3><pre class="programlisting">int	xmlTextWriterWriteCDATA		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write an xml CDATA.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>CDATA content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteComment" id="xmlTextWriterWriteComment"></a>Function: xmlTextWriterWriteComment</h3><pre class="programlisting">int	xmlTextWriterWriteComment	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write an xml comment.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td><a href="libxml-SAX.html#comment">comment</a> string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteDTD" id="xmlTextWriterWriteDTD"></a>Function: xmlTextWriterWriteDTD</h3><pre class="programlisting">int	xmlTextWriterWriteDTD		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * subset)<br />
</pre><p>Write a DTD.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>subset</tt></i>:</span></td><td>string content of the DTD</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteDTDAttlist" id="xmlTextWriterWriteDTDAttlist"></a>Function: xmlTextWriterWriteDTDAttlist</h3><pre class="programlisting">int	xmlTextWriterWriteDTDAttlist	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write a DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>content of the ATTLIST</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteDTDElement" id="xmlTextWriterWriteDTDElement"></a>Function: xmlTextWriterWriteDTDElement</h3><pre class="programlisting">int	xmlTextWriterWriteDTDElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write a DTD element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD element</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>content of the element</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteDTDEntity" id="xmlTextWriterWriteDTDEntity"></a>Function: xmlTextWriterWriteDTDEntity</h3><pre class="programlisting">int	xmlTextWriterWriteDTDEntity	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 int pe, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ndataid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write a DTD entity.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>ndataid</tt></i>:</span></td><td>the xml notation name.</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>content of the entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteDTDExternalEntity" id="xmlTextWriterWriteDTDExternalEntity"></a>Function: xmlTextWriterWriteDTDExternalEntity</h3><pre class="programlisting">int	xmlTextWriterWriteDTDExternalEntity	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 int pe, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ndataid)<br />
</pre><p>Write a DTD external entity. The entity must have been started with <a href="libxml-xmlwriter.html#xmlTextWriterStartDTDEntity">xmlTextWriterStartDTDEntity</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>ndataid</tt></i>:</span></td><td>the xml notation name.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteDTDExternalEntityContents" id="xmlTextWriterWriteDTDExternalEntityContents"></a>Function: xmlTextWriterWriteDTDExternalEntityContents</h3><pre class="programlisting">int	xmlTextWriterWriteDTDExternalEntityContents	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ndataid)<br />
</pre><p>Write the contents of a DTD external entity.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>ndataid</tt></i>:</span></td><td>the xml notation name.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteDTDInternalEntity" id="xmlTextWriterWriteDTDInternalEntity"></a>Function: xmlTextWriterWriteDTDInternalEntity</h3><pre class="programlisting">int	xmlTextWriterWriteDTDInternalEntity	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 int pe, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write a DTD internal entity.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>content of the entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteDTDNotation" id="xmlTextWriterWriteDTDNotation"></a>Function: xmlTextWriterWriteDTDNotation</h3><pre class="programlisting">int	xmlTextWriterWriteDTDNotation	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid)<br />
</pre><p>Write a DTD entity.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the xml notation</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteElement" id="xmlTextWriterWriteElement"></a>Function: xmlTextWriterWriteElement</h3><pre class="programlisting">int	xmlTextWriterWriteElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write an xml element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>element content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteElementNS" id="xmlTextWriterWriteElementNS"></a>Function: xmlTextWriterWriteElementNS</h3><pre class="programlisting">int	xmlTextWriterWriteElementNS	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write an xml element with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>element content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatAttribute" id="xmlTextWriterWriteFormatAttribute"></a>Function: xmlTextWriterWriteFormatAttribute</h3><pre class="programlisting">int	xmlTextWriterWriteFormatAttribute	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 ... ...)<br />
</pre><p>Write a formatted xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatAttributeNS" id="xmlTextWriterWriteFormatAttributeNS"></a>Function: xmlTextWriterWriteFormatAttributeNS</h3><pre class="programlisting">int	xmlTextWriterWriteFormatAttributeNS	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />						 const char * format, <br />						 ... ...)<br />
</pre><p>Write a formatted xml attribute.with namespace support</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml-SAX.html#attribute">attribute</a> local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatCDATA" id="xmlTextWriterWriteFormatCDATA"></a>Function: xmlTextWriterWriteFormatCDATA</h3><pre class="programlisting">int	xmlTextWriterWriteFormatCDATA	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 ... ...)<br />
</pre><p>Write a formatted xml CDATA.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatComment" id="xmlTextWriterWriteFormatComment"></a>Function: xmlTextWriterWriteFormatComment</h3><pre class="programlisting">int	xmlTextWriterWriteFormatComment	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 ... ...)<br />
</pre><p>Write an xml comment.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatDTD" id="xmlTextWriterWriteFormatDTD"></a>Function: xmlTextWriterWriteFormatDTD</h3><pre class="programlisting">int	xmlTextWriterWriteFormatDTD	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />					 const char * format, <br />					 ... ...)<br />
</pre><p>Write a DTD with a formatted markup declarations part.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatDTDAttlist" id="xmlTextWriterWriteFormatDTDAttlist"></a>Function: xmlTextWriterWriteFormatDTDAttlist</h3><pre class="programlisting">int	xmlTextWriterWriteFormatDTDAttlist	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 ... ...)<br />
</pre><p>Write a formatted DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatDTDElement" id="xmlTextWriterWriteFormatDTDElement"></a>Function: xmlTextWriterWriteFormatDTDElement</h3><pre class="programlisting">int	xmlTextWriterWriteFormatDTDElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 ... ...)<br />
</pre><p>Write a formatted DTD element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD element</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatDTDInternalEntity" id="xmlTextWriterWriteFormatDTDInternalEntity"></a>Function: xmlTextWriterWriteFormatDTDInternalEntity</h3><pre class="programlisting">int	xmlTextWriterWriteFormatDTDInternalEntity	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />							 int pe, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />							 const char * format, <br />							 ... ...)<br />
</pre><p>Write a formatted DTD internal entity.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatElement" id="xmlTextWriterWriteFormatElement"></a>Function: xmlTextWriterWriteFormatElement</h3><pre class="programlisting">int	xmlTextWriterWriteFormatElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const char * format, <br />					 ... ...)<br />
</pre><p>Write a formatted xml element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatElementNS" id="xmlTextWriterWriteFormatElementNS"></a>Function: xmlTextWriterWriteFormatElementNS</h3><pre class="programlisting">int	xmlTextWriterWriteFormatElementNS	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />						 const char * format, <br />						 ... ...)<br />
</pre><p>Write a formatted xml element with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatPI" id="xmlTextWriterWriteFormatPI"></a>Function: xmlTextWriterWriteFormatPI</h3><pre class="programlisting">int	xmlTextWriterWriteFormatPI	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const char * format, <br />					 ... ...)<br />
</pre><p>Write a formatted PI.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>PI target</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatRaw" id="xmlTextWriterWriteFormatRaw"></a>Function: xmlTextWriterWriteFormatRaw</h3><pre class="programlisting">int	xmlTextWriterWriteFormatRaw	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 ... ...)<br />
</pre><p>Write a formatted raw xml text.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteFormatString" id="xmlTextWriterWriteFormatString"></a>Function: xmlTextWriterWriteFormatString</h3><pre class="programlisting">int	xmlTextWriterWriteFormatString	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 ... ...)<br />
</pre><p>Write a formatted xml text.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWritePI" id="xmlTextWriterWritePI"></a>Function: xmlTextWriterWritePI</h3><pre class="programlisting">int	xmlTextWriterWritePI		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write an xml PI.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>PI target</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>PI content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteRaw" id="xmlTextWriterWriteRaw"></a>Function: xmlTextWriterWriteRaw</h3><pre class="programlisting">int	xmlTextWriterWriteRaw		(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write a raw xml text.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>text string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteRawLen" id="xmlTextWriterWriteRawLen"></a>Function: xmlTextWriterWriteRawLen</h3><pre class="programlisting">int	xmlTextWriterWriteRawLen	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)<br />
</pre><p>Write an xml text. TODO: what about entities and special chars??</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>text string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>length of the text string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteString" id="xmlTextWriterWriteString"></a>Function: xmlTextWriterWriteString</h3><pre class="programlisting">int	xmlTextWriterWriteString	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Write an xml text.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>text string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatAttribute" id="xmlTextWriterWriteVFormatAttribute"></a>Function: xmlTextWriterWriteVFormatAttribute</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatAttribute	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 va_list argptr)<br />
</pre><p>Write a formatted xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatAttributeNS" id="xmlTextWriterWriteVFormatAttributeNS"></a>Function: xmlTextWriterWriteVFormatAttributeNS</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatAttributeNS	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />						 const char * format, <br />						 va_list argptr)<br />
</pre><p>Write a formatted xml attribute.with namespace support</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml-SAX.html#attribute">attribute</a> local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatCDATA" id="xmlTextWriterWriteVFormatCDATA"></a>Function: xmlTextWriterWriteVFormatCDATA</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatCDATA	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 va_list argptr)<br />
</pre><p>Write a formatted xml CDATA.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatComment" id="xmlTextWriterWriteVFormatComment"></a>Function: xmlTextWriterWriteVFormatComment</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatComment	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const char * format, <br />						 va_list argptr)<br />
</pre><p>Write an xml comment.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatDTD" id="xmlTextWriterWriteVFormatDTD"></a>Function: xmlTextWriterWriteVFormatDTD</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatDTD	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br />					 const char * format, <br />					 va_list argptr)<br />
</pre><p>Write a DTD with a formatted markup declarations part.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatDTDAttlist" id="xmlTextWriterWriteVFormatDTDAttlist"></a>Function: xmlTextWriterWriteVFormatDTDAttlist</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatDTDAttlist	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 va_list argptr)<br />
</pre><p>Write a formatted DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatDTDElement" id="xmlTextWriterWriteVFormatDTDElement"></a>Function: xmlTextWriterWriteVFormatDTDElement</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatDTDElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 va_list argptr)<br />
</pre><p>Write a formatted DTD element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD element</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatDTDInternalEntity" id="xmlTextWriterWriteVFormatDTDInternalEntity"></a>Function: xmlTextWriterWriteVFormatDTDInternalEntity</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatDTDInternalEntity	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />							 int pe, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />							 const char * format, <br />							 va_list argptr)<br />
</pre><p>Write a formatted DTD internal entity.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatElement" id="xmlTextWriterWriteVFormatElement"></a>Function: xmlTextWriterWriteVFormatElement</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatElement	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const char * format, <br />						 va_list argptr)<br />
</pre><p>Write a formatted xml element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatElementNS" id="xmlTextWriterWriteVFormatElementNS"></a>Function: xmlTextWriterWriteVFormatElementNS</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatElementNS	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br />						 const char * format, <br />						 va_list argptr)<br />
</pre><p>Write a formatted xml element with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatPI" id="xmlTextWriterWriteVFormatPI"></a>Function: xmlTextWriterWriteVFormatPI</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatPI	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const char * format, <br />					 va_list argptr)<br />
</pre><p>Write a formatted xml PI.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>PI target</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatRaw" id="xmlTextWriterWriteVFormatRaw"></a>Function: xmlTextWriterWriteVFormatRaw</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatRaw	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 va_list argptr)<br />
</pre><p>Write a formatted raw xml text.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextWriterWriteVFormatString" id="xmlTextWriterWriteVFormatString"></a>Function: xmlTextWriterWriteVFormatString</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatString	(<a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br />					 const char * format, <br />					 va_list argptr)<br />
</pre><p>Write a formatted xml text.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
