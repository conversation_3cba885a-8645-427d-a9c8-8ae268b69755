<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlschemas from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlschemas from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlsave.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlsave.html">xmlsave</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlschemastypes.html">xmlschemastypes</a></th><td><a accesskey="n" href="libxml-xmlschemastypes.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>interface to the XML Schemas handling and schema validity checking, it is incomplete right now. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlSchema">xmlSchema</a><br />struct _xmlSchema
</pre><pre class="programlisting">Structure <a href="#xmlSchemaParserCtxt">xmlSchemaParserCtxt</a><br />struct _xmlSchemaParserCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlschemas.html#xmlSchemaParserCtxt">xmlSchemaParserCtxt</a> * <a name="xmlSchemaParserCtxtPtr" id="xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-xmlschemas.html#xmlSchema">xmlSchema</a> * <a name="xmlSchemaPtr" id="xmlSchemaPtr">xmlSchemaPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-xmlschemas.html#xmlSchemaSAXPlugStruct">xmlSchemaSAXPlugStruct</a> * <a name="xmlSchemaSAXPlugPtr" id="xmlSchemaSAXPlugPtr">xmlSchemaSAXPlugPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaSAXPlugStruct">xmlSchemaSAXPlugStruct</a><br />struct _xmlSchemaSAXPlug
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Structure <a href="#xmlSchemaValidCtxt">xmlSchemaValidCtxt</a><br />struct _xmlSchemaValidCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlschemas.html#xmlSchemaValidCtxt">xmlSchemaValidCtxt</a> * <a name="xmlSchemaValidCtxtPtr" id="xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlSchemaValidError">xmlSchemaValidError</a>
</pre><pre class="programlisting">Enum <a href="#xmlSchemaValidOption">xmlSchemaValidOption</a>
</pre><pre class="programlisting">void	<a href="#xmlSchemaDump">xmlSchemaDump</a>			(FILE * output, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a> schema)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaFree">xmlSchemaFree</a>			(<a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a> schema)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaFreeParserCtxt">xmlSchemaFreeParserCtxt</a>		(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaFreeValidCtxt">xmlSchemaFreeValidCtxt</a>		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaGetParserErrors">xmlSchemaGetParserErrors</a>	(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a> * err, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a> * warn, <br />					 void ** ctx)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaGetValidErrors">xmlSchemaGetValidErrors</a>		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a> * err, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a> * warn, <br />					 void ** ctx)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaIsValid">xmlSchemaIsValid</a>		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a>	<a href="#xmlSchemaNewDocParserCtxt">xmlSchemaNewDocParserCtxt</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a>	<a href="#xmlSchemaNewMemParserCtxt">xmlSchemaNewMemParserCtxt</a>	(const char * buffer, <br />							 int size)</pre>
<pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a>	<a href="#xmlSchemaNewParserCtxt">xmlSchemaNewParserCtxt</a>	(const char * URL)</pre>
<pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a>	<a href="#xmlSchemaNewValidCtxt">xmlSchemaNewValidCtxt</a>	(<a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a> schema)</pre>
<pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a>	<a href="#xmlSchemaParse">xmlSchemaParse</a>		(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaSAXPlugPtr">xmlSchemaSAXPlugPtr</a>	<a href="#xmlSchemaSAXPlug">xmlSchemaSAXPlug</a>	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />						 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> * sax, <br />						 void ** user_data)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaSAXUnplug">xmlSchemaSAXUnplug</a>		(<a href="libxml-xmlschemas.html#xmlSchemaSAXPlugPtr">xmlSchemaSAXPlugPtr</a> plug)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaSetParserErrors">xmlSchemaSetParserErrors</a>	(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a> err, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a> warn, <br />					 void * ctx)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaSetParserStructuredErrors">xmlSchemaSetParserStructuredErrors</a>	(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt, <br />						 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> serror, <br />						 void * ctx)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaSetValidErrors">xmlSchemaSetValidErrors</a>		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a> err, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a> warn, <br />					 void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaSetValidOptions">xmlSchemaSetValidOptions</a>	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 int options)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaSetValidStructuredErrors">xmlSchemaSetValidStructuredErrors</a>	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />						 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> serror, <br />						 void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaValidCtxtGetOptions">xmlSchemaValidCtxtGetOptions</a>	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlSchemaValidCtxtGetParserCtxt">xmlSchemaValidCtxtGetParserCtxt</a>	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaValidateDoc">xmlSchemaValidateDoc</a>		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaValidateFile">xmlSchemaValidateFile</a>		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 const char * filename, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaValidateOneElement">xmlSchemaValidateOneElement</a>	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaValidateSetFilename">xmlSchemaValidateSetFilename</a>	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> vctxt, <br />					 const char * filename)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaValidateSetLocator">xmlSchemaValidateSetLocator</a>	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> vctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityLocatorFunc">xmlSchemaValidityLocatorFunc</a> f, <br />					 void * ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlSchemaValidateStream">xmlSchemaValidateStream</a>		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />					 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc, <br />					 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data)</pre>
<pre class="programlisting">Function type: <a href="#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a>
void	<a href="#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a>	(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre>
<pre class="programlisting">Function type: <a href="#xmlSchemaValidityLocatorFunc">xmlSchemaValidityLocatorFunc</a>
int	<a href="#xmlSchemaValidityLocatorFunc">xmlSchemaValidityLocatorFunc</a>	(void * ctx, <br />					 const char ** file, <br />					 unsigned long * line)
</pre>
<pre class="programlisting">Function type: <a href="#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a>
void	<a href="#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a>	(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre>
<h2>Description</h2>
<h3><a name="xmlSchema" id="xmlSchema">Structure xmlSchema</a></h3><pre class="programlisting">Structure xmlSchema<br />struct _xmlSchema {
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: schema name
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	targetNamespace	: the target namespace
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	version
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	id	: Obsolete
    <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	doc
    <a href="libxml-schemasInternals.html#xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>	annot
    int	flags
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	typeDecl
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	attrDecl
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	attrgrpDecl
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	elemDecl
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	notaDecl
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	schemasImports
    void *	_private	: unused by the library for users or bind
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	groupDecl
    <a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a>	dict
    void *	includes	: the includes, this is opaque for now
    int	preserve	: whether to free the document
    int	counter	: used to give ononymous components uniqu
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	idcDef	: All identity-constraint defs.
    void *	volatiles	: Obsolete
}</pre><h3><a name="xmlSchemaParserCtxt" id="xmlSchemaParserCtxt">Structure xmlSchemaParserCtxt</a></h3><pre class="programlisting">Structure xmlSchemaParserCtxt<br />struct _xmlSchemaParserCtxt {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlSchemaSAXPlugStruct" id="xmlSchemaSAXPlugStruct">Structure xmlSchemaSAXPlugStruct</a></h3><pre class="programlisting">Structure xmlSchemaSAXPlugStruct<br />struct _xmlSchemaSAXPlug {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlSchemaValidCtxt" id="xmlSchemaValidCtxt">Structure xmlSchemaValidCtxt</a></h3><pre class="programlisting">Structure xmlSchemaValidCtxt<br />struct _xmlSchemaValidCtxt {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlSchemaValidError" id="xmlSchemaValidError">xmlSchemaValidError</a></h3><pre class="programlisting">Enum xmlSchemaValidError {
    <a name="XML_SCHEMAS_ERR_OK" id="XML_SCHEMAS_ERR_OK">XML_SCHEMAS_ERR_OK</a> = 0
    <a name="XML_SCHEMAS_ERR_NOROOT" id="XML_SCHEMAS_ERR_NOROOT">XML_SCHEMAS_ERR_NOROOT</a> = 1
    <a name="XML_SCHEMAS_ERR_UNDECLAREDELEM" id="XML_SCHEMAS_ERR_UNDECLAREDELEM">XML_SCHEMAS_ERR_UNDECLAREDELEM</a> = 2
    <a name="XML_SCHEMAS_ERR_NOTTOPLEVEL" id="XML_SCHEMAS_ERR_NOTTOPLEVEL">XML_SCHEMAS_ERR_NOTTOPLEVEL</a> = 3
    <a name="XML_SCHEMAS_ERR_MISSING" id="XML_SCHEMAS_ERR_MISSING">XML_SCHEMAS_ERR_MISSING</a> = 4
    <a name="XML_SCHEMAS_ERR_WRONGELEM" id="XML_SCHEMAS_ERR_WRONGELEM">XML_SCHEMAS_ERR_WRONGELEM</a> = 5
    <a name="XML_SCHEMAS_ERR_NOTYPE" id="XML_SCHEMAS_ERR_NOTYPE">XML_SCHEMAS_ERR_NOTYPE</a> = 6
    <a name="XML_SCHEMAS_ERR_NOROLLBACK" id="XML_SCHEMAS_ERR_NOROLLBACK">XML_SCHEMAS_ERR_NOROLLBACK</a> = 7
    <a name="XML_SCHEMAS_ERR_ISABSTRACT" id="XML_SCHEMAS_ERR_ISABSTRACT">XML_SCHEMAS_ERR_ISABSTRACT</a> = 8
    <a name="XML_SCHEMAS_ERR_NOTEMPTY" id="XML_SCHEMAS_ERR_NOTEMPTY">XML_SCHEMAS_ERR_NOTEMPTY</a> = 9
    <a name="XML_SCHEMAS_ERR_ELEMCONT" id="XML_SCHEMAS_ERR_ELEMCONT">XML_SCHEMAS_ERR_ELEMCONT</a> = 10
    <a name="XML_SCHEMAS_ERR_HAVEDEFAULT" id="XML_SCHEMAS_ERR_HAVEDEFAULT">XML_SCHEMAS_ERR_HAVEDEFAULT</a> = 11
    <a name="XML_SCHEMAS_ERR_NOTNILLABLE" id="XML_SCHEMAS_ERR_NOTNILLABLE">XML_SCHEMAS_ERR_NOTNILLABLE</a> = 12
    <a name="XML_SCHEMAS_ERR_EXTRACONTENT" id="XML_SCHEMAS_ERR_EXTRACONTENT">XML_SCHEMAS_ERR_EXTRACONTENT</a> = 13
    <a name="XML_SCHEMAS_ERR_INVALIDATTR" id="XML_SCHEMAS_ERR_INVALIDATTR">XML_SCHEMAS_ERR_INVALIDATTR</a> = 14
    <a name="XML_SCHEMAS_ERR_INVALIDELEM" id="XML_SCHEMAS_ERR_INVALIDELEM">XML_SCHEMAS_ERR_INVALIDELEM</a> = 15
    <a name="XML_SCHEMAS_ERR_NOTDETERMINIST" id="XML_SCHEMAS_ERR_NOTDETERMINIST">XML_SCHEMAS_ERR_NOTDETERMINIST</a> = 16
    <a name="XML_SCHEMAS_ERR_CONSTRUCT" id="XML_SCHEMAS_ERR_CONSTRUCT">XML_SCHEMAS_ERR_CONSTRUCT</a> = 17
    <a name="XML_SCHEMAS_ERR_INTERNAL" id="XML_SCHEMAS_ERR_INTERNAL">XML_SCHEMAS_ERR_INTERNAL</a> = 18
    <a name="XML_SCHEMAS_ERR_NOTSIMPLE" id="XML_SCHEMAS_ERR_NOTSIMPLE">XML_SCHEMAS_ERR_NOTSIMPLE</a> = 19
    <a name="XML_SCHEMAS_ERR_ATTRUNKNOWN" id="XML_SCHEMAS_ERR_ATTRUNKNOWN">XML_SCHEMAS_ERR_ATTRUNKNOWN</a> = 20
    <a name="XML_SCHEMAS_ERR_ATTRINVALID" id="XML_SCHEMAS_ERR_ATTRINVALID">XML_SCHEMAS_ERR_ATTRINVALID</a> = 21
    <a name="XML_SCHEMAS_ERR_VALUE" id="XML_SCHEMAS_ERR_VALUE">XML_SCHEMAS_ERR_VALUE</a> = 22
    <a name="XML_SCHEMAS_ERR_FACET" id="XML_SCHEMAS_ERR_FACET">XML_SCHEMAS_ERR_FACET</a> = 23
    <a name="XML_SCHEMAS_ERR_" id="XML_SCHEMAS_ERR_">XML_SCHEMAS_ERR_</a> = 24
    <a name="XML_SCHEMAS_ERR_XXX" id="XML_SCHEMAS_ERR_XXX">XML_SCHEMAS_ERR_XXX</a> = 25
}
</pre><h3>Enum <a name="xmlSchemaValidOption" id="xmlSchemaValidOption">xmlSchemaValidOption</a></h3><pre class="programlisting">Enum xmlSchemaValidOption {
    <a name="XML_SCHEMA_VAL_VC_I_CREATE" id="XML_SCHEMA_VAL_VC_I_CREATE">XML_SCHEMA_VAL_VC_I_CREATE</a> = 1 : Default/fixed: create an <a href="libxml-SAX.html#attribute">attribute</a> node * or an element's text node on the instance. *
}
</pre><h3><a name="xmlSchemaDump" id="xmlSchemaDump"></a>Function: xmlSchemaDump</h3><pre class="programlisting">void	xmlSchemaDump			(FILE * output, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a> schema)<br />
</pre><p>Dump a Schema structure.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the file output</td></tr><tr><td><span class="term"><i><tt>schema</tt></i>:</span></td><td>a schema structure</td></tr></tbody></table></div><h3><a name="xmlSchemaFree" id="xmlSchemaFree"></a>Function: xmlSchemaFree</h3><pre class="programlisting">void	xmlSchemaFree			(<a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a> schema)<br />
</pre><p>Deallocate a Schema structure.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>schema</tt></i>:</span></td><td>a schema structure</td></tr></tbody></table></div><h3><a name="xmlSchemaFreeParserCtxt" id="xmlSchemaFreeParserCtxt"></a>Function: xmlSchemaFreeParserCtxt</h3><pre class="programlisting">void	xmlSchemaFreeParserCtxt		(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt)<br />
</pre><p>Free the resources associated to the schema parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the schema parser context</td></tr></tbody></table></div><h3><a name="xmlSchemaFreeValidCtxt" id="xmlSchemaFreeValidCtxt"></a>Function: xmlSchemaFreeValidCtxt</h3><pre class="programlisting">void	xmlSchemaFreeValidCtxt		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt)<br />
</pre><p>Free the resources associated to the schema validation context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the schema validation context</td></tr></tbody></table></div><h3><a name="xmlSchemaGetParserErrors" id="xmlSchemaGetParserErrors"></a>Function: xmlSchemaGetParserErrors</h3><pre class="programlisting">int	xmlSchemaGetParserErrors	(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a> * err, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a> * warn, <br />					 void ** ctx)<br />
</pre><p>Get the callback information used to handle errors for a parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a XMl-Schema parser context</td></tr><tr><td><span class="term"><i><tt>err</tt></i>:</span></td><td>the error callback result</td></tr><tr><td><span class="term"><i><tt>warn</tt></i>:</span></td><td>the warning callback result</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>contextual data for the callbacks result</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of failure, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlSchemaGetValidErrors" id="xmlSchemaGetValidErrors"></a>Function: xmlSchemaGetValidErrors</h3><pre class="programlisting">int	xmlSchemaGetValidErrors		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a> * err, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a> * warn, <br />					 void ** ctx)<br />
</pre><p>Get the error and warning callback informations</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a XML-Schema validation context</td></tr><tr><td><span class="term"><i><tt>err</tt></i>:</span></td><td>the error function result</td></tr><tr><td><span class="term"><i><tt>warn</tt></i>:</span></td><td>the warning function result</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the functions context result</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error and 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlSchemaIsValid" id="xmlSchemaIsValid"></a>Function: xmlSchemaIsValid</h3><pre class="programlisting">int	xmlSchemaIsValid		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt)<br />
</pre><p>Check if any error was detected during validation.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the schema validation context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if valid so far, 0 if errors were detected, and -1 in case of internal error.</td></tr></tbody></table></div><h3><a name="xmlSchemaNewDocParserCtxt" id="xmlSchemaNewDocParserCtxt"></a>Function: xmlSchemaNewDocParserCtxt</h3><pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a>	xmlSchemaNewDocParserCtxt	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Create an XML Schemas parse context for that document. NB. The document may be modified during the parsing process.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>a preparsed document tree</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the parser context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchemaNewMemParserCtxt" id="xmlSchemaNewMemParserCtxt"></a>Function: xmlSchemaNewMemParserCtxt</h3><pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a>	xmlSchemaNewMemParserCtxt	(const char * buffer, <br />							 int size)<br />
</pre><p>Create an XML Schemas parse context for that memory buffer expected to contain an XML Schemas file.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a pointer to a char array containing the schemas</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the parser context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchemaNewParserCtxt" id="xmlSchemaNewParserCtxt"></a>Function: xmlSchemaNewParserCtxt</h3><pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a>	xmlSchemaNewParserCtxt	(const char * URL)<br />
</pre><p>Create an XML Schemas parse context for that file/resource expected to contain an XML Schemas file.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the location of the schema</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the parser context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchemaNewValidCtxt" id="xmlSchemaNewValidCtxt"></a>Function: xmlSchemaNewValidCtxt</h3><pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a>	xmlSchemaNewValidCtxt	(<a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a> schema)<br />
</pre><p>Create an XML Schemas validation context based on the given schema.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>schema</tt></i>:</span></td><td>a precompiled XML Schemas</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the validation context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchemaParse" id="xmlSchemaParse"></a>Function: xmlSchemaParse</h3><pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a>	xmlSchemaParse		(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt)<br />
</pre><p>parse a schema definition resource and build an internal XML Shema struture which can be used to validate instances.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the internal XML Schema structure built from the resource or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchemaSAXPlug" id="xmlSchemaSAXPlug"></a>Function: xmlSchemaSAXPlug</h3><pre class="programlisting"><a href="libxml-xmlschemas.html#xmlSchemaSAXPlugPtr">xmlSchemaSAXPlugPtr</a>	xmlSchemaSAXPlug	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />						 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> * sax, <br />						 void ** user_data)<br />
</pre><p>Plug a SAX based validation layer in a SAX parsing event flow. The original @saxptr and @dataptr data are replaced by new pointers but the calls to the original will be maintained.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>a pointer to the original <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a></td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>a pointer to the original SAX user data pointer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to a data structure needed to unplug the validation layer or NULL in case of errors.</td></tr></tbody></table></div><h3><a name="xmlSchemaSAXUnplug" id="xmlSchemaSAXUnplug"></a>Function: xmlSchemaSAXUnplug</h3><pre class="programlisting">int	xmlSchemaSAXUnplug		(<a href="libxml-xmlschemas.html#xmlSchemaSAXPlugPtr">xmlSchemaSAXPlugPtr</a> plug)<br />
</pre><p>Unplug a SAX based validation layer in a SAX parsing event flow. The original pointers used in the call are restored.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>plug</tt></i>:</span></td><td>a data structure returned by <a href="libxml-xmlschemas.html#xmlSchemaSAXPlug">xmlSchemaSAXPlug</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlSchemaSetParserErrors" id="xmlSchemaSetParserErrors"></a>Function: xmlSchemaSetParserErrors</h3><pre class="programlisting">void	xmlSchemaSetParserErrors	(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a> err, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a> warn, <br />					 void * ctx)<br />
</pre><p>Set the callback functions used to handle errors for a validation context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>err</tt></i>:</span></td><td>the error callback</td></tr><tr><td><span class="term"><i><tt>warn</tt></i>:</span></td><td>the warning callback</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>contextual data for the callbacks</td></tr></tbody></table></div><h3><a name="xmlSchemaSetParserStructuredErrors" id="xmlSchemaSetParserStructuredErrors"></a>Function: xmlSchemaSetParserStructuredErrors</h3><pre class="programlisting">void	xmlSchemaSetParserStructuredErrors	(<a href="libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">xmlSchemaParserCtxtPtr</a> ctxt, <br />						 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> serror, <br />						 void * ctx)<br />
</pre><p>Set the structured error callback</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema parser context</td></tr><tr><td><span class="term"><i><tt>serror</tt></i>:</span></td><td>the structured error function</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the functions context</td></tr></tbody></table></div><h3><a name="xmlSchemaSetValidErrors" id="xmlSchemaSetValidErrors"></a>Function: xmlSchemaSetValidErrors</h3><pre class="programlisting">void	xmlSchemaSetValidErrors		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">xmlSchemaValidityErrorFunc</a> err, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">xmlSchemaValidityWarningFunc</a> warn, <br />					 void * ctx)<br />
</pre><p>Set the error and warning callback informations</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>err</tt></i>:</span></td><td>the error function</td></tr><tr><td><span class="term"><i><tt>warn</tt></i>:</span></td><td>the warning function</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the functions context</td></tr></tbody></table></div><h3><a name="xmlSchemaSetValidOptions" id="xmlSchemaSetValidOptions"></a>Function: xmlSchemaSetValidOptions</h3><pre class="programlisting">int	xmlSchemaSetValidOptions	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 int options)<br />
</pre><p>Sets the options to be used during the validation.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-xmlschemas.html#xmlSchemaValidOption">xmlSchemaValidOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of an API error.</td></tr></tbody></table></div><h3><a name="xmlSchemaSetValidStructuredErrors" id="xmlSchemaSetValidStructuredErrors"></a>Function: xmlSchemaSetValidStructuredErrors</h3><pre class="programlisting">void	xmlSchemaSetValidStructuredErrors	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />						 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> serror, <br />						 void * ctx)<br />
</pre><p>Set the structured error callback</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>serror</tt></i>:</span></td><td>the structured error function</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the functions context</td></tr></tbody></table></div><h3><a name="xmlSchemaValidCtxtGetOptions" id="xmlSchemaValidCtxtGetOptions"></a>Function: xmlSchemaValidCtxtGetOptions</h3><pre class="programlisting">int	xmlSchemaValidCtxtGetOptions	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt)<br />
</pre><p>Get the validation context options.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the option combination or -1 on error.</td></tr></tbody></table></div><h3><a name="xmlSchemaValidCtxtGetParserCtxt" id="xmlSchemaValidCtxtGetParserCtxt"></a>Function: xmlSchemaValidCtxtGetParserCtxt</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlSchemaValidCtxtGetParserCtxt	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt)<br />
</pre><p>allow access to the parser context of the schema validation context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the parser context of the schema validation context or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlSchemaValidateDoc" id="xmlSchemaValidateDoc"></a>Function: xmlSchemaValidateDoc</h3><pre class="programlisting">int	xmlSchemaValidateDoc		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Validate a document tree in memory.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>a parsed document tree</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the document is schemas valid, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlSchemaValidateFile" id="xmlSchemaValidateFile"></a>Function: xmlSchemaValidateFile</h3><pre class="programlisting">int	xmlSchemaValidateFile		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 const char * filename, <br />					 int options)<br />
</pre><p>Do a schemas validation of the given resource, it will use the SAX streamable validation internally.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the URI of the instance</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a future set of options, currently unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the document is valid, a positive error code number otherwise and -1 in case of an internal or API error.</td></tr></tbody></table></div><h3><a name="xmlSchemaValidateOneElement" id="xmlSchemaValidateOneElement"></a>Function: xmlSchemaValidateOneElement</h3><pre class="programlisting">int	xmlSchemaValidateOneElement	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem)<br />
</pre><p>Validate a branch of a tree, starting with the given @elem.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>an element node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the element and its subtree is valid, a positive error code number otherwise and -1 in case of an internal or API error.</td></tr></tbody></table></div><h3><a name="xmlSchemaValidateSetFilename" id="xmlSchemaValidateSetFilename"></a>Function: xmlSchemaValidateSetFilename</h3><pre class="programlisting">void	xmlSchemaValidateSetFilename	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> vctxt, <br />					 const char * filename)<br />
</pre><p>Workaround to provide file error reporting information when this is not provided by current APIs</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>vctxt</tt></i>:</span></td><td>the schema validation context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file name</td></tr></tbody></table></div><h3><a name="xmlSchemaValidateSetLocator" id="xmlSchemaValidateSetLocator"></a>Function: xmlSchemaValidateSetLocator</h3><pre class="programlisting">void	xmlSchemaValidateSetLocator	(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> vctxt, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidityLocatorFunc">xmlSchemaValidityLocatorFunc</a> f, <br />					 void * ctxt)<br />
</pre><p>Allows to set a locator function to the validation context, which will be used to provide file and line information since those are not provided as part of the SAX validation flow Setting @f to NULL disable the locator.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>vctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the locator function pointer</td></tr><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the locator context</td></tr></tbody></table></div><h3><a name="xmlSchemaValidateStream" id="xmlSchemaValidateStream"></a>Function: xmlSchemaValidateStream</h3><pre class="programlisting">int	xmlSchemaValidateStream		(<a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />					 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc, <br />					 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data)<br />
</pre><p>Validate an input based on a flow of SAX event from the parser and forward the events to the @sax handler with the provided @user_data the user provided @sax handler must be a SAX2 one.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the input to use for reading the data</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>an optional encoding information</td></tr><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>a SAX handler for the resulting events</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>the context to provide to the SAX handler.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the document is schemas valid, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlSchemaValidityErrorFunc" id="xmlSchemaValidityErrorFunc"></a>Function type: xmlSchemaValidityErrorFunc</h3><pre class="programlisting">Function type: xmlSchemaValidityErrorFunc
void	xmlSchemaValidityErrorFunc	(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre><p>Signature of an error callback from an XSD validation</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the validation context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra arguments</td></tr></tbody></table></div><br />
<h3><a name="xmlSchemaValidityLocatorFunc" id="xmlSchemaValidityLocatorFunc"></a>Function type: xmlSchemaValidityLocatorFunc</h3><pre class="programlisting">Function type: xmlSchemaValidityLocatorFunc
int	xmlSchemaValidityLocatorFunc	(void * ctx, <br />					 const char ** file, <br />					 unsigned long * line)
</pre><p>A schemas validation locator, a callback called by the validator. This is used when file or node informations are not available to find out what file and line number are affected</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>user provided context</td></tr><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>returned file information</td></tr><tr><td><span class="term"><i><tt>line</tt></i>:</span></td><td>returned line information</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><br />
<h3><a name="xmlSchemaValidityWarningFunc" id="xmlSchemaValidityWarningFunc"></a>Function type: xmlSchemaValidityWarningFunc</h3><pre class="programlisting">Function type: xmlSchemaValidityWarningFunc
void	xmlSchemaValidityWarningFunc	(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre><p>Signature of a warning callback from an XSD validation</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the validation context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra arguments</td></tr></tbody></table></div><br />
<p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
