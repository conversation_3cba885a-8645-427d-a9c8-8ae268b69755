<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module uri from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module uri from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-tree.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-tree.html">tree</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-valid.html">valid</a></th><td><a accesskey="n" href="libxml-valid.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>library of generic URI related routines Implements RFC 2396 </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlURI">xmlURI</a><br />struct _xmlURI
</pre><pre class="programlisting">Typedef <a href="libxml-uri.html#xmlURI">xmlURI</a> * <a name="xmlURIPtr" id="xmlURIPtr">xmlURIPtr</a>
</pre><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlBuildRelativeURI">xmlBuildRelativeURI</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * base)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlBuildURI">xmlBuildURI</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * base)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCanonicPath">xmlCanonicPath</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * path)</pre>
<pre class="programlisting"><a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a>	<a href="#xmlCreateURI">xmlCreateURI</a>		(void)</pre>
<pre class="programlisting">void	<a href="#xmlFreeURI">xmlFreeURI</a>			(<a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> uri)</pre>
<pre class="programlisting">int	<a href="#xmlNormalizeURIPath">xmlNormalizeURIPath</a>		(char * path)</pre>
<pre class="programlisting"><a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a>	<a href="#xmlParseURI">xmlParseURI</a>		(const char * str)</pre>
<pre class="programlisting"><a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a>	<a href="#xmlParseURIRaw">xmlParseURIRaw</a>		(const char * str, <br />					 int raw)</pre>
<pre class="programlisting">int	<a href="#xmlParseURIReference">xmlParseURIReference</a>		(<a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> uri, <br />					 const char * str)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlPathToURI">xmlPathToURI</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * path)</pre>
<pre class="programlisting">void	<a href="#xmlPrintURI">xmlPrintURI</a>			(FILE * stream, <br />					 <a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> uri)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlSaveUri">xmlSaveUri</a>		(<a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> uri)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlURIEscape">xmlURIEscape</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlURIEscapeStr">xmlURIEscapeStr</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * list)</pre>
<pre class="programlisting">char *	<a href="#xmlURIUnescapeString">xmlURIUnescapeString</a>		(const char * str, <br />					 int len, <br />					 char * target)</pre>
<h2>Description</h2>
<h3><a name="xmlURI" id="xmlURI">Structure xmlURI</a></h3><pre class="programlisting">Structure xmlURI<br />struct _xmlURI {
    char *	scheme	: the URI scheme
    char *	opaque	: opaque part
    char *	authority	: the authority part
    char *	server	: the server part
    char *	user	: the user part
    int	port	: the port number
    char *	path	: the path string
    char *	query	: the query string (deprecated - use with
    char *	fragment	: the fragment identifier
    int	cleanup	: parsing potentially unclean URI
    char *	query_raw	: the query string (as it appears in the
}</pre><h3><a name="xmlBuildRelativeURI" id="xmlBuildRelativeURI"></a>Function: xmlBuildRelativeURI</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlBuildRelativeURI	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * base)<br />
</pre><p>Expresses the URI of the <a href="libxml-SAX.html#reference">reference</a> in terms relative to the base. Some examples of this operation include: base = "http://site1.com/docs/book1.html" URI input URI returned docs/pic1.gif pic1.gif docs/img/pic1.gif img/pic1.gif img/pic1.gif ../img/pic1.gif http://site1.com/docs/pic1.gif pic1.gif http://site2.com/docs/pic1.gif http://site2.com/docs/pic1.gif base = "docs/book1.html" URI input URI returned docs/pic1.gif pic1.gif docs/img/pic1.gif img/pic1.gif img/pic1.gif ../img/pic1.gif http://site1.com/docs/pic1.gif http://site1.com/docs/pic1.gif Note: if the URI <a href="libxml-SAX.html#reference">reference</a> is really wierd or complicated, it may be worthwhile to first convert it into a "nice" one by calling <a href="libxml-uri.html#xmlBuildURI">xmlBuildURI</a> (using 'base') before calling this routine, since this routine (for reasonable efficiency) assumes URI has already been through some validation.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI <a href="libxml-SAX.html#reference">reference</a> under consideration</td></tr><tr><td><span class="term"><i><tt>base</tt></i>:</span></td><td>the base value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new URI string (to be freed by the caller) or NULL in case error.</td></tr></tbody></table></div><h3><a name="xmlBuildURI" id="xmlBuildURI"></a>Function: xmlBuildURI</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlBuildURI		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * base)<br />
</pre><p>Computes he final URI of the <a href="libxml-SAX.html#reference">reference</a> done by checking that the given URI is valid, and building the final URI using the base URI. This is processed according to section 5.2 of the RFC 2396 5.2. Resolving Relative References to Absolute Form</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI instance found in the document</td></tr><tr><td><span class="term"><i><tt>base</tt></i>:</span></td><td>the base value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new URI string (to be freed by the caller) or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCanonicPath" id="xmlCanonicPath"></a>Function: xmlCanonicPath</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCanonicPath		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * path)<br />
</pre><p>Constructs a canonic path from the specified path.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>path</tt></i>:</span></td><td>the resource locator in a filesystem notation</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new canonic path, or a duplicate of the path parameter if the construction fails. The caller is responsible for freeing the memory occupied by the returned string. If there is insufficient memory available, or the argument is NULL, the function returns NULL.</td></tr></tbody></table></div><h3><a name="xmlCreateURI" id="xmlCreateURI"></a>Function: xmlCreateURI</h3><pre class="programlisting"><a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a>	xmlCreateURI		(void)<br />
</pre><p>Simply creates an empty <a href="libxml-uri.html#xmlURI">xmlURI</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new structure or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlFreeURI" id="xmlFreeURI"></a>Function: xmlFreeURI</h3><pre class="programlisting">void	xmlFreeURI			(<a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> uri)<br />
</pre><p>Free up the <a href="libxml-uri.html#xmlURI">xmlURI</a> struct</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>uri</tt></i>:</span></td><td>pointer to an <a href="libxml-uri.html#xmlURI">xmlURI</a></td></tr></tbody></table></div><h3><a name="xmlNormalizeURIPath" id="xmlNormalizeURIPath"></a>Function: xmlNormalizeURIPath</h3><pre class="programlisting">int	xmlNormalizeURIPath		(char * path)<br />
</pre><p>Applies the 5 normalization steps to a path string--that is, RFC 2396 Section 5.2, steps 6.c through 6.g. Normalization occurs directly on the string, no new allocation is done</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>path</tt></i>:</span></td><td>pointer to the path string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or an error code</td></tr></tbody></table></div><h3><a name="xmlParseURI" id="xmlParseURI"></a>Function: xmlParseURI</h3><pre class="programlisting"><a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a>	xmlParseURI		(const char * str)<br />
</pre><p>Parse an URI based on RFC 3986 URI-reference = [ absoluteURI | relativeURI ] [ "#" fragment ]</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the URI string to analyze</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a newly built <a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlParseURIRaw" id="xmlParseURIRaw"></a>Function: xmlParseURIRaw</h3><pre class="programlisting"><a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a>	xmlParseURIRaw		(const char * str, <br />					 int raw)<br />
</pre><p>Parse an URI but allows to keep intact the original fragments. URI-reference = URI / relative-ref</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the URI string to analyze</td></tr><tr><td><span class="term"><i><tt>raw</tt></i>:</span></td><td>if 1 unescaping of URI pieces are disabled</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a newly built <a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlParseURIReference" id="xmlParseURIReference"></a>Function: xmlParseURIReference</h3><pre class="programlisting">int	xmlParseURIReference		(<a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> uri, <br />					 const char * str)<br />
</pre><p>Parse an URI <a href="libxml-SAX.html#reference">reference</a> string based on RFC 3986 and fills in the appropriate fields of the @uri structure URI-reference = URI / relative-ref</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>uri</tt></i>:</span></td><td>pointer to an URI structure</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string to analyze</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or the error code</td></tr></tbody></table></div><h3><a name="xmlPathToURI" id="xmlPathToURI"></a>Function: xmlPathToURI</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlPathToURI		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * path)<br />
</pre><p>Constructs an URI expressing the existing path</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>path</tt></i>:</span></td><td>the resource locator in a filesystem notation</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new URI, or a duplicate of the path parameter if the construction fails. The caller is responsible for freeing the memory occupied by the returned string. If there is insufficient memory available, or the argument is NULL, the function returns NULL.</td></tr></tbody></table></div><h3><a name="xmlPrintURI" id="xmlPrintURI"></a>Function: xmlPrintURI</h3><pre class="programlisting">void	xmlPrintURI			(FILE * stream, <br />					 <a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> uri)<br />
</pre><p>Prints the URI in the stream @stream.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>stream</tt></i>:</span></td><td>a FILE* for the output</td></tr><tr><td><span class="term"><i><tt>uri</tt></i>:</span></td><td>pointer to an <a href="libxml-uri.html#xmlURI">xmlURI</a></td></tr></tbody></table></div><h3><a name="xmlSaveUri" id="xmlSaveUri"></a>Function: xmlSaveUri</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlSaveUri		(<a href="libxml-uri.html#xmlURIPtr">xmlURIPtr</a> uri)<br />
</pre><p>Save the URI as an escaped string</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>uri</tt></i>:</span></td><td>pointer to an <a href="libxml-uri.html#xmlURI">xmlURI</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new string (to be deallocated by caller)</td></tr></tbody></table></div><h3><a name="xmlURIEscape" id="xmlURIEscape"></a>Function: xmlURIEscape</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlURIEscape		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>Escaping routine, does not do validity checks ! It will try to escape the chars needing this, but this is heuristic based it's impossible to be sure.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string of the URI to escape</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an copy of the string, but escaped 25 May 2001 Uses <a href="libxml-uri.html#xmlParseURI">xmlParseURI</a> and <a href="libxml-uri.html#xmlURIEscapeStr">xmlURIEscapeStr</a> to try to escape correctly according to RFC2396. - Carl Douglas</td></tr></tbody></table></div><h3><a name="xmlURIEscapeStr" id="xmlURIEscapeStr"></a>Function: xmlURIEscapeStr</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlURIEscapeStr		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * list)<br />
</pre><p>This routine escapes a string to hex, ignoring reserved <a href="libxml-SAX.html#characters">characters</a> (a-z) and the <a href="libxml-SAX.html#characters">characters</a> in the exception list.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>string to escape</td></tr><tr><td><span class="term"><i><tt>list</tt></i>:</span></td><td>exception list string of chars not to escape</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new escaped string or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlURIUnescapeString" id="xmlURIUnescapeString"></a>Function: xmlURIUnescapeString</h3><pre class="programlisting">char *	xmlURIUnescapeString		(const char * str, <br />					 int len, <br />					 char * target)<br />
</pre><p>Unescaping routine, but does not check that the string is an URI. The output is a direct unsigned char translation of %XX values (no encoding) Note that the length of the result can only be smaller or same size as the input string.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string to unescape</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length in bytes to unescape (or &lt;= 0 to indicate full string)</td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>optional destination buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a copy of the string, but unescaped, will return NULL only in case of error</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
