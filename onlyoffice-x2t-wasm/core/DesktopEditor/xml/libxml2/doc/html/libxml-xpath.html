<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xpath from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xpath from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlwriter.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlwriter.html">xmlwriter</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xpathInternals.html">xpathInternals</a></th><td><a accesskey="n" href="libxml-xpathInternals.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>API for the XML Path Language implementation  XML Path Language implementation XPath is a language for addressing parts of an XML document, designed to be used by both XSLT and XPointer</p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#XML_XPATH_CHECKNS">XML_XPATH_CHECKNS</a></pre><pre class="programlisting">#define <a href="#XML_XPATH_NOVAR">XML_XPATH_NOVAR</a></pre><pre class="programlisting">#define <a href="#xmlXPathNodeSetGetLength">xmlXPathNodeSetGetLength</a></pre><pre class="programlisting">#define <a href="#xmlXPathNodeSetIsEmpty">xmlXPathNodeSetIsEmpty</a></pre><pre class="programlisting">#define <a href="#xmlXPathNodeSetItem">xmlXPathNodeSetItem</a></pre><pre class="programlisting">Structure <a href="#xmlNodeSet">xmlNodeSet</a><br />struct _xmlNodeSet
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlNodeSet">xmlNodeSet</a> * <a name="xmlNodeSetPtr" id="xmlNodeSetPtr">xmlNodeSetPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlXPathAxis">xmlXPathAxis</a><br />struct _xmlXPathAxis
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlXPathAxis">xmlXPathAxis</a> * <a name="xmlXPathAxisPtr" id="xmlXPathAxisPtr">xmlXPathAxisPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlXPathCompExpr">xmlXPathCompExpr</a><br />struct _xmlXPathCompExpr
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlXPathCompExpr">xmlXPathCompExpr</a> * <a name="xmlXPathCompExprPtr" id="xmlXPathCompExprPtr">xmlXPathCompExprPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlXPathContext">xmlXPathContext</a><br />struct _xmlXPathContext
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlXPathContext">xmlXPathContext</a> * <a name="xmlXPathContextPtr" id="xmlXPathContextPtr">xmlXPathContextPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlXPathError">xmlXPathError</a>
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlXPathFunct">xmlXPathFunct</a> * <a name="xmlXPathFuncPtr" id="xmlXPathFuncPtr">xmlXPathFuncPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlXPathFunct">xmlXPathFunct</a><br />struct _xmlXPathFunct
</pre><pre class="programlisting">Structure <a href="#xmlXPathObject">xmlXPathObject</a><br />struct _xmlXPathObject
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlXPathObject">xmlXPathObject</a> * <a name="xmlXPathObjectPtr" id="xmlXPathObjectPtr">xmlXPathObjectPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlXPathObjectType">xmlXPathObjectType</a>
</pre><pre class="programlisting">Structure <a href="#xmlXPathParserContext">xmlXPathParserContext</a><br />struct _xmlXPathParserContext
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlXPathParserContext">xmlXPathParserContext</a> * <a name="xmlXPathParserContextPtr" id="xmlXPathParserContextPtr">xmlXPathParserContextPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlXPathType">xmlXPathType</a><br />struct _xmlXPathType
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlXPathType">xmlXPathType</a> * <a name="xmlXPathTypePtr" id="xmlXPathTypePtr">xmlXPathTypePtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlXPathVariable">xmlXPathVariable</a><br />struct _xmlXPathVariable
</pre><pre class="programlisting">Typedef <a href="libxml-xpath.html#xmlXPathVariable">xmlXPathVariable</a> * <a name="xmlXPathVariablePtr" id="xmlXPathVariablePtr">xmlXPathVariablePtr</a>
</pre><pre class="programlisting">Function type: <a href="#xmlXPathAxisFunc">xmlXPathAxisFunc</a>
<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathAxisFunc">xmlXPathAxisFunc</a>	(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br />						 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> cur)
</pre>
<pre class="programlisting">double	<a href="#xmlXPathCastBooleanToNumber">xmlXPathCastBooleanToNumber</a>	(int val)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathCastBooleanToString">xmlXPathCastBooleanToString</a>	(int val)</pre>
<pre class="programlisting">int	<a href="#xmlXPathCastNodeSetToBoolean">xmlXPathCastNodeSetToBoolean</a>	(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> ns)</pre>
<pre class="programlisting">double	<a href="#xmlXPathCastNodeSetToNumber">xmlXPathCastNodeSetToNumber</a>	(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> ns)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathCastNodeSetToString">xmlXPathCastNodeSetToString</a>	(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> ns)</pre>
<pre class="programlisting">double	<a href="#xmlXPathCastNodeToNumber">xmlXPathCastNodeToNumber</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathCastNodeToString">xmlXPathCastNodeToString</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">int	<a href="#xmlXPathCastNumberToBoolean">xmlXPathCastNumberToBoolean</a>	(double val)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathCastNumberToString">xmlXPathCastNumberToString</a>	(double val)</pre>
<pre class="programlisting">int	<a href="#xmlXPathCastStringToBoolean">xmlXPathCastStringToBoolean</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * val)</pre>
<pre class="programlisting">double	<a href="#xmlXPathCastStringToNumber">xmlXPathCastStringToNumber</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * val)</pre>
<pre class="programlisting">int	<a href="#xmlXPathCastToBoolean">xmlXPathCastToBoolean</a>		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting">double	<a href="#xmlXPathCastToNumber">xmlXPathCastToNumber</a>		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathCastToString">xmlXPathCastToString</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting">int	<a href="#xmlXPathCmpNodes">xmlXPathCmpNodes</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node1, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a>	<a href="#xmlXPathCompile">xmlXPathCompile</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathCompiledEval">xmlXPathCompiledEval</a>	(<a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> comp, <br />						 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)</pre>
<pre class="programlisting">int	<a href="#xmlXPathCompiledEvalToBoolean">xmlXPathCompiledEvalToBoolean</a>	(<a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> comp, <br />					 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlXPathContextSetCache">xmlXPathContextSetCache</a>		(<a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br />					 int active, <br />					 int value, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathConvertBoolean">xmlXPathConvertBoolean</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting">Function type: <a href="#xmlXPathConvertFunc">xmlXPathConvertFunc</a>
int	<a href="#xmlXPathConvertFunc">xmlXPathConvertFunc</a>		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> obj, <br />					 int type)
</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathConvertNumber">xmlXPathConvertNumber</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathConvertString">xmlXPathConvertString</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a>	<a href="#xmlXPathCtxtCompile">xmlXPathCtxtCompile</a>	(<a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathEval">xmlXPathEval</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathEvalExpression">xmlXPathEvalExpression</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />						 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)</pre>
<pre class="programlisting">Function type: <a href="#xmlXPathEvalFunc">xmlXPathEvalFunc</a>
void	<a href="#xmlXPathEvalFunc">xmlXPathEvalFunc</a>		(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br />					 int nargs)
</pre>
<pre class="programlisting">int	<a href="#xmlXPathEvalPredicate">xmlXPathEvalPredicate</a>		(<a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br />					 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> res)</pre>
<pre class="programlisting">void	<a href="#xmlXPathFreeCompExpr">xmlXPathFreeCompExpr</a>		(<a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> comp)</pre>
<pre class="programlisting">void	<a href="#xmlXPathFreeContext">xmlXPathFreeContext</a>		(<a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)</pre>
<pre class="programlisting">void	<a href="#xmlXPathFreeNodeSet">xmlXPathFreeNodeSet</a>		(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> obj)</pre>
<pre class="programlisting">void	<a href="#xmlXPathFreeNodeSetList">xmlXPathFreeNodeSetList</a>		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> obj)</pre>
<pre class="programlisting">void	<a href="#xmlXPathFreeObject">xmlXPathFreeObject</a>		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> obj)</pre>
<pre class="programlisting">Function type: <a href="#xmlXPathFuncLookupFunc">xmlXPathFuncLookupFunc</a>
<a href="libxml-xpath.html#xmlXPathFunction">xmlXPathFunction</a>	<a href="#xmlXPathFuncLookupFunc">xmlXPathFuncLookupFunc</a>	(void * ctxt, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns_uri)
</pre>
<pre class="programlisting">Function type: <a href="#xmlXPathFunction">xmlXPathFunction</a>
void	<a href="#xmlXPathFunction">xmlXPathFunction</a>		(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br />					 int nargs)
</pre>
<pre class="programlisting">void	<a href="#xmlXPathInit">xmlXPathInit</a>			(void)</pre>
<pre class="programlisting">int	<a href="#xmlXPathIsInf">xmlXPathIsInf</a>			(double val)</pre>
<pre class="programlisting">int	<a href="#xmlXPathIsNaN">xmlXPathIsNaN</a>			(double val)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a>	<a href="#xmlXPathNewContext">xmlXPathNewContext</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathNodeEval">xmlXPathNodeEval</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />						 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathNodeSetCreate">xmlXPathNodeSetCreate</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> val)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathObjectCopy">xmlXPathObjectCopy</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting">long	<a href="#xmlXPathOrderDocElems">xmlXPathOrderDocElems</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">int	<a href="#xmlXPathSetContextNode">xmlXPathSetContextNode</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)</pre>
<pre class="programlisting">Function type: <a href="#xmlXPathVariableLookupFunc">xmlXPathVariableLookupFunc</a>
<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathVariableLookupFunc">xmlXPathVariableLookupFunc</a>	(void * ctxt, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns_uri)
</pre>
<h2>Description</h2>
<h3><a name="XML_XPATH_CHECKNS" id="XML_XPATH_CHECKNS"></a>Macro: XML_XPATH_CHECKNS</h3><pre>#define XML_XPATH_CHECKNS</pre><p>check namespaces at compilation</p>
<h3><a name="XML_XPATH_NOVAR" id="XML_XPATH_NOVAR"></a>Macro: XML_XPATH_NOVAR</h3><pre>#define XML_XPATH_NOVAR</pre><p>forbid variables in expression</p>
<h3><a name="xmlXPathNodeSetGetLength" id="xmlXPathNodeSetGetLength"></a>Macro: xmlXPathNodeSetGetLength</h3><pre>#define xmlXPathNodeSetGetLength</pre><p>Implement a functionality similar to the DOM NodeList.length. Returns the number of nodes in the node-set.</p>
<h3><a name="xmlXPathNodeSetIsEmpty" id="xmlXPathNodeSetIsEmpty"></a>Macro: xmlXPathNodeSetIsEmpty</h3><pre>#define xmlXPathNodeSetIsEmpty</pre><p>Checks whether @ns is empty or not. Returns %TRUE if @ns is an empty node-set.</p>
<h3><a name="xmlXPathNodeSetItem" id="xmlXPathNodeSetItem"></a>Macro: xmlXPathNodeSetItem</h3><pre>#define xmlXPathNodeSetItem</pre><p>Implements a functionality similar to the DOM NodeList.item(). Returns the <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> at the given @index in @ns or NULL if @index is out of range (0 to length-1)</p>
<h3><a name="xmlNodeSet" id="xmlNodeSet">Structure xmlNodeSet</a></h3><pre class="programlisting">Structure xmlNodeSet<br />struct _xmlNodeSet {
    int	nodeNr	: number of nodes in the set
    int	nodeMax	: size of the array as allocated
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> *	nodeTab	: array of nodes in no particular order @
}</pre><h3><a name="xmlXPathAxis" id="xmlXPathAxis">Structure xmlXPathAxis</a></h3><pre class="programlisting">Structure xmlXPathAxis<br />struct _xmlXPathAxis {
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: the axis name
    <a href="libxml-xpath.html#xmlXPathAxisFunc">xmlXPathAxisFunc</a>	func	: the search function
}</pre><h3><a name="xmlXPathCompExpr" id="xmlXPathCompExpr">Structure xmlXPathCompExpr</a></h3><pre class="programlisting">Structure xmlXPathCompExpr<br />struct _xmlXPathCompExpr {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlXPathContext" id="xmlXPathContext">Structure xmlXPathContext</a></h3><pre class="programlisting">Structure xmlXPathContext<br />struct _xmlXPathContext {
    <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	doc	: The current document
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node	: The current node
    int	nb_variables_unused	: unused (hash table)
    int	max_variables_unused	: unused (hash table)
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	varHash	: Hash table of defined variables
    int	nb_types	: number of defined types
    int	max_types	: max number of types
    <a href="libxml-xpath.html#xmlXPathTypePtr">xmlXPathTypePtr</a>	types	: Array of defined types
    int	nb_funcs_unused	: unused (hash table)
    int	max_funcs_unused	: unused (hash table)
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	funcHash	: Hash table of defined funcs
    int	nb_axis	: number of defined axis
    int	max_axis	: max number of axis
    <a href="libxml-xpath.html#xmlXPathAxisPtr">xmlXPathAxisPtr</a>	axis	: Array of defined axis the namespace nod
    <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> *	namespaces	: Array of namespaces
    int	nsNr	: number of namespace in scope
    void *	user	: function to free extra variables
    int	contextSize	: the context size
    int	proximityPosition	: the proximity position extra stuff for
    int	xptr	: is this an XPointer context?
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	here	: for here()
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	origin	: for origin() the set of namespace decla
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	nsHash	: The namespaces hash table
    <a href="libxml-xpath.html#xmlXPathVariableLookupFunc">xmlXPathVariableLookupFunc</a>	varLookupFunc	: variable lookup func
    void *	varLookupData	: variable lookup data Possibility to lin
    void *	extra	: needed for XSLT The function name and U
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	function
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	functionURI	: function lookup function and data
    <a href="libxml-xpath.html#xmlXPathFuncLookupFunc">xmlXPathFuncLookupFunc</a>	funcLookupFunc	: function lookup func
    void *	funcLookupData	: function lookup data temporary namespac
    <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> *	tmpNsList	: Array of namespaces
    int	tmpNsNr	: number of namespaces in scope error rep
    void *	userData	: user specific data block
    <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a>	error	: the callback in case of errors
    <a href="libxml-xmlerror.html#xmlError">xmlError</a>	lastError	: the last error
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	debugNode	: the source node XSLT dictionary
    <a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a>	dict	: dictionary if any
    int	flags	: flags to control compilation Cache for
    void *	cache
}</pre><h3>Enum <a name="xmlXPathError" id="xmlXPathError">xmlXPathError</a></h3><pre class="programlisting">Enum xmlXPathError {
    <a name="XPATH_EXPRESSION_OK" id="XPATH_EXPRESSION_OK">XPATH_EXPRESSION_OK</a> = 0
    <a name="XPATH_NUMBER_ERROR" id="XPATH_NUMBER_ERROR">XPATH_NUMBER_ERROR</a> = 1
    <a name="XPATH_UNFINISHED_LITERAL_ERROR" id="XPATH_UNFINISHED_LITERAL_ERROR">XPATH_UNFINISHED_LITERAL_ERROR</a> = 2
    <a name="XPATH_START_LITERAL_ERROR" id="XPATH_START_LITERAL_ERROR">XPATH_START_LITERAL_ERROR</a> = 3
    <a name="XPATH_VARIABLE_REF_ERROR" id="XPATH_VARIABLE_REF_ERROR">XPATH_VARIABLE_REF_ERROR</a> = 4
    <a name="XPATH_UNDEF_VARIABLE_ERROR" id="XPATH_UNDEF_VARIABLE_ERROR">XPATH_UNDEF_VARIABLE_ERROR</a> = 5
    <a name="XPATH_INVALID_PREDICATE_ERROR" id="XPATH_INVALID_PREDICATE_ERROR">XPATH_INVALID_PREDICATE_ERROR</a> = 6
    <a name="XPATH_EXPR_ERROR" id="XPATH_EXPR_ERROR">XPATH_EXPR_ERROR</a> = 7
    <a name="XPATH_UNCLOSED_ERROR" id="XPATH_UNCLOSED_ERROR">XPATH_UNCLOSED_ERROR</a> = 8
    <a name="XPATH_UNKNOWN_FUNC_ERROR" id="XPATH_UNKNOWN_FUNC_ERROR">XPATH_UNKNOWN_FUNC_ERROR</a> = 9
    <a name="XPATH_INVALID_OPERAND" id="XPATH_INVALID_OPERAND">XPATH_INVALID_OPERAND</a> = 10
    <a name="XPATH_INVALID_TYPE" id="XPATH_INVALID_TYPE">XPATH_INVALID_TYPE</a> = 11
    <a name="XPATH_INVALID_ARITY" id="XPATH_INVALID_ARITY">XPATH_INVALID_ARITY</a> = 12
    <a name="XPATH_INVALID_CTXT_SIZE" id="XPATH_INVALID_CTXT_SIZE">XPATH_INVALID_CTXT_SIZE</a> = 13
    <a name="XPATH_INVALID_CTXT_POSITION" id="XPATH_INVALID_CTXT_POSITION">XPATH_INVALID_CTXT_POSITION</a> = 14
    <a name="XPATH_MEMORY_ERROR" id="XPATH_MEMORY_ERROR">XPATH_MEMORY_ERROR</a> = 15
    <a name="XPTR_SYNTAX_ERROR" id="XPTR_SYNTAX_ERROR">XPTR_SYNTAX_ERROR</a> = 16
    <a name="XPTR_RESOURCE_ERROR" id="XPTR_RESOURCE_ERROR">XPTR_RESOURCE_ERROR</a> = 17
    <a name="XPTR_SUB_RESOURCE_ERROR" id="XPTR_SUB_RESOURCE_ERROR">XPTR_SUB_RESOURCE_ERROR</a> = 18
    <a name="XPATH_UNDEF_PREFIX_ERROR" id="XPATH_UNDEF_PREFIX_ERROR">XPATH_UNDEF_PREFIX_ERROR</a> = 19
    <a name="XPATH_ENCODING_ERROR" id="XPATH_ENCODING_ERROR">XPATH_ENCODING_ERROR</a> = 20
    <a name="XPATH_INVALID_CHAR_ERROR" id="XPATH_INVALID_CHAR_ERROR">XPATH_INVALID_CHAR_ERROR</a> = 21
    <a name="XPATH_INVALID_CTXT" id="XPATH_INVALID_CTXT">XPATH_INVALID_CTXT</a> = 22
    <a name="XPATH_STACK_ERROR" id="XPATH_STACK_ERROR">XPATH_STACK_ERROR</a> = 23
    <a name="XPATH_FORBID_VARIABLE_ERROR" id="XPATH_FORBID_VARIABLE_ERROR">XPATH_FORBID_VARIABLE_ERROR</a> = 24
}
</pre><h3><a name="xmlXPathFunct" id="xmlXPathFunct">Structure xmlXPathFunct</a></h3><pre class="programlisting">Structure xmlXPathFunct<br />struct _xmlXPathFunct {
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: the function name
    <a href="libxml-xpath.html#xmlXPathEvalFunc">xmlXPathEvalFunc</a>	func	: the evaluation function
}</pre><h3><a name="xmlXPathObject" id="xmlXPathObject">Structure xmlXPathObject</a></h3><pre class="programlisting">Structure xmlXPathObject<br />struct _xmlXPathObject {
    <a href="libxml-xpath.html#xmlXPathObjectType">xmlXPathObjectType</a>	type
    <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	nodesetval
    int	boolval
    double	floatval
    <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	stringval
    void *	user
    int	index
    void *	user2
    int	index2
}</pre><h3>Enum <a name="xmlXPathObjectType" id="xmlXPathObjectType">xmlXPathObjectType</a></h3><pre class="programlisting">Enum xmlXPathObjectType {
    <a name="XPATH_UNDEFINED" id="XPATH_UNDEFINED">XPATH_UNDEFINED</a> = 0
    <a name="XPATH_NODESET" id="XPATH_NODESET">XPATH_NODESET</a> = 1
    <a name="XPATH_BOOLEAN" id="XPATH_BOOLEAN">XPATH_BOOLEAN</a> = 2
    <a name="XPATH_NUMBER" id="XPATH_NUMBER">XPATH_NUMBER</a> = 3
    <a name="XPATH_STRING" id="XPATH_STRING">XPATH_STRING</a> = 4
    <a name="XPATH_POINT" id="XPATH_POINT">XPATH_POINT</a> = 5
    <a name="XPATH_RANGE" id="XPATH_RANGE">XPATH_RANGE</a> = 6
    <a name="XPATH_LOCATIONSET" id="XPATH_LOCATIONSET">XPATH_LOCATIONSET</a> = 7
    <a name="XPATH_USERS" id="XPATH_USERS">XPATH_USERS</a> = 8
    <a name="XPATH_XSLT_TREE" id="XPATH_XSLT_TREE">XPATH_XSLT_TREE</a> = 9 : An XSLT value tree, non modifiable
}
</pre><h3><a name="xmlXPathParserContext" id="xmlXPathParserContext">Structure xmlXPathParserContext</a></h3><pre class="programlisting">Structure xmlXPathParserContext<br />struct _xmlXPathParserContext {
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	cur	: the current char being parsed
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	base	: the full expression
    int	error	: error code
    <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a>	context	: the evaluation context
    <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	value	: the current value
    int	valueNr	: number of values stacked
    int	valueMax	: max number of values stacked
    <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> *	valueTab	: stack of values
    <a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a>	comp	: the precompiled expression
    int	xptr	: it this an XPointer expression
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	ancestor	: used for walking preceding axis
    int	valueFrame	: used to limit Pop on the stack
}</pre><h3><a name="xmlXPathType" id="xmlXPathType">Structure xmlXPathType</a></h3><pre class="programlisting">Structure xmlXPathType<br />struct _xmlXPathType {
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: the type name
    <a href="libxml-xpath.html#xmlXPathConvertFunc">xmlXPathConvertFunc</a>	func	: the conversion function
}</pre><h3><a name="xmlXPathVariable" id="xmlXPathVariable">Structure xmlXPathVariable</a></h3><pre class="programlisting">Structure xmlXPathVariable<br />struct _xmlXPathVariable {
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: the variable name
    <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	value	: the value
}</pre><h3><a name="xmlXPathAxisFunc" id="xmlXPathAxisFunc"></a>Function type: xmlXPathAxisFunc</h3><pre class="programlisting">Function type: xmlXPathAxisFunc
<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathAxisFunc	(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br />						 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> cur)
</pre><p>An axis traversal function. To traverse an axis, the engine calls the first time with cur == NULL and repeat until the function returns NULL indicating the end of the axis traversal.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath interpreter context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the previous node being explored on that axis</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next node in that axis or NULL if at the end of the axis.</td></tr></tbody></table></div><br />
<h3><a name="xmlXPathCastBooleanToNumber" id="xmlXPathCastBooleanToNumber"></a>Function: xmlXPathCastBooleanToNumber</h3><pre class="programlisting">double	xmlXPathCastBooleanToNumber	(int val)<br />
</pre><p>Converts a boolean to its number value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a boolean</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number value</td></tr></tbody></table></div><h3><a name="xmlXPathCastBooleanToString" id="xmlXPathCastBooleanToString"></a>Function: xmlXPathCastBooleanToString</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathCastBooleanToString	(int val)<br />
</pre><p>Converts a boolean to its string value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a boolean</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a newly allocated string.</td></tr></tbody></table></div><h3><a name="xmlXPathCastNodeSetToBoolean" id="xmlXPathCastNodeSetToBoolean"></a>Function: xmlXPathCastNodeSetToBoolean</h3><pre class="programlisting">int	xmlXPathCastNodeSetToBoolean	(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> ns)<br />
</pre><p>Converts a node-set to its boolean value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the boolean value</td></tr></tbody></table></div><h3><a name="xmlXPathCastNodeSetToNumber" id="xmlXPathCastNodeSetToNumber"></a>Function: xmlXPathCastNodeSetToNumber</h3><pre class="programlisting">double	xmlXPathCastNodeSetToNumber	(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> ns)<br />
</pre><p>Converts a node-set to its number value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number value</td></tr></tbody></table></div><h3><a name="xmlXPathCastNodeSetToString" id="xmlXPathCastNodeSetToString"></a>Function: xmlXPathCastNodeSetToString</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathCastNodeSetToString	(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> ns)<br />
</pre><p>Converts a node-set to its string value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a newly allocated string.</td></tr></tbody></table></div><h3><a name="xmlXPathCastNodeToNumber" id="xmlXPathCastNodeToNumber"></a>Function: xmlXPathCastNodeToNumber</h3><pre class="programlisting">double	xmlXPathCastNodeToNumber	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Converts a node to its number value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number value</td></tr></tbody></table></div><h3><a name="xmlXPathCastNodeToString" id="xmlXPathCastNodeToString"></a>Function: xmlXPathCastNodeToString</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathCastNodeToString	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Converts a node to its string value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a newly allocated string.</td></tr></tbody></table></div><h3><a name="xmlXPathCastNumberToBoolean" id="xmlXPathCastNumberToBoolean"></a>Function: xmlXPathCastNumberToBoolean</h3><pre class="programlisting">int	xmlXPathCastNumberToBoolean	(double val)<br />
</pre><p>Converts a number to its boolean value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a number</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the boolean value</td></tr></tbody></table></div><h3><a name="xmlXPathCastNumberToString" id="xmlXPathCastNumberToString"></a>Function: xmlXPathCastNumberToString</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathCastNumberToString	(double val)<br />
</pre><p>Converts a number to its string value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a number</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a newly allocated string.</td></tr></tbody></table></div><h3><a name="xmlXPathCastStringToBoolean" id="xmlXPathCastStringToBoolean"></a>Function: xmlXPathCastStringToBoolean</h3><pre class="programlisting">int	xmlXPathCastStringToBoolean	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * val)<br />
</pre><p>Converts a string to its boolean value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the boolean value</td></tr></tbody></table></div><h3><a name="xmlXPathCastStringToNumber" id="xmlXPathCastStringToNumber"></a>Function: xmlXPathCastStringToNumber</h3><pre class="programlisting">double	xmlXPathCastStringToNumber	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * val)<br />
</pre><p>Converts a string to its number value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number value</td></tr></tbody></table></div><h3><a name="xmlXPathCastToBoolean" id="xmlXPathCastToBoolean"></a>Function: xmlXPathCastToBoolean</h3><pre class="programlisting">int	xmlXPathCastToBoolean		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>Converts an XPath object to its boolean value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an XPath object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the boolean value</td></tr></tbody></table></div><h3><a name="xmlXPathCastToNumber" id="xmlXPathCastToNumber"></a>Function: xmlXPathCastToNumber</h3><pre class="programlisting">double	xmlXPathCastToNumber		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>Converts an XPath object to its number value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an XPath object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number value</td></tr></tbody></table></div><h3><a name="xmlXPathCastToString" id="xmlXPathCastToString"></a>Function: xmlXPathCastToString</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathCastToString	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>Converts an existing object to its string() equivalent</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an XPath object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the allocated string value of the object, NULL in case of error. It's up to the caller to free the string memory with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlXPathCmpNodes" id="xmlXPathCmpNodes"></a>Function: xmlXPathCmpNodes</h3><pre class="programlisting">int	xmlXPathCmpNodes		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node1, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Compare two nodes w.r.t document order</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node1</tt></i>:</span></td><td>the first node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>the second node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-2 in case of error 1 if first point &lt; second point, 0 if it's the same node, -1 otherwise</td></tr></tbody></table></div><h3><a name="xmlXPathCompile" id="xmlXPathCompile"></a>Function: xmlXPathCompile</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a>	xmlXPathCompile	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>Compile an XPath expression</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the XPath expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> resulting from the compilation or NULL. the caller has to free the object.</td></tr></tbody></table></div><h3><a name="xmlXPathCompiledEval" id="xmlXPathCompiledEval"></a>Function: xmlXPathCompiledEval</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathCompiledEval	(<a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> comp, <br />						 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)<br />
</pre><p>Evaluate the Precompiled XPath expression in the given context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the compiled XPath expression</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> resulting from the evaluation or NULL. the caller has to free the object.</td></tr></tbody></table></div><h3><a name="xmlXPathCompiledEvalToBoolean" id="xmlXPathCompiledEvalToBoolean"></a>Function: xmlXPathCompiledEvalToBoolean</h3><pre class="programlisting">int	xmlXPathCompiledEvalToBoolean	(<a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> comp, <br />					 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)<br />
</pre><p>Applies the XPath boolean() function on the result of the given compiled expression.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the compiled XPath expression</td></tr><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the expression evaluated to true, 0 if to false and -1 in API and internal errors.</td></tr></tbody></table></div><h3><a name="xmlXPathContextSetCache" id="xmlXPathContextSetCache"></a>Function: xmlXPathContextSetCache</h3><pre class="programlisting">int	xmlXPathContextSetCache		(<a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br />					 int active, <br />					 int value, <br />					 int options)<br />
</pre><p>Creates/frees an object cache on the XPath context. If activates XPath objects (xmlXPathObject) will be cached internally to be reused. @options: 0: This will set the XPath object caching: @value: This will set the maximum number of XPath objects to be cached per slot There are 5 slots for: node-set, string, number, boolean, and misc objects. Use &lt;0 for the default number (100). Other values for @options have currently no effect.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>active</tt></i>:</span></td><td>enables/disables (creates/frees) the cache</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>a value with semantics dependant on @options</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>options (currently only the value 0 is used)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the setting succeeded, and -1 on API or internal errors.</td></tr></tbody></table></div><h3><a name="xmlXPathConvertBoolean" id="xmlXPathConvertBoolean"></a>Function: xmlXPathConvertBoolean</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathConvertBoolean	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>Converts an existing object to its boolean() equivalent</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an XPath object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new object, the old one is freed (or the operation is done directly on @val)</td></tr></tbody></table></div><h3><a name="xmlXPathConvertFunc" id="xmlXPathConvertFunc"></a>Function type: xmlXPathConvertFunc</h3><pre class="programlisting">Function type: xmlXPathConvertFunc
int	xmlXPathConvertFunc		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> obj, <br />					 int type)
</pre><p>A conversion function is associated to a type and used to cast the new type to primitive values.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>obj</tt></i>:</span></td><td>an XPath object</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the number of the target type</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, 0 otherwise</td></tr></tbody></table></div><br />
<h3><a name="xmlXPathConvertNumber" id="xmlXPathConvertNumber"></a>Function: xmlXPathConvertNumber</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathConvertNumber	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>Converts an existing object to its number() equivalent</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an XPath object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new object, the old one is freed (or the operation is done directly on @val)</td></tr></tbody></table></div><h3><a name="xmlXPathConvertString" id="xmlXPathConvertString"></a>Function: xmlXPathConvertString</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathConvertString	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>Converts an existing object to its string() equivalent</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an XPath object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new object, the old one is freed (or the operation is done directly on @val)</td></tr></tbody></table></div><h3><a name="xmlXPathCtxtCompile" id="xmlXPathCtxtCompile"></a>Function: xmlXPathCtxtCompile</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a>	xmlXPathCtxtCompile	(<a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>Compile an XPath expression</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath context</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the XPath expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> resulting from the compilation or NULL. the caller has to free the object.</td></tr></tbody></table></div><h3><a name="xmlXPathEval" id="xmlXPathEval"></a>Function: xmlXPathEval</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathEval	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)<br />
</pre><p>Evaluate the XPath Location Path in the given context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the XPath expression</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> resulting from the evaluation or NULL. the caller has to free the object.</td></tr></tbody></table></div><h3><a name="xmlXPathEvalExpression" id="xmlXPathEvalExpression"></a>Function: xmlXPathEvalExpression</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathEvalExpression	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />						 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)<br />
</pre><p>Evaluate the XPath expression in the given context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the XPath expression</td></tr><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> resulting from the evaluation or NULL. the caller has to free the object.</td></tr></tbody></table></div><h3><a name="xmlXPathEvalFunc" id="xmlXPathEvalFunc"></a>Function type: xmlXPathEvalFunc</h3><pre class="programlisting">Function type: xmlXPathEvalFunc
void	xmlXPathEvalFunc		(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br />					 int nargs)
</pre><p>An XPath evaluation function, the parameters are on the XPath context stack.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments passed to the function</td></tr></tbody></table></div><br />
<h3><a name="xmlXPathEvalPredicate" id="xmlXPathEvalPredicate"></a>Function: xmlXPathEvalPredicate</h3><pre class="programlisting">int	xmlXPathEvalPredicate		(<a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br />					 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> res)<br />
</pre><p>Evaluate a predicate result for the current node. A PredicateExpr is evaluated by evaluating the Expr and converting the result to a boolean. If the result is a number, the result will be converted to true if the number is equal to the position of the context node in the context node list (as returned by the position function) and will be converted to false otherwise; if the result is not a number, then the result will be converted as if by a call to the boolean function.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>res</tt></i>:</span></td><td>the Predicate Expression evaluation result</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if predicate is true, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlXPathFreeCompExpr" id="xmlXPathFreeCompExpr"></a>Function: xmlXPathFreeCompExpr</h3><pre class="programlisting">void	xmlXPathFreeCompExpr		(<a href="libxml-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> comp)<br />
</pre><p>Free up the memory allocated by @comp</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>an XPATH comp</td></tr></tbody></table></div><h3><a name="xmlXPathFreeContext" id="xmlXPathFreeContext"></a>Function: xmlXPathFreeContext</h3><pre class="programlisting">void	xmlXPathFreeContext		(<a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)<br />
</pre><p>Free up an <a href="libxml-xpath.html#xmlXPathContext">xmlXPathContext</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the context to free</td></tr></tbody></table></div><h3><a name="xmlXPathFreeNodeSet" id="xmlXPathFreeNodeSet"></a>Function: xmlXPathFreeNodeSet</h3><pre class="programlisting">void	xmlXPathFreeNodeSet		(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> obj)<br />
</pre><p>Free the NodeSet compound (not the actual nodes !).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>obj</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> to free</td></tr></tbody></table></div><h3><a name="xmlXPathFreeNodeSetList" id="xmlXPathFreeNodeSetList"></a>Function: xmlXPathFreeNodeSetList</h3><pre class="programlisting">void	xmlXPathFreeNodeSetList		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> obj)<br />
</pre><p>Free up the <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> @obj but don't deallocate the objects in the list contrary to xmlXPathFreeObject().</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>obj</tt></i>:</span></td><td>an existing NodeSetList object</td></tr></tbody></table></div><h3><a name="xmlXPathFreeObject" id="xmlXPathFreeObject"></a>Function: xmlXPathFreeObject</h3><pre class="programlisting">void	xmlXPathFreeObject		(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> obj)<br />
</pre><p>Free up an <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> object.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>obj</tt></i>:</span></td><td>the object to free</td></tr></tbody></table></div><h3><a name="xmlXPathFuncLookupFunc" id="xmlXPathFuncLookupFunc"></a>Function type: xmlXPathFuncLookupFunc</h3><pre class="programlisting">Function type: xmlXPathFuncLookupFunc
<a href="libxml-xpath.html#xmlXPathFunction">xmlXPathFunction</a>	xmlXPathFuncLookupFunc	(void * ctxt, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns_uri)
</pre><p>Prototype for callbacks used to plug function lookup in the XPath engine.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>name of the function</td></tr><tr><td><span class="term"><i><tt>ns_uri</tt></i>:</span></td><td>the namespace name hosting this function</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the XPath function or NULL if not found.</td></tr></tbody></table></div><br />
<h3><a name="xmlXPathFunction" id="xmlXPathFunction"></a>Function type: xmlXPathFunction</h3><pre class="programlisting">Function type: xmlXPathFunction
void	xmlXPathFunction		(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br />					 int nargs)
</pre><p>An XPath function. The arguments (if any) are popped out from the context stack and the result is pushed on the stack.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath interprestation context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div><br />
<h3><a name="xmlXPathInit" id="xmlXPathInit"></a>Function: xmlXPathInit</h3><pre class="programlisting">void	xmlXPathInit			(void)<br />
</pre><p>Initialize the XPath environment</p>
<h3><a name="xmlXPathIsInf" id="xmlXPathIsInf"></a>Function: xmlXPathIsInf</h3><pre class="programlisting">int	xmlXPathIsInf			(double val)<br />
</pre><p>Provides a portable isinf() function to detect whether a double is a +Infinite or -Infinite. Based on trio code http://sourceforge.net/projects/ctrio/</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a double value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 vi the value is +Infinite, -1 if -Infinite, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlXPathIsNaN" id="xmlXPathIsNaN"></a>Function: xmlXPathIsNaN</h3><pre class="programlisting">int	xmlXPathIsNaN			(double val)<br />
</pre><p>Provides a portable isnan() function to detect whether a double is a NotaNumber. Based on trio code http://sourceforge.net/projects/ctrio/</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a double value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the value is a NaN, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlXPathNewContext" id="xmlXPathNewContext"></a>Function: xmlXPathNewContext</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a>	xmlXPathNewContext	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathContext">xmlXPathContext</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the XML document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathContext">xmlXPathContext</a> just allocated. The caller will need to free it.</td></tr></tbody></table></div><h3><a name="xmlXPathNodeEval" id="xmlXPathNodeEval"></a>Function: xmlXPathNodeEval</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathNodeEval	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />						 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)<br />
</pre><p>Evaluate the XPath Location Path in the given context. The node 'node' is set as the context node. The context node is not restored.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to to use as the context node</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the XPath expression</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> resulting from the evaluation or NULL. the caller has to free the object.</td></tr></tbody></table></div><h3><a name="xmlXPathNodeSetCreate" id="xmlXPathNodeSetCreate"></a>Function: xmlXPathNodeSetCreate</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathNodeSetCreate	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> val)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> of type double and of value @val</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an initial xmlNodePtr, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPathObjectCopy" id="xmlXPathObjectCopy"></a>Function: xmlXPathObjectCopy</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathObjectCopy	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>allocate a new copy of a given object</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the original object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPathOrderDocElems" id="xmlXPathOrderDocElems"></a>Function: xmlXPathOrderDocElems</h3><pre class="programlisting">long	xmlXPathOrderDocElems		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Call this routine to speed up XPath computation on static documents. This stamps all the element nodes with the document order Like for line information, the order is kept in the element-&gt;content field, the value stored is actually - the node number (starting at -1) to be able to differentiate from line numbers.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>an input document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of elements found in the document or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlXPathSetContextNode" id="xmlXPathSetContextNode"></a>Function: xmlXPathSetContextNode</h3><pre class="programlisting">int	xmlXPathSetContextNode		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)<br />
</pre><p>Sets 'node' as the context node. The node must be in the same document as that associated with the context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to to use as the context node</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error or 0 if successful</td></tr></tbody></table></div><h3><a name="xmlXPathVariableLookupFunc" id="xmlXPathVariableLookupFunc"></a>Function type: xmlXPathVariableLookupFunc</h3><pre class="programlisting">Function type: xmlXPathVariableLookupFunc
<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathVariableLookupFunc	(void * ctxt, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns_uri)
</pre><p>Prototype for callbacks used to plug variable lookup in the XPath engine.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>name of the variable</td></tr><tr><td><span class="term"><i><tt>ns_uri</tt></i>:</span></td><td>the namespace name hosting this variable</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the XPath object value or NULL if not found.</td></tr></tbody></table></div><br />
<p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
