<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xinclude from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xinclude from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-valid.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-valid.html">valid</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xlink.html">xlink</a></th><td><a accesskey="n" href="libxml-xlink.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>API to handle XInclude processing, implements the World Wide Web Consortium Last Call Working Draft 10 November 2003</p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#XINCLUDE_FALLBACK">XINCLUDE_FALLBACK</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_HREF">XINCLUDE_HREF</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_NODE">XINCLUDE_NODE</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_NS">XINCLUDE_NS</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_OLD_NS">XINCLUDE_OLD_NS</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_PARSE">XINCLUDE_PARSE</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_PARSE_ENCODING">XINCLUDE_PARSE_ENCODING</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_PARSE_TEXT">XINCLUDE_PARSE_TEXT</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_PARSE_XML">XINCLUDE_PARSE_XML</a></pre><pre class="programlisting">#define <a href="#XINCLUDE_PARSE_XPOINTER">XINCLUDE_PARSE_XPOINTER</a></pre><pre class="programlisting">Structure <a href="#xmlXIncludeCtxt">xmlXIncludeCtxt</a><br />struct _xmlXIncludeCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xinclude.html#xmlXIncludeCtxt">xmlXIncludeCtxt</a> * <a name="xmlXIncludeCtxtPtr" id="xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a>
</pre><pre class="programlisting">void	<a href="#xmlXIncludeFreeContext">xmlXIncludeFreeContext</a>		(<a href="libxml-xinclude.html#xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-xinclude.html#xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a>	<a href="#xmlXIncludeNewContext">xmlXIncludeNewContext</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">int	<a href="#xmlXIncludeProcess">xmlXIncludeProcess</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">int	<a href="#xmlXIncludeProcessFlags">xmlXIncludeProcessFlags</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int flags)</pre>
<pre class="programlisting">int	<a href="#xmlXIncludeProcessFlagsData">xmlXIncludeProcessFlagsData</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int flags, <br />					 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlXIncludeProcessNode">xmlXIncludeProcessNode</a>		(<a href="libxml-xinclude.html#xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">int	<a href="#xmlXIncludeProcessTree">xmlXIncludeProcessTree</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree)</pre>
<pre class="programlisting">int	<a href="#xmlXIncludeProcessTreeFlags">xmlXIncludeProcessTreeFlags</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br />					 int flags)</pre>
<pre class="programlisting">int	<a href="#xmlXIncludeProcessTreeFlagsData">xmlXIncludeProcessTreeFlagsData</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br />					 int flags, <br />					 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlXIncludeSetFlags">xmlXIncludeSetFlags</a>		(<a href="libxml-xinclude.html#xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a> ctxt, <br />					 int flags)</pre>
<h2>Description</h2>
<h3><a name="XINCLUDE_FALLBACK" id="XINCLUDE_FALLBACK"></a>Macro: XINCLUDE_FALLBACK</h3><pre>#define XINCLUDE_FALLBACK</pre><p>Macro defining "fallback"</p>
<h3><a name="XINCLUDE_HREF" id="XINCLUDE_HREF"></a>Macro: XINCLUDE_HREF</h3><pre>#define XINCLUDE_HREF</pre><p>Macro defining "href"</p>
<h3><a name="XINCLUDE_NODE" id="XINCLUDE_NODE"></a>Macro: XINCLUDE_NODE</h3><pre>#define XINCLUDE_NODE</pre><p>Macro defining "include"</p>
<h3><a name="XINCLUDE_NS" id="XINCLUDE_NS"></a>Macro: XINCLUDE_NS</h3><pre>#define XINCLUDE_NS</pre><p>Macro defining the Xinclude namespace: http://www.w3.org/2003/XInclude</p>
<h3><a name="XINCLUDE_OLD_NS" id="XINCLUDE_OLD_NS"></a>Macro: XINCLUDE_OLD_NS</h3><pre>#define XINCLUDE_OLD_NS</pre><p>Macro defining the draft Xinclude namespace: http://www.w3.org/2001/XInclude</p>
<h3><a name="XINCLUDE_PARSE" id="XINCLUDE_PARSE"></a>Macro: XINCLUDE_PARSE</h3><pre>#define XINCLUDE_PARSE</pre><p>Macro defining "parse"</p>
<h3><a name="XINCLUDE_PARSE_ENCODING" id="XINCLUDE_PARSE_ENCODING"></a>Macro: XINCLUDE_PARSE_ENCODING</h3><pre>#define XINCLUDE_PARSE_ENCODING</pre><p>Macro defining "encoding"</p>
<h3><a name="XINCLUDE_PARSE_TEXT" id="XINCLUDE_PARSE_TEXT"></a>Macro: XINCLUDE_PARSE_TEXT</h3><pre>#define XINCLUDE_PARSE_TEXT</pre><p>Macro defining "text"</p>
<h3><a name="XINCLUDE_PARSE_XML" id="XINCLUDE_PARSE_XML"></a>Macro: XINCLUDE_PARSE_XML</h3><pre>#define XINCLUDE_PARSE_XML</pre><p>Macro defining "xml"</p>
<h3><a name="XINCLUDE_PARSE_XPOINTER" id="XINCLUDE_PARSE_XPOINTER"></a>Macro: XINCLUDE_PARSE_XPOINTER</h3><pre>#define XINCLUDE_PARSE_XPOINTER</pre><p>Macro defining "xpointer"</p>
<h3><a name="xmlXIncludeCtxt" id="xmlXIncludeCtxt">Structure xmlXIncludeCtxt</a></h3><pre class="programlisting">Structure xmlXIncludeCtxt<br />struct _xmlXIncludeCtxt {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlXIncludeFreeContext" id="xmlXIncludeFreeContext"></a>Function: xmlXIncludeFreeContext</h3><pre class="programlisting">void	xmlXIncludeFreeContext		(<a href="libxml-xinclude.html#xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a> ctxt)<br />
</pre><p>Free an XInclude context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XInclude context</td></tr></tbody></table></div><h3><a name="xmlXIncludeNewContext" id="xmlXIncludeNewContext"></a>Function: xmlXIncludeNewContext</h3><pre class="programlisting"><a href="libxml-xinclude.html#xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a>	xmlXIncludeNewContext	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Creates a new XInclude context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>an XML Document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new set</td></tr></tbody></table></div><h3><a name="xmlXIncludeProcess" id="xmlXIncludeProcess"></a>Function: xmlXIncludeProcess</h3><pre class="programlisting">int	xmlXIncludeProcess		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Implement the XInclude substitution on the XML document @doc</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>an XML document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if no substitution were done, -1 if some processing failed or the number of substitutions done.</td></tr></tbody></table></div><h3><a name="xmlXIncludeProcessFlags" id="xmlXIncludeProcessFlags"></a>Function: xmlXIncludeProcessFlags</h3><pre class="programlisting">int	xmlXIncludeProcessFlags		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int flags)<br />
</pre><p>Implement the XInclude substitution on the XML document @doc</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>an XML document</td></tr><tr><td><span class="term"><i><tt>flags</tt></i>:</span></td><td>a set of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a> used for parsing XML includes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if no substitution were done, -1 if some processing failed or the number of substitutions done.</td></tr></tbody></table></div><h3><a name="xmlXIncludeProcessFlagsData" id="xmlXIncludeProcessFlagsData"></a>Function: xmlXIncludeProcessFlagsData</h3><pre class="programlisting">int	xmlXIncludeProcessFlagsData	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int flags, <br />					 void * data)<br />
</pre><p>Implement the XInclude substitution on the XML document @doc</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>an XML document</td></tr><tr><td><span class="term"><i><tt>flags</tt></i>:</span></td><td>a set of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a> used for parsing XML includes</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>application data that will be passed to the parser context in the _private field of the parser context(s)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if no substitution were done, -1 if some processing failed or the number of substitutions done.</td></tr></tbody></table></div><h3><a name="xmlXIncludeProcessNode" id="xmlXIncludeProcessNode"></a>Function: xmlXIncludeProcessNode</h3><pre class="programlisting">int	xmlXIncludeProcessNode		(<a href="libxml-xinclude.html#xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Implement the XInclude substitution for the given subtree reusing the informations and data coming from the given context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an existing XInclude context</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node in an XML document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if no substitution were done, -1 if some processing failed or the number of substitutions done.</td></tr></tbody></table></div><h3><a name="xmlXIncludeProcessTree" id="xmlXIncludeProcessTree"></a>Function: xmlXIncludeProcessTree</h3><pre class="programlisting">int	xmlXIncludeProcessTree		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree)<br />
</pre><p>Implement the XInclude substitution for the given subtree</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>a node in an XML document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if no substitution were done, -1 if some processing failed or the number of substitutions done.</td></tr></tbody></table></div><h3><a name="xmlXIncludeProcessTreeFlags" id="xmlXIncludeProcessTreeFlags"></a>Function: xmlXIncludeProcessTreeFlags</h3><pre class="programlisting">int	xmlXIncludeProcessTreeFlags	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br />					 int flags)<br />
</pre><p>Implement the XInclude substitution for the given subtree</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>a node in an XML document</td></tr><tr><td><span class="term"><i><tt>flags</tt></i>:</span></td><td>a set of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a> used for parsing XML includes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if no substitution were done, -1 if some processing failed or the number of substitutions done.</td></tr></tbody></table></div><h3><a name="xmlXIncludeProcessTreeFlagsData" id="xmlXIncludeProcessTreeFlagsData"></a>Function: xmlXIncludeProcessTreeFlagsData</h3><pre class="programlisting">int	xmlXIncludeProcessTreeFlagsData	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br />					 int flags, <br />					 void * data)<br />
</pre><p>Implement the XInclude substitution on the XML node @tree</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>an XML node</td></tr><tr><td><span class="term"><i><tt>flags</tt></i>:</span></td><td>a set of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a> used for parsing XML includes</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>application data that will be passed to the parser context in the _private field of the parser context(s)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if no substitution were done, -1 if some processing failed or the number of substitutions done.</td></tr></tbody></table></div><h3><a name="xmlXIncludeSetFlags" id="xmlXIncludeSetFlags"></a>Function: xmlXIncludeSetFlags</h3><pre class="programlisting">int	xmlXIncludeSetFlags		(<a href="libxml-xinclude.html#xmlXIncludeCtxtPtr">xmlXIncludeCtxtPtr</a> ctxt, <br />					 int flags)<br />
</pre><p>Set the flags used for further processing of XML resources.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XInclude processing context</td></tr><tr><td><span class="term"><i><tt>flags</tt></i>:</span></td><td>a set of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a> used for parsing XML includes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error.</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
