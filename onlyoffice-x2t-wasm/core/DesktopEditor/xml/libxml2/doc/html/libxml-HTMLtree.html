<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module HTMLtree from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module HTMLtree from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-HTMLparser.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-HTMLparser.html">HTMLparser</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-SAX.html">SAX</a></th><td><a accesskey="n" href="libxml-SAX.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>this module implements a few function needed to process tree in an HTML specific way. </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#HTML_COMMENT_NODE">HTML_COMMENT_NODE</a></pre><pre class="programlisting">#define <a href="#HTML_ENTITY_REF_NODE">HTML_ENTITY_REF_NODE</a></pre><pre class="programlisting">#define <a href="#HTML_PI_NODE">HTML_PI_NODE</a></pre><pre class="programlisting">#define <a href="#HTML_PRESERVE_NODE">HTML_PRESERVE_NODE</a></pre><pre class="programlisting">#define <a href="#HTML_TEXT_NODE">HTML_TEXT_NODE</a></pre><pre class="programlisting">void	<a href="#htmlDocContentDumpFormatOutput">htmlDocContentDumpFormatOutput</a>	(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding, <br />					 int format)</pre>
<pre class="programlisting">void	<a href="#htmlDocContentDumpOutput">htmlDocContentDumpOutput</a>	(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding)</pre>
<pre class="programlisting">int	<a href="#htmlDocDump">htmlDocDump</a>			(FILE * f, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#htmlDocDumpMemory">htmlDocDumpMemory</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** mem, <br />					 int * size)</pre>
<pre class="programlisting">void	<a href="#htmlDocDumpMemoryFormat">htmlDocDumpMemoryFormat</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** mem, <br />					 int * size, <br />					 int format)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#htmlGetMetaEncoding">htmlGetMetaEncoding</a>	(<a href="libxml-HTMLparser.html#htmlDocPtr">htmlDocPtr</a> doc)</pre>
<pre class="programlisting">int	<a href="#htmlIsBooleanAttr">htmlIsBooleanAttr</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-HTMLparser.html#htmlDocPtr">htmlDocPtr</a>	<a href="#htmlNewDoc">htmlNewDoc</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID)</pre>
<pre class="programlisting"><a href="libxml-HTMLparser.html#htmlDocPtr">htmlDocPtr</a>	<a href="#htmlNewDocNoDtD">htmlNewDocNoDtD</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID)</pre>
<pre class="programlisting">int	<a href="#htmlNodeDump">htmlNodeDump</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#htmlNodeDumpFile">htmlNodeDumpFile</a>		(FILE * out, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting">int	<a href="#htmlNodeDumpFileFormat">htmlNodeDumpFileFormat</a>		(FILE * out, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const char * encoding, <br />					 int format)</pre>
<pre class="programlisting">void	<a href="#htmlNodeDumpFormatOutput">htmlNodeDumpFormatOutput</a>	(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const char * encoding, <br />					 int format)</pre>
<pre class="programlisting">void	<a href="#htmlNodeDumpOutput">htmlNodeDumpOutput</a>		(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const char * encoding)</pre>
<pre class="programlisting">int	<a href="#htmlSaveFile">htmlSaveFile</a>			(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)</pre>
<pre class="programlisting">int	<a href="#htmlSaveFileEnc">htmlSaveFileEnc</a>			(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding)</pre>
<pre class="programlisting">int	<a href="#htmlSaveFileFormat">htmlSaveFileFormat</a>		(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding, <br />					 int format)</pre>
<pre class="programlisting">int	<a href="#htmlSetMetaEncoding">htmlSetMetaEncoding</a>		(<a href="libxml-HTMLparser.html#htmlDocPtr">htmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * encoding)</pre>
<h2>Description</h2>
<h3><a name="HTML_COMMENT_NODE" id="HTML_COMMENT_NODE"></a>Macro: HTML_COMMENT_NODE</h3><pre>#define HTML_COMMENT_NODE</pre><p>Macro. A <a href="libxml-SAX.html#comment">comment</a> in a HTML document is really implemented the same way as a <a href="libxml-SAX.html#comment">comment</a> in an XML document.</p>
<h3><a name="HTML_ENTITY_REF_NODE" id="HTML_ENTITY_REF_NODE"></a>Macro: HTML_ENTITY_REF_NODE</h3><pre>#define HTML_ENTITY_REF_NODE</pre><p>Macro. An entity <a href="libxml-SAX.html#reference">reference</a> in a HTML document is really implemented the same way as an entity <a href="libxml-SAX.html#reference">reference</a> in an XML document.</p>
<h3><a name="HTML_PI_NODE" id="HTML_PI_NODE"></a>Macro: HTML_PI_NODE</h3><pre>#define HTML_PI_NODE</pre><p>Macro. A processing instruction in a HTML document is really implemented the same way as a processing instruction in an XML document.</p>
<h3><a name="HTML_PRESERVE_NODE" id="HTML_PRESERVE_NODE"></a>Macro: HTML_PRESERVE_NODE</h3><pre>#define HTML_PRESERVE_NODE</pre><p>Macro. A preserved node in a HTML document is really implemented the same way as a CDATA section in an XML document.</p>
<h3><a name="HTML_TEXT_NODE" id="HTML_TEXT_NODE"></a>Macro: HTML_TEXT_NODE</h3><pre>#define HTML_TEXT_NODE</pre><p>Macro. A text node in a HTML document is really implemented the same way as a text node in an XML document.</p>
<h3><a name="htmlDocContentDumpFormatOutput" id="htmlDocContentDumpFormatOutput"></a>Function: htmlDocContentDumpFormatOutput</h3><pre class="programlisting">void	htmlDocContentDumpFormatOutput	(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding, <br />					 int format)<br />
</pre><p>Dump an HTML document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the HTML buffer output</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding string</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr></tbody></table></div><h3><a name="htmlDocContentDumpOutput" id="htmlDocContentDumpOutput"></a>Function: htmlDocContentDumpOutput</h3><pre class="programlisting">void	htmlDocContentDumpOutput	(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding)<br />
</pre><p>Dump an HTML document. Formating return/spaces are added.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the HTML buffer output</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding string</td></tr></tbody></table></div><h3><a name="htmlDocDump" id="htmlDocDump"></a>Function: htmlDocDump</h3><pre class="programlisting">int	htmlDocDump			(FILE * f, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)<br />
</pre><p>Dump an HTML document to an open FILE.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the FILE*</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="htmlDocDumpMemory" id="htmlDocDumpMemory"></a>Function: htmlDocDumpMemory</h3><pre class="programlisting">void	htmlDocDumpMemory		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** mem, <br />					 int * size)<br />
</pre><p>Dump an HTML document in memory and return the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * and it's size. It's up to the caller to free the memory.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>OUT: the memory pointer</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>OUT: the memory length</td></tr></tbody></table></div><h3><a name="htmlDocDumpMemoryFormat" id="htmlDocDumpMemoryFormat"></a>Function: htmlDocDumpMemoryFormat</h3><pre class="programlisting">void	htmlDocDumpMemoryFormat		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** mem, <br />					 int * size, <br />					 int format)<br />
</pre><p>Dump an HTML document in memory and return the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * and it's size. It's up to the caller to free the memory.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>OUT: the memory pointer</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>OUT: the memory length</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr></tbody></table></div><h3><a name="htmlGetMetaEncoding" id="htmlGetMetaEncoding"></a>Function: htmlGetMetaEncoding</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	htmlGetMetaEncoding	(<a href="libxml-HTMLparser.html#htmlDocPtr">htmlDocPtr</a> doc)<br />
</pre><p>Encoding definition lookup in the Meta tags</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current encoding as flagged in the HTML source</td></tr></tbody></table></div><h3><a name="htmlIsBooleanAttr" id="htmlIsBooleanAttr"></a>Function: htmlIsBooleanAttr</h3><pre class="programlisting">int	htmlIsBooleanAttr		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Determine if a given <a href="libxml-SAX.html#attribute">attribute</a> is a boolean attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the <a href="libxml-SAX.html#attribute">attribute</a> to check</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>false if the <a href="libxml-SAX.html#attribute">attribute</a> is not boolean, true otherwise.</td></tr></tbody></table></div><h3><a name="htmlNewDoc" id="htmlNewDoc"></a>Function: htmlNewDoc</h3><pre class="programlisting"><a href="libxml-HTMLparser.html#htmlDocPtr">htmlDocPtr</a>	htmlNewDoc		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID)<br />
</pre><p>Creates a new HTML document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>URI for the dtd, or NULL</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID of the DTD, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new document</td></tr></tbody></table></div><h3><a name="htmlNewDocNoDtD" id="htmlNewDocNoDtD"></a>Function: htmlNewDocNoDtD</h3><pre class="programlisting"><a href="libxml-HTMLparser.html#htmlDocPtr">htmlDocPtr</a>	htmlNewDocNoDtD		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID)<br />
</pre><p>Creates a new HTML document without a DTD node if @URI and @ExternalID are NULL</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>URI for the dtd, or NULL</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID of the DTD, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new document, do not initialize the DTD if not provided</td></tr></tbody></table></div><h3><a name="htmlNodeDump" id="htmlNodeDump"></a>Function: htmlNodeDump</h3><pre class="programlisting">int	htmlNodeDump			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Dump an HTML node, recursive behaviour,children are printed too, and formatting returns are added.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the HTML buffer output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error</td></tr></tbody></table></div><h3><a name="htmlNodeDumpFile" id="htmlNodeDumpFile"></a>Function: htmlNodeDumpFile</h3><pre class="programlisting">void	htmlNodeDumpFile		(FILE * out, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Dump an HTML node, recursive behaviour,children are printed too, and formatting returns are added.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>the FILE pointer</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr></tbody></table></div><h3><a name="htmlNodeDumpFileFormat" id="htmlNodeDumpFileFormat"></a>Function: htmlNodeDumpFileFormat</h3><pre class="programlisting">int	htmlNodeDumpFileFormat		(FILE * out, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const char * encoding, <br />					 int format)<br />
</pre><p>Dump an HTML node, recursive behaviour,children are printed too. TODO: if encoding == NULL try to save in the doc encoding</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>the FILE pointer</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="htmlNodeDumpFormatOutput" id="htmlNodeDumpFormatOutput"></a>Function: htmlNodeDumpFormatOutput</h3><pre class="programlisting">void	htmlNodeDumpFormatOutput	(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const char * encoding, <br />					 int format)<br />
</pre><p>Dump an HTML node, recursive behaviour,children are printed too.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the HTML buffer output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding string</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr></tbody></table></div><h3><a name="htmlNodeDumpOutput" id="htmlNodeDumpOutput"></a>Function: htmlNodeDumpOutput</h3><pre class="programlisting">void	htmlNodeDumpOutput		(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const char * encoding)<br />
</pre><p>Dump an HTML node, recursive behaviour,children are printed too, and formatting returns/spaces are added.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the HTML buffer output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding string</td></tr></tbody></table></div><h3><a name="htmlSaveFile" id="htmlSaveFile"></a>Function: htmlSaveFile</h3><pre class="programlisting">int	htmlSaveFile			(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)<br />
</pre><p>Dump an HTML document to a file. If @filename is "-" the stdout file is used.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename (or URL)</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="htmlSaveFileEnc" id="htmlSaveFileEnc"></a>Function: htmlSaveFileEnc</h3><pre class="programlisting">int	htmlSaveFileEnc			(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding)<br />
</pre><p>Dump an HTML document to a file using a given encoding and formatting returns/spaces are added.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="htmlSaveFileFormat" id="htmlSaveFileFormat"></a>Function: htmlSaveFileFormat</h3><pre class="programlisting">int	htmlSaveFileFormat		(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding, <br />					 int format)<br />
</pre><p>Dump an HTML document to a file using a given encoding.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="htmlSetMetaEncoding" id="htmlSetMetaEncoding"></a>Function: htmlSetMetaEncoding</h3><pre class="programlisting">int	htmlSetMetaEncoding		(<a href="libxml-HTMLparser.html#htmlDocPtr">htmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * encoding)<br />
</pre><p>Sets the current encoding in the Meta tags NOTE: this will not change the document content encoding, just the META flag associated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
