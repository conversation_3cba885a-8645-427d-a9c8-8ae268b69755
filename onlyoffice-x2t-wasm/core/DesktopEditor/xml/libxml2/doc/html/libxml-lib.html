<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Reference Manual for libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Reference Manual for libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><h2>Table of Contents</h2><ul><li><a href="libxml-DOCBparser.html">DOCBparser</a>: old DocBook SGML parser</li><li><a href="libxml-HTMLparser.html">HTMLparser</a>: interface for an HTML 4.0 non-verifying parser</li><li><a href="libxml-HTMLtree.html">HTMLtree</a>: specific APIs to process HTML tree, especially serialization</li><li><a href="libxml-SAX.html">SAX</a>: Old SAX version 1 handler, deprecated</li><li><a href="libxml-SAX2.html">SAX2</a>: SAX2 parser interface used to build the DOM tree</li><li><a href="libxml-c14n.html">c14n</a>: Provide Canonical XML and Exclusive XML Canonicalization</li><li><a href="libxml-catalog.html">catalog</a>: interfaces to the Catalog handling system</li><li><a href="libxml-chvalid.html">chvalid</a>: Unicode character range checking</li><li><a href="libxml-debugXML.html">debugXML</a>: Tree debugging APIs</li><li><a href="libxml-dict.html">dict</a>: string dictionary</li><li><a href="libxml-encoding.html">encoding</a>: interface for the encoding conversion functions</li><li><a href="libxml-entities.html">entities</a>: interface for the XML entities handling</li><li><a href="libxml-globals.html">globals</a>: interface for all global variables of the library</li><li><a href="libxml-hash.html">hash</a>: Chained hash tables</li><li><a href="libxml-list.html">list</a>: lists interfaces</li><li><a href="libxml-nanoftp.html">nanoftp</a>: minimal FTP implementation</li><li><a href="libxml-nanohttp.html">nanohttp</a>: minimal HTTP implementation</li><li><a href="libxml-parser.html">parser</a>: the core parser module</li><li><a href="libxml-parserInternals.html">parserInternals</a>: internals routines and limits exported by the parser.</li><li><a href="libxml-pattern.html">pattern</a>: pattern expression handling</li><li><a href="libxml-relaxng.html">relaxng</a>: implementation of the Relax-NG validation</li><li><a href="libxml-schemasInternals.html">schemasInternals</a>: internal interfaces for XML Schemas</li><li><a href="libxml-schematron.html">schematron</a>: XML Schemastron implementation</li><li><a href="libxml-threads.html">threads</a>: interfaces for thread handling</li><li><a href="libxml-tree.html">tree</a>: interfaces for tree manipulation</li><li><a href="libxml-uri.html">uri</a>: library of generic URI related routines</li><li><a href="libxml-valid.html">valid</a>: The DTD validation</li><li><a href="libxml-xinclude.html">xinclude</a>: implementation of XInclude</li><li><a href="libxml-xlink.html">xlink</a>: unfinished XLink detection module</li><li><a href="libxml-xmlIO.html">xmlIO</a>: interface for the I/O interfaces used by the parser</li><li><a href="libxml-xmlautomata.html">xmlautomata</a>: API to build regexp automata</li><li><a href="libxml-xmlerror.html">xmlerror</a>: error handling</li><li><a href="libxml-xmlexports.html">xmlexports</a>: macros for marking symbols as exportable/importable.</li><li><a href="libxml-xmlmemory.html">xmlmemory</a>: interface for the memory allocator</li><li><a href="libxml-xmlmodule.html">xmlmodule</a>: dynamic module loading</li><li><a href="libxml-xmlreader.html">xmlreader</a>: the XMLReader implementation</li><li><a href="libxml-xmlregexp.html">xmlregexp</a>: regular expressions handling</li><li><a href="libxml-xmlsave.html">xmlsave</a>: the XML document serializer</li><li><a href="libxml-xmlschemas.html">xmlschemas</a>: incomplete XML Schemas structure implementation</li><li><a href="libxml-xmlschemastypes.html">xmlschemastypes</a>: implementation of XML Schema Datatypes</li><li><a href="libxml-xmlstring.html">xmlstring</a>: set of routines to process strings</li><li><a href="libxml-xmlunicode.html">xmlunicode</a>: Unicode character APIs</li><li><a href="libxml-xmlversion.html">xmlversion</a>: compile-time version informations</li><li><a href="libxml-xmlwriter.html">xmlwriter</a>: text writing API for XML</li><li><a href="libxml-xpath.html">xpath</a>: XML Path Language implementation</li><li><a href="libxml-xpathInternals.html">xpathInternals</a>: internal interfaces for XML Path Language implementation</li><li><a href="libxml-xpointer.html">xpointer</a>: API to handle XML Pointers</li></ul><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
