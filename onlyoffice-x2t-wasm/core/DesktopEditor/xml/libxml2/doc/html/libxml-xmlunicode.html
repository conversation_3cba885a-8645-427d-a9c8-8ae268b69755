<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlunicode from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlunicode from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlstring.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlstring.html">xmlstring</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlversion.html">xmlversion</a></th><td><a accesskey="n" href="libxml-xmlversion.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>API for the Unicode character APIs  This file is automatically generated from the UCS description files of the Unicode Character Database</p><h2>Table of Contents</h2><pre class="programlisting">int	<a href="#xmlUCSIsAegeanNumbers">xmlUCSIsAegeanNumbers</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsAlphabeticPresentationForms">xmlUCSIsAlphabeticPresentationForms</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsArabic">xmlUCSIsArabic</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsArabicPresentationFormsA">xmlUCSIsArabicPresentationFormsA</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsArabicPresentationFormsB">xmlUCSIsArabicPresentationFormsB</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsArmenian">xmlUCSIsArmenian</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsArrows">xmlUCSIsArrows</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBasicLatin">xmlUCSIsBasicLatin</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBengali">xmlUCSIsBengali</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBlock">xmlUCSIsBlock</a>			(int code, <br />					 const char * block)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBlockElements">xmlUCSIsBlockElements</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBopomofo">xmlUCSIsBopomofo</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBopomofoExtended">xmlUCSIsBopomofoExtended</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBoxDrawing">xmlUCSIsBoxDrawing</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBraillePatterns">xmlUCSIsBraillePatterns</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsBuhid">xmlUCSIsBuhid</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsByzantineMusicalSymbols">xmlUCSIsByzantineMusicalSymbols</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKCompatibility">xmlUCSIsCJKCompatibility</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKCompatibilityForms">xmlUCSIsCJKCompatibilityForms</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKCompatibilityIdeographs">xmlUCSIsCJKCompatibilityIdeographs</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKCompatibilityIdeographsSupplement">xmlUCSIsCJKCompatibilityIdeographsSupplement</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKRadicalsSupplement">xmlUCSIsCJKRadicalsSupplement</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKSymbolsandPunctuation">xmlUCSIsCJKSymbolsandPunctuation</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKUnifiedIdeographs">xmlUCSIsCJKUnifiedIdeographs</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKUnifiedIdeographsExtensionA">xmlUCSIsCJKUnifiedIdeographsExtensionA</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCJKUnifiedIdeographsExtensionB">xmlUCSIsCJKUnifiedIdeographsExtensionB</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCat">xmlUCSIsCat</a>			(int code, <br />					 const char * cat)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatC">xmlUCSIsCatC</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatCc">xmlUCSIsCatCc</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatCf">xmlUCSIsCatCf</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatCo">xmlUCSIsCatCo</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatCs">xmlUCSIsCatCs</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatL">xmlUCSIsCatL</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatLl">xmlUCSIsCatLl</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatLm">xmlUCSIsCatLm</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatLo">xmlUCSIsCatLo</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatLt">xmlUCSIsCatLt</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatLu">xmlUCSIsCatLu</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatM">xmlUCSIsCatM</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatMc">xmlUCSIsCatMc</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatMe">xmlUCSIsCatMe</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatMn">xmlUCSIsCatMn</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatN">xmlUCSIsCatN</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatNd">xmlUCSIsCatNd</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatNl">xmlUCSIsCatNl</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatNo">xmlUCSIsCatNo</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatP">xmlUCSIsCatP</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatPc">xmlUCSIsCatPc</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatPd">xmlUCSIsCatPd</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatPe">xmlUCSIsCatPe</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatPf">xmlUCSIsCatPf</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatPi">xmlUCSIsCatPi</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatPo">xmlUCSIsCatPo</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatPs">xmlUCSIsCatPs</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatS">xmlUCSIsCatS</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatSc">xmlUCSIsCatSc</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatSk">xmlUCSIsCatSk</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatSm">xmlUCSIsCatSm</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatSo">xmlUCSIsCatSo</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatZ">xmlUCSIsCatZ</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatZl">xmlUCSIsCatZl</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatZp">xmlUCSIsCatZp</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCatZs">xmlUCSIsCatZs</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCherokee">xmlUCSIsCherokee</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCombiningDiacriticalMarks">xmlUCSIsCombiningDiacriticalMarks</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCombiningDiacriticalMarksforSymbols">xmlUCSIsCombiningDiacriticalMarksforSymbols</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCombiningHalfMarks">xmlUCSIsCombiningHalfMarks</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCombiningMarksforSymbols">xmlUCSIsCombiningMarksforSymbols</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsControlPictures">xmlUCSIsControlPictures</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCurrencySymbols">xmlUCSIsCurrencySymbols</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCypriotSyllabary">xmlUCSIsCypriotSyllabary</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCyrillic">xmlUCSIsCyrillic</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsCyrillicSupplement">xmlUCSIsCyrillicSupplement</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsDeseret">xmlUCSIsDeseret</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsDevanagari">xmlUCSIsDevanagari</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsDingbats">xmlUCSIsDingbats</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsEnclosedAlphanumerics">xmlUCSIsEnclosedAlphanumerics</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsEnclosedCJKLettersandMonths">xmlUCSIsEnclosedCJKLettersandMonths</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsEthiopic">xmlUCSIsEthiopic</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGeneralPunctuation">xmlUCSIsGeneralPunctuation</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGeometricShapes">xmlUCSIsGeometricShapes</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGeorgian">xmlUCSIsGeorgian</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGothic">xmlUCSIsGothic</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGreek">xmlUCSIsGreek</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGreekExtended">xmlUCSIsGreekExtended</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGreekandCoptic">xmlUCSIsGreekandCoptic</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGujarati">xmlUCSIsGujarati</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsGurmukhi">xmlUCSIsGurmukhi</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHalfwidthandFullwidthForms">xmlUCSIsHalfwidthandFullwidthForms</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHangulCompatibilityJamo">xmlUCSIsHangulCompatibilityJamo</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHangulJamo">xmlUCSIsHangulJamo</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHangulSyllables">xmlUCSIsHangulSyllables</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHanunoo">xmlUCSIsHanunoo</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHebrew">xmlUCSIsHebrew</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHighPrivateUseSurrogates">xmlUCSIsHighPrivateUseSurrogates</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHighSurrogates">xmlUCSIsHighSurrogates</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsHiragana">xmlUCSIsHiragana</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsIPAExtensions">xmlUCSIsIPAExtensions</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsIdeographicDescriptionCharacters">xmlUCSIsIdeographicDescriptionCharacters</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsKanbun">xmlUCSIsKanbun</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsKangxiRadicals">xmlUCSIsKangxiRadicals</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsKannada">xmlUCSIsKannada</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsKatakana">xmlUCSIsKatakana</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsKatakanaPhoneticExtensions">xmlUCSIsKatakanaPhoneticExtensions</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsKhmer">xmlUCSIsKhmer</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsKhmerSymbols">xmlUCSIsKhmerSymbols</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLao">xmlUCSIsLao</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLatin1Supplement">xmlUCSIsLatin1Supplement</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLatinExtendedA">xmlUCSIsLatinExtendedA</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLatinExtendedAdditional">xmlUCSIsLatinExtendedAdditional</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLatinExtendedB">xmlUCSIsLatinExtendedB</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLetterlikeSymbols">xmlUCSIsLetterlikeSymbols</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLimbu">xmlUCSIsLimbu</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLinearBIdeograms">xmlUCSIsLinearBIdeograms</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLinearBSyllabary">xmlUCSIsLinearBSyllabary</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsLowSurrogates">xmlUCSIsLowSurrogates</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMalayalam">xmlUCSIsMalayalam</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMathematicalAlphanumericSymbols">xmlUCSIsMathematicalAlphanumericSymbols</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMathematicalOperators">xmlUCSIsMathematicalOperators</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMiscellaneousMathematicalSymbolsA">xmlUCSIsMiscellaneousMathematicalSymbolsA</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMiscellaneousMathematicalSymbolsB">xmlUCSIsMiscellaneousMathematicalSymbolsB</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMiscellaneousSymbols">xmlUCSIsMiscellaneousSymbols</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMiscellaneousSymbolsandArrows">xmlUCSIsMiscellaneousSymbolsandArrows</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMiscellaneousTechnical">xmlUCSIsMiscellaneousTechnical</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMongolian">xmlUCSIsMongolian</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMusicalSymbols">xmlUCSIsMusicalSymbols</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsMyanmar">xmlUCSIsMyanmar</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsNumberForms">xmlUCSIsNumberForms</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsOgham">xmlUCSIsOgham</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsOldItalic">xmlUCSIsOldItalic</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsOpticalCharacterRecognition">xmlUCSIsOpticalCharacterRecognition</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsOriya">xmlUCSIsOriya</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsOsmanya">xmlUCSIsOsmanya</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsPhoneticExtensions">xmlUCSIsPhoneticExtensions</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsPrivateUse">xmlUCSIsPrivateUse</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsPrivateUseArea">xmlUCSIsPrivateUseArea</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsRunic">xmlUCSIsRunic</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsShavian">xmlUCSIsShavian</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSinhala">xmlUCSIsSinhala</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSmallFormVariants">xmlUCSIsSmallFormVariants</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSpacingModifierLetters">xmlUCSIsSpacingModifierLetters</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSpecials">xmlUCSIsSpecials</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSuperscriptsandSubscripts">xmlUCSIsSuperscriptsandSubscripts</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSupplementalArrowsA">xmlUCSIsSupplementalArrowsA</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSupplementalArrowsB">xmlUCSIsSupplementalArrowsB</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSupplementalMathematicalOperators">xmlUCSIsSupplementalMathematicalOperators</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSupplementaryPrivateUseAreaA">xmlUCSIsSupplementaryPrivateUseAreaA</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSupplementaryPrivateUseAreaB">xmlUCSIsSupplementaryPrivateUseAreaB</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsSyriac">xmlUCSIsSyriac</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsTagalog">xmlUCSIsTagalog</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsTagbanwa">xmlUCSIsTagbanwa</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsTags">xmlUCSIsTags</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsTaiLe">xmlUCSIsTaiLe</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsTaiXuanJingSymbols">xmlUCSIsTaiXuanJingSymbols</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsTamil">xmlUCSIsTamil</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsTelugu">xmlUCSIsTelugu</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsThaana">xmlUCSIsThaana</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsThai">xmlUCSIsThai</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsTibetan">xmlUCSIsTibetan</a>			(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsUgaritic">xmlUCSIsUgaritic</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsUnifiedCanadianAboriginalSyllabics">xmlUCSIsUnifiedCanadianAboriginalSyllabics</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsVariationSelectors">xmlUCSIsVariationSelectors</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsVariationSelectorsSupplement">xmlUCSIsVariationSelectorsSupplement</a>	(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsYiRadicals">xmlUCSIsYiRadicals</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsYiSyllables">xmlUCSIsYiSyllables</a>		(int code)</pre>
<pre class="programlisting">int	<a href="#xmlUCSIsYijingHexagramSymbols">xmlUCSIsYijingHexagramSymbols</a>	(int code)</pre>
<h2>Description</h2>
<h3><a name="xmlUCSIsAegeanNumbers" id="xmlUCSIsAegeanNumbers"></a>Function: xmlUCSIsAegeanNumbers</h3><pre class="programlisting">int	xmlUCSIsAegeanNumbers		(int code)<br />
</pre><p>Check whether the character is part of AegeanNumbers UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsAlphabeticPresentationForms" id="xmlUCSIsAlphabeticPresentationForms"></a>Function: xmlUCSIsAlphabeticPresentationForms</h3><pre class="programlisting">int	xmlUCSIsAlphabeticPresentationForms	(int code)<br />
</pre><p>Check whether the character is part of AlphabeticPresentationForms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsArabic" id="xmlUCSIsArabic"></a>Function: xmlUCSIsArabic</h3><pre class="programlisting">int	xmlUCSIsArabic			(int code)<br />
</pre><p>Check whether the character is part of Arabic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsArabicPresentationFormsA" id="xmlUCSIsArabicPresentationFormsA"></a>Function: xmlUCSIsArabicPresentationFormsA</h3><pre class="programlisting">int	xmlUCSIsArabicPresentationFormsA	(int code)<br />
</pre><p>Check whether the character is part of ArabicPresentationForms-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsArabicPresentationFormsB" id="xmlUCSIsArabicPresentationFormsB"></a>Function: xmlUCSIsArabicPresentationFormsB</h3><pre class="programlisting">int	xmlUCSIsArabicPresentationFormsB	(int code)<br />
</pre><p>Check whether the character is part of ArabicPresentationForms-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsArmenian" id="xmlUCSIsArmenian"></a>Function: xmlUCSIsArmenian</h3><pre class="programlisting">int	xmlUCSIsArmenian		(int code)<br />
</pre><p>Check whether the character is part of Armenian UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsArrows" id="xmlUCSIsArrows"></a>Function: xmlUCSIsArrows</h3><pre class="programlisting">int	xmlUCSIsArrows			(int code)<br />
</pre><p>Check whether the character is part of Arrows UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsBasicLatin" id="xmlUCSIsBasicLatin"></a>Function: xmlUCSIsBasicLatin</h3><pre class="programlisting">int	xmlUCSIsBasicLatin		(int code)<br />
</pre><p>Check whether the character is part of BasicLatin UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsBengali" id="xmlUCSIsBengali"></a>Function: xmlUCSIsBengali</h3><pre class="programlisting">int	xmlUCSIsBengali			(int code)<br />
</pre><p>Check whether the character is part of Bengali UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsBlock" id="xmlUCSIsBlock"></a>Function: xmlUCSIsBlock</h3><pre class="programlisting">int	xmlUCSIsBlock			(int code, <br />					 const char * block)<br />
</pre><p>Check whether the character is part of the UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>block</tt></i>:</span></td><td>UCS block name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if false and -1 on unknown block</td></tr></tbody></table></div><h3><a name="xmlUCSIsBlockElements" id="xmlUCSIsBlockElements"></a>Function: xmlUCSIsBlockElements</h3><pre class="programlisting">int	xmlUCSIsBlockElements		(int code)<br />
</pre><p>Check whether the character is part of BlockElements UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsBopomofo" id="xmlUCSIsBopomofo"></a>Function: xmlUCSIsBopomofo</h3><pre class="programlisting">int	xmlUCSIsBopomofo		(int code)<br />
</pre><p>Check whether the character is part of Bopomofo UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsBopomofoExtended" id="xmlUCSIsBopomofoExtended"></a>Function: xmlUCSIsBopomofoExtended</h3><pre class="programlisting">int	xmlUCSIsBopomofoExtended	(int code)<br />
</pre><p>Check whether the character is part of BopomofoExtended UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsBoxDrawing" id="xmlUCSIsBoxDrawing"></a>Function: xmlUCSIsBoxDrawing</h3><pre class="programlisting">int	xmlUCSIsBoxDrawing		(int code)<br />
</pre><p>Check whether the character is part of BoxDrawing UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsBraillePatterns" id="xmlUCSIsBraillePatterns"></a>Function: xmlUCSIsBraillePatterns</h3><pre class="programlisting">int	xmlUCSIsBraillePatterns		(int code)<br />
</pre><p>Check whether the character is part of BraillePatterns UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsBuhid" id="xmlUCSIsBuhid"></a>Function: xmlUCSIsBuhid</h3><pre class="programlisting">int	xmlUCSIsBuhid			(int code)<br />
</pre><p>Check whether the character is part of Buhid UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsByzantineMusicalSymbols" id="xmlUCSIsByzantineMusicalSymbols"></a>Function: xmlUCSIsByzantineMusicalSymbols</h3><pre class="programlisting">int	xmlUCSIsByzantineMusicalSymbols	(int code)<br />
</pre><p>Check whether the character is part of ByzantineMusicalSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKCompatibility" id="xmlUCSIsCJKCompatibility"></a>Function: xmlUCSIsCJKCompatibility</h3><pre class="programlisting">int	xmlUCSIsCJKCompatibility	(int code)<br />
</pre><p>Check whether the character is part of CJKCompatibility UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKCompatibilityForms" id="xmlUCSIsCJKCompatibilityForms"></a>Function: xmlUCSIsCJKCompatibilityForms</h3><pre class="programlisting">int	xmlUCSIsCJKCompatibilityForms	(int code)<br />
</pre><p>Check whether the character is part of CJKCompatibilityForms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKCompatibilityIdeographs" id="xmlUCSIsCJKCompatibilityIdeographs"></a>Function: xmlUCSIsCJKCompatibilityIdeographs</h3><pre class="programlisting">int	xmlUCSIsCJKCompatibilityIdeographs	(int code)<br />
</pre><p>Check whether the character is part of CJKCompatibilityIdeographs UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKCompatibilityIdeographsSupplement" id="xmlUCSIsCJKCompatibilityIdeographsSupplement"></a>Function: xmlUCSIsCJKCompatibilityIdeographsSupplement</h3><pre class="programlisting">int	xmlUCSIsCJKCompatibilityIdeographsSupplement	(int code)<br />
</pre><p>Check whether the character is part of CJKCompatibilityIdeographsSupplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKRadicalsSupplement" id="xmlUCSIsCJKRadicalsSupplement"></a>Function: xmlUCSIsCJKRadicalsSupplement</h3><pre class="programlisting">int	xmlUCSIsCJKRadicalsSupplement	(int code)<br />
</pre><p>Check whether the character is part of CJKRadicalsSupplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKSymbolsandPunctuation" id="xmlUCSIsCJKSymbolsandPunctuation"></a>Function: xmlUCSIsCJKSymbolsandPunctuation</h3><pre class="programlisting">int	xmlUCSIsCJKSymbolsandPunctuation	(int code)<br />
</pre><p>Check whether the character is part of CJKSymbolsandPunctuation UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKUnifiedIdeographs" id="xmlUCSIsCJKUnifiedIdeographs"></a>Function: xmlUCSIsCJKUnifiedIdeographs</h3><pre class="programlisting">int	xmlUCSIsCJKUnifiedIdeographs	(int code)<br />
</pre><p>Check whether the character is part of CJKUnifiedIdeographs UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKUnifiedIdeographsExtensionA" id="xmlUCSIsCJKUnifiedIdeographsExtensionA"></a>Function: xmlUCSIsCJKUnifiedIdeographsExtensionA</h3><pre class="programlisting">int	xmlUCSIsCJKUnifiedIdeographsExtensionA	(int code)<br />
</pre><p>Check whether the character is part of CJKUnifiedIdeographsExtensionA UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCJKUnifiedIdeographsExtensionB" id="xmlUCSIsCJKUnifiedIdeographsExtensionB"></a>Function: xmlUCSIsCJKUnifiedIdeographsExtensionB</h3><pre class="programlisting">int	xmlUCSIsCJKUnifiedIdeographsExtensionB	(int code)<br />
</pre><p>Check whether the character is part of CJKUnifiedIdeographsExtensionB UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCat" id="xmlUCSIsCat"></a>Function: xmlUCSIsCat</h3><pre class="programlisting">int	xmlUCSIsCat			(int code, <br />					 const char * cat)<br />
</pre><p>Check whether the character is part of the UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>cat</tt></i>:</span></td><td>UCS Category name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if false and -1 on unknown category</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatC" id="xmlUCSIsCatC"></a>Function: xmlUCSIsCatC</h3><pre class="programlisting">int	xmlUCSIsCatC			(int code)<br />
</pre><p>Check whether the character is part of C UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatCc" id="xmlUCSIsCatCc"></a>Function: xmlUCSIsCatCc</h3><pre class="programlisting">int	xmlUCSIsCatCc			(int code)<br />
</pre><p>Check whether the character is part of Cc UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatCf" id="xmlUCSIsCatCf"></a>Function: xmlUCSIsCatCf</h3><pre class="programlisting">int	xmlUCSIsCatCf			(int code)<br />
</pre><p>Check whether the character is part of Cf UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatCo" id="xmlUCSIsCatCo"></a>Function: xmlUCSIsCatCo</h3><pre class="programlisting">int	xmlUCSIsCatCo			(int code)<br />
</pre><p>Check whether the character is part of Co UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatCs" id="xmlUCSIsCatCs"></a>Function: xmlUCSIsCatCs</h3><pre class="programlisting">int	xmlUCSIsCatCs			(int code)<br />
</pre><p>Check whether the character is part of Cs UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatL" id="xmlUCSIsCatL"></a>Function: xmlUCSIsCatL</h3><pre class="programlisting">int	xmlUCSIsCatL			(int code)<br />
</pre><p>Check whether the character is part of L UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatLl" id="xmlUCSIsCatLl"></a>Function: xmlUCSIsCatLl</h3><pre class="programlisting">int	xmlUCSIsCatLl			(int code)<br />
</pre><p>Check whether the character is part of Ll UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatLm" id="xmlUCSIsCatLm"></a>Function: xmlUCSIsCatLm</h3><pre class="programlisting">int	xmlUCSIsCatLm			(int code)<br />
</pre><p>Check whether the character is part of Lm UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatLo" id="xmlUCSIsCatLo"></a>Function: xmlUCSIsCatLo</h3><pre class="programlisting">int	xmlUCSIsCatLo			(int code)<br />
</pre><p>Check whether the character is part of Lo UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatLt" id="xmlUCSIsCatLt"></a>Function: xmlUCSIsCatLt</h3><pre class="programlisting">int	xmlUCSIsCatLt			(int code)<br />
</pre><p>Check whether the character is part of Lt UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatLu" id="xmlUCSIsCatLu"></a>Function: xmlUCSIsCatLu</h3><pre class="programlisting">int	xmlUCSIsCatLu			(int code)<br />
</pre><p>Check whether the character is part of Lu UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatM" id="xmlUCSIsCatM"></a>Function: xmlUCSIsCatM</h3><pre class="programlisting">int	xmlUCSIsCatM			(int code)<br />
</pre><p>Check whether the character is part of M UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatMc" id="xmlUCSIsCatMc"></a>Function: xmlUCSIsCatMc</h3><pre class="programlisting">int	xmlUCSIsCatMc			(int code)<br />
</pre><p>Check whether the character is part of Mc UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatMe" id="xmlUCSIsCatMe"></a>Function: xmlUCSIsCatMe</h3><pre class="programlisting">int	xmlUCSIsCatMe			(int code)<br />
</pre><p>Check whether the character is part of Me UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatMn" id="xmlUCSIsCatMn"></a>Function: xmlUCSIsCatMn</h3><pre class="programlisting">int	xmlUCSIsCatMn			(int code)<br />
</pre><p>Check whether the character is part of Mn UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatN" id="xmlUCSIsCatN"></a>Function: xmlUCSIsCatN</h3><pre class="programlisting">int	xmlUCSIsCatN			(int code)<br />
</pre><p>Check whether the character is part of N UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatNd" id="xmlUCSIsCatNd"></a>Function: xmlUCSIsCatNd</h3><pre class="programlisting">int	xmlUCSIsCatNd			(int code)<br />
</pre><p>Check whether the character is part of Nd UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatNl" id="xmlUCSIsCatNl"></a>Function: xmlUCSIsCatNl</h3><pre class="programlisting">int	xmlUCSIsCatNl			(int code)<br />
</pre><p>Check whether the character is part of Nl UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatNo" id="xmlUCSIsCatNo"></a>Function: xmlUCSIsCatNo</h3><pre class="programlisting">int	xmlUCSIsCatNo			(int code)<br />
</pre><p>Check whether the character is part of No UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatP" id="xmlUCSIsCatP"></a>Function: xmlUCSIsCatP</h3><pre class="programlisting">int	xmlUCSIsCatP			(int code)<br />
</pre><p>Check whether the character is part of P UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatPc" id="xmlUCSIsCatPc"></a>Function: xmlUCSIsCatPc</h3><pre class="programlisting">int	xmlUCSIsCatPc			(int code)<br />
</pre><p>Check whether the character is part of Pc UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatPd" id="xmlUCSIsCatPd"></a>Function: xmlUCSIsCatPd</h3><pre class="programlisting">int	xmlUCSIsCatPd			(int code)<br />
</pre><p>Check whether the character is part of Pd UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatPe" id="xmlUCSIsCatPe"></a>Function: xmlUCSIsCatPe</h3><pre class="programlisting">int	xmlUCSIsCatPe			(int code)<br />
</pre><p>Check whether the character is part of Pe UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatPf" id="xmlUCSIsCatPf"></a>Function: xmlUCSIsCatPf</h3><pre class="programlisting">int	xmlUCSIsCatPf			(int code)<br />
</pre><p>Check whether the character is part of Pf UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatPi" id="xmlUCSIsCatPi"></a>Function: xmlUCSIsCatPi</h3><pre class="programlisting">int	xmlUCSIsCatPi			(int code)<br />
</pre><p>Check whether the character is part of Pi UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatPo" id="xmlUCSIsCatPo"></a>Function: xmlUCSIsCatPo</h3><pre class="programlisting">int	xmlUCSIsCatPo			(int code)<br />
</pre><p>Check whether the character is part of Po UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatPs" id="xmlUCSIsCatPs"></a>Function: xmlUCSIsCatPs</h3><pre class="programlisting">int	xmlUCSIsCatPs			(int code)<br />
</pre><p>Check whether the character is part of Ps UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatS" id="xmlUCSIsCatS"></a>Function: xmlUCSIsCatS</h3><pre class="programlisting">int	xmlUCSIsCatS			(int code)<br />
</pre><p>Check whether the character is part of S UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatSc" id="xmlUCSIsCatSc"></a>Function: xmlUCSIsCatSc</h3><pre class="programlisting">int	xmlUCSIsCatSc			(int code)<br />
</pre><p>Check whether the character is part of Sc UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatSk" id="xmlUCSIsCatSk"></a>Function: xmlUCSIsCatSk</h3><pre class="programlisting">int	xmlUCSIsCatSk			(int code)<br />
</pre><p>Check whether the character is part of Sk UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatSm" id="xmlUCSIsCatSm"></a>Function: xmlUCSIsCatSm</h3><pre class="programlisting">int	xmlUCSIsCatSm			(int code)<br />
</pre><p>Check whether the character is part of Sm UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatSo" id="xmlUCSIsCatSo"></a>Function: xmlUCSIsCatSo</h3><pre class="programlisting">int	xmlUCSIsCatSo			(int code)<br />
</pre><p>Check whether the character is part of So UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatZ" id="xmlUCSIsCatZ"></a>Function: xmlUCSIsCatZ</h3><pre class="programlisting">int	xmlUCSIsCatZ			(int code)<br />
</pre><p>Check whether the character is part of Z UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatZl" id="xmlUCSIsCatZl"></a>Function: xmlUCSIsCatZl</h3><pre class="programlisting">int	xmlUCSIsCatZl			(int code)<br />
</pre><p>Check whether the character is part of Zl UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatZp" id="xmlUCSIsCatZp"></a>Function: xmlUCSIsCatZp</h3><pre class="programlisting">int	xmlUCSIsCatZp			(int code)<br />
</pre><p>Check whether the character is part of Zp UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCatZs" id="xmlUCSIsCatZs"></a>Function: xmlUCSIsCatZs</h3><pre class="programlisting">int	xmlUCSIsCatZs			(int code)<br />
</pre><p>Check whether the character is part of Zs UCS Category</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCherokee" id="xmlUCSIsCherokee"></a>Function: xmlUCSIsCherokee</h3><pre class="programlisting">int	xmlUCSIsCherokee		(int code)<br />
</pre><p>Check whether the character is part of Cherokee UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCombiningDiacriticalMarks" id="xmlUCSIsCombiningDiacriticalMarks"></a>Function: xmlUCSIsCombiningDiacriticalMarks</h3><pre class="programlisting">int	xmlUCSIsCombiningDiacriticalMarks	(int code)<br />
</pre><p>Check whether the character is part of CombiningDiacriticalMarks UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCombiningDiacriticalMarksforSymbols" id="xmlUCSIsCombiningDiacriticalMarksforSymbols"></a>Function: xmlUCSIsCombiningDiacriticalMarksforSymbols</h3><pre class="programlisting">int	xmlUCSIsCombiningDiacriticalMarksforSymbols	(int code)<br />
</pre><p>Check whether the character is part of CombiningDiacriticalMarksforSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCombiningHalfMarks" id="xmlUCSIsCombiningHalfMarks"></a>Function: xmlUCSIsCombiningHalfMarks</h3><pre class="programlisting">int	xmlUCSIsCombiningHalfMarks	(int code)<br />
</pre><p>Check whether the character is part of CombiningHalfMarks UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCombiningMarksforSymbols" id="xmlUCSIsCombiningMarksforSymbols"></a>Function: xmlUCSIsCombiningMarksforSymbols</h3><pre class="programlisting">int	xmlUCSIsCombiningMarksforSymbols	(int code)<br />
</pre><p>Check whether the character is part of CombiningMarksforSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsControlPictures" id="xmlUCSIsControlPictures"></a>Function: xmlUCSIsControlPictures</h3><pre class="programlisting">int	xmlUCSIsControlPictures		(int code)<br />
</pre><p>Check whether the character is part of ControlPictures UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCurrencySymbols" id="xmlUCSIsCurrencySymbols"></a>Function: xmlUCSIsCurrencySymbols</h3><pre class="programlisting">int	xmlUCSIsCurrencySymbols		(int code)<br />
</pre><p>Check whether the character is part of CurrencySymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCypriotSyllabary" id="xmlUCSIsCypriotSyllabary"></a>Function: xmlUCSIsCypriotSyllabary</h3><pre class="programlisting">int	xmlUCSIsCypriotSyllabary	(int code)<br />
</pre><p>Check whether the character is part of CypriotSyllabary UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCyrillic" id="xmlUCSIsCyrillic"></a>Function: xmlUCSIsCyrillic</h3><pre class="programlisting">int	xmlUCSIsCyrillic		(int code)<br />
</pre><p>Check whether the character is part of Cyrillic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsCyrillicSupplement" id="xmlUCSIsCyrillicSupplement"></a>Function: xmlUCSIsCyrillicSupplement</h3><pre class="programlisting">int	xmlUCSIsCyrillicSupplement	(int code)<br />
</pre><p>Check whether the character is part of CyrillicSupplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsDeseret" id="xmlUCSIsDeseret"></a>Function: xmlUCSIsDeseret</h3><pre class="programlisting">int	xmlUCSIsDeseret			(int code)<br />
</pre><p>Check whether the character is part of Deseret UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsDevanagari" id="xmlUCSIsDevanagari"></a>Function: xmlUCSIsDevanagari</h3><pre class="programlisting">int	xmlUCSIsDevanagari		(int code)<br />
</pre><p>Check whether the character is part of Devanagari UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsDingbats" id="xmlUCSIsDingbats"></a>Function: xmlUCSIsDingbats</h3><pre class="programlisting">int	xmlUCSIsDingbats		(int code)<br />
</pre><p>Check whether the character is part of Dingbats UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsEnclosedAlphanumerics" id="xmlUCSIsEnclosedAlphanumerics"></a>Function: xmlUCSIsEnclosedAlphanumerics</h3><pre class="programlisting">int	xmlUCSIsEnclosedAlphanumerics	(int code)<br />
</pre><p>Check whether the character is part of EnclosedAlphanumerics UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsEnclosedCJKLettersandMonths" id="xmlUCSIsEnclosedCJKLettersandMonths"></a>Function: xmlUCSIsEnclosedCJKLettersandMonths</h3><pre class="programlisting">int	xmlUCSIsEnclosedCJKLettersandMonths	(int code)<br />
</pre><p>Check whether the character is part of EnclosedCJKLettersandMonths UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsEthiopic" id="xmlUCSIsEthiopic"></a>Function: xmlUCSIsEthiopic</h3><pre class="programlisting">int	xmlUCSIsEthiopic		(int code)<br />
</pre><p>Check whether the character is part of Ethiopic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGeneralPunctuation" id="xmlUCSIsGeneralPunctuation"></a>Function: xmlUCSIsGeneralPunctuation</h3><pre class="programlisting">int	xmlUCSIsGeneralPunctuation	(int code)<br />
</pre><p>Check whether the character is part of GeneralPunctuation UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGeometricShapes" id="xmlUCSIsGeometricShapes"></a>Function: xmlUCSIsGeometricShapes</h3><pre class="programlisting">int	xmlUCSIsGeometricShapes		(int code)<br />
</pre><p>Check whether the character is part of GeometricShapes UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGeorgian" id="xmlUCSIsGeorgian"></a>Function: xmlUCSIsGeorgian</h3><pre class="programlisting">int	xmlUCSIsGeorgian		(int code)<br />
</pre><p>Check whether the character is part of Georgian UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGothic" id="xmlUCSIsGothic"></a>Function: xmlUCSIsGothic</h3><pre class="programlisting">int	xmlUCSIsGothic			(int code)<br />
</pre><p>Check whether the character is part of Gothic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGreek" id="xmlUCSIsGreek"></a>Function: xmlUCSIsGreek</h3><pre class="programlisting">int	xmlUCSIsGreek			(int code)<br />
</pre><p>Check whether the character is part of Greek UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGreekExtended" id="xmlUCSIsGreekExtended"></a>Function: xmlUCSIsGreekExtended</h3><pre class="programlisting">int	xmlUCSIsGreekExtended		(int code)<br />
</pre><p>Check whether the character is part of GreekExtended UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGreekandCoptic" id="xmlUCSIsGreekandCoptic"></a>Function: xmlUCSIsGreekandCoptic</h3><pre class="programlisting">int	xmlUCSIsGreekandCoptic		(int code)<br />
</pre><p>Check whether the character is part of GreekandCoptic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGujarati" id="xmlUCSIsGujarati"></a>Function: xmlUCSIsGujarati</h3><pre class="programlisting">int	xmlUCSIsGujarati		(int code)<br />
</pre><p>Check whether the character is part of Gujarati UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsGurmukhi" id="xmlUCSIsGurmukhi"></a>Function: xmlUCSIsGurmukhi</h3><pre class="programlisting">int	xmlUCSIsGurmukhi		(int code)<br />
</pre><p>Check whether the character is part of Gurmukhi UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHalfwidthandFullwidthForms" id="xmlUCSIsHalfwidthandFullwidthForms"></a>Function: xmlUCSIsHalfwidthandFullwidthForms</h3><pre class="programlisting">int	xmlUCSIsHalfwidthandFullwidthForms	(int code)<br />
</pre><p>Check whether the character is part of HalfwidthandFullwidthForms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHangulCompatibilityJamo" id="xmlUCSIsHangulCompatibilityJamo"></a>Function: xmlUCSIsHangulCompatibilityJamo</h3><pre class="programlisting">int	xmlUCSIsHangulCompatibilityJamo	(int code)<br />
</pre><p>Check whether the character is part of HangulCompatibilityJamo UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHangulJamo" id="xmlUCSIsHangulJamo"></a>Function: xmlUCSIsHangulJamo</h3><pre class="programlisting">int	xmlUCSIsHangulJamo		(int code)<br />
</pre><p>Check whether the character is part of HangulJamo UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHangulSyllables" id="xmlUCSIsHangulSyllables"></a>Function: xmlUCSIsHangulSyllables</h3><pre class="programlisting">int	xmlUCSIsHangulSyllables		(int code)<br />
</pre><p>Check whether the character is part of HangulSyllables UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHanunoo" id="xmlUCSIsHanunoo"></a>Function: xmlUCSIsHanunoo</h3><pre class="programlisting">int	xmlUCSIsHanunoo			(int code)<br />
</pre><p>Check whether the character is part of Hanunoo UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHebrew" id="xmlUCSIsHebrew"></a>Function: xmlUCSIsHebrew</h3><pre class="programlisting">int	xmlUCSIsHebrew			(int code)<br />
</pre><p>Check whether the character is part of Hebrew UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHighPrivateUseSurrogates" id="xmlUCSIsHighPrivateUseSurrogates"></a>Function: xmlUCSIsHighPrivateUseSurrogates</h3><pre class="programlisting">int	xmlUCSIsHighPrivateUseSurrogates	(int code)<br />
</pre><p>Check whether the character is part of HighPrivateUseSurrogates UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHighSurrogates" id="xmlUCSIsHighSurrogates"></a>Function: xmlUCSIsHighSurrogates</h3><pre class="programlisting">int	xmlUCSIsHighSurrogates		(int code)<br />
</pre><p>Check whether the character is part of HighSurrogates UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsHiragana" id="xmlUCSIsHiragana"></a>Function: xmlUCSIsHiragana</h3><pre class="programlisting">int	xmlUCSIsHiragana		(int code)<br />
</pre><p>Check whether the character is part of Hiragana UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsIPAExtensions" id="xmlUCSIsIPAExtensions"></a>Function: xmlUCSIsIPAExtensions</h3><pre class="programlisting">int	xmlUCSIsIPAExtensions		(int code)<br />
</pre><p>Check whether the character is part of IPAExtensions UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsIdeographicDescriptionCharacters" id="xmlUCSIsIdeographicDescriptionCharacters"></a>Function: xmlUCSIsIdeographicDescriptionCharacters</h3><pre class="programlisting">int	xmlUCSIsIdeographicDescriptionCharacters	(int code)<br />
</pre><p>Check whether the character is part of IdeographicDescriptionCharacters UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsKanbun" id="xmlUCSIsKanbun"></a>Function: xmlUCSIsKanbun</h3><pre class="programlisting">int	xmlUCSIsKanbun			(int code)<br />
</pre><p>Check whether the character is part of Kanbun UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsKangxiRadicals" id="xmlUCSIsKangxiRadicals"></a>Function: xmlUCSIsKangxiRadicals</h3><pre class="programlisting">int	xmlUCSIsKangxiRadicals		(int code)<br />
</pre><p>Check whether the character is part of KangxiRadicals UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsKannada" id="xmlUCSIsKannada"></a>Function: xmlUCSIsKannada</h3><pre class="programlisting">int	xmlUCSIsKannada			(int code)<br />
</pre><p>Check whether the character is part of Kannada UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsKatakana" id="xmlUCSIsKatakana"></a>Function: xmlUCSIsKatakana</h3><pre class="programlisting">int	xmlUCSIsKatakana		(int code)<br />
</pre><p>Check whether the character is part of Katakana UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsKatakanaPhoneticExtensions" id="xmlUCSIsKatakanaPhoneticExtensions"></a>Function: xmlUCSIsKatakanaPhoneticExtensions</h3><pre class="programlisting">int	xmlUCSIsKatakanaPhoneticExtensions	(int code)<br />
</pre><p>Check whether the character is part of KatakanaPhoneticExtensions UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsKhmer" id="xmlUCSIsKhmer"></a>Function: xmlUCSIsKhmer</h3><pre class="programlisting">int	xmlUCSIsKhmer			(int code)<br />
</pre><p>Check whether the character is part of Khmer UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsKhmerSymbols" id="xmlUCSIsKhmerSymbols"></a>Function: xmlUCSIsKhmerSymbols</h3><pre class="programlisting">int	xmlUCSIsKhmerSymbols		(int code)<br />
</pre><p>Check whether the character is part of KhmerSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLao" id="xmlUCSIsLao"></a>Function: xmlUCSIsLao</h3><pre class="programlisting">int	xmlUCSIsLao			(int code)<br />
</pre><p>Check whether the character is part of Lao UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLatin1Supplement" id="xmlUCSIsLatin1Supplement"></a>Function: xmlUCSIsLatin1Supplement</h3><pre class="programlisting">int	xmlUCSIsLatin1Supplement	(int code)<br />
</pre><p>Check whether the character is part of Latin-1Supplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLatinExtendedA" id="xmlUCSIsLatinExtendedA"></a>Function: xmlUCSIsLatinExtendedA</h3><pre class="programlisting">int	xmlUCSIsLatinExtendedA		(int code)<br />
</pre><p>Check whether the character is part of LatinExtended-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLatinExtendedAdditional" id="xmlUCSIsLatinExtendedAdditional"></a>Function: xmlUCSIsLatinExtendedAdditional</h3><pre class="programlisting">int	xmlUCSIsLatinExtendedAdditional	(int code)<br />
</pre><p>Check whether the character is part of LatinExtendedAdditional UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLatinExtendedB" id="xmlUCSIsLatinExtendedB"></a>Function: xmlUCSIsLatinExtendedB</h3><pre class="programlisting">int	xmlUCSIsLatinExtendedB		(int code)<br />
</pre><p>Check whether the character is part of LatinExtended-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLetterlikeSymbols" id="xmlUCSIsLetterlikeSymbols"></a>Function: xmlUCSIsLetterlikeSymbols</h3><pre class="programlisting">int	xmlUCSIsLetterlikeSymbols	(int code)<br />
</pre><p>Check whether the character is part of LetterlikeSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLimbu" id="xmlUCSIsLimbu"></a>Function: xmlUCSIsLimbu</h3><pre class="programlisting">int	xmlUCSIsLimbu			(int code)<br />
</pre><p>Check whether the character is part of Limbu UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLinearBIdeograms" id="xmlUCSIsLinearBIdeograms"></a>Function: xmlUCSIsLinearBIdeograms</h3><pre class="programlisting">int	xmlUCSIsLinearBIdeograms	(int code)<br />
</pre><p>Check whether the character is part of LinearBIdeograms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLinearBSyllabary" id="xmlUCSIsLinearBSyllabary"></a>Function: xmlUCSIsLinearBSyllabary</h3><pre class="programlisting">int	xmlUCSIsLinearBSyllabary	(int code)<br />
</pre><p>Check whether the character is part of LinearBSyllabary UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsLowSurrogates" id="xmlUCSIsLowSurrogates"></a>Function: xmlUCSIsLowSurrogates</h3><pre class="programlisting">int	xmlUCSIsLowSurrogates		(int code)<br />
</pre><p>Check whether the character is part of LowSurrogates UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMalayalam" id="xmlUCSIsMalayalam"></a>Function: xmlUCSIsMalayalam</h3><pre class="programlisting">int	xmlUCSIsMalayalam		(int code)<br />
</pre><p>Check whether the character is part of Malayalam UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMathematicalAlphanumericSymbols" id="xmlUCSIsMathematicalAlphanumericSymbols"></a>Function: xmlUCSIsMathematicalAlphanumericSymbols</h3><pre class="programlisting">int	xmlUCSIsMathematicalAlphanumericSymbols	(int code)<br />
</pre><p>Check whether the character is part of MathematicalAlphanumericSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMathematicalOperators" id="xmlUCSIsMathematicalOperators"></a>Function: xmlUCSIsMathematicalOperators</h3><pre class="programlisting">int	xmlUCSIsMathematicalOperators	(int code)<br />
</pre><p>Check whether the character is part of MathematicalOperators UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMiscellaneousMathematicalSymbolsA" id="xmlUCSIsMiscellaneousMathematicalSymbolsA"></a>Function: xmlUCSIsMiscellaneousMathematicalSymbolsA</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousMathematicalSymbolsA	(int code)<br />
</pre><p>Check whether the character is part of MiscellaneousMathematicalSymbols-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMiscellaneousMathematicalSymbolsB" id="xmlUCSIsMiscellaneousMathematicalSymbolsB"></a>Function: xmlUCSIsMiscellaneousMathematicalSymbolsB</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousMathematicalSymbolsB	(int code)<br />
</pre><p>Check whether the character is part of MiscellaneousMathematicalSymbols-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMiscellaneousSymbols" id="xmlUCSIsMiscellaneousSymbols"></a>Function: xmlUCSIsMiscellaneousSymbols</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousSymbols	(int code)<br />
</pre><p>Check whether the character is part of MiscellaneousSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMiscellaneousSymbolsandArrows" id="xmlUCSIsMiscellaneousSymbolsandArrows"></a>Function: xmlUCSIsMiscellaneousSymbolsandArrows</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousSymbolsandArrows	(int code)<br />
</pre><p>Check whether the character is part of MiscellaneousSymbolsandArrows UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMiscellaneousTechnical" id="xmlUCSIsMiscellaneousTechnical"></a>Function: xmlUCSIsMiscellaneousTechnical</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousTechnical	(int code)<br />
</pre><p>Check whether the character is part of MiscellaneousTechnical UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMongolian" id="xmlUCSIsMongolian"></a>Function: xmlUCSIsMongolian</h3><pre class="programlisting">int	xmlUCSIsMongolian		(int code)<br />
</pre><p>Check whether the character is part of Mongolian UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMusicalSymbols" id="xmlUCSIsMusicalSymbols"></a>Function: xmlUCSIsMusicalSymbols</h3><pre class="programlisting">int	xmlUCSIsMusicalSymbols		(int code)<br />
</pre><p>Check whether the character is part of MusicalSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsMyanmar" id="xmlUCSIsMyanmar"></a>Function: xmlUCSIsMyanmar</h3><pre class="programlisting">int	xmlUCSIsMyanmar			(int code)<br />
</pre><p>Check whether the character is part of Myanmar UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsNumberForms" id="xmlUCSIsNumberForms"></a>Function: xmlUCSIsNumberForms</h3><pre class="programlisting">int	xmlUCSIsNumberForms		(int code)<br />
</pre><p>Check whether the character is part of NumberForms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsOgham" id="xmlUCSIsOgham"></a>Function: xmlUCSIsOgham</h3><pre class="programlisting">int	xmlUCSIsOgham			(int code)<br />
</pre><p>Check whether the character is part of Ogham UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsOldItalic" id="xmlUCSIsOldItalic"></a>Function: xmlUCSIsOldItalic</h3><pre class="programlisting">int	xmlUCSIsOldItalic		(int code)<br />
</pre><p>Check whether the character is part of OldItalic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsOpticalCharacterRecognition" id="xmlUCSIsOpticalCharacterRecognition"></a>Function: xmlUCSIsOpticalCharacterRecognition</h3><pre class="programlisting">int	xmlUCSIsOpticalCharacterRecognition	(int code)<br />
</pre><p>Check whether the character is part of OpticalCharacterRecognition UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsOriya" id="xmlUCSIsOriya"></a>Function: xmlUCSIsOriya</h3><pre class="programlisting">int	xmlUCSIsOriya			(int code)<br />
</pre><p>Check whether the character is part of Oriya UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsOsmanya" id="xmlUCSIsOsmanya"></a>Function: xmlUCSIsOsmanya</h3><pre class="programlisting">int	xmlUCSIsOsmanya			(int code)<br />
</pre><p>Check whether the character is part of Osmanya UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsPhoneticExtensions" id="xmlUCSIsPhoneticExtensions"></a>Function: xmlUCSIsPhoneticExtensions</h3><pre class="programlisting">int	xmlUCSIsPhoneticExtensions	(int code)<br />
</pre><p>Check whether the character is part of PhoneticExtensions UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsPrivateUse" id="xmlUCSIsPrivateUse"></a>Function: xmlUCSIsPrivateUse</h3><pre class="programlisting">int	xmlUCSIsPrivateUse		(int code)<br />
</pre><p>Check whether the character is part of PrivateUse UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsPrivateUseArea" id="xmlUCSIsPrivateUseArea"></a>Function: xmlUCSIsPrivateUseArea</h3><pre class="programlisting">int	xmlUCSIsPrivateUseArea		(int code)<br />
</pre><p>Check whether the character is part of PrivateUseArea UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsRunic" id="xmlUCSIsRunic"></a>Function: xmlUCSIsRunic</h3><pre class="programlisting">int	xmlUCSIsRunic			(int code)<br />
</pre><p>Check whether the character is part of Runic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsShavian" id="xmlUCSIsShavian"></a>Function: xmlUCSIsShavian</h3><pre class="programlisting">int	xmlUCSIsShavian			(int code)<br />
</pre><p>Check whether the character is part of Shavian UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSinhala" id="xmlUCSIsSinhala"></a>Function: xmlUCSIsSinhala</h3><pre class="programlisting">int	xmlUCSIsSinhala			(int code)<br />
</pre><p>Check whether the character is part of Sinhala UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSmallFormVariants" id="xmlUCSIsSmallFormVariants"></a>Function: xmlUCSIsSmallFormVariants</h3><pre class="programlisting">int	xmlUCSIsSmallFormVariants	(int code)<br />
</pre><p>Check whether the character is part of SmallFormVariants UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSpacingModifierLetters" id="xmlUCSIsSpacingModifierLetters"></a>Function: xmlUCSIsSpacingModifierLetters</h3><pre class="programlisting">int	xmlUCSIsSpacingModifierLetters	(int code)<br />
</pre><p>Check whether the character is part of SpacingModifierLetters UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSpecials" id="xmlUCSIsSpecials"></a>Function: xmlUCSIsSpecials</h3><pre class="programlisting">int	xmlUCSIsSpecials		(int code)<br />
</pre><p>Check whether the character is part of Specials UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSuperscriptsandSubscripts" id="xmlUCSIsSuperscriptsandSubscripts"></a>Function: xmlUCSIsSuperscriptsandSubscripts</h3><pre class="programlisting">int	xmlUCSIsSuperscriptsandSubscripts	(int code)<br />
</pre><p>Check whether the character is part of SuperscriptsandSubscripts UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSupplementalArrowsA" id="xmlUCSIsSupplementalArrowsA"></a>Function: xmlUCSIsSupplementalArrowsA</h3><pre class="programlisting">int	xmlUCSIsSupplementalArrowsA	(int code)<br />
</pre><p>Check whether the character is part of SupplementalArrows-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSupplementalArrowsB" id="xmlUCSIsSupplementalArrowsB"></a>Function: xmlUCSIsSupplementalArrowsB</h3><pre class="programlisting">int	xmlUCSIsSupplementalArrowsB	(int code)<br />
</pre><p>Check whether the character is part of SupplementalArrows-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSupplementalMathematicalOperators" id="xmlUCSIsSupplementalMathematicalOperators"></a>Function: xmlUCSIsSupplementalMathematicalOperators</h3><pre class="programlisting">int	xmlUCSIsSupplementalMathematicalOperators	(int code)<br />
</pre><p>Check whether the character is part of SupplementalMathematicalOperators UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSupplementaryPrivateUseAreaA" id="xmlUCSIsSupplementaryPrivateUseAreaA"></a>Function: xmlUCSIsSupplementaryPrivateUseAreaA</h3><pre class="programlisting">int	xmlUCSIsSupplementaryPrivateUseAreaA	(int code)<br />
</pre><p>Check whether the character is part of SupplementaryPrivateUseArea-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSupplementaryPrivateUseAreaB" id="xmlUCSIsSupplementaryPrivateUseAreaB"></a>Function: xmlUCSIsSupplementaryPrivateUseAreaB</h3><pre class="programlisting">int	xmlUCSIsSupplementaryPrivateUseAreaB	(int code)<br />
</pre><p>Check whether the character is part of SupplementaryPrivateUseArea-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsSyriac" id="xmlUCSIsSyriac"></a>Function: xmlUCSIsSyriac</h3><pre class="programlisting">int	xmlUCSIsSyriac			(int code)<br />
</pre><p>Check whether the character is part of Syriac UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsTagalog" id="xmlUCSIsTagalog"></a>Function: xmlUCSIsTagalog</h3><pre class="programlisting">int	xmlUCSIsTagalog			(int code)<br />
</pre><p>Check whether the character is part of Tagalog UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsTagbanwa" id="xmlUCSIsTagbanwa"></a>Function: xmlUCSIsTagbanwa</h3><pre class="programlisting">int	xmlUCSIsTagbanwa		(int code)<br />
</pre><p>Check whether the character is part of Tagbanwa UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsTags" id="xmlUCSIsTags"></a>Function: xmlUCSIsTags</h3><pre class="programlisting">int	xmlUCSIsTags			(int code)<br />
</pre><p>Check whether the character is part of Tags UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsTaiLe" id="xmlUCSIsTaiLe"></a>Function: xmlUCSIsTaiLe</h3><pre class="programlisting">int	xmlUCSIsTaiLe			(int code)<br />
</pre><p>Check whether the character is part of TaiLe UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsTaiXuanJingSymbols" id="xmlUCSIsTaiXuanJingSymbols"></a>Function: xmlUCSIsTaiXuanJingSymbols</h3><pre class="programlisting">int	xmlUCSIsTaiXuanJingSymbols	(int code)<br />
</pre><p>Check whether the character is part of TaiXuanJingSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsTamil" id="xmlUCSIsTamil"></a>Function: xmlUCSIsTamil</h3><pre class="programlisting">int	xmlUCSIsTamil			(int code)<br />
</pre><p>Check whether the character is part of Tamil UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsTelugu" id="xmlUCSIsTelugu"></a>Function: xmlUCSIsTelugu</h3><pre class="programlisting">int	xmlUCSIsTelugu			(int code)<br />
</pre><p>Check whether the character is part of Telugu UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsThaana" id="xmlUCSIsThaana"></a>Function: xmlUCSIsThaana</h3><pre class="programlisting">int	xmlUCSIsThaana			(int code)<br />
</pre><p>Check whether the character is part of Thaana UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsThai" id="xmlUCSIsThai"></a>Function: xmlUCSIsThai</h3><pre class="programlisting">int	xmlUCSIsThai			(int code)<br />
</pre><p>Check whether the character is part of Thai UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsTibetan" id="xmlUCSIsTibetan"></a>Function: xmlUCSIsTibetan</h3><pre class="programlisting">int	xmlUCSIsTibetan			(int code)<br />
</pre><p>Check whether the character is part of Tibetan UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsUgaritic" id="xmlUCSIsUgaritic"></a>Function: xmlUCSIsUgaritic</h3><pre class="programlisting">int	xmlUCSIsUgaritic		(int code)<br />
</pre><p>Check whether the character is part of Ugaritic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsUnifiedCanadianAboriginalSyllabics" id="xmlUCSIsUnifiedCanadianAboriginalSyllabics"></a>Function: xmlUCSIsUnifiedCanadianAboriginalSyllabics</h3><pre class="programlisting">int	xmlUCSIsUnifiedCanadianAboriginalSyllabics	(int code)<br />
</pre><p>Check whether the character is part of UnifiedCanadianAboriginalSyllabics UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsVariationSelectors" id="xmlUCSIsVariationSelectors"></a>Function: xmlUCSIsVariationSelectors</h3><pre class="programlisting">int	xmlUCSIsVariationSelectors	(int code)<br />
</pre><p>Check whether the character is part of VariationSelectors UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsVariationSelectorsSupplement" id="xmlUCSIsVariationSelectorsSupplement"></a>Function: xmlUCSIsVariationSelectorsSupplement</h3><pre class="programlisting">int	xmlUCSIsVariationSelectorsSupplement	(int code)<br />
</pre><p>Check whether the character is part of VariationSelectorsSupplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsYiRadicals" id="xmlUCSIsYiRadicals"></a>Function: xmlUCSIsYiRadicals</h3><pre class="programlisting">int	xmlUCSIsYiRadicals		(int code)<br />
</pre><p>Check whether the character is part of YiRadicals UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsYiSyllables" id="xmlUCSIsYiSyllables"></a>Function: xmlUCSIsYiSyllables</h3><pre class="programlisting">int	xmlUCSIsYiSyllables		(int code)<br />
</pre><p>Check whether the character is part of YiSyllables UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlUCSIsYijingHexagramSymbols" id="xmlUCSIsYijingHexagramSymbols"></a>Function: xmlUCSIsYijingHexagramSymbols</h3><pre class="programlisting">int	xmlUCSIsYijingHexagramSymbols	(int code)<br />
</pre><p>Check whether the character is part of YijingHexagramSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
