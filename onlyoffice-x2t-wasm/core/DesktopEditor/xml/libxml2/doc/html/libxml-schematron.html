<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module schematron from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module schematron from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-schemasInternals.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-schemasInternals.html">schemasInternals</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-threads.html">threads</a></th><td><a accesskey="n" href="libxml-threads.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>interface to the XML Schematron validity checking. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlSchematron">xmlSchematron</a><br />struct _xmlSchematron
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Structure <a href="#xmlSchematronParserCtxt">xmlSchematronParserCtxt</a><br />struct _xmlSchematronParserCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-schematron.html#xmlSchematronParserCtxt">xmlSchematronParserCtxt</a> * <a name="xmlSchematronParserCtxtPtr" id="xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-schematron.html#xmlSchematron">xmlSchematron</a> * <a name="xmlSchematronPtr" id="xmlSchematronPtr">xmlSchematronPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchematronValidCtxt">xmlSchematronValidCtxt</a><br />struct _xmlSchematronValidCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-schematron.html#xmlSchematronValidCtxt">xmlSchematronValidCtxt</a> * <a name="xmlSchematronValidCtxtPtr" id="xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlSchematronValidOptions">xmlSchematronValidOptions</a>
</pre><pre class="programlisting">void	<a href="#xmlSchematronFree">xmlSchematronFree</a>		(<a href="libxml-schematron.html#xmlSchematronPtr">xmlSchematronPtr</a> schema)</pre>
<pre class="programlisting">void	<a href="#xmlSchematronFreeParserCtxt">xmlSchematronFreeParserCtxt</a>	(<a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">void	<a href="#xmlSchematronFreeValidCtxt">xmlSchematronFreeValidCtxt</a>	(<a href="libxml-schematron.html#xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a>	<a href="#xmlSchematronNewDocParserCtxt">xmlSchematronNewDocParserCtxt</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a>	<a href="#xmlSchematronNewMemParserCtxt">xmlSchematronNewMemParserCtxt</a>	(const char * buffer, <br />							 int size)</pre>
<pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a>	<a href="#xmlSchematronNewParserCtxt">xmlSchematronNewParserCtxt</a>	(const char * URL)</pre>
<pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a>	<a href="#xmlSchematronNewValidCtxt">xmlSchematronNewValidCtxt</a>	(<a href="libxml-schematron.html#xmlSchematronPtr">xmlSchematronPtr</a> schema, <br />							 int options)</pre>
<pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronPtr">xmlSchematronPtr</a>	<a href="#xmlSchematronParse">xmlSchematronParse</a>	(<a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">void	<a href="#xmlSchematronSetValidStructuredErrors">xmlSchematronSetValidStructuredErrors</a>	(<a href="libxml-schematron.html#xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a> ctxt, <br />						 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> serror, <br />						 void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlSchematronValidateDoc">xmlSchematronValidateDoc</a>	(<a href="libxml-schematron.html#xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> instance)</pre>
<pre class="programlisting">Function type: <a href="#xmlSchematronValidityErrorFunc">xmlSchematronValidityErrorFunc</a>
void	<a href="#xmlSchematronValidityErrorFunc">xmlSchematronValidityErrorFunc</a>	(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre>
<pre class="programlisting">Function type: <a href="#xmlSchematronValidityWarningFunc">xmlSchematronValidityWarningFunc</a>
void	<a href="#xmlSchematronValidityWarningFunc">xmlSchematronValidityWarningFunc</a>	(void * ctx, <br />						 const char * msg, <br />						 ... ...)
</pre>
<h2>Description</h2>
<h3><a name="xmlSchematron" id="xmlSchematron">Structure xmlSchematron</a></h3><pre class="programlisting">Structure xmlSchematron<br />struct _xmlSchematron {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlSchematronParserCtxt" id="xmlSchematronParserCtxt">Structure xmlSchematronParserCtxt</a></h3><pre class="programlisting">Structure xmlSchematronParserCtxt<br />struct _xmlSchematronParserCtxt {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlSchematronValidCtxt" id="xmlSchematronValidCtxt">Structure xmlSchematronValidCtxt</a></h3><pre class="programlisting">Structure xmlSchematronValidCtxt<br />struct _xmlSchematronValidCtxt {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlSchematronValidOptions" id="xmlSchematronValidOptions">xmlSchematronValidOptions</a></h3><pre class="programlisting">Enum xmlSchematronValidOptions {
    <a name="XML_SCHEMATRON_OUT_QUIET" id="XML_SCHEMATRON_OUT_QUIET">XML_SCHEMATRON_OUT_QUIET</a> = 1 : quiet no report
    <a name="XML_SCHEMATRON_OUT_TEXT" id="XML_SCHEMATRON_OUT_TEXT">XML_SCHEMATRON_OUT_TEXT</a> = 2 : build a textual report
    <a name="XML_SCHEMATRON_OUT_XML" id="XML_SCHEMATRON_OUT_XML">XML_SCHEMATRON_OUT_XML</a> = 4 : output SVRL
    <a name="XML_SCHEMATRON_OUT_ERROR" id="XML_SCHEMATRON_OUT_ERROR">XML_SCHEMATRON_OUT_ERROR</a> = 8 : output via <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a>
    <a name="XML_SCHEMATRON_OUT_FILE" id="XML_SCHEMATRON_OUT_FILE">XML_SCHEMATRON_OUT_FILE</a> = 256 : output to a file descriptor
    <a name="XML_SCHEMATRON_OUT_BUFFER" id="XML_SCHEMATRON_OUT_BUFFER">XML_SCHEMATRON_OUT_BUFFER</a> = 512 : output to a buffer
    <a name="XML_SCHEMATRON_OUT_IO" id="XML_SCHEMATRON_OUT_IO">XML_SCHEMATRON_OUT_IO</a> = 1024 : output to I/O mechanism
}
</pre><h3><a name="xmlSchematronFree" id="xmlSchematronFree"></a>Function: xmlSchematronFree</h3><pre class="programlisting">void	xmlSchematronFree		(<a href="libxml-schematron.html#xmlSchematronPtr">xmlSchematronPtr</a> schema)<br />
</pre><p>Deallocate a Schematron structure.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>schema</tt></i>:</span></td><td>a schema structure</td></tr></tbody></table></div><h3><a name="xmlSchematronFreeParserCtxt" id="xmlSchematronFreeParserCtxt"></a>Function: xmlSchematronFreeParserCtxt</h3><pre class="programlisting">void	xmlSchematronFreeParserCtxt	(<a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a> ctxt)<br />
</pre><p>Free the resources associated to the schema parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the schema parser context</td></tr></tbody></table></div><h3><a name="xmlSchematronFreeValidCtxt" id="xmlSchematronFreeValidCtxt"></a>Function: xmlSchematronFreeValidCtxt</h3><pre class="programlisting">void	xmlSchematronFreeValidCtxt	(<a href="libxml-schematron.html#xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a> ctxt)<br />
</pre><p>Free the resources associated to the schema validation context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the schema validation context</td></tr></tbody></table></div><h3><a name="xmlSchematronNewDocParserCtxt" id="xmlSchematronNewDocParserCtxt"></a>Function: xmlSchematronNewDocParserCtxt</h3><pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a>	xmlSchematronNewDocParserCtxt	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Create an XML Schematrons parse context for that document. NB. The document may be modified during the parsing process.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>a preparsed document tree</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the parser context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchematronNewMemParserCtxt" id="xmlSchematronNewMemParserCtxt"></a>Function: xmlSchematronNewMemParserCtxt</h3><pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a>	xmlSchematronNewMemParserCtxt	(const char * buffer, <br />							 int size)<br />
</pre><p>Create an XML Schematrons parse context for that memory buffer expected to contain an XML Schematrons file.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a pointer to a char array containing the schemas</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the parser context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchematronNewParserCtxt" id="xmlSchematronNewParserCtxt"></a>Function: xmlSchematronNewParserCtxt</h3><pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a>	xmlSchematronNewParserCtxt	(const char * URL)<br />
</pre><p>Create an XML Schematrons parse context for that file/resource expected to contain an XML Schematrons file.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the location of the schema</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the parser context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchematronNewValidCtxt" id="xmlSchematronNewValidCtxt"></a>Function: xmlSchematronNewValidCtxt</h3><pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a>	xmlSchematronNewValidCtxt	(<a href="libxml-schematron.html#xmlSchematronPtr">xmlSchematronPtr</a> schema, <br />							 int options)<br />
</pre><p>Create an XML Schematrons validation context based on the given schema.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>schema</tt></i>:</span></td><td>a precompiled XML Schematrons</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of <a href="libxml-schematron.html#xmlSchematronValidOptions">xmlSchematronValidOptions</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the validation context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchematronParse" id="xmlSchematronParse"></a>Function: xmlSchematronParse</h3><pre class="programlisting"><a href="libxml-schematron.html#xmlSchematronPtr">xmlSchematronPtr</a>	xmlSchematronParse	(<a href="libxml-schematron.html#xmlSchematronParserCtxtPtr">xmlSchematronParserCtxtPtr</a> ctxt)<br />
</pre><p>parse a schema definition resource and build an internal XML Shema struture which can be used to validate instances.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a schema validation context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the internal XML Schematron structure built from the resource or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSchematronSetValidStructuredErrors" id="xmlSchematronSetValidStructuredErrors"></a>Function: xmlSchematronSetValidStructuredErrors</h3><pre class="programlisting">void	xmlSchematronSetValidStructuredErrors	(<a href="libxml-schematron.html#xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a> ctxt, <br />						 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> serror, <br />						 void * ctx)<br />
</pre><p>Set the structured error callback</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a Schematron validation context</td></tr><tr><td><span class="term"><i><tt>serror</tt></i>:</span></td><td>the structured error function</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the functions context</td></tr></tbody></table></div><h3><a name="xmlSchematronValidateDoc" id="xmlSchematronValidateDoc"></a>Function: xmlSchematronValidateDoc</h3><pre class="programlisting">int	xmlSchematronValidateDoc	(<a href="libxml-schematron.html#xmlSchematronValidCtxtPtr">xmlSchematronValidCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> instance)<br />
</pre><p>Validate a tree instance against the schematron</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the schema validation context</td></tr><tr><td><span class="term"><i><tt>instance</tt></i>:</span></td><td>the document instace tree</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of internal error and an error count otherwise.</td></tr></tbody></table></div><h3><a name="xmlSchematronValidityErrorFunc" id="xmlSchematronValidityErrorFunc"></a>Function type: xmlSchematronValidityErrorFunc</h3><pre class="programlisting">Function type: xmlSchematronValidityErrorFunc
void	xmlSchematronValidityErrorFunc	(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre><p>Signature of an error callback from a Schematron validation</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the validation context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra arguments</td></tr></tbody></table></div><br />
<h3><a name="xmlSchematronValidityWarningFunc" id="xmlSchematronValidityWarningFunc"></a>Function type: xmlSchematronValidityWarningFunc</h3><pre class="programlisting">Function type: xmlSchematronValidityWarningFunc
void	xmlSchematronValidityWarningFunc	(void * ctx, <br />						 const char * msg, <br />						 ... ...)
</pre><p>Signature of a warning callback from a Schematron validation</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the validation context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra arguments</td></tr></tbody></table></div><br />
<p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
