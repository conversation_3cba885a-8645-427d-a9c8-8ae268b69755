<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlautomata from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlautomata from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlIO.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlIO.html">xmlIO</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlerror.html">xmlerror</a></th><td><a accesskey="n" href="libxml-xmlerror.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>the API to build regexp automata </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlAutomata">xmlAutomata</a><br />struct _xmlAutomata
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlautomata.html#xmlAutomata">xmlAutomata</a> * <a name="xmlAutomataPtr" id="xmlAutomataPtr">xmlAutomataPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlAutomataState">xmlAutomataState</a><br />struct _xmlAutomataState
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlautomata.html#xmlAutomataState">xmlAutomataState</a> * <a name="xmlAutomataStatePtr" id="xmlAutomataStatePtr">xmlAutomataStatePtr</a>
</pre><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	<a href="#xmlAutomataCompile">xmlAutomataCompile</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataGetInitState">xmlAutomataGetInitState</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)</pre>
<pre class="programlisting">int	<a href="#xmlAutomataIsDeterminist">xmlAutomataIsDeterminist</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewAllTrans">xmlAutomataNewAllTrans</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />						 int lax)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewCountTrans">xmlAutomataNewCountTrans</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 int min, <br />							 int max, <br />							 void * data)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewCountTrans2">xmlAutomataNewCountTrans2</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token2, <br />							 int min, <br />							 int max, <br />							 void * data)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewCountedTrans">xmlAutomataNewCountedTrans</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 int counter)</pre>
<pre class="programlisting">int	<a href="#xmlAutomataNewCounter">xmlAutomataNewCounter</a>		(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />					 int min, <br />					 int max)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewCounterTrans">xmlAutomataNewCounterTrans</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 int counter)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewEpsilon">xmlAutomataNewEpsilon</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewNegTrans">xmlAutomataNewNegTrans</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token2, <br />						 void * data)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewOnceTrans">xmlAutomataNewOnceTrans</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />						 int min, <br />						 int max, <br />						 void * data)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewOnceTrans2">xmlAutomataNewOnceTrans2</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token2, <br />							 int min, <br />							 int max, <br />							 void * data)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewState">xmlAutomataNewState</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewTransition">xmlAutomataNewTransition</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 void * data)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	<a href="#xmlAutomataNewTransition2">xmlAutomataNewTransition2</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token2, <br />							 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlAutomataSetFinalState">xmlAutomataSetFinalState</a>	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />					 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> state)</pre>
<pre class="programlisting">void	<a href="#xmlFreeAutomata">xmlFreeAutomata</a>			(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)</pre>
<pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a>	<a href="#xmlNewAutomata">xmlNewAutomata</a>		(void)</pre>
<h2>Description</h2>
<h3><a name="xmlAutomata" id="xmlAutomata">Structure xmlAutomata</a></h3><pre class="programlisting">Structure xmlAutomata<br />struct _xmlAutomata {
The content of this structure is not made public by the API.
}</pre>
      A libxml automata description, It can be compiled into a regexp
    <h3><a name="xmlAutomataState" id="xmlAutomataState">Structure xmlAutomataState</a></h3><pre class="programlisting">Structure xmlAutomataState<br />struct _xmlAutomataState {
The content of this structure is not made public by the API.
}</pre>
      A state int the automata description,
    <h3><a name="xmlAutomataCompile" id="xmlAutomataCompile"></a>Function: xmlAutomataCompile</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	xmlAutomataCompile	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)<br />
</pre><p>Compile the automata into a Reg Exp ready for being executed. The automata should be free after this point.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the compiled regexp or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataGetInitState" id="xmlAutomataGetInitState"></a>Function: xmlAutomataGetInitState</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataGetInitState	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)<br />
</pre><p>Initial state lookup</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the initial state of the automata</td></tr></tbody></table></div><h3><a name="xmlAutomataIsDeterminist" id="xmlAutomataIsDeterminist"></a>Function: xmlAutomataIsDeterminist</h3><pre class="programlisting">int	xmlAutomataIsDeterminist	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)<br />
</pre><p>Checks if an automata is determinist.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if not, and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewAllTrans" id="xmlAutomataNewAllTrans"></a>Function: xmlAutomataNewAllTrans</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewAllTrans	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />						 int lax)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds a an ALL transition from the @from state to the target state. That transition is an epsilon transition allowed only when all transitions from the @from node have been activated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>lax</tt></i>:</span></td><td>allow to transition if not all all transitions have been activated</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewCountTrans" id="xmlAutomataNewCountTrans"></a>Function: xmlAutomataNewCountTrans</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewCountTrans	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 int min, <br />							 int max, <br />							 void * data)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds a transition from the @from state to the target state activated by a succession of input of value @token and whose number is between @min and @max</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>min</tt></i>:</span></td><td>the minimum successive occurences of token</td></tr><tr><td><span class="term"><i><tt>max</tt></i>:</span></td><td>the maximum successive occurences of token</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data associated to the transition</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewCountTrans2" id="xmlAutomataNewCountTrans2"></a>Function: xmlAutomataNewCountTrans2</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewCountTrans2	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token2, <br />							 int min, <br />							 int max, <br />							 void * data)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds a transition from the @from state to the target state activated by a succession of input of value @token and @token2 and whose number is between @min and @max</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>token2</tt></i>:</span></td><td>the second input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>min</tt></i>:</span></td><td>the minimum successive occurences of token</td></tr><tr><td><span class="term"><i><tt>max</tt></i>:</span></td><td>the maximum successive occurences of token</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data associated to the transition</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewCountedTrans" id="xmlAutomataNewCountedTrans"></a>Function: xmlAutomataNewCountedTrans</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewCountedTrans	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 int counter)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds an epsilon transition from the @from state to the target state which will increment the counter provided</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>counter</tt></i>:</span></td><td>the counter associated to that transition</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewCounter" id="xmlAutomataNewCounter"></a>Function: xmlAutomataNewCounter</h3><pre class="programlisting">int	xmlAutomataNewCounter		(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />					 int min, <br />					 int max)<br />
</pre><p>Create a new counter</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>min</tt></i>:</span></td><td>the minimal value on the counter</td></tr><tr><td><span class="term"><i><tt>max</tt></i>:</span></td><td>the maximal value on the counter</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the counter number or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewCounterTrans" id="xmlAutomataNewCounterTrans"></a>Function: xmlAutomataNewCounterTrans</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewCounterTrans	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 int counter)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds an epsilon transition from the @from state to the target state which will be allowed only if the counter is within the right range.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>counter</tt></i>:</span></td><td>the counter associated to that transition</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewEpsilon" id="xmlAutomataNewEpsilon"></a>Function: xmlAutomataNewEpsilon</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewEpsilon	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds an epsilon transition from the @from state to the target state</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewNegTrans" id="xmlAutomataNewNegTrans"></a>Function: xmlAutomataNewNegTrans</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewNegTrans	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token2, <br />						 void * data)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds a transition from the @from state to the target state activated by any value except (@token,@token2) Note that if @token2 is not NULL, then (X, NULL) won't match to follow # the semantic of XSD ##other</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the first input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>token2</tt></i>:</span></td><td>the second input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data passed to the callback function if the transition is activated</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewOnceTrans" id="xmlAutomataNewOnceTrans"></a>Function: xmlAutomataNewOnceTrans</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewOnceTrans	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />						 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />						 int min, <br />						 int max, <br />						 void * data)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds a transition from the @from state to the target state activated by a succession of input of value @token and whose number is between @min and @max, moreover that transition can only be crossed once.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>min</tt></i>:</span></td><td>the minimum successive occurences of token</td></tr><tr><td><span class="term"><i><tt>max</tt></i>:</span></td><td>the maximum successive occurences of token</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data associated to the transition</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewOnceTrans2" id="xmlAutomataNewOnceTrans2"></a>Function: xmlAutomataNewOnceTrans2</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewOnceTrans2	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token2, <br />							 int min, <br />							 int max, <br />							 void * data)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds a transition from the @from state to the target state activated by a succession of input of value @token and @token2 and whose number is between @min and @max, moreover that transition can only be crossed once.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>token2</tt></i>:</span></td><td>the second input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>min</tt></i>:</span></td><td>the minimum successive occurences of token</td></tr><tr><td><span class="term"><i><tt>max</tt></i>:</span></td><td>the maximum successive occurences of token</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data associated to the transition</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewState" id="xmlAutomataNewState"></a>Function: xmlAutomataNewState</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewState	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)<br />
</pre><p>Create a new disconnected state in the automata</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewTransition" id="xmlAutomataNewTransition"></a>Function: xmlAutomataNewTransition</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewTransition	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 void * data)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds a transition from the @from state to the target state activated by the value of @token</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data passed to the callback function if the transition is activated</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataNewTransition2" id="xmlAutomataNewTransition2"></a>Function: xmlAutomataNewTransition2</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a>	xmlAutomataNewTransition2	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> from, <br />							 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> to, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />							 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token2, <br />							 void * data)<br />
</pre><p>If @to is NULL, this creates first a new target state in the automata and then adds a transition from the @from state to the target state activated by the value of @token</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>the starting point of the transition</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>the target point of the transition or NULL</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the first input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>token2</tt></i>:</span></td><td>the second input string associated to that transition</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data passed to the callback function if the transition is activated</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the target state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAutomataSetFinalState" id="xmlAutomataSetFinalState"></a>Function: xmlAutomataSetFinalState</h3><pre class="programlisting">int	xmlAutomataSetFinalState	(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am, <br />					 <a href="libxml-xmlautomata.html#xmlAutomataStatePtr">xmlAutomataStatePtr</a> state)<br />
</pre><p>Makes that state a final state</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr><tr><td><span class="term"><i><tt>state</tt></i>:</span></td><td>a state in this automata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlFreeAutomata" id="xmlFreeAutomata"></a>Function: xmlFreeAutomata</h3><pre class="programlisting">void	xmlFreeAutomata			(<a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a> am)<br />
</pre><p>Free an automata</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>am</tt></i>:</span></td><td>an automata</td></tr></tbody></table></div><h3><a name="xmlNewAutomata" id="xmlNewAutomata"></a>Function: xmlNewAutomata</h3><pre class="programlisting"><a href="libxml-xmlautomata.html#xmlAutomataPtr">xmlAutomataPtr</a>	xmlNewAutomata		(void)<br />
</pre><p>Create a new automata</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new object or NULL in case of failure</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
