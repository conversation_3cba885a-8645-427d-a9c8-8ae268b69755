<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlreader from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlreader from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlmodule.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlmodule.html">xmlmodule</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlregexp.html">xmlregexp</a></th><td><a accesskey="n" href="libxml-xmlregexp.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>API of the XML streaming API based on C# interfaces. </p><h2>Table of Contents</h2><pre class="programlisting">Enum <a href="#xmlParserProperties">xmlParserProperties</a>
</pre><pre class="programlisting">Enum <a href="#xmlParserSeverities">xmlParserSeverities</a>
</pre><pre class="programlisting">Enum <a href="#xmlReaderTypes">xmlReaderTypes</a>
</pre><pre class="programlisting">Structure <a href="#xmlTextReader">xmlTextReader</a><br />struct _xmlTextReader
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef void * <a name="xmlTextReaderLocatorPtr" id="xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlTextReaderMode">xmlTextReaderMode</a>
</pre><pre class="programlisting">Typedef <a href="libxml-xmlreader.html#xmlTextReader">xmlTextReader</a> * <a name="xmlTextReaderPtr" id="xmlTextReaderPtr">xmlTextReaderPtr</a>
</pre><pre class="programlisting">void	<a href="#xmlFreeTextReader">xmlFreeTextReader</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	<a href="#xmlNewTextReader">xmlNewTextReader</a>	(<a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />						 const char * URI)</pre>
<pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	<a href="#xmlNewTextReaderFilename">xmlNewTextReaderFilename</a>	(const char * URI)</pre>
<pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	<a href="#xmlReaderForDoc">xmlReaderForDoc</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	<a href="#xmlReaderForFd">xmlReaderForFd</a>	(int fd, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	<a href="#xmlReaderForFile">xmlReaderForFile</a>	(const char * filename, <br />						 const char * encoding, <br />						 int options)</pre>
<pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	<a href="#xmlReaderForIO">xmlReaderForIO</a>	(<a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />					 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	<a href="#xmlReaderForMemory">xmlReaderForMemory</a>	(const char * buffer, <br />						 int size, <br />						 const char * URL, <br />						 const char * encoding, <br />						 int options)</pre>
<pre class="programlisting">int	<a href="#xmlReaderNewDoc">xmlReaderNewDoc</a>			(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlReaderNewFd">xmlReaderNewFd</a>			(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 int fd, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlReaderNewFile">xmlReaderNewFile</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const char * filename, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlReaderNewIO">xmlReaderNewIO</a>			(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />					 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlReaderNewMemory">xmlReaderNewMemory</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const char * buffer, <br />					 int size, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlReaderNewWalker">xmlReaderNewWalker</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	<a href="#xmlReaderWalker">xmlReaderWalker</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderAttributeCount">xmlTextReaderAttributeCount</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderBaseUri">xmlTextReaderBaseUri</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">long	<a href="#xmlTextReaderByteConsumed">xmlTextReaderByteConsumed</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderClose">xmlTextReaderClose</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstBaseUri">xmlTextReaderConstBaseUri</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstEncoding">xmlTextReaderConstEncoding</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstLocalName">xmlTextReaderConstLocalName</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstName">xmlTextReaderConstName</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstNamespaceUri">xmlTextReaderConstNamespaceUri</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstPrefix">xmlTextReaderConstPrefix</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstString">xmlTextReaderConstString</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstValue">xmlTextReaderConstValue</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstXmlLang">xmlTextReaderConstXmlLang</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderConstXmlVersion">xmlTextReaderConstXmlVersion</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlTextReaderCurrentDoc">xmlTextReaderCurrentDoc</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlTextReaderCurrentNode">xmlTextReaderCurrentNode</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderDepth">xmlTextReaderDepth</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">Function type: <a href="#xmlTextReaderErrorFunc">xmlTextReaderErrorFunc</a>
void	<a href="#xmlTextReaderErrorFunc">xmlTextReaderErrorFunc</a>		(void * arg, <br />					 const char * msg, <br />					 <a href="libxml-xmlreader.html#xmlParserSeverities">xmlParserSeverities</a> severity, <br />					 <a href="libxml-xmlreader.html#xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a> locator)
</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlTextReaderExpand">xmlTextReaderExpand</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderGetAttribute">xmlTextReaderGetAttribute</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderGetAttributeNo">xmlTextReaderGetAttributeNo</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 int no)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderGetAttributeNs">xmlTextReaderGetAttributeNs</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localName, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)</pre>
<pre class="programlisting">void	<a href="#xmlTextReaderGetErrorHandler">xmlTextReaderGetErrorHandler</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlreader.html#xmlTextReaderErrorFunc">xmlTextReaderErrorFunc</a> * f, <br />					 void ** arg)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderGetParserColumnNumber">xmlTextReaderGetParserColumnNumber</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderGetParserLineNumber">xmlTextReaderGetParserLineNumber</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderGetParserProp">xmlTextReaderGetParserProp</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 int prop)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlTextReaderGetRemainder">xmlTextReaderGetRemainder</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderHasAttributes">xmlTextReaderHasAttributes</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderHasValue">xmlTextReaderHasValue</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderIsDefault">xmlTextReaderIsDefault</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderIsEmptyElement">xmlTextReaderIsEmptyElement</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderIsNamespaceDecl">xmlTextReaderIsNamespaceDecl</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderIsValid">xmlTextReaderIsValid</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderLocalName">xmlTextReaderLocalName</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderLocatorBaseURI">xmlTextReaderLocatorBaseURI</a>	(<a href="libxml-xmlreader.html#xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a> locator)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderLocatorLineNumber">xmlTextReaderLocatorLineNumber</a>	(<a href="libxml-xmlreader.html#xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a> locator)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderLookupNamespace">xmlTextReaderLookupNamespace</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderMoveToAttribute">xmlTextReaderMoveToAttribute</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderMoveToAttributeNo">xmlTextReaderMoveToAttributeNo</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 int no)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderMoveToAttributeNs">xmlTextReaderMoveToAttributeNs</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localName, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderMoveToElement">xmlTextReaderMoveToElement</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderMoveToFirstAttribute">xmlTextReaderMoveToFirstAttribute</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderMoveToNextAttribute">xmlTextReaderMoveToNextAttribute</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderName">xmlTextReaderName</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderNamespaceUri">xmlTextReaderNamespaceUri</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderNext">xmlTextReaderNext</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderNextSibling">xmlTextReaderNextSibling</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderNodeType">xmlTextReaderNodeType</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderNormalization">xmlTextReaderNormalization</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderPrefix">xmlTextReaderPrefix</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlTextReaderPreserve">xmlTextReaderPreserve</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderPreservePattern">xmlTextReaderPreservePattern</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pattern, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** namespaces)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderQuoteChar">xmlTextReaderQuoteChar</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderRead">xmlTextReaderRead</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderReadAttributeValue">xmlTextReaderReadAttributeValue</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderReadInnerXml">xmlTextReaderReadInnerXml</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderReadOuterXml">xmlTextReaderReadOuterXml</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderReadState">xmlTextReaderReadState</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderReadString">xmlTextReaderReadString</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderRelaxNGSetSchema">xmlTextReaderRelaxNGSetSchema</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-relaxng.html#xmlRelaxNGPtr">xmlRelaxNGPtr</a> schema)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderRelaxNGValidate">xmlTextReaderRelaxNGValidate</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const char * rng)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderRelaxNGValidateCtxt">xmlTextReaderRelaxNGValidateCtxt</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 <a href="libxml-relaxng.html#xmlRelaxNGValidCtxtPtr">xmlRelaxNGValidCtxtPtr</a> ctxt, <br />						 int options)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderSchemaValidate">xmlTextReaderSchemaValidate</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const char * xsd)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderSchemaValidateCtxt">xmlTextReaderSchemaValidateCtxt</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 int options)</pre>
<pre class="programlisting">void	<a href="#xmlTextReaderSetErrorHandler">xmlTextReaderSetErrorHandler</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlreader.html#xmlTextReaderErrorFunc">xmlTextReaderErrorFunc</a> f, <br />					 void * arg)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderSetParserProp">xmlTextReaderSetParserProp</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 int prop, <br />					 int value)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderSetSchema">xmlTextReaderSetSchema</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a> schema)</pre>
<pre class="programlisting">void	<a href="#xmlTextReaderSetStructuredErrorHandler">xmlTextReaderSetStructuredErrorHandler</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> f, <br />						 void * arg)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderSetup">xmlTextReaderSetup</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlTextReaderStandalone">xmlTextReaderStandalone</a>		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderValue">xmlTextReaderValue</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlTextReaderXmlLang">xmlTextReaderXmlLang</a>	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)</pre>
<h2>Description</h2>
<h3>Enum <a name="xmlParserProperties" id="xmlParserProperties">xmlParserProperties</a></h3><pre class="programlisting">Enum xmlParserProperties {
    <a name="XML_PARSER_LOADDTD" id="XML_PARSER_LOADDTD">XML_PARSER_LOADDTD</a> = 1
    <a name="XML_PARSER_DEFAULTATTRS" id="XML_PARSER_DEFAULTATTRS">XML_PARSER_DEFAULTATTRS</a> = 2
    <a name="XML_PARSER_VALIDATE" id="XML_PARSER_VALIDATE">XML_PARSER_VALIDATE</a> = 3
    <a name="XML_PARSER_SUBST_ENTITIES" id="XML_PARSER_SUBST_ENTITIES">XML_PARSER_SUBST_ENTITIES</a> = 4
}
</pre><h3>Enum <a name="xmlParserSeverities" id="xmlParserSeverities">xmlParserSeverities</a></h3><pre class="programlisting">Enum xmlParserSeverities {
    <a name="XML_PARSER_SEVERITY_VALIDITY_WARNING" id="XML_PARSER_SEVERITY_VALIDITY_WARNING">XML_PARSER_SEVERITY_VALIDITY_WARNING</a> = 1
    <a name="XML_PARSER_SEVERITY_VALIDITY_ERROR" id="XML_PARSER_SEVERITY_VALIDITY_ERROR">XML_PARSER_SEVERITY_VALIDITY_ERROR</a> = 2
    <a name="XML_PARSER_SEVERITY_WARNING" id="XML_PARSER_SEVERITY_WARNING">XML_PARSER_SEVERITY_WARNING</a> = 3
    <a name="XML_PARSER_SEVERITY_ERROR" id="XML_PARSER_SEVERITY_ERROR">XML_PARSER_SEVERITY_ERROR</a> = 4
}
</pre><h3>Enum <a name="xmlReaderTypes" id="xmlReaderTypes">xmlReaderTypes</a></h3><pre class="programlisting">Enum xmlReaderTypes {
    <a name="XML_READER_TYPE_NONE" id="XML_READER_TYPE_NONE">XML_READER_TYPE_NONE</a> = 0
    <a name="XML_READER_TYPE_ELEMENT" id="XML_READER_TYPE_ELEMENT">XML_READER_TYPE_ELEMENT</a> = 1
    <a name="XML_READER_TYPE_ATTRIBUTE" id="XML_READER_TYPE_ATTRIBUTE">XML_READER_TYPE_ATTRIBUTE</a> = 2
    <a name="XML_READER_TYPE_TEXT" id="XML_READER_TYPE_TEXT">XML_READER_TYPE_TEXT</a> = 3
    <a name="XML_READER_TYPE_CDATA" id="XML_READER_TYPE_CDATA">XML_READER_TYPE_CDATA</a> = 4
    <a name="XML_READER_TYPE_ENTITY_REFERENCE" id="XML_READER_TYPE_ENTITY_REFERENCE">XML_READER_TYPE_ENTITY_REFERENCE</a> = 5
    <a name="XML_READER_TYPE_ENTITY" id="XML_READER_TYPE_ENTITY">XML_READER_TYPE_ENTITY</a> = 6
    <a name="XML_READER_TYPE_PROCESSING_INSTRUCTION" id="XML_READER_TYPE_PROCESSING_INSTRUCTION">XML_READER_TYPE_PROCESSING_INSTRUCTION</a> = 7
    <a name="XML_READER_TYPE_COMMENT" id="XML_READER_TYPE_COMMENT">XML_READER_TYPE_COMMENT</a> = 8
    <a name="XML_READER_TYPE_DOCUMENT" id="XML_READER_TYPE_DOCUMENT">XML_READER_TYPE_DOCUMENT</a> = 9
    <a name="XML_READER_TYPE_DOCUMENT_TYPE" id="XML_READER_TYPE_DOCUMENT_TYPE">XML_READER_TYPE_DOCUMENT_TYPE</a> = 10
    <a name="XML_READER_TYPE_DOCUMENT_FRAGMENT" id="XML_READER_TYPE_DOCUMENT_FRAGMENT">XML_READER_TYPE_DOCUMENT_FRAGMENT</a> = 11
    <a name="XML_READER_TYPE_NOTATION" id="XML_READER_TYPE_NOTATION">XML_READER_TYPE_NOTATION</a> = 12
    <a name="XML_READER_TYPE_WHITESPACE" id="XML_READER_TYPE_WHITESPACE">XML_READER_TYPE_WHITESPACE</a> = 13
    <a name="XML_READER_TYPE_SIGNIFICANT_WHITESPACE" id="XML_READER_TYPE_SIGNIFICANT_WHITESPACE">XML_READER_TYPE_SIGNIFICANT_WHITESPACE</a> = 14
    <a name="XML_READER_TYPE_END_ELEMENT" id="XML_READER_TYPE_END_ELEMENT">XML_READER_TYPE_END_ELEMENT</a> = 15
    <a name="XML_READER_TYPE_END_ENTITY" id="XML_READER_TYPE_END_ENTITY">XML_READER_TYPE_END_ENTITY</a> = 16
    <a name="XML_READER_TYPE_XML_DECLARATION" id="XML_READER_TYPE_XML_DECLARATION">XML_READER_TYPE_XML_DECLARATION</a> = 17
}
</pre><h3><a name="xmlTextReader" id="xmlTextReader">Structure xmlTextReader</a></h3><pre class="programlisting">Structure xmlTextReader<br />struct _xmlTextReader {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlTextReaderMode" id="xmlTextReaderMode">xmlTextReaderMode</a></h3><pre class="programlisting">Enum xmlTextReaderMode {
    <a name="XML_TEXTREADER_MODE_INITIAL" id="XML_TEXTREADER_MODE_INITIAL">XML_TEXTREADER_MODE_INITIAL</a> = 0
    <a name="XML_TEXTREADER_MODE_INTERACTIVE" id="XML_TEXTREADER_MODE_INTERACTIVE">XML_TEXTREADER_MODE_INTERACTIVE</a> = 1
    <a name="XML_TEXTREADER_MODE_ERROR" id="XML_TEXTREADER_MODE_ERROR">XML_TEXTREADER_MODE_ERROR</a> = 2
    <a name="XML_TEXTREADER_MODE_EOF" id="XML_TEXTREADER_MODE_EOF">XML_TEXTREADER_MODE_EOF</a> = 3
    <a name="XML_TEXTREADER_MODE_CLOSED" id="XML_TEXTREADER_MODE_CLOSED">XML_TEXTREADER_MODE_CLOSED</a> = 4
    <a name="XML_TEXTREADER_MODE_READING" id="XML_TEXTREADER_MODE_READING">XML_TEXTREADER_MODE_READING</a> = 5
}
</pre>
      Pointer to an xmlReader context.
    <h3><a name="xmlFreeTextReader" id="xmlFreeTextReader"></a>Function: xmlFreeTextReader</h3><pre class="programlisting">void	xmlFreeTextReader		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Deallocate all the resources associated to the reader</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a></td></tr></tbody></table></div><h3><a name="xmlNewTextReader" id="xmlNewTextReader"></a>Function: xmlNewTextReader</h3><pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	xmlNewTextReader	(<a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />						 const char * URI)<br />
</pre><p>Create an <a href="libxml-xmlreader.html#xmlTextReader">xmlTextReader</a> structure fed with @input</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> used to read data</td></tr><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI information for the source if available</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlNewTextReaderFilename" id="xmlNewTextReaderFilename"></a>Function: xmlNewTextReaderFilename</h3><pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	xmlNewTextReaderFilename	(const char * URI)<br />
</pre><p>Create an <a href="libxml-xmlreader.html#xmlTextReader">xmlTextReader</a> structure fed with the resource at @URI</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI of the resource to process</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlReaderForDoc" id="xmlReaderForDoc"></a>Function: xmlReaderForDoc</h3><pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	xmlReaderForDoc	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Create an xmltextReader for an XML in-memory document. The parsing flags @options are a combination of xmlParserOption.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to a zero terminated string</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new reader or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlReaderForFd" id="xmlReaderForFd"></a>Function: xmlReaderForFd</h3><pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	xmlReaderForFd	(int fd, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Create an xmltextReader for an XML from a file descriptor. The parsing flags @options are a combination of xmlParserOption. NOTE that the file descriptor will not be closed when the reader is closed or reset.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>fd</tt></i>:</span></td><td>an open file descriptor</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new reader or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlReaderForFile" id="xmlReaderForFile"></a>Function: xmlReaderForFile</h3><pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	xmlReaderForFile	(const char * filename, <br />						 const char * encoding, <br />						 int options)<br />
</pre><p>parse an XML file from the filesystem or the network. The parsing flags @options are a combination of xmlParserOption.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file or URL</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new reader or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlReaderForIO" id="xmlReaderForIO"></a>Function: xmlReaderForIO</h3><pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	xmlReaderForIO	(<a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />					 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Create an xmltextReader for an XML document from I/O functions and source. The parsing flags @options are a combination of xmlParserOption.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ioread</tt></i>:</span></td><td>an I/O read function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new reader or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlReaderForMemory" id="xmlReaderForMemory"></a>Function: xmlReaderForMemory</h3><pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	xmlReaderForMemory	(const char * buffer, <br />						 int size, <br />						 const char * URL, <br />						 const char * encoding, <br />						 int options)<br />
</pre><p>Create an xmltextReader for an XML in-memory document. The parsing flags @options are a combination of xmlParserOption.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new reader or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlReaderNewDoc" id="xmlReaderNewDoc"></a>Function: xmlReaderNewDoc</h3><pre class="programlisting">int	xmlReaderNewDoc			(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Setup an xmltextReader to parse an XML in-memory document. The parsing flags @options are a combination of xmlParserOption. This reuses the existing @reader xmlTextReader.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>an XML reader</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to a zero terminated string</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlReaderNewFd" id="xmlReaderNewFd"></a>Function: xmlReaderNewFd</h3><pre class="programlisting">int	xmlReaderNewFd			(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 int fd, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Setup an xmltextReader to parse an XML from a file descriptor. NOTE that the file descriptor will not be closed when the reader is closed or reset. The parsing flags @options are a combination of xmlParserOption. This reuses the existing @reader xmlTextReader.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>an XML reader</td></tr><tr><td><span class="term"><i><tt>fd</tt></i>:</span></td><td>an open file descriptor</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlReaderNewFile" id="xmlReaderNewFile"></a>Function: xmlReaderNewFile</h3><pre class="programlisting">int	xmlReaderNewFile		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const char * filename, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML file from the filesystem or the network. The parsing flags @options are a combination of xmlParserOption. This reuses the existing @reader xmlTextReader.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>an XML reader</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file or URL</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlReaderNewIO" id="xmlReaderNewIO"></a>Function: xmlReaderNewIO</h3><pre class="programlisting">int	xmlReaderNewIO			(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />					 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Setup an xmltextReader to parse an XML document from I/O functions and source. The parsing flags @options are a combination of xmlParserOption. This reuses the existing @reader xmlTextReader.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>an XML reader</td></tr><tr><td><span class="term"><i><tt>ioread</tt></i>:</span></td><td>an I/O read function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlReaderNewMemory" id="xmlReaderNewMemory"></a>Function: xmlReaderNewMemory</h3><pre class="programlisting">int	xmlReaderNewMemory		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const char * buffer, <br />					 int size, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Setup an xmltextReader to parse an XML in-memory document. The parsing flags @options are a combination of xmlParserOption. This reuses the existing @reader xmlTextReader.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>an XML reader</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlReaderNewWalker" id="xmlReaderNewWalker"></a>Function: xmlReaderNewWalker</h3><pre class="programlisting">int	xmlReaderNewWalker		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Setup an xmltextReader to parse a preparsed XML document. This reuses the existing @reader xmlTextReader.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>an XML reader</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>a preparsed document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlReaderWalker" id="xmlReaderWalker"></a>Function: xmlReaderWalker</h3><pre class="programlisting"><a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a>	xmlReaderWalker	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Create an xmltextReader for a preparsed document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>a preparsed document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new reader or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderAttributeCount" id="xmlTextReaderAttributeCount"></a>Function: xmlTextReaderAttributeCount</h3><pre class="programlisting">int	xmlTextReaderAttributeCount	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Provides the number of attributes of the current node</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 i no attributes, -1 in case of error or the <a href="libxml-SAX.html#attribute">attribute</a> count</td></tr></tbody></table></div><h3><a name="xmlTextReaderBaseUri" id="xmlTextReaderBaseUri"></a>Function: xmlTextReaderBaseUri</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderBaseUri	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The base URI of the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the base URI or NULL if not available, if non NULL it need to be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderByteConsumed" id="xmlTextReaderByteConsumed"></a>Function: xmlTextReaderByteConsumed</h3><pre class="programlisting">long	xmlTextReaderByteConsumed	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>This function provides the current index of the parser used by the reader, relative to the start of the current entity. This function actually just wraps a call to xmlBytesConsumed() for the parser context associated with the reader. See xmlBytesConsumed() for more information.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>an XML reader</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the index in bytes from the beginning of the entity or -1 in case the index could not be computed.</td></tr></tbody></table></div><h3><a name="xmlTextReaderClose" id="xmlTextReaderClose"></a>Function: xmlTextReaderClose</h3><pre class="programlisting">int	xmlTextReaderClose		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>This method releases any resources allocated by the current instance changes the state to Closed and close any underlying input.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstBaseUri" id="xmlTextReaderConstBaseUri"></a>Function: xmlTextReaderConstBaseUri</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstBaseUri	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The base URI of the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the base URI or NULL if not available, the string will be deallocated with the reader</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstEncoding" id="xmlTextReaderConstEncoding"></a>Function: xmlTextReaderConstEncoding</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstEncoding	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Determine the encoding of the document being read.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the encoding of the document or NULL in case of error. The string is deallocated with the reader.</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstLocalName" id="xmlTextReaderConstLocalName"></a>Function: xmlTextReaderConstLocalName</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstLocalName	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The local name of the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the local name or NULL if not available, the string will be deallocated with the reader.</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstName" id="xmlTextReaderConstName"></a>Function: xmlTextReaderConstName</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstName	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The qualified name of the node, equal to Prefix :LocalName.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the local name or NULL if not available, the string is deallocated with the reader.</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstNamespaceUri" id="xmlTextReaderConstNamespaceUri"></a>Function: xmlTextReaderConstNamespaceUri</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstNamespaceUri	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The URI defining the namespace associated with the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the namespace URI or NULL if not available, the string will be deallocated with the reader</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstPrefix" id="xmlTextReaderConstPrefix"></a>Function: xmlTextReaderConstPrefix</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstPrefix	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>A shorthand <a href="libxml-SAX.html#reference">reference</a> to the namespace associated with the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the prefix or NULL if not available, the string is deallocated with the reader.</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstString" id="xmlTextReaderConstString"></a>Function: xmlTextReaderConstString</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstString	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>Get an interned string from the reader, allows for example to speedup string name comparisons</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string to intern.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an interned copy of the string or NULL in case of error. The string will be deallocated with the reader.</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstValue" id="xmlTextReaderConstValue"></a>Function: xmlTextReaderConstValue</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstValue	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Provides the text value of the node if present</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the string or NULL if not available. The result will be deallocated on the next Read() operation.</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstXmlLang" id="xmlTextReaderConstXmlLang"></a>Function: xmlTextReaderConstXmlLang</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstXmlLang	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The xml:lang scope within which the node resides.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the xml:lang value or NULL if none exists.</td></tr></tbody></table></div><h3><a name="xmlTextReaderConstXmlVersion" id="xmlTextReaderConstXmlVersion"></a>Function: xmlTextReaderConstXmlVersion</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderConstXmlVersion	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Determine the XML version of the document being read.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the XML version of the document or NULL in case of error. The string is deallocated with the reader.</td></tr></tbody></table></div><h3><a name="xmlTextReaderCurrentDoc" id="xmlTextReaderCurrentDoc"></a>Function: xmlTextReaderCurrentDoc</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlTextReaderCurrentDoc	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Hacking interface allowing to get the <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> correponding to the current document being accessed by the xmlTextReader. NOTE: as a result of this call, the reader will not destroy the associated XML document and calling xmlFreeDoc() on the result is needed once the reader parsing has finished.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderCurrentNode" id="xmlTextReaderCurrentNode"></a>Function: xmlTextReaderCurrentNode</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlTextReaderCurrentNode	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Hacking interface allowing to get the <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> correponding to the current node being accessed by the xmlTextReader. This is dangerous because the underlying node may be destroyed on the next Reads.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderDepth" id="xmlTextReaderDepth"></a>Function: xmlTextReaderDepth</h3><pre class="programlisting">int	xmlTextReaderDepth		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The depth of the node in the tree.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the depth or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderErrorFunc" id="xmlTextReaderErrorFunc"></a>Function type: xmlTextReaderErrorFunc</h3><pre class="programlisting">Function type: xmlTextReaderErrorFunc
void	xmlTextReaderErrorFunc		(void * arg, <br />					 const char * msg, <br />					 <a href="libxml-xmlreader.html#xmlParserSeverities">xmlParserSeverities</a> severity, <br />					 <a href="libxml-xmlreader.html#xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a> locator)
</pre><p>Signature of an error callback from a reader parser</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>the user argument</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message</td></tr><tr><td><span class="term"><i><tt>severity</tt></i>:</span></td><td>the severity of the error</td></tr><tr><td><span class="term"><i><tt>locator</tt></i>:</span></td><td>a locator indicating where the error occured</td></tr></tbody></table></div><br />
<h3><a name="xmlTextReaderExpand" id="xmlTextReaderExpand"></a>Function: xmlTextReaderExpand</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlTextReaderExpand	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Reads the contents of the current node and the full subtree. It then makes the subtree available until the next xmlTextReaderRead() call</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a node pointer valid until the next xmlTextReaderRead() call or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderGetAttribute" id="xmlTextReaderGetAttribute"></a>Function: xmlTextReaderGetAttribute</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderGetAttribute	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Provides the value of the <a href="libxml-SAX.html#attribute">attribute</a> with the specified qualified name.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the qualified name of the attribute.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the value of the specified attribute, or NULL in case of error. The string must be deallocated by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderGetAttributeNo" id="xmlTextReaderGetAttributeNo"></a>Function: xmlTextReaderGetAttributeNo</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderGetAttributeNo	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 int no)<br />
</pre><p>Provides the value of the <a href="libxml-SAX.html#attribute">attribute</a> with the specified index relative to the containing element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>no</tt></i>:</span></td><td>the zero-based index of the <a href="libxml-SAX.html#attribute">attribute</a> relative to the containing element</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the value of the specified attribute, or NULL in case of error. The string must be deallocated by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderGetAttributeNs" id="xmlTextReaderGetAttributeNs"></a>Function: xmlTextReaderGetAttributeNs</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderGetAttributeNs	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localName, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)<br />
</pre><p>Provides the value of the specified <a href="libxml-SAX.html#attribute">attribute</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>localName</tt></i>:</span></td><td>the local name of the attribute.</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>the namespace URI of the attribute.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the value of the specified attribute, or NULL in case of error. The string must be deallocated by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderGetErrorHandler" id="xmlTextReaderGetErrorHandler"></a>Function: xmlTextReaderGetErrorHandler</h3><pre class="programlisting">void	xmlTextReaderGetErrorHandler	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlreader.html#xmlTextReaderErrorFunc">xmlTextReaderErrorFunc</a> * f, <br />					 void ** arg)<br />
</pre><p>Retrieve the error callback function and user argument.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the callback function or NULL is no callback has been registered</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>a user argument</td></tr></tbody></table></div><h3><a name="xmlTextReaderGetParserColumnNumber" id="xmlTextReaderGetParserColumnNumber"></a>Function: xmlTextReaderGetParserColumnNumber</h3><pre class="programlisting">int	xmlTextReaderGetParserColumnNumber	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Provide the column number of the current parsing point.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the user data (XML reader context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int or 0 if not available</td></tr></tbody></table></div><h3><a name="xmlTextReaderGetParserLineNumber" id="xmlTextReaderGetParserLineNumber"></a>Function: xmlTextReaderGetParserLineNumber</h3><pre class="programlisting">int	xmlTextReaderGetParserLineNumber	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Provide the line number of the current parsing point.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the user data (XML reader context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int or 0 if not available</td></tr></tbody></table></div><h3><a name="xmlTextReaderGetParserProp" id="xmlTextReaderGetParserProp"></a>Function: xmlTextReaderGetParserProp</h3><pre class="programlisting">int	xmlTextReaderGetParserProp	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 int prop)<br />
</pre><p>Read the parser internal property.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>prop</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlParserProperties">xmlParserProperties</a> to get</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the value, usually 0 or 1, or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderGetRemainder" id="xmlTextReaderGetRemainder"></a>Function: xmlTextReaderGetRemainder</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlTextReaderGetRemainder	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Method to get the remainder of the buffered XML. this method stops the parser, set its state to End Of File and return the input stream with what is left that the parser did not use. The implementation is not good, the parser certainly procgressed past what's left in reader-&gt;input, and there is an allocation problem. Best would be to rewrite it differently.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> attached to the XML or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderHasAttributes" id="xmlTextReaderHasAttributes"></a>Function: xmlTextReaderHasAttributes</h3><pre class="programlisting">int	xmlTextReaderHasAttributes	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Whether the node has attributes.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if false, and -1 in case or error</td></tr></tbody></table></div><h3><a name="xmlTextReaderHasValue" id="xmlTextReaderHasValue"></a>Function: xmlTextReaderHasValue</h3><pre class="programlisting">int	xmlTextReaderHasValue		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Whether the node can have a text value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if false, and -1 in case or error</td></tr></tbody></table></div><h3><a name="xmlTextReaderIsDefault" id="xmlTextReaderIsDefault"></a>Function: xmlTextReaderIsDefault</h3><pre class="programlisting">int	xmlTextReaderIsDefault		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Whether an Attribute node was generated from the default value defined in the DTD or schema.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if not defaulted, 1 if defaulted, and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderIsEmptyElement" id="xmlTextReaderIsEmptyElement"></a>Function: xmlTextReaderIsEmptyElement</h3><pre class="programlisting">int	xmlTextReaderIsEmptyElement	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Check if the current node is empty</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if empty, 0 if not and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderIsNamespaceDecl" id="xmlTextReaderIsNamespaceDecl"></a>Function: xmlTextReaderIsNamespaceDecl</h3><pre class="programlisting">int	xmlTextReaderIsNamespaceDecl	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Determine whether the current node is a namespace declaration rather than a regular attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the current node is a namespace declaration, 0 if it is a regular <a href="libxml-SAX.html#attribute">attribute</a> or other type of node, or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderIsValid" id="xmlTextReaderIsValid"></a>Function: xmlTextReaderIsValid</h3><pre class="programlisting">int	xmlTextReaderIsValid		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Retrieve the validity status from the parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the flag value 1 if valid, 0 if no, and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderLocalName" id="xmlTextReaderLocalName"></a>Function: xmlTextReaderLocalName</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderLocalName	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The local name of the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the local name or NULL if not available, if non NULL it need to be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderLocatorBaseURI" id="xmlTextReaderLocatorBaseURI"></a>Function: xmlTextReaderLocatorBaseURI</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderLocatorBaseURI	(<a href="libxml-xmlreader.html#xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a> locator)<br />
</pre><p>Obtain the base URI for the given locator.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>locator</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the base URI or NULL in case of error, if non NULL it need to be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderLocatorLineNumber" id="xmlTextReaderLocatorLineNumber"></a>Function: xmlTextReaderLocatorLineNumber</h3><pre class="programlisting">int	xmlTextReaderLocatorLineNumber	(<a href="libxml-xmlreader.html#xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a> locator)<br />
</pre><p>Obtain the line number for the given locator.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>locator</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderLocatorPtr">xmlTextReaderLocatorPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the line number or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderLookupNamespace" id="xmlTextReaderLookupNamespace"></a>Function: xmlTextReaderLookupNamespace</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderLookupNamespace	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)<br />
</pre><p>Resolves a namespace prefix in the scope of the current element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the prefix whose namespace URI is to be resolved. To return the default namespace, specify NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the namespace URI to which the prefix maps or NULL in case of error. The string must be deallocated by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderMoveToAttribute" id="xmlTextReaderMoveToAttribute"></a>Function: xmlTextReaderMoveToAttribute</h3><pre class="programlisting">int	xmlTextReaderMoveToAttribute	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Moves the position of the current instance to the <a href="libxml-SAX.html#attribute">attribute</a> with the specified qualified name.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the qualified name of the attribute.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 in case of success, -1 in case of error, 0 if not found</td></tr></tbody></table></div><h3><a name="xmlTextReaderMoveToAttributeNo" id="xmlTextReaderMoveToAttributeNo"></a>Function: xmlTextReaderMoveToAttributeNo</h3><pre class="programlisting">int	xmlTextReaderMoveToAttributeNo	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 int no)<br />
</pre><p>Moves the position of the current instance to the <a href="libxml-SAX.html#attribute">attribute</a> with the specified index relative to the containing element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>no</tt></i>:</span></td><td>the zero-based index of the <a href="libxml-SAX.html#attribute">attribute</a> relative to the containing element.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 in case of success, -1 in case of error, 0 if not found</td></tr></tbody></table></div><h3><a name="xmlTextReaderMoveToAttributeNs" id="xmlTextReaderMoveToAttributeNs"></a>Function: xmlTextReaderMoveToAttributeNs</h3><pre class="programlisting">int	xmlTextReaderMoveToAttributeNs	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localName, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)<br />
</pre><p>Moves the position of the current instance to the <a href="libxml-SAX.html#attribute">attribute</a> with the specified local name and namespace URI.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>localName</tt></i>:</span></td><td>the local name of the attribute.</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>the namespace URI of the attribute.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 in case of success, -1 in case of error, 0 if not found</td></tr></tbody></table></div><h3><a name="xmlTextReaderMoveToElement" id="xmlTextReaderMoveToElement"></a>Function: xmlTextReaderMoveToElement</h3><pre class="programlisting">int	xmlTextReaderMoveToElement	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Moves the position of the current instance to the node that contains the current Attribute node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 in case of success, -1 in case of error, 0 if not moved</td></tr></tbody></table></div><h3><a name="xmlTextReaderMoveToFirstAttribute" id="xmlTextReaderMoveToFirstAttribute"></a>Function: xmlTextReaderMoveToFirstAttribute</h3><pre class="programlisting">int	xmlTextReaderMoveToFirstAttribute	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Moves the position of the current instance to the first <a href="libxml-SAX.html#attribute">attribute</a> associated with the current node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 in case of success, -1 in case of error, 0 if not found</td></tr></tbody></table></div><h3><a name="xmlTextReaderMoveToNextAttribute" id="xmlTextReaderMoveToNextAttribute"></a>Function: xmlTextReaderMoveToNextAttribute</h3><pre class="programlisting">int	xmlTextReaderMoveToNextAttribute	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Moves the position of the current instance to the next <a href="libxml-SAX.html#attribute">attribute</a> associated with the current node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 in case of success, -1 in case of error, 0 if not found</td></tr></tbody></table></div><h3><a name="xmlTextReaderName" id="xmlTextReaderName"></a>Function: xmlTextReaderName</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderName	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The qualified name of the node, equal to Prefix :LocalName.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the local name or NULL if not available, if non NULL it need to be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderNamespaceUri" id="xmlTextReaderNamespaceUri"></a>Function: xmlTextReaderNamespaceUri</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderNamespaceUri	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The URI defining the namespace associated with the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the namespace URI or NULL if not available, if non NULL it need to be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderNext" id="xmlTextReaderNext"></a>Function: xmlTextReaderNext</h3><pre class="programlisting">int	xmlTextReaderNext		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Skip to the node following the current one in document order while avoiding the subtree if any.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the node was read successfully, 0 if there is no more nodes to read, or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderNextSibling" id="xmlTextReaderNextSibling"></a>Function: xmlTextReaderNextSibling</h3><pre class="programlisting">int	xmlTextReaderNextSibling	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Skip to the node following the current one in document order while avoiding the subtree if any. Currently implemented only for Readers built on a document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the node was read successfully, 0 if there is no more nodes to read, or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderNodeType" id="xmlTextReaderNodeType"></a>Function: xmlTextReaderNodeType</h3><pre class="programlisting">int	xmlTextReaderNodeType		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Get the node type of the current node Reference: http://www.gnu.org/software/dotgnu/pnetlib-doc/System/Xml/XmlNodeType.html</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the xmlNodeType of the current node or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderNormalization" id="xmlTextReaderNormalization"></a>Function: xmlTextReaderNormalization</h3><pre class="programlisting">int	xmlTextReaderNormalization	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The value indicating whether to normalize white space and <a href="libxml-SAX.html#attribute">attribute</a> values. Since <a href="libxml-SAX.html#attribute">attribute</a> value and end of line normalizations are a MUST in the XML specification only the value true is accepted. The broken bahaviour of accepting out of range character entities like &amp;#0; is of course not supported either.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderPrefix" id="xmlTextReaderPrefix"></a>Function: xmlTextReaderPrefix</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderPrefix	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>A shorthand <a href="libxml-SAX.html#reference">reference</a> to the namespace associated with the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the prefix or NULL if not available, if non NULL it need to be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderPreserve" id="xmlTextReaderPreserve"></a>Function: xmlTextReaderPreserve</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlTextReaderPreserve	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>This tells the XML Reader to preserve the current node. The caller must also use xmlTextReaderCurrentDoc() to keep an handle on the resulting document once parsing has finished</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderPreservePattern" id="xmlTextReaderPreservePattern"></a>Function: xmlTextReaderPreservePattern</h3><pre class="programlisting">int	xmlTextReaderPreservePattern	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pattern, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** namespaces)<br />
</pre><p>This tells the XML Reader to preserve all nodes matched by the pattern. The caller must also use xmlTextReaderCurrentDoc() to keep an handle on the resulting document once parsing has finished</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>pattern</tt></i>:</span></td><td>an XPath subset pattern</td></tr><tr><td><span class="term"><i><tt>namespaces</tt></i>:</span></td><td>the prefix definitions, array of [URI, prefix] or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a positive number in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderQuoteChar" id="xmlTextReaderQuoteChar"></a>Function: xmlTextReaderQuoteChar</h3><pre class="programlisting">int	xmlTextReaderQuoteChar		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The quotation mark character used to enclose the value of an attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>" or ' and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderRead" id="xmlTextReaderRead"></a>Function: xmlTextReaderRead</h3><pre class="programlisting">int	xmlTextReaderRead		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Moves the position of the current instance to the next node in the stream, exposing its properties.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the node was read successfully, 0 if there is no more nodes to read, or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderReadAttributeValue" id="xmlTextReaderReadAttributeValue"></a>Function: xmlTextReaderReadAttributeValue</h3><pre class="programlisting">int	xmlTextReaderReadAttributeValue	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Parses an <a href="libxml-SAX.html#attribute">attribute</a> value into one or more Text and EntityReference nodes.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 in case of success, 0 if the reader was not positionned on an ttribute node or all the <a href="libxml-SAX.html#attribute">attribute</a> values have been read, or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderReadInnerXml" id="xmlTextReaderReadInnerXml"></a>Function: xmlTextReaderReadInnerXml</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderReadInnerXml	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Reads the contents of the current node, including child nodes and markup.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the XML content, or NULL if the current node is neither an element nor attribute, or has no child nodes. The string must be deallocated by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderReadOuterXml" id="xmlTextReaderReadOuterXml"></a>Function: xmlTextReaderReadOuterXml</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderReadOuterXml	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Reads the contents of the current node, including child nodes and markup.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the node and any XML content, or NULL if the current node cannot be serialized. The string must be deallocated by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderReadState" id="xmlTextReaderReadState"></a>Function: xmlTextReaderReadState</h3><pre class="programlisting">int	xmlTextReaderReadState		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Gets the read state of the reader.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the state value, or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderReadString" id="xmlTextReaderReadString"></a>Function: xmlTextReaderReadString</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderReadString	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Reads the contents of an element or a text node as a string.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string containing the contents of the Element or Text node, or NULL if the reader is positioned on any other type of node. The string must be deallocated by the caller.</td></tr></tbody></table></div><h3><a name="xmlTextReaderRelaxNGSetSchema" id="xmlTextReaderRelaxNGSetSchema"></a>Function: xmlTextReaderRelaxNGSetSchema</h3><pre class="programlisting">int	xmlTextReaderRelaxNGSetSchema	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-relaxng.html#xmlRelaxNGPtr">xmlRelaxNGPtr</a> schema)<br />
</pre><p>Use RelaxNG to validate the document as it is processed. Activation is only possible before the first Read(). if @schema is NULL, then RelaxNG validation is desactivated. @ The @schema should not be freed until the reader is deallocated or its use has been deactivated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>schema</tt></i>:</span></td><td>a precompiled RelaxNG schema</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case the RelaxNG validation could be (des)activated and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderRelaxNGValidate" id="xmlTextReaderRelaxNGValidate"></a>Function: xmlTextReaderRelaxNGValidate</h3><pre class="programlisting">int	xmlTextReaderRelaxNGValidate	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const char * rng)<br />
</pre><p>Use RelaxNG schema to validate the document as it is processed. Activation is only possible before the first Read(). If @rng is NULL, then RelaxNG schema validation is deactivated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>rng</tt></i>:</span></td><td>the path to a RelaxNG schema or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case the schemas validation could be (de)activated and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderRelaxNGValidateCtxt" id="xmlTextReaderRelaxNGValidateCtxt"></a>Function: xmlTextReaderRelaxNGValidateCtxt</h3><pre class="programlisting">int	xmlTextReaderRelaxNGValidateCtxt	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 <a href="libxml-relaxng.html#xmlRelaxNGValidCtxtPtr">xmlRelaxNGValidCtxtPtr</a> ctxt, <br />						 int options)<br />
</pre><p>Use RelaxNG schema context to validate the document as it is processed. Activation is only possible before the first Read(). If @ctxt is NULL, then RelaxNG schema validation is deactivated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the RelaxNG schema validation context or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>options (not used yet)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case the schemas validation could be (de)activated and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderSchemaValidate" id="xmlTextReaderSchemaValidate"></a>Function: xmlTextReaderSchemaValidate</h3><pre class="programlisting">int	xmlTextReaderSchemaValidate	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 const char * xsd)<br />
</pre><p>Use W3C XSD schema to validate the document as it is processed. Activation is only possible before the first Read(). If @xsd is NULL, then XML Schema validation is deactivated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>xsd</tt></i>:</span></td><td>the path to a W3C XSD schema or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case the schemas validation could be (de)activated and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderSchemaValidateCtxt" id="xmlTextReaderSchemaValidateCtxt"></a>Function: xmlTextReaderSchemaValidateCtxt</h3><pre class="programlisting">int	xmlTextReaderSchemaValidateCtxt	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">xmlSchemaValidCtxtPtr</a> ctxt, <br />					 int options)<br />
</pre><p>Use W3C XSD schema context to validate the document as it is processed. Activation is only possible before the first Read(). If @ctxt is NULL, then XML Schema validation is deactivated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XML Schema validation context or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>options (not used yet)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case the schemas validation could be (de)activated and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderSetErrorHandler" id="xmlTextReaderSetErrorHandler"></a>Function: xmlTextReaderSetErrorHandler</h3><pre class="programlisting">void	xmlTextReaderSetErrorHandler	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlreader.html#xmlTextReaderErrorFunc">xmlTextReaderErrorFunc</a> f, <br />					 void * arg)<br />
</pre><p>Register a callback function that will be called on error and warnings. If @f is NULL, the default error and warning handlers are restored.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the callback function to call on error and warnings</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>a user argument to pass to the callback function</td></tr></tbody></table></div><h3><a name="xmlTextReaderSetParserProp" id="xmlTextReaderSetParserProp"></a>Function: xmlTextReaderSetParserProp</h3><pre class="programlisting">int	xmlTextReaderSetParserProp	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 int prop, <br />					 int value)<br />
</pre><p>Change the parser processing behaviour by changing some of its internal properties. Note that some properties can only be changed before any read has been done.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>prop</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlParserProperties">xmlParserProperties</a> to set</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>usually 0 or 1 to (de)activate it</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the call was successful, or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlTextReaderSetSchema" id="xmlTextReaderSetSchema"></a>Function: xmlTextReaderSetSchema</h3><pre class="programlisting">int	xmlTextReaderSetSchema		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-xmlschemas.html#xmlSchemaPtr">xmlSchemaPtr</a> schema)<br />
</pre><p>Use XSD Schema to validate the document as it is processed. Activation is only possible before the first Read(). if @schema is NULL, then Schema validation is desactivated. @ The @schema should not be freed until the reader is deallocated or its use has been deactivated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>schema</tt></i>:</span></td><td>a precompiled Schema schema</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case the Schema validation could be (des)activated and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderSetStructuredErrorHandler" id="xmlTextReaderSetStructuredErrorHandler"></a>Function: xmlTextReaderSetStructuredErrorHandler</h3><pre class="programlisting">void	xmlTextReaderSetStructuredErrorHandler	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />						 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> f, <br />						 void * arg)<br />
</pre><p>Register a callback function that will be called on error and warnings. If @f is NULL, the default error and warning handlers are restored.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the callback function to call on error and warnings</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>a user argument to pass to the callback function</td></tr></tbody></table></div><h3><a name="xmlTextReaderSetup" id="xmlTextReaderSetup"></a>Function: xmlTextReaderSetup</h3><pre class="programlisting">int	xmlTextReaderSetup		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader, <br />					 <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Setup an XML reader with new options</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>an XML reader</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td><a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> used to feed the reader, will be destroyed with it.</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderStandalone" id="xmlTextReaderStandalone"></a>Function: xmlTextReaderStandalone</h3><pre class="programlisting">int	xmlTextReaderStandalone		(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Determine the standalone status of the document being read.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the document was declared to be standalone, 0 if it was declared to be not standalone, or -1 if the document did not specify its standalone status or in case of error.</td></tr></tbody></table></div><h3><a name="xmlTextReaderValue" id="xmlTextReaderValue"></a>Function: xmlTextReaderValue</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderValue	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>Provides the text value of the node if present</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the string or NULL if not available. The result must be deallocated with xmlFree()</td></tr></tbody></table></div><h3><a name="xmlTextReaderXmlLang" id="xmlTextReaderXmlLang"></a>Function: xmlTextReaderXmlLang</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlTextReaderXmlLang	(<a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> reader)<br />
</pre><p>The xml:lang scope within which the node resides.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>reader</tt></i>:</span></td><td>the <a href="libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> used</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the xml:lang value or NULL if none exists., if non NULL it need to be freed by the caller.</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
