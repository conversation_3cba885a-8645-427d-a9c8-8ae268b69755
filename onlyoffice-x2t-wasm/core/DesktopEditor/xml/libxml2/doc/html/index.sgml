<ANCHOR id="libxml-testOOMlib" href="libxml/libxml-testOOMlib.html">
<ANCHOR id="test-malloc" href="libxml/libxml-testOOMlib.html#test-malloc">
<ANCHOR id="test-realloc" href="libxml/libxml-testOOMlib.html#test-realloc">
<ANCHOR id="test-free" href="libxml/libxml-testOOMlib.html#test-free">
<ANCHOR id="test-strdup" href="libxml/libxml-testOOMlib.html#test-strdup">
<ANCHOR id="TestMemoryFunction" href="libxml/libxml-testOOMlib.html#TestMemoryFunction">
<ANCHOR id="test-oom-handling" href="libxml/libxml-testOOMlib.html#test-oom-handling">
<ANCHOR id="test-get-malloc-blocks-outstanding" href="libxml/libxml-testOOMlib.html#test-get-malloc-blocks-outstanding">
<ANCHOR id="libxml-wincecompat" href="libxml/libxml-wincecompat.html">
<ANCHOR id="MAX-STRERROR-CAPS" href="libxml/libxml-wincecompat.html#MAX-STRERROR-CAPS">
<ANCHOR id="O-RDONLY-CAPS" href="libxml/libxml-wincecompat.html#O-RDONLY-CAPS">
<ANCHOR id="O-WRONLY-CAPS" href="libxml/libxml-wincecompat.html#O-WRONLY-CAPS">
<ANCHOR id="O-RDWR-CAPS" href="libxml/libxml-wincecompat.html#O-RDWR-CAPS">
<ANCHOR id="O-APPEND-CAPS" href="libxml/libxml-wincecompat.html#O-APPEND-CAPS">
<ANCHOR id="O-CREAT-CAPS" href="libxml/libxml-wincecompat.html#O-CREAT-CAPS">
<ANCHOR id="O-TRUNC-CAPS" href="libxml/libxml-wincecompat.html#O-TRUNC-CAPS">
<ANCHOR id="O-EXCL-CAPS" href="libxml/libxml-wincecompat.html#O-EXCL-CAPS">
<ANCHOR id="errno" href="libxml/libxml-wincecompat.html#errno">
<ANCHOR id="read" href="libxml/libxml-wincecompat.html#read">
<ANCHOR id="write" href="libxml/libxml-wincecompat.html#write">
<ANCHOR id="open" href="libxml/libxml-wincecompat.html#open">
<ANCHOR id="close" href="libxml/libxml-wincecompat.html#close">
<ANCHOR id="getenv" href="libxml/libxml-wincecompat.html#getenv">
<ANCHOR id="strerror" href="libxml/libxml-wincecompat.html#strerror">
<ANCHOR id="snprintf" href="libxml/libxml-wincecompat.html#snprintf">
<ANCHOR id="vsnprintf" href="libxml/libxml-wincecompat.html#vsnprintf">
<ANCHOR id="perror" href="libxml/libxml-wincecompat.html#perror">
<ANCHOR id="libxml-wsockcompat" href="libxml/libxml-wsockcompat.html">
<ANCHOR id="SOCKLEN-T-CAPS" href="libxml/libxml-wsockcompat.html#SOCKLEN-T-CAPS">
<ANCHOR id="EWOULDBLOCK-CAPS" href="libxml/libxml-wsockcompat.html#EWOULDBLOCK-CAPS">
<ANCHOR id="EINPROGRESS-CAPS" href="libxml/libxml-wsockcompat.html#EINPROGRESS-CAPS">
<ANCHOR id="EALREADY-CAPS" href="libxml/libxml-wsockcompat.html#EALREADY-CAPS">
<ANCHOR id="ENOTSOCK-CAPS" href="libxml/libxml-wsockcompat.html#ENOTSOCK-CAPS">
<ANCHOR id="EDESTADDRREQ-CAPS" href="libxml/libxml-wsockcompat.html#EDESTADDRREQ-CAPS">
<ANCHOR id="EMSGSIZE-CAPS" href="libxml/libxml-wsockcompat.html#EMSGSIZE-CAPS">
<ANCHOR id="EPROTOTYPE-CAPS" href="libxml/libxml-wsockcompat.html#EPROTOTYPE-CAPS">
<ANCHOR id="ENOPROTOOPT-CAPS" href="libxml/libxml-wsockcompat.html#ENOPROTOOPT-CAPS">
<ANCHOR id="EPROTONOSUPPORT-CAPS" href="libxml/libxml-wsockcompat.html#EPROTONOSUPPORT-CAPS">
<ANCHOR id="ESOCKTNOSUPPORT-CAPS" href="libxml/libxml-wsockcompat.html#ESOCKTNOSUPPORT-CAPS">
<ANCHOR id="EOPNOTSUPP-CAPS" href="libxml/libxml-wsockcompat.html#EOPNOTSUPP-CAPS">
<ANCHOR id="EPFNOSUPPORT-CAPS" href="libxml/libxml-wsockcompat.html#EPFNOSUPPORT-CAPS">
<ANCHOR id="EAFNOSUPPORT-CAPS" href="libxml/libxml-wsockcompat.html#EAFNOSUPPORT-CAPS">
<ANCHOR id="EADDRINUSE-CAPS" href="libxml/libxml-wsockcompat.html#EADDRINUSE-CAPS">
<ANCHOR id="EADDRNOTAVAIL-CAPS" href="libxml/libxml-wsockcompat.html#EADDRNOTAVAIL-CAPS">
<ANCHOR id="ENETDOWN-CAPS" href="libxml/libxml-wsockcompat.html#ENETDOWN-CAPS">
<ANCHOR id="ENETUNREACH-CAPS" href="libxml/libxml-wsockcompat.html#ENETUNREACH-CAPS">
<ANCHOR id="ENETRESET-CAPS" href="libxml/libxml-wsockcompat.html#ENETRESET-CAPS">
<ANCHOR id="ECONNABORTED-CAPS" href="libxml/libxml-wsockcompat.html#ECONNABORTED-CAPS">
<ANCHOR id="ECONNRESET-CAPS" href="libxml/libxml-wsockcompat.html#ECONNRESET-CAPS">
<ANCHOR id="ENOBUFS-CAPS" href="libxml/libxml-wsockcompat.html#ENOBUFS-CAPS">
<ANCHOR id="EISCONN-CAPS" href="libxml/libxml-wsockcompat.html#EISCONN-CAPS">
<ANCHOR id="ENOTCONN-CAPS" href="libxml/libxml-wsockcompat.html#ENOTCONN-CAPS">
<ANCHOR id="ESHUTDOWN-CAPS" href="libxml/libxml-wsockcompat.html#ESHUTDOWN-CAPS">
<ANCHOR id="ETOOMANYREFS-CAPS" href="libxml/libxml-wsockcompat.html#ETOOMANYREFS-CAPS">
<ANCHOR id="ETIMEDOUT-CAPS" href="libxml/libxml-wsockcompat.html#ETIMEDOUT-CAPS">
<ANCHOR id="ECONNREFUSED-CAPS" href="libxml/libxml-wsockcompat.html#ECONNREFUSED-CAPS">
<ANCHOR id="ELOOP-CAPS" href="libxml/libxml-wsockcompat.html#ELOOP-CAPS">
<ANCHOR id="EHOSTDOWN-CAPS" href="libxml/libxml-wsockcompat.html#EHOSTDOWN-CAPS">
<ANCHOR id="EHOSTUNREACH-CAPS" href="libxml/libxml-wsockcompat.html#EHOSTUNREACH-CAPS">
<ANCHOR id="EPROCLIM-CAPS" href="libxml/libxml-wsockcompat.html#EPROCLIM-CAPS">
<ANCHOR id="EUSERS-CAPS" href="libxml/libxml-wsockcompat.html#EUSERS-CAPS">
<ANCHOR id="EDQUOT-CAPS" href="libxml/libxml-wsockcompat.html#EDQUOT-CAPS">
<ANCHOR id="ESTALE-CAPS" href="libxml/libxml-wsockcompat.html#ESTALE-CAPS">
<ANCHOR id="EREMOTE-CAPS" href="libxml/libxml-wsockcompat.html#EREMOTE-CAPS">
<ANCHOR id="libxml-encoding" href="libxml/libxml-encoding.html">
<ANCHOR id="xmlCharEncoding" href="libxml/libxml-encoding.html#xmlCharEncoding">
<ANCHOR id="xmlCharEncodingInputFunc" href="libxml/libxml-encoding.html#xmlCharEncodingInputFunc">
<ANCHOR id="xmlCharEncodingOutputFunc" href="libxml/libxml-encoding.html#xmlCharEncodingOutputFunc">
<ANCHOR id="xmlCharEncodingHandler" href="libxml/libxml-encoding.html#xmlCharEncodingHandler">
<ANCHOR id="xmlCharEncodingHandlerPtr" href="libxml/libxml-encoding.html#xmlCharEncodingHandlerPtr">
<ANCHOR id="libxml-xmlregexp" href="libxml/libxml-xmlregexp.html">
<ANCHOR id="xmlRegexp" href="libxml/libxml-xmlregexp.html#xmlRegexp">
<ANCHOR id="xmlRegexpPtr" href="libxml/libxml-xmlregexp.html#xmlRegexpPtr">
<ANCHOR id="xmlRegExecCtxt" href="libxml/libxml-xmlregexp.html#xmlRegExecCtxt">
<ANCHOR id="xmlRegExecCtxtPtr" href="libxml/libxml-xmlregexp.html#xmlRegExecCtxtPtr">
<ANCHOR id="xmlRegExecCallbacks" href="libxml/libxml-xmlregexp.html#xmlRegExecCallbacks">
<ANCHOR id="libxml-xmlmemory" href="libxml/libxml-xmlmemory.html">
<ANCHOR id="DEBUG-MEMORY-CAPS" href="libxml/libxml-xmlmemory.html#DEBUG-MEMORY-CAPS">
<ANCHOR id="xmlFreeFunc" href="libxml/libxml-xmlmemory.html#xmlFreeFunc">
<ANCHOR id="xmlMallocFunc" href="libxml/libxml-xmlmemory.html#xmlMallocFunc">
<ANCHOR id="xmlReallocFunc" href="libxml/libxml-xmlmemory.html#xmlReallocFunc">
<ANCHOR id="xmlStrdupFunc" href="libxml/libxml-xmlmemory.html#xmlStrdupFunc">
<ANCHOR id="xmlMalloc" href="libxml/libxml-xmlmemory.html#xmlMalloc">
<ANCHOR id="xmlMallocAtomic" href="libxml/libxml-xmlmemory.html#xmlMallocAtomic">
<ANCHOR id="xmlRealloc" href="libxml/libxml-xmlmemory.html#xmlRealloc">
<ANCHOR id="xmlMemStrdup" href="libxml/libxml-xmlmemory.html#xmlMemStrdup">
<ANCHOR id="libxml-xmlIO" href="libxml/libxml-xmlIO.html">
<ANCHOR id="xmlInputMatchCallback" href="libxml/libxml-xmlIO.html#xmlInputMatchCallback">
<ANCHOR id="xmlInputOpenCallback" href="libxml/libxml-xmlIO.html#xmlInputOpenCallback">
<ANCHOR id="xmlInputReadCallback" href="libxml/libxml-xmlIO.html#xmlInputReadCallback">
<ANCHOR id="xmlInputCloseCallback" href="libxml/libxml-xmlIO.html#xmlInputCloseCallback">
<ANCHOR id="xmlOutputMatchCallback" href="libxml/libxml-xmlIO.html#xmlOutputMatchCallback">
<ANCHOR id="xmlOutputOpenCallback" href="libxml/libxml-xmlIO.html#xmlOutputOpenCallback">
<ANCHOR id="xmlOutputWriteCallback" href="libxml/libxml-xmlIO.html#xmlOutputWriteCallback">
<ANCHOR id="xmlOutputCloseCallback" href="libxml/libxml-xmlIO.html#xmlOutputCloseCallback">
<ANCHOR id="xmlParserInputBuffer" href="libxml/libxml-xmlIO.html#xmlParserInputBuffer">
<ANCHOR id="xmlOutputBuffer" href="libxml/libxml-xmlIO.html#xmlOutputBuffer">
<ANCHOR id="libxml-xpath" href="libxml/libxml-xpath.html">
<ANCHOR id="xmlXPathContext" href="libxml/libxml-xpath.html#xmlXPathContext">
<ANCHOR id="xmlXPathContextPtr" href="libxml/libxml-xpath.html#xmlXPathContextPtr">
<ANCHOR id="xmlXPathParserContext" href="libxml/libxml-xpath.html#xmlXPathParserContext">
<ANCHOR id="xmlXPathParserContextPtr" href="libxml/libxml-xpath.html#xmlXPathParserContextPtr">
<ANCHOR id="xmlXPathError" href="libxml/libxml-xpath.html#xmlXPathError">
<ANCHOR id="xmlNodeSet" href="libxml/libxml-xpath.html#xmlNodeSet">
<ANCHOR id="xmlNodeSetPtr" href="libxml/libxml-xpath.html#xmlNodeSetPtr">
<ANCHOR id="xmlXPathObjectType" href="libxml/libxml-xpath.html#xmlXPathObjectType">
<ANCHOR id="xmlXPathObject" href="libxml/libxml-xpath.html#xmlXPathObject">
<ANCHOR id="xmlXPathObjectPtr" href="libxml/libxml-xpath.html#xmlXPathObjectPtr">
<ANCHOR id="xmlXPathConvertFunc" href="libxml/libxml-xpath.html#xmlXPathConvertFunc">
<ANCHOR id="xmlXPathType" href="libxml/libxml-xpath.html#xmlXPathType">
<ANCHOR id="xmlXPathTypePtr" href="libxml/libxml-xpath.html#xmlXPathTypePtr">
<ANCHOR id="xmlXPathVariable" href="libxml/libxml-xpath.html#xmlXPathVariable">
<ANCHOR id="xmlXPathVariablePtr" href="libxml/libxml-xpath.html#xmlXPathVariablePtr">
<ANCHOR id="xmlXPathEvalFunc" href="libxml/libxml-xpath.html#xmlXPathEvalFunc">
<ANCHOR id="xmlXPathFunct" href="libxml/libxml-xpath.html#xmlXPathFunct">
<ANCHOR id="xmlXPathFuncPtr" href="libxml/libxml-xpath.html#xmlXPathFuncPtr">
<ANCHOR id="xmlXPathAxisFunc" href="libxml/libxml-xpath.html#xmlXPathAxisFunc">
<ANCHOR id="xmlXPathAxis" href="libxml/libxml-xpath.html#xmlXPathAxis">
<ANCHOR id="xmlXPathAxisPtr" href="libxml/libxml-xpath.html#xmlXPathAxisPtr">
<ANCHOR id="xmlXPathCompExpr" href="libxml/libxml-xpath.html#xmlXPathCompExpr">
<ANCHOR id="xmlXPathCompExprPtr" href="libxml/libxml-xpath.html#xmlXPathCompExprPtr">
<ANCHOR id="xmlXPathFunction" href="libxml/libxml-xpath.html#xmlXPathFunction">
<ANCHOR id="xmlXPathNAN" href="libxml/libxml-xpath.html#xmlXPathNAN">
<ANCHOR id="xmlXPathPINF" href="libxml/libxml-xpath.html#xmlXPathPINF">
<ANCHOR id="xmlXPathNINF" href="libxml/libxml-xpath.html#xmlXPathNINF">
<ANCHOR id="xmlXPathNodeSetGetLength" href="libxml/libxml-xpath.html#xmlXPathNodeSetGetLength">
<ANCHOR id="xmlXPathNodeSetItem" href="libxml/libxml-xpath.html#xmlXPathNodeSetItem">
<ANCHOR id="xmlXPathNodeSetIsEmpty" href="libxml/libxml-xpath.html#xmlXPathNodeSetIsEmpty">
<ANCHOR id="libxml-chvalid" href="libxml/libxml-chvalid.html">
<ANCHOR id="xmlChSRange" href="libxml/libxml-chvalid.html#xmlChSRange">
<ANCHOR id="xmlChSRangePtr" href="libxml/libxml-chvalid.html#xmlChSRangePtr">
<ANCHOR id="xmlChLRange" href="libxml/libxml-chvalid.html#xmlChLRange">
<ANCHOR id="xmlChLRangePtr" href="libxml/libxml-chvalid.html#xmlChLRangePtr">
<ANCHOR id="xmlChRangeGroup" href="libxml/libxml-chvalid.html#xmlChRangeGroup">
<ANCHOR id="xmlChRangeGroupPtr" href="libxml/libxml-chvalid.html#xmlChRangeGroupPtr">
<ANCHOR id="xmlIsBaseChar-ch" href="libxml/libxml-chvalid.html#xmlIsBaseChar-ch">
<ANCHOR id="xmlIsBaseCharQ" href="libxml/libxml-chvalid.html#xmlIsBaseCharQ">
<ANCHOR id="xmlIsBaseCharGroup" href="libxml/libxml-chvalid.html#xmlIsBaseCharGroup">
<ANCHOR id="xmlIsBlank-ch" href="libxml/libxml-chvalid.html#xmlIsBlank-ch">
<ANCHOR id="xmlIsBlankQ" href="libxml/libxml-chvalid.html#xmlIsBlankQ">
<ANCHOR id="xmlIsChar-ch" href="libxml/libxml-chvalid.html#xmlIsChar-ch">
<ANCHOR id="xmlIsCharQ" href="libxml/libxml-chvalid.html#xmlIsCharQ">
<ANCHOR id="xmlIsCharGroup" href="libxml/libxml-chvalid.html#xmlIsCharGroup">
<ANCHOR id="xmlIsCombiningQ" href="libxml/libxml-chvalid.html#xmlIsCombiningQ">
<ANCHOR id="xmlIsCombiningGroup" href="libxml/libxml-chvalid.html#xmlIsCombiningGroup">
<ANCHOR id="xmlIsDigit-ch" href="libxml/libxml-chvalid.html#xmlIsDigit-ch">
<ANCHOR id="xmlIsDigitQ" href="libxml/libxml-chvalid.html#xmlIsDigitQ">
<ANCHOR id="xmlIsDigitGroup" href="libxml/libxml-chvalid.html#xmlIsDigitGroup">
<ANCHOR id="xmlIsExtender-ch" href="libxml/libxml-chvalid.html#xmlIsExtender-ch">
<ANCHOR id="xmlIsExtenderQ" href="libxml/libxml-chvalid.html#xmlIsExtenderQ">
<ANCHOR id="xmlIsExtenderGroup" href="libxml/libxml-chvalid.html#xmlIsExtenderGroup">
<ANCHOR id="xmlIsIdeographicQ" href="libxml/libxml-chvalid.html#xmlIsIdeographicQ">
<ANCHOR id="xmlIsIdeographicGroup" href="libxml/libxml-chvalid.html#xmlIsIdeographicGroup">
<ANCHOR id="xmlIsPubidChar-ch" href="libxml/libxml-chvalid.html#xmlIsPubidChar-ch">
<ANCHOR id="xmlIsPubidCharQ" href="libxml/libxml-chvalid.html#xmlIsPubidCharQ">
<ANCHOR id="libxml-uri" href="libxml/libxml-uri.html">
<ANCHOR id="xmlURI" href="libxml/libxml-uri.html#xmlURI">
<ANCHOR id="xmlURIPtr" href="libxml/libxml-uri.html#xmlURIPtr">
<ANCHOR id="libxml-nanoftp" href="libxml/libxml-nanoftp.html">
<ANCHOR id="ftpListCallback" href="libxml/libxml-nanoftp.html#ftpListCallback">
<ANCHOR id="ftpDataCallback" href="libxml/libxml-nanoftp.html#ftpDataCallback">
<ANCHOR id="libxml-schemasInternals" href="libxml/libxml-schemasInternals.html">
<ANCHOR id="xmlSchemaTypeType" href="libxml/libxml-schemasInternals.html#xmlSchemaTypeType">
<ANCHOR id="xmlSchemaContentType" href="libxml/libxml-schemasInternals.html#xmlSchemaContentType">
<ANCHOR id="xmlSchemaVal" href="libxml/libxml-schemasInternals.html#xmlSchemaVal">
<ANCHOR id="xmlSchemaValPtr" href="libxml/libxml-schemasInternals.html#xmlSchemaValPtr">
<ANCHOR id="xmlSchemaType" href="libxml/libxml-schemasInternals.html#xmlSchemaType">
<ANCHOR id="xmlSchemaTypePtr" href="libxml/libxml-schemasInternals.html#xmlSchemaTypePtr">
<ANCHOR id="xmlSchemaFacet" href="libxml/libxml-schemasInternals.html#xmlSchemaFacet">
<ANCHOR id="xmlSchemaFacetPtr" href="libxml/libxml-schemasInternals.html#xmlSchemaFacetPtr">
<ANCHOR id="xmlSchemaAnnot" href="libxml/libxml-schemasInternals.html#xmlSchemaAnnot">
<ANCHOR id="xmlSchemaAnnotPtr" href="libxml/libxml-schemasInternals.html#xmlSchemaAnnotPtr">
<ANCHOR id="XML-SCHEMAS-ANYATTR-SKIP-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ANYATTR-SKIP-CAPS">
<ANCHOR id="XML-SCHEMAS-ANYATTR-LAX-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ANYATTR-LAX-CAPS">
<ANCHOR id="XML-SCHEMAS-ANYATTR-STRICT-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ANYATTR-STRICT-CAPS">
<ANCHOR id="xmlSchemaAttribute" href="libxml/libxml-schemasInternals.html#xmlSchemaAttribute">
<ANCHOR id="xmlSchemaAttributePtr" href="libxml/libxml-schemasInternals.html#xmlSchemaAttributePtr">
<ANCHOR id="xmlSchemaAttributeGroup" href="libxml/libxml-schemasInternals.html#xmlSchemaAttributeGroup">
<ANCHOR id="xmlSchemaAttributeGroupPtr" href="libxml/libxml-schemasInternals.html#xmlSchemaAttributeGroupPtr">
<ANCHOR id="XML-SCHEMAS-TYPE-MIXED-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-TYPE-MIXED-CAPS">
<ANCHOR id="XML-SCHEMAS-ELEM-NILLABLE-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ELEM-NILLABLE-CAPS">
<ANCHOR id="XML-SCHEMAS-ELEM-GLOBAL-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ELEM-GLOBAL-CAPS">
<ANCHOR id="XML-SCHEMAS-ELEM-DEFAULT-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ELEM-DEFAULT-CAPS">
<ANCHOR id="XML-SCHEMAS-ELEM-FIXED-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ELEM-FIXED-CAPS">
<ANCHOR id="XML-SCHEMAS-ELEM-ABSTRACT-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ELEM-ABSTRACT-CAPS">
<ANCHOR id="XML-SCHEMAS-ELEM-TOPLEVEL-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ELEM-TOPLEVEL-CAPS">
<ANCHOR id="XML-SCHEMAS-ELEM-REF-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-ELEM-REF-CAPS">
<ANCHOR id="xmlSchemaElement" href="libxml/libxml-schemasInternals.html#xmlSchemaElement">
<ANCHOR id="xmlSchemaElementPtr" href="libxml/libxml-schemasInternals.html#xmlSchemaElementPtr">
<ANCHOR id="XML-SCHEMAS-FACET-UNKNOWN-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-FACET-UNKNOWN-CAPS">
<ANCHOR id="XML-SCHEMAS-FACET-PRESERVE-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-FACET-PRESERVE-CAPS">
<ANCHOR id="XML-SCHEMAS-FACET-REPLACE-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-FACET-REPLACE-CAPS">
<ANCHOR id="XML-SCHEMAS-FACET-COLLAPSE-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-FACET-COLLAPSE-CAPS">
<ANCHOR id="xmlSchemaNotation" href="libxml/libxml-schemasInternals.html#xmlSchemaNotation">
<ANCHOR id="xmlSchemaNotationPtr" href="libxml/libxml-schemasInternals.html#xmlSchemaNotationPtr">
<ANCHOR id="XML-SCHEMAS-QUALIF-ELEM-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-QUALIF-ELEM-CAPS">
<ANCHOR id="XML-SCHEMAS-QUALIF-ATTR-CAPS" href="libxml/libxml-schemasInternals.html#XML-SCHEMAS-QUALIF-ATTR-CAPS">
<ANCHOR id="xmlSchema" href="libxml/libxml-schemasInternals.html#xmlSchema">
<ANCHOR id="libxml-threads" href="libxml/libxml-threads.html">
<ANCHOR id="xmlMutex" href="libxml/libxml-threads.html#xmlMutex">
<ANCHOR id="xmlMutexPtr" href="libxml/libxml-threads.html#xmlMutexPtr">
<ANCHOR id="xmlRMutex" href="libxml/libxml-threads.html#xmlRMutex">
<ANCHOR id="xmlRMutexPtr" href="libxml/libxml-threads.html#xmlRMutexPtr">
<ANCHOR id="libxml-parser" href="libxml/libxml-parser.html">
<ANCHOR id="XML-DEFAULT-VERSION-CAPS" href="libxml/libxml-parser.html#XML-DEFAULT-VERSION-CAPS">
<ANCHOR id="xmlParserInputDeallocate" href="libxml/libxml-parser.html#xmlParserInputDeallocate">
<ANCHOR id="xmlParserInput" href="libxml/libxml-parser.html#xmlParserInput">
<ANCHOR id="xmlParserNodeInfo" href="libxml/libxml-parser.html#xmlParserNodeInfo">
<ANCHOR id="xmlParserNodeInfoPtr" href="libxml/libxml-parser.html#xmlParserNodeInfoPtr">
<ANCHOR id="xmlParserNodeInfoSeq" href="libxml/libxml-parser.html#xmlParserNodeInfoSeq">
<ANCHOR id="xmlParserNodeInfoSeqPtr" href="libxml/libxml-parser.html#xmlParserNodeInfoSeqPtr">
<ANCHOR id="xmlParserInputState" href="libxml/libxml-parser.html#xmlParserInputState">
<ANCHOR id="XML-DETECT-IDS-CAPS" href="libxml/libxml-parser.html#XML-DETECT-IDS-CAPS">
<ANCHOR id="XML-COMPLETE-ATTRS-CAPS" href="libxml/libxml-parser.html#XML-COMPLETE-ATTRS-CAPS">
<ANCHOR id="XML-SKIP-IDS-CAPS" href="libxml/libxml-parser.html#XML-SKIP-IDS-CAPS">
<ANCHOR id="xmlParserCtxt" href="libxml/libxml-parser.html#xmlParserCtxt">
<ANCHOR id="xmlSAXLocator" href="libxml/libxml-parser.html#xmlSAXLocator">
<ANCHOR id="resolveEntitySAXFunc" href="libxml/libxml-parser.html#resolveEntitySAXFunc">
<ANCHOR id="internalSubsetSAXFunc" href="libxml/libxml-parser.html#internalSubsetSAXFunc">
<ANCHOR id="externalSubsetSAXFunc" href="libxml/libxml-parser.html#externalSubsetSAXFunc">
<ANCHOR id="getEntitySAXFunc" href="libxml/libxml-parser.html#getEntitySAXFunc">
<ANCHOR id="getParameterEntitySAXFunc" href="libxml/libxml-parser.html#getParameterEntitySAXFunc">
<ANCHOR id="entityDeclSAXFunc" href="libxml/libxml-parser.html#entityDeclSAXFunc">
<ANCHOR id="notationDeclSAXFunc" href="libxml/libxml-parser.html#notationDeclSAXFunc">
<ANCHOR id="attributeDeclSAXFunc" href="libxml/libxml-parser.html#attributeDeclSAXFunc">
<ANCHOR id="elementDeclSAXFunc" href="libxml/libxml-parser.html#elementDeclSAXFunc">
<ANCHOR id="unparsedEntityDeclSAXFunc" href="libxml/libxml-parser.html#unparsedEntityDeclSAXFunc">
<ANCHOR id="setDocumentLocatorSAXFunc" href="libxml/libxml-parser.html#setDocumentLocatorSAXFunc">
<ANCHOR id="startDocumentSAXFunc" href="libxml/libxml-parser.html#startDocumentSAXFunc">
<ANCHOR id="endDocumentSAXFunc" href="libxml/libxml-parser.html#endDocumentSAXFunc">
<ANCHOR id="startElementSAXFunc" href="libxml/libxml-parser.html#startElementSAXFunc">
<ANCHOR id="endElementSAXFunc" href="libxml/libxml-parser.html#endElementSAXFunc">
<ANCHOR id="attributeSAXFunc" href="libxml/libxml-parser.html#attributeSAXFunc">
<ANCHOR id="referenceSAXFunc" href="libxml/libxml-parser.html#referenceSAXFunc">
<ANCHOR id="charactersSAXFunc" href="libxml/libxml-parser.html#charactersSAXFunc">
<ANCHOR id="ignorableWhitespaceSAXFunc" href="libxml/libxml-parser.html#ignorableWhitespaceSAXFunc">
<ANCHOR id="processingInstructionSAXFunc" href="libxml/libxml-parser.html#processingInstructionSAXFunc">
<ANCHOR id="commentSAXFunc" href="libxml/libxml-parser.html#commentSAXFunc">
<ANCHOR id="cdataBlockSAXFunc" href="libxml/libxml-parser.html#cdataBlockSAXFunc">
<ANCHOR id="warningSAXFunc" href="libxml/libxml-parser.html#warningSAXFunc">
<ANCHOR id="errorSAXFunc" href="libxml/libxml-parser.html#errorSAXFunc">
<ANCHOR id="fatalErrorSAXFunc" href="libxml/libxml-parser.html#fatalErrorSAXFunc">
<ANCHOR id="isStandaloneSAXFunc" href="libxml/libxml-parser.html#isStandaloneSAXFunc">
<ANCHOR id="hasInternalSubsetSAXFunc" href="libxml/libxml-parser.html#hasInternalSubsetSAXFunc">
<ANCHOR id="hasExternalSubsetSAXFunc" href="libxml/libxml-parser.html#hasExternalSubsetSAXFunc">
<ANCHOR id="XML-SAX2-MAGIC-CAPS" href="libxml/libxml-parser.html#XML-SAX2-MAGIC-CAPS">
<ANCHOR id="startElementNsSAX2Func" href="libxml/libxml-parser.html#startElementNsSAX2Func">
<ANCHOR id="endElementNsSAX2Func" href="libxml/libxml-parser.html#endElementNsSAX2Func">
<ANCHOR id="xmlSAXHandler" href="libxml/libxml-parser.html#xmlSAXHandler">
<ANCHOR id="xmlSAXHandlerV1" href="libxml/libxml-parser.html#xmlSAXHandlerV1">
<ANCHOR id="xmlSAXHandlerV1Ptr" href="libxml/libxml-parser.html#xmlSAXHandlerV1Ptr">
<ANCHOR id="xmlExternalEntityLoader" href="libxml/libxml-parser.html#xmlExternalEntityLoader">
<ANCHOR id="xmlParserOption" href="libxml/libxml-parser.html#xmlParserOption">
<ANCHOR id="libxml-c14n" href="libxml/libxml-c14n.html">
<ANCHOR id="xmlC14NIsVisibleCallback" href="libxml/libxml-c14n.html#xmlC14NIsVisibleCallback">
<ANCHOR id="libxml-xmlerror" href="libxml/libxml-xmlerror.html">
<ANCHOR id="xmlErrorLevel" href="libxml/libxml-xmlerror.html#xmlErrorLevel">
<ANCHOR id="xmlErrorDomain" href="libxml/libxml-xmlerror.html#xmlErrorDomain">
<ANCHOR id="xmlError" href="libxml/libxml-xmlerror.html#xmlError">
<ANCHOR id="xmlErrorPtr" href="libxml/libxml-xmlerror.html#xmlErrorPtr">
<ANCHOR id="xmlParserErrors" href="libxml/libxml-xmlerror.html#xmlParserErrors">
<ANCHOR id="xmlGenericErrorFunc" href="libxml/libxml-xmlerror.html#xmlGenericErrorFunc">
<ANCHOR id="xmlStructuredErrorFunc" href="libxml/libxml-xmlerror.html#xmlStructuredErrorFunc">
<ANCHOR id="libxml-xmlwin32version" href="libxml/libxml-xmlwin32version.html">
<ANCHOR id="xmlCheckVersion" href="libxml/libxml-xmlwin32version.html#xmlCheckVersion">
<ANCHOR id="LIBXML-DOTTED-VERSION-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-DOTTED-VERSION-CAPS">
<ANCHOR id="LIBXML-VERSION-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-VERSION-CAPS">
<ANCHOR id="LIBXML-VERSION-STRING-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-VERSION-STRING-CAPS">
<ANCHOR id="LIBXML-TEST-VERSION-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-TEST-VERSION-CAPS">
<ANCHOR id="WITH-TRIO-CAPS" href="libxml/libxml-xmlwin32version.html#WITH-TRIO-CAPS">
<ANCHOR id="WITHOUT-TRIO-CAPS" href="libxml/libxml-xmlwin32version.html#WITHOUT-TRIO-CAPS">
<ANCHOR id="LIBXML-THREAD-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-THREAD-ENABLED-CAPS">
<ANCHOR id="LIBXML-FTP-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-FTP-ENABLED-CAPS">
<ANCHOR id="LIBXML-HTTP-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-HTTP-ENABLED-CAPS">
<ANCHOR id="LIBXML-HTML-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-HTML-ENABLED-CAPS">
<ANCHOR id="LIBXML-CATALOG-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-CATALOG-ENABLED-CAPS">
<ANCHOR id="LIBXML-DOCB-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-DOCB-ENABLED-CAPS">
<ANCHOR id="LIBXML-XPATH-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-XPATH-ENABLED-CAPS">
<ANCHOR id="LIBXML-XPTR-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-XPTR-ENABLED-CAPS">
<ANCHOR id="LIBXML-C14N-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-C14N-ENABLED-CAPS">
<ANCHOR id="LIBXML-XINCLUDE-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-XINCLUDE-ENABLED-CAPS">
<ANCHOR id="LIBXML-ICONV-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-ICONV-ENABLED-CAPS">
<ANCHOR id="LIBXML-DEBUG-ENABLED-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-DEBUG-ENABLED-CAPS">
<ANCHOR id="DEBUG-MEMORY-LOCATION-CAPS" href="libxml/libxml-xmlwin32version.html#DEBUG-MEMORY-LOCATION-CAPS">
<ANCHOR id="LIBXML-DLL-IMPORT-CAPS" href="libxml/libxml-xmlwin32version.html#LIBXML-DLL-IMPORT-CAPS">
<ANCHOR id="ATTRIBUTE-UNUSED-CAPS" href="libxml/libxml-xmlwin32version.html#ATTRIBUTE-UNUSED-CAPS">
<ANCHOR id="libxml-DOCBparser" href="libxml/libxml-DOCBparser.html">
<ANCHOR id="docbParserCtxt" href="libxml/libxml-DOCBparser.html#docbParserCtxt">
<ANCHOR id="docbParserCtxtPtr" href="libxml/libxml-DOCBparser.html#docbParserCtxtPtr">
<ANCHOR id="docbSAXHandler" href="libxml/libxml-DOCBparser.html#docbSAXHandler">
<ANCHOR id="docbSAXHandlerPtr" href="libxml/libxml-DOCBparser.html#docbSAXHandlerPtr">
<ANCHOR id="docbParserInput" href="libxml/libxml-DOCBparser.html#docbParserInput">
<ANCHOR id="docbParserInputPtr" href="libxml/libxml-DOCBparser.html#docbParserInputPtr">
<ANCHOR id="docbDocPtr" href="libxml/libxml-DOCBparser.html#docbDocPtr">
<ANCHOR id="libxml-dict" href="libxml/libxml-dict.html">
<ANCHOR id="xmlDict" href="libxml/libxml-dict.html#xmlDict">
<ANCHOR id="xmlDictPtr" href="libxml/libxml-dict.html#xmlDictPtr">
<ANCHOR id="libxml-xmlautomata" href="libxml/libxml-xmlautomata.html">
<ANCHOR id="xmlAutomata" href="libxml/libxml-xmlautomata.html#xmlAutomata">
<ANCHOR id="xmlAutomataPtr" href="libxml/libxml-xmlautomata.html#xmlAutomataPtr">
<ANCHOR id="xmlAutomataState" href="libxml/libxml-xmlautomata.html#xmlAutomataState">
<ANCHOR id="xmlAutomataStatePtr" href="libxml/libxml-xmlautomata.html#xmlAutomataStatePtr">
<ANCHOR id="libxml-xpointer" href="libxml/libxml-xpointer.html">
<ANCHOR id="xmlLocationSet" href="libxml/libxml-xpointer.html#xmlLocationSet">
<ANCHOR id="xmlLocationSetPtr" href="libxml/libxml-xpointer.html#xmlLocationSetPtr">
<ANCHOR id="libxml-hash" href="libxml/libxml-hash.html">
<ANCHOR id="xmlHashTable" href="libxml/libxml-hash.html#xmlHashTable">
<ANCHOR id="xmlHashTablePtr" href="libxml/libxml-hash.html#xmlHashTablePtr">
<ANCHOR id="xmlHashDeallocator" href="libxml/libxml-hash.html#xmlHashDeallocator">
<ANCHOR id="xmlHashCopier" href="libxml/libxml-hash.html#xmlHashCopier">
<ANCHOR id="xmlHashScanner" href="libxml/libxml-hash.html#xmlHashScanner">
<ANCHOR id="xmlHashScannerFull" href="libxml/libxml-hash.html#xmlHashScannerFull">
<ANCHOR id="libxml-relaxng" href="libxml/libxml-relaxng.html">
<ANCHOR id="xmlRelaxNG" href="libxml/libxml-relaxng.html#xmlRelaxNG">
<ANCHOR id="xmlRelaxNGPtr" href="libxml/libxml-relaxng.html#xmlRelaxNGPtr">
<ANCHOR id="xmlRelaxNGValidityErrorFunc" href="libxml/libxml-relaxng.html#xmlRelaxNGValidityErrorFunc">
<ANCHOR id="xmlRelaxNGValidityWarningFunc" href="libxml/libxml-relaxng.html#xmlRelaxNGValidityWarningFunc">
<ANCHOR id="xmlRelaxNGParserCtxt" href="libxml/libxml-relaxng.html#xmlRelaxNGParserCtxt">
<ANCHOR id="xmlRelaxNGParserCtxtPtr" href="libxml/libxml-relaxng.html#xmlRelaxNGParserCtxtPtr">
<ANCHOR id="xmlRelaxNGValidCtxt" href="libxml/libxml-relaxng.html#xmlRelaxNGValidCtxt">
<ANCHOR id="xmlRelaxNGValidCtxtPtr" href="libxml/libxml-relaxng.html#xmlRelaxNGValidCtxtPtr">
<ANCHOR id="xmlRelaxNGValidErr" href="libxml/libxml-relaxng.html#xmlRelaxNGValidErr">
<ANCHOR id="libxml-xpathInternals" href="libxml/libxml-xpathInternals.html">
<ANCHOR id="xmlXPathSetError" href="libxml/libxml-xpathInternals.html#xmlXPathSetError">
<ANCHOR id="xmlXPathSetArityError" href="libxml/libxml-xpathInternals.html#xmlXPathSetArityError">
<ANCHOR id="xmlXPathSetTypeError" href="libxml/libxml-xpathInternals.html#xmlXPathSetTypeError">
<ANCHOR id="xmlXPathGetError" href="libxml/libxml-xpathInternals.html#xmlXPathGetError">
<ANCHOR id="xmlXPathCheckError" href="libxml/libxml-xpathInternals.html#xmlXPathCheckError">
<ANCHOR id="xmlXPathGetDocument" href="libxml/libxml-xpathInternals.html#xmlXPathGetDocument">
<ANCHOR id="xmlXPathGetContextNode" href="libxml/libxml-xpathInternals.html#xmlXPathGetContextNode">
<ANCHOR id="xmlXPathReturnBoolean" href="libxml/libxml-xpathInternals.html#xmlXPathReturnBoolean">
<ANCHOR id="xmlXPathReturnTrue" href="libxml/libxml-xpathInternals.html#xmlXPathReturnTrue">
<ANCHOR id="xmlXPathReturnFalse" href="libxml/libxml-xpathInternals.html#xmlXPathReturnFalse">
<ANCHOR id="xmlXPathReturnNumber" href="libxml/libxml-xpathInternals.html#xmlXPathReturnNumber">
<ANCHOR id="xmlXPathReturnString" href="libxml/libxml-xpathInternals.html#xmlXPathReturnString">
<ANCHOR id="xmlXPathReturnEmptyString" href="libxml/libxml-xpathInternals.html#xmlXPathReturnEmptyString">
<ANCHOR id="xmlXPathReturnNodeSet" href="libxml/libxml-xpathInternals.html#xmlXPathReturnNodeSet">
<ANCHOR id="xmlXPathReturnEmptyNodeSet" href="libxml/libxml-xpathInternals.html#xmlXPathReturnEmptyNodeSet">
<ANCHOR id="xmlXPathReturnExternal" href="libxml/libxml-xpathInternals.html#xmlXPathReturnExternal">
<ANCHOR id="xmlXPathStackIsNodeSet" href="libxml/libxml-xpathInternals.html#xmlXPathStackIsNodeSet">
<ANCHOR id="xmlXPathStackIsExternal" href="libxml/libxml-xpathInternals.html#xmlXPathStackIsExternal">
<ANCHOR id="xmlXPathEmptyNodeSet" href="libxml/libxml-xpathInternals.html#xmlXPathEmptyNodeSet">
<ANCHOR id="CHECK-ERROR-CAPS" href="libxml/libxml-xpathInternals.html#CHECK-ERROR-CAPS">
<ANCHOR id="CHECK-ERROR0-CAPS" href="libxml/libxml-xpathInternals.html#CHECK-ERROR0-CAPS">
<ANCHOR id="XP-ERROR-CAPS" href="libxml/libxml-xpathInternals.html#XP-ERROR-CAPS">
<ANCHOR id="XP-ERROR0-CAPS" href="libxml/libxml-xpathInternals.html#XP-ERROR0-CAPS">
<ANCHOR id="CHECK-TYPE-CAPS" href="libxml/libxml-xpathInternals.html#CHECK-TYPE-CAPS">
<ANCHOR id="CHECK-TYPE0-CAPS" href="libxml/libxml-xpathInternals.html#CHECK-TYPE0-CAPS">
<ANCHOR id="CHECK-ARITY-CAPS" href="libxml/libxml-xpathInternals.html#CHECK-ARITY-CAPS">
<ANCHOR id="CAST-TO-STRING-CAPS" href="libxml/libxml-xpathInternals.html#CAST-TO-STRING-CAPS">
<ANCHOR id="CAST-TO-NUMBER-CAPS" href="libxml/libxml-xpathInternals.html#CAST-TO-NUMBER-CAPS">
<ANCHOR id="CAST-TO-BOOLEAN-CAPS" href="libxml/libxml-xpathInternals.html#CAST-TO-BOOLEAN-CAPS">
<ANCHOR id="xmlXPathVariableLookupFunc" href="libxml/libxml-xpathInternals.html#xmlXPathVariableLookupFunc">
<ANCHOR id="xmlXPathFuncLookupFunc" href="libxml/libxml-xpathInternals.html#xmlXPathFuncLookupFunc">
<ANCHOR id="libxml-xmlversion" href="libxml/libxml-xmlversion.html">
<ANCHOR id="LIBXML-DOTTED-VERSION-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-DOTTED-VERSION-CAPS">
<ANCHOR id="LIBXML-VERSION-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-VERSION-CAPS">
<ANCHOR id="LIBXML-VERSION-STRING-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-VERSION-STRING-CAPS">
<ANCHOR id="LIBXML-TEST-VERSION-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-TEST-VERSION-CAPS">
<ANCHOR id="WITH-TRIO-CAPS" href="libxml/libxml-xmlversion.html#WITH-TRIO-CAPS">
<ANCHOR id="WITHOUT-TRIO-CAPS" href="libxml/libxml-xmlversion.html#WITHOUT-TRIO-CAPS">
<ANCHOR id="LIBXML-THREAD-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-THREAD-ENABLED-CAPS">
<ANCHOR id="LIBXML-TREE-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-TREE-ENABLED-CAPS">
<ANCHOR id="LIBXML-OUTPUT-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-OUTPUT-ENABLED-CAPS">
<ANCHOR id="LIBXML-PUSH-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-PUSH-ENABLED-CAPS">
<ANCHOR id="LIBXML-READER-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-READER-ENABLED-CAPS">
<ANCHOR id="LIBXML-WRITER-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-WRITER-ENABLED-CAPS">
<ANCHOR id="LIBXML-SAX1-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-SAX1-ENABLED-CAPS">
<ANCHOR id="LIBXML-FTP-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-FTP-ENABLED-CAPS">
<ANCHOR id="LIBXML-HTTP-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-HTTP-ENABLED-CAPS">
<ANCHOR id="LIBXML-VALID-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-VALID-ENABLED-CAPS">
<ANCHOR id="LIBXML-HTML-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-HTML-ENABLED-CAPS">
<ANCHOR id="LIBXML-LEGACY-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-LEGACY-ENABLED-CAPS">
<ANCHOR id="LIBXML-C14N-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-C14N-ENABLED-CAPS">
<ANCHOR id="LIBXML-CATALOG-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-CATALOG-ENABLED-CAPS">
<ANCHOR id="LIBXML-DOCB-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-DOCB-ENABLED-CAPS">
<ANCHOR id="LIBXML-XPATH-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-XPATH-ENABLED-CAPS">
<ANCHOR id="LIBXML-XPTR-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-XPTR-ENABLED-CAPS">
<ANCHOR id="LIBXML-XINCLUDE-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-XINCLUDE-ENABLED-CAPS">
<ANCHOR id="LIBXML-ICONV-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-ICONV-ENABLED-CAPS">
<ANCHOR id="LIBXML-ISO8859X-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-ISO8859X-ENABLED-CAPS">
<ANCHOR id="LIBXML-DEBUG-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-DEBUG-ENABLED-CAPS">
<ANCHOR id="DEBUG-MEMORY-LOCATION-CAPS" href="libxml/libxml-xmlversion.html#DEBUG-MEMORY-LOCATION-CAPS">
<ANCHOR id="LIBXML-UNICODE-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-UNICODE-ENABLED-CAPS">
<ANCHOR id="LIBXML-REGEXP-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-REGEXP-ENABLED-CAPS">
<ANCHOR id="LIBXML-AUTOMATA-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-AUTOMATA-ENABLED-CAPS">
<ANCHOR id="LIBXML-SCHEMAS-ENABLED-CAPS" href="libxml/libxml-xmlversion.html#LIBXML-SCHEMAS-ENABLED-CAPS">
<ANCHOR id="ATTRIBUTE-UNUSED-CAPS" href="libxml/libxml-xmlversion.html#ATTRIBUTE-UNUSED-CAPS">
<ANCHOR id="libxml-list" href="libxml/libxml-list.html">
<ANCHOR id="xmlLink" href="libxml/libxml-list.html#xmlLink">
<ANCHOR id="xmlLinkPtr" href="libxml/libxml-list.html#xmlLinkPtr">
<ANCHOR id="xmlList" href="libxml/libxml-list.html#xmlList">
<ANCHOR id="xmlListPtr" href="libxml/libxml-list.html#xmlListPtr">
<ANCHOR id="xmlListDeallocator" href="libxml/libxml-list.html#xmlListDeallocator">
<ANCHOR id="xmlListDataCompare" href="libxml/libxml-list.html#xmlListDataCompare">
<ANCHOR id="xmlListWalker" href="libxml/libxml-list.html#xmlListWalker">
<ANCHOR id="libxml-HTMLtree" href="libxml/libxml-HTMLtree.html">
<ANCHOR id="HTML-TEXT-NODE-CAPS" href="libxml/libxml-HTMLtree.html#HTML-TEXT-NODE-CAPS">
<ANCHOR id="HTML-ENTITY-REF-NODE-CAPS" href="libxml/libxml-HTMLtree.html#HTML-ENTITY-REF-NODE-CAPS">
<ANCHOR id="HTML-COMMENT-NODE-CAPS" href="libxml/libxml-HTMLtree.html#HTML-COMMENT-NODE-CAPS">
<ANCHOR id="HTML-PRESERVE-NODE-CAPS" href="libxml/libxml-HTMLtree.html#HTML-PRESERVE-NODE-CAPS">
<ANCHOR id="HTML-PI-NODE-CAPS" href="libxml/libxml-HTMLtree.html#HTML-PI-NODE-CAPS">
<ANCHOR id="libxml-parserInternals" href="libxml/libxml-parserInternals.html">
<ANCHOR id="xmlParserMaxDepth" href="libxml/libxml-parserInternals.html#xmlParserMaxDepth">
<ANCHOR id="XML-MAX-NAMELEN-CAPS" href="libxml/libxml-parserInternals.html#XML-MAX-NAMELEN-CAPS">
<ANCHOR id="INPUT-CHUNK-CAPS" href="libxml/libxml-parserInternals.html#INPUT-CHUNK-CAPS">
<ANCHOR id="IS-BYTE-CHAR-CAPS" href="libxml/libxml-parserInternals.html#IS-BYTE-CHAR-CAPS">
<ANCHOR id="IS-CHAR-CAPS" href="libxml/libxml-parserInternals.html#IS-CHAR-CAPS">
<ANCHOR id="IS-CHAR-CH-CAPS" href="libxml/libxml-parserInternals.html#IS-CHAR-CH-CAPS">
<ANCHOR id="IS-BLANK-CAPS" href="libxml/libxml-parserInternals.html#IS-BLANK-CAPS">
<ANCHOR id="IS-BLANK-CH-CAPS" href="libxml/libxml-parserInternals.html#IS-BLANK-CH-CAPS">
<ANCHOR id="IS-BASECHAR-CAPS" href="libxml/libxml-parserInternals.html#IS-BASECHAR-CAPS">
<ANCHOR id="IS-DIGIT-CAPS" href="libxml/libxml-parserInternals.html#IS-DIGIT-CAPS">
<ANCHOR id="IS-DIGIT-CH-CAPS" href="libxml/libxml-parserInternals.html#IS-DIGIT-CH-CAPS">
<ANCHOR id="IS-COMBINING-CAPS" href="libxml/libxml-parserInternals.html#IS-COMBINING-CAPS">
<ANCHOR id="IS-COMBINING-CH-CAPS" href="libxml/libxml-parserInternals.html#IS-COMBINING-CH-CAPS">
<ANCHOR id="IS-EXTENDER-CAPS" href="libxml/libxml-parserInternals.html#IS-EXTENDER-CAPS">
<ANCHOR id="IS-EXTENDER-CH-CAPS" href="libxml/libxml-parserInternals.html#IS-EXTENDER-CH-CAPS">
<ANCHOR id="IS-IDEOGRAPHIC-CAPS" href="libxml/libxml-parserInternals.html#IS-IDEOGRAPHIC-CAPS">
<ANCHOR id="IS-LETTER-CAPS" href="libxml/libxml-parserInternals.html#IS-LETTER-CAPS">
<ANCHOR id="IS-LETTER-CH-CAPS" href="libxml/libxml-parserInternals.html#IS-LETTER-CH-CAPS">
<ANCHOR id="IS-PUBIDCHAR-CAPS" href="libxml/libxml-parserInternals.html#IS-PUBIDCHAR-CAPS">
<ANCHOR id="IS-PUBIDCHAR-CH-CAPS" href="libxml/libxml-parserInternals.html#IS-PUBIDCHAR-CH-CAPS">
<ANCHOR id="SKIP-EOL-CAPS" href="libxml/libxml-parserInternals.html#SKIP-EOL-CAPS">
<ANCHOR id="MOVETO-ENDTAG-CAPS" href="libxml/libxml-parserInternals.html#MOVETO-ENDTAG-CAPS">
<ANCHOR id="MOVETO-STARTTAG-CAPS" href="libxml/libxml-parserInternals.html#MOVETO-STARTTAG-CAPS">
<ANCHOR id="XML-SUBSTITUTE-NONE-CAPS" href="libxml/libxml-parserInternals.html#XML-SUBSTITUTE-NONE-CAPS">
<ANCHOR id="XML-SUBSTITUTE-REF-CAPS" href="libxml/libxml-parserInternals.html#XML-SUBSTITUTE-REF-CAPS">
<ANCHOR id="XML-SUBSTITUTE-PEREF-CAPS" href="libxml/libxml-parserInternals.html#XML-SUBSTITUTE-PEREF-CAPS">
<ANCHOR id="XML-SUBSTITUTE-BOTH-CAPS" href="libxml/libxml-parserInternals.html#XML-SUBSTITUTE-BOTH-CAPS">
<ANCHOR id="xmlEntityReferenceFunc" href="libxml/libxml-parserInternals.html#xmlEntityReferenceFunc">
<ANCHOR id="libxml-entities" href="libxml/libxml-entities.html">
<ANCHOR id="xmlEntityType" href="libxml/libxml-entities.html#xmlEntityType">
<ANCHOR id="xmlEntity" href="libxml/libxml-entities.html#xmlEntity">
<ANCHOR id="xmlEntitiesTablePtr" href="libxml/libxml-entities.html#xmlEntitiesTablePtr">
<ANCHOR id="libxml-HTMLparser" href="libxml/libxml-HTMLparser.html">
<ANCHOR id="htmlParserCtxt" href="libxml/libxml-HTMLparser.html#htmlParserCtxt">
<ANCHOR id="htmlParserCtxtPtr" href="libxml/libxml-HTMLparser.html#htmlParserCtxtPtr">
<ANCHOR id="htmlParserNodeInfo" href="libxml/libxml-HTMLparser.html#htmlParserNodeInfo">
<ANCHOR id="htmlSAXHandler" href="libxml/libxml-HTMLparser.html#htmlSAXHandler">
<ANCHOR id="htmlSAXHandlerPtr" href="libxml/libxml-HTMLparser.html#htmlSAXHandlerPtr">
<ANCHOR id="htmlParserInput" href="libxml/libxml-HTMLparser.html#htmlParserInput">
<ANCHOR id="htmlParserInputPtr" href="libxml/libxml-HTMLparser.html#htmlParserInputPtr">
<ANCHOR id="htmlDocPtr" href="libxml/libxml-HTMLparser.html#htmlDocPtr">
<ANCHOR id="htmlNodePtr" href="libxml/libxml-HTMLparser.html#htmlNodePtr">
<ANCHOR id="htmlElemDesc" href="libxml/libxml-HTMLparser.html#htmlElemDesc">
<ANCHOR id="htmlElemDescPtr" href="libxml/libxml-HTMLparser.html#htmlElemDescPtr">
<ANCHOR id="htmlEntityDesc" href="libxml/libxml-HTMLparser.html#htmlEntityDesc">
<ANCHOR id="htmlEntityDescPtr" href="libxml/libxml-HTMLparser.html#htmlEntityDescPtr">
<ANCHOR id="htmlParserOption" href="libxml/libxml-HTMLparser.html#htmlParserOption">
<ANCHOR id="libxml-valid" href="libxml/libxml-valid.html">
<ANCHOR id="xmlValidState" href="libxml/libxml-valid.html#xmlValidState">
<ANCHOR id="xmlValidStatePtr" href="libxml/libxml-valid.html#xmlValidStatePtr">
<ANCHOR id="xmlValidityErrorFunc" href="libxml/libxml-valid.html#xmlValidityErrorFunc">
<ANCHOR id="xmlValidityWarningFunc" href="libxml/libxml-valid.html#xmlValidityWarningFunc">
<ANCHOR id="xmlValidCtxt" href="libxml/libxml-valid.html#xmlValidCtxt">
<ANCHOR id="xmlValidCtxtPtr" href="libxml/libxml-valid.html#xmlValidCtxtPtr">
<ANCHOR id="xmlNotationTablePtr" href="libxml/libxml-valid.html#xmlNotationTablePtr">
<ANCHOR id="xmlElementTablePtr" href="libxml/libxml-valid.html#xmlElementTablePtr">
<ANCHOR id="xmlAttributeTablePtr" href="libxml/libxml-valid.html#xmlAttributeTablePtr">
<ANCHOR id="xmlIDTablePtr" href="libxml/libxml-valid.html#xmlIDTablePtr">
<ANCHOR id="xmlRefTablePtr" href="libxml/libxml-valid.html#xmlRefTablePtr">
<ANCHOR id="libxml-catalog" href="libxml/libxml-catalog.html">
<ANCHOR id="XML-CATALOGS-NAMESPACE-CAPS" href="libxml/libxml-catalog.html#XML-CATALOGS-NAMESPACE-CAPS">
<ANCHOR id="XML-CATALOG-PI-CAPS" href="libxml/libxml-catalog.html#XML-CATALOG-PI-CAPS">
<ANCHOR id="xmlCatalogPrefer" href="libxml/libxml-catalog.html#xmlCatalogPrefer">
<ANCHOR id="xmlCatalogAllow" href="libxml/libxml-catalog.html#xmlCatalogAllow">
<ANCHOR id="xmlCatalog" href="libxml/libxml-catalog.html#xmlCatalog">
<ANCHOR id="xmlCatalogPtr" href="libxml/libxml-catalog.html#xmlCatalogPtr">
<ANCHOR id="libxml-tree" href="libxml/libxml-tree.html">
<ANCHOR id="xmlParserInputBuffer" href="libxml/libxml-tree.html#xmlParserInputBuffer">
<ANCHOR id="xmlParserInputBufferPtr" href="libxml/libxml-tree.html#xmlParserInputBufferPtr">
<ANCHOR id="xmlOutputBuffer" href="libxml/libxml-tree.html#xmlOutputBuffer">
<ANCHOR id="xmlOutputBufferPtr" href="libxml/libxml-tree.html#xmlOutputBufferPtr">
<ANCHOR id="xmlParserInput" href="libxml/libxml-tree.html#xmlParserInput">
<ANCHOR id="xmlParserInputPtr" href="libxml/libxml-tree.html#xmlParserInputPtr">
<ANCHOR id="xmlParserCtxt" href="libxml/libxml-tree.html#xmlParserCtxt">
<ANCHOR id="xmlParserCtxtPtr" href="libxml/libxml-tree.html#xmlParserCtxtPtr">
<ANCHOR id="xmlSAXLocator" href="libxml/libxml-tree.html#xmlSAXLocator">
<ANCHOR id="xmlSAXLocatorPtr" href="libxml/libxml-tree.html#xmlSAXLocatorPtr">
<ANCHOR id="xmlSAXHandler" href="libxml/libxml-tree.html#xmlSAXHandler">
<ANCHOR id="xmlSAXHandlerPtr" href="libxml/libxml-tree.html#xmlSAXHandlerPtr">
<ANCHOR id="xmlEntity" href="libxml/libxml-tree.html#xmlEntity">
<ANCHOR id="xmlEntityPtr" href="libxml/libxml-tree.html#xmlEntityPtr">
<ANCHOR id="BASE-BUFFER-SIZE-CAPS" href="libxml/libxml-tree.html#BASE-BUFFER-SIZE-CAPS">
<ANCHOR id="XML-XML-NAMESPACE-CAPS" href="libxml/libxml-tree.html#XML-XML-NAMESPACE-CAPS">
<ANCHOR id="xmlElementType" href="libxml/libxml-tree.html#xmlElementType">
<ANCHOR id="xmlChar" href="libxml/libxml-tree.html#xmlChar">
<ANCHOR id="BAD-CAST-CAPS" href="libxml/libxml-tree.html#BAD-CAST-CAPS">
<ANCHOR id="xmlNotation" href="libxml/libxml-tree.html#xmlNotation">
<ANCHOR id="xmlNotationPtr" href="libxml/libxml-tree.html#xmlNotationPtr">
<ANCHOR id="xmlAttributeType" href="libxml/libxml-tree.html#xmlAttributeType">
<ANCHOR id="xmlAttributeDefault" href="libxml/libxml-tree.html#xmlAttributeDefault">
<ANCHOR id="xmlEnumeration" href="libxml/libxml-tree.html#xmlEnumeration">
<ANCHOR id="xmlEnumerationPtr" href="libxml/libxml-tree.html#xmlEnumerationPtr">
<ANCHOR id="xmlAttribute" href="libxml/libxml-tree.html#xmlAttribute">
<ANCHOR id="xmlAttributePtr" href="libxml/libxml-tree.html#xmlAttributePtr">
<ANCHOR id="xmlElementContentType" href="libxml/libxml-tree.html#xmlElementContentType">
<ANCHOR id="xmlElementContentOccur" href="libxml/libxml-tree.html#xmlElementContentOccur">
<ANCHOR id="xmlElementContent" href="libxml/libxml-tree.html#xmlElementContent">
<ANCHOR id="xmlElementContentPtr" href="libxml/libxml-tree.html#xmlElementContentPtr">
<ANCHOR id="xmlElementTypeVal" href="libxml/libxml-tree.html#xmlElementTypeVal">
<ANCHOR id="xmlElement" href="libxml/libxml-tree.html#xmlElement">
<ANCHOR id="xmlElementPtr" href="libxml/libxml-tree.html#xmlElementPtr">
<ANCHOR id="XML-LOCAL-NAMESPACE-CAPS" href="libxml/libxml-tree.html#XML-LOCAL-NAMESPACE-CAPS">
<ANCHOR id="xmlNsType" href="libxml/libxml-tree.html#xmlNsType">
<ANCHOR id="xmlNs" href="libxml/libxml-tree.html#xmlNs">
<ANCHOR id="xmlNsPtr" href="libxml/libxml-tree.html#xmlNsPtr">
<ANCHOR id="xmlDtd" href="libxml/libxml-tree.html#xmlDtd">
<ANCHOR id="xmlDtdPtr" href="libxml/libxml-tree.html#xmlDtdPtr">
<ANCHOR id="xmlAttr" href="libxml/libxml-tree.html#xmlAttr">
<ANCHOR id="xmlAttrPtr" href="libxml/libxml-tree.html#xmlAttrPtr">
<ANCHOR id="xmlID" href="libxml/libxml-tree.html#xmlID">
<ANCHOR id="xmlIDPtr" href="libxml/libxml-tree.html#xmlIDPtr">
<ANCHOR id="xmlRef" href="libxml/libxml-tree.html#xmlRef">
<ANCHOR id="xmlRefPtr" href="libxml/libxml-tree.html#xmlRefPtr">
<ANCHOR id="xmlBufferAllocationScheme" href="libxml/libxml-tree.html#xmlBufferAllocationScheme">
<ANCHOR id="xmlBuffer" href="libxml/libxml-tree.html#xmlBuffer">
<ANCHOR id="xmlBufferPtr" href="libxml/libxml-tree.html#xmlBufferPtr">
<ANCHOR id="xmlNode" href="libxml/libxml-tree.html#xmlNode">
<ANCHOR id="xmlNodePtr" href="libxml/libxml-tree.html#xmlNodePtr">
<ANCHOR id="XML-GET-CONTENT-CAPS" href="libxml/libxml-tree.html#XML-GET-CONTENT-CAPS">
<ANCHOR id="XML-GET-LINE-CAPS" href="libxml/libxml-tree.html#XML-GET-LINE-CAPS">
<ANCHOR id="xmlDoc" href="libxml/libxml-tree.html#xmlDoc">
<ANCHOR id="xmlDocPtr" href="libxml/libxml-tree.html#xmlDocPtr">
<ANCHOR id="xmlChildrenNode" href="libxml/libxml-tree.html#xmlChildrenNode">
<ANCHOR id="xmlRootNode" href="libxml/libxml-tree.html#xmlRootNode">
<ANCHOR id="libxml-globals" href="libxml/libxml-globals.html">
<ANCHOR id="xmlRegisterNodeFunc" href="libxml/libxml-globals.html#xmlRegisterNodeFunc">
<ANCHOR id="xmlDeregisterNodeFunc" href="libxml/libxml-globals.html#xmlDeregisterNodeFunc">
<ANCHOR id="xmlGlobalState" href="libxml/libxml-globals.html#xmlGlobalState">
<ANCHOR id="xmlGlobalStatePtr" href="libxml/libxml-globals.html#xmlGlobalStatePtr">
<ANCHOR id="xmlMalloc" href="libxml/libxml-globals.html#xmlMalloc">
<ANCHOR id="xmlMallocAtomic" href="libxml/libxml-globals.html#xmlMallocAtomic">
<ANCHOR id="xmlRealloc" href="libxml/libxml-globals.html#xmlRealloc">
<ANCHOR id="xmlFree" href="libxml/libxml-globals.html#xmlFree">
<ANCHOR id="xmlMemStrdup" href="libxml/libxml-globals.html#xmlMemStrdup">
<ANCHOR id="docbDefaultSAXHandler" href="libxml/libxml-globals.html#docbDefaultSAXHandler">
<ANCHOR id="htmlDefaultSAXHandler" href="libxml/libxml-globals.html#htmlDefaultSAXHandler">
<ANCHOR id="xmlLastError" href="libxml/libxml-globals.html#xmlLastError">
<ANCHOR id="oldXMLWDcompatibility" href="libxml/libxml-globals.html#oldXMLWDcompatibility">
<ANCHOR id="xmlBufferAllocScheme" href="libxml/libxml-globals.html#xmlBufferAllocScheme">
<ANCHOR id="xmlDefaultBufferSize" href="libxml/libxml-globals.html#xmlDefaultBufferSize">
<ANCHOR id="xmlDefaultSAXHandler" href="libxml/libxml-globals.html#xmlDefaultSAXHandler">
<ANCHOR id="xmlDefaultSAXLocator" href="libxml/libxml-globals.html#xmlDefaultSAXLocator">
<ANCHOR id="xmlDoValidityCheckingDefaultValue" href="libxml/libxml-globals.html#xmlDoValidityCheckingDefaultValue">
<ANCHOR id="xmlGenericError" href="libxml/libxml-globals.html#xmlGenericError">
<ANCHOR id="xmlStructuredError" href="libxml/libxml-globals.html#xmlStructuredError">
<ANCHOR id="xmlGenericErrorContext" href="libxml/libxml-globals.html#xmlGenericErrorContext">
<ANCHOR id="xmlGetWarningsDefaultValue" href="libxml/libxml-globals.html#xmlGetWarningsDefaultValue">
<ANCHOR id="xmlIndentTreeOutput" href="libxml/libxml-globals.html#xmlIndentTreeOutput">
<ANCHOR id="xmlTreeIndentString" href="libxml/libxml-globals.html#xmlTreeIndentString">
<ANCHOR id="xmlKeepBlanksDefaultValue" href="libxml/libxml-globals.html#xmlKeepBlanksDefaultValue">
<ANCHOR id="xmlLineNumbersDefaultValue" href="libxml/libxml-globals.html#xmlLineNumbersDefaultValue">
<ANCHOR id="xmlLoadExtDtdDefaultValue" href="libxml/libxml-globals.html#xmlLoadExtDtdDefaultValue">
<ANCHOR id="xmlParserDebugEntities" href="libxml/libxml-globals.html#xmlParserDebugEntities">
<ANCHOR id="xmlParserVersion" href="libxml/libxml-globals.html#xmlParserVersion">
<ANCHOR id="xmlPedanticParserDefaultValue" href="libxml/libxml-globals.html#xmlPedanticParserDefaultValue">
<ANCHOR id="xmlSaveNoEmptyTags" href="libxml/libxml-globals.html#xmlSaveNoEmptyTags">
<ANCHOR id="xmlSubstituteEntitiesDefaultValue" href="libxml/libxml-globals.html#xmlSubstituteEntitiesDefaultValue">
<ANCHOR id="xmlRegisterNodeDefaultValue" href="libxml/libxml-globals.html#xmlRegisterNodeDefaultValue">
<ANCHOR id="xmlDeregisterNodeDefaultValue" href="libxml/libxml-globals.html#xmlDeregisterNodeDefaultValue">
<ANCHOR id="libxml-xmlexports" href="libxml/libxml-xmlexports.html">
<ANCHOR id="XMLPUBFUN-CAPS" href="libxml/libxml-xmlexports.html#XMLPUBFUN-CAPS">
<ANCHOR id="XMLPUBVAR-CAPS" href="libxml/libxml-xmlexports.html#XMLPUBVAR-CAPS">
<ANCHOR id="XMLCALL-CAPS" href="libxml/libxml-xmlexports.html#XMLCALL-CAPS">
<ANCHOR id="LIBXML-DLL-IMPORT-CAPS" href="libxml/libxml-xmlexports.html#LIBXML-DLL-IMPORT-CAPS">
<ANCHOR id="libxml-xinclude" href="libxml/libxml-xinclude.html">
<ANCHOR id="XINCLUDE-NS-CAPS" href="libxml/libxml-xinclude.html#XINCLUDE-NS-CAPS">
<ANCHOR id="XINCLUDE-NODE-CAPS" href="libxml/libxml-xinclude.html#XINCLUDE-NODE-CAPS">
<ANCHOR id="XINCLUDE-FALLBACK-CAPS" href="libxml/libxml-xinclude.html#XINCLUDE-FALLBACK-CAPS">
<ANCHOR id="XINCLUDE-HREF-CAPS" href="libxml/libxml-xinclude.html#XINCLUDE-HREF-CAPS">
<ANCHOR id="XINCLUDE-PARSE-CAPS" href="libxml/libxml-xinclude.html#XINCLUDE-PARSE-CAPS">
<ANCHOR id="XINCLUDE-PARSE-XML-CAPS" href="libxml/libxml-xinclude.html#XINCLUDE-PARSE-XML-CAPS">
<ANCHOR id="XINCLUDE-PARSE-TEXT-CAPS" href="libxml/libxml-xinclude.html#XINCLUDE-PARSE-TEXT-CAPS">
<ANCHOR id="XINCLUDE-PARSE-ENCODING-CAPS" href="libxml/libxml-xinclude.html#XINCLUDE-PARSE-ENCODING-CAPS">
<ANCHOR id="xmlXIncludeCtxt" href="libxml/libxml-xinclude.html#xmlXIncludeCtxt">
<ANCHOR id="xmlXIncludeCtxtPtr" href="libxml/libxml-xinclude.html#xmlXIncludeCtxtPtr">
<ANCHOR id="libxml-xmlreader" href="libxml/libxml-xmlreader.html">
<ANCHOR id="xmlParserProperties" href="libxml/libxml-xmlreader.html#xmlParserProperties">
<ANCHOR id="xmlParserSeverities" href="libxml/libxml-xmlreader.html#xmlParserSeverities">
<ANCHOR id="xmlReaderTypes" href="libxml/libxml-xmlreader.html#xmlReaderTypes">
<ANCHOR id="xmlTextReader" href="libxml/libxml-xmlreader.html#xmlTextReader">
<ANCHOR id="xmlTextReaderPtr" href="libxml/libxml-xmlreader.html#xmlTextReaderPtr">
<ANCHOR id="xmlTextReaderLocatorPtr" href="libxml/libxml-xmlreader.html#xmlTextReaderLocatorPtr">
<ANCHOR id="xmlTextReaderErrorFunc" href="libxml/libxml-xmlreader.html#xmlTextReaderErrorFunc">
<ANCHOR id="libxml-debugXML" href="libxml/libxml-debugXML.html">
<ANCHOR id="xmlShellReadlineFunc" href="libxml/libxml-debugXML.html#xmlShellReadlineFunc">
<ANCHOR id="xmlShellCtxt" href="libxml/libxml-debugXML.html#xmlShellCtxt">
<ANCHOR id="xmlShellCtxtPtr" href="libxml/libxml-debugXML.html#xmlShellCtxtPtr">
<ANCHOR id="xmlShellCmd" href="libxml/libxml-debugXML.html#xmlShellCmd">
<ANCHOR id="libxml-xmlwriter" href="libxml/libxml-xmlwriter.html">
<ANCHOR id="xmlTextWriter" href="libxml/libxml-xmlwriter.html#xmlTextWriter">
<ANCHOR id="xmlTextWriterPtr" href="libxml/libxml-xmlwriter.html#xmlTextWriterPtr">
<ANCHOR id="xmlTextWriterWriteProcessingInstruction" href="libxml/libxml-xmlwriter.html#xmlTextWriterWriteProcessingInstruction">
<ANCHOR id="xmlTextWriterWriteDocType" href="libxml/libxml-xmlwriter.html#xmlTextWriterWriteDocType">
<ANCHOR id="xmlTextWriterEndDTDElement" href="libxml/libxml-xmlwriter.html#xmlTextWriterEndDTDElement">
<ANCHOR id="xmlTextWriterEndDTDAttlist" href="libxml/libxml-xmlwriter.html#xmlTextWriterEndDTDAttlist">
<ANCHOR id="xmlTextWriterEndDTDEntity" href="libxml/libxml-xmlwriter.html#xmlTextWriterEndDTDEntity">
<ANCHOR id="libxml-xmlschemas" href="libxml/libxml-xmlschemas.html">
<ANCHOR id="xmlSchemaValidError" href="libxml/libxml-xmlschemas.html#xmlSchemaValidError">
<ANCHOR id="xmlSchema" href="libxml/libxml-xmlschemas.html#xmlSchema">
<ANCHOR id="xmlSchemaPtr" href="libxml/libxml-xmlschemas.html#xmlSchemaPtr">
<ANCHOR id="xmlSchemaValidityErrorFunc" href="libxml/libxml-xmlschemas.html#xmlSchemaValidityErrorFunc">
<ANCHOR id="xmlSchemaValidityWarningFunc" href="libxml/libxml-xmlschemas.html#xmlSchemaValidityWarningFunc">
<ANCHOR id="xmlSchemaParserCtxt" href="libxml/libxml-xmlschemas.html#xmlSchemaParserCtxt">
<ANCHOR id="xmlSchemaParserCtxtPtr" href="libxml/libxml-xmlschemas.html#xmlSchemaParserCtxtPtr">
<ANCHOR id="xmlSchemaValidCtxt" href="libxml/libxml-xmlschemas.html#xmlSchemaValidCtxt">
<ANCHOR id="xmlSchemaValidCtxtPtr" href="libxml/libxml-xmlschemas.html#xmlSchemaValidCtxtPtr">
