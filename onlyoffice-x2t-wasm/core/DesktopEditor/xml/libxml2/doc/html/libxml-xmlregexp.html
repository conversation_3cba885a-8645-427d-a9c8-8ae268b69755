<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlregexp from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlregexp from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlreader.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlreader.html">xmlreader</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlsave.html">xmlsave</a></th><td><a accesskey="n" href="libxml-xmlsave.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>basic API for libxml regular expressions handling used for XML Schemas and validation. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlExpCtxt">xmlExpCtxt</a><br />struct _xmlExpCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlregexp.html#xmlExpCtxt">xmlExpCtxt</a> * <a name="xmlExpCtxtPtr" id="xmlExpCtxtPtr">xmlExpCtxtPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlExpNode">xmlExpNode</a><br />struct _xmlExpNode
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlregexp.html#xmlExpNode">xmlExpNode</a> * <a name="xmlExpNodePtr" id="xmlExpNodePtr">xmlExpNodePtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlExpNodeType">xmlExpNodeType</a>
</pre><pre class="programlisting">Structure <a href="#xmlRegExecCtxt">xmlRegExecCtxt</a><br />struct _xmlRegExecCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlregexp.html#xmlRegExecCtxt">xmlRegExecCtxt</a> * <a name="xmlRegExecCtxtPtr" id="xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlRegexp">xmlRegexp</a><br />struct _xmlRegexp
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlregexp.html#xmlRegexp">xmlRegexp</a> * <a name="xmlRegexpPtr" id="xmlRegexpPtr">xmlRegexpPtr</a>
</pre><pre class="programlisting">int	<a href="#xmlExpCtxtNbCons">xmlExpCtxtNbCons</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlExpCtxtNbNodes">xmlExpCtxtNbNodes</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">void	<a href="#xmlExpDump">xmlExpDump</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> expr)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpExpDerive">xmlExpExpDerive</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> sub)</pre>
<pre class="programlisting">void	<a href="#xmlExpFree">xmlExpFree</a>			(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)</pre>
<pre class="programlisting">void	<a href="#xmlExpFreeCtxt">xmlExpFreeCtxt</a>			(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlExpGetLanguage">xmlExpGetLanguage</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** langList, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlExpGetStart">xmlExpGetStart</a>			(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** tokList, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlExpIsNillable">xmlExpIsNillable</a>		(<a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)</pre>
<pre class="programlisting">int	<a href="#xmlExpMaxToken">xmlExpMaxToken</a>			(<a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> expr)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpNewAtom">xmlExpNewAtom</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a>	<a href="#xmlExpNewCtxt">xmlExpNewCtxt</a>		(int maxNodes, <br />					 <a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpNewOr">xmlExpNewOr</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> left, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> right)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpNewRange">xmlExpNewRange</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> subset, <br />					 int min, <br />					 int max)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpNewSeq">xmlExpNewSeq</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> left, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> right)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpParse">xmlExpParse</a>		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 const char * expr)</pre>
<pre class="programlisting">void	<a href="#xmlExpRef">xmlExpRef</a>			(<a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpStringDerive">xmlExpStringDerive</a>	(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlExpSubsume">xmlExpSubsume</a>			(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> sub)</pre>
<pre class="programlisting">Function type: <a href="#xmlRegExecCallbacks">xmlRegExecCallbacks</a>
void	<a href="#xmlRegExecCallbacks">xmlRegExecCallbacks</a>		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />					 void * transdata, <br />					 void * inputdata)
</pre>
<pre class="programlisting">int	<a href="#xmlRegExecErrInfo">xmlRegExecErrInfo</a>		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** string, <br />					 int * nbval, <br />					 int * nbneg, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** values, <br />					 int * terminal)</pre>
<pre class="programlisting">int	<a href="#xmlRegExecNextValues">xmlRegExecNextValues</a>		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 int * nbval, <br />					 int * nbneg, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** values, <br />					 int * terminal)</pre>
<pre class="programlisting">int	<a href="#xmlRegExecPushString">xmlRegExecPushString</a>		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlRegExecPushString2">xmlRegExecPushString2</a>		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value2, <br />					 void * data)</pre>
<pre class="programlisting">void	<a href="#xmlRegFreeExecCtxt">xmlRegFreeExecCtxt</a>		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec)</pre>
<pre class="programlisting">void	<a href="#xmlRegFreeRegexp">xmlRegFreeRegexp</a>		(<a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> regexp)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a>	<a href="#xmlRegNewExecCtxt">xmlRegNewExecCtxt</a>	(<a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp, <br />						 <a href="libxml-xmlregexp.html#xmlRegExecCallbacks">xmlRegExecCallbacks</a> callback, <br />						 void * data)</pre>
<pre class="programlisting"><a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	<a href="#xmlRegexpCompile">xmlRegexpCompile</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * regexp)</pre>
<pre class="programlisting">int	<a href="#xmlRegexpExec">xmlRegexpExec</a>			(<a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">int	<a href="#xmlRegexpIsDeterminist">xmlRegexpIsDeterminist</a>		(<a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp)</pre>
<pre class="programlisting">void	<a href="#xmlRegexpPrint">xmlRegexpPrint</a>			(FILE * output, <br />					 <a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> regexp)</pre>
<h2>Description</h2>
<h3><a name="xmlExpCtxt" id="xmlExpCtxt">Structure xmlExpCtxt</a></h3><pre class="programlisting">Structure xmlExpCtxt<br />struct _xmlExpCtxt {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlExpNode" id="xmlExpNode">Structure xmlExpNode</a></h3><pre class="programlisting">Structure xmlExpNode<br />struct _xmlExpNode {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlExpNodeType" id="xmlExpNodeType">xmlExpNodeType</a></h3><pre class="programlisting">Enum xmlExpNodeType {
    <a name="XML_EXP_EMPTY" id="XML_EXP_EMPTY">XML_EXP_EMPTY</a> = 0
    <a name="XML_EXP_FORBID" id="XML_EXP_FORBID">XML_EXP_FORBID</a> = 1
    <a name="XML_EXP_ATOM" id="XML_EXP_ATOM">XML_EXP_ATOM</a> = 2
    <a name="XML_EXP_SEQ" id="XML_EXP_SEQ">XML_EXP_SEQ</a> = 3
    <a name="XML_EXP_OR" id="XML_EXP_OR">XML_EXP_OR</a> = 4
    <a name="XML_EXP_COUNT" id="XML_EXP_COUNT">XML_EXP_COUNT</a> = 5
}
</pre><h3><a name="xmlRegExecCtxt" id="xmlRegExecCtxt">Structure xmlRegExecCtxt</a></h3><pre class="programlisting">Structure xmlRegExecCtxt<br />struct _xmlRegExecCtxt {
The content of this structure is not made public by the API.
}</pre>
      A libxml progressive regular expression evaluation context
    <h3><a name="xmlRegexp" id="xmlRegexp">Structure xmlRegexp</a></h3><pre class="programlisting">Structure xmlRegexp<br />struct _xmlRegexp {
The content of this structure is not made public by the API.
}</pre>
      A libxml regular expression, they can actually be far more complex thank the POSIX regex expressions.
    <h3><a name="xmlExpCtxtNbCons" id="xmlExpCtxtNbCons"></a>Function: xmlExpCtxtNbCons</h3><pre class="programlisting">int	xmlExpCtxtNbCons		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)<br />
</pre><p>Debugging facility provides the number of allocated nodes over lifetime</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an expression context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of nodes ever allocated or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlExpCtxtNbNodes" id="xmlExpCtxtNbNodes"></a>Function: xmlExpCtxtNbNodes</h3><pre class="programlisting">int	xmlExpCtxtNbNodes		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)<br />
</pre><p>Debugging facility provides the number of allocated nodes at a that point</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an expression context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of nodes in use or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlExpDump" id="xmlExpDump"></a>Function: xmlExpDump</h3><pre class="programlisting">void	xmlExpDump			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> expr)<br />
</pre><p>Serialize the expression as compiled to the buffer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>a buffer to receive the output</td></tr><tr><td><span class="term"><i><tt>expr</tt></i>:</span></td><td>the compiled expression</td></tr></tbody></table></div><h3><a name="xmlExpExpDerive" id="xmlExpExpDerive"></a>Function: xmlExpExpDerive</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpExpDerive		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> sub)<br />
</pre><p>Evaluates the expression resulting from @exp consuming a sub expression @sub Based on algebraic derivation and sometimes direct Brzozowski derivation it usually tatkes less than linear time and can handle expressions generating infinite languages.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expressions context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the englobing expression</td></tr><tr><td><span class="term"><i><tt>sub</tt></i>:</span></td><td>the subexpression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting expression or NULL in case of internal error, the result must be freed</td></tr></tbody></table></div><h3><a name="xmlExpFree" id="xmlExpFree"></a>Function: xmlExpFree</h3><pre class="programlisting">void	xmlExpFree			(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)<br />
</pre><p>Dereference the expression</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr></tbody></table></div><h3><a name="xmlExpFreeCtxt" id="xmlExpFreeCtxt"></a>Function: xmlExpFreeCtxt</h3><pre class="programlisting">void	xmlExpFreeCtxt			(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)<br />
</pre><p>Free an expression context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an expression context</td></tr></tbody></table></div><h3><a name="xmlExpGetLanguage" id="xmlExpGetLanguage"></a>Function: xmlExpGetLanguage</h3><pre class="programlisting">int	xmlExpGetLanguage		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** langList, <br />					 int len)<br />
</pre><p>Find all the strings used in @exp and store them in @list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr><tr><td><span class="term"><i><tt>langList</tt></i>:</span></td><td>where to store the tokens</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the allocated length of @list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of unique strings found, -1 in case of errors and -2 if there is more than @len strings</td></tr></tbody></table></div><h3><a name="xmlExpGetStart" id="xmlExpGetStart"></a>Function: xmlExpGetStart</h3><pre class="programlisting">int	xmlExpGetStart			(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** tokList, <br />					 int len)<br />
</pre><p>Find all the strings that appears at the start of the languages accepted by @exp and store them in @list. E.g. for (a, b) | c it will return the list [a, c]</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr><tr><td><span class="term"><i><tt>tokList</tt></i>:</span></td><td>where to store the tokens</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the allocated length of @list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of unique strings found, -1 in case of errors and -2 if there is more than @len strings</td></tr></tbody></table></div><h3><a name="xmlExpIsNillable" id="xmlExpIsNillable"></a>Function: xmlExpIsNillable</h3><pre class="programlisting">int	xmlExpIsNillable		(<a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)<br />
</pre><p>Finds if the expression is nillable, i.e. if it accepts the empty sequqnce</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if nillable, 0 if not and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlExpMaxToken" id="xmlExpMaxToken"></a>Function: xmlExpMaxToken</h3><pre class="programlisting">int	xmlExpMaxToken			(<a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> expr)<br />
</pre><p>Indicate the maximum number of input a expression can accept</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>expr</tt></i>:</span></td><td>a compiled expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the maximum length or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlExpNewAtom" id="xmlExpNewAtom"></a>Function: xmlExpNewAtom</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpNewAtom		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int len)<br />
</pre><p>Get the atom associated to this name from that context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the atom name</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the atom name length in byte (or -1);</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlExpNewCtxt" id="xmlExpNewCtxt"></a>Function: xmlExpNewCtxt</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a>	xmlExpNewCtxt		(int maxNodes, <br />					 <a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)<br />
</pre><p>Creates a new context for manipulating expressions</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>maxNodes</tt></i>:</span></td><td>the maximum number of nodes</td></tr><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>optional dictionary to use internally</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the context or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlExpNewOr" id="xmlExpNewOr"></a>Function: xmlExpNewOr</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpNewOr		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> left, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> right)<br />
</pre><p>Get the atom associated to the choice @left | @right Note that @left and @right are consumed in the operation, to keep an handle on them use xmlExpRef() and use xmlExpFree() to release them, this is true even in case of failure (unless ctxt == NULL).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>left</tt></i>:</span></td><td>left expression</td></tr><tr><td><span class="term"><i><tt>right</tt></i>:</span></td><td>right expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlExpNewRange" id="xmlExpNewRange"></a>Function: xmlExpNewRange</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpNewRange		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> subset, <br />					 int min, <br />					 int max)<br />
</pre><p>Get the atom associated to the range (@subset){@min, @max} Note that @subset is consumed in the operation, to keep an handle on it use xmlExpRef() and use xmlExpFree() to release it, this is true even in case of failure (unless ctxt == NULL).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>subset</tt></i>:</span></td><td>the expression to be repeated</td></tr><tr><td><span class="term"><i><tt>min</tt></i>:</span></td><td>the lower bound for the repetition</td></tr><tr><td><span class="term"><i><tt>max</tt></i>:</span></td><td>the upper bound for the repetition, -1 means infinite</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlExpNewSeq" id="xmlExpNewSeq"></a>Function: xmlExpNewSeq</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpNewSeq		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> left, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> right)<br />
</pre><p>Get the atom associated to the sequence @left , @right Note that @left and @right are consumed in the operation, to keep an handle on them use xmlExpRef() and use xmlExpFree() to release them, this is true even in case of failure (unless ctxt == NULL).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>left</tt></i>:</span></td><td>left expression</td></tr><tr><td><span class="term"><i><tt>right</tt></i>:</span></td><td>right expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlExpParse" id="xmlExpParse"></a>Function: xmlExpParse</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpParse		(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 const char * expr)<br />
</pre><p>Minimal parser for regexps, it understand the following constructs - string terminals - choice operator | - sequence operator , - subexpressions (...) - usual cardinality operators + * and ? - finite sequences { min, max } - infinite sequences { min, * } There is minimal checkings made especially no checking on strings values</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expressions context</td></tr><tr><td><span class="term"><i><tt>expr</tt></i>:</span></td><td>the 0 terminated string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new expression or NULL in case of failure</td></tr></tbody></table></div><h3><a name="xmlExpRef" id="xmlExpRef"></a>Function: xmlExpRef</h3><pre class="programlisting">void	xmlExpRef			(<a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)<br />
</pre><p>Increase the <a href="libxml-SAX.html#reference">reference</a> count of the expression</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr></tbody></table></div><h3><a name="xmlExpStringDerive" id="xmlExpStringDerive"></a>Function: xmlExpStringDerive</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpStringDerive	(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 int len)<br />
</pre><p>Do one step of Brzozowski derivation of the expression @exp with respect to the input string</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the string len in bytes if available</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting expression or NULL in case of internal error</td></tr></tbody></table></div><h3><a name="xmlExpSubsume" id="xmlExpSubsume"></a>Function: xmlExpSubsume</h3><pre class="programlisting">int	xmlExpSubsume			(<a href="libxml-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br />					 <a href="libxml-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> sub)<br />
</pre><p>Check whether @exp accepts all the languages accexpted by @sub the input being a subexpression.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expressions context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the englobing expression</td></tr><tr><td><span class="term"><i><tt>sub</tt></i>:</span></td><td>the subexpression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 if false and -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlRegExecCallbacks" id="xmlRegExecCallbacks"></a>Function type: xmlRegExecCallbacks</h3><pre class="programlisting">Function type: xmlRegExecCallbacks
void	xmlRegExecCallbacks		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * token, <br />					 void * transdata, <br />					 void * inputdata)
</pre><p>Callback function when doing a transition in the automata</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>the regular expression context</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the current token string</td></tr><tr><td><span class="term"><i><tt>transdata</tt></i>:</span></td><td>transition data</td></tr><tr><td><span class="term"><i><tt>inputdata</tt></i>:</span></td><td>input data</td></tr></tbody></table></div><br />
<h3><a name="xmlRegExecErrInfo" id="xmlRegExecErrInfo"></a>Function: xmlRegExecErrInfo</h3><pre class="programlisting">int	xmlRegExecErrInfo		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** string, <br />					 int * nbval, <br />					 int * nbneg, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** values, <br />					 int * terminal)<br />
</pre><p>Extract error informations from the regexp execution, the parameter @string will be updated with the value pushed and not accepted, the parameter @values must point to an array of @nbval string pointers on return nbval will contain the number of possible strings in that state and the @values array will be updated with them. The string values</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regexp execution context generating an error</td></tr><tr><td><span class="term"><i><tt>string</tt></i>:</span></td><td>return value for the error string</td></tr><tr><td><span class="term"><i><tt>nbval</tt></i>:</span></td><td>pointer to the number of accepted values IN/OUT</td></tr><tr><td><span class="term"><i><tt>nbneg</tt></i>:</span></td><td>return number of negative transitions</td></tr><tr><td><span class="term"><i><tt>values</tt></i>:</span></td><td>pointer to the array of acceptable values</td></tr><tr><td><span class="term"><i><tt>terminal</tt></i>:</span></td><td>return value if this was a terminal state</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>will be freed with the @exec context and don't need to be deallocated. Returns: 0 in case of success or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlRegExecNextValues" id="xmlRegExecNextValues"></a>Function: xmlRegExecNextValues</h3><pre class="programlisting">int	xmlRegExecNextValues		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 int * nbval, <br />					 int * nbneg, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** values, <br />					 int * terminal)<br />
</pre><p>Extract informations from the regexp execution, the parameter @values must point to an array of @nbval string pointers on return nbval will contain the number of possible strings in that state and the @values array will be updated with them. The string values</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regexp execution context</td></tr><tr><td><span class="term"><i><tt>nbval</tt></i>:</span></td><td>pointer to the number of accepted values IN/OUT</td></tr><tr><td><span class="term"><i><tt>nbneg</tt></i>:</span></td><td>return number of negative transitions</td></tr><tr><td><span class="term"><i><tt>values</tt></i>:</span></td><td>pointer to the array of acceptable values</td></tr><tr><td><span class="term"><i><tt>terminal</tt></i>:</span></td><td>return value if this was a terminal state</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>will be freed with the @exec context and don't need to be deallocated. Returns: 0 in case of success or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlRegExecPushString" id="xmlRegExecPushString"></a>Function: xmlRegExecPushString</h3><pre class="programlisting">int	xmlRegExecPushString		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 void * data)<br />
</pre><p>Push one input token in the execution context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regexp execution context or NULL to indicate the end</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>a string token input</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data associated to the token to reuse in callbacks</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the regexp reached a final state, 0 if non-final, and a negative value in case of error.</td></tr></tbody></table></div><h3><a name="xmlRegExecPushString2" id="xmlRegExecPushString2"></a>Function: xmlRegExecPushString2</h3><pre class="programlisting">int	xmlRegExecPushString2		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value2, <br />					 void * data)<br />
</pre><p>Push one input token in the execution context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regexp execution context or NULL to indicate the end</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the first string token input</td></tr><tr><td><span class="term"><i><tt>value2</tt></i>:</span></td><td>the second string token input</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data associated to the token to reuse in callbacks</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the regexp reached a final state, 0 if non-final, and a negative value in case of error.</td></tr></tbody></table></div><h3><a name="xmlRegFreeExecCtxt" id="xmlRegFreeExecCtxt"></a>Function: xmlRegFreeExecCtxt</h3><pre class="programlisting">void	xmlRegFreeExecCtxt		(<a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec)<br />
</pre><p>Free the structures associated to a regular expression evaulation context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regular expression evaulation context</td></tr></tbody></table></div><h3><a name="xmlRegFreeRegexp" id="xmlRegFreeRegexp"></a>Function: xmlRegFreeRegexp</h3><pre class="programlisting">void	xmlRegFreeRegexp		(<a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> regexp)<br />
</pre><p>Free a regexp</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>regexp</tt></i>:</span></td><td>the regexp</td></tr></tbody></table></div><h3><a name="xmlRegNewExecCtxt" id="xmlRegNewExecCtxt"></a>Function: xmlRegNewExecCtxt</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a>	xmlRegNewExecCtxt	(<a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp, <br />						 <a href="libxml-xmlregexp.html#xmlRegExecCallbacks">xmlRegExecCallbacks</a> callback, <br />						 void * data)<br />
</pre><p>Build a context used for progressive evaluation of a regexp.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>a precompiled regular expression</td></tr><tr><td><span class="term"><i><tt>callback</tt></i>:</span></td><td>a callback function used for handling progresses in the automata matching phase</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the context data associated to the callback in this context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new context</td></tr></tbody></table></div><h3><a name="xmlRegexpCompile" id="xmlRegexpCompile"></a>Function: xmlRegexpCompile</h3><pre class="programlisting"><a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	xmlRegexpCompile	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * regexp)<br />
</pre><p>Parses a regular expression conforming to XML Schemas Part 2 Datatype Appendix F and builds an automata suitable for testing strings against that regular expression</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>regexp</tt></i>:</span></td><td>a regular expression string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the compiled expression or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlRegexpExec" id="xmlRegexpExec"></a>Function: xmlRegexpExec</h3><pre class="programlisting">int	xmlRegexpExec			(<a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Check if the regular expression generates the value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the compiled regular expression</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the value to check against the regular expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if it matches, 0 if not and a negative value in case of error</td></tr></tbody></table></div><h3><a name="xmlRegexpIsDeterminist" id="xmlRegexpIsDeterminist"></a>Function: xmlRegexpIsDeterminist</h3><pre class="programlisting">int	xmlRegexpIsDeterminist		(<a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp)<br />
</pre><p>Check if the regular expression is determinist</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the compiled regular expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if it yes, 0 if not and a negative value in case of error</td></tr></tbody></table></div><h3><a name="xmlRegexpPrint" id="xmlRegexpPrint"></a>Function: xmlRegexpPrint</h3><pre class="programlisting">void	xmlRegexpPrint			(FILE * output, <br />					 <a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> regexp)<br />
</pre><p>Print the content of the compiled regular expression</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the file for the output debug</td></tr><tr><td><span class="term"><i><tt>regexp</tt></i>:</span></td><td>the compiled regexp</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
