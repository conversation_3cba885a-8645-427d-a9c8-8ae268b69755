<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module c14n from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module c14n from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-SAX2.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-SAX2.html">SAX2</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-catalog.html">catalog</a></th><td><a accesskey="n" href="libxml-catalog.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>the c14n modules provides a  "Canonical XML" implementation</p><h2>Table of Contents</h2><pre class="programlisting">Enum <a href="#xmlC14NMode">xmlC14NMode</a>
</pre><pre class="programlisting">int	<a href="#xmlC14NDocDumpMemory">xmlC14NDocDumpMemory</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br />					 int mode, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** inclusive_ns_prefixes, <br />					 int with_comments, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** doc_txt_ptr)</pre>
<pre class="programlisting">int	<a href="#xmlC14NDocSave">xmlC14NDocSave</a>			(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br />					 int mode, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** inclusive_ns_prefixes, <br />					 int with_comments, <br />					 const char * filename, <br />					 int compression)</pre>
<pre class="programlisting">int	<a href="#xmlC14NDocSaveTo">xmlC14NDocSaveTo</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br />					 int mode, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** inclusive_ns_prefixes, <br />					 int with_comments, <br />					 <a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf)</pre>
<pre class="programlisting">int	<a href="#xmlC14NExecute">xmlC14NExecute</a>			(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-c14n.html#xmlC14NIsVisibleCallback">xmlC14NIsVisibleCallback</a> is_visible_callback, <br />					 void * user_data, <br />					 int mode, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** inclusive_ns_prefixes, <br />					 int with_comments, <br />					 <a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf)</pre>
<pre class="programlisting">Function type: <a href="#xmlC14NIsVisibleCallback">xmlC14NIsVisibleCallback</a>
int	<a href="#xmlC14NIsVisibleCallback">xmlC14NIsVisibleCallback</a>	(void * user_data, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent)
</pre>
<h2>Description</h2>
<h3>Enum <a name="xmlC14NMode" id="xmlC14NMode">xmlC14NMode</a></h3><pre class="programlisting">Enum xmlC14NMode {
    <a name="XML_C14N_1_0" id="XML_C14N_1_0">XML_C14N_1_0</a> = 0 : Origianal C14N 1.0 spec
    <a name="XML_C14N_EXCLUSIVE_1_0" id="XML_C14N_EXCLUSIVE_1_0">XML_C14N_EXCLUSIVE_1_0</a> = 1 : Exclusive C14N 1.0 spec
    <a name="XML_C14N_1_1" id="XML_C14N_1_1">XML_C14N_1_1</a> = 2 : C14N 1.1 spec
}
</pre><h3><a name="xmlC14NDocDumpMemory" id="xmlC14NDocDumpMemory"></a>Function: xmlC14NDocDumpMemory</h3><pre class="programlisting">int	xmlC14NDocDumpMemory		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br />					 int mode, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** inclusive_ns_prefixes, <br />					 int with_comments, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** doc_txt_ptr)<br />
</pre><p>Dumps the canonized image of given XML document into memory. For details see "Canonical XML" (http://www.w3.org/TR/xml-c14n) or "Exclusive XML Canonicalization" (http://www.w3.org/TR/xml-exc-c14n)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the XML document for canonization</td></tr><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>the nodes set to be included in the canonized image or NULL if all document nodes should be included</td></tr><tr><td><span class="term"><i><tt>mode</tt></i>:</span></td><td>the c14n mode (see @xmlC14NMode)</td></tr><tr><td><span class="term"><i><tt>inclusive_ns_prefixes</tt></i>:</span></td><td>the list of inclusive namespace prefixes ended with a NULL or NULL if there is no inclusive namespaces (only for exclusive canonicalization, ignored otherwise)</td></tr><tr><td><span class="term"><i><tt>with_comments</tt></i>:</span></td><td>include comments in the result (!=0) or not (==0)</td></tr><tr><td><span class="term"><i><tt>doc_txt_ptr</tt></i>:</span></td><td>the memory pointer for allocated canonical XML text; the caller of this functions is responsible for calling xmlFree() to free allocated memory</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written on success or a negative value on fail</td></tr></tbody></table></div><h3><a name="xmlC14NDocSave" id="xmlC14NDocSave"></a>Function: xmlC14NDocSave</h3><pre class="programlisting">int	xmlC14NDocSave			(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br />					 int mode, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** inclusive_ns_prefixes, <br />					 int with_comments, <br />					 const char * filename, <br />					 int compression)<br />
</pre><p>Dumps the canonized image of given XML document into the file. For details see "Canonical XML" (http://www.w3.org/TR/xml-c14n) or "Exclusive XML Canonicalization" (http://www.w3.org/TR/xml-exc-c14n)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the XML document for canonization</td></tr><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>the nodes set to be included in the canonized image or NULL if all document nodes should be included</td></tr><tr><td><span class="term"><i><tt>mode</tt></i>:</span></td><td>the c14n mode (see @xmlC14NMode)</td></tr><tr><td><span class="term"><i><tt>inclusive_ns_prefixes</tt></i>:</span></td><td>the list of inclusive namespace prefixes ended with a NULL or NULL if there is no inclusive namespaces (only for exclusive canonicalization, ignored otherwise)</td></tr><tr><td><span class="term"><i><tt>with_comments</tt></i>:</span></td><td>include comments in the result (!=0) or not (==0)</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename to store canonical XML image</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>the compression level (zlib requred): -1 - libxml default, 0 - uncompressed, &gt;0 - compression level</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written success or a negative value on fail</td></tr></tbody></table></div><h3><a name="xmlC14NDocSaveTo" id="xmlC14NDocSaveTo"></a>Function: xmlC14NDocSaveTo</h3><pre class="programlisting">int	xmlC14NDocSaveTo		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br />					 int mode, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** inclusive_ns_prefixes, <br />					 int with_comments, <br />					 <a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf)<br />
</pre><p>Dumps the canonized image of given XML document into the provided buffer. For details see "Canonical XML" (http://www.w3.org/TR/xml-c14n) or "Exclusive XML Canonicalization" (http://www.w3.org/TR/xml-exc-c14n)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the XML document for canonization</td></tr><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>the nodes set to be included in the canonized image or NULL if all document nodes should be included</td></tr><tr><td><span class="term"><i><tt>mode</tt></i>:</span></td><td>the c14n mode (see @xmlC14NMode)</td></tr><tr><td><span class="term"><i><tt>inclusive_ns_prefixes</tt></i>:</span></td><td>the list of inclusive namespace prefixes ended with a NULL or NULL if there is no inclusive namespaces (only for exclusive canonicalization, ignored otherwise)</td></tr><tr><td><span class="term"><i><tt>with_comments</tt></i>:</span></td><td>include comments in the result (!=0) or not (==0)</td></tr><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the output buffer to store canonical XML; this buffer MUST have encoder==NULL because C14N requires UTF-8 output</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>non-negative value on success or a negative value on fail</td></tr></tbody></table></div><h3><a name="xmlC14NExecute" id="xmlC14NExecute"></a>Function: xmlC14NExecute</h3><pre class="programlisting">int	xmlC14NExecute			(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-c14n.html#xmlC14NIsVisibleCallback">xmlC14NIsVisibleCallback</a> is_visible_callback, <br />					 void * user_data, <br />					 int mode, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** inclusive_ns_prefixes, <br />					 int with_comments, <br />					 <a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf)<br />
</pre><p>Dumps the canonized image of given XML document into the provided buffer. For details see "Canonical XML" (http://www.w3.org/TR/xml-c14n) or "Exclusive XML Canonicalization" (http://www.w3.org/TR/xml-exc-c14n)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the XML document for canonization</td></tr><tr><td><span class="term"><i><tt>is_visible_callback</tt></i>:</span></td><td>the function to use to determine is node visible or not</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>the first parameter for @is_visible_callback function (in most cases, it is nodes set)</td></tr><tr><td><span class="term"><i><tt>mode</tt></i>:</span></td><td>the c14n mode (see @xmlC14NMode)</td></tr><tr><td><span class="term"><i><tt>inclusive_ns_prefixes</tt></i>:</span></td><td>the list of inclusive namespace prefixes ended with a NULL or NULL if there is no inclusive namespaces (only for exclusive canonicalization, ignored otherwise)</td></tr><tr><td><span class="term"><i><tt>with_comments</tt></i>:</span></td><td>include comments in the result (!=0) or not (==0)</td></tr><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the output buffer to store canonical XML; this buffer MUST have encoder==NULL because C14N requires UTF-8 output</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>non-negative value on success or a negative value on fail</td></tr></tbody></table></div><h3><a name="xmlC14NIsVisibleCallback" id="xmlC14NIsVisibleCallback"></a>Function type: xmlC14NIsVisibleCallback</h3><pre class="programlisting">Function type: xmlC14NIsVisibleCallback
int	xmlC14NIsVisibleCallback	(void * user_data, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent)
</pre><p>Signature for a C14N callback on visible nodes</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>user data</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the curent node</td></tr><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the node should be included</td></tr></tbody></table></div><br />
<p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
