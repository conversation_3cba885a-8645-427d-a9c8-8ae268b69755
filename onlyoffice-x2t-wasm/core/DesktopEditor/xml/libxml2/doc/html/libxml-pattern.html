<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module pattern from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module pattern from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-parserInternals.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-parserInternals.html">parserInternals</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-relaxng.html">relaxng</a></th><td><a accesskey="n" href="libxml-relaxng.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>allows to compile and test pattern expressions for nodes either in a tree or based on a parser state. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlPattern">xmlPattern</a><br />struct _xmlPattern
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Enum <a href="#xmlPatternFlags">xmlPatternFlags</a>
</pre><pre class="programlisting">Typedef <a href="libxml-pattern.html#xmlPattern">xmlPattern</a> * <a name="xmlPatternPtr" id="xmlPatternPtr">xmlPatternPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlStreamCtxt">xmlStreamCtxt</a><br />struct _xmlStreamCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-pattern.html#xmlStreamCtxt">xmlStreamCtxt</a> * <a name="xmlStreamCtxtPtr" id="xmlStreamCtxtPtr">xmlStreamCtxtPtr</a>
</pre><pre class="programlisting">void	<a href="#xmlFreePattern">xmlFreePattern</a>			(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)</pre>
<pre class="programlisting">void	<a href="#xmlFreePatternList">xmlFreePatternList</a>		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)</pre>
<pre class="programlisting">void	<a href="#xmlFreeStreamCtxt">xmlFreeStreamCtxt</a>		(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream)</pre>
<pre class="programlisting">int	<a href="#xmlPatternFromRoot">xmlPatternFromRoot</a>		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)</pre>
<pre class="programlisting"><a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a>	<a href="#xmlPatternGetStreamCtxt">xmlPatternGetStreamCtxt</a>	(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)</pre>
<pre class="programlisting">int	<a href="#xmlPatternMatch">xmlPatternMatch</a>			(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">int	<a href="#xmlPatternMaxDepth">xmlPatternMaxDepth</a>		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)</pre>
<pre class="programlisting">int	<a href="#xmlPatternMinDepth">xmlPatternMinDepth</a>		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)</pre>
<pre class="programlisting">int	<a href="#xmlPatternStreamable">xmlPatternStreamable</a>		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)</pre>
<pre class="programlisting"><a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a>	<a href="#xmlPatterncompile">xmlPatterncompile</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pattern, <br />					 <a href="libxml-dict.html#xmlDict">xmlDict</a> * dict, <br />					 int flags, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** namespaces)</pre>
<pre class="programlisting">int	<a href="#xmlStreamPop">xmlStreamPop</a>			(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream)</pre>
<pre class="programlisting">int	<a href="#xmlStreamPush">xmlStreamPush</a>			(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns)</pre>
<pre class="programlisting">int	<a href="#xmlStreamPushAttr">xmlStreamPushAttr</a>		(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns)</pre>
<pre class="programlisting">int	<a href="#xmlStreamPushNode">xmlStreamPushNode</a>		(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns, <br />					 int nodeType)</pre>
<pre class="programlisting">int	<a href="#xmlStreamWantsAnyNode">xmlStreamWantsAnyNode</a>		(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> streamCtxt)</pre>
<h2>Description</h2>
<h3><a name="xmlPattern" id="xmlPattern">Structure xmlPattern</a></h3><pre class="programlisting">Structure xmlPattern<br />struct _xmlPattern {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlPatternFlags" id="xmlPatternFlags">xmlPatternFlags</a></h3><pre class="programlisting">Enum xmlPatternFlags {
    <a name="XML_PATTERN_DEFAULT" id="XML_PATTERN_DEFAULT">XML_PATTERN_DEFAULT</a> = 0 : simple pattern match
    <a name="XML_PATTERN_XPATH" id="XML_PATTERN_XPATH">XML_PATTERN_XPATH</a> = 1 : standard XPath pattern
    <a name="XML_PATTERN_XSSEL" id="XML_PATTERN_XSSEL">XML_PATTERN_XSSEL</a> = 2 : XPath subset for schema selector
    <a name="XML_PATTERN_XSFIELD" id="XML_PATTERN_XSFIELD">XML_PATTERN_XSFIELD</a> = 4 : XPath subset for schema field
}
</pre><h3><a name="xmlStreamCtxt" id="xmlStreamCtxt">Structure xmlStreamCtxt</a></h3><pre class="programlisting">Structure xmlStreamCtxt<br />struct _xmlStreamCtxt {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlFreePattern" id="xmlFreePattern"></a>Function: xmlFreePattern</h3><pre class="programlisting">void	xmlFreePattern			(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)<br />
</pre><p>Free up the memory allocated by @comp</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>an XSLT comp</td></tr></tbody></table></div><h3><a name="xmlFreePatternList" id="xmlFreePatternList"></a>Function: xmlFreePatternList</h3><pre class="programlisting">void	xmlFreePatternList		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)<br />
</pre><p>Free up the memory allocated by all the elements of @comp</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>an XSLT comp list</td></tr></tbody></table></div><h3><a name="xmlFreeStreamCtxt" id="xmlFreeStreamCtxt"></a>Function: xmlFreeStreamCtxt</h3><pre class="programlisting">void	xmlFreeStreamCtxt		(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream)<br />
</pre><p>Free the stream context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>stream</tt></i>:</span></td><td>the stream context</td></tr></tbody></table></div><h3><a name="xmlPatternFromRoot" id="xmlPatternFromRoot"></a>Function: xmlPatternFromRoot</h3><pre class="programlisting">int	xmlPatternFromRoot		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)<br />
</pre><p>Check if the pattern must be looked at from the root.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the precompiled pattern</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if false and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlPatternGetStreamCtxt" id="xmlPatternGetStreamCtxt"></a>Function: xmlPatternGetStreamCtxt</h3><pre class="programlisting"><a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a>	xmlPatternGetStreamCtxt	(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)<br />
</pre><p>Get a streaming context for that pattern Use <a href="libxml-pattern.html#xmlFreeStreamCtxt">xmlFreeStreamCtxt</a> to free the context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the precompiled pattern</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the context or NULL in case of failure</td></tr></tbody></table></div><h3><a name="xmlPatternMatch" id="xmlPatternMatch"></a>Function: xmlPatternMatch</h3><pre class="programlisting">int	xmlPatternMatch			(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Test whether the node matches the pattern</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the precompiled pattern</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if it matches, 0 if it doesn't and -1 in case of failure</td></tr></tbody></table></div><h3><a name="xmlPatternMaxDepth" id="xmlPatternMaxDepth"></a>Function: xmlPatternMaxDepth</h3><pre class="programlisting">int	xmlPatternMaxDepth		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)<br />
</pre><p>Check the maximum depth reachable by a pattern</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the precompiled pattern</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-2 if no limit (using //), otherwise the depth, and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlPatternMinDepth" id="xmlPatternMinDepth"></a>Function: xmlPatternMinDepth</h3><pre class="programlisting">int	xmlPatternMinDepth		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)<br />
</pre><p>Check the minimum depth reachable by a pattern, 0 mean the / or . are part of the set.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the precompiled pattern</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error otherwise the depth,</td></tr></tbody></table></div><h3><a name="xmlPatternStreamable" id="xmlPatternStreamable"></a>Function: xmlPatternStreamable</h3><pre class="programlisting">int	xmlPatternStreamable		(<a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a> comp)<br />
</pre><p>Check if the pattern is streamable i.e. xmlPatternGetStreamCtxt() should work.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the precompiled pattern</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if streamable, 0 if not and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlPatterncompile" id="xmlPatterncompile"></a>Function: xmlPatterncompile</h3><pre class="programlisting"><a href="libxml-pattern.html#xmlPatternPtr">xmlPatternPtr</a>	xmlPatterncompile	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pattern, <br />					 <a href="libxml-dict.html#xmlDict">xmlDict</a> * dict, <br />					 int flags, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** namespaces)<br />
</pre><p>Compile a pattern.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>pattern</tt></i>:</span></td><td>the pattern to compile</td></tr><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>an optional dictionary for interned strings</td></tr><tr><td><span class="term"><i><tt>flags</tt></i>:</span></td><td>compilation flags, see <a href="libxml-pattern.html#xmlPatternFlags">xmlPatternFlags</a></td></tr><tr><td><span class="term"><i><tt>namespaces</tt></i>:</span></td><td>the prefix definitions, array of [URI, prefix] or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the compiled form of the pattern or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlStreamPop" id="xmlStreamPop"></a>Function: xmlStreamPop</h3><pre class="programlisting">int	xmlStreamPop			(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream)<br />
</pre><p>push one level from the stream.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>stream</tt></i>:</span></td><td>the stream context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, 0 otherwise.</td></tr></tbody></table></div><h3><a name="xmlStreamPush" id="xmlStreamPush"></a>Function: xmlStreamPush</h3><pre class="programlisting">int	xmlStreamPush			(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns)<br />
</pre><p>Push new data onto the stream. NOTE: if the call xmlPatterncompile() indicated a dictionary, then strings for name and ns will be expected to come from the dictionary. Both @name and @ns being NULL means the / i.e. the root of the document. This can also act as a reset. Otherwise the function will act as if it has been given an element-node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>stream</tt></i>:</span></td><td>the stream context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the current name</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>the namespace name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, 1 if the current state in the stream is a match and 0 otherwise.</td></tr></tbody></table></div><h3><a name="xmlStreamPushAttr" id="xmlStreamPushAttr"></a>Function: xmlStreamPushAttr</h3><pre class="programlisting">int	xmlStreamPushAttr		(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns)<br />
</pre><p>Push new <a href="libxml-SAX.html#attribute">attribute</a> data onto the stream. NOTE: if the call xmlPatterncompile() indicated a dictionary, then strings for name and ns will be expected to come from the dictionary. Both @name and @ns being NULL means the / i.e. the root of the document. This can also act as a reset. Otherwise the function will act as if it has been given an attribute-node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>stream</tt></i>:</span></td><td>the stream context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the current name</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>the namespace name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, 1 if the current state in the stream is a match and 0 otherwise.</td></tr></tbody></table></div><h3><a name="xmlStreamPushNode" id="xmlStreamPushNode"></a>Function: xmlStreamPushNode</h3><pre class="programlisting">int	xmlStreamPushNode		(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> stream, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ns, <br />					 int nodeType)<br />
</pre><p>Push new data onto the stream. NOTE: if the call xmlPatterncompile() indicated a dictionary, then strings for name and ns will be expected to come from the dictionary. Both @name and @ns being NULL means the / i.e. the root of the document. This can also act as a reset. Different from xmlStreamPush() this function can be fed with nodes of type: element-, attribute-, text-, cdata-section-, comment- and processing-instruction-node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>stream</tt></i>:</span></td><td>the stream context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the current name</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>the namespace name</td></tr><tr><td><span class="term"><i><tt>nodeType</tt></i>:</span></td><td>the type of the node being pushed</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, 1 if the current state in the stream is a match and 0 otherwise.</td></tr></tbody></table></div><h3><a name="xmlStreamWantsAnyNode" id="xmlStreamWantsAnyNode"></a>Function: xmlStreamWantsAnyNode</h3><pre class="programlisting">int	xmlStreamWantsAnyNode		(<a href="libxml-pattern.html#xmlStreamCtxtPtr">xmlStreamCtxtPtr</a> streamCtxt)<br />
</pre><p>Query if the streaming pattern additionally needs to be fed with text-, cdata-section-, comment- and processing-instruction-nodes. If the result is 0 then only element-nodes and attribute-nodes need to be pushed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>streamCtxt</tt></i>:</span></td><td>the stream context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 in case of need of nodes of the above described types, 0 otherwise. -1 on API errors.</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
