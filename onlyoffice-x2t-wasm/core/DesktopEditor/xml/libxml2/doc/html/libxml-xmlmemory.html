<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlmemory from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlmemory from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlexports.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlexports.html">xmlexports</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlmodule.html">xmlmodule</a></th><td><a accesskey="n" href="libxml-xmlmodule.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>provides interfaces for the memory allocator, including debugging capabilities. </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#DEBUG_MEMORY">DEBUG_MEMORY</a></pre><pre class="programlisting">Variable <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> <a name="xmlMalloc" id="xmlMalloc"></a>xmlMalloc

</pre><pre class="programlisting">Variable <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> <a name="xmlMallocAtomic" id="xmlMallocAtomic"></a>xmlMallocAtomic

</pre><pre class="programlisting">Variable <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> <a name="xmlMemStrdup" id="xmlMemStrdup"></a>xmlMemStrdup

</pre><pre class="programlisting">Variable <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> <a name="xmlRealloc" id="xmlRealloc"></a>xmlRealloc

</pre><pre class="programlisting">void	<a href="#xmlCleanupMemory">xmlCleanupMemory</a>		(void)</pre>
<pre class="programlisting">Function type: <a href="#xmlFreeFunc">xmlFreeFunc</a>
void	<a href="#xmlFreeFunc">xmlFreeFunc</a>			(void * mem)
</pre>
<pre class="programlisting">int	<a href="#xmlGcMemGet">xmlGcMemGet</a>			(<a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> * freeFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> * mallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> * mallocAtomicFunc, <br />					 <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> * reallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> * strdupFunc)</pre>
<pre class="programlisting">int	<a href="#xmlGcMemSetup">xmlGcMemSetup</a>			(<a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> freeFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> mallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> mallocAtomicFunc, <br />					 <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> reallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> strdupFunc)</pre>
<pre class="programlisting">int	<a href="#xmlInitMemory">xmlInitMemory</a>			(void)</pre>
<pre class="programlisting">void *	<a href="#xmlMallocAtomicLoc">xmlMallocAtomicLoc</a>		(size_t size, <br />					 const char * file, <br />					 int line)</pre>
<pre class="programlisting">Function type: <a href="#xmlMallocFunc">xmlMallocFunc</a>
void *	<a href="#xmlMallocFunc">xmlMallocFunc</a>			(size_t size)
</pre>
<pre class="programlisting">void *	<a href="#xmlMallocLoc">xmlMallocLoc</a>			(size_t size, <br />					 const char * file, <br />					 int line)</pre>
<pre class="programlisting">int	<a href="#xmlMemBlocks">xmlMemBlocks</a>			(void)</pre>
<pre class="programlisting">void	<a href="#xmlMemDisplay">xmlMemDisplay</a>			(FILE * fp)</pre>
<pre class="programlisting">void	<a href="#xmlMemDisplayLast">xmlMemDisplayLast</a>		(FILE * fp, <br />					 long nbBytes)</pre>
<pre class="programlisting">void	<a href="#xmlMemFree">xmlMemFree</a>			(void * ptr)</pre>
<pre class="programlisting">int	<a href="#xmlMemGet">xmlMemGet</a>			(<a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> * freeFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> * mallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> * reallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> * strdupFunc)</pre>
<pre class="programlisting">void *	<a href="#xmlMemMalloc">xmlMemMalloc</a>			(size_t size)</pre>
<pre class="programlisting">void *	<a href="#xmlMemRealloc">xmlMemRealloc</a>			(void * ptr, <br />					 size_t size)</pre>
<pre class="programlisting">int	<a href="#xmlMemSetup">xmlMemSetup</a>			(<a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> freeFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> mallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> reallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> strdupFunc)</pre>
<pre class="programlisting">void	<a href="#xmlMemShow">xmlMemShow</a>			(FILE * fp, <br />					 int nr)</pre>
<pre class="programlisting">char *	<a href="#xmlMemStrdupLoc">xmlMemStrdupLoc</a>			(const char * str, <br />					 const char * file, <br />					 int line)</pre>
<pre class="programlisting">int	<a href="#xmlMemUsed">xmlMemUsed</a>			(void)</pre>
<pre class="programlisting">void	<a href="#xmlMemoryDump">xmlMemoryDump</a>			(void)</pre>
<pre class="programlisting">char *	<a href="#xmlMemoryStrdup">xmlMemoryStrdup</a>			(const char * str)</pre>
<pre class="programlisting">Function type: <a href="#xmlReallocFunc">xmlReallocFunc</a>
void *	<a href="#xmlReallocFunc">xmlReallocFunc</a>			(void * mem, <br />					 size_t size)
</pre>
<pre class="programlisting">void *	<a href="#xmlReallocLoc">xmlReallocLoc</a>			(void * ptr, <br />					 size_t size, <br />					 const char * file, <br />					 int line)</pre>
<pre class="programlisting">Function type: <a href="#xmlStrdupFunc">xmlStrdupFunc</a>
char *	<a href="#xmlStrdupFunc">xmlStrdupFunc</a>			(const char * str)
</pre>
<h2>Description</h2>
<h3><a name="DEBUG_MEMORY" id="DEBUG_MEMORY"></a>Macro: DEBUG_MEMORY</h3><pre>#define DEBUG_MEMORY</pre><p><a href="libxml-xmlmemory.html#DEBUG_MEMORY">DEBUG_MEMORY</a> replaces the allocator with a collect and debug shell to the libc allocator. <a href="libxml-xmlmemory.html#DEBUG_MEMORY">DEBUG_MEMORY</a> should only be activated when debugging libxml i.e. if libxml has been configured with --with-debug-mem too. #define DEBUG_MEMORY_FREED #define <a href="libxml-xmlversion.html#DEBUG_MEMORY_LOCATION">DEBUG_MEMORY_LOCATION</a></p>
<h3><a name="xmlCleanupMemory" id="xmlCleanupMemory"></a>Function: xmlCleanupMemory</h3><pre class="programlisting">void	xmlCleanupMemory		(void)<br />
</pre><p>Free up all the memory allocated by the library for its own use. This should not be called by user level code.</p>
<h3><a name="xmlFreeFunc" id="xmlFreeFunc"></a>Function type: xmlFreeFunc</h3><pre class="programlisting">Function type: xmlFreeFunc
void	xmlFreeFunc			(void * mem)
</pre><p>Signature for a free() implementation.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>an already allocated block of memory</td></tr></tbody></table></div><br />
<h3><a name="xmlGcMemGet" id="xmlGcMemGet"></a>Function: xmlGcMemGet</h3><pre class="programlisting">int	xmlGcMemGet			(<a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> * freeFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> * mallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> * mallocAtomicFunc, <br />					 <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> * reallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> * strdupFunc)<br />
</pre><p>Provides the memory access functions set currently in use The mallocAtomicFunc is specialized for atomic block allocations (i.e. of areas useful for garbage collected memory allocators</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>freeFunc</tt></i>:</span></td><td>place to save the free() function in use</td></tr><tr><td><span class="term"><i><tt>mallocFunc</tt></i>:</span></td><td>place to save the malloc() function in use</td></tr><tr><td><span class="term"><i><tt>mallocAtomicFunc</tt></i>:</span></td><td>place to save the atomic malloc() function in use</td></tr><tr><td><span class="term"><i><tt>reallocFunc</tt></i>:</span></td><td>place to save the realloc() function in use</td></tr><tr><td><span class="term"><i><tt>strdupFunc</tt></i>:</span></td><td>place to save the strdup() function in use</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 on success</td></tr></tbody></table></div><h3><a name="xmlGcMemSetup" id="xmlGcMemSetup"></a>Function: xmlGcMemSetup</h3><pre class="programlisting">int	xmlGcMemSetup			(<a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> freeFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> mallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> mallocAtomicFunc, <br />					 <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> reallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> strdupFunc)<br />
</pre><p>Override the default memory access functions with a new set This has to be called before any other libxml routines ! The mallocAtomicFunc is specialized for atomic block allocations (i.e. of areas useful for garbage collected memory allocators Should this be blocked if there was already some allocations done ?</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>freeFunc</tt></i>:</span></td><td>the free() function to use</td></tr><tr><td><span class="term"><i><tt>mallocFunc</tt></i>:</span></td><td>the malloc() function to use</td></tr><tr><td><span class="term"><i><tt>mallocAtomicFunc</tt></i>:</span></td><td>the malloc() function to use for atomic allocations</td></tr><tr><td><span class="term"><i><tt>reallocFunc</tt></i>:</span></td><td>the realloc() function to use</td></tr><tr><td><span class="term"><i><tt>strdupFunc</tt></i>:</span></td><td>the strdup() function to use</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 on success</td></tr></tbody></table></div><h3><a name="xmlInitMemory" id="xmlInitMemory"></a>Function: xmlInitMemory</h3><pre class="programlisting">int	xmlInitMemory			(void)<br />
</pre><p>Initialize the memory layer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 on success</td></tr></tbody></table></div><h3><a name="xmlMallocAtomicLoc" id="xmlMallocAtomicLoc"></a>Function: xmlMallocAtomicLoc</h3><pre class="programlisting">void *	xmlMallocAtomicLoc		(size_t size, <br />					 const char * file, <br />					 int line)<br />
</pre><p>a malloc() equivalent, with logging of the allocation info.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>an unsigned int specifying the size in byte to allocate.</td></tr><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>the file name or NULL</td></tr><tr><td><span class="term"><i><tt>line</tt></i>:</span></td><td>the line number</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the allocated area or NULL in case of lack of memory.</td></tr></tbody></table></div><h3><a name="xmlMallocFunc" id="xmlMallocFunc"></a>Function type: xmlMallocFunc</h3><pre class="programlisting">Function type: xmlMallocFunc
void *	xmlMallocFunc			(size_t size)
</pre><p>Signature for a malloc() implementation.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size requested in bytes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the newly allocated block or NULL in case of error.</td></tr></tbody></table></div><br />
<h3><a name="xmlMallocLoc" id="xmlMallocLoc"></a>Function: xmlMallocLoc</h3><pre class="programlisting">void *	xmlMallocLoc			(size_t size, <br />					 const char * file, <br />					 int line)<br />
</pre><p>a malloc() equivalent, with logging of the allocation info.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>an int specifying the size in byte to allocate.</td></tr><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>the file name or NULL</td></tr><tr><td><span class="term"><i><tt>line</tt></i>:</span></td><td>the line number</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the allocated area or NULL in case of lack of memory.</td></tr></tbody></table></div><h3><a name="xmlMemBlocks" id="xmlMemBlocks"></a>Function: xmlMemBlocks</h3><pre class="programlisting">int	xmlMemBlocks			(void)<br />
</pre><p>Provides the number of memory areas currently allocated</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int representing the number of blocks</td></tr></tbody></table></div><h3><a name="xmlMemDisplay" id="xmlMemDisplay"></a>Function: xmlMemDisplay</h3><pre class="programlisting">void	xmlMemDisplay			(FILE * fp)<br />
</pre><p>show in-extenso the memory blocks allocated</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>fp</tt></i>:</span></td><td>a FILE descriptor used as the output file, if NULL, the result is written to the file .memorylist</td></tr></tbody></table></div><h3><a name="xmlMemDisplayLast" id="xmlMemDisplayLast"></a>Function: xmlMemDisplayLast</h3><pre class="programlisting">void	xmlMemDisplayLast		(FILE * fp, <br />					 long nbBytes)<br />
</pre><p>the last nbBytes of memory allocated and not freed, useful for dumping the memory left allocated between two places at runtime.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>fp</tt></i>:</span></td><td>a FILE descriptor used as the output file, if NULL, the result is written to the file .memorylist</td></tr><tr><td><span class="term"><i><tt>nbBytes</tt></i>:</span></td><td>the amount of memory to dump</td></tr></tbody></table></div><h3><a name="xmlMemFree" id="xmlMemFree"></a>Function: xmlMemFree</h3><pre class="programlisting">void	xmlMemFree			(void * ptr)<br />
</pre><p>a free() equivalent, with error checking.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ptr</tt></i>:</span></td><td>the memory block pointer</td></tr></tbody></table></div><h3><a name="xmlMemGet" id="xmlMemGet"></a>Function: xmlMemGet</h3><pre class="programlisting">int	xmlMemGet			(<a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> * freeFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> * mallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> * reallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> * strdupFunc)<br />
</pre><p>Provides the memory access functions set currently in use</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>freeFunc</tt></i>:</span></td><td>place to save the free() function in use</td></tr><tr><td><span class="term"><i><tt>mallocFunc</tt></i>:</span></td><td>place to save the malloc() function in use</td></tr><tr><td><span class="term"><i><tt>reallocFunc</tt></i>:</span></td><td>place to save the realloc() function in use</td></tr><tr><td><span class="term"><i><tt>strdupFunc</tt></i>:</span></td><td>place to save the strdup() function in use</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 on success</td></tr></tbody></table></div><h3><a name="xmlMemMalloc" id="xmlMemMalloc"></a>Function: xmlMemMalloc</h3><pre class="programlisting">void *	xmlMemMalloc			(size_t size)<br />
</pre><p>a malloc() equivalent, with logging of the allocation info.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>an int specifying the size in byte to allocate.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the allocated area or NULL in case of lack of memory.</td></tr></tbody></table></div><h3><a name="xmlMemRealloc" id="xmlMemRealloc"></a>Function: xmlMemRealloc</h3><pre class="programlisting">void *	xmlMemRealloc			(void * ptr, <br />					 size_t size)<br />
</pre><p>a realloc() equivalent, with logging of the allocation info.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ptr</tt></i>:</span></td><td>the initial memory block pointer</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>an int specifying the size in byte to allocate.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the allocated area or NULL in case of lack of memory.</td></tr></tbody></table></div><h3><a name="xmlMemSetup" id="xmlMemSetup"></a>Function: xmlMemSetup</h3><pre class="programlisting">int	xmlMemSetup			(<a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> freeFunc, <br />					 <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> mallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> reallocFunc, <br />					 <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> strdupFunc)<br />
</pre><p>Override the default memory access functions with a new set This has to be called before any other libxml routines ! Should this be blocked if there was already some allocations done ?</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>freeFunc</tt></i>:</span></td><td>the free() function to use</td></tr><tr><td><span class="term"><i><tt>mallocFunc</tt></i>:</span></td><td>the malloc() function to use</td></tr><tr><td><span class="term"><i><tt>reallocFunc</tt></i>:</span></td><td>the realloc() function to use</td></tr><tr><td><span class="term"><i><tt>strdupFunc</tt></i>:</span></td><td>the strdup() function to use</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 on success</td></tr></tbody></table></div><h3><a name="xmlMemShow" id="xmlMemShow"></a>Function: xmlMemShow</h3><pre class="programlisting">void	xmlMemShow			(FILE * fp, <br />					 int nr)<br />
</pre><p>show a show display of the memory allocated, and dump the @nr last allocated areas which were not freed</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>fp</tt></i>:</span></td><td>a FILE descriptor used as the output file</td></tr><tr><td><span class="term"><i><tt>nr</tt></i>:</span></td><td>number of entries to dump</td></tr></tbody></table></div><h3><a name="xmlMemStrdupLoc" id="xmlMemStrdupLoc"></a>Function: xmlMemStrdupLoc</h3><pre class="programlisting">char *	xmlMemStrdupLoc			(const char * str, <br />					 const char * file, <br />					 int line)<br />
</pre><p>a strdup() equivalent, with logging of the allocation info.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the initial string pointer</td></tr><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>the file name or NULL</td></tr><tr><td><span class="term"><i><tt>line</tt></i>:</span></td><td>the line number</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new string or NULL if allocation error occurred.</td></tr></tbody></table></div><h3><a name="xmlMemUsed" id="xmlMemUsed"></a>Function: xmlMemUsed</h3><pre class="programlisting">int	xmlMemUsed			(void)<br />
</pre><p>Provides the amount of memory currently allocated</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int representing the amount of memory allocated.</td></tr></tbody></table></div><h3><a name="xmlMemoryDump" id="xmlMemoryDump"></a>Function: xmlMemoryDump</h3><pre class="programlisting">void	xmlMemoryDump			(void)<br />
</pre><p>Dump in-extenso the memory blocks allocated to the file .memorylist</p>
<h3><a name="xmlMemoryStrdup" id="xmlMemoryStrdup"></a>Function: xmlMemoryStrdup</h3><pre class="programlisting">char *	xmlMemoryStrdup			(const char * str)<br />
</pre><p>a strdup() equivalent, with logging of the allocation info.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the initial string pointer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new string or NULL if allocation error occurred.</td></tr></tbody></table></div><h3><a name="xmlReallocFunc" id="xmlReallocFunc"></a>Function type: xmlReallocFunc</h3><pre class="programlisting">Function type: xmlReallocFunc
void *	xmlReallocFunc			(void * mem, <br />					 size_t size)
</pre><p>Signature for a realloc() implementation.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>an already allocated block of memory</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the new size requested in bytes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the newly reallocated block or NULL in case of error.</td></tr></tbody></table></div><br />
<h3><a name="xmlReallocLoc" id="xmlReallocLoc"></a>Function: xmlReallocLoc</h3><pre class="programlisting">void *	xmlReallocLoc			(void * ptr, <br />					 size_t size, <br />					 const char * file, <br />					 int line)<br />
</pre><p>a realloc() equivalent, with logging of the allocation info.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ptr</tt></i>:</span></td><td>the initial memory block pointer</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>an int specifying the size in byte to allocate.</td></tr><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>the file name or NULL</td></tr><tr><td><span class="term"><i><tt>line</tt></i>:</span></td><td>the line number</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the allocated area or NULL in case of lack of memory.</td></tr></tbody></table></div><h3><a name="xmlStrdupFunc" id="xmlStrdupFunc"></a>Function type: xmlStrdupFunc</h3><pre class="programlisting">Function type: xmlStrdupFunc
char *	xmlStrdupFunc			(const char * str)
</pre><p>Signature for an strdup() implementation.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>a zero terminated string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the copy of the string or NULL in case of error.</td></tr></tbody></table></div><br />
<p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
