<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module threads from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module threads from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-schematron.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-schematron.html">schematron</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-tree.html">tree</a></th><td><a accesskey="n" href="libxml-tree.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>set of generic threading related routines should work with pthreads, Windows native or TLS threads </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlMutex">xmlMutex</a><br />struct _xmlMutex
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-threads.html#xmlMutex">xmlMutex</a> * <a name="xmlMutexPtr" id="xmlMutexPtr">xmlMutexPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlRMutex">xmlRMutex</a><br />struct _xmlRMutex
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-threads.html#xmlRMutex">xmlRMutex</a> * <a name="xmlRMutexPtr" id="xmlRMutexPtr">xmlRMutexPtr</a>
</pre><pre class="programlisting">void	<a href="#xmlCleanupThreads">xmlCleanupThreads</a>		(void)</pre>
<pre class="programlisting">int	<a href="#xmlDllMain">xmlDllMain</a>			(void * hinstDLL, <br />					 unsigned long fdwReason, <br />					 void * lpvReserved)</pre>
<pre class="programlisting">void	<a href="#xmlFreeMutex">xmlFreeMutex</a>			(<a href="libxml-threads.html#xmlMutexPtr">xmlMutexPtr</a> tok)</pre>
<pre class="programlisting">void	<a href="#xmlFreeRMutex">xmlFreeRMutex</a>			(<a href="libxml-threads.html#xmlRMutexPtr">xmlRMutexPtr</a> tok)</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlGlobalStatePtr">xmlGlobalStatePtr</a>	<a href="#xmlGetGlobalState">xmlGetGlobalState</a>	(void)</pre>
<pre class="programlisting">int	<a href="#xmlGetThreadId">xmlGetThreadId</a>			(void)</pre>
<pre class="programlisting">void	<a href="#xmlInitThreads">xmlInitThreads</a>			(void)</pre>
<pre class="programlisting">int	<a href="#xmlIsMainThread">xmlIsMainThread</a>			(void)</pre>
<pre class="programlisting">void	<a href="#xmlLockLibrary">xmlLockLibrary</a>			(void)</pre>
<pre class="programlisting">void	<a href="#xmlMutexLock">xmlMutexLock</a>			(<a href="libxml-threads.html#xmlMutexPtr">xmlMutexPtr</a> tok)</pre>
<pre class="programlisting">void	<a href="#xmlMutexUnlock">xmlMutexUnlock</a>			(<a href="libxml-threads.html#xmlMutexPtr">xmlMutexPtr</a> tok)</pre>
<pre class="programlisting"><a href="libxml-threads.html#xmlMutexPtr">xmlMutexPtr</a>	<a href="#xmlNewMutex">xmlNewMutex</a>		(void)</pre>
<pre class="programlisting"><a href="libxml-threads.html#xmlRMutexPtr">xmlRMutexPtr</a>	<a href="#xmlNewRMutex">xmlNewRMutex</a>		(void)</pre>
<pre class="programlisting">void	<a href="#xmlRMutexLock">xmlRMutexLock</a>			(<a href="libxml-threads.html#xmlRMutexPtr">xmlRMutexPtr</a> tok)</pre>
<pre class="programlisting">void	<a href="#xmlRMutexUnlock">xmlRMutexUnlock</a>			(<a href="libxml-threads.html#xmlRMutexPtr">xmlRMutexPtr</a> tok)</pre>
<pre class="programlisting">void	<a href="#xmlUnlockLibrary">xmlUnlockLibrary</a>		(void)</pre>
<h2>Description</h2>
<h3><a name="xmlMutex" id="xmlMutex">Structure xmlMutex</a></h3><pre class="programlisting">Structure xmlMutex<br />struct _xmlMutex {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlRMutex" id="xmlRMutex">Structure xmlRMutex</a></h3><pre class="programlisting">Structure xmlRMutex<br />struct _xmlRMutex {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlCleanupThreads" id="xmlCleanupThreads"></a>Function: xmlCleanupThreads</h3><pre class="programlisting">void	xmlCleanupThreads		(void)<br />
</pre><p>xmlCleanupThreads() is used to to cleanup all the thread related data of the libxml2 library once processing has ended. WARNING: if your application is multithreaded or has plugin support calling this may crash the application if another thread or a plugin is still using libxml2. It's sometimes very hard to guess if libxml2 is in use in the application, some libraries or plugins may use it without notice. In case of doubt abstain from calling this function or do it just before calling exit() to avoid leak reports from valgrind !</p>
<h3><a name="xmlDllMain" id="xmlDllMain"></a>Function: xmlDllMain</h3><pre class="programlisting">int	xmlDllMain			(void * hinstDLL, <br />					 unsigned long fdwReason, <br />					 void * lpvReserved)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>hinstDLL</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>fdwReason</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>lpvReserved</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlFreeMutex" id="xmlFreeMutex"></a>Function: xmlFreeMutex</h3><pre class="programlisting">void	xmlFreeMutex			(<a href="libxml-threads.html#xmlMutexPtr">xmlMutexPtr</a> tok)<br />
</pre><p>xmlFreeMutex() is used to reclaim resources associated with a libxml2 token struct.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tok</tt></i>:</span></td><td>the simple mutex</td></tr></tbody></table></div><h3><a name="xmlFreeRMutex" id="xmlFreeRMutex"></a>Function: xmlFreeRMutex</h3><pre class="programlisting">void	xmlFreeRMutex			(<a href="libxml-threads.html#xmlRMutexPtr">xmlRMutexPtr</a> tok)<br />
</pre><p>xmlRFreeMutex() is used to reclaim resources associated with a reentrant mutex.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tok</tt></i>:</span></td><td>the reentrant mutex</td></tr></tbody></table></div><h3><a name="xmlGetGlobalState" id="xmlGetGlobalState"></a>Function: xmlGetGlobalState</h3><pre class="programlisting"><a href="libxml-globals.html#xmlGlobalStatePtr">xmlGlobalStatePtr</a>	xmlGetGlobalState	(void)<br />
</pre><p>xmlGetGlobalState() is called to retrieve the global state for a thread.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the thread global state or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlGetThreadId" id="xmlGetThreadId"></a>Function: xmlGetThreadId</h3><pre class="programlisting">int	xmlGetThreadId			(void)<br />
</pre><p>xmlGetThreadId() find the current thread ID number Note that this is likely to be broken on some platforms using pthreads as the specification doesn't mandate pthread_t to be an integer type</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current thread ID number</td></tr></tbody></table></div><h3><a name="xmlInitThreads" id="xmlInitThreads"></a>Function: xmlInitThreads</h3><pre class="programlisting">void	xmlInitThreads			(void)<br />
</pre><p>xmlInitThreads() is used to to initialize all the thread related data of the libxml2 library.</p>
<h3><a name="xmlIsMainThread" id="xmlIsMainThread"></a>Function: xmlIsMainThread</h3><pre class="programlisting">int	xmlIsMainThread			(void)<br />
</pre><p>xmlIsMainThread() check whether the current thread is the main thread.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the current thread is the main thread, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlLockLibrary" id="xmlLockLibrary"></a>Function: xmlLockLibrary</h3><pre class="programlisting">void	xmlLockLibrary			(void)<br />
</pre><p>xmlLockLibrary() is used to take out a re-entrant lock on the libxml2 library.</p>
<h3><a name="xmlMutexLock" id="xmlMutexLock"></a>Function: xmlMutexLock</h3><pre class="programlisting">void	xmlMutexLock			(<a href="libxml-threads.html#xmlMutexPtr">xmlMutexPtr</a> tok)<br />
</pre><p>xmlMutexLock() is used to lock a libxml2 token.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tok</tt></i>:</span></td><td>the simple mutex</td></tr></tbody></table></div><h3><a name="xmlMutexUnlock" id="xmlMutexUnlock"></a>Function: xmlMutexUnlock</h3><pre class="programlisting">void	xmlMutexUnlock			(<a href="libxml-threads.html#xmlMutexPtr">xmlMutexPtr</a> tok)<br />
</pre><p>xmlMutexUnlock() is used to unlock a libxml2 token.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tok</tt></i>:</span></td><td>the simple mutex</td></tr></tbody></table></div><h3><a name="xmlNewMutex" id="xmlNewMutex"></a>Function: xmlNewMutex</h3><pre class="programlisting"><a href="libxml-threads.html#xmlMutexPtr">xmlMutexPtr</a>	xmlNewMutex		(void)<br />
</pre><p>xmlNewMutex() is used to allocate a libxml2 token struct for use in synchronizing access to data.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new simple mutex pointer or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlNewRMutex" id="xmlNewRMutex"></a>Function: xmlNewRMutex</h3><pre class="programlisting"><a href="libxml-threads.html#xmlRMutexPtr">xmlRMutexPtr</a>	xmlNewRMutex		(void)<br />
</pre><p>xmlRNewMutex() is used to allocate a reentrant mutex for use in synchronizing access to data. token_r is a re-entrant lock and thus useful for synchronizing access to data structures that may be manipulated in a recursive fashion.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new reentrant mutex pointer or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlRMutexLock" id="xmlRMutexLock"></a>Function: xmlRMutexLock</h3><pre class="programlisting">void	xmlRMutexLock			(<a href="libxml-threads.html#xmlRMutexPtr">xmlRMutexPtr</a> tok)<br />
</pre><p>xmlRMutexLock() is used to lock a libxml2 token_r.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tok</tt></i>:</span></td><td>the reentrant mutex</td></tr></tbody></table></div><h3><a name="xmlRMutexUnlock" id="xmlRMutexUnlock"></a>Function: xmlRMutexUnlock</h3><pre class="programlisting">void	xmlRMutexUnlock			(<a href="libxml-threads.html#xmlRMutexPtr">xmlRMutexPtr</a> tok)<br />
</pre><p>xmlRMutexUnlock() is used to unlock a libxml2 token_r.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tok</tt></i>:</span></td><td>the reentrant mutex</td></tr></tbody></table></div><h3><a name="xmlUnlockLibrary" id="xmlUnlockLibrary"></a>Function: xmlUnlockLibrary</h3><pre class="programlisting">void	xmlUnlockLibrary		(void)<br />
</pre><p>xmlUnlockLibrary() is used to release a re-entrant lock on the libxml2 library.</p>
<p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
