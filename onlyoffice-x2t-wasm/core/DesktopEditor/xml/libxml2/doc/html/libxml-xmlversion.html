<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlversion from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlversion from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlunicode.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlunicode.html">xmlunicode</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlwriter.html">xmlwriter</a></th><td><a accesskey="n" href="libxml-xmlwriter.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>compile-time version informations for the XML library </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#ATTRIBUTE_UNUSED">ATTRIBUTE_UNUSED</a></pre><pre class="programlisting">#define <a href="#DEBUG_MEMORY_LOCATION">DEBUG_MEMORY_LOCATION</a></pre><pre class="programlisting">#define <a href="#LIBXML_ATTR_ALLOC_SIZE">LIBXML_ATTR_ALLOC_SIZE</a></pre><pre class="programlisting">#define <a href="#LIBXML_ATTR_FORMAT">LIBXML_ATTR_FORMAT</a></pre><pre class="programlisting">#define <a href="#LIBXML_AUTOMATA_ENABLED">LIBXML_AUTOMATA_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_C14N_ENABLED">LIBXML_C14N_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_CATALOG_ENABLED">LIBXML_CATALOG_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_DEBUG_ENABLED">LIBXML_DEBUG_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_DEBUG_RUNTIME">LIBXML_DEBUG_RUNTIME</a></pre><pre class="programlisting">#define <a href="#LIBXML_DOCB_ENABLED">LIBXML_DOCB_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_DOTTED_VERSION">LIBXML_DOTTED_VERSION</a></pre><pre class="programlisting">#define <a href="#LIBXML_EXPR_ENABLED">LIBXML_EXPR_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_FTP_ENABLED">LIBXML_FTP_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_HTML_ENABLED">LIBXML_HTML_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_HTTP_ENABLED">LIBXML_HTTP_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_ICONV_ENABLED">LIBXML_ICONV_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_ICU_ENABLED">LIBXML_ICU_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_ISO8859X_ENABLED">LIBXML_ISO8859X_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_LEGACY_ENABLED">LIBXML_LEGACY_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_LZMA_ENABLED">LIBXML_LZMA_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_MODULES_ENABLED">LIBXML_MODULES_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_MODULE_EXTENSION">LIBXML_MODULE_EXTENSION</a></pre><pre class="programlisting">#define <a href="#LIBXML_OUTPUT_ENABLED">LIBXML_OUTPUT_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_PATTERN_ENABLED">LIBXML_PATTERN_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_PUSH_ENABLED">LIBXML_PUSH_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_READER_ENABLED">LIBXML_READER_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_REGEXP_ENABLED">LIBXML_REGEXP_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_SAX1_ENABLED">LIBXML_SAX1_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_SCHEMAS_ENABLED">LIBXML_SCHEMAS_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_SCHEMATRON_ENABLED">LIBXML_SCHEMATRON_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_TEST_VERSION">LIBXML_TEST_VERSION</a></pre><pre class="programlisting">#define <a href="#LIBXML_THREAD_ALLOC_ENABLED">LIBXML_THREAD_ALLOC_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_THREAD_ENABLED">LIBXML_THREAD_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_TREE_ENABLED">LIBXML_TREE_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_UNICODE_ENABLED">LIBXML_UNICODE_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_VALID_ENABLED">LIBXML_VALID_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_VERSION">LIBXML_VERSION</a></pre><pre class="programlisting">#define <a href="#LIBXML_VERSION_EXTRA">LIBXML_VERSION_EXTRA</a></pre><pre class="programlisting">#define <a href="#LIBXML_VERSION_STRING">LIBXML_VERSION_STRING</a></pre><pre class="programlisting">#define <a href="#LIBXML_WRITER_ENABLED">LIBXML_WRITER_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_XINCLUDE_ENABLED">LIBXML_XINCLUDE_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_XPATH_ENABLED">LIBXML_XPATH_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_XPTR_ENABLED">LIBXML_XPTR_ENABLED</a></pre><pre class="programlisting">#define <a href="#LIBXML_ZLIB_ENABLED">LIBXML_ZLIB_ENABLED</a></pre><pre class="programlisting">#define <a href="#WITHOUT_TRIO">WITHOUT_TRIO</a></pre><pre class="programlisting">#define <a href="#WITH_TRIO">WITH_TRIO</a></pre><pre class="programlisting">void	<a href="#xmlCheckVersion">xmlCheckVersion</a>			(int version)</pre>
<h2>Description</h2>
<h3><a name="ATTRIBUTE_UNUSED" id="ATTRIBUTE_UNUSED"></a>Macro: ATTRIBUTE_UNUSED</h3><pre>#define ATTRIBUTE_UNUSED</pre><p>Macro used to signal to GCC unused function parameters</p>
<h3><a name="DEBUG_MEMORY_LOCATION" id="DEBUG_MEMORY_LOCATION"></a>Macro: DEBUG_MEMORY_LOCATION</h3><pre>#define DEBUG_MEMORY_LOCATION</pre><p>Whether the memory debugging is configured in</p>
<h3><a name="LIBXML_ATTR_ALLOC_SIZE" id="LIBXML_ATTR_ALLOC_SIZE"></a>Macro: LIBXML_ATTR_ALLOC_SIZE</h3><pre>#define LIBXML_ATTR_ALLOC_SIZE</pre><p>Macro used to indicate to GCC this is an allocator function</p>
<h3><a name="LIBXML_ATTR_FORMAT" id="LIBXML_ATTR_FORMAT"></a>Macro: LIBXML_ATTR_FORMAT</h3><pre>#define LIBXML_ATTR_FORMAT</pre><p>Macro used to indicate to GCC the parameter are printf like</p>
<h3><a name="LIBXML_AUTOMATA_ENABLED" id="LIBXML_AUTOMATA_ENABLED"></a>Macro: LIBXML_AUTOMATA_ENABLED</h3><pre>#define LIBXML_AUTOMATA_ENABLED</pre><p>Whether the automata interfaces are compiled in</p>
<h3><a name="LIBXML_C14N_ENABLED" id="LIBXML_C14N_ENABLED"></a>Macro: LIBXML_C14N_ENABLED</h3><pre>#define LIBXML_C14N_ENABLED</pre><p>Whether the Canonicalization support is configured in</p>
<h3><a name="LIBXML_CATALOG_ENABLED" id="LIBXML_CATALOG_ENABLED"></a>Macro: LIBXML_CATALOG_ENABLED</h3><pre>#define LIBXML_CATALOG_ENABLED</pre><p>Whether the Catalog support is configured in</p>
<h3><a name="LIBXML_DEBUG_ENABLED" id="LIBXML_DEBUG_ENABLED"></a>Macro: LIBXML_DEBUG_ENABLED</h3><pre>#define LIBXML_DEBUG_ENABLED</pre><p>Whether Debugging module is configured in</p>
<h3><a name="LIBXML_DEBUG_RUNTIME" id="LIBXML_DEBUG_RUNTIME"></a>Macro: LIBXML_DEBUG_RUNTIME</h3><pre>#define LIBXML_DEBUG_RUNTIME</pre><p>Whether the runtime debugging is configured in</p>
<h3><a name="LIBXML_DOCB_ENABLED" id="LIBXML_DOCB_ENABLED"></a>Macro: LIBXML_DOCB_ENABLED</h3><pre>#define LIBXML_DOCB_ENABLED</pre><p>Whether the SGML Docbook support is configured in</p>
<h3><a name="LIBXML_DOTTED_VERSION" id="LIBXML_DOTTED_VERSION"></a>Macro: LIBXML_DOTTED_VERSION</h3><pre>#define LIBXML_DOTTED_VERSION</pre><p>the version string like "1.2.3"</p>
<h3><a name="LIBXML_EXPR_ENABLED" id="LIBXML_EXPR_ENABLED"></a>Macro: LIBXML_EXPR_ENABLED</h3><pre>#define LIBXML_EXPR_ENABLED</pre><p>Whether the formal expressions interfaces are compiled in</p>
<h3><a name="LIBXML_FTP_ENABLED" id="LIBXML_FTP_ENABLED"></a>Macro: LIBXML_FTP_ENABLED</h3><pre>#define LIBXML_FTP_ENABLED</pre><p>Whether the FTP support is configured in</p>
<h3><a name="LIBXML_HTML_ENABLED" id="LIBXML_HTML_ENABLED"></a>Macro: LIBXML_HTML_ENABLED</h3><pre>#define LIBXML_HTML_ENABLED</pre><p>Whether the HTML support is configured in</p>
<h3><a name="LIBXML_HTTP_ENABLED" id="LIBXML_HTTP_ENABLED"></a>Macro: LIBXML_HTTP_ENABLED</h3><pre>#define LIBXML_HTTP_ENABLED</pre><p>Whether the HTTP support is configured in</p>
<h3><a name="LIBXML_ICONV_ENABLED" id="LIBXML_ICONV_ENABLED"></a>Macro: LIBXML_ICONV_ENABLED</h3><pre>#define LIBXML_ICONV_ENABLED</pre><p>Whether iconv support is available</p>
<h3><a name="LIBXML_ICU_ENABLED" id="LIBXML_ICU_ENABLED"></a>Macro: LIBXML_ICU_ENABLED</h3><pre>#define LIBXML_ICU_ENABLED</pre><p>Whether icu support is available</p>
<h3><a name="LIBXML_ISO8859X_ENABLED" id="LIBXML_ISO8859X_ENABLED"></a>Macro: LIBXML_ISO8859X_ENABLED</h3><pre>#define LIBXML_ISO8859X_ENABLED</pre><p>Whether ISO-8859-* support is made available in case iconv is not</p>
<h3><a name="LIBXML_LEGACY_ENABLED" id="LIBXML_LEGACY_ENABLED"></a>Macro: LIBXML_LEGACY_ENABLED</h3><pre>#define LIBXML_LEGACY_ENABLED</pre><p>Whether the deprecated APIs are compiled in for compatibility</p>
<h3><a name="LIBXML_LZMA_ENABLED" id="LIBXML_LZMA_ENABLED"></a>Macro: LIBXML_LZMA_ENABLED</h3><pre>#define LIBXML_LZMA_ENABLED</pre><p>Whether the Lzma support is compiled in</p>
<h3><a name="LIBXML_MODULES_ENABLED" id="LIBXML_MODULES_ENABLED"></a>Macro: LIBXML_MODULES_ENABLED</h3><pre>#define LIBXML_MODULES_ENABLED</pre><p>Whether the module interfaces are compiled in</p>
<h3><a name="LIBXML_MODULE_EXTENSION" id="LIBXML_MODULE_EXTENSION"></a>Macro: LIBXML_MODULE_EXTENSION</h3><pre>#define LIBXML_MODULE_EXTENSION</pre><p>the string suffix used by dynamic modules (usually shared libraries)</p>
<h3><a name="LIBXML_OUTPUT_ENABLED" id="LIBXML_OUTPUT_ENABLED"></a>Macro: LIBXML_OUTPUT_ENABLED</h3><pre>#define LIBXML_OUTPUT_ENABLED</pre><p>Whether the serialization/saving support is configured in</p>
<h3><a name="LIBXML_PATTERN_ENABLED" id="LIBXML_PATTERN_ENABLED"></a>Macro: LIBXML_PATTERN_ENABLED</h3><pre>#define LIBXML_PATTERN_ENABLED</pre><p>Whether the <a href="libxml-pattern.html#xmlPattern">xmlPattern</a> node selection interface is configured in</p>
<h3><a name="LIBXML_PUSH_ENABLED" id="LIBXML_PUSH_ENABLED"></a>Macro: LIBXML_PUSH_ENABLED</h3><pre>#define LIBXML_PUSH_ENABLED</pre><p>Whether the push parsing interfaces are configured in</p>
<h3><a name="LIBXML_READER_ENABLED" id="LIBXML_READER_ENABLED"></a>Macro: LIBXML_READER_ENABLED</h3><pre>#define LIBXML_READER_ENABLED</pre><p>Whether the xmlReader parsing interface is configured in</p>
<h3><a name="LIBXML_REGEXP_ENABLED" id="LIBXML_REGEXP_ENABLED"></a>Macro: LIBXML_REGEXP_ENABLED</h3><pre>#define LIBXML_REGEXP_ENABLED</pre><p>Whether the regular expressions interfaces are compiled in</p>
<h3><a name="LIBXML_SAX1_ENABLED" id="LIBXML_SAX1_ENABLED"></a>Macro: LIBXML_SAX1_ENABLED</h3><pre>#define LIBXML_SAX1_ENABLED</pre><p>Whether the older SAX1 interface is configured in</p>
<h3><a name="LIBXML_SCHEMAS_ENABLED" id="LIBXML_SCHEMAS_ENABLED"></a>Macro: LIBXML_SCHEMAS_ENABLED</h3><pre>#define LIBXML_SCHEMAS_ENABLED</pre><p>Whether the Schemas validation interfaces are compiled in</p>
<h3><a name="LIBXML_SCHEMATRON_ENABLED" id="LIBXML_SCHEMATRON_ENABLED"></a>Macro: LIBXML_SCHEMATRON_ENABLED</h3><pre>#define LIBXML_SCHEMATRON_ENABLED</pre><p>Whether the Schematron validation interfaces are compiled in</p>
<h3><a name="LIBXML_TEST_VERSION" id="LIBXML_TEST_VERSION"></a>Macro: LIBXML_TEST_VERSION</h3><pre>#define LIBXML_TEST_VERSION</pre><p>Macro to check that the libxml version in use is compatible with the version the software has been compiled against</p>
<h3><a name="LIBXML_THREAD_ALLOC_ENABLED" id="LIBXML_THREAD_ALLOC_ENABLED"></a>Macro: LIBXML_THREAD_ALLOC_ENABLED</h3><pre>#define LIBXML_THREAD_ALLOC_ENABLED</pre><p>Whether the allocation hooks are per-thread</p>
<h3><a name="LIBXML_THREAD_ENABLED" id="LIBXML_THREAD_ENABLED"></a>Macro: LIBXML_THREAD_ENABLED</h3><pre>#define LIBXML_THREAD_ENABLED</pre><p>Whether the thread support is configured in</p>
<h3><a name="LIBXML_TREE_ENABLED" id="LIBXML_TREE_ENABLED"></a>Macro: LIBXML_TREE_ENABLED</h3><pre>#define LIBXML_TREE_ENABLED</pre><p>Whether the DOM like tree manipulation API support is configured in</p>
<h3><a name="LIBXML_UNICODE_ENABLED" id="LIBXML_UNICODE_ENABLED"></a>Macro: LIBXML_UNICODE_ENABLED</h3><pre>#define LIBXML_UNICODE_ENABLED</pre><p>Whether the Unicode related interfaces are compiled in</p>
<h3><a name="LIBXML_VALID_ENABLED" id="LIBXML_VALID_ENABLED"></a>Macro: LIBXML_VALID_ENABLED</h3><pre>#define LIBXML_VALID_ENABLED</pre><p>Whether the DTD validation support is configured in</p>
<h3><a name="LIBXML_VERSION" id="LIBXML_VERSION"></a>Macro: LIBXML_VERSION</h3><pre>#define LIBXML_VERSION</pre><p>the version number: 1.2.3 value is 10203</p>
<h3><a name="LIBXML_VERSION_EXTRA" id="LIBXML_VERSION_EXTRA"></a>Macro: LIBXML_VERSION_EXTRA</h3><pre>#define LIBXML_VERSION_EXTRA</pre><p>extra version information, used to show a CVS compilation</p>
<h3><a name="LIBXML_VERSION_STRING" id="LIBXML_VERSION_STRING"></a>Macro: LIBXML_VERSION_STRING</h3><pre>#define LIBXML_VERSION_STRING</pre><p>the version number string, 1.2.3 value is "10203"</p>
<h3><a name="LIBXML_WRITER_ENABLED" id="LIBXML_WRITER_ENABLED"></a>Macro: LIBXML_WRITER_ENABLED</h3><pre>#define LIBXML_WRITER_ENABLED</pre><p>Whether the xmlWriter saving interface is configured in</p>
<h3><a name="LIBXML_XINCLUDE_ENABLED" id="LIBXML_XINCLUDE_ENABLED"></a>Macro: LIBXML_XINCLUDE_ENABLED</h3><pre>#define LIBXML_XINCLUDE_ENABLED</pre><p>Whether XInclude is configured in</p>
<h3><a name="LIBXML_XPATH_ENABLED" id="LIBXML_XPATH_ENABLED"></a>Macro: LIBXML_XPATH_ENABLED</h3><pre>#define LIBXML_XPATH_ENABLED</pre><p>Whether XPath is configured in</p>
<h3><a name="LIBXML_XPTR_ENABLED" id="LIBXML_XPTR_ENABLED"></a>Macro: LIBXML_XPTR_ENABLED</h3><pre>#define LIBXML_XPTR_ENABLED</pre><p>Whether XPointer is configured in</p>
<h3><a name="LIBXML_ZLIB_ENABLED" id="LIBXML_ZLIB_ENABLED"></a>Macro: LIBXML_ZLIB_ENABLED</h3><pre>#define LIBXML_ZLIB_ENABLED</pre><p>Whether the Zlib support is compiled in</p>
<h3><a name="WITHOUT_TRIO" id="WITHOUT_TRIO"></a>Macro: WITHOUT_TRIO</h3><pre>#define WITHOUT_TRIO</pre><p>defined if the trio support should not be configured in</p>
<h3><a name="WITH_TRIO" id="WITH_TRIO"></a>Macro: WITH_TRIO</h3><pre>#define WITH_TRIO</pre><p>defined if the trio support need to be configured in</p>
<h3><a name="xmlCheckVersion" id="xmlCheckVersion"></a>Function: xmlCheckVersion</h3><pre class="programlisting">void	xmlCheckVersion			(int version)<br />
</pre><p>check the compiled lib version against the include one. This can warn or immediately kill the application</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>version</tt></i>:</span></td><td>the include version number</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
