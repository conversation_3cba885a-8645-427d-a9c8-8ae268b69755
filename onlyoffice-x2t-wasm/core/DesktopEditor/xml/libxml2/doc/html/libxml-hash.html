<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module hash from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module hash from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-globals.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-globals.html">globals</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-list.html">list</a></th><td><a accesskey="n" href="libxml-list.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>This module implements the hash table support used in various places in the library. </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#XML_CAST_FPTR">XML_CAST_FPTR</a></pre><pre class="programlisting">Structure <a href="#xmlHashTable">xmlHashTable</a><br />struct _xmlHashTable
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-hash.html#xmlHashTable">xmlHashTable</a> * <a name="xmlHashTablePtr" id="xmlHashTablePtr">xmlHashTablePtr</a>
</pre><pre class="programlisting">int	<a href="#xmlHashAddEntry">xmlHashAddEntry</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 void * userdata)</pre>
<pre class="programlisting">int	<a href="#xmlHashAddEntry2">xmlHashAddEntry2</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 void * userdata)</pre>
<pre class="programlisting">int	<a href="#xmlHashAddEntry3">xmlHashAddEntry3</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 void * userdata)</pre>
<pre class="programlisting">Function type: <a href="#xmlHashCopier">xmlHashCopier</a>
void *	<a href="#xmlHashCopier">xmlHashCopier</a>			(void * payload, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre>
<pre class="programlisting"><a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	<a href="#xmlHashCopy">xmlHashCopy</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 <a href="libxml-hash.html#xmlHashCopier">xmlHashCopier</a> f)</pre>
<pre class="programlisting"><a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	<a href="#xmlHashCreate">xmlHashCreate</a>		(int size)</pre>
<pre class="programlisting"><a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	<a href="#xmlHashCreateDict">xmlHashCreateDict</a>	(int size, <br />					 <a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)</pre>
<pre class="programlisting">Function type: <a href="#xmlHashDeallocator">xmlHashDeallocator</a>
void	<a href="#xmlHashDeallocator">xmlHashDeallocator</a>		(void * payload, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre>
<pre class="programlisting">void	<a href="#xmlHashFree">xmlHashFree</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)</pre>
<pre class="programlisting">void *	<a href="#xmlHashLookup">xmlHashLookup</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void *	<a href="#xmlHashLookup2">xmlHashLookup2</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2)</pre>
<pre class="programlisting">void *	<a href="#xmlHashLookup3">xmlHashLookup3</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3)</pre>
<pre class="programlisting">void *	<a href="#xmlHashQLookup">xmlHashQLookup</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void *	<a href="#xmlHashQLookup2">xmlHashQLookup2</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2)</pre>
<pre class="programlisting">void *	<a href="#xmlHashQLookup3">xmlHashQLookup3</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix3, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3)</pre>
<pre class="programlisting">int	<a href="#xmlHashRemoveEntry">xmlHashRemoveEntry</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)</pre>
<pre class="programlisting">int	<a href="#xmlHashRemoveEntry2">xmlHashRemoveEntry2</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)</pre>
<pre class="programlisting">int	<a href="#xmlHashRemoveEntry3">xmlHashRemoveEntry3</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)</pre>
<pre class="programlisting">void	<a href="#xmlHashScan">xmlHashScan</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 <a href="libxml-hash.html#xmlHashScanner">xmlHashScanner</a> f, <br />					 void * data)</pre>
<pre class="programlisting">void	<a href="#xmlHashScan3">xmlHashScan3</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 <a href="libxml-hash.html#xmlHashScanner">xmlHashScanner</a> f, <br />					 void * data)</pre>
<pre class="programlisting">void	<a href="#xmlHashScanFull">xmlHashScanFull</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 <a href="libxml-hash.html#xmlHashScannerFull">xmlHashScannerFull</a> f, <br />					 void * data)</pre>
<pre class="programlisting">void	<a href="#xmlHashScanFull3">xmlHashScanFull3</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 <a href="libxml-hash.html#xmlHashScannerFull">xmlHashScannerFull</a> f, <br />					 void * data)</pre>
<pre class="programlisting">Function type: <a href="#xmlHashScanner">xmlHashScanner</a>
void	<a href="#xmlHashScanner">xmlHashScanner</a>			(void * payload, <br />					 void * data, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre>
<pre class="programlisting">Function type: <a href="#xmlHashScannerFull">xmlHashScannerFull</a>
void	<a href="#xmlHashScannerFull">xmlHashScannerFull</a>		(void * payload, <br />					 void * data, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3)
</pre>
<pre class="programlisting">int	<a href="#xmlHashSize">xmlHashSize</a>			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table)</pre>
<pre class="programlisting">int	<a href="#xmlHashUpdateEntry">xmlHashUpdateEntry</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 void * userdata, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)</pre>
<pre class="programlisting">int	<a href="#xmlHashUpdateEntry2">xmlHashUpdateEntry2</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 void * userdata, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)</pre>
<pre class="programlisting">int	<a href="#xmlHashUpdateEntry3">xmlHashUpdateEntry3</a>		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 void * userdata, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)</pre>
<h2>Description</h2>
<h3><a name="XML_CAST_FPTR" id="XML_CAST_FPTR"></a>Macro: XML_CAST_FPTR</h3><pre>#define XML_CAST_FPTR</pre><p>Macro to do a casting from an object pointer to a function pointer without encountering a warning from gcc #define XML_CAST_FPTR(fptr) (*(void **)(&amp;fptr)) This macro violated ISO C aliasing rules (gcc4 on s390 broke) so it is disabled now</p>
<h3><a name="xmlHashTable" id="xmlHashTable">Structure xmlHashTable</a></h3><pre class="programlisting">Structure xmlHashTable<br />struct _xmlHashTable {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlHashAddEntry" id="xmlHashAddEntry"></a>Function: xmlHashAddEntry</h3><pre class="programlisting">int	xmlHashAddEntry			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 void * userdata)<br />
</pre><p>Add the @userdata to the hash @table. This can later be retrieved by using the @name. Duplicate names generate errors.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>userdata</tt></i>:</span></td><td>a pointer to the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 the addition succeeded and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlHashAddEntry2" id="xmlHashAddEntry2"></a>Function: xmlHashAddEntry2</h3><pre class="programlisting">int	xmlHashAddEntry2		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 void * userdata)<br />
</pre><p>Add the @userdata to the hash @table. This can later be retrieved by using the (@name, @name2) tuple. Duplicate tuples generate errors.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>userdata</tt></i>:</span></td><td>a pointer to the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 the addition succeeded and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlHashAddEntry3" id="xmlHashAddEntry3"></a>Function: xmlHashAddEntry3</h3><pre class="programlisting">int	xmlHashAddEntry3		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 void * userdata)<br />
</pre><p>Add the @userdata to the hash @table. This can later be retrieved by using the tuple (@name, @name2, @name3). Duplicate entries generate errors.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>name3</tt></i>:</span></td><td>a third name of the userdata</td></tr><tr><td><span class="term"><i><tt>userdata</tt></i>:</span></td><td>a pointer to the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 the addition succeeded and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlHashCopier" id="xmlHashCopier"></a>Function type: xmlHashCopier</h3><pre class="programlisting">Function type: xmlHashCopier
void *	xmlHashCopier			(void * payload, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre><p>Callback to copy data from a hash.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>payload</tt></i>:</span></td><td>the data in the hash</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name associated</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a copy of the data or NULL in case of error.</td></tr></tbody></table></div><br />
<h3><a name="xmlHashCopy" id="xmlHashCopy"></a>Function: xmlHashCopy</h3><pre class="programlisting"><a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	xmlHashCopy		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 <a href="libxml-hash.html#xmlHashCopier">xmlHashCopier</a> f)<br />
</pre><p>Scan the hash @table and applied @f to each value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the copier function for items in the hash</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new table or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlHashCreate" id="xmlHashCreate"></a>Function: xmlHashCreate</h3><pre class="programlisting"><a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	xmlHashCreate		(int size)<br />
</pre><p>Create a new xmlHashTablePtr.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the hash table</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object, or NULL if an error occured.</td></tr></tbody></table></div><h3><a name="xmlHashCreateDict" id="xmlHashCreateDict"></a>Function: xmlHashCreateDict</h3><pre class="programlisting"><a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	xmlHashCreateDict	(int size, <br />					 <a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)<br />
</pre><p>Create a new <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> which will use @dict as the internal dictionary</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the hash table</td></tr><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>a dictionary to use for the hash</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object, or NULL if an error occured.</td></tr></tbody></table></div><h3><a name="xmlHashDeallocator" id="xmlHashDeallocator"></a>Function type: xmlHashDeallocator</h3><pre class="programlisting">Function type: xmlHashDeallocator
void	xmlHashDeallocator		(void * payload, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre><p>Callback to free data from a hash.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>payload</tt></i>:</span></td><td>the data in the hash</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name associated</td></tr></tbody></table></div><br />
<h3><a name="xmlHashFree" id="xmlHashFree"></a>Function: xmlHashFree</h3><pre class="programlisting">void	xmlHashFree			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)<br />
</pre><p>Free the hash @table and its contents. The userdata is deallocated with @f if provided.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the deallocator function for items in the hash</td></tr></tbody></table></div><h3><a name="xmlHashLookup" id="xmlHashLookup"></a>Function: xmlHashLookup</h3><pre class="programlisting">void *	xmlHashLookup			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Find the userdata specified by the @name.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the pointer to the userdata</td></tr></tbody></table></div><h3><a name="xmlHashLookup2" id="xmlHashLookup2"></a>Function: xmlHashLookup2</h3><pre class="programlisting">void *	xmlHashLookup2			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2)<br />
</pre><p>Find the userdata specified by the (@name, @name2) tuple.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the pointer to the userdata</td></tr></tbody></table></div><h3><a name="xmlHashLookup3" id="xmlHashLookup3"></a>Function: xmlHashLookup3</h3><pre class="programlisting">void *	xmlHashLookup3			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3)<br />
</pre><p>Find the userdata specified by the (@name, @name2, @name3) tuple.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>name3</tt></i>:</span></td><td>a third name of the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the a pointer to the userdata</td></tr></tbody></table></div><h3><a name="xmlHashQLookup" id="xmlHashQLookup"></a>Function: xmlHashQLookup</h3><pre class="programlisting">void *	xmlHashQLookup			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Find the userdata specified by the QName @prefix:@name/@name.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the prefix of the userdata</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the pointer to the userdata</td></tr></tbody></table></div><h3><a name="xmlHashQLookup2" id="xmlHashQLookup2"></a>Function: xmlHashQLookup2</h3><pre class="programlisting">void *	xmlHashQLookup2			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2)<br />
</pre><p>Find the userdata specified by the QNames tuple</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the prefix of the userdata</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>prefix2</tt></i>:</span></td><td>the second prefix of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the pointer to the userdata</td></tr></tbody></table></div><h3><a name="xmlHashQLookup3" id="xmlHashQLookup3"></a>Function: xmlHashQLookup3</h3><pre class="programlisting">void *	xmlHashQLookup3			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix3, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3)<br />
</pre><p>Find the userdata specified by the (@name, @name2, @name3) tuple.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the prefix of the userdata</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>prefix2</tt></i>:</span></td><td>the second prefix of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>prefix3</tt></i>:</span></td><td>the third prefix of the userdata</td></tr><tr><td><span class="term"><i><tt>name3</tt></i>:</span></td><td>a third name of the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the a pointer to the userdata</td></tr></tbody></table></div><h3><a name="xmlHashRemoveEntry" id="xmlHashRemoveEntry"></a>Function: xmlHashRemoveEntry</h3><pre class="programlisting">int	xmlHashRemoveEntry		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)<br />
</pre><p>Find the userdata specified by the @name and remove it from the hash @table. Existing userdata for this tuple will be removed and freed with @f.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the deallocator function for removed item (if any)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the removal succeeded and -1 in case of error or not found.</td></tr></tbody></table></div><h3><a name="xmlHashRemoveEntry2" id="xmlHashRemoveEntry2"></a>Function: xmlHashRemoveEntry2</h3><pre class="programlisting">int	xmlHashRemoveEntry2		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)<br />
</pre><p>Find the userdata specified by the (@name, @name2) tuple and remove it from the hash @table. Existing userdata for this tuple will be removed and freed with @f.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the deallocator function for removed item (if any)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the removal succeeded and -1 in case of error or not found.</td></tr></tbody></table></div><h3><a name="xmlHashRemoveEntry3" id="xmlHashRemoveEntry3"></a>Function: xmlHashRemoveEntry3</h3><pre class="programlisting">int	xmlHashRemoveEntry3		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)<br />
</pre><p>Find the userdata specified by the (@name, @name2, @name3) tuple and remove it from the hash @table. Existing userdata for this tuple will be removed and freed with @f.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>name3</tt></i>:</span></td><td>a third name of the userdata</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the deallocator function for removed item (if any)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the removal succeeded and -1 in case of error or not found.</td></tr></tbody></table></div><h3><a name="xmlHashScan" id="xmlHashScan"></a>Function: xmlHashScan</h3><pre class="programlisting">void	xmlHashScan			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 <a href="libxml-hash.html#xmlHashScanner">xmlHashScanner</a> f, <br />					 void * data)<br />
</pre><p>Scan the hash @table and applied @f to each value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the scanner function for items in the hash</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>extra data passed to f</td></tr></tbody></table></div><h3><a name="xmlHashScan3" id="xmlHashScan3"></a>Function: xmlHashScan3</h3><pre class="programlisting">void	xmlHashScan3			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 <a href="libxml-hash.html#xmlHashScanner">xmlHashScanner</a> f, <br />					 void * data)<br />
</pre><p>Scan the hash @table and applied @f to each value matching (@name, @name2, @name3) tuple. If one of the names is null, the comparison is considered to match.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata or NULL</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata or NULL</td></tr><tr><td><span class="term"><i><tt>name3</tt></i>:</span></td><td>a third name of the userdata or NULL</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the scanner function for items in the hash</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>extra data passed to f</td></tr></tbody></table></div><h3><a name="xmlHashScanFull" id="xmlHashScanFull"></a>Function: xmlHashScanFull</h3><pre class="programlisting">void	xmlHashScanFull			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 <a href="libxml-hash.html#xmlHashScannerFull">xmlHashScannerFull</a> f, <br />					 void * data)<br />
</pre><p>Scan the hash @table and applied @f to each value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the scanner function for items in the hash</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>extra data passed to f</td></tr></tbody></table></div><h3><a name="xmlHashScanFull3" id="xmlHashScanFull3"></a>Function: xmlHashScanFull3</h3><pre class="programlisting">void	xmlHashScanFull3		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 <a href="libxml-hash.html#xmlHashScannerFull">xmlHashScannerFull</a> f, <br />					 void * data)<br />
</pre><p>Scan the hash @table and applied @f to each value matching (@name, @name2, @name3) tuple. If one of the names is null, the comparison is considered to match.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata or NULL</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata or NULL</td></tr><tr><td><span class="term"><i><tt>name3</tt></i>:</span></td><td>a third name of the userdata or NULL</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the scanner function for items in the hash</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>extra data passed to f</td></tr></tbody></table></div><h3><a name="xmlHashScanner" id="xmlHashScanner"></a>Function type: xmlHashScanner</h3><pre class="programlisting">Function type: xmlHashScanner
void	xmlHashScanner			(void * payload, <br />					 void * data, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre><p>Callback when scanning data in a hash with the simple scanner.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>payload</tt></i>:</span></td><td>the data in the hash</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>extra scannner data</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name associated</td></tr></tbody></table></div><br />
<h3><a name="xmlHashScannerFull" id="xmlHashScannerFull"></a>Function type: xmlHashScannerFull</h3><pre class="programlisting">Function type: xmlHashScannerFull
void	xmlHashScannerFull		(void * payload, <br />					 void * data, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3)
</pre><p>Callback when scanning data in a hash with the full scanner.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>payload</tt></i>:</span></td><td>the data in the hash</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>extra scannner data</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name associated</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>the second name associated</td></tr><tr><td><span class="term"><i><tt>name3</tt></i>:</span></td><td>the third name associated</td></tr></tbody></table></div><br />
<h3><a name="xmlHashSize" id="xmlHashSize"></a>Function: xmlHashSize</h3><pre class="programlisting">int	xmlHashSize			(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table)<br />
</pre><p>Query the number of elements installed in the hash @table.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of elements in the hash table or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlHashUpdateEntry" id="xmlHashUpdateEntry"></a>Function: xmlHashUpdateEntry</h3><pre class="programlisting">int	xmlHashUpdateEntry		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 void * userdata, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)<br />
</pre><p>Add the @userdata to the hash @table. This can later be retrieved by using the @name. Existing entry for this @name will be removed and freed with @f if found.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>userdata</tt></i>:</span></td><td>a pointer to the userdata</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the deallocator function for replaced item (if any)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 the addition succeeded and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlHashUpdateEntry2" id="xmlHashUpdateEntry2"></a>Function: xmlHashUpdateEntry2</h3><pre class="programlisting">int	xmlHashUpdateEntry2		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 void * userdata, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)<br />
</pre><p>Add the @userdata to the hash @table. This can later be retrieved by using the (@name, @name2) tuple. Existing entry for this tuple will be removed and freed with @f if found.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>userdata</tt></i>:</span></td><td>a pointer to the userdata</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the deallocator function for replaced item (if any)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 the addition succeeded and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlHashUpdateEntry3" id="xmlHashUpdateEntry3"></a>Function: xmlHashUpdateEntry3</h3><pre class="programlisting">int	xmlHashUpdateEntry3		(<a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a> table, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name2, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name3, <br />					 void * userdata, <br />					 <a href="libxml-hash.html#xmlHashDeallocator">xmlHashDeallocator</a> f)<br />
</pre><p>Add the @userdata to the hash @table. This can later be retrieved by using the tuple (@name, @name2, @name3). Existing entry for this tuple will be removed and freed with @f if found.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>the hash table</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>name2</tt></i>:</span></td><td>a second name of the userdata</td></tr><tr><td><span class="term"><i><tt>name3</tt></i>:</span></td><td>a third name of the userdata</td></tr><tr><td><span class="term"><i><tt>userdata</tt></i>:</span></td><td>a pointer to the userdata</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the deallocator function for replaced item (if any)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 the addition succeeded and -1 in case of error.</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
