<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlstring from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlstring from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlschemastypes.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlschemastypes.html">xmlschemastypes</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlunicode.html">xmlunicode</a></th><td><a accesskey="n" href="libxml-xmlunicode.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>type and interfaces needed for the internal string handling of the library, especially UTF8 processing. </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#BAD_CAST">BAD_CAST</a></pre><pre class="programlisting">Typedef unsigned char <a name="xmlChar" id="xmlChar">xmlChar</a>
</pre><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCharStrdup">xmlCharStrdup</a>		(const char * cur)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCharStrndup">xmlCharStrndup</a>		(const char * cur, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlCheckUTF8">xmlCheckUTF8</a>			(const unsigned char * utf)</pre>
<pre class="programlisting">int	<a href="#xmlGetUTF8Char">xmlGetUTF8Char</a>			(const unsigned char * utf, <br />					 int * len)</pre>
<pre class="programlisting">int	<a href="#xmlStrEqual">xmlStrEqual</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2)</pre>
<pre class="programlisting">int	<a href="#xmlStrPrintf">xmlStrPrintf</a>			(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * buf, <br />					 int len, <br />					 const char * msg, <br />					 ... ...)</pre>
<pre class="programlisting">int	<a href="#xmlStrQEqual">xmlStrQEqual</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pref, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting">int	<a href="#xmlStrVPrintf">xmlStrVPrintf</a>			(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * buf, <br />					 int len, <br />					 const char * msg, <br />					 va_list ap)</pre>
<pre class="programlisting">int	<a href="#xmlStrcasecmp">xmlStrcasecmp</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrcasestr">xmlStrcasestr</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * val)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrcat">xmlStrcat</a>		(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * add)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrchr">xmlStrchr</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> val)</pre>
<pre class="programlisting">int	<a href="#xmlStrcmp">xmlStrcmp</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrdup">xmlStrdup</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur)</pre>
<pre class="programlisting">int	<a href="#xmlStrlen">xmlStrlen</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting">int	<a href="#xmlStrncasecmp">xmlStrncasecmp</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrncat">xmlStrncat</a>		(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * add, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrncatNew">xmlStrncatNew</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlStrncmp">xmlStrncmp</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrndup">xmlStrndup</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 int len)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrstr">xmlStrstr</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * val)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStrsub">xmlStrsub</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 int start, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlUTF8Charcmp">xmlUTF8Charcmp</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf2)</pre>
<pre class="programlisting">int	<a href="#xmlUTF8Size">xmlUTF8Size</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf)</pre>
<pre class="programlisting">int	<a href="#xmlUTF8Strlen">xmlUTF8Strlen</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf)</pre>
<pre class="programlisting">int	<a href="#xmlUTF8Strloc">xmlUTF8Strloc</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utfchar)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlUTF8Strndup">xmlUTF8Strndup</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 int len)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlUTF8Strpos">xmlUTF8Strpos</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 int pos)</pre>
<pre class="programlisting">int	<a href="#xmlUTF8Strsize">xmlUTF8Strsize</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlUTF8Strsub">xmlUTF8Strsub</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 int start, <br />					 int len)</pre>
<h2>Description</h2>
<h3><a name="BAD_CAST" id="BAD_CAST"></a>Macro: BAD_CAST</h3><pre>#define BAD_CAST</pre><p>Macro to cast a string to an <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * when one know its safe.</p>

      This is a basic byte in an UTF-8 encoded string. It's unsigned allowing to pinpoint case where char * are assigned to xmlChar * (possibly making serialization back impossible).
    <h3><a name="xmlCharStrdup" id="xmlCharStrdup"></a>Function: xmlCharStrdup</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCharStrdup		(const char * cur)<br />
</pre><p>a strdup for char's to xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the input char *</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * or NULL</td></tr></tbody></table></div><h3><a name="xmlCharStrndup" id="xmlCharStrndup"></a>Function: xmlCharStrndup</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCharStrndup		(const char * cur, <br />					 int len)<br />
</pre><p>a strndup for char's to xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the input char *</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the len of @cur</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * or NULL</td></tr></tbody></table></div><h3><a name="xmlCheckUTF8" id="xmlCheckUTF8"></a>Function: xmlCheckUTF8</h3><pre class="programlisting">int	xmlCheckUTF8			(const unsigned char * utf)<br />
</pre><p>Checks @utf for being valid UTF-8. @utf is assumed to be null-terminated. This function is not super-strict, as it will allow longer UTF-8 sequences than necessary. Note that Java is capable of producing these sequences if provoked. Also note, this routine checks for the 4-byte maximum size, but does not check for 0x10ffff maximum value.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>Pointer to putative UTF-8 encoded string.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>value: true if @utf is valid.</td></tr></tbody></table></div><h3><a name="xmlGetUTF8Char" id="xmlGetUTF8Char"></a>Function: xmlGetUTF8Char</h3><pre class="programlisting">int	xmlGetUTF8Char			(const unsigned char * utf, <br />					 int * len)<br />
</pre><p>Read the first UTF8 character from @utf</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>a sequence of UTF-8 encoded bytes</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>a pointer to the minimum number of bytes present in the sequence. This is used to assure the next character is completely contained within the sequence.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the char value or -1 in case of error, and sets *len to the actual number of bytes consumed (0 in case of error)</td></tr></tbody></table></div><h3><a name="xmlStrEqual" id="xmlStrEqual"></a>Function: xmlStrEqual</h3><pre class="programlisting">int	xmlStrEqual			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2)<br />
</pre><p>Check if both strings are equal of have same content. Should be a bit more readable and faster than xmlStrcmp()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str1</tt></i>:</span></td><td>the first <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>str2</tt></i>:</span></td><td>the second <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if they are equal, 0 if they are different</td></tr></tbody></table></div><h3><a name="xmlStrPrintf" id="xmlStrPrintf"></a>Function: xmlStrPrintf</h3><pre class="programlisting">int	xmlStrPrintf			(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * buf, <br />					 int len, <br />					 const char * msg, <br />					 ... ...)<br />
</pre><p>Formats @msg and places result into @buf.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the result buffer.</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the result buffer length.</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message with printf formatting.</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the message.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of <a href="libxml-SAX.html#characters">characters</a> written to @buf or -1 if an error occurs.</td></tr></tbody></table></div><h3><a name="xmlStrQEqual" id="xmlStrQEqual"></a>Function: xmlStrQEqual</h3><pre class="programlisting">int	xmlStrQEqual			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pref, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>Check if a QName is Equal to a given string</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>pref</tt></i>:</span></td><td>the prefix of the QName</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the localname of the QName</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the second <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if they are equal, 0 if they are different</td></tr></tbody></table></div><h3><a name="xmlStrVPrintf" id="xmlStrVPrintf"></a>Function: xmlStrVPrintf</h3><pre class="programlisting">int	xmlStrVPrintf			(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * buf, <br />					 int len, <br />					 const char * msg, <br />					 va_list ap)<br />
</pre><p>Formats @msg and places result into @buf.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the result buffer.</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the result buffer length.</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message with printf formatting.</td></tr><tr><td><span class="term"><i><tt>ap</tt></i>:</span></td><td>extra parameters for the message.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of <a href="libxml-SAX.html#characters">characters</a> written to @buf or -1 if an error occurs.</td></tr></tbody></table></div><h3><a name="xmlStrcasecmp" id="xmlStrcasecmp"></a>Function: xmlStrcasecmp</h3><pre class="programlisting">int	xmlStrcasecmp			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2)<br />
</pre><p>a strcasecmp for xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str1</tt></i>:</span></td><td>the first <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>str2</tt></i>:</span></td><td>the second <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the integer result of the comparison</td></tr></tbody></table></div><h3><a name="xmlStrcasestr" id="xmlStrcasestr"></a>Function: xmlStrcasestr</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrcasestr		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * val)<br />
</pre><p>a case-ignoring strstr for xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array (haystack)</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> to search (needle)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * for the first occurrence or NULL.</td></tr></tbody></table></div><h3><a name="xmlStrcat" id="xmlStrcat"></a>Function: xmlStrcat</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrcat		(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * add)<br />
</pre><p>a strcat for array of xmlChar's. Since they are supposed to be encoded in UTF-8 or an encoding with 8bit based chars, we assume a termination mark of '0'.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the original <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array</td></tr><tr><td><span class="term"><i><tt>add</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array added</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * containing the concatenated string.</td></tr></tbody></table></div><h3><a name="xmlStrchr" id="xmlStrchr"></a>Function: xmlStrchr</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrchr		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> val)<br />
</pre><p>a strchr for xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> to search</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * for the first occurrence or NULL.</td></tr></tbody></table></div><h3><a name="xmlStrcmp" id="xmlStrcmp"></a>Function: xmlStrcmp</h3><pre class="programlisting">int	xmlStrcmp			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2)<br />
</pre><p>a strcmp for xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str1</tt></i>:</span></td><td>the first <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>str2</tt></i>:</span></td><td>the second <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the integer result of the comparison</td></tr></tbody></table></div><h3><a name="xmlStrdup" id="xmlStrdup"></a>Function: xmlStrdup</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrdup		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur)<br />
</pre><p>a strdup for array of xmlChar's. Since they are supposed to be encoded in UTF-8 or an encoding with 8bit based chars, we assume a termination mark of '0'.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the input <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * or NULL</td></tr></tbody></table></div><h3><a name="xmlStrlen" id="xmlStrlen"></a>Function: xmlStrlen</h3><pre class="programlisting">int	xmlStrlen			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>length of a xmlChar's string</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> contained in the ARRAY.</td></tr></tbody></table></div><h3><a name="xmlStrncasecmp" id="xmlStrncasecmp"></a>Function: xmlStrncasecmp</h3><pre class="programlisting">int	xmlStrncasecmp			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2, <br />					 int len)<br />
</pre><p>a strncasecmp for xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str1</tt></i>:</span></td><td>the first <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>str2</tt></i>:</span></td><td>the second <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the max comparison length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the integer result of the comparison</td></tr></tbody></table></div><h3><a name="xmlStrncat" id="xmlStrncat"></a>Function: xmlStrncat</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrncat		(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * add, <br />					 int len)<br />
</pre><p>a strncat for array of xmlChar's, it will extend @cur with the len first bytes of @add. Note that if @len &lt; 0 then this is an API error and NULL will be returned.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the original <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array</td></tr><tr><td><span class="term"><i><tt>add</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array added</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of @add</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *, the original @cur is reallocated if needed and should not be freed</td></tr></tbody></table></div><h3><a name="xmlStrncatNew" id="xmlStrncatNew"></a>Function: xmlStrncatNew</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrncatNew		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2, <br />					 int len)<br />
</pre><p>same as xmlStrncat, but creates a new string. The original two strings are not freed. If @len is &lt; 0 then the length will be calculated automatically.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str1</tt></i>:</span></td><td>first <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>str2</tt></i>:</span></td><td>second <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the len of @str2 or &lt; 0</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * or NULL</td></tr></tbody></table></div><h3><a name="xmlStrncmp" id="xmlStrncmp"></a>Function: xmlStrncmp</h3><pre class="programlisting">int	xmlStrncmp			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str2, <br />					 int len)<br />
</pre><p>a strncmp for xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str1</tt></i>:</span></td><td>the first <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>str2</tt></i>:</span></td><td>the second <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the max comparison length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the integer result of the comparison</td></tr></tbody></table></div><h3><a name="xmlStrndup" id="xmlStrndup"></a>Function: xmlStrndup</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrndup		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 int len)<br />
</pre><p>a strndup for array of xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the input <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the len of @cur</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * or NULL</td></tr></tbody></table></div><h3><a name="xmlStrstr" id="xmlStrstr"></a>Function: xmlStrstr</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrstr		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * val)<br />
</pre><p>a strstr for xmlChar's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array (haystack)</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> to search (needle)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * for the first occurrence or NULL.</td></tr></tbody></table></div><h3><a name="xmlStrsub" id="xmlStrsub"></a>Function: xmlStrsub</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlStrsub		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 int start, <br />					 int len)<br />
</pre><p>Extract a substring of a given string</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * array (haystack)</td></tr><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the index of the first char (zero based)</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of the substring</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * for the first occurrence or NULL.</td></tr></tbody></table></div><h3><a name="xmlUTF8Charcmp" id="xmlUTF8Charcmp"></a>Function: xmlUTF8Charcmp</h3><pre class="programlisting">int	xmlUTF8Charcmp			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf1, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf2)<br />
</pre><p>compares the two UCS4 values</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf1</tt></i>:</span></td><td>pointer to first UTF8 char</td></tr><tr><td><span class="term"><i><tt>utf2</tt></i>:</span></td><td>pointer to second UTF8 char</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>result of the compare as with <a href="libxml-xmlstring.html#xmlStrncmp">xmlStrncmp</a></td></tr></tbody></table></div><h3><a name="xmlUTF8Size" id="xmlUTF8Size"></a>Function: xmlUTF8Size</h3><pre class="programlisting">int	xmlUTF8Size			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf)<br />
</pre><p>calculates the internal size of a UTF8 character</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>pointer to the UTF8 character</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the numbers of bytes in the character, -1 on format error</td></tr></tbody></table></div><h3><a name="xmlUTF8Strlen" id="xmlUTF8Strlen"></a>Function: xmlUTF8Strlen</h3><pre class="programlisting">int	xmlUTF8Strlen			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf)<br />
</pre><p>compute the length of an UTF8 string, it doesn't do a full UTF8 checking of the content of the string.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>a sequence of UTF-8 encoded bytes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of <a href="libxml-SAX.html#characters">characters</a> in the string or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlUTF8Strloc" id="xmlUTF8Strloc"></a>Function: xmlUTF8Strloc</h3><pre class="programlisting">int	xmlUTF8Strloc			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utfchar)<br />
</pre><p>a function to provide the relative location of a UTF8 char</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>the input UTF8 *</td></tr><tr><td><span class="term"><i><tt>utfchar</tt></i>:</span></td><td>the UTF8 character to be found</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the relative character position of the desired char or -1 if not found</td></tr></tbody></table></div><h3><a name="xmlUTF8Strndup" id="xmlUTF8Strndup"></a>Function: xmlUTF8Strndup</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlUTF8Strndup		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 int len)<br />
</pre><p>a strndup for array of UTF8's</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>the input UTF8 *</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the len of @utf (in chars)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new UTF8 * or NULL</td></tr></tbody></table></div><h3><a name="xmlUTF8Strpos" id="xmlUTF8Strpos"></a>Function: xmlUTF8Strpos</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlUTF8Strpos		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 int pos)<br />
</pre><p>a function to provide the equivalent of fetching a character from a string array</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>the input UTF8 *</td></tr><tr><td><span class="term"><i><tt>pos</tt></i>:</span></td><td>the position of the desired UTF8 char (in chars)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the UTF8 character or NULL</td></tr></tbody></table></div><h3><a name="xmlUTF8Strsize" id="xmlUTF8Strsize"></a>Function: xmlUTF8Strsize</h3><pre class="programlisting">int	xmlUTF8Strsize			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 int len)<br />
</pre><p>storage size of an UTF8 string the behaviour is not garanteed if the input string is not UTF-8</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>a sequence of UTF-8 encoded bytes</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-SAX.html#characters">characters</a> in the array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the storage size of the first 'len' <a href="libxml-SAX.html#characters">characters</a> of ARRAY</td></tr></tbody></table></div><h3><a name="xmlUTF8Strsub" id="xmlUTF8Strsub"></a>Function: xmlUTF8Strsub</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlUTF8Strsub		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * utf, <br />					 int start, <br />					 int len)<br />
</pre><p>Create a substring from a given UTF-8 string Note: positions are given in units of UTF-8 chars</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>utf</tt></i>:</span></td><td>a sequence of UTF-8 encoded bytes</td></tr><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>relative pos of first char</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>total number to copy</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to a newly created string or NULL if any problem</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
