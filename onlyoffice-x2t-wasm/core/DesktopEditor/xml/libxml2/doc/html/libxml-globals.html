<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module globals from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module globals from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-entities.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-entities.html">entities</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-hash.html">hash</a></th><td><a accesskey="n" href="libxml-hash.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>all the global variables and thread handling for those variables is handled by this module.  The bottom of this file is automatically generated by build_glob.py based on the description file global.data </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlGlobalState">xmlGlobalState</a><br />struct _xmlGlobalState
</pre><pre class="programlisting">Typedef <a href="libxml-globals.html#xmlGlobalState">xmlGlobalState</a> * <a name="xmlGlobalStatePtr" id="xmlGlobalStatePtr">xmlGlobalStatePtr</a>
</pre><pre class="programlisting">void	<a href="#xmlCleanupGlobals">xmlCleanupGlobals</a>		(void)</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	<a href="#xmlDeregisterNodeDefault">xmlDeregisterNodeDefault</a>	(<a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> func)</pre>
<pre class="programlisting">Function type: <a href="#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>
void	<a href="#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)
</pre>
<pre class="programlisting">void	<a href="#xmlInitGlobals">xmlInitGlobals</a>			(void)</pre>
<pre class="programlisting">void	<a href="#xmlInitializeGlobalState">xmlInitializeGlobalState</a>	(<a href="libxml-globals.html#xmlGlobalStatePtr">xmlGlobalStatePtr</a> gs)</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	<a href="#xmlOutputBufferCreateFilenameDefault">xmlOutputBufferCreateFilenameDefault</a>	(<a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> func)</pre>
<pre class="programlisting">Function type: <a href="#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>
<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	<a href="#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	(const char * URI, <br />							 <a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder, <br />							 int compression)
</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	<a href="#xmlParserInputBufferCreateFilenameDefault">xmlParserInputBufferCreateFilenameDefault</a>	(<a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> func)</pre>
<pre class="programlisting">Function type: <a href="#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>
<a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	(const char * URI, <br />							 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)
</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	<a href="#xmlRegisterNodeDefault">xmlRegisterNodeDefault</a>	(<a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> func)</pre>
<pre class="programlisting">Function type: <a href="#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>
void	<a href="#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)
</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	<a href="#xmlThrDefBufferAllocScheme">xmlThrDefBufferAllocScheme</a>	(<a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> v)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefDefaultBufferSize">xmlThrDefDefaultBufferSize</a>	(int v)</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	<a href="#xmlThrDefDeregisterNodeDefault">xmlThrDefDeregisterNodeDefault</a>	(<a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> func)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefDoValidityCheckingDefaultValue">xmlThrDefDoValidityCheckingDefaultValue</a>	(int v)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefGetWarningsDefaultValue">xmlThrDefGetWarningsDefaultValue</a>	(int v)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefIndentTreeOutput">xmlThrDefIndentTreeOutput</a>	(int v)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefKeepBlanksDefaultValue">xmlThrDefKeepBlanksDefaultValue</a>	(int v)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefLineNumbersDefaultValue">xmlThrDefLineNumbersDefaultValue</a>	(int v)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefLoadExtDtdDefaultValue">xmlThrDefLoadExtDtdDefaultValue</a>	(int v)</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	<a href="#xmlThrDefOutputBufferCreateFilenameDefault">xmlThrDefOutputBufferCreateFilenameDefault</a>	(<a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> func)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefParserDebugEntities">xmlThrDefParserDebugEntities</a>	(int v)</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	<a href="#xmlThrDefParserInputBufferCreateFilenameDefault">xmlThrDefParserInputBufferCreateFilenameDefault</a>	(<a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> func)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefPedanticParserDefaultValue">xmlThrDefPedanticParserDefaultValue</a>	(int v)</pre>
<pre class="programlisting"><a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	<a href="#xmlThrDefRegisterNodeDefault">xmlThrDefRegisterNodeDefault</a>	(<a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> func)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefSaveNoEmptyTags">xmlThrDefSaveNoEmptyTags</a>	(int v)</pre>
<pre class="programlisting">void	<a href="#xmlThrDefSetGenericErrorFunc">xmlThrDefSetGenericErrorFunc</a>	(void * ctx, <br />					 <a href="libxml-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> handler)</pre>
<pre class="programlisting">void	<a href="#xmlThrDefSetStructuredErrorFunc">xmlThrDefSetStructuredErrorFunc</a>	(void * ctx, <br />					 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> handler)</pre>
<pre class="programlisting">int	<a href="#xmlThrDefSubstituteEntitiesDefaultValue">xmlThrDefSubstituteEntitiesDefaultValue</a>	(int v)</pre>
<pre class="programlisting">const char *	<a href="#xmlThrDefTreeIndentString">xmlThrDefTreeIndentString</a>	(const char * v)</pre>
<h2>Description</h2>
<h3><a name="xmlGlobalState" id="xmlGlobalState">Structure xmlGlobalState</a></h3><pre class="programlisting">Structure xmlGlobalState<br />struct _xmlGlobalState {
    const char *	xmlParserVersion
    <a href="libxml-tree.html#xmlSAXLocator">xmlSAXLocator</a>	xmlDefaultSAXLocator
    <a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a>	xmlDefaultSAXHandler
    <a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a>	docbDefaultSAXHandler
    <a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a>	htmlDefaultSAXHandler
    <a href="libxml-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a>	xmlFree
    <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a>	xmlMalloc
    <a href="libxml-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a>	xmlMemStrdup
    <a href="libxml-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a>	xmlRealloc
    <a href="libxml-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a>	xmlGenericError
    <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a>	xmlStructuredError
    void *	xmlGenericErrorContext
    int	oldXMLWDcompatibility
    <a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	xmlBufferAllocScheme
    int	xmlDefaultBufferSize
    int	xmlSubstituteEntitiesDefaultValue
    int	xmlDoValidityCheckingDefaultValue
    int	xmlGetWarningsDefaultValue
    int	xmlKeepBlanksDefaultValue
    int	xmlLineNumbersDefaultValue
    int	xmlLoadExtDtdDefaultValue
    int	xmlParserDebugEntities
    int	xmlPedanticParserDefaultValue
    int	xmlSaveNoEmptyTags
    int	xmlIndentTreeOutput
    const char *	xmlTreeIndentString
    <a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	xmlRegisterNodeDefaultValue
    <a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	xmlDeregisterNodeDefaultValue
    <a href="libxml-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a>	xmlMallocAtomic
    <a href="libxml-xmlerror.html#xmlError">xmlError</a>	xmlLastError
    <a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	xmlParserInputBufferCreateFilenameValue
    <a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	xmlOutputBufferCreateFilenameValue
    void *	xmlStructuredErrorContext
}</pre><h3><a name="xmlCleanupGlobals" id="xmlCleanupGlobals"></a>Function: xmlCleanupGlobals</h3><pre class="programlisting">void	xmlCleanupGlobals		(void)<br />
</pre><p>Additional cleanup for multi-threading</p>
<h3><a name="xmlDeregisterNodeDefault" id="xmlDeregisterNodeDefault"></a>Function: xmlDeregisterNodeDefault</h3><pre class="programlisting"><a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	xmlDeregisterNodeDefault	(<a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> func)<br />
</pre><p>Registers a callback for node destruction</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>function pointer to the new DeregisterNodeFunc</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the previous value of the deregistration function</td></tr></tbody></table></div><h3><a name="xmlDeregisterNodeFunc" id="xmlDeregisterNodeFunc"></a>Function type: xmlDeregisterNodeFunc</h3><pre class="programlisting">Function type: xmlDeregisterNodeFunc
void	xmlDeregisterNodeFunc		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)
</pre><p>Signature for the deregistration callback of a discarded node</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr></tbody></table></div><br />
<h3><a name="xmlInitGlobals" id="xmlInitGlobals"></a>Function: xmlInitGlobals</h3><pre class="programlisting">void	xmlInitGlobals			(void)<br />
</pre><p>Additional initialisation for multi-threading</p>
<h3><a name="xmlInitializeGlobalState" id="xmlInitializeGlobalState"></a>Function: xmlInitializeGlobalState</h3><pre class="programlisting">void	xmlInitializeGlobalState	(<a href="libxml-globals.html#xmlGlobalStatePtr">xmlGlobalStatePtr</a> gs)<br />
</pre><p>xmlInitializeGlobalState() initialize a global state with all the default values of the library.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>gs</tt></i>:</span></td><td>a pointer to a newly allocated global state</td></tr></tbody></table></div><h3><a name="xmlOutputBufferCreateFilenameDefault" id="xmlOutputBufferCreateFilenameDefault"></a>Function: xmlOutputBufferCreateFilenameDefault</h3><pre class="programlisting"><a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	xmlOutputBufferCreateFilenameDefault	(<a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> func)<br />
</pre><p>Registers a callback for URI output file handling</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>function pointer to the new OutputBufferCreateFilenameFunc</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the old value of the registration function</td></tr></tbody></table></div><h3><a name="xmlOutputBufferCreateFilenameFunc" id="xmlOutputBufferCreateFilenameFunc"></a>Function type: xmlOutputBufferCreateFilenameFunc</h3><pre class="programlisting">Function type: xmlOutputBufferCreateFilenameFunc
<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	xmlOutputBufferCreateFilenameFunc	(const char * URI, <br />							 <a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder, <br />							 int compression)
</pre><p>Signature for the function doing the lookup for a suitable output method corresponding to an URI.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI to write to</td></tr><tr><td><span class="term"><i><tt>encoder</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> in case of success or NULL if no method was found.</td></tr></tbody></table></div><br />
<h3><a name="xmlParserInputBufferCreateFilenameDefault" id="xmlParserInputBufferCreateFilenameDefault"></a>Function: xmlParserInputBufferCreateFilenameDefault</h3><pre class="programlisting"><a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	xmlParserInputBufferCreateFilenameDefault	(<a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> func)<br />
</pre><p>Registers a callback for URI input file handling</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>function pointer to the new ParserInputBufferCreateFilenameFunc</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the old value of the registration function</td></tr></tbody></table></div><h3><a name="xmlParserInputBufferCreateFilenameFunc" id="xmlParserInputBufferCreateFilenameFunc"></a>Function type: xmlParserInputBufferCreateFilenameFunc</h3><pre class="programlisting">Function type: xmlParserInputBufferCreateFilenameFunc
<a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlParserInputBufferCreateFilenameFunc	(const char * URI, <br />							 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)
</pre><p>Signature for the function doing the lookup for a suitable input method corresponding to an URI.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI to read from</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the requested source encoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in case of success or NULL if no method was found.</td></tr></tbody></table></div><br />
<h3><a name="xmlRegisterNodeDefault" id="xmlRegisterNodeDefault"></a>Function: xmlRegisterNodeDefault</h3><pre class="programlisting"><a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	xmlRegisterNodeDefault	(<a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> func)<br />
</pre><p>Registers a callback for node creation</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>function pointer to the new RegisterNodeFunc</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the old value of the registration function</td></tr></tbody></table></div><h3><a name="xmlRegisterNodeFunc" id="xmlRegisterNodeFunc"></a>Function type: xmlRegisterNodeFunc</h3><pre class="programlisting">Function type: xmlRegisterNodeFunc
void	xmlRegisterNodeFunc		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)
</pre><p>Signature for the registration callback of a created node</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr></tbody></table></div><br />
<h3><a name="xmlThrDefBufferAllocScheme" id="xmlThrDefBufferAllocScheme"></a>Function: xmlThrDefBufferAllocScheme</h3><pre class="programlisting"><a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	xmlThrDefBufferAllocScheme	(<a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefDefaultBufferSize" id="xmlThrDefDefaultBufferSize"></a>Function: xmlThrDefDefaultBufferSize</h3><pre class="programlisting">int	xmlThrDefDefaultBufferSize	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefDeregisterNodeDefault" id="xmlThrDefDeregisterNodeDefault"></a>Function: xmlThrDefDeregisterNodeDefault</h3><pre class="programlisting"><a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	xmlThrDefDeregisterNodeDefault	(<a href="libxml-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> func)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefDoValidityCheckingDefaultValue" id="xmlThrDefDoValidityCheckingDefaultValue"></a>Function: xmlThrDefDoValidityCheckingDefaultValue</h3><pre class="programlisting">int	xmlThrDefDoValidityCheckingDefaultValue	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefGetWarningsDefaultValue" id="xmlThrDefGetWarningsDefaultValue"></a>Function: xmlThrDefGetWarningsDefaultValue</h3><pre class="programlisting">int	xmlThrDefGetWarningsDefaultValue	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefIndentTreeOutput" id="xmlThrDefIndentTreeOutput"></a>Function: xmlThrDefIndentTreeOutput</h3><pre class="programlisting">int	xmlThrDefIndentTreeOutput	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefKeepBlanksDefaultValue" id="xmlThrDefKeepBlanksDefaultValue"></a>Function: xmlThrDefKeepBlanksDefaultValue</h3><pre class="programlisting">int	xmlThrDefKeepBlanksDefaultValue	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefLineNumbersDefaultValue" id="xmlThrDefLineNumbersDefaultValue"></a>Function: xmlThrDefLineNumbersDefaultValue</h3><pre class="programlisting">int	xmlThrDefLineNumbersDefaultValue	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefLoadExtDtdDefaultValue" id="xmlThrDefLoadExtDtdDefaultValue"></a>Function: xmlThrDefLoadExtDtdDefaultValue</h3><pre class="programlisting">int	xmlThrDefLoadExtDtdDefaultValue	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefOutputBufferCreateFilenameDefault" id="xmlThrDefOutputBufferCreateFilenameDefault"></a>Function: xmlThrDefOutputBufferCreateFilenameDefault</h3><pre class="programlisting"><a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	xmlThrDefOutputBufferCreateFilenameDefault	(<a href="libxml-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> func)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefParserDebugEntities" id="xmlThrDefParserDebugEntities"></a>Function: xmlThrDefParserDebugEntities</h3><pre class="programlisting">int	xmlThrDefParserDebugEntities	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefParserInputBufferCreateFilenameDefault" id="xmlThrDefParserInputBufferCreateFilenameDefault"></a>Function: xmlThrDefParserInputBufferCreateFilenameDefault</h3><pre class="programlisting"><a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	xmlThrDefParserInputBufferCreateFilenameDefault	(<a href="libxml-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> func)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefPedanticParserDefaultValue" id="xmlThrDefPedanticParserDefaultValue"></a>Function: xmlThrDefPedanticParserDefaultValue</h3><pre class="programlisting">int	xmlThrDefPedanticParserDefaultValue	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefRegisterNodeDefault" id="xmlThrDefRegisterNodeDefault"></a>Function: xmlThrDefRegisterNodeDefault</h3><pre class="programlisting"><a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	xmlThrDefRegisterNodeDefault	(<a href="libxml-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> func)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefSaveNoEmptyTags" id="xmlThrDefSaveNoEmptyTags"></a>Function: xmlThrDefSaveNoEmptyTags</h3><pre class="programlisting">int	xmlThrDefSaveNoEmptyTags	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefSetGenericErrorFunc" id="xmlThrDefSetGenericErrorFunc"></a>Function: xmlThrDefSetGenericErrorFunc</h3><pre class="programlisting">void	xmlThrDefSetGenericErrorFunc	(void * ctx, <br />					 <a href="libxml-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> handler)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefSetStructuredErrorFunc" id="xmlThrDefSetStructuredErrorFunc"></a>Function: xmlThrDefSetStructuredErrorFunc</h3><pre class="programlisting">void	xmlThrDefSetStructuredErrorFunc	(void * ctx, <br />					 <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> handler)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefSubstituteEntitiesDefaultValue" id="xmlThrDefSubstituteEntitiesDefaultValue"></a>Function: xmlThrDefSubstituteEntitiesDefaultValue</h3><pre class="programlisting">int	xmlThrDefSubstituteEntitiesDefaultValue	(int v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><h3><a name="xmlThrDefTreeIndentString" id="xmlThrDefTreeIndentString"></a>Function: xmlThrDefTreeIndentString</h3><pre class="programlisting">const char *	xmlThrDefTreeIndentString	(const char * v)<br />
</pre><p></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td></td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
