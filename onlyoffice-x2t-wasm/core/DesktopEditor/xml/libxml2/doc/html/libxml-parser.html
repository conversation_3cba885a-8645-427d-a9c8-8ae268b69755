<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module parser from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module parser from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-nanohttp.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-nanohttp.html">nanohttp</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-parserInternals.html">parserInternals</a></th><td><a accesskey="n" href="libxml-parserInternals.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>Interfaces, constants and types related to the XML parser </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#XML_COMPLETE_ATTRS">XML_COMPLETE_ATTRS</a></pre><pre class="programlisting">#define <a href="#XML_DEFAULT_VERSION">XML_DEFAULT_VERSION</a></pre><pre class="programlisting">#define <a href="#XML_DETECT_IDS">XML_DETECT_IDS</a></pre><pre class="programlisting">#define <a href="#XML_SAX2_MAGIC">XML_SAX2_MAGIC</a></pre><pre class="programlisting">#define <a href="#XML_SKIP_IDS">XML_SKIP_IDS</a></pre><pre class="programlisting">Enum <a href="#xmlFeature">xmlFeature</a>
</pre><pre class="programlisting">Enum <a href="#xmlParserInputState">xmlParserInputState</a>
</pre><pre class="programlisting">Enum <a href="#xmlParserMode">xmlParserMode</a>
</pre><pre class="programlisting">Structure <a href="#xmlParserNodeInfo">xmlParserNodeInfo</a><br />struct _xmlParserNodeInfo
</pre><pre class="programlisting">Typedef <a href="libxml-parser.html#xmlParserNodeInfo">xmlParserNodeInfo</a> * <a name="xmlParserNodeInfoPtr" id="xmlParserNodeInfoPtr">xmlParserNodeInfoPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlParserNodeInfoSeq">xmlParserNodeInfoSeq</a><br />struct _xmlParserNodeInfoSeq
</pre><pre class="programlisting">Typedef <a href="libxml-parser.html#xmlParserNodeInfoSeq">xmlParserNodeInfoSeq</a> * <a name="xmlParserNodeInfoSeqPtr" id="xmlParserNodeInfoSeqPtr">xmlParserNodeInfoSeqPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlParserOption">xmlParserOption</a>
</pre><pre class="programlisting">Structure <a href="#xmlSAXHandlerV1">xmlSAXHandlerV1</a><br />struct _xmlSAXHandlerV1
</pre><pre class="programlisting">Typedef <a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * <a name="xmlSAXHandlerV1Ptr" id="xmlSAXHandlerV1Ptr">xmlSAXHandlerV1Ptr</a>
</pre><pre class="programlisting">Function type: <a href="#attributeDeclSAXFunc">attributeDeclSAXFunc</a>
void	<a href="#attributeDeclSAXFunc">attributeDeclSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * elem, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 int type, <br />					 int def, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * defaultValue, <br />					 <a href="libxml-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> tree)
</pre>
<pre class="programlisting">Function type: <a href="#attributeSAXFunc">attributeSAXFunc</a>
void	<a href="#attributeSAXFunc">attributeSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)
</pre>
<pre class="programlisting">Function type: <a href="#cdataBlockSAXFunc">cdataBlockSAXFunc</a>
void	<a href="#cdataBlockSAXFunc">cdataBlockSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int len)
</pre>
<pre class="programlisting">Function type: <a href="#charactersSAXFunc">charactersSAXFunc</a>
void	<a href="#charactersSAXFunc">charactersSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)
</pre>
<pre class="programlisting">Function type: <a href="#commentSAXFunc">commentSAXFunc</a>
void	<a href="#commentSAXFunc">commentSAXFunc</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)
</pre>
<pre class="programlisting">Function type: <a href="#elementDeclSAXFunc">elementDeclSAXFunc</a>
void	<a href="#elementDeclSAXFunc">elementDeclSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 <a href="libxml-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> content)
</pre>
<pre class="programlisting">Function type: <a href="#endDocumentSAXFunc">endDocumentSAXFunc</a>
void	<a href="#endDocumentSAXFunc">endDocumentSAXFunc</a>		(void * ctx)
</pre>
<pre class="programlisting">Function type: <a href="#endElementNsSAX2Func">endElementNsSAX2Func</a>
void	<a href="#endElementNsSAX2Func">endElementNsSAX2Func</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)
</pre>
<pre class="programlisting">Function type: <a href="#endElementSAXFunc">endElementSAXFunc</a>
void	<a href="#endElementSAXFunc">endElementSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre>
<pre class="programlisting">Function type: <a href="#entityDeclSAXFunc">entityDeclSAXFunc</a>
void	<a href="#entityDeclSAXFunc">entityDeclSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)
</pre>
<pre class="programlisting">Function type: <a href="#errorSAXFunc">errorSAXFunc</a>
void	<a href="#errorSAXFunc">errorSAXFunc</a>			(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre>
<pre class="programlisting">Function type: <a href="#externalSubsetSAXFunc">externalSubsetSAXFunc</a>
void	<a href="#externalSubsetSAXFunc">externalSubsetSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)
</pre>
<pre class="programlisting">Function type: <a href="#fatalErrorSAXFunc">fatalErrorSAXFunc</a>
void	<a href="#fatalErrorSAXFunc">fatalErrorSAXFunc</a>		(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre>
<pre class="programlisting">Function type: <a href="#getEntitySAXFunc">getEntitySAXFunc</a>
<a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#getEntitySAXFunc">getEntitySAXFunc</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre>
<pre class="programlisting">Function type: <a href="#getParameterEntitySAXFunc">getParameterEntitySAXFunc</a>
<a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#getParameterEntitySAXFunc">getParameterEntitySAXFunc</a>	(void * ctx, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre>
<pre class="programlisting">Function type: <a href="#hasExternalSubsetSAXFunc">hasExternalSubsetSAXFunc</a>
int	<a href="#hasExternalSubsetSAXFunc">hasExternalSubsetSAXFunc</a>	(void * ctx)
</pre>
<pre class="programlisting">Function type: <a href="#hasInternalSubsetSAXFunc">hasInternalSubsetSAXFunc</a>
int	<a href="#hasInternalSubsetSAXFunc">hasInternalSubsetSAXFunc</a>	(void * ctx)
</pre>
<pre class="programlisting">Function type: <a href="#ignorableWhitespaceSAXFunc">ignorableWhitespaceSAXFunc</a>
void	<a href="#ignorableWhitespaceSAXFunc">ignorableWhitespaceSAXFunc</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)
</pre>
<pre class="programlisting">Function type: <a href="#internalSubsetSAXFunc">internalSubsetSAXFunc</a>
void	<a href="#internalSubsetSAXFunc">internalSubsetSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)
</pre>
<pre class="programlisting">Function type: <a href="#isStandaloneSAXFunc">isStandaloneSAXFunc</a>
int	<a href="#isStandaloneSAXFunc">isStandaloneSAXFunc</a>		(void * ctx)
</pre>
<pre class="programlisting">Function type: <a href="#notationDeclSAXFunc">notationDeclSAXFunc</a>
void	<a href="#notationDeclSAXFunc">notationDeclSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)
</pre>
<pre class="programlisting">Function type: <a href="#processingInstructionSAXFunc">processingInstructionSAXFunc</a>
void	<a href="#processingInstructionSAXFunc">processingInstructionSAXFunc</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * data)
</pre>
<pre class="programlisting">Function type: <a href="#referenceSAXFunc">referenceSAXFunc</a>
void	<a href="#referenceSAXFunc">referenceSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre>
<pre class="programlisting">Function type: <a href="#resolveEntitySAXFunc">resolveEntitySAXFunc</a>
<a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#resolveEntitySAXFunc">resolveEntitySAXFunc</a>	(void * ctx, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)
</pre>
<pre class="programlisting">Function type: <a href="#setDocumentLocatorSAXFunc">setDocumentLocatorSAXFunc</a>
void	<a href="#setDocumentLocatorSAXFunc">setDocumentLocatorSAXFunc</a>	(void * ctx, <br />					 <a href="libxml-tree.html#xmlSAXLocatorPtr">xmlSAXLocatorPtr</a> loc)
</pre>
<pre class="programlisting">Function type: <a href="#startDocumentSAXFunc">startDocumentSAXFunc</a>
void	<a href="#startDocumentSAXFunc">startDocumentSAXFunc</a>		(void * ctx)
</pre>
<pre class="programlisting">Function type: <a href="#startElementNsSAX2Func">startElementNsSAX2Func</a>
void	<a href="#startElementNsSAX2Func">startElementNsSAX2Func</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 int nb_namespaces, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** namespaces, <br />					 int nb_attributes, <br />					 int nb_defaulted, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** attributes)
</pre>
<pre class="programlisting">Function type: <a href="#startElementSAXFunc">startElementSAXFunc</a>
void	<a href="#startElementSAXFunc">startElementSAXFunc</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** atts)
</pre>
<pre class="programlisting">Function type: <a href="#unparsedEntityDeclSAXFunc">unparsedEntityDeclSAXFunc</a>
void	<a href="#unparsedEntityDeclSAXFunc">unparsedEntityDeclSAXFunc</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * notationName)
</pre>
<pre class="programlisting">Function type: <a href="#warningSAXFunc">warningSAXFunc</a>
void	<a href="#warningSAXFunc">warningSAXFunc</a>			(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre>
<pre class="programlisting">long	<a href="#xmlByteConsumed">xmlByteConsumed</a>			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">void	<a href="#xmlCleanupParser">xmlCleanupParser</a>		(void)</pre>
<pre class="programlisting">void	<a href="#xmlClearNodeInfoSeq">xmlClearNodeInfoSeq</a>		(<a href="libxml-parser.html#xmlParserNodeInfoSeqPtr">xmlParserNodeInfoSeqPtr</a> seq)</pre>
<pre class="programlisting">void	<a href="#xmlClearParserCtxt">xmlClearParserCtxt</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlCreateDocParserCtxt">xmlCreateDocParserCtxt</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlCreateIOParserCtxt">xmlCreateIOParserCtxt</a>	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />						 void * user_data, <br />						 <a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />						 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />						 void * ioctx, <br />						 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlCreatePushParserCtxt">xmlCreatePushParserCtxt</a>	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />						 void * user_data, <br />						 const char * chunk, <br />						 int size, <br />						 const char * filename)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlCtxtReadDoc">xmlCtxtReadDoc</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlCtxtReadFd">xmlCtxtReadFd</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 int fd, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlCtxtReadFile">xmlCtxtReadFile</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * filename, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlCtxtReadIO">xmlCtxtReadIO</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />					 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlCtxtReadMemory">xmlCtxtReadMemory</a>	(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * buffer, <br />					 int size, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting">void	<a href="#xmlCtxtReset">xmlCtxtReset</a>			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlCtxtResetPush">xmlCtxtResetPush</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * chunk, <br />					 int size, <br />					 const char * filename, <br />					 const char * encoding)</pre>
<pre class="programlisting">int	<a href="#xmlCtxtUseOptions">xmlCtxtUseOptions</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 int options)</pre>
<pre class="programlisting">Function type: <a href="#xmlExternalEntityLoader">xmlExternalEntityLoader</a>
<a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlExternalEntityLoader">xmlExternalEntityLoader</a>	(const char * URL, <br />						 const char * ID, <br />						 <a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> context)
</pre>
<pre class="programlisting">void	<a href="#xmlFreeParserCtxt">xmlFreeParserCtxt</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-parser.html#xmlExternalEntityLoader">xmlExternalEntityLoader</a>	<a href="#xmlGetExternalEntityLoader">xmlGetExternalEntityLoader</a>	(void)</pre>
<pre class="programlisting">int	<a href="#xmlGetFeature">xmlGetFeature</a>			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * name, <br />					 void * result)</pre>
<pre class="programlisting">int	<a href="#xmlGetFeaturesList">xmlGetFeaturesList</a>		(int * len, <br />					 const char ** result)</pre>
<pre class="programlisting">int	<a href="#xmlHasFeature">xmlHasFeature</a>			(<a href="libxml-parser.html#xmlFeature">xmlFeature</a> feature)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	<a href="#xmlIOParseDTD">xmlIOParseDTD</a>		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />					 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)</pre>
<pre class="programlisting">void	<a href="#xmlInitNodeInfoSeq">xmlInitNodeInfoSeq</a>		(<a href="libxml-parser.html#xmlParserNodeInfoSeqPtr">xmlParserNodeInfoSeqPtr</a> seq)</pre>
<pre class="programlisting">void	<a href="#xmlInitParser">xmlInitParser</a>			(void)</pre>
<pre class="programlisting">int	<a href="#xmlInitParserCtxt">xmlInitParserCtxt</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlKeepBlanksDefault">xmlKeepBlanksDefault</a>		(int val)</pre>
<pre class="programlisting">int	<a href="#xmlLineNumbersDefault">xmlLineNumbersDefault</a>		(int val)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlLoadExternalEntity">xmlLoadExternalEntity</a>	(const char * URL, <br />						 const char * ID, <br />						 <a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlNewIOInputStream">xmlNewIOInputStream</a>	(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />						 <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />						 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlNewParserCtxt">xmlNewParserCtxt</a>	(void)</pre>
<pre class="programlisting">int	<a href="#xmlParseBalancedChunkMemory">xmlParseBalancedChunkMemory</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data, <br />					 int depth, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst)</pre>
<pre class="programlisting">int	<a href="#xmlParseBalancedChunkMemoryRecover">xmlParseBalancedChunkMemoryRecover</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />						 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />						 void * user_data, <br />						 int depth, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst, <br />						 int recover)</pre>
<pre class="programlisting">int	<a href="#xmlParseChunk">xmlParseChunk</a>			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * chunk, <br />					 int size, <br />					 int terminate)</pre>
<pre class="programlisting">int	<a href="#xmlParseCtxtExternalEntity">xmlParseCtxtExternalEntity</a>	(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URL, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ID, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	<a href="#xmlParseDTD">xmlParseDTD</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlParseDoc">xmlParseDoc</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur)</pre>
<pre class="programlisting">int	<a href="#xmlParseDocument">xmlParseDocument</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlParseEntity">xmlParseEntity</a>		(const char * filename)</pre>
<pre class="programlisting">int	<a href="#xmlParseExtParsedEnt">xmlParseExtParsedEnt</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlParseExternalEntity">xmlParseExternalEntity</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data, <br />					 int depth, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URL, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ID, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlParseFile">xmlParseFile</a>		(const char * filename)</pre>
<pre class="programlisting"><a href="libxml-xmlerror.html#xmlParserErrors">xmlParserErrors</a>	<a href="#xmlParseInNodeContext">xmlParseInNodeContext</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const char * data, <br />					 int datalen, <br />					 int options, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlParseMemory">xmlParseMemory</a>		(const char * buffer, <br />					 int size)</pre>
<pre class="programlisting">void	<a href="#xmlParserAddNodeInfo">xmlParserAddNodeInfo</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const <a href="libxml-parser.html#xmlParserNodeInfoPtr">xmlParserNodeInfoPtr</a> info)</pre>
<pre class="programlisting">const <a href="libxml-parser.html#xmlParserNodeInfo">xmlParserNodeInfo</a> *	<a href="#xmlParserFindNodeInfo">xmlParserFindNodeInfo</a>	(const <a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctx, <br />							 const <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">unsigned long	<a href="#xmlParserFindNodeInfoIndex">xmlParserFindNodeInfoIndex</a>	(const <a href="libxml-parser.html#xmlParserNodeInfoSeqPtr">xmlParserNodeInfoSeqPtr</a> seq, <br />						 const <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">Function type: <a href="#xmlParserInputDeallocate">xmlParserInputDeallocate</a>
void	<a href="#xmlParserInputDeallocate">xmlParserInputDeallocate</a>	(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)
</pre>
<pre class="programlisting">int	<a href="#xmlParserInputGrow">xmlParserInputGrow</a>		(<a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> in, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlParserInputRead">xmlParserInputRead</a>		(<a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> in, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlPedanticParserDefault">xmlPedanticParserDefault</a>	(int val)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlReadDoc">xmlReadDoc</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlReadFd">xmlReadFd</a>		(int fd, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlReadFile">xmlReadFile</a>		(const char * filename, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlReadIO">xmlReadIO</a>		(<a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />					 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlReadMemory">xmlReadMemory</a>		(const char * buffer, <br />					 int size, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlRecoverDoc">xmlRecoverDoc</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlRecoverFile">xmlRecoverFile</a>		(const char * filename)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlRecoverMemory">xmlRecoverMemory</a>	(const char * buffer, <br />					 int size)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	<a href="#xmlSAXParseDTD">xmlSAXParseDTD</a>		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlSAXParseDoc">xmlSAXParseDoc</a>		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 int recovery)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlSAXParseEntity">xmlSAXParseEntity</a>	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const char * filename)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlSAXParseFile">xmlSAXParseFile</a>		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const char * filename, <br />					 int recovery)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlSAXParseFileWithData">xmlSAXParseFileWithData</a>	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const char * filename, <br />					 int recovery, <br />					 void * data)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlSAXParseMemory">xmlSAXParseMemory</a>	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const char * buffer, <br />					 int size, <br />					 int recovery)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlSAXParseMemoryWithData">xmlSAXParseMemoryWithData</a>	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />						 const char * buffer, <br />						 int size, <br />						 int recovery, <br />						 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlSAXUserParseFile">xmlSAXUserParseFile</a>		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data, <br />					 const char * filename)</pre>
<pre class="programlisting">int	<a href="#xmlSAXUserParseMemory">xmlSAXUserParseMemory</a>		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data, <br />					 const char * buffer, <br />					 int size)</pre>
<pre class="programlisting">void	<a href="#xmlSetExternalEntityLoader">xmlSetExternalEntityLoader</a>	(<a href="libxml-parser.html#xmlExternalEntityLoader">xmlExternalEntityLoader</a> f)</pre>
<pre class="programlisting">int	<a href="#xmlSetFeature">xmlSetFeature</a>			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * name, <br />					 void * value)</pre>
<pre class="programlisting">void	<a href="#xmlSetupParserForBuffer">xmlSetupParserForBuffer</a>		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * buffer, <br />					 const char * filename)</pre>
<pre class="programlisting">void	<a href="#xmlStopParser">xmlStopParser</a>			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlSubstituteEntitiesDefault">xmlSubstituteEntitiesDefault</a>	(int val)</pre>
<h2>Description</h2>
<h3><a name="XML_COMPLETE_ATTRS" id="XML_COMPLETE_ATTRS"></a>Macro: XML_COMPLETE_ATTRS</h3><pre>#define XML_COMPLETE_ATTRS</pre><p>Bit in the loadsubset context field to tell to do complete the elements attributes lists with the ones defaulted from the DTDs. Use it to initialize xmlLoadExtDtdDefaultValue.</p>
<h3><a name="XML_DEFAULT_VERSION" id="XML_DEFAULT_VERSION"></a>Macro: XML_DEFAULT_VERSION</h3><pre>#define XML_DEFAULT_VERSION</pre><p>The default version of XML used: 1.0</p>
<h3><a name="XML_DETECT_IDS" id="XML_DETECT_IDS"></a>Macro: XML_DETECT_IDS</h3><pre>#define XML_DETECT_IDS</pre><p>Bit in the loadsubset context field to tell to do ID/REFs lookups. Use it to initialize xmlLoadExtDtdDefaultValue.</p>
<h3><a name="XML_SAX2_MAGIC" id="XML_SAX2_MAGIC"></a>Macro: XML_SAX2_MAGIC</h3><pre>#define XML_SAX2_MAGIC</pre><p>Special constant found in SAX2 blocks initialized fields</p>
<h3><a name="XML_SKIP_IDS" id="XML_SKIP_IDS"></a>Macro: XML_SKIP_IDS</h3><pre>#define XML_SKIP_IDS</pre><p>Bit in the loadsubset context field to tell to not do ID/REFs registration. Used to initialize <a href="libxml-globals.html#xmlLoadExtDtdDefaultValue">xmlLoadExtDtdDefaultValue</a> in some special cases.</p>
<h3>Enum <a name="xmlFeature" id="xmlFeature">xmlFeature</a></h3><pre class="programlisting">Enum xmlFeature {
    <a name="XML_WITH_THREAD" id="XML_WITH_THREAD">XML_WITH_THREAD</a> = 1
    <a name="XML_WITH_TREE" id="XML_WITH_TREE">XML_WITH_TREE</a> = 2
    <a name="XML_WITH_OUTPUT" id="XML_WITH_OUTPUT">XML_WITH_OUTPUT</a> = 3
    <a name="XML_WITH_PUSH" id="XML_WITH_PUSH">XML_WITH_PUSH</a> = 4
    <a name="XML_WITH_READER" id="XML_WITH_READER">XML_WITH_READER</a> = 5
    <a name="XML_WITH_PATTERN" id="XML_WITH_PATTERN">XML_WITH_PATTERN</a> = 6
    <a name="XML_WITH_WRITER" id="XML_WITH_WRITER">XML_WITH_WRITER</a> = 7
    <a name="XML_WITH_SAX1" id="XML_WITH_SAX1">XML_WITH_SAX1</a> = 8
    <a name="XML_WITH_FTP" id="XML_WITH_FTP">XML_WITH_FTP</a> = 9
    <a name="XML_WITH_HTTP" id="XML_WITH_HTTP">XML_WITH_HTTP</a> = 10
    <a name="XML_WITH_VALID" id="XML_WITH_VALID">XML_WITH_VALID</a> = 11
    <a name="XML_WITH_HTML" id="XML_WITH_HTML">XML_WITH_HTML</a> = 12
    <a name="XML_WITH_LEGACY" id="XML_WITH_LEGACY">XML_WITH_LEGACY</a> = 13
    <a name="XML_WITH_C14N" id="XML_WITH_C14N">XML_WITH_C14N</a> = 14
    <a name="XML_WITH_CATALOG" id="XML_WITH_CATALOG">XML_WITH_CATALOG</a> = 15
    <a name="XML_WITH_XPATH" id="XML_WITH_XPATH">XML_WITH_XPATH</a> = 16
    <a name="XML_WITH_XPTR" id="XML_WITH_XPTR">XML_WITH_XPTR</a> = 17
    <a name="XML_WITH_XINCLUDE" id="XML_WITH_XINCLUDE">XML_WITH_XINCLUDE</a> = 18
    <a name="XML_WITH_ICONV" id="XML_WITH_ICONV">XML_WITH_ICONV</a> = 19
    <a name="XML_WITH_ISO8859X" id="XML_WITH_ISO8859X">XML_WITH_ISO8859X</a> = 20
    <a name="XML_WITH_UNICODE" id="XML_WITH_UNICODE">XML_WITH_UNICODE</a> = 21
    <a name="XML_WITH_REGEXP" id="XML_WITH_REGEXP">XML_WITH_REGEXP</a> = 22
    <a name="XML_WITH_AUTOMATA" id="XML_WITH_AUTOMATA">XML_WITH_AUTOMATA</a> = 23
    <a name="XML_WITH_EXPR" id="XML_WITH_EXPR">XML_WITH_EXPR</a> = 24
    <a name="XML_WITH_SCHEMAS" id="XML_WITH_SCHEMAS">XML_WITH_SCHEMAS</a> = 25
    <a name="XML_WITH_SCHEMATRON" id="XML_WITH_SCHEMATRON">XML_WITH_SCHEMATRON</a> = 26
    <a name="XML_WITH_MODULES" id="XML_WITH_MODULES">XML_WITH_MODULES</a> = 27
    <a name="XML_WITH_DEBUG" id="XML_WITH_DEBUG">XML_WITH_DEBUG</a> = 28
    <a name="XML_WITH_DEBUG_MEM" id="XML_WITH_DEBUG_MEM">XML_WITH_DEBUG_MEM</a> = 29
    <a name="XML_WITH_DEBUG_RUN" id="XML_WITH_DEBUG_RUN">XML_WITH_DEBUG_RUN</a> = 30
    <a name="XML_WITH_ZLIB" id="XML_WITH_ZLIB">XML_WITH_ZLIB</a> = 31
    <a name="XML_WITH_ICU" id="XML_WITH_ICU">XML_WITH_ICU</a> = 32
    <a name="XML_WITH_LZMA" id="XML_WITH_LZMA">XML_WITH_LZMA</a> = 33
    <a name="XML_WITH_NONE" id="XML_WITH_NONE">XML_WITH_NONE</a> = 99999 : just to be sure of allocation size
}
</pre><h3>Enum <a name="xmlParserInputState" id="xmlParserInputState">xmlParserInputState</a></h3><pre class="programlisting">Enum xmlParserInputState {
    <a name="XML_PARSER_EOF" id="XML_PARSER_EOF">XML_PARSER_EOF</a> = -1 : nothing is to be parsed
    <a name="XML_PARSER_START" id="XML_PARSER_START">XML_PARSER_START</a> = 0 : nothing has been parsed
    <a name="XML_PARSER_MISC" id="XML_PARSER_MISC">XML_PARSER_MISC</a> = 1 : Misc* before int subset
    <a name="XML_PARSER_PI" id="XML_PARSER_PI">XML_PARSER_PI</a> = 2 : Within a processing instruction
    <a name="XML_PARSER_DTD" id="XML_PARSER_DTD">XML_PARSER_DTD</a> = 3 : within some DTD content
    <a name="XML_PARSER_PROLOG" id="XML_PARSER_PROLOG">XML_PARSER_PROLOG</a> = 4 : Misc* after internal subset
    <a name="XML_PARSER_COMMENT" id="XML_PARSER_COMMENT">XML_PARSER_COMMENT</a> = 5 : within a <a href="libxml-SAX.html#comment">comment</a>
    <a name="XML_PARSER_START_TAG" id="XML_PARSER_START_TAG">XML_PARSER_START_TAG</a> = 6 : within a start tag
    <a name="XML_PARSER_CONTENT" id="XML_PARSER_CONTENT">XML_PARSER_CONTENT</a> = 7 : within the content
    <a name="XML_PARSER_CDATA_SECTION" id="XML_PARSER_CDATA_SECTION">XML_PARSER_CDATA_SECTION</a> = 8 : within a CDATA section
    <a name="XML_PARSER_END_TAG" id="XML_PARSER_END_TAG">XML_PARSER_END_TAG</a> = 9 : within a closing tag
    <a name="XML_PARSER_ENTITY_DECL" id="XML_PARSER_ENTITY_DECL">XML_PARSER_ENTITY_DECL</a> = 10 : within an entity declaration
    <a name="XML_PARSER_ENTITY_VALUE" id="XML_PARSER_ENTITY_VALUE">XML_PARSER_ENTITY_VALUE</a> = 11 : within an entity value in a decl
    <a name="XML_PARSER_ATTRIBUTE_VALUE" id="XML_PARSER_ATTRIBUTE_VALUE">XML_PARSER_ATTRIBUTE_VALUE</a> = 12 : within an <a href="libxml-SAX.html#attribute">attribute</a> value
    <a name="XML_PARSER_SYSTEM_LITERAL" id="XML_PARSER_SYSTEM_LITERAL">XML_PARSER_SYSTEM_LITERAL</a> = 13 : within a SYSTEM value
    <a name="XML_PARSER_EPILOG" id="XML_PARSER_EPILOG">XML_PARSER_EPILOG</a> = 14 : the Misc* after the last end tag
    <a name="XML_PARSER_IGNORE" id="XML_PARSER_IGNORE">XML_PARSER_IGNORE</a> = 15 : within an IGNORED section
    <a name="XML_PARSER_PUBLIC_LITERAL" id="XML_PARSER_PUBLIC_LITERAL">XML_PARSER_PUBLIC_LITERAL</a> = 16 : within a PUBLIC value
}
</pre><h3>Enum <a name="xmlParserMode" id="xmlParserMode">xmlParserMode</a></h3><pre class="programlisting">Enum xmlParserMode {
    <a name="XML_PARSE_UNKNOWN" id="XML_PARSE_UNKNOWN">XML_PARSE_UNKNOWN</a> = 0
    <a name="XML_PARSE_DOM" id="XML_PARSE_DOM">XML_PARSE_DOM</a> = 1
    <a name="XML_PARSE_SAX" id="XML_PARSE_SAX">XML_PARSE_SAX</a> = 2
    <a name="XML_PARSE_PUSH_DOM" id="XML_PARSE_PUSH_DOM">XML_PARSE_PUSH_DOM</a> = 3
    <a name="XML_PARSE_PUSH_SAX" id="XML_PARSE_PUSH_SAX">XML_PARSE_PUSH_SAX</a> = 4
    <a name="XML_PARSE_READER" id="XML_PARSE_READER">XML_PARSE_READER</a> = 5
}
</pre><h3><a name="xmlParserNodeInfo" id="xmlParserNodeInfo">Structure xmlParserNodeInfo</a></h3><pre class="programlisting">Structure xmlParserNodeInfo<br />struct _xmlParserNodeInfo {
    const struct _xmlNode *	node	: Position &amp; line # that text that create
    unsigned long	begin_pos
    unsigned long	begin_line
    unsigned long	end_pos
    unsigned long	end_line
}</pre><h3><a name="xmlParserNodeInfoSeq" id="xmlParserNodeInfoSeq">Structure xmlParserNodeInfoSeq</a></h3><pre class="programlisting">Structure xmlParserNodeInfoSeq<br />struct _xmlParserNodeInfoSeq {
    unsigned long	maximum
    unsigned long	length
    <a href="libxml-parser.html#xmlParserNodeInfo">xmlParserNodeInfo</a> *	buffer
}</pre><h3>Enum <a name="xmlParserOption" id="xmlParserOption">xmlParserOption</a></h3><pre class="programlisting">Enum xmlParserOption {
    <a name="XML_PARSE_RECOVER" id="XML_PARSE_RECOVER">XML_PARSE_RECOVER</a> = 1 : recover on errors
    <a name="XML_PARSE_NOENT" id="XML_PARSE_NOENT">XML_PARSE_NOENT</a> = 2 : substitute entities
    <a name="XML_PARSE_DTDLOAD" id="XML_PARSE_DTDLOAD">XML_PARSE_DTDLOAD</a> = 4 : load the external subset
    <a name="XML_PARSE_DTDATTR" id="XML_PARSE_DTDATTR">XML_PARSE_DTDATTR</a> = 8 : default DTD attributes
    <a name="XML_PARSE_DTDVALID" id="XML_PARSE_DTDVALID">XML_PARSE_DTDVALID</a> = 16 : validate with the DTD
    <a name="XML_PARSE_NOERROR" id="XML_PARSE_NOERROR">XML_PARSE_NOERROR</a> = 32 : suppress error reports
    <a name="XML_PARSE_NOWARNING" id="XML_PARSE_NOWARNING">XML_PARSE_NOWARNING</a> = 64 : suppress warning reports
    <a name="XML_PARSE_PEDANTIC" id="XML_PARSE_PEDANTIC">XML_PARSE_PEDANTIC</a> = 128 : pedantic error reporting
    <a name="XML_PARSE_NOBLANKS" id="XML_PARSE_NOBLANKS">XML_PARSE_NOBLANKS</a> = 256 : remove blank nodes
    <a name="XML_PARSE_SAX1" id="XML_PARSE_SAX1">XML_PARSE_SAX1</a> = 512 : use the SAX1 interface internally
    <a name="XML_PARSE_XINCLUDE" id="XML_PARSE_XINCLUDE">XML_PARSE_XINCLUDE</a> = 1024 : Implement XInclude substitition
    <a name="XML_PARSE_NONET" id="XML_PARSE_NONET">XML_PARSE_NONET</a> = 2048 : Forbid network access
    <a name="XML_PARSE_NODICT" id="XML_PARSE_NODICT">XML_PARSE_NODICT</a> = 4096 : Do not reuse the context dictionary
    <a name="XML_PARSE_NSCLEAN" id="XML_PARSE_NSCLEAN">XML_PARSE_NSCLEAN</a> = 8192 : remove redundant namespaces declarations
    <a name="XML_PARSE_NOCDATA" id="XML_PARSE_NOCDATA">XML_PARSE_NOCDATA</a> = 16384 : merge CDATA as text nodes
    <a name="XML_PARSE_NOXINCNODE" id="XML_PARSE_NOXINCNODE">XML_PARSE_NOXINCNODE</a> = 32768 : do not generate XINCLUDE START/END nodes
    <a name="XML_PARSE_COMPACT" id="XML_PARSE_COMPACT">XML_PARSE_COMPACT</a> = 65536 : compact small text nodes; no modification of the tree allowed afterwards (will possibly crash if you try to modify the tree)
    <a name="XML_PARSE_OLD10" id="XML_PARSE_OLD10">XML_PARSE_OLD10</a> = 131072 : parse using XML-1.0 before update 5
    <a name="XML_PARSE_NOBASEFIX" id="XML_PARSE_NOBASEFIX">XML_PARSE_NOBASEFIX</a> = 262144 : do not fixup XINCLUDE xml:base uris
    <a name="XML_PARSE_HUGE" id="XML_PARSE_HUGE">XML_PARSE_HUGE</a> = 524288 : relax any hardcoded limit from the parser
    <a name="XML_PARSE_OLDSAX" id="XML_PARSE_OLDSAX">XML_PARSE_OLDSAX</a> = 1048576 : parse using SAX2 interface before 2.7.0
    <a name="XML_PARSE_IGNORE_ENC" id="XML_PARSE_IGNORE_ENC">XML_PARSE_IGNORE_ENC</a> = 2097152 : ignore internal document encoding hint
    <a name="XML_PARSE_BIG_LINES" id="XML_PARSE_BIG_LINES">XML_PARSE_BIG_LINES</a> = 4194304 : Store big lines numbers in text PSVI field
}
</pre><h3><a name="xmlSAXHandlerV1" id="xmlSAXHandlerV1">Structure xmlSAXHandlerV1</a></h3><pre class="programlisting">Structure xmlSAXHandlerV1<br />struct _xmlSAXHandlerV1 {
    <a href="libxml-parser.html#internalSubsetSAXFunc">internalSubsetSAXFunc</a>	internalSubset
    <a href="libxml-parser.html#isStandaloneSAXFunc">isStandaloneSAXFunc</a>	isStandalone
    <a href="libxml-parser.html#hasInternalSubsetSAXFunc">hasInternalSubsetSAXFunc</a>	hasInternalSubset
    <a href="libxml-parser.html#hasExternalSubsetSAXFunc">hasExternalSubsetSAXFunc</a>	hasExternalSubset
    <a href="libxml-parser.html#resolveEntitySAXFunc">resolveEntitySAXFunc</a>	resolveEntity
    <a href="libxml-parser.html#getEntitySAXFunc">getEntitySAXFunc</a>	getEntity
    <a href="libxml-parser.html#entityDeclSAXFunc">entityDeclSAXFunc</a>	entityDecl
    <a href="libxml-parser.html#notationDeclSAXFunc">notationDeclSAXFunc</a>	notationDecl
    <a href="libxml-parser.html#attributeDeclSAXFunc">attributeDeclSAXFunc</a>	attributeDecl
    <a href="libxml-parser.html#elementDeclSAXFunc">elementDeclSAXFunc</a>	elementDecl
    <a href="libxml-parser.html#unparsedEntityDeclSAXFunc">unparsedEntityDeclSAXFunc</a>	unparsedEntityDecl
    <a href="libxml-parser.html#setDocumentLocatorSAXFunc">setDocumentLocatorSAXFunc</a>	setDocumentLocator
    <a href="libxml-parser.html#startDocumentSAXFunc">startDocumentSAXFunc</a>	startDocument
    <a href="libxml-parser.html#endDocumentSAXFunc">endDocumentSAXFunc</a>	endDocument
    <a href="libxml-parser.html#startElementSAXFunc">startElementSAXFunc</a>	startElement
    <a href="libxml-parser.html#endElementSAXFunc">endElementSAXFunc</a>	endElement
    <a href="libxml-parser.html#referenceSAXFunc">referenceSAXFunc</a>	reference
    <a href="libxml-parser.html#charactersSAXFunc">charactersSAXFunc</a>	characters
    <a href="libxml-parser.html#ignorableWhitespaceSAXFunc">ignorableWhitespaceSAXFunc</a>	ignorableWhitespace
    <a href="libxml-parser.html#processingInstructionSAXFunc">processingInstructionSAXFunc</a>	processingInstruction
    <a href="libxml-parser.html#commentSAXFunc">commentSAXFunc</a>	comment
    <a href="libxml-parser.html#warningSAXFunc">warningSAXFunc</a>	warning
    <a href="libxml-parser.html#errorSAXFunc">errorSAXFunc</a>	error
    <a href="libxml-parser.html#fatalErrorSAXFunc">fatalErrorSAXFunc</a>	fatalError	: unused error() get all the errors
    <a href="libxml-parser.html#getParameterEntitySAXFunc">getParameterEntitySAXFunc</a>	getParameterEntity
    <a href="libxml-parser.html#cdataBlockSAXFunc">cdataBlockSAXFunc</a>	cdataBlock
    <a href="libxml-parser.html#externalSubsetSAXFunc">externalSubsetSAXFunc</a>	externalSubset
    unsigned int	initialized
}</pre><h3><a name="attributeDeclSAXFunc" id="attributeDeclSAXFunc"></a>Function type: attributeDeclSAXFunc</h3><pre class="programlisting">Function type: attributeDeclSAXFunc
void	attributeDeclSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * elem, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 int type, <br />					 int def, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * defaultValue, <br />					 <a href="libxml-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> tree)
</pre><p>An <a href="libxml-SAX.html#attribute">attribute</a> definition has been parsed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>the name of the element</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> type</td></tr><tr><td><span class="term"><i><tt>def</tt></i>:</span></td><td>the type of default value</td></tr><tr><td><span class="term"><i><tt>defaultValue</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> default value</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>the tree of enumerated value set</td></tr></tbody></table></div><br />
<h3><a name="attributeSAXFunc" id="attributeSAXFunc"></a>Function type: attributeSAXFunc</h3><pre class="programlisting">Function type: attributeSAXFunc
void	attributeSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)
</pre><p>Handle an <a href="libxml-SAX.html#attribute">attribute</a> that has been read by the parser. The default handling is to convert the <a href="libxml-SAX.html#attribute">attribute</a> into an DOM subtree and past it in a new <a href="libxml-tree.html#xmlAttr">xmlAttr</a> element added to the element.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The <a href="libxml-SAX.html#attribute">attribute</a> name, including namespace prefix</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>The <a href="libxml-SAX.html#attribute">attribute</a> value</td></tr></tbody></table></div><br />
<h3><a name="cdataBlockSAXFunc" id="cdataBlockSAXFunc"></a>Function type: cdataBlockSAXFunc</h3><pre class="programlisting">Function type: cdataBlockSAXFunc
void	cdataBlockSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int len)
</pre><p>Called when a pcdata block has been parsed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>The pcdata content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the block length</td></tr></tbody></table></div><br />
<h3><a name="charactersSAXFunc" id="charactersSAXFunc"></a>Function type: charactersSAXFunc</h3><pre class="programlisting">Function type: charactersSAXFunc
void	charactersSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)
</pre><p>Receiving some chars from the parser.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr></tbody></table></div><br />
<h3><a name="commentSAXFunc" id="commentSAXFunc"></a>Function type: commentSAXFunc</h3><pre class="programlisting">Function type: commentSAXFunc
void	commentSAXFunc			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)
</pre><p>A <a href="libxml-SAX.html#comment">comment</a> has been parsed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the <a href="libxml-SAX.html#comment">comment</a> content</td></tr></tbody></table></div><br />
<h3><a name="elementDeclSAXFunc" id="elementDeclSAXFunc"></a>Function type: elementDeclSAXFunc</h3><pre class="programlisting">Function type: elementDeclSAXFunc
void	elementDeclSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 <a href="libxml-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> content)
</pre><p>An element definition has been parsed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the element name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the element type</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the element value tree</td></tr></tbody></table></div><br />
<h3><a name="endDocumentSAXFunc" id="endDocumentSAXFunc"></a>Function type: endDocumentSAXFunc</h3><pre class="programlisting">Function type: endDocumentSAXFunc
void	endDocumentSAXFunc		(void * ctx)
</pre><p>Called when the document end has been detected.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr></tbody></table></div><br />
<h3><a name="endElementNsSAX2Func" id="endElementNsSAX2Func"></a>Function type: endElementNsSAX2Func</h3><pre class="programlisting">Function type: endElementNsSAX2Func
void	endElementNsSAX2Func		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)
</pre><p>SAX2 callback when an element end has been detected by the parser. It provides the namespace informations for the element.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>localname</tt></i>:</span></td><td>the local name of the element</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the element namespace prefix if available</td></tr><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the element namespace name if available</td></tr></tbody></table></div><br />
<h3><a name="endElementSAXFunc" id="endElementSAXFunc"></a>Function type: endElementSAXFunc</h3><pre class="programlisting">Function type: endElementSAXFunc
void	endElementSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre><p>Called when the end of an element has been detected.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The element name</td></tr></tbody></table></div><br />
<h3><a name="entityDeclSAXFunc" id="entityDeclSAXFunc"></a>Function type: entityDeclSAXFunc</h3><pre class="programlisting">Function type: entityDeclSAXFunc
void	entityDeclSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)
</pre><p>An entity definition has been parsed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the entity type</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the entity value (without processing).</td></tr></tbody></table></div><br />
<h3><a name="errorSAXFunc" id="errorSAXFunc"></a>Function type: errorSAXFunc</h3><pre class="programlisting">Function type: errorSAXFunc
void	errorSAXFunc			(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre><p>Display and format an error messages, callback.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message to display/transmit</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the message display</td></tr></tbody></table></div><br />
<h3><a name="externalSubsetSAXFunc" id="externalSubsetSAXFunc"></a>Function type: externalSubsetSAXFunc</h3><pre class="programlisting">Function type: externalSubsetSAXFunc
void	externalSubsetSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)
</pre><p>Callback on external subset declaration.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the root element name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the SYSTEM ID (e.g. filename or URL)</td></tr></tbody></table></div><br />
<h3><a name="fatalErrorSAXFunc" id="fatalErrorSAXFunc"></a>Function type: fatalErrorSAXFunc</h3><pre class="programlisting">Function type: fatalErrorSAXFunc
void	fatalErrorSAXFunc		(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre><p>Display and format fatal error messages, callback. Note: so far fatalError() SAX callbacks are not used, error() get all the callbacks for errors.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message to display/transmit</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the message display</td></tr></tbody></table></div><br />
<h3><a name="getEntitySAXFunc" id="getEntitySAXFunc"></a>Function type: getEntitySAXFunc</h3><pre class="programlisting">Function type: getEntitySAXFunc
<a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	getEntitySAXFunc	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre><p>Get an entity by name.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found.</td></tr></tbody></table></div><br />
<h3><a name="getParameterEntitySAXFunc" id="getParameterEntitySAXFunc"></a>Function type: getParameterEntitySAXFunc</h3><pre class="programlisting">Function type: getParameterEntitySAXFunc
<a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	getParameterEntitySAXFunc	(void * ctx, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre><p>Get a parameter entity by name.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found.</td></tr></tbody></table></div><br />
<h3><a name="hasExternalSubsetSAXFunc" id="hasExternalSubsetSAXFunc"></a>Function type: hasExternalSubsetSAXFunc</h3><pre class="programlisting">Function type: hasExternalSubsetSAXFunc
int	hasExternalSubsetSAXFunc	(void * ctx)
</pre><p>Does this document has an external subset?</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><br />
<h3><a name="hasInternalSubsetSAXFunc" id="hasInternalSubsetSAXFunc"></a>Function type: hasInternalSubsetSAXFunc</h3><pre class="programlisting">Function type: hasInternalSubsetSAXFunc
int	hasInternalSubsetSAXFunc	(void * ctx)
</pre><p>Does this document has an internal subset.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><br />
<h3><a name="ignorableWhitespaceSAXFunc" id="ignorableWhitespaceSAXFunc"></a>Function type: ignorableWhitespaceSAXFunc</h3><pre class="programlisting">Function type: ignorableWhitespaceSAXFunc
void	ignorableWhitespaceSAXFunc	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)
</pre><p>Receiving some ignorable whitespaces from the parser. UNUSED: by default the DOM building will use characters.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr></tbody></table></div><br />
<h3><a name="internalSubsetSAXFunc" id="internalSubsetSAXFunc"></a>Function type: internalSubsetSAXFunc</h3><pre class="programlisting">Function type: internalSubsetSAXFunc
void	internalSubsetSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)
</pre><p>Callback on internal subset declaration.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the root element name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the SYSTEM ID (e.g. filename or URL)</td></tr></tbody></table></div><br />
<h3><a name="isStandaloneSAXFunc" id="isStandaloneSAXFunc"></a>Function type: isStandaloneSAXFunc</h3><pre class="programlisting">Function type: isStandaloneSAXFunc
int	isStandaloneSAXFunc		(void * ctx)
</pre><p>Is this document tagged standalone?</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><br />
<h3><a name="notationDeclSAXFunc" id="notationDeclSAXFunc"></a>Function type: notationDeclSAXFunc</h3><pre class="programlisting">Function type: notationDeclSAXFunc
void	notationDeclSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)
</pre><p>What to do when a notation declaration has been parsed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The name of the notation</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr></tbody></table></div><br />
<h3><a name="processingInstructionSAXFunc" id="processingInstructionSAXFunc"></a>Function type: processingInstructionSAXFunc</h3><pre class="programlisting">Function type: processingInstructionSAXFunc
void	processingInstructionSAXFunc	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * data)
</pre><p>A processing instruction has been parsed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>the target name</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the PI data's</td></tr></tbody></table></div><br />
<h3><a name="referenceSAXFunc" id="referenceSAXFunc"></a>Function type: referenceSAXFunc</h3><pre class="programlisting">Function type: referenceSAXFunc
void	referenceSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)
</pre><p>Called when an entity <a href="libxml-SAX.html#reference">reference</a> is detected.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr></tbody></table></div><br />
<h3><a name="resolveEntitySAXFunc" id="resolveEntitySAXFunc"></a>Function type: resolveEntitySAXFunc</h3><pre class="programlisting">Function type: resolveEntitySAXFunc
<a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	resolveEntitySAXFunc	(void * ctx, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)
</pre><p>Callback: The entity loader, to control the loading of external entities, the application can either: - override this resolveEntity() callback in the SAX block - or better use the xmlSetExternalEntityLoader() function to set up it's own entity resolution routine</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> if inlined or NULL for DOM behaviour.</td></tr></tbody></table></div><br />
<h3><a name="setDocumentLocatorSAXFunc" id="setDocumentLocatorSAXFunc"></a>Function type: setDocumentLocatorSAXFunc</h3><pre class="programlisting">Function type: setDocumentLocatorSAXFunc
void	setDocumentLocatorSAXFunc	(void * ctx, <br />					 <a href="libxml-tree.html#xmlSAXLocatorPtr">xmlSAXLocatorPtr</a> loc)
</pre><p>Receive the document locator at startup, actually xmlDefaultSAXLocator. Everything is available on the context, so this is useless in our case.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>loc</tt></i>:</span></td><td>A SAX Locator</td></tr></tbody></table></div><br />
<h3><a name="startDocumentSAXFunc" id="startDocumentSAXFunc"></a>Function type: startDocumentSAXFunc</h3><pre class="programlisting">Function type: startDocumentSAXFunc
void	startDocumentSAXFunc		(void * ctx)
</pre><p>Called when the document start being processed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr></tbody></table></div><br />
<h3><a name="startElementNsSAX2Func" id="startElementNsSAX2Func"></a>Function type: startElementNsSAX2Func</h3><pre class="programlisting">Function type: startElementNsSAX2Func
void	startElementNsSAX2Func		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 int nb_namespaces, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** namespaces, <br />					 int nb_attributes, <br />					 int nb_defaulted, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** attributes)
</pre><p>SAX2 callback when an element start has been detected by the parser. It provides the namespace informations for the element, as well as the new namespace declarations on the element.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>localname</tt></i>:</span></td><td>the local name of the element</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the element namespace prefix if available</td></tr><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the element namespace name if available</td></tr><tr><td><span class="term"><i><tt>nb_namespaces</tt></i>:</span></td><td>number of namespace definitions on that node</td></tr><tr><td><span class="term"><i><tt>namespaces</tt></i>:</span></td><td>pointer to the array of prefix/URI pairs namespace definitions</td></tr><tr><td><span class="term"><i><tt>nb_attributes</tt></i>:</span></td><td>the number of attributes on that node</td></tr><tr><td><span class="term"><i><tt>nb_defaulted</tt></i>:</span></td><td>the number of defaulted attributes. The defaulted ones are at the end of the array</td></tr><tr><td><span class="term"><i><tt>attributes</tt></i>:</span></td><td>pointer to the array of (localname/prefix/URI/value/end) <a href="libxml-SAX.html#attribute">attribute</a> values.</td></tr></tbody></table></div><br />
<h3><a name="startElementSAXFunc" id="startElementSAXFunc"></a>Function type: startElementSAXFunc</h3><pre class="programlisting">Function type: startElementSAXFunc
void	startElementSAXFunc		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** atts)
</pre><p>Called when an opening tag has been processed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The element name, including namespace prefix</td></tr><tr><td><span class="term"><i><tt>atts</tt></i>:</span></td><td>An array of name/value attributes pairs, NULL terminated</td></tr></tbody></table></div><br />
<h3><a name="unparsedEntityDeclSAXFunc" id="unparsedEntityDeclSAXFunc"></a>Function type: unparsedEntityDeclSAXFunc</h3><pre class="programlisting">Function type: unparsedEntityDeclSAXFunc
void	unparsedEntityDeclSAXFunc	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * notationName)
</pre><p>What to do when an unparsed entity declaration is parsed.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The name of the entity</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>notationName</tt></i>:</span></td><td>the name of the notation</td></tr></tbody></table></div><br />
<h3><a name="warningSAXFunc" id="warningSAXFunc"></a>Function type: warningSAXFunc</h3><pre class="programlisting">Function type: warningSAXFunc
void	warningSAXFunc			(void * ctx, <br />					 const char * msg, <br />					 ... ...)
</pre><p>Display and format a warning messages, callback.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message to display/transmit</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the message display</td></tr></tbody></table></div><br />
<h3><a name="xmlByteConsumed" id="xmlByteConsumed"></a>Function: xmlByteConsumed</h3><pre class="programlisting">long	xmlByteConsumed			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>This function provides the current index of the parser relative to the start of the current entity. This function is computed in bytes from the beginning starting at zero and finishing at the size in byte of the file if parsing a file. The function is of constant cost if the input is UTF-8 but can be costly if run on non-UTF-8 input.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the index in bytes from the beginning of the entity or -1 in case the index could not be computed.</td></tr></tbody></table></div><h3><a name="xmlCleanupParser" id="xmlCleanupParser"></a>Function: xmlCleanupParser</h3><pre class="programlisting">void	xmlCleanupParser		(void)<br />
</pre><p>This function name is somewhat misleading. It does not clean up parser state, it cleans up memory allocated by the library itself. It is a cleanup function for the XML library. It tries to reclaim all related global memory allocated for the library processing. It doesn't deallocate any document related memory. One should call xmlCleanupParser() only when the process has finished using the library and all XML/HTML documents built with it. See also xmlInitParser() which has the opposite function of preparing the library for operations. WARNING: if your application is multithreaded or has plugin support calling this may crash the application if another thread or a plugin is still using libxml2. It's sometimes very hard to guess if libxml2 is in use in the application, some libraries or plugins may use it without notice. In case of doubt abstain from calling this function or do it just before calling exit() to avoid leak reports from valgrind !</p>
<h3><a name="xmlClearNodeInfoSeq" id="xmlClearNodeInfoSeq"></a>Function: xmlClearNodeInfoSeq</h3><pre class="programlisting">void	xmlClearNodeInfoSeq		(<a href="libxml-parser.html#xmlParserNodeInfoSeqPtr">xmlParserNodeInfoSeqPtr</a> seq)<br />
</pre><p>-- Clear (release memory and reinitialize) node info sequence</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>seq</tt></i>:</span></td><td>a node info sequence pointer</td></tr></tbody></table></div><h3><a name="xmlClearParserCtxt" id="xmlClearParserCtxt"></a>Function: xmlClearParserCtxt</h3><pre class="programlisting">void	xmlClearParserCtxt		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>Clear (release owned resources) and reinitialize a parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div><h3><a name="xmlCreateDocParserCtxt" id="xmlCreateDocParserCtxt"></a>Function: xmlCreateDocParserCtxt</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlCreateDocParserCtxt	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur)<br />
</pre><p>Creates a parser context for an XML in-memory document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to an array of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div><h3><a name="xmlCreateIOParserCtxt" id="xmlCreateIOParserCtxt"></a>Function: xmlCreateIOParserCtxt</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlCreateIOParserCtxt	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />						 void * user_data, <br />						 <a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />						 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />						 void * ioctx, <br />						 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br />
</pre><p>Create a parser context for using the XML parser with an existing I/O stream</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>a SAX handler</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>The user data returned on SAX callbacks</td></tr><tr><td><span class="term"><i><tt>ioread</tt></i>:</span></td><td>an I/O read function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div><h3><a name="xmlCreatePushParserCtxt" id="xmlCreatePushParserCtxt"></a>Function: xmlCreatePushParserCtxt</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlCreatePushParserCtxt	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />						 void * user_data, <br />						 const char * chunk, <br />						 int size, <br />						 const char * filename)<br />
</pre><p>Create a parser context for using the XML parser in push mode. If @buffer and @size are non-NULL, the data is used to detect the encoding. The remaining <a href="libxml-SAX.html#characters">characters</a> will be parsed so they don't need to be fed in again through xmlParseChunk. To allow content encoding detection, @size should be &gt;= 4 The value of @filename is used for fetching external entities and error/warning reports.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>a SAX handler</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>The user data returned on SAX callbacks</td></tr><tr><td><span class="term"><i><tt>chunk</tt></i>:</span></td><td>a pointer to an array of chars</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>number of chars in the array</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>an optional file name or URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div><h3><a name="xmlCtxtReadDoc" id="xmlCtxtReadDoc"></a>Function: xmlCtxtReadDoc</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlCtxtReadDoc		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML in-memory document and build a tree. This reuses the existing @ctxt parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to a zero terminated string</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlCtxtReadFd" id="xmlCtxtReadFd"></a>Function: xmlCtxtReadFd</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlCtxtReadFd		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 int fd, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML from a file descriptor and build a tree. This reuses the existing @ctxt parser context NOTE that the file descriptor will not be closed when the reader is closed or reset.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>fd</tt></i>:</span></td><td>an open file descriptor</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlCtxtReadFile" id="xmlCtxtReadFile"></a>Function: xmlCtxtReadFile</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlCtxtReadFile		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * filename, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML file from the filesystem or the network. This reuses the existing @ctxt parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file or URL</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlCtxtReadIO" id="xmlCtxtReadIO"></a>Function: xmlCtxtReadIO</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlCtxtReadIO		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 <a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />					 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML document from I/O functions and source and build a tree. This reuses the existing @ctxt parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>ioread</tt></i>:</span></td><td>an I/O read function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlCtxtReadMemory" id="xmlCtxtReadMemory"></a>Function: xmlCtxtReadMemory</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlCtxtReadMemory	(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * buffer, <br />					 int size, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML in-memory document and build a tree. This reuses the existing @ctxt parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlCtxtReset" id="xmlCtxtReset"></a>Function: xmlCtxtReset</h3><pre class="programlisting">void	xmlCtxtReset			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>Reset a parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div><h3><a name="xmlCtxtResetPush" id="xmlCtxtResetPush"></a>Function: xmlCtxtResetPush</h3><pre class="programlisting">int	xmlCtxtResetPush		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * chunk, <br />					 int size, <br />					 const char * filename, <br />					 const char * encoding)<br />
</pre><p>Reset a push parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>chunk</tt></i>:</span></td><td>a pointer to an array of chars</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>number of chars in the array</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>an optional file name or URI</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and 1 in case of error</td></tr></tbody></table></div><h3><a name="xmlCtxtUseOptions" id="xmlCtxtUseOptions"></a>Function: xmlCtxtUseOptions</h3><pre class="programlisting">int	xmlCtxtUseOptions		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 int options)<br />
</pre><p>Applies the options to the parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, the set of unknown or unimplemented options in case of error.</td></tr></tbody></table></div><h3><a name="xmlExternalEntityLoader" id="xmlExternalEntityLoader"></a>Function type: xmlExternalEntityLoader</h3><pre class="programlisting">Function type: xmlExternalEntityLoader
<a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlExternalEntityLoader	(const char * URL, <br />						 const char * ID, <br />						 <a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> context)
</pre><p>External entity loaders types.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The System ID of the resource requested</td></tr><tr><td><span class="term"><i><tt>ID</tt></i>:</span></td><td>The Public ID of the resource requested</td></tr><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>the XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the entity input parser.</td></tr></tbody></table></div><br />
<h3><a name="xmlFreeParserCtxt" id="xmlFreeParserCtxt"></a>Function: xmlFreeParserCtxt</h3><pre class="programlisting">void	xmlFreeParserCtxt		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>Free all the memory used by a parser context. However the parsed document in ctxt-&gt;myDoc is not freed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div><h3><a name="xmlGetExternalEntityLoader" id="xmlGetExternalEntityLoader"></a>Function: xmlGetExternalEntityLoader</h3><pre class="programlisting"><a href="libxml-parser.html#xmlExternalEntityLoader">xmlExternalEntityLoader</a>	xmlGetExternalEntityLoader	(void)<br />
</pre><p>Get the default external entity resolver function for the application</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-parser.html#xmlExternalEntityLoader">xmlExternalEntityLoader</a> function pointer</td></tr></tbody></table></div><h3><a name="xmlGetFeature" id="xmlGetFeature"></a>Function: xmlGetFeature</h3><pre class="programlisting">int	xmlGetFeature			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * name, <br />					 void * result)<br />
</pre><p>Read the current value of one feature of this parser instance</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML/HTML parser context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the feature name</td></tr><tr><td><span class="term"><i><tt>result</tt></i>:</span></td><td>location to store the result</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case or error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlGetFeaturesList" id="xmlGetFeaturesList"></a>Function: xmlGetFeaturesList</h3><pre class="programlisting">int	xmlGetFeaturesList		(int * len, <br />					 const char ** result)<br />
</pre><p>Copy at most *@len feature names into the @result array</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of the features name array (input/output)</td></tr><tr><td><span class="term"><i><tt>result</tt></i>:</span></td><td>an array of string to be filled with the features name.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case or error, or the total number of features, len is updated with the number of strings copied, strings must not be deallocated</td></tr></tbody></table></div><h3><a name="xmlHasFeature" id="xmlHasFeature"></a>Function: xmlHasFeature</h3><pre class="programlisting">int	xmlHasFeature			(<a href="libxml-parser.html#xmlFeature">xmlFeature</a> feature)<br />
</pre><p>Examines if the library has been compiled with a given feature.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>feature</tt></i>:</span></td><td>the feature to be examined</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a non-zero value if the feature exist, otherwise zero. Returns zero (0) if the feature does not exist or an unknown unknown feature is requested, non-zero otherwise.</td></tr></tbody></table></div><h3><a name="xmlIOParseDTD" id="xmlIOParseDTD"></a>Function: xmlIOParseDTD</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	xmlIOParseDTD		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />					 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br />
</pre><p>Load and parse a DTD</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block or NULL</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>an Input Buffer</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting <a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> or NULL in case of error. @input will be freed by the function in any case.</td></tr></tbody></table></div><h3><a name="xmlInitNodeInfoSeq" id="xmlInitNodeInfoSeq"></a>Function: xmlInitNodeInfoSeq</h3><pre class="programlisting">void	xmlInitNodeInfoSeq		(<a href="libxml-parser.html#xmlParserNodeInfoSeqPtr">xmlParserNodeInfoSeqPtr</a> seq)<br />
</pre><p>-- Initialize (set to initial state) node info sequence</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>seq</tt></i>:</span></td><td>a node info sequence pointer</td></tr></tbody></table></div><h3><a name="xmlInitParser" id="xmlInitParser"></a>Function: xmlInitParser</h3><pre class="programlisting">void	xmlInitParser			(void)<br />
</pre><p>Initialization function for the XML parser. This is not reentrant. Call once before processing in case of use in multithreaded programs.</p>
<h3><a name="xmlInitParserCtxt" id="xmlInitParserCtxt"></a>Function: xmlInitParserCtxt</h3><pre class="programlisting">int	xmlInitParserCtxt		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>Initialize a parser context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlKeepBlanksDefault" id="xmlKeepBlanksDefault"></a>Function: xmlKeepBlanksDefault</h3><pre class="programlisting">int	xmlKeepBlanksDefault		(int val)<br />
</pre><p>Set and return the previous value for default blanks text nodes support. The 1.x version of the parser used an heuristic to try to detect ignorable white spaces. As a result the SAX callback was generating xmlSAX2IgnorableWhitespace() callbacks instead of characters() one, and when using the DOM output text nodes containing those blanks were not generated. The 2.x and later version will switch to the XML standard way and ignorableWhitespace() are only generated when running the parser in validating mode and when the current element doesn't allow CDATA or mixed content. This function is provided as a way to force the standard behavior on 1.X libs and to switch back to the old mode for compatibility when running 1.X client code on 2.X . Upgrade of 1.X code should be done by using xmlIsBlankNode() commodity function to detect the "empty" nodes generated. This value also affect autogeneration of indentation when saving code if blanks sections are kept, indentation is not generated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>int 0 or 1</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the last value for 0 for no substitution, 1 for substitution.</td></tr></tbody></table></div><h3><a name="xmlLineNumbersDefault" id="xmlLineNumbersDefault"></a>Function: xmlLineNumbersDefault</h3><pre class="programlisting">int	xmlLineNumbersDefault		(int val)<br />
</pre><p>Set and return the previous value for enabling line numbers in elements contents. This may break on old application and is turned off by default.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>int 0 or 1</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the last value for 0 for no substitution, 1 for substitution.</td></tr></tbody></table></div><h3><a name="xmlLoadExternalEntity" id="xmlLoadExternalEntity"></a>Function: xmlLoadExternalEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlLoadExternalEntity	(const char * URL, <br />						 const char * ID, <br />						 <a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>Load an external entity, note that the use of this function for unparsed entities may generate problems</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the URL for the entity to load</td></tr><tr><td><span class="term"><i><tt>ID</tt></i>:</span></td><td>the Public ID for the entity to load</td></tr><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the context in which the entity is called or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> or NULL</td></tr></tbody></table></div><h3><a name="xmlNewIOInputStream" id="xmlNewIOInputStream"></a>Function: xmlNewIOInputStream</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlNewIOInputStream	(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />						 <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> input, <br />						 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br />
</pre><p>Create a new input stream structure encapsulating the @input into a stream suitable for the parser.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>an I/O Input</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new input stream or NULL</td></tr></tbody></table></div><h3><a name="xmlNewParserCtxt" id="xmlNewParserCtxt"></a>Function: xmlNewParserCtxt</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlNewParserCtxt	(void)<br />
</pre><p>Allocate and initialize a new parser context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> or NULL</td></tr></tbody></table></div><h3><a name="xmlParseBalancedChunkMemory" id="xmlParseBalancedChunkMemory"></a>Function: xmlParseBalancedChunkMemory</h3><pre class="programlisting">int	xmlParseBalancedChunkMemory	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data, <br />					 int depth, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst)<br />
</pre><p>Parse a well-balanced chunk of an XML document called by the parser The allowed sequence for the Well Balanced Chunk is the one defined by the content production in the XML grammar: [43] content ::= (element | CharData | Reference | CDSect | PI | Comment)*</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document the chunk pertains to</td></tr><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler bloc (possibly NULL)</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>The user data returned on SAX callbacks (possibly NULL)</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>Used for loop detection, use 0</td></tr><tr><td><span class="term"><i><tt>string</tt></i>:</span></td><td>the input string in UTF8 or ISO-Latin (zero terminated)</td></tr><tr><td><span class="term"><i><tt>lst</tt></i>:</span></td><td>the return value for the set of parsed nodes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the chunk is well balanced, -1 in case of args problem and the parser error code otherwise</td></tr></tbody></table></div><h3><a name="xmlParseBalancedChunkMemoryRecover" id="xmlParseBalancedChunkMemoryRecover"></a>Function: xmlParseBalancedChunkMemoryRecover</h3><pre class="programlisting">int	xmlParseBalancedChunkMemoryRecover	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />						 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />						 void * user_data, <br />						 int depth, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst, <br />						 int recover)<br />
</pre><p>Parse a well-balanced chunk of an XML document called by the parser The allowed sequence for the Well Balanced Chunk is the one defined by the content production in the XML grammar: [43] content ::= (element | CharData | Reference | CDSect | PI | Comment)*</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document the chunk pertains to</td></tr><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler bloc (possibly NULL)</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>The user data returned on SAX callbacks (possibly NULL)</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>Used for loop detection, use 0</td></tr><tr><td><span class="term"><i><tt>string</tt></i>:</span></td><td>the input string in UTF8 or ISO-Latin (zero terminated)</td></tr><tr><td><span class="term"><i><tt>lst</tt></i>:</span></td><td>the return value for the set of parsed nodes</td></tr><tr><td><span class="term"><i><tt>recover</tt></i>:</span></td><td>return nodes even if the data is broken (use 0)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the chunk is well balanced, -1 in case of args problem and the parser error code otherwise In case recover is set to 1, the nodelist will not be empty even if the parsed chunk is not well balanced, assuming the parsing succeeded to some extent.</td></tr></tbody></table></div><h3><a name="xmlParseChunk" id="xmlParseChunk"></a>Function: xmlParseChunk</h3><pre class="programlisting">int	xmlParseChunk			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * chunk, <br />					 int size, <br />					 int terminate)<br />
</pre><p>Parse a Chunk of memory</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>chunk</tt></i>:</span></td><td>an char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size in byte of the chunk</td></tr><tr><td><span class="term"><i><tt>terminate</tt></i>:</span></td><td>last chunk indicator</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>zero if no error, the <a href="libxml-xmlerror.html#xmlParserErrors">xmlParserErrors</a> otherwise.</td></tr></tbody></table></div><h3><a name="xmlParseCtxtExternalEntity" id="xmlParseCtxtExternalEntity"></a>Function: xmlParseCtxtExternalEntity</h3><pre class="programlisting">int	xmlParseCtxtExternalEntity	(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URL, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ID, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst)<br />
</pre><p>Parse an external general entity within an existing parsing context An external general parsed entity is well-formed if it matches the production labeled extParsedEnt. [78] extParsedEnt ::= TextDecl? content</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the existing parsing context</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the URL for the entity to load</td></tr><tr><td><span class="term"><i><tt>ID</tt></i>:</span></td><td>the System ID for the entity to load</td></tr><tr><td><span class="term"><i><tt>lst</tt></i>:</span></td><td>the return value for the set of parsed nodes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the entity is well formed, -1 in case of args problem and the parser error code otherwise</td></tr></tbody></table></div><h3><a name="xmlParseDTD" id="xmlParseDTD"></a>Function: xmlParseDTD</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	xmlParseDTD		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br />
</pre><p>Load and parse an external subset.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>a NAME* containing the External ID of the DTD</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>a NAME* containing the URL to the DTD</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting <a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlParseDoc" id="xmlParseDoc"></a>Function: xmlParseDoc</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlParseDoc		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur)<br />
</pre><p>parse an XML in-memory document and build a tree.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to an array of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlParseDocument" id="xmlParseDocument"></a>Function: xmlParseDocument</h3><pre class="programlisting">int	xmlParseDocument		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>parse an XML document (and build a tree if using the standard SAX interface). [1] document ::= prolog element Misc* [22] prolog ::= XMLDecl? Misc* (doctypedecl Misc*)?</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0, -1 in case of error. the parser context is augmented as a result of the parsing.</td></tr></tbody></table></div><h3><a name="xmlParseEntity" id="xmlParseEntity"></a>Function: xmlParseEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlParseEntity		(const char * filename)<br />
</pre><p>parse an XML external entity out of context and build a tree. [78] extParsedEnt ::= TextDecl? content This correspond to a "Well Balanced" chunk</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlParseExtParsedEnt" id="xmlParseExtParsedEnt"></a>Function: xmlParseExtParsedEnt</h3><pre class="programlisting">int	xmlParseExtParsedEnt		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>parse a general parsed entity An external general parsed entity is well-formed if it matches the production labeled extParsedEnt. [78] extParsedEnt ::= TextDecl? content</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0, -1 in case of error. the parser context is augmented as a result of the parsing.</td></tr></tbody></table></div><h3><a name="xmlParseExternalEntity" id="xmlParseExternalEntity"></a>Function: xmlParseExternalEntity</h3><pre class="programlisting">int	xmlParseExternalEntity		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data, <br />					 int depth, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URL, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ID, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst)<br />
</pre><p>Parse an external general entity An external general parsed entity is well-formed if it matches the production labeled extParsedEnt. [78] extParsedEnt ::= TextDecl? content</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document the chunk pertains to</td></tr><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler bloc (possibly NULL)</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>The user data returned on SAX callbacks (possibly NULL)</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>Used for loop detection, use 0</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the URL for the entity to load</td></tr><tr><td><span class="term"><i><tt>ID</tt></i>:</span></td><td>the System ID for the entity to load</td></tr><tr><td><span class="term"><i><tt>lst</tt></i>:</span></td><td>the return value for the set of parsed nodes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the entity is well formed, -1 in case of args problem and the parser error code otherwise</td></tr></tbody></table></div><h3><a name="xmlParseFile" id="xmlParseFile"></a>Function: xmlParseFile</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlParseFile		(const char * filename)<br />
</pre><p>parse an XML file and build a tree. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree if the file was wellformed, NULL otherwise.</td></tr></tbody></table></div><h3><a name="xmlParseInNodeContext" id="xmlParseInNodeContext"></a>Function: xmlParseInNodeContext</h3><pre class="programlisting"><a href="libxml-xmlerror.html#xmlParserErrors">xmlParserErrors</a>	xmlParseInNodeContext	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const char * data, <br />					 int datalen, <br />					 int options, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * lst)<br />
</pre><p>Parse a well-balanced chunk of an XML document within the context (DTD, namespaces, etc ...) of the given node. The allowed sequence for the data is a Well Balanced Chunk defined by the content production in the XML grammar: [43] content ::= (element | CharData | Reference | CDSect | PI | Comment)*</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the context node</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the input string</td></tr><tr><td><span class="term"><i><tt>datalen</tt></i>:</span></td><td>the input string length in bytes</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>lst</tt></i>:</span></td><td>the return value for the set of parsed nodes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td><a href="libxml-xmlerror.html#XML_ERR_OK">XML_ERR_OK</a> if the chunk is well balanced, and the parser error code otherwise</td></tr></tbody></table></div><h3><a name="xmlParseMemory" id="xmlParseMemory"></a>Function: xmlParseMemory</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlParseMemory		(const char * buffer, <br />					 int size)<br />
</pre><p>parse an XML in-memory block and build a tree.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>an pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlParserAddNodeInfo" id="xmlParserAddNodeInfo"></a>Function: xmlParserAddNodeInfo</h3><pre class="programlisting">void	xmlParserAddNodeInfo		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const <a href="libxml-parser.html#xmlParserNodeInfoPtr">xmlParserNodeInfoPtr</a> info)<br />
</pre><p>Insert node info record into the sorted sequence</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>info</tt></i>:</span></td><td>a node info sequence pointer</td></tr></tbody></table></div><h3><a name="xmlParserFindNodeInfo" id="xmlParserFindNodeInfo"></a>Function: xmlParserFindNodeInfo</h3><pre class="programlisting">const <a href="libxml-parser.html#xmlParserNodeInfo">xmlParserNodeInfo</a> *	xmlParserFindNodeInfo	(const <a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctx, <br />							 const <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Find the parser node info struct for a given node</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>an XML node within the tree</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an <a href="libxml-parser.html#xmlParserNodeInfo">xmlParserNodeInfo</a> block pointer or NULL</td></tr></tbody></table></div><h3><a name="xmlParserFindNodeInfoIndex" id="xmlParserFindNodeInfoIndex"></a>Function: xmlParserFindNodeInfoIndex</h3><pre class="programlisting">unsigned long	xmlParserFindNodeInfoIndex	(const <a href="libxml-parser.html#xmlParserNodeInfoSeqPtr">xmlParserNodeInfoSeqPtr</a> seq, <br />						 const <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p><a href="libxml-parser.html#xmlParserFindNodeInfoIndex">xmlParserFindNodeInfoIndex</a> : Find the index that the info record for the given node is or should be at in a sorted sequence</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>seq</tt></i>:</span></td><td>a node info sequence pointer</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>an XML node pointer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a long indicating the position of the record</td></tr></tbody></table></div><h3><a name="xmlParserInputDeallocate" id="xmlParserInputDeallocate"></a>Function type: xmlParserInputDeallocate</h3><pre class="programlisting">Function type: xmlParserInputDeallocate
void	xmlParserInputDeallocate	(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)
</pre><p>Callback for freeing some parser input allocations.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string to deallocate</td></tr></tbody></table></div><br />
<h3><a name="xmlParserInputGrow" id="xmlParserInputGrow"></a>Function: xmlParserInputGrow</h3><pre class="programlisting">int	xmlParserInputGrow		(<a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> in, <br />					 int len)<br />
</pre><p>This function increase the input for the parser. It tries to preserve pointers to the input buffer, and keep already read data</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an XML parser input</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>an indicative size for the lookahead</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the amount of char read, or -1 in case of error, 0 indicate the end of this entity</td></tr></tbody></table></div><h3><a name="xmlParserInputRead" id="xmlParserInputRead"></a>Function: xmlParserInputRead</h3><pre class="programlisting">int	xmlParserInputRead		(<a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> in, <br />					 int len)<br />
</pre><p>This function was internal and is deprecated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an XML parser input</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>an indicative size for the lookahead</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 as this is an error to use it.</td></tr></tbody></table></div><h3><a name="xmlPedanticParserDefault" id="xmlPedanticParserDefault"></a>Function: xmlPedanticParserDefault</h3><pre class="programlisting">int	xmlPedanticParserDefault	(int val)<br />
</pre><p>Set and return the previous value for enabling pedantic warnings.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>int 0 or 1</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the last value for 0 for no substitution, 1 for substitution.</td></tr></tbody></table></div><h3><a name="xmlReadDoc" id="xmlReadDoc"></a>Function: xmlReadDoc</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlReadDoc		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML in-memory document and build a tree.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to a zero terminated string</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlReadFd" id="xmlReadFd"></a>Function: xmlReadFd</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlReadFd		(int fd, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML from a file descriptor and build a tree. NOTE that the file descriptor will not be closed when the reader is closed or reset.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>fd</tt></i>:</span></td><td>an open file descriptor</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlReadFile" id="xmlReadFile"></a>Function: xmlReadFile</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlReadFile		(const char * filename, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML file from the filesystem or the network.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file or URL</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlReadIO" id="xmlReadIO"></a>Function: xmlReadIO</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlReadIO		(<a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br />					 <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML document from I/O functions and source and build a tree.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ioread</tt></i>:</span></td><td>an I/O read function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlReadMemory" id="xmlReadMemory"></a>Function: xmlReadMemory</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlReadMemory		(const char * buffer, <br />					 int size, <br />					 const char * URL, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>parse an XML in-memory document and build a tree.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the base URL to use for the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlRecoverDoc" id="xmlRecoverDoc"></a>Function: xmlRecoverDoc</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlRecoverDoc		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur)<br />
</pre><p>parse an XML in-memory document and build a tree. In the case the document is not Well Formed, a attempt to build a tree is tried anyway</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to an array of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree or NULL in case of failure</td></tr></tbody></table></div><h3><a name="xmlRecoverFile" id="xmlRecoverFile"></a>Function: xmlRecoverFile</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlRecoverFile		(const char * filename)<br />
</pre><p>parse an XML file and build a tree. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time. In the case the document is not Well Formed, it attempts to build a tree anyway</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree or NULL in case of failure</td></tr></tbody></table></div><h3><a name="xmlRecoverMemory" id="xmlRecoverMemory"></a>Function: xmlRecoverMemory</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlRecoverMemory	(const char * buffer, <br />					 int size)<br />
</pre><p>parse an XML in-memory block and build a tree. In the case the document is not Well Formed, an attempt to build a tree is tried anyway</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>an pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlSAXParseDTD" id="xmlSAXParseDTD"></a>Function: xmlSAXParseDTD</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	xmlSAXParseDTD		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br />
</pre><p>Load and parse an external subset.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>a NAME* containing the External ID of the DTD</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>a NAME* containing the URL to the DTD</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting <a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlSAXParseDoc" id="xmlSAXParseDoc"></a>Function: xmlSAXParseDoc</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlSAXParseDoc		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 int recovery)<br />
</pre><p>parse an XML in-memory document and build a tree. It use the given SAX function block to handle the parsing callback. If sax is NULL, fallback to the default DOM tree building routines.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to an array of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr><tr><td><span class="term"><i><tt>recovery</tt></i>:</span></td><td>work in recovery mode, i.e. tries to read no Well Formed documents</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlSAXParseEntity" id="xmlSAXParseEntity"></a>Function: xmlSAXParseEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlSAXParseEntity	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const char * filename)<br />
</pre><p>parse an XML external entity out of context and build a tree. It use the given SAX function block to handle the parsing callback. If sax is NULL, fallback to the default DOM tree building routines. [78] extParsedEnt ::= TextDecl? content This correspond to a "Well Balanced" chunk</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlSAXParseFile" id="xmlSAXParseFile"></a>Function: xmlSAXParseFile</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlSAXParseFile		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const char * filename, <br />					 int recovery)<br />
</pre><p>parse an XML file and build a tree. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time. It use the given SAX function block to handle the parsing callback. If sax is NULL, fallback to the default DOM tree building routines.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>recovery</tt></i>:</span></td><td>work in recovery mode, i.e. tries to read no Well Formed documents</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlSAXParseFileWithData" id="xmlSAXParseFileWithData"></a>Function: xmlSAXParseFileWithData</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlSAXParseFileWithData	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const char * filename, <br />					 int recovery, <br />					 void * data)<br />
</pre><p>parse an XML file and build a tree. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time. It use the given SAX function block to handle the parsing callback. If sax is NULL, fallback to the default DOM tree building routines. User data (void *) is stored within the parser context in the context's _private member, so it is available nearly everywhere in libxml</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>recovery</tt></i>:</span></td><td>work in recovery mode, i.e. tries to read no Well Formed documents</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlSAXParseMemory" id="xmlSAXParseMemory"></a>Function: xmlSAXParseMemory</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlSAXParseMemory	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 const char * buffer, <br />					 int size, <br />					 int recovery)<br />
</pre><p>parse an XML in-memory block and use the given SAX function block to handle the parsing callback. If sax is NULL, fallback to the default DOM tree building routines.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>an pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>recovery</tt></i>:</span></td><td>work in recovery mode, i.e. tries to read not Well Formed documents</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlSAXParseMemoryWithData" id="xmlSAXParseMemoryWithData"></a>Function: xmlSAXParseMemoryWithData</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlSAXParseMemoryWithData	(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />						 const char * buffer, <br />						 int size, <br />						 int recovery, <br />						 void * data)<br />
</pre><p>parse an XML in-memory block and use the given SAX function block to handle the parsing callback. If sax is NULL, fallback to the default DOM tree building routines. User data (void *) is stored within the parser context in the context's _private member, so it is available nearly everywhere in libxml</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>an pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>recovery</tt></i>:</span></td><td>work in recovery mode, i.e. tries to read no Well Formed documents</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the userdata</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="xmlSAXUserParseFile" id="xmlSAXUserParseFile"></a>Function: xmlSAXUserParseFile</h3><pre class="programlisting">int	xmlSAXUserParseFile		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data, <br />					 const char * filename)<br />
</pre><p>parse an XML file and call the given SAX handler routines. Automatic support for ZLIB/Compress compressed document is provided</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>a SAX handler</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>The user data returned on SAX callbacks</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success or a error number otherwise</td></tr></tbody></table></div><h3><a name="xmlSAXUserParseMemory" id="xmlSAXUserParseMemory"></a>Function: xmlSAXUserParseMemory</h3><pre class="programlisting">int	xmlSAXUserParseMemory		(<a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> sax, <br />					 void * user_data, <br />					 const char * buffer, <br />					 int size)<br />
</pre><p>A better SAX parsing routine. parse an XML in-memory buffer and call the given SAX handler routines.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>a SAX handler</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>The user data returned on SAX callbacks</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>an in-memory XML document input</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the length of the XML document in bytes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success or a error number otherwise</td></tr></tbody></table></div><h3><a name="xmlSetExternalEntityLoader" id="xmlSetExternalEntityLoader"></a>Function: xmlSetExternalEntityLoader</h3><pre class="programlisting">void	xmlSetExternalEntityLoader	(<a href="libxml-parser.html#xmlExternalEntityLoader">xmlExternalEntityLoader</a> f)<br />
</pre><p>Changes the defaultexternal entity resolver function for the application</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the new entity resolver function</td></tr></tbody></table></div><h3><a name="xmlSetFeature" id="xmlSetFeature"></a>Function: xmlSetFeature</h3><pre class="programlisting">int	xmlSetFeature			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const char * name, <br />					 void * value)<br />
</pre><p>Change the current value of one feature of this parser instance</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML/HTML parser context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the feature name</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>pointer to the location of the new value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case or error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlSetupParserForBuffer" id="xmlSetupParserForBuffer"></a>Function: xmlSetupParserForBuffer</h3><pre class="programlisting">void	xmlSetupParserForBuffer		(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * buffer, <br />					 const char * filename)<br />
</pre><p>Setup the parser context to parse a new buffer; Clears any prior contents from the parser context. The buffer parameter must not be NULL, but the filename parameter can be</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * buffer</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file name</td></tr></tbody></table></div><h3><a name="xmlStopParser" id="xmlStopParser"></a>Function: xmlStopParser</h3><pre class="programlisting">void	xmlStopParser			(<a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br />
</pre><p>Blocks further parser processing</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div><h3><a name="xmlSubstituteEntitiesDefault" id="xmlSubstituteEntitiesDefault"></a>Function: xmlSubstituteEntitiesDefault</h3><pre class="programlisting">int	xmlSubstituteEntitiesDefault	(int val)<br />
</pre><p>Set and return the previous value for default entity support. Initially the parser always keep entity references instead of substituting entity values in the output. This function has to be used to change the default parser behavior SAX::substituteEntities() has to be used for changing that on a file by file basis.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>int 0 or 1</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the last value for 0 for no substitution, 1 for substitution.</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
