<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xlink from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xlink from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xinclude.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xinclude.html">xinclude</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlIO.html">xmlIO</a></th><td><a accesskey="n" href="libxml-xmlIO.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>unfinished XLink detection module </p><h2>Table of Contents</h2><pre class="programlisting">Enum <a href="#xlinkActuate">xlinkActuate</a>
</pre><pre class="programlisting">Typedef <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * <a name="xlinkHRef" id="xlinkHRef">xlinkHRef</a>
</pre><pre class="programlisting">Structure <a href="#xlinkHandler">xlinkHandler</a><br />struct _xlinkHandler
</pre><pre class="programlisting">Typedef <a href="libxml-xlink.html#xlinkHandler">xlinkHandler</a> * <a name="xlinkHandlerPtr" id="xlinkHandlerPtr">xlinkHandlerPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * <a name="xlinkRole" id="xlinkRole">xlinkRole</a>
</pre><pre class="programlisting">Enum <a href="#xlinkShow">xlinkShow</a>
</pre><pre class="programlisting">Typedef <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * <a name="xlinkTitle" id="xlinkTitle">xlinkTitle</a>
</pre><pre class="programlisting">Enum <a href="#xlinkType">xlinkType</a>
</pre><pre class="programlisting">Function type: <a href="#xlinkExtendedLinkFunk">xlinkExtendedLinkFunk</a>
void	<a href="#xlinkExtendedLinkFunk">xlinkExtendedLinkFunk</a>		(void * ctx, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int nbLocators, <br />					 const <a href="libxml-xlink.html#xlinkHRef">xlinkHRef</a> * hrefs, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> * roles, <br />					 int nbArcs, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> * from, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> * to, <br />					 <a href="libxml-xlink.html#xlinkShow">xlinkShow</a> * show, <br />					 <a href="libxml-xlink.html#xlinkActuate">xlinkActuate</a> * actuate, <br />					 int nbTitles, <br />					 const <a href="libxml-xlink.html#xlinkTitle">xlinkTitle</a> * titles, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** langs)
</pre>
<pre class="programlisting">Function type: <a href="#xlinkExtendedLinkSetFunk">xlinkExtendedLinkSetFunk</a>
void	<a href="#xlinkExtendedLinkSetFunk">xlinkExtendedLinkSetFunk</a>	(void * ctx, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int nbLocators, <br />					 const <a href="libxml-xlink.html#xlinkHRef">xlinkHRef</a> * hrefs, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> * roles, <br />					 int nbTitles, <br />					 const <a href="libxml-xlink.html#xlinkTitle">xlinkTitle</a> * titles, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** langs)
</pre>
<pre class="programlisting"><a href="libxml-xlink.html#xlinkNodeDetectFunc">xlinkNodeDetectFunc</a>	<a href="#xlinkGetDefaultDetect">xlinkGetDefaultDetect</a>	(void)</pre>
<pre class="programlisting"><a href="libxml-xlink.html#xlinkHandlerPtr">xlinkHandlerPtr</a>	<a href="#xlinkGetDefaultHandler">xlinkGetDefaultHandler</a>	(void)</pre>
<pre class="programlisting"><a href="libxml-xlink.html#xlinkType">xlinkType</a>	<a href="#xlinkIsLink">xlinkIsLink</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">Function type: <a href="#xlinkNodeDetectFunc">xlinkNodeDetectFunc</a>
void	<a href="#xlinkNodeDetectFunc">xlinkNodeDetectFunc</a>		(void * ctx, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)
</pre>
<pre class="programlisting">void	<a href="#xlinkSetDefaultDetect">xlinkSetDefaultDetect</a>		(<a href="libxml-xlink.html#xlinkNodeDetectFunc">xlinkNodeDetectFunc</a> func)</pre>
<pre class="programlisting">void	<a href="#xlinkSetDefaultHandler">xlinkSetDefaultHandler</a>		(<a href="libxml-xlink.html#xlinkHandlerPtr">xlinkHandlerPtr</a> handler)</pre>
<pre class="programlisting">Function type: <a href="#xlinkSimpleLinkFunk">xlinkSimpleLinkFunk</a>
void	<a href="#xlinkSimpleLinkFunk">xlinkSimpleLinkFunk</a>		(void * ctx, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xlink.html#xlinkHRef">xlinkHRef</a> href, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> role, <br />					 const <a href="libxml-xlink.html#xlinkTitle">xlinkTitle</a> title)
</pre>
<h2>Description</h2>
<h3>Enum <a name="xlinkActuate" id="xlinkActuate">xlinkActuate</a></h3><pre class="programlisting">Enum xlinkActuate {
    <a name="XLINK_ACTUATE_NONE" id="XLINK_ACTUATE_NONE">XLINK_ACTUATE_NONE</a> = 0
    <a name="XLINK_ACTUATE_AUTO" id="XLINK_ACTUATE_AUTO">XLINK_ACTUATE_AUTO</a> = 1
    <a name="XLINK_ACTUATE_ONREQUEST" id="XLINK_ACTUATE_ONREQUEST">XLINK_ACTUATE_ONREQUEST</a> = 2
}
</pre><h3><a name="xlinkHandler" id="xlinkHandler">Structure xlinkHandler</a></h3><pre class="programlisting">Structure xlinkHandler<br />struct _xlinkHandler {
    <a href="libxml-xlink.html#xlinkSimpleLinkFunk">xlinkSimpleLinkFunk</a>	simple
    <a href="libxml-xlink.html#xlinkExtendedLinkFunk">xlinkExtendedLinkFunk</a>	extended
    <a href="libxml-xlink.html#xlinkExtendedLinkSetFunk">xlinkExtendedLinkSetFunk</a>	set
}</pre><h3>Enum <a name="xlinkShow" id="xlinkShow">xlinkShow</a></h3><pre class="programlisting">Enum xlinkShow {
    <a name="XLINK_SHOW_NONE" id="XLINK_SHOW_NONE">XLINK_SHOW_NONE</a> = 0
    <a name="XLINK_SHOW_NEW" id="XLINK_SHOW_NEW">XLINK_SHOW_NEW</a> = 1
    <a name="XLINK_SHOW_EMBED" id="XLINK_SHOW_EMBED">XLINK_SHOW_EMBED</a> = 2
    <a name="XLINK_SHOW_REPLACE" id="XLINK_SHOW_REPLACE">XLINK_SHOW_REPLACE</a> = 3
}
</pre><h3>Enum <a name="xlinkType" id="xlinkType">xlinkType</a></h3><pre class="programlisting">Enum xlinkType {
    <a name="XLINK_TYPE_NONE" id="XLINK_TYPE_NONE">XLINK_TYPE_NONE</a> = 0
    <a name="XLINK_TYPE_SIMPLE" id="XLINK_TYPE_SIMPLE">XLINK_TYPE_SIMPLE</a> = 1
    <a name="XLINK_TYPE_EXTENDED" id="XLINK_TYPE_EXTENDED">XLINK_TYPE_EXTENDED</a> = 2
    <a name="XLINK_TYPE_EXTENDED_SET" id="XLINK_TYPE_EXTENDED_SET">XLINK_TYPE_EXTENDED_SET</a> = 3
}
</pre><h3><a name="xlinkExtendedLinkFunk" id="xlinkExtendedLinkFunk"></a>Function type: xlinkExtendedLinkFunk</h3><pre class="programlisting">Function type: xlinkExtendedLinkFunk
void	xlinkExtendedLinkFunk		(void * ctx, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int nbLocators, <br />					 const <a href="libxml-xlink.html#xlinkHRef">xlinkHRef</a> * hrefs, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> * roles, <br />					 int nbArcs, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> * from, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> * to, <br />					 <a href="libxml-xlink.html#xlinkShow">xlinkShow</a> * show, <br />					 <a href="libxml-xlink.html#xlinkActuate">xlinkActuate</a> * actuate, <br />					 int nbTitles, <br />					 const <a href="libxml-xlink.html#xlinkTitle">xlinkTitle</a> * titles, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** langs)
</pre><p>This is the prototype for a extended link detection callback.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>user data pointer</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node carrying the link</td></tr><tr><td><span class="term"><i><tt>nbLocators</tt></i>:</span></td><td>the number of locators detected on the link</td></tr><tr><td><span class="term"><i><tt>hrefs</tt></i>:</span></td><td>pointer to the array of locator hrefs</td></tr><tr><td><span class="term"><i><tt>roles</tt></i>:</span></td><td>pointer to the array of locator roles</td></tr><tr><td><span class="term"><i><tt>nbArcs</tt></i>:</span></td><td>the number of arcs detected on the link</td></tr><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>pointer to the array of source roles found on the arcs</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>pointer to the array of target roles found on the arcs</td></tr><tr><td><span class="term"><i><tt>show</tt></i>:</span></td><td>array of values for the show attributes found on the arcs</td></tr><tr><td><span class="term"><i><tt>actuate</tt></i>:</span></td><td>array of values for the actuate attributes found on the arcs</td></tr><tr><td><span class="term"><i><tt>nbTitles</tt></i>:</span></td><td>the number of titles detected on the link</td></tr><tr><td><span class="term"><i><tt>titles</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>langs</tt></i>:</span></td><td>array of xml:lang values for the titles</td></tr></tbody></table></div><br />
<h3><a name="xlinkExtendedLinkSetFunk" id="xlinkExtendedLinkSetFunk"></a>Function type: xlinkExtendedLinkSetFunk</h3><pre class="programlisting">Function type: xlinkExtendedLinkSetFunk
void	xlinkExtendedLinkSetFunk	(void * ctx, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int nbLocators, <br />					 const <a href="libxml-xlink.html#xlinkHRef">xlinkHRef</a> * hrefs, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> * roles, <br />					 int nbTitles, <br />					 const <a href="libxml-xlink.html#xlinkTitle">xlinkTitle</a> * titles, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** langs)
</pre><p>This is the prototype for a extended link set detection callback.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>user data pointer</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node carrying the link</td></tr><tr><td><span class="term"><i><tt>nbLocators</tt></i>:</span></td><td>the number of locators detected on the link</td></tr><tr><td><span class="term"><i><tt>hrefs</tt></i>:</span></td><td>pointer to the array of locator hrefs</td></tr><tr><td><span class="term"><i><tt>roles</tt></i>:</span></td><td>pointer to the array of locator roles</td></tr><tr><td><span class="term"><i><tt>nbTitles</tt></i>:</span></td><td>the number of titles detected on the link</td></tr><tr><td><span class="term"><i><tt>titles</tt></i>:</span></td><td></td></tr><tr><td><span class="term"><i><tt>langs</tt></i>:</span></td><td>array of xml:lang values for the titles</td></tr></tbody></table></div><br />
<h3><a name="xlinkGetDefaultDetect" id="xlinkGetDefaultDetect"></a>Function: xlinkGetDefaultDetect</h3><pre class="programlisting"><a href="libxml-xlink.html#xlinkNodeDetectFunc">xlinkNodeDetectFunc</a>	xlinkGetDefaultDetect	(void)<br />
</pre><p>Get the default xlink detection routine</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current function or NULL;</td></tr></tbody></table></div><h3><a name="xlinkGetDefaultHandler" id="xlinkGetDefaultHandler"></a>Function: xlinkGetDefaultHandler</h3><pre class="programlisting"><a href="libxml-xlink.html#xlinkHandlerPtr">xlinkHandlerPtr</a>	xlinkGetDefaultHandler	(void)<br />
</pre><p>Get the default xlink handler.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current <a href="libxml-xlink.html#xlinkHandlerPtr">xlinkHandlerPtr</a> value.</td></tr></tbody></table></div><h3><a name="xlinkIsLink" id="xlinkIsLink"></a>Function: xlinkIsLink</h3><pre class="programlisting"><a href="libxml-xlink.html#xlinkType">xlinkType</a>	xlinkIsLink		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Check whether the given node carries the attributes needed to be a link element (or is one of the linking elements issued from the (X)HTML DtDs). This routine don't try to do full checking of the link validity but tries to detect and return the appropriate link type.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document containing the node</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node pointer itself</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xlink.html#xlinkType">xlinkType</a> of the node (XLINK_TYPE_NONE if there is no link detected.</td></tr></tbody></table></div><h3><a name="xlinkNodeDetectFunc" id="xlinkNodeDetectFunc"></a>Function type: xlinkNodeDetectFunc</h3><pre class="programlisting">Function type: xlinkNodeDetectFunc
void	xlinkNodeDetectFunc		(void * ctx, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)
</pre><p>This is the prototype for the link detection routine. It calls the default link detection callbacks upon link detection.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>user data pointer</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to check</td></tr></tbody></table></div><br />
<h3><a name="xlinkSetDefaultDetect" id="xlinkSetDefaultDetect"></a>Function: xlinkSetDefaultDetect</h3><pre class="programlisting">void	xlinkSetDefaultDetect		(<a href="libxml-xlink.html#xlinkNodeDetectFunc">xlinkNodeDetectFunc</a> func)<br />
</pre><p>Set the default xlink detection routine</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>pointer to the new detection routine.</td></tr></tbody></table></div><h3><a name="xlinkSetDefaultHandler" id="xlinkSetDefaultHandler"></a>Function: xlinkSetDefaultHandler</h3><pre class="programlisting">void	xlinkSetDefaultHandler		(<a href="libxml-xlink.html#xlinkHandlerPtr">xlinkHandlerPtr</a> handler)<br />
</pre><p>Set the default xlink handlers</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>the new value for the xlink handler block</td></tr></tbody></table></div><h3><a name="xlinkSimpleLinkFunk" id="xlinkSimpleLinkFunk"></a>Function type: xlinkSimpleLinkFunk</h3><pre class="programlisting">Function type: xlinkSimpleLinkFunk
void	xlinkSimpleLinkFunk		(void * ctx, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xlink.html#xlinkHRef">xlinkHRef</a> href, <br />					 const <a href="libxml-xlink.html#xlinkRole">xlinkRole</a> role, <br />					 const <a href="libxml-xlink.html#xlinkTitle">xlinkTitle</a> title)
</pre><p>This is the prototype for a simple link detection callback.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>user data pointer</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node carrying the link</td></tr><tr><td><span class="term"><i><tt>href</tt></i>:</span></td><td>the target of the link</td></tr><tr><td><span class="term"><i><tt>role</tt></i>:</span></td><td>the role string</td></tr><tr><td><span class="term"><i><tt>title</tt></i>:</span></td><td>the link title</td></tr></tbody></table></div><br />
<p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
