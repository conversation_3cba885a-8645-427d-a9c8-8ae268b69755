<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlsave from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlsave from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlregexp.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlregexp.html">xmlregexp</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlschemas.html">xmlschemas</a></th><td><a accesskey="n" href="libxml-xmlschemas.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>API to save document or subtree of document </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlSaveCtxt">xmlSaveCtxt</a><br />struct _xmlSaveCtxt
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-xmlsave.html#xmlSaveCtxt">xmlSaveCtxt</a> * <a name="xmlSaveCtxtPtr" id="xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlSaveOption">xmlSaveOption</a>
</pre><pre class="programlisting">int	<a href="#xmlSaveClose">xmlSaveClose</a>			(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">long	<a href="#xmlSaveDoc">xmlSaveDoc</a>			(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">int	<a href="#xmlSaveFlush">xmlSaveFlush</a>			(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#xmlSaveSetAttrEscape">xmlSaveSetAttrEscape</a>		(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br />					 <a href="libxml-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escape)</pre>
<pre class="programlisting">int	<a href="#xmlSaveSetEscape">xmlSaveSetEscape</a>		(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br />					 <a href="libxml-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escape)</pre>
<pre class="programlisting"><a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	<a href="#xmlSaveToBuffer">xmlSaveToBuffer</a>		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buffer, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	<a href="#xmlSaveToFd">xmlSaveToFd</a>		(int fd, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	<a href="#xmlSaveToFilename">xmlSaveToFilename</a>	(const char * filename, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	<a href="#xmlSaveToIO">xmlSaveToIO</a>		(<a href="libxml-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a> iowrite, <br />					 <a href="libxml-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * encoding, <br />					 int options)</pre>
<pre class="programlisting">long	<a href="#xmlSaveTree">xmlSaveTree</a>			(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<h2>Description</h2>
<h3><a name="xmlSaveCtxt" id="xmlSaveCtxt">Structure xmlSaveCtxt</a></h3><pre class="programlisting">Structure xmlSaveCtxt<br />struct _xmlSaveCtxt {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlSaveOption" id="xmlSaveOption">xmlSaveOption</a></h3><pre class="programlisting">Enum xmlSaveOption {
    <a name="XML_SAVE_FORMAT" id="XML_SAVE_FORMAT">XML_SAVE_FORMAT</a> = 1 : format save output
    <a name="XML_SAVE_NO_DECL" id="XML_SAVE_NO_DECL">XML_SAVE_NO_DECL</a> = 2 : drop the xml declaration
    <a name="XML_SAVE_NO_EMPTY" id="XML_SAVE_NO_EMPTY">XML_SAVE_NO_EMPTY</a> = 4 : no empty tags
    <a name="XML_SAVE_NO_XHTML" id="XML_SAVE_NO_XHTML">XML_SAVE_NO_XHTML</a> = 8 : disable XHTML1 specific rules
    <a name="XML_SAVE_XHTML" id="XML_SAVE_XHTML">XML_SAVE_XHTML</a> = 16 : force XHTML1 specific rules
    <a name="XML_SAVE_AS_XML" id="XML_SAVE_AS_XML">XML_SAVE_AS_XML</a> = 32 : force XML serialization on HTML doc
    <a name="XML_SAVE_AS_HTML" id="XML_SAVE_AS_HTML">XML_SAVE_AS_HTML</a> = 64 : force HTML serialization on XML doc
    <a name="XML_SAVE_WSNONSIG" id="XML_SAVE_WSNONSIG">XML_SAVE_WSNONSIG</a> = 128 : format with non-significant whitespace
}
</pre><h3><a name="xmlSaveClose" id="xmlSaveClose"></a>Function: xmlSaveClose</h3><pre class="programlisting">int	xmlSaveClose			(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt)<br />
</pre><p>Close a document saving context, i.e. make sure that all bytes have been output and free the associated data.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlSaveDoc" id="xmlSaveDoc"></a>Function: xmlSaveDoc</h3><pre class="programlisting">long	xmlSaveDoc			(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Save a full document to a saving context TODO: The function is not fully implemented yet as it does not return the byte count but 0 instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>a document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlSaveFlush" id="xmlSaveFlush"></a>Function: xmlSaveFlush</h3><pre class="programlisting">int	xmlSaveFlush			(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt)<br />
</pre><p>Flush a document saving context, i.e. make sure that all bytes have been output.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlSaveSetAttrEscape" id="xmlSaveSetAttrEscape"></a>Function: xmlSaveSetAttrEscape</h3><pre class="programlisting">int	xmlSaveSetAttrEscape		(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br />					 <a href="libxml-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escape)<br />
</pre><p>Set a custom escaping function to be used for text in <a href="libxml-SAX.html#attribute">attribute</a> content</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>escape</tt></i>:</span></td><td>the escaping function</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if successful or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlSaveSetEscape" id="xmlSaveSetEscape"></a>Function: xmlSaveSetEscape</h3><pre class="programlisting">int	xmlSaveSetEscape		(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br />					 <a href="libxml-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escape)<br />
</pre><p>Set a custom escaping function to be used for text in element content</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>escape</tt></i>:</span></td><td>the escaping function</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if successful or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlSaveToBuffer" id="xmlSaveToBuffer"></a>Function: xmlSaveToBuffer</h3><pre class="programlisting"><a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	xmlSaveToBuffer		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buffer, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Create a document saving context serializing to a buffer with the encoding and the options given</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a buffer</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding name to use or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of xmlSaveOptions</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new serialization context or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlSaveToFd" id="xmlSaveToFd"></a>Function: xmlSaveToFd</h3><pre class="programlisting"><a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	xmlSaveToFd		(int fd, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Create a document saving context serializing to a file descriptor with the encoding and the options given.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>fd</tt></i>:</span></td><td>a file descriptor number</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding name to use or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of xmlSaveOptions</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new serialization context or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlSaveToFilename" id="xmlSaveToFilename"></a>Function: xmlSaveToFilename</h3><pre class="programlisting"><a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	xmlSaveToFilename	(const char * filename, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Create a document saving context serializing to a filename or possibly to an URL (but this is less reliable) with the encoding and the options given.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file name or an URL</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding name to use or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of xmlSaveOptions</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new serialization context or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlSaveToIO" id="xmlSaveToIO"></a>Function: xmlSaveToIO</h3><pre class="programlisting"><a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	xmlSaveToIO		(<a href="libxml-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a> iowrite, <br />					 <a href="libxml-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a> ioclose, <br />					 void * ioctx, <br />					 const char * encoding, <br />					 int options)<br />
</pre><p>Create a document saving context serializing to a file descriptor with the encoding and the options given</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>iowrite</tt></i>:</span></td><td>an I/O write function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding name to use or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of xmlSaveOptions</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new serialization context or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlSaveTree" id="xmlSaveTree"></a>Function: xmlSaveTree</h3><pre class="programlisting">long	xmlSaveTree			(<a href="libxml-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Save a subtree starting at the node parameter to a saving context TODO: The function is not fully implemented yet as it does not return the byte count but 0 instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the top node of the subtree to save</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
