<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module dict from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module dict from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-debugXML.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-debugXML.html">debugXML</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-encoding.html">encoding</a></th><td><a accesskey="n" href="libxml-encoding.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>dictionary of reusable strings, just used to avoid allocation and freeing operations. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlDict">xmlDict</a><br />struct _xmlDict
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-dict.html#xmlDict">xmlDict</a> * <a name="xmlDictPtr" id="xmlDictPtr">xmlDictPtr</a>
</pre><pre class="programlisting">void	<a href="#xmlDictCleanup">xmlDictCleanup</a>			(void)</pre>
<pre class="programlisting"><a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a>	<a href="#xmlDictCreate">xmlDictCreate</a>		(void)</pre>
<pre class="programlisting"><a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a>	<a href="#xmlDictCreateSub">xmlDictCreateSub</a>	(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> sub)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlDictExists">xmlDictExists</a>		(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int len)</pre>
<pre class="programlisting">void	<a href="#xmlDictFree">xmlDictFree</a>			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)</pre>
<pre class="programlisting">size_t	<a href="#xmlDictGetUsage">xmlDictGetUsage</a>			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlDictLookup">xmlDictLookup</a>		(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlDictOwns">xmlDictOwns</a>			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlDictQLookup">xmlDictQLookup</a>		(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlDictReference">xmlDictReference</a>		(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)</pre>
<pre class="programlisting">size_t	<a href="#xmlDictSetLimit">xmlDictSetLimit</a>			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 size_t limit)</pre>
<pre class="programlisting">int	<a href="#xmlDictSize">xmlDictSize</a>			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)</pre>
<pre class="programlisting">int	<a href="#xmlInitializeDict">xmlInitializeDict</a>		(void)</pre>
<h2>Description</h2>
<h3><a name="xmlDict" id="xmlDict">Structure xmlDict</a></h3><pre class="programlisting">Structure xmlDict<br />struct _xmlDict {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlDictCleanup" id="xmlDictCleanup"></a>Function: xmlDictCleanup</h3><pre class="programlisting">void	xmlDictCleanup			(void)<br />
</pre><p>Free the dictionary mutex. Do not call unless sure the library is not in use anymore !</p>
<h3><a name="xmlDictCreate" id="xmlDictCreate"></a>Function: xmlDictCreate</h3><pre class="programlisting"><a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a>	xmlDictCreate		(void)<br />
</pre><p>Create a new dictionary</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created dictionary, or NULL if an error occured.</td></tr></tbody></table></div><h3><a name="xmlDictCreateSub" id="xmlDictCreateSub"></a>Function: xmlDictCreateSub</h3><pre class="programlisting"><a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a>	xmlDictCreateSub	(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> sub)<br />
</pre><p>Create a new dictionary, inheriting strings from the read-only dictionary @sub. On lookup, strings are first searched in the new dictionary, then in @sub, and if not found are created in the new dictionary.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sub</tt></i>:</span></td><td>an existing dictionary</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created dictionary, or NULL if an error occured.</td></tr></tbody></table></div><h3><a name="xmlDictExists" id="xmlDictExists"></a>Function: xmlDictExists</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlDictExists		(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int len)<br />
</pre><p>Check if the @name exists in the dictionary @dict.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of the name, if -1 it is recomputed</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the internal copy of the name or NULL if not found.</td></tr></tbody></table></div><h3><a name="xmlDictFree" id="xmlDictFree"></a>Function: xmlDictFree</h3><pre class="programlisting">void	xmlDictFree			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)<br />
</pre><p>Free the hash @dict and its contents. The userdata is deallocated with @f if provided.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr></tbody></table></div><h3><a name="xmlDictGetUsage" id="xmlDictGetUsage"></a>Function: xmlDictGetUsage</h3><pre class="programlisting">size_t	xmlDictGetUsage			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)<br />
</pre><p>Get how much memory is used by a dictionary for strings Added in 2.9.0</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the amount of strings allocated</td></tr></tbody></table></div><h3><a name="xmlDictLookup" id="xmlDictLookup"></a>Function: xmlDictLookup</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlDictLookup		(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int len)<br />
</pre><p>Add the @name to the dictionary @dict if not present.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the userdata</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of the name, if -1 it is recomputed</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the internal copy of the name or NULL in case of internal error</td></tr></tbody></table></div><h3><a name="xmlDictOwns" id="xmlDictOwns"></a>Function: xmlDictOwns</h3><pre class="programlisting">int	xmlDictOwns			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>check if a string is owned by the disctionary</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if false and -1 in case of error -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlDictQLookup" id="xmlDictQLookup"></a>Function: xmlDictQLookup</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlDictQLookup		(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Add the QName @prefix:@name to the hash @dict if not present.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the internal copy of the QName or NULL in case of internal error</td></tr></tbody></table></div><h3><a name="xmlDictReference" id="xmlDictReference"></a>Function: xmlDictReference</h3><pre class="programlisting">int	xmlDictReference		(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)<br />
</pre><p>Increment the <a href="libxml-SAX.html#reference">reference</a> counter of a dictionary</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlDictSetLimit" id="xmlDictSetLimit"></a>Function: xmlDictSetLimit</h3><pre class="programlisting">size_t	xmlDictSetLimit			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict, <br />					 size_t limit)<br />
</pre><p>Set a size limit for the dictionary Added in 2.9.0</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr><tr><td><span class="term"><i><tt>limit</tt></i>:</span></td><td>the limit in bytes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the previous limit of the dictionary or 0</td></tr></tbody></table></div><h3><a name="xmlDictSize" id="xmlDictSize"></a>Function: xmlDictSize</h3><pre class="programlisting">int	xmlDictSize			(<a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a> dict)<br />
</pre><p>Query the number of elements installed in the hash @dict.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>the dictionary</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of elements in the dictionary or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlInitializeDict" id="xmlInitializeDict"></a>Function: xmlInitializeDict</h3><pre class="programlisting">int	xmlInitializeDict		(void)<br />
</pre><p>Do the dictionary mutex initialization. this function is deprecated</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if initialization was already done, and 1 if that call led to the initialization</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
