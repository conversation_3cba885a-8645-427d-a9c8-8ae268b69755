<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module SAX from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module SAX from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-HTMLtree.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-HTMLtree.html">HTMLtree</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-SAX2.html">SAX2</a></th><td><a accesskey="n" href="libxml-SAX2.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><h2 style="font-weight:bold;color:red;text-align:center">This module is deprecated</h2><p>DEPRECATED set of SAX version 1 interfaces used to build the DOM tree. </p><div class="deprecated"><h2>Table of Contents</h2><pre class="programlisting">void	<a href="#comment">comment</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting">int	<a href="#checkNamespace">checkNamespace</a>			(void * ctx, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespace)</pre>
<pre class="programlisting">int	<a href="#getColumnNumber">getColumnNumber</a>			(void * ctx)</pre>
<pre class="programlisting">void	<a href="#entityDecl">entityDecl</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">void	<a href="#attribute">attribute</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#getNamespace">getNamespace</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#setDocumentLocator">setDocumentLocator</a>		(void * ctx, <br />					 <a href="libxml-tree.html#xmlSAXLocatorPtr">xmlSAXLocatorPtr</a> loc)</pre>
<pre class="programlisting">void	<a href="#initxmlDefaultSAXHandler">initxmlDefaultSAXHandler</a>	(<a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr, <br />					 int warning)</pre>
<pre class="programlisting">void	<a href="#ignorableWhitespace">ignorableWhitespace</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#hasExternalSubset">hasExternalSubset</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#unparsedEntityDecl">unparsedEntityDecl</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * notationName)</pre>
<pre class="programlisting">void	<a href="#globalNamespace">globalNamespace</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)</pre>
<pre class="programlisting">int	<a href="#hasInternalSubset">hasInternalSubset</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#reference">reference</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void	<a href="#notationDecl">notationDecl</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#getSystemId">getSystemId</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#externalSubset">externalSubset</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#resolveEntity">resolveEntity</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)</pre>
<pre class="programlisting">void	<a href="#startDocument">startDocument</a>			(void * ctx)</pre>
<pre class="programlisting">void	<a href="#setNamespace">setNamespace</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void	<a href="#cdataBlock">cdataBlock</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int len)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#getPublicId">getPublicId</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#inithtmlDefaultSAXHandler">inithtmlDefaultSAXHandler</a>	(<a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr)</pre>
<pre class="programlisting">void	<a href="#processingInstruction">processingInstruction</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * data)</pre>
<pre class="programlisting">void	<a href="#endElement">endElement</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void	<a href="#namespaceDecl">namespaceDecl</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)</pre>
<pre class="programlisting">void	<a href="#initdocbDefaultSAXHandler">initdocbDefaultSAXHandler</a>	(<a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#getEntity">getEntity</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void	<a href="#characters">characters</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)</pre>
<pre class="programlisting">void	<a href="#elementDecl">elementDecl</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 <a href="libxml-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> content)</pre>
<pre class="programlisting">void	<a href="#startElement">startElement</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** atts)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#getParameterEntity">getParameterEntity</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void	<a href="#attributeDecl">attributeDecl</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * elem, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 int type, <br />					 int def, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * defaultValue, <br />					 <a href="libxml-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> tree)</pre>
<pre class="programlisting">int	<a href="#isStandalone">isStandalone</a>			(void * ctx)</pre>
<pre class="programlisting">void	<a href="#internalSubset">internalSubset</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)</pre>
<pre class="programlisting">void	<a href="#endDocument">endDocument</a>			(void * ctx)</pre>
<pre class="programlisting">int	<a href="#getLineNumber">getLineNumber</a>			(void * ctx)</pre>
<h2>Description</h2>
<h3><a name="comment" id="comment"></a>Function: comment</h3><pre class="programlisting">void	comment			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>A <a href="libxml-SAX.html#comment">comment</a> has been parsed. DEPRECATED: use xmlSAX2Comment()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the <a href="libxml-SAX.html#comment">comment</a> content</td></tr></tbody></table></div><h3><a name="checkNamespace" id="checkNamespace"></a>Function: checkNamespace</h3><pre class="programlisting">int	checkNamespace			(void * ctx, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * namespace)<br />
</pre><p>Check that the current element namespace is the same as the one read upon parsing. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>namespace</tt></i>:</span></td><td>the namespace to check against</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div><h3><a name="getColumnNumber" id="getColumnNumber"></a>Function: getColumnNumber</h3><pre class="programlisting">int	getColumnNumber			(void * ctx)<br />
</pre><p>Provide the column number of the current parsing point. DEPRECATED: use xmlSAX2GetColumnNumber()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int</td></tr></tbody></table></div><h3><a name="entityDecl" id="entityDecl"></a>Function: entityDecl</h3><pre class="programlisting">void	entityDecl			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>An entity definition has been parsed DEPRECATED: use xmlSAX2EntityDecl()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the entity type</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the entity value (without processing).</td></tr></tbody></table></div><h3><a name="attribute" id="attribute"></a>Function: attribute</h3><pre class="programlisting">void	attribute			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Handle an <a href="libxml-SAX.html#attribute">attribute</a> that has been read by the parser. The default handling is to convert the <a href="libxml-SAX.html#attribute">attribute</a> into an DOM subtree and past it in a new <a href="libxml-tree.html#xmlAttr">xmlAttr</a> element added to the element. DEPRECATED: use xmlSAX2Attribute()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>The <a href="libxml-SAX.html#attribute">attribute</a> name, including namespace prefix</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>The <a href="libxml-SAX.html#attribute">attribute</a> value</td></tr></tbody></table></div><h3><a name="getNamespace" id="getNamespace"></a>Function: getNamespace</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	getNamespace		(void * ctx)<br />
</pre><p>Get the current element namespace. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> or NULL if none</td></tr></tbody></table></div><h3><a name="setDocumentLocator" id="setDocumentLocator"></a>Function: setDocumentLocator</h3><pre class="programlisting">void	setDocumentLocator		(void * ctx, <br />					 <a href="libxml-tree.html#xmlSAXLocatorPtr">xmlSAXLocatorPtr</a> loc)<br />
</pre><p>Receive the document locator at startup, actually <a href="libxml-globals.html#xmlDefaultSAXLocator">xmlDefaultSAXLocator</a> Everything is available on the context, so this is useless in our case. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>loc</tt></i>:</span></td><td>A SAX Locator</td></tr></tbody></table></div><h3><a name="initxmlDefaultSAXHandler" id="initxmlDefaultSAXHandler"></a>Function: initxmlDefaultSAXHandler</h3><pre class="programlisting">void	initxmlDefaultSAXHandler	(<a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr, <br />					 int warning)<br />
</pre><p>Initialize the default XML SAX version 1 handler DEPRECATED: use xmlSAX2InitDefaultSAXHandler() for the new SAX2 blocks</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr><tr><td><span class="term"><i><tt>warning</tt></i>:</span></td><td>flag if non-zero sets the handler warning procedure</td></tr></tbody></table></div><h3><a name="ignorableWhitespace" id="ignorableWhitespace"></a>Function: ignorableWhitespace</h3><pre class="programlisting">void	ignorableWhitespace		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)<br />
</pre><p>receiving some ignorable whitespaces from the parser. UNUSED: by default the DOM building will use <a href="libxml-SAX.html#characters">characters</a> DEPRECATED: use xmlSAX2IgnorableWhitespace()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr></tbody></table></div><h3><a name="hasExternalSubset" id="hasExternalSubset"></a>Function: hasExternalSubset</h3><pre class="programlisting">int	hasExternalSubset		(void * ctx)<br />
</pre><p>Does this document has an external subset DEPRECATED: use xmlSAX2HasExternalSubset()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><h3><a name="unparsedEntityDecl" id="unparsedEntityDecl"></a>Function: unparsedEntityDecl</h3><pre class="programlisting">void	unparsedEntityDecl		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * notationName)<br />
</pre><p>What to do when an unparsed entity declaration is parsed DEPRECATED: use xmlSAX2UnparsedEntityDecl()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The name of the entity</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>notationName</tt></i>:</span></td><td>the name of the notation</td></tr></tbody></table></div><h3><a name="globalNamespace" id="globalNamespace"></a>Function: globalNamespace</h3><pre class="programlisting">void	globalNamespace			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)<br />
</pre><p>An old global namespace has been parsed. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>href</tt></i>:</span></td><td>the namespace associated URN</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the namespace prefix</td></tr></tbody></table></div><h3><a name="hasInternalSubset" id="hasInternalSubset"></a>Function: hasInternalSubset</h3><pre class="programlisting">int	hasInternalSubset		(void * ctx)<br />
</pre><p>Does this document has an internal subset DEPRECATED: use xmlSAX2HasInternalSubset()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><h3><a name="reference" id="reference"></a>Function: reference</h3><pre class="programlisting">void	reference			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>called when an entity <a href="libxml-SAX.html#reference">reference</a> is detected. DEPRECATED: use xmlSAX2Reference()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr></tbody></table></div><h3><a name="notationDecl" id="notationDecl"></a>Function: notationDecl</h3><pre class="programlisting">void	notationDecl			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)<br />
</pre><p>What to do when a notation declaration has been parsed. DEPRECATED: use xmlSAX2NotationDecl()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The name of the notation</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr></tbody></table></div><h3><a name="getSystemId" id="getSystemId"></a>Function: getSystemId</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	getSystemId		(void * ctx)<br />
</pre><p>Provides the system ID, basically URL or filename e.g. http://www.sgmlsource.com/dtds/memo.dtd DEPRECATED: use xmlSAX2GetSystemId()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr></tbody></table></div><h3><a name="externalSubset" id="externalSubset"></a>Function: externalSubset</h3><pre class="programlisting">void	externalSubset			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br />
</pre><p>Callback on external subset declaration. DEPRECATED: use xmlSAX2ExternalSubset()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the root element name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the SYSTEM ID (e.g. filename or URL)</td></tr></tbody></table></div><h3><a name="resolveEntity" id="resolveEntity"></a>Function: resolveEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	resolveEntity	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)<br />
</pre><p>The entity loader, to control the loading of external entities, the application can either: - override this resolveEntity() callback in the SAX block - or better use the xmlSetExternalEntityLoader() function to set up it's own entity resolution routine DEPRECATED: use xmlSAX2ResolveEntity()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> if inlined or NULL for DOM behaviour.</td></tr></tbody></table></div><h3><a name="startDocument" id="startDocument"></a>Function: startDocument</h3><pre class="programlisting">void	startDocument			(void * ctx)<br />
</pre><p>called when the document start being processed. DEPRECATED: use xmlSAX2StartDocument()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr></tbody></table></div><h3><a name="setNamespace" id="setNamespace"></a>Function: setNamespace</h3><pre class="programlisting">void	setNamespace			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Set the current element namespace. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the namespace prefix</td></tr></tbody></table></div><h3><a name="cdataBlock" id="cdataBlock"></a>Function: cdataBlock</h3><pre class="programlisting">void	cdataBlock			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int len)<br />
</pre><p>called when a pcdata block has been parsed DEPRECATED: use xmlSAX2CDataBlock()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>The pcdata content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the block length</td></tr></tbody></table></div><h3><a name="getPublicId" id="getPublicId"></a>Function: getPublicId</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	getPublicId		(void * ctx)<br />
</pre><p>Provides the public ID e.g. "-//SGMLSOURCE//DTD DEMO//EN" DEPRECATED: use xmlSAX2GetPublicId()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr></tbody></table></div><h3><a name="inithtmlDefaultSAXHandler" id="inithtmlDefaultSAXHandler"></a>Function: inithtmlDefaultSAXHandler</h3><pre class="programlisting">void	inithtmlDefaultSAXHandler	(<a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr)<br />
</pre><p>Initialize the default HTML SAX version 1 handler DEPRECATED: use xmlSAX2InitHtmlDefaultSAXHandler() for the new SAX2 blocks</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr></tbody></table></div><h3><a name="processingInstruction" id="processingInstruction"></a>Function: processingInstruction</h3><pre class="programlisting">void	processingInstruction		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * data)<br />
</pre><p>A processing instruction has been parsed. DEPRECATED: use xmlSAX2ProcessingInstruction()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>the target name</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the PI data's</td></tr></tbody></table></div><h3><a name="endElement" id="endElement"></a>Function: endElement</h3><pre class="programlisting">void	endElement			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>called when the end of an element has been detected. DEPRECATED: use xmlSAX2EndElement()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The element name</td></tr></tbody></table></div><h3><a name="namespaceDecl" id="namespaceDecl"></a>Function: namespaceDecl</h3><pre class="programlisting">void	namespaceDecl			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)<br />
</pre><p>A namespace has been parsed. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>href</tt></i>:</span></td><td>the namespace associated URN</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the namespace prefix</td></tr></tbody></table></div><h3><a name="initdocbDefaultSAXHandler" id="initdocbDefaultSAXHandler"></a>Function: initdocbDefaultSAXHandler</h3><pre class="programlisting">void	initdocbDefaultSAXHandler	(<a href="libxml-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr)<br />
</pre><p>Initialize the default DocBook SAX version 1 handler DEPRECATED: use xmlSAX2InitDocbDefaultSAXHandler() for the new SAX2 blocks</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr></tbody></table></div><h3><a name="getEntity" id="getEntity"></a>Function: getEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	getEntity		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Get an entity by name DEPRECATED: use xmlSAX2GetEntity()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found.</td></tr></tbody></table></div><h3><a name="characters" id="characters"></a>Function: characters</h3><pre class="programlisting">void	characters			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)<br />
</pre><p>receiving some chars from the parser. DEPRECATED: use xmlSAX2Characters()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr></tbody></table></div><h3><a name="elementDecl" id="elementDecl"></a>Function: elementDecl</h3><pre class="programlisting">void	elementDecl			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 <a href="libxml-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> content)<br />
</pre><p>An element definition has been parsed DEPRECATED: use xmlSAX2ElementDecl()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the element name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the element type</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the element value tree</td></tr></tbody></table></div><h3><a name="startElement" id="startElement"></a>Function: startElement</h3><pre class="programlisting">void	startElement			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** atts)<br />
</pre><p>called when an opening tag has been processed. DEPRECATED: use xmlSAX2StartElement()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>The element name, including namespace prefix</td></tr><tr><td><span class="term"><i><tt>atts</tt></i>:</span></td><td>An array of name/value attributes pairs, NULL terminated</td></tr></tbody></table></div><h3><a name="getParameterEntity" id="getParameterEntity"></a>Function: getParameterEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	getParameterEntity	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Get a parameter entity by name DEPRECATED: use xmlSAX2GetParameterEntity()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found.</td></tr></tbody></table></div><h3><a name="attributeDecl" id="attributeDecl"></a>Function: attributeDecl</h3><pre class="programlisting">void	attributeDecl			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * elem, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 int type, <br />					 int def, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * defaultValue, <br />					 <a href="libxml-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> tree)<br />
</pre><p>An <a href="libxml-SAX.html#attribute">attribute</a> definition has been parsed DEPRECATED: use xmlSAX2AttributeDecl()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>the name of the element</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> type</td></tr><tr><td><span class="term"><i><tt>def</tt></i>:</span></td><td>the type of default value</td></tr><tr><td><span class="term"><i><tt>defaultValue</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> default value</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>the tree of enumerated value set</td></tr></tbody></table></div><h3><a name="isStandalone" id="isStandalone"></a>Function: isStandalone</h3><pre class="programlisting">int	isStandalone			(void * ctx)<br />
</pre><p>Is this document tagged standalone ? DEPRECATED: use xmlSAX2IsStandalone()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><h3><a name="internalSubset" id="internalSubset"></a>Function: internalSubset</h3><pre class="programlisting">void	internalSubset			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br />
</pre><p>Callback on internal subset declaration. DEPRECATED: use xmlSAX2InternalSubset()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the root element name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the SYSTEM ID (e.g. filename or URL)</td></tr></tbody></table></div><h3><a name="endDocument" id="endDocument"></a>Function: endDocument</h3><pre class="programlisting">void	endDocument			(void * ctx)<br />
</pre><p>called when the document end has been detected. DEPRECATED: use xmlSAX2EndDocument()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr></tbody></table></div><h3><a name="getLineNumber" id="getLineNumber"></a>Function: getLineNumber</h3><pre class="programlisting">int	getLineNumber			(void * ctx)<br />
</pre><p>Provide the line number of the current parsing point. DEPRECATED: use xmlSAX2GetLineNumber()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int</td></tr></tbody></table></div></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
