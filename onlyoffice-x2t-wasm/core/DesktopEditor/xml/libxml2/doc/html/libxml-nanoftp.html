<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module nanoftp from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module nanoftp from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-list.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-list.html">list</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-nanohttp.html">nanohttp</a></th><td><a accesskey="n" href="libxml-nanohttp.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>minimal FTP implementation allowing to fetch resources like external subset. </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#INVALID_SOCKET">INVALID_SOCKET</a></pre><pre class="programlisting">#define <a href="#SOCKET">SOCKET</a></pre><pre class="programlisting">Function type: <a href="#ftpDataCallback">ftpDataCallback</a>
void	<a href="#ftpDataCallback">ftpDataCallback</a>			(void * userData, <br />					 const char * data, <br />					 int len)
</pre>
<pre class="programlisting">Function type: <a href="#ftpListCallback">ftpListCallback</a>
void	<a href="#ftpListCallback">ftpListCallback</a>			(void * userData, <br />					 const char * filename, <br />					 const char * attrib, <br />					 const char * owner, <br />					 const char * group, <br />					 unsigned long size, <br />					 int links, <br />					 int year, <br />					 const char * month, <br />					 int day, <br />					 int hour, <br />					 int minute)
</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPCheckResponse">xmlNanoFTPCheckResponse</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#xmlNanoFTPCleanup">xmlNanoFTPCleanup</a>		(void)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPClose">xmlNanoFTPClose</a>			(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPCloseConnection">xmlNanoFTPCloseConnection</a>	(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPConnect">xmlNanoFTPConnect</a>		(void * ctx)</pre>
<pre class="programlisting">void *	<a href="#xmlNanoFTPConnectTo">xmlNanoFTPConnectTo</a>		(const char * server, <br />					 int port)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPCwd">xmlNanoFTPCwd</a>			(void * ctx, <br />					 const char * directory)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPDele">xmlNanoFTPDele</a>			(void * ctx, <br />					 const char * file)</pre>
<pre class="programlisting">void	<a href="#xmlNanoFTPFreeCtxt">xmlNanoFTPFreeCtxt</a>		(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPGet">xmlNanoFTPGet</a>			(void * ctx, <br />					 <a href="libxml-nanoftp.html#ftpDataCallback">ftpDataCallback</a> callback, <br />					 void * userData, <br />					 const char * filename)</pre>
<pre class="programlisting"><a href="libxml-nanoftp.html#SOCKET">SOCKET</a>	<a href="#xmlNanoFTPGetConnection">xmlNanoFTPGetConnection</a>		(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPGetResponse">xmlNanoFTPGetResponse</a>		(void * ctx)</pre>
<pre class="programlisting"><a href="libxml-nanoftp.html#SOCKET">SOCKET</a>	<a href="#xmlNanoFTPGetSocket">xmlNanoFTPGetSocket</a>		(void * ctx, <br />					 const char * filename)</pre>
<pre class="programlisting">void	<a href="#xmlNanoFTPInit">xmlNanoFTPInit</a>			(void)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPList">xmlNanoFTPList</a>			(void * ctx, <br />					 <a href="libxml-nanoftp.html#ftpListCallback">ftpListCallback</a> callback, <br />					 void * userData, <br />					 const char * filename)</pre>
<pre class="programlisting">void *	<a href="#xmlNanoFTPNewCtxt">xmlNanoFTPNewCtxt</a>		(const char * URL)</pre>
<pre class="programlisting">void *	<a href="#xmlNanoFTPOpen">xmlNanoFTPOpen</a>			(const char * URL)</pre>
<pre class="programlisting">void	<a href="#xmlNanoFTPProxy">xmlNanoFTPProxy</a>			(const char * host, <br />					 int port, <br />					 const char * user, <br />					 const char * passwd, <br />					 int type)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPQuit">xmlNanoFTPQuit</a>			(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPRead">xmlNanoFTPRead</a>			(void * ctx, <br />					 void * dest, <br />					 int len)</pre>
<pre class="programlisting">void	<a href="#xmlNanoFTPScanProxy">xmlNanoFTPScanProxy</a>		(const char * URL)</pre>
<pre class="programlisting">int	<a href="#xmlNanoFTPUpdateURL">xmlNanoFTPUpdateURL</a>		(void * ctx, <br />					 const char * URL)</pre>
<h2>Description</h2>
<h3><a name="INVALID_SOCKET" id="INVALID_SOCKET"></a>Macro: INVALID_SOCKET</h3><pre>#define INVALID_SOCKET</pre><p>macro used to provide portability of code to windows sockets the value to be used when the socket is not valid</p>
<h3><a name="SOCKET" id="SOCKET"></a>Macro: SOCKET</h3><pre>#define SOCKET</pre><p>macro used to provide portability of code to windows sockets</p>
<h3><a name="ftpDataCallback" id="ftpDataCallback"></a>Function type: ftpDataCallback</h3><pre class="programlisting">Function type: ftpDataCallback
void	ftpDataCallback			(void * userData, <br />					 const char * data, <br />					 int len)
</pre><p>A callback for the <a href="libxml-nanoftp.html#xmlNanoFTPGet">xmlNanoFTPGet</a> command.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>userData</tt></i>:</span></td><td>the user provided context</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the data received</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>its size in bytes</td></tr></tbody></table></div><br />
<h3><a name="ftpListCallback" id="ftpListCallback"></a>Function type: ftpListCallback</h3><pre class="programlisting">Function type: ftpListCallback
void	ftpListCallback			(void * userData, <br />					 const char * filename, <br />					 const char * attrib, <br />					 const char * owner, <br />					 const char * group, <br />					 unsigned long size, <br />					 int links, <br />					 int year, <br />					 const char * month, <br />					 int day, <br />					 int hour, <br />					 int minute)
</pre><p>A callback for the <a href="libxml-nanoftp.html#xmlNanoFTPList">xmlNanoFTPList</a> command. Note that only one of year and day:minute are specified.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>userData</tt></i>:</span></td><td>user provided data for the callback</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file name (including "-&gt;" when links are shown)</td></tr><tr><td><span class="term"><i><tt>attrib</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> string</td></tr><tr><td><span class="term"><i><tt>owner</tt></i>:</span></td><td>the owner string</td></tr><tr><td><span class="term"><i><tt>group</tt></i>:</span></td><td>the group string</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the file size</td></tr><tr><td><span class="term"><i><tt>links</tt></i>:</span></td><td>the link count</td></tr><tr><td><span class="term"><i><tt>year</tt></i>:</span></td><td>the year</td></tr><tr><td><span class="term"><i><tt>month</tt></i>:</span></td><td>the month</td></tr><tr><td><span class="term"><i><tt>day</tt></i>:</span></td><td>the day</td></tr><tr><td><span class="term"><i><tt>hour</tt></i>:</span></td><td>the hour</td></tr><tr><td><span class="term"><i><tt>minute</tt></i>:</span></td><td>the minute</td></tr></tbody></table></div><br />
<h3><a name="xmlNanoFTPCheckResponse" id="xmlNanoFTPCheckResponse"></a>Function: xmlNanoFTPCheckResponse</h3><pre class="programlisting">int	xmlNanoFTPCheckResponse		(void * ctx)<br />
</pre><p>Check if there is a response from the FTP server after a command.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the code number, or 0</td></tr></tbody></table></div><h3><a name="xmlNanoFTPCleanup" id="xmlNanoFTPCleanup"></a>Function: xmlNanoFTPCleanup</h3><pre class="programlisting">void	xmlNanoFTPCleanup		(void)<br />
</pre><p>Cleanup the FTP protocol layer. This cleanup proxy informations.</p>
<h3><a name="xmlNanoFTPClose" id="xmlNanoFTPClose"></a>Function: xmlNanoFTPClose</h3><pre class="programlisting">int	xmlNanoFTPClose			(void * ctx)<br />
</pre><p>Close the connection and both control and transport</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 incase of error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlNanoFTPCloseConnection" id="xmlNanoFTPCloseConnection"></a>Function: xmlNanoFTPCloseConnection</h3><pre class="programlisting">int	xmlNanoFTPCloseConnection	(void * ctx)<br />
</pre><p>Close the data connection from the server</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 incase of error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlNanoFTPConnect" id="xmlNanoFTPConnect"></a>Function: xmlNanoFTPConnect</h3><pre class="programlisting">int	xmlNanoFTPConnect		(void * ctx)<br />
</pre><p>Tries to open a control connection</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlNanoFTPConnectTo" id="xmlNanoFTPConnectTo"></a>Function: xmlNanoFTPConnectTo</h3><pre class="programlisting">void *	xmlNanoFTPConnectTo		(const char * server, <br />					 int port)<br />
</pre><p>Tries to open a control connection to the given server/port</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>server</tt></i>:</span></td><td>an FTP server name</td></tr><tr><td><span class="term"><i><tt>port</tt></i>:</span></td><td>the port (use 21 if 0)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an fTP context or NULL if it failed</td></tr></tbody></table></div><h3><a name="xmlNanoFTPCwd" id="xmlNanoFTPCwd"></a>Function: xmlNanoFTPCwd</h3><pre class="programlisting">int	xmlNanoFTPCwd			(void * ctx, <br />					 const char * directory)<br />
</pre><p>Tries to change the remote directory</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>directory</tt></i>:</span></td><td>a directory on the server</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 incase of error, 1 if CWD worked, 0 if it failed</td></tr></tbody></table></div><h3><a name="xmlNanoFTPDele" id="xmlNanoFTPDele"></a>Function: xmlNanoFTPDele</h3><pre class="programlisting">int	xmlNanoFTPDele			(void * ctx, <br />					 const char * file)<br />
</pre><p>Tries to delete an item (file or directory) from server</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>a file or directory on the server</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 incase of error, 1 if DELE worked, 0 if it failed</td></tr></tbody></table></div><h3><a name="xmlNanoFTPFreeCtxt" id="xmlNanoFTPFreeCtxt"></a>Function: xmlNanoFTPFreeCtxt</h3><pre class="programlisting">void	xmlNanoFTPFreeCtxt		(void * ctx)<br />
</pre><p>Frees the context after closing the connection.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr></tbody></table></div><h3><a name="xmlNanoFTPGet" id="xmlNanoFTPGet"></a>Function: xmlNanoFTPGet</h3><pre class="programlisting">int	xmlNanoFTPGet			(void * ctx, <br />					 <a href="libxml-nanoftp.html#ftpDataCallback">ftpDataCallback</a> callback, <br />					 void * userData, <br />					 const char * filename)<br />
</pre><p>Fetch the given file from the server. All data are passed back in the callbacks. The last callback has a size of 0 block.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>callback</tt></i>:</span></td><td>the user callback</td></tr><tr><td><span class="term"><i><tt>userData</tt></i>:</span></td><td>the user callback data</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file to retrieve</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 incase of error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlNanoFTPGetConnection" id="xmlNanoFTPGetConnection"></a>Function: xmlNanoFTPGetConnection</h3><pre class="programlisting"><a href="libxml-nanoftp.html#SOCKET">SOCKET</a>	xmlNanoFTPGetConnection		(void * ctx)<br />
</pre><p>Try to open a data connection to the server. Currently only passive mode is supported.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 incase of error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlNanoFTPGetResponse" id="xmlNanoFTPGetResponse"></a>Function: xmlNanoFTPGetResponse</h3><pre class="programlisting">int	xmlNanoFTPGetResponse		(void * ctx)<br />
</pre><p>Get the response from the FTP server after a command.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the code number</td></tr></tbody></table></div><h3><a name="xmlNanoFTPGetSocket" id="xmlNanoFTPGetSocket"></a>Function: xmlNanoFTPGetSocket</h3><pre class="programlisting"><a href="libxml-nanoftp.html#SOCKET">SOCKET</a>	xmlNanoFTPGetSocket		(void * ctx, <br />					 const char * filename)<br />
</pre><p>Initiate fetch of the given file from the server.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file to retrieve (or NULL if path is in context).</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the socket for the data connection, or &lt;0 in case of error</td></tr></tbody></table></div><h3><a name="xmlNanoFTPInit" id="xmlNanoFTPInit"></a>Function: xmlNanoFTPInit</h3><pre class="programlisting">void	xmlNanoFTPInit			(void)<br />
</pre><p>Initialize the FTP protocol layer. Currently it just checks for proxy informations, and get the hostname</p>
<h3><a name="xmlNanoFTPList" id="xmlNanoFTPList"></a>Function: xmlNanoFTPList</h3><pre class="programlisting">int	xmlNanoFTPList			(void * ctx, <br />					 <a href="libxml-nanoftp.html#ftpListCallback">ftpListCallback</a> callback, <br />					 void * userData, <br />					 const char * filename)<br />
</pre><p>Do a listing on the server. All files info are passed back in the callbacks.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>callback</tt></i>:</span></td><td>the user callback</td></tr><tr><td><span class="term"><i><tt>userData</tt></i>:</span></td><td>the user callback data</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>optional files to list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 incase of error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlNanoFTPNewCtxt" id="xmlNanoFTPNewCtxt"></a>Function: xmlNanoFTPNewCtxt</h3><pre class="programlisting">void *	xmlNanoFTPNewCtxt		(const char * URL)<br />
</pre><p>Allocate and initialize a new FTP context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL used to initialize the context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an FTP context or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlNanoFTPOpen" id="xmlNanoFTPOpen"></a>Function: xmlNanoFTPOpen</h3><pre class="programlisting">void *	xmlNanoFTPOpen			(const char * URL)<br />
</pre><p>Start to fetch the given ftp:// resource</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the URL to the resource</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an FTP context, or NULL</td></tr></tbody></table></div><h3><a name="xmlNanoFTPProxy" id="xmlNanoFTPProxy"></a>Function: xmlNanoFTPProxy</h3><pre class="programlisting">void	xmlNanoFTPProxy			(const char * host, <br />					 int port, <br />					 const char * user, <br />					 const char * passwd, <br />					 int type)<br />
</pre><p>Setup the FTP proxy informations. This can also be done by using ftp_proxy ftp_proxy_user and ftp_proxy_password environment variables.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>host</tt></i>:</span></td><td>the proxy host name</td></tr><tr><td><span class="term"><i><tt>port</tt></i>:</span></td><td>the proxy port</td></tr><tr><td><span class="term"><i><tt>user</tt></i>:</span></td><td>the proxy user name</td></tr><tr><td><span class="term"><i><tt>passwd</tt></i>:</span></td><td>the proxy password</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the type of proxy 1 for using SITE, 2 for USER a@b</td></tr></tbody></table></div><h3><a name="xmlNanoFTPQuit" id="xmlNanoFTPQuit"></a>Function: xmlNanoFTPQuit</h3><pre class="programlisting">int	xmlNanoFTPQuit			(void * ctx)<br />
</pre><p>Send a QUIT command to the server</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlNanoFTPRead" id="xmlNanoFTPRead"></a>Function: xmlNanoFTPRead</h3><pre class="programlisting">int	xmlNanoFTPRead			(void * ctx, <br />					 void * dest, <br />					 int len)<br />
</pre><p>This function tries to read @len bytes from the existing FTP connection and saves them in @dest. This is a blocking call.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the FTP context</td></tr><tr><td><span class="term"><i><tt>dest</tt></i>:</span></td><td>a buffer</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the buffer length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte read. 0 is an indication of an end of connection. -1 indicates a parameter error.</td></tr></tbody></table></div><h3><a name="xmlNanoFTPScanProxy" id="xmlNanoFTPScanProxy"></a>Function: xmlNanoFTPScanProxy</h3><pre class="programlisting">void	xmlNanoFTPScanProxy		(const char * URL)<br />
</pre><p>(Re)Initialize the FTP Proxy context by parsing the URL and finding the protocol host port it indicates. Should be like ftp://myproxy/ or ftp://myproxy:3128/ A NULL URL cleans up proxy informations.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The proxy URL used to initialize the proxy context</td></tr></tbody></table></div><h3><a name="xmlNanoFTPUpdateURL" id="xmlNanoFTPUpdateURL"></a>Function: xmlNanoFTPUpdateURL</h3><pre class="programlisting">int	xmlNanoFTPUpdateURL		(void * ctx, <br />					 const char * URL)<br />
</pre><p>Update an FTP context by parsing the URL and finding new path it indicates. If there is an error in the protocol, hostname, port or other information, the error is raised. It indicates a new connection has to be established.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an FTP context</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL used to update the context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if Ok, -1 in case of error (other host).</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
