<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module nanohttp from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module nanohttp from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-nanoftp.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-nanoftp.html">nanoftp</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-parser.html">parser</a></th><td><a accesskey="n" href="libxml-parser.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>minimal HTTP implementation allowing to fetch resources like external subset. </p><h2>Table of Contents</h2><pre class="programlisting">const char *	<a href="#xmlNanoHTTPAuthHeader">xmlNanoHTTPAuthHeader</a>	(void * ctx)</pre>
<pre class="programlisting">void	<a href="#xmlNanoHTTPCleanup">xmlNanoHTTPCleanup</a>		(void)</pre>
<pre class="programlisting">void	<a href="#xmlNanoHTTPClose">xmlNanoHTTPClose</a>		(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoHTTPContentLength">xmlNanoHTTPContentLength</a>	(void * ctx)</pre>
<pre class="programlisting">const char *	<a href="#xmlNanoHTTPEncoding">xmlNanoHTTPEncoding</a>	(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoHTTPFetch">xmlNanoHTTPFetch</a>		(const char * URL, <br />					 const char * filename, <br />					 char ** contentType)</pre>
<pre class="programlisting">void	<a href="#xmlNanoHTTPInit">xmlNanoHTTPInit</a>			(void)</pre>
<pre class="programlisting">void *	<a href="#xmlNanoHTTPMethod">xmlNanoHTTPMethod</a>		(const char * URL, <br />					 const char * method, <br />					 const char * input, <br />					 char ** contentType, <br />					 const char * headers, <br />					 int ilen)</pre>
<pre class="programlisting">void *	<a href="#xmlNanoHTTPMethodRedir">xmlNanoHTTPMethodRedir</a>		(const char * URL, <br />					 const char * method, <br />					 const char * input, <br />					 char ** contentType, <br />					 char ** redir, <br />					 const char * headers, <br />					 int ilen)</pre>
<pre class="programlisting">const char *	<a href="#xmlNanoHTTPMimeType">xmlNanoHTTPMimeType</a>	(void * ctx)</pre>
<pre class="programlisting">void *	<a href="#xmlNanoHTTPOpen">xmlNanoHTTPOpen</a>			(const char * URL, <br />					 char ** contentType)</pre>
<pre class="programlisting">void *	<a href="#xmlNanoHTTPOpenRedir">xmlNanoHTTPOpenRedir</a>		(const char * URL, <br />					 char ** contentType, <br />					 char ** redir)</pre>
<pre class="programlisting">int	<a href="#xmlNanoHTTPRead">xmlNanoHTTPRead</a>			(void * ctx, <br />					 void * dest, <br />					 int len)</pre>
<pre class="programlisting">const char *	<a href="#xmlNanoHTTPRedir">xmlNanoHTTPRedir</a>	(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoHTTPReturnCode">xmlNanoHTTPReturnCode</a>		(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlNanoHTTPSave">xmlNanoHTTPSave</a>			(void * ctxt, <br />					 const char * filename)</pre>
<pre class="programlisting">void	<a href="#xmlNanoHTTPScanProxy">xmlNanoHTTPScanProxy</a>		(const char * URL)</pre>
<h2>Description</h2>
<h3><a name="xmlNanoHTTPAuthHeader" id="xmlNanoHTTPAuthHeader"></a>Function: xmlNanoHTTPAuthHeader</h3><pre class="programlisting">const char *	xmlNanoHTTPAuthHeader	(void * ctx)<br />
</pre><p>Get the authentication header of an HTTP context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the stashed value of the WWW-Authenticate or Proxy-Authenticate header.</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPCleanup" id="xmlNanoHTTPCleanup"></a>Function: xmlNanoHTTPCleanup</h3><pre class="programlisting">void	xmlNanoHTTPCleanup		(void)<br />
</pre><p>Cleanup the HTTP protocol layer.</p>
<h3><a name="xmlNanoHTTPClose" id="xmlNanoHTTPClose"></a>Function: xmlNanoHTTPClose</h3><pre class="programlisting">void	xmlNanoHTTPClose		(void * ctx)<br />
</pre><p>This function closes an HTTP context, it ends up the connection and free all data related to it.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPContentLength" id="xmlNanoHTTPContentLength"></a>Function: xmlNanoHTTPContentLength</h3><pre class="programlisting">int	xmlNanoHTTPContentLength	(void * ctx)<br />
</pre><p>Provides the specified content length from the HTTP header.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the specified content length from the HTTP header. Note that a value of -1 indicates that the content length element was not included in the response header.</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPEncoding" id="xmlNanoHTTPEncoding"></a>Function: xmlNanoHTTPEncoding</h3><pre class="programlisting">const char *	xmlNanoHTTPEncoding	(void * ctx)<br />
</pre><p>Provides the specified encoding if specified in the HTTP headers.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the specified encoding or NULL if not available</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPFetch" id="xmlNanoHTTPFetch"></a>Function: xmlNanoHTTPFetch</h3><pre class="programlisting">int	xmlNanoHTTPFetch		(const char * URL, <br />					 const char * filename, <br />					 char ** contentType)<br />
</pre><p>This function try to fetch the indicated resource via HTTP GET and save it's content in the file.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename where the content should be saved</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>if available the Content-Type information will be returned at that location</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of failure, 0 incase of success. The contentType, if provided must be freed by the caller</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPInit" id="xmlNanoHTTPInit"></a>Function: xmlNanoHTTPInit</h3><pre class="programlisting">void	xmlNanoHTTPInit			(void)<br />
</pre><p>Initialize the HTTP protocol layer. Currently it just checks for proxy informations</p>
<h3><a name="xmlNanoHTTPMethod" id="xmlNanoHTTPMethod"></a>Function: xmlNanoHTTPMethod</h3><pre class="programlisting">void *	xmlNanoHTTPMethod		(const char * URL, <br />					 const char * method, <br />					 const char * input, <br />					 char ** contentType, <br />					 const char * headers, <br />					 int ilen)<br />
</pre><p>This function try to open a connection to the indicated resource via HTTP using the given @method, adding the given extra headers and the input buffer for the request content.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>method</tt></i>:</span></td><td>the HTTP method to use</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the input string if any</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>the Content-Type information IN and OUT</td></tr><tr><td><span class="term"><i><tt>headers</tt></i>:</span></td><td>the extra headers</td></tr><tr><td><span class="term"><i><tt>ilen</tt></i>:</span></td><td>input length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL in case of failure, otherwise a request handler. The contentType, if provided must be freed by the caller</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPMethodRedir" id="xmlNanoHTTPMethodRedir"></a>Function: xmlNanoHTTPMethodRedir</h3><pre class="programlisting">void *	xmlNanoHTTPMethodRedir		(const char * URL, <br />					 const char * method, <br />					 const char * input, <br />					 char ** contentType, <br />					 char ** redir, <br />					 const char * headers, <br />					 int ilen)<br />
</pre><p>This function try to open a connection to the indicated resource via HTTP using the given @method, adding the given extra headers and the input buffer for the request content.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>method</tt></i>:</span></td><td>the HTTP method to use</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the input string if any</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>the Content-Type information IN and OUT</td></tr><tr><td><span class="term"><i><tt>redir</tt></i>:</span></td><td>the redirected URL OUT</td></tr><tr><td><span class="term"><i><tt>headers</tt></i>:</span></td><td>the extra headers</td></tr><tr><td><span class="term"><i><tt>ilen</tt></i>:</span></td><td>input length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL in case of failure, otherwise a request handler. The contentType, or redir, if provided must be freed by the caller</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPMimeType" id="xmlNanoHTTPMimeType"></a>Function: xmlNanoHTTPMimeType</h3><pre class="programlisting">const char *	xmlNanoHTTPMimeType	(void * ctx)<br />
</pre><p>Provides the specified Mime-Type if specified in the HTTP headers.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the specified Mime-Type or NULL if not available</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPOpen" id="xmlNanoHTTPOpen"></a>Function: xmlNanoHTTPOpen</h3><pre class="programlisting">void *	xmlNanoHTTPOpen			(const char * URL, <br />					 char ** contentType)<br />
</pre><p>This function try to open a connection to the indicated resource via HTTP GET.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>if available the Content-Type information will be returned at that location</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL in case of failure, otherwise a request handler. The contentType, if provided must be freed by the caller</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPOpenRedir" id="xmlNanoHTTPOpenRedir"></a>Function: xmlNanoHTTPOpenRedir</h3><pre class="programlisting">void *	xmlNanoHTTPOpenRedir		(const char * URL, <br />					 char ** contentType, <br />					 char ** redir)<br />
</pre><p>This function try to open a connection to the indicated resource via HTTP GET.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>if available the Content-Type information will be returned at that location</td></tr><tr><td><span class="term"><i><tt>redir</tt></i>:</span></td><td>if available the redirected URL will be returned</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL in case of failure, otherwise a request handler. The contentType, if provided must be freed by the caller</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPRead" id="xmlNanoHTTPRead"></a>Function: xmlNanoHTTPRead</h3><pre class="programlisting">int	xmlNanoHTTPRead			(void * ctx, <br />					 void * dest, <br />					 int len)<br />
</pre><p>This function tries to read @len bytes from the existing HTTP connection and saves them in @dest. This is a blocking call.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>dest</tt></i>:</span></td><td>a buffer</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the buffer length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte read. 0 is an indication of an end of connection. -1 indicates a parameter error.</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPRedir" id="xmlNanoHTTPRedir"></a>Function: xmlNanoHTTPRedir</h3><pre class="programlisting">const char *	xmlNanoHTTPRedir	(void * ctx)<br />
</pre><p>Provides the specified redirection URL if available from the HTTP header.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the specified redirection URL or NULL if not redirected.</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPReturnCode" id="xmlNanoHTTPReturnCode"></a>Function: xmlNanoHTTPReturnCode</h3><pre class="programlisting">int	xmlNanoHTTPReturnCode		(void * ctx)<br />
</pre><p>Get the latest HTTP return code received</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the HTTP return code for the request.</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPSave" id="xmlNanoHTTPSave"></a>Function: xmlNanoHTTPSave</h3><pre class="programlisting">int	xmlNanoHTTPSave			(void * ctxt, <br />					 const char * filename)<br />
</pre><p>This function saves the output of the HTTP transaction to a file It closes and free the context at the end</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename where the content should be saved</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of failure, 0 incase of success.</td></tr></tbody></table></div><h3><a name="xmlNanoHTTPScanProxy" id="xmlNanoHTTPScanProxy"></a>Function: xmlNanoHTTPScanProxy</h3><pre class="programlisting">void	xmlNanoHTTPScanProxy		(const char * URL)<br />
</pre><p>(Re)Initialize the HTTP Proxy context by parsing the URL and finding the protocol host port it indicates. Should be like http://myproxy/ or http://myproxy:3128/ A NULL URL cleans up proxy informations.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The proxy URL used to initialize the proxy context</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
