<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module debugXML from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module debugXML from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-chvalid.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-chvalid.html">chvalid</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-dict.html">dict</a></th><td><a accesskey="n" href="libxml-dict.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>Interfaces to a set of routines used for debugging the tree produced by the XML parser. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlShellCtxt">xmlShellCtxt</a><br />struct _xmlShellCtxt
</pre><pre class="programlisting">Typedef <a href="libxml-debugXML.html#xmlShellCtxt">xmlShellCtxt</a> * <a name="xmlShellCtxtPtr" id="xmlShellCtxtPtr">xmlShellCtxtPtr</a>
</pre><pre class="programlisting">const char *	<a href="#xmlBoolToText">xmlBoolToText</a>		(int boolval)</pre>
<pre class="programlisting">int	<a href="#xmlDebugCheckDocument">xmlDebugCheckDocument</a>		(FILE * output, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpAttr">xmlDebugDumpAttr</a>		(FILE * output, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br />					 int depth)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpAttrList">xmlDebugDumpAttrList</a>		(FILE * output, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br />					 int depth)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpDTD">xmlDebugDumpDTD</a>			(FILE * output, <br />					 <a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> dtd)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpDocument">xmlDebugDumpDocument</a>		(FILE * output, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpDocumentHead">xmlDebugDumpDocumentHead</a>	(FILE * output, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpEntities">xmlDebugDumpEntities</a>		(FILE * output, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpNode">xmlDebugDumpNode</a>		(FILE * output, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int depth)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpNodeList">xmlDebugDumpNodeList</a>		(FILE * output, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int depth)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpOneNode">xmlDebugDumpOneNode</a>		(FILE * output, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int depth)</pre>
<pre class="programlisting">void	<a href="#xmlDebugDumpString">xmlDebugDumpString</a>		(FILE * output, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting">int	<a href="#xmlLsCountNode">xmlLsCountNode</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">void	<a href="#xmlLsOneNode">xmlLsOneNode</a>			(FILE * output, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">void	<a href="#xmlShell">xmlShell</a>			(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 char * filename, <br />					 <a href="libxml-debugXML.html#xmlShellReadlineFunc">xmlShellReadlineFunc</a> input, <br />					 FILE * output)</pre>
<pre class="programlisting">int	<a href="#xmlShellBase">xmlShellBase</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">int	<a href="#xmlShellCat">xmlShellCat</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">Function type: <a href="#xmlShellCmd">xmlShellCmd</a>
int	<a href="#xmlShellCmd">xmlShellCmd</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)
</pre>
<pre class="programlisting">int	<a href="#xmlShellDir">xmlShellDir</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">int	<a href="#xmlShellDu">xmlShellDu</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">int	<a href="#xmlShellList">xmlShellList</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">int	<a href="#xmlShellLoad">xmlShellLoad</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * filename, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">void	<a href="#xmlShellPrintNode">xmlShellPrintNode</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">void	<a href="#xmlShellPrintXPathError">xmlShellPrintXPathError</a>		(int errorType, <br />					 const char * arg)</pre>
<pre class="programlisting">void	<a href="#xmlShellPrintXPathResult">xmlShellPrintXPathResult</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> list)</pre>
<pre class="programlisting">int	<a href="#xmlShellPwd">xmlShellPwd</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * buffer, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">Function type: <a href="#xmlShellReadlineFunc">xmlShellReadlineFunc</a>
char *	<a href="#xmlShellReadlineFunc">xmlShellReadlineFunc</a>		(char * prompt)
</pre>
<pre class="programlisting">int	<a href="#xmlShellSave">xmlShellSave</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * filename, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">int	<a href="#xmlShellValidate">xmlShellValidate</a>		(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * dtd, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<pre class="programlisting">int	<a href="#xmlShellWrite">xmlShellWrite</a>			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * filename, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)</pre>
<h2>Description</h2>
<h3><a name="xmlShellCtxt" id="xmlShellCtxt">Structure xmlShellCtxt</a></h3><pre class="programlisting">Structure xmlShellCtxt<br />struct _xmlShellCtxt {
    char *	filename
    <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	doc
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node
    <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a>	pctxt
    int	loaded
    FILE *	output
    <a href="libxml-debugXML.html#xmlShellReadlineFunc">xmlShellReadlineFunc</a>	input
}</pre><h3><a name="xmlBoolToText" id="xmlBoolToText"></a>Function: xmlBoolToText</h3><pre class="programlisting">const char *	xmlBoolToText		(int boolval)<br />
</pre><p>Convenient way to turn bool into text</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>boolval</tt></i>:</span></td><td>a bool to turn into text</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to either "True" or "False"</td></tr></tbody></table></div><h3><a name="xmlDebugCheckDocument" id="xmlDebugCheckDocument"></a>Function: xmlDebugCheckDocument</h3><pre class="programlisting">int	xmlDebugCheckDocument		(FILE * output, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Check the document for potential content problems, and output the errors to @output</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of errors found</td></tr></tbody></table></div><h3><a name="xmlDebugDumpAttr" id="xmlDebugDumpAttr"></a>Function: xmlDebugDumpAttr</h3><pre class="programlisting">void	xmlDebugDumpAttr		(FILE * output, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br />					 int depth)<br />
</pre><p>Dumps debug information for the <a href="libxml-SAX.html#attribute">attribute</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>attr</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div><h3><a name="xmlDebugDumpAttrList" id="xmlDebugDumpAttrList"></a>Function: xmlDebugDumpAttrList</h3><pre class="programlisting">void	xmlDebugDumpAttrList		(FILE * output, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br />					 int depth)<br />
</pre><p>Dumps debug information for the <a href="libxml-SAX.html#attribute">attribute</a> list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>attr</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> list</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div><h3><a name="xmlDebugDumpDTD" id="xmlDebugDumpDTD"></a>Function: xmlDebugDumpDTD</h3><pre class="programlisting">void	xmlDebugDumpDTD			(FILE * output, <br />					 <a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> dtd)<br />
</pre><p>Dumps debug information for the DTD</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>dtd</tt></i>:</span></td><td>the DTD</td></tr></tbody></table></div><h3><a name="xmlDebugDumpDocument" id="xmlDebugDumpDocument"></a>Function: xmlDebugDumpDocument</h3><pre class="programlisting">void	xmlDebugDumpDocument		(FILE * output, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Dumps debug information for the document, it's recursive</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr></tbody></table></div><h3><a name="xmlDebugDumpDocumentHead" id="xmlDebugDumpDocumentHead"></a>Function: xmlDebugDumpDocumentHead</h3><pre class="programlisting">void	xmlDebugDumpDocumentHead	(FILE * output, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Dumps debug information cncerning the document, not recursive</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr></tbody></table></div><h3><a name="xmlDebugDumpEntities" id="xmlDebugDumpEntities"></a>Function: xmlDebugDumpEntities</h3><pre class="programlisting">void	xmlDebugDumpEntities		(FILE * output, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Dumps debug information for all the entities in use by the document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr></tbody></table></div><h3><a name="xmlDebugDumpNode" id="xmlDebugDumpNode"></a>Function: xmlDebugDumpNode</h3><pre class="programlisting">void	xmlDebugDumpNode		(FILE * output, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int depth)<br />
</pre><p>Dumps debug information for the element node, it is recursive</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div><h3><a name="xmlDebugDumpNodeList" id="xmlDebugDumpNodeList"></a>Function: xmlDebugDumpNodeList</h3><pre class="programlisting">void	xmlDebugDumpNodeList		(FILE * output, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int depth)<br />
</pre><p>Dumps debug information for the list of element node, it is recursive</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node list</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div><h3><a name="xmlDebugDumpOneNode" id="xmlDebugDumpOneNode"></a>Function: xmlDebugDumpOneNode</h3><pre class="programlisting">void	xmlDebugDumpOneNode		(FILE * output, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int depth)<br />
</pre><p>Dumps debug information for the element node, it is not recursive</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div><h3><a name="xmlDebugDumpString" id="xmlDebugDumpString"></a>Function: xmlDebugDumpString</h3><pre class="programlisting">void	xmlDebugDumpString		(FILE * output, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>Dumps informations about the string, shorten it if necessary</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string</td></tr></tbody></table></div><h3><a name="xmlLsCountNode" id="xmlLsCountNode"></a>Function: xmlLsCountNode</h3><pre class="programlisting">int	xmlLsCountNode			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Count the children of @node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to count</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of children of @node.</td></tr></tbody></table></div><h3><a name="xmlLsOneNode" id="xmlLsOneNode"></a>Function: xmlLsOneNode</h3><pre class="programlisting">void	xmlLsOneNode			(FILE * output, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Dump to @output the type and name of @node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to dump</td></tr></tbody></table></div><h3><a name="xmlShell" id="xmlShell"></a>Function: xmlShell</h3><pre class="programlisting">void	xmlShell			(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 char * filename, <br />					 <a href="libxml-debugXML.html#xmlShellReadlineFunc">xmlShellReadlineFunc</a> input, <br />					 FILE * output)<br />
</pre><p>Implements the XML shell This allow to load, validate, view, modify and save a document using a environment similar to a UNIX commandline.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the initial document</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the output buffer</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the line reading function</td></tr><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the output FILE*, defaults to stdout if NULL</td></tr></tbody></table></div><h3><a name="xmlShellBase" id="xmlShellBase"></a>Function: xmlShellBase</h3><pre class="programlisting">int	xmlShellBase			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "base" dumps the current XML base of the node</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div><h3><a name="xmlShellCat" id="xmlShellCat"></a>Function: xmlShellCat</h3><pre class="programlisting">int	xmlShellCat			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "cat" dumps the serialization node content (XML or HTML).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div><h3><a name="xmlShellCmd" id="xmlShellCmd"></a>Function type: xmlShellCmd</h3><pre class="programlisting">Function type: xmlShellCmd
int	xmlShellCmd			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)
</pre><p>This is a generic signature for the XML shell functions.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>a string argument</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a first node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>a second node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int, negative returns indicating errors.</td></tr></tbody></table></div><br />
<h3><a name="xmlShellDir" id="xmlShellDir"></a>Function: xmlShellDir</h3><pre class="programlisting">int	xmlShellDir			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "dir" dumps informations about the node (namespace, attributes, content).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div><h3><a name="xmlShellDu" id="xmlShellDu"></a>Function: xmlShellDu</h3><pre class="programlisting">int	xmlShellDu			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "du" show the structure of the subtree under node @tree If @tree is null, the command works on the current node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>a node defining a subtree</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlShellList" id="xmlShellList"></a>Function: xmlShellList</h3><pre class="programlisting">int	xmlShellList			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * arg, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "ls" Does an Unix like listing of the given node (like a directory)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div><h3><a name="xmlShellLoad" id="xmlShellLoad"></a>Function: xmlShellLoad</h3><pre class="programlisting">int	xmlShellLoad			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * filename, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "load" loads a new document specified by the filename</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file name</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 if loading failed</td></tr></tbody></table></div><h3><a name="xmlShellPrintNode" id="xmlShellPrintNode"></a>Function: xmlShellPrintNode</h3><pre class="programlisting">void	xmlShellPrintNode		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Print node to the output FILE</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a non-null node to print to the output FILE</td></tr></tbody></table></div><h3><a name="xmlShellPrintXPathError" id="xmlShellPrintXPathError"></a>Function: xmlShellPrintXPathError</h3><pre class="programlisting">void	xmlShellPrintXPathError		(int errorType, <br />					 const char * arg)<br />
</pre><p>Print the xpath error to libxml default error channel</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>errorType</tt></i>:</span></td><td>valid xpath error id</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>the argument that cause xpath to fail</td></tr></tbody></table></div><h3><a name="xmlShellPrintXPathResult" id="xmlShellPrintXPathResult"></a>Function: xmlShellPrintXPathResult</h3><pre class="programlisting">void	xmlShellPrintXPathResult	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> list)<br />
</pre><p>Prints result to the output FILE</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>list</tt></i>:</span></td><td>a valid result generated by an xpath evaluation</td></tr></tbody></table></div><h3><a name="xmlShellPwd" id="xmlShellPwd"></a>Function: xmlShellPwd</h3><pre class="programlisting">int	xmlShellPwd			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * buffer, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "pwd" Show the full path from the root to the node, if needed building thumblers when similar elements exists at a given ancestor level. The output is compatible with XPath commands.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>the output buffer</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlShellReadlineFunc" id="xmlShellReadlineFunc"></a>Function type: xmlShellReadlineFunc</h3><pre class="programlisting">Function type: xmlShellReadlineFunc
char *	xmlShellReadlineFunc		(char * prompt)
</pre><p>This is a generic signature for the XML shell input function.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>prompt</tt></i>:</span></td><td>a string prompt</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string which will be freed by the Shell.</td></tr></tbody></table></div><br />
<h3><a name="xmlShellSave" id="xmlShellSave"></a>Function: xmlShellSave</h3><pre class="programlisting">int	xmlShellSave			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * filename, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "save" Write the current document to the filename, or it's original name</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file name (optional)</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlShellValidate" id="xmlShellValidate"></a>Function: xmlShellValidate</h3><pre class="programlisting">int	xmlShellValidate		(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * dtd, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "validate" Validate the document, if a DTD path is provided, then the validation is done against the given DTD.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>dtd</tt></i>:</span></td><td>the DTD URI (optional)</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlShellWrite" id="xmlShellWrite"></a>Function: xmlShellWrite</h3><pre class="programlisting">int	xmlShellWrite			(<a href="libxml-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br />					 char * filename, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br />
</pre><p>Implements the XML shell function "write" Write the current node to the filename, it saves the serialization of the subtree under the @node specified</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file name</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node in the tree</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
