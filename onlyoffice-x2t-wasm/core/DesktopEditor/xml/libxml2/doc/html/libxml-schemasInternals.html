<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module schemasInternals from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module schemasInternals from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-relaxng.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-relaxng.html">relaxng</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-schematron.html">schematron</a></th><td><a accesskey="n" href="libxml-schematron.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>internal interfaces for the XML Schemas handling and schema validity checking The Schemas development is a Work In Progress. Some of those interfaces are not garanteed to be API or ABI stable ! </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#XML_SCHEMAS_ANYATTR_LAX">XML_SCHEMAS_ANYATTR_LAX</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ANYATTR_SKIP">XML_SCHEMAS_ANYATTR_SKIP</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ANYATTR_STRICT">XML_SCHEMAS_ANYATTR_STRICT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ANY_LAX">XML_SCHEMAS_ANY_LAX</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ANY_SKIP">XML_SCHEMAS_ANY_SKIP</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ANY_STRICT">XML_SCHEMAS_ANY_STRICT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTRGROUP_GLOBAL">XML_SCHEMAS_ATTRGROUP_GLOBAL</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTRGROUP_HAS_REFS">XML_SCHEMAS_ATTRGROUP_HAS_REFS</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTRGROUP_MARKED">XML_SCHEMAS_ATTRGROUP_MARKED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTRGROUP_REDEFINED">XML_SCHEMAS_ATTRGROUP_REDEFINED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTRGROUP_WILDCARD_BUILDED">XML_SCHEMAS_ATTRGROUP_WILDCARD_BUILDED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTR_FIXED">XML_SCHEMAS_ATTR_FIXED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTR_GLOBAL">XML_SCHEMAS_ATTR_GLOBAL</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTR_INTERNAL_RESOLVED">XML_SCHEMAS_ATTR_INTERNAL_RESOLVED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTR_NSDEFAULT">XML_SCHEMAS_ATTR_NSDEFAULT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTR_USE_OPTIONAL">XML_SCHEMAS_ATTR_USE_OPTIONAL</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTR_USE_PROHIBITED">XML_SCHEMAS_ATTR_USE_PROHIBITED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ATTR_USE_REQUIRED">XML_SCHEMAS_ATTR_USE_REQUIRED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_BLOCK_DEFAULT_EXTENSION">XML_SCHEMAS_BLOCK_DEFAULT_EXTENSION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_BLOCK_DEFAULT_RESTRICTION">XML_SCHEMAS_BLOCK_DEFAULT_RESTRICTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_BLOCK_DEFAULT_SUBSTITUTION">XML_SCHEMAS_BLOCK_DEFAULT_SUBSTITUTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_ABSTRACT">XML_SCHEMAS_ELEM_ABSTRACT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_BLOCK_ABSENT">XML_SCHEMAS_ELEM_BLOCK_ABSENT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_BLOCK_EXTENSION">XML_SCHEMAS_ELEM_BLOCK_EXTENSION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_BLOCK_RESTRICTION">XML_SCHEMAS_ELEM_BLOCK_RESTRICTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_BLOCK_SUBSTITUTION">XML_SCHEMAS_ELEM_BLOCK_SUBSTITUTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_CIRCULAR">XML_SCHEMAS_ELEM_CIRCULAR</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_DEFAULT">XML_SCHEMAS_ELEM_DEFAULT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_FINAL_ABSENT">XML_SCHEMAS_ELEM_FINAL_ABSENT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_FINAL_EXTENSION">XML_SCHEMAS_ELEM_FINAL_EXTENSION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_FINAL_RESTRICTION">XML_SCHEMAS_ELEM_FINAL_RESTRICTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_FIXED">XML_SCHEMAS_ELEM_FIXED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_GLOBAL">XML_SCHEMAS_ELEM_GLOBAL</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_INTERNAL_CHECKED">XML_SCHEMAS_ELEM_INTERNAL_CHECKED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_INTERNAL_RESOLVED">XML_SCHEMAS_ELEM_INTERNAL_RESOLVED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_NILLABLE">XML_SCHEMAS_ELEM_NILLABLE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_NSDEFAULT">XML_SCHEMAS_ELEM_NSDEFAULT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_REF">XML_SCHEMAS_ELEM_REF</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_SUBST_GROUP_HEAD">XML_SCHEMAS_ELEM_SUBST_GROUP_HEAD</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_ELEM_TOPLEVEL">XML_SCHEMAS_ELEM_TOPLEVEL</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_FACET_COLLAPSE">XML_SCHEMAS_FACET_COLLAPSE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_FACET_PRESERVE">XML_SCHEMAS_FACET_PRESERVE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_FACET_REPLACE">XML_SCHEMAS_FACET_REPLACE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_FACET_UNKNOWN">XML_SCHEMAS_FACET_UNKNOWN</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_FINAL_DEFAULT_EXTENSION">XML_SCHEMAS_FINAL_DEFAULT_EXTENSION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_FINAL_DEFAULT_LIST">XML_SCHEMAS_FINAL_DEFAULT_LIST</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_FINAL_DEFAULT_RESTRICTION">XML_SCHEMAS_FINAL_DEFAULT_RESTRICTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_FINAL_DEFAULT_UNION">XML_SCHEMAS_FINAL_DEFAULT_UNION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_INCLUDING_CONVERT_NS">XML_SCHEMAS_INCLUDING_CONVERT_NS</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_QUALIF_ATTR">XML_SCHEMAS_QUALIF_ATTR</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_QUALIF_ELEM">XML_SCHEMAS_QUALIF_ELEM</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_ABSTRACT">XML_SCHEMAS_TYPE_ABSTRACT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_BLOCK_DEFAULT">XML_SCHEMAS_TYPE_BLOCK_DEFAULT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_BLOCK_EXTENSION">XML_SCHEMAS_TYPE_BLOCK_EXTENSION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_BLOCK_RESTRICTION">XML_SCHEMAS_TYPE_BLOCK_RESTRICTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_BUILTIN_PRIMITIVE">XML_SCHEMAS_TYPE_BUILTIN_PRIMITIVE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_DERIVATION_METHOD_EXTENSION">XML_SCHEMAS_TYPE_DERIVATION_METHOD_EXTENSION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_DERIVATION_METHOD_RESTRICTION">XML_SCHEMAS_TYPE_DERIVATION_METHOD_RESTRICTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_FACETSNEEDVALUE">XML_SCHEMAS_TYPE_FACETSNEEDVALUE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_FINAL_DEFAULT">XML_SCHEMAS_TYPE_FINAL_DEFAULT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_FINAL_EXTENSION">XML_SCHEMAS_TYPE_FINAL_EXTENSION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_FINAL_LIST">XML_SCHEMAS_TYPE_FINAL_LIST</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_FINAL_RESTRICTION">XML_SCHEMAS_TYPE_FINAL_RESTRICTION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_FINAL_UNION">XML_SCHEMAS_TYPE_FINAL_UNION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_FIXUP_1">XML_SCHEMAS_TYPE_FIXUP_1</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_GLOBAL">XML_SCHEMAS_TYPE_GLOBAL</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_HAS_FACETS">XML_SCHEMAS_TYPE_HAS_FACETS</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_INTERNAL_INVALID">XML_SCHEMAS_TYPE_INTERNAL_INVALID</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_INTERNAL_RESOLVED">XML_SCHEMAS_TYPE_INTERNAL_RESOLVED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_MARKED">XML_SCHEMAS_TYPE_MARKED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_MIXED">XML_SCHEMAS_TYPE_MIXED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_NORMVALUENEEDED">XML_SCHEMAS_TYPE_NORMVALUENEEDED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_OWNED_ATTR_WILDCARD">XML_SCHEMAS_TYPE_OWNED_ATTR_WILDCARD</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_REDEFINED">XML_SCHEMAS_TYPE_REDEFINED</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_VARIETY_ABSENT">XML_SCHEMAS_TYPE_VARIETY_ABSENT</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_VARIETY_ATOMIC">XML_SCHEMAS_TYPE_VARIETY_ATOMIC</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_VARIETY_LIST">XML_SCHEMAS_TYPE_VARIETY_LIST</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_VARIETY_UNION">XML_SCHEMAS_TYPE_VARIETY_UNION</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_WHITESPACE_COLLAPSE">XML_SCHEMAS_TYPE_WHITESPACE_COLLAPSE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_WHITESPACE_PRESERVE">XML_SCHEMAS_TYPE_WHITESPACE_PRESERVE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_TYPE_WHITESPACE_REPLACE">XML_SCHEMAS_TYPE_WHITESPACE_REPLACE</a></pre><pre class="programlisting">#define <a href="#XML_SCHEMAS_WILDCARD_COMPLETE">XML_SCHEMAS_WILDCARD_COMPLETE</a></pre><pre class="programlisting">Structure <a href="#xmlSchemaAnnot">xmlSchemaAnnot</a><br />struct _xmlSchemaAnnot
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaAnnot">xmlSchemaAnnot</a> * <a name="xmlSchemaAnnotPtr" id="xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaAttribute">xmlSchemaAttribute</a><br />struct _xmlSchemaAttribute
</pre><pre class="programlisting">Structure <a href="#xmlSchemaAttributeGroup">xmlSchemaAttributeGroup</a><br />struct _xmlSchemaAttributeGroup
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaAttributeGroup">xmlSchemaAttributeGroup</a> * <a name="xmlSchemaAttributeGroupPtr" id="xmlSchemaAttributeGroupPtr">xmlSchemaAttributeGroupPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaAttributeLink">xmlSchemaAttributeLink</a><br />struct _xmlSchemaAttributeLink
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaAttributeLink">xmlSchemaAttributeLink</a> * <a name="xmlSchemaAttributeLinkPtr" id="xmlSchemaAttributeLinkPtr">xmlSchemaAttributeLinkPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaAttribute">xmlSchemaAttribute</a> * <a name="xmlSchemaAttributePtr" id="xmlSchemaAttributePtr">xmlSchemaAttributePtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlSchemaContentType">xmlSchemaContentType</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaElement">xmlSchemaElement</a><br />struct _xmlSchemaElement
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaElement">xmlSchemaElement</a> * <a name="xmlSchemaElementPtr" id="xmlSchemaElementPtr">xmlSchemaElementPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaFacet">xmlSchemaFacet</a><br />struct _xmlSchemaFacet
</pre><pre class="programlisting">Structure <a href="#xmlSchemaFacetLink">xmlSchemaFacetLink</a><br />struct _xmlSchemaFacetLink
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaFacetLink">xmlSchemaFacetLink</a> * <a name="xmlSchemaFacetLinkPtr" id="xmlSchemaFacetLinkPtr">xmlSchemaFacetLinkPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaFacet">xmlSchemaFacet</a> * <a name="xmlSchemaFacetPtr" id="xmlSchemaFacetPtr">xmlSchemaFacetPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaNotation">xmlSchemaNotation</a><br />struct _xmlSchemaNotation
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaNotation">xmlSchemaNotation</a> * <a name="xmlSchemaNotationPtr" id="xmlSchemaNotationPtr">xmlSchemaNotationPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaType">xmlSchemaType</a><br />struct _xmlSchemaType
</pre><pre class="programlisting">Structure <a href="#xmlSchemaTypeLink">xmlSchemaTypeLink</a><br />struct _xmlSchemaTypeLink
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaTypeLink">xmlSchemaTypeLink</a> * <a name="xmlSchemaTypeLinkPtr" id="xmlSchemaTypeLinkPtr">xmlSchemaTypeLinkPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaType">xmlSchemaType</a> * <a name="xmlSchemaTypePtr" id="xmlSchemaTypePtr">xmlSchemaTypePtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlSchemaTypeType">xmlSchemaTypeType</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaVal">xmlSchemaVal</a><br />struct _xmlSchemaVal
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaVal">xmlSchemaVal</a> * <a name="xmlSchemaValPtr" id="xmlSchemaValPtr">xmlSchemaValPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlSchemaValType">xmlSchemaValType</a>
</pre><pre class="programlisting">Structure <a href="#xmlSchemaWildcard">xmlSchemaWildcard</a><br />struct _xmlSchemaWildcard
</pre><pre class="programlisting">Structure <a href="#xmlSchemaWildcardNs">xmlSchemaWildcardNs</a><br />struct _xmlSchemaWildcardNs
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaWildcardNs">xmlSchemaWildcardNs</a> * <a name="xmlSchemaWildcardNsPtr" id="xmlSchemaWildcardNsPtr">xmlSchemaWildcardNsPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-schemasInternals.html#xmlSchemaWildcard">xmlSchemaWildcard</a> * <a name="xmlSchemaWildcardPtr" id="xmlSchemaWildcardPtr">xmlSchemaWildcardPtr</a>
</pre><pre class="programlisting">void	<a href="#xmlSchemaFreeType">xmlSchemaFreeType</a>		(<a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a> type)</pre>
<pre class="programlisting">void	<a href="#xmlSchemaFreeWildcard">xmlSchemaFreeWildcard</a>		(<a href="libxml-schemasInternals.html#xmlSchemaWildcardPtr">xmlSchemaWildcardPtr</a> wildcard)</pre>
<h2>Description</h2>
<h3><a name="XML_SCHEMAS_ANYATTR_LAX" id="XML_SCHEMAS_ANYATTR_LAX"></a>Macro: XML_SCHEMAS_ANYATTR_LAX</h3><pre>#define XML_SCHEMAS_ANYATTR_LAX</pre><p>Ignore validation non definition on attributes Obsolete, not used anymore.</p>
<h3><a name="XML_SCHEMAS_ANYATTR_SKIP" id="XML_SCHEMAS_ANYATTR_SKIP"></a>Macro: XML_SCHEMAS_ANYATTR_SKIP</h3><pre>#define XML_SCHEMAS_ANYATTR_SKIP</pre><p>Skip unknown <a href="libxml-SAX.html#attribute">attribute</a> from validation Obsolete, not used anymore.</p>
<h3><a name="XML_SCHEMAS_ANYATTR_STRICT" id="XML_SCHEMAS_ANYATTR_STRICT"></a>Macro: XML_SCHEMAS_ANYATTR_STRICT</h3><pre>#define XML_SCHEMAS_ANYATTR_STRICT</pre><p>Apply strict validation rules on attributes Obsolete, not used anymore.</p>
<h3><a name="XML_SCHEMAS_ANY_LAX" id="XML_SCHEMAS_ANY_LAX"></a>Macro: XML_SCHEMAS_ANY_LAX</h3><pre>#define XML_SCHEMAS_ANY_LAX</pre><p>Used by wildcards. Validate if type found, don't worry if not found</p>
<h3><a name="XML_SCHEMAS_ANY_SKIP" id="XML_SCHEMAS_ANY_SKIP"></a>Macro: XML_SCHEMAS_ANY_SKIP</h3><pre>#define XML_SCHEMAS_ANY_SKIP</pre><p>Skip unknown <a href="libxml-SAX.html#attribute">attribute</a> from validation</p>
<h3><a name="XML_SCHEMAS_ANY_STRICT" id="XML_SCHEMAS_ANY_STRICT"></a>Macro: XML_SCHEMAS_ANY_STRICT</h3><pre>#define XML_SCHEMAS_ANY_STRICT</pre><p>Used by wildcards. Apply strict validation rules</p>
<h3><a name="XML_SCHEMAS_ATTRGROUP_GLOBAL" id="XML_SCHEMAS_ATTRGROUP_GLOBAL"></a>Macro: XML_SCHEMAS_ATTRGROUP_GLOBAL</h3><pre>#define XML_SCHEMAS_ATTRGROUP_GLOBAL</pre><p>The <a href="libxml-SAX.html#attribute">attribute</a> wildcard has been already builded.</p>
<h3><a name="XML_SCHEMAS_ATTRGROUP_HAS_REFS" id="XML_SCHEMAS_ATTRGROUP_HAS_REFS"></a>Macro: XML_SCHEMAS_ATTRGROUP_HAS_REFS</h3><pre>#define XML_SCHEMAS_ATTRGROUP_HAS_REFS</pre><p>Whether this attr. group contains attr. group references.</p>
<h3><a name="XML_SCHEMAS_ATTRGROUP_MARKED" id="XML_SCHEMAS_ATTRGROUP_MARKED"></a>Macro: XML_SCHEMAS_ATTRGROUP_MARKED</h3><pre>#define XML_SCHEMAS_ATTRGROUP_MARKED</pre><p>Marks the attr group as marked; used for circular checks.</p>
<h3><a name="XML_SCHEMAS_ATTRGROUP_REDEFINED" id="XML_SCHEMAS_ATTRGROUP_REDEFINED"></a>Macro: XML_SCHEMAS_ATTRGROUP_REDEFINED</h3><pre>#define XML_SCHEMAS_ATTRGROUP_REDEFINED</pre><p>The attr group was redefined.</p>
<h3><a name="XML_SCHEMAS_ATTRGROUP_WILDCARD_BUILDED" id="XML_SCHEMAS_ATTRGROUP_WILDCARD_BUILDED"></a>Macro: XML_SCHEMAS_ATTRGROUP_WILDCARD_BUILDED</h3><pre>#define XML_SCHEMAS_ATTRGROUP_WILDCARD_BUILDED</pre><p>The <a href="libxml-SAX.html#attribute">attribute</a> wildcard has been already builded.</p>
<h3><a name="XML_SCHEMAS_ATTR_FIXED" id="XML_SCHEMAS_ATTR_FIXED"></a>Macro: XML_SCHEMAS_ATTR_FIXED</h3><pre>#define XML_SCHEMAS_ATTR_FIXED</pre><p>the <a href="libxml-SAX.html#attribute">attribute</a> has a fixed value</p>
<h3><a name="XML_SCHEMAS_ATTR_GLOBAL" id="XML_SCHEMAS_ATTR_GLOBAL"></a>Macro: XML_SCHEMAS_ATTR_GLOBAL</h3><pre>#define XML_SCHEMAS_ATTR_GLOBAL</pre><p>allow elements in no namespace</p>
<h3><a name="XML_SCHEMAS_ATTR_INTERNAL_RESOLVED" id="XML_SCHEMAS_ATTR_INTERNAL_RESOLVED"></a>Macro: XML_SCHEMAS_ATTR_INTERNAL_RESOLVED</h3><pre>#define XML_SCHEMAS_ATTR_INTERNAL_RESOLVED</pre><p>this is set when the "type" and "ref" references have been resolved.</p>
<h3><a name="XML_SCHEMAS_ATTR_NSDEFAULT" id="XML_SCHEMAS_ATTR_NSDEFAULT"></a>Macro: XML_SCHEMAS_ATTR_NSDEFAULT</h3><pre>#define XML_SCHEMAS_ATTR_NSDEFAULT</pre><p>allow elements in no namespace</p>
<h3><a name="XML_SCHEMAS_ATTR_USE_OPTIONAL" id="XML_SCHEMAS_ATTR_USE_OPTIONAL"></a>Macro: XML_SCHEMAS_ATTR_USE_OPTIONAL</h3><pre>#define XML_SCHEMAS_ATTR_USE_OPTIONAL</pre><p>The <a href="libxml-SAX.html#attribute">attribute</a> is optional.</p>
<h3><a name="XML_SCHEMAS_ATTR_USE_PROHIBITED" id="XML_SCHEMAS_ATTR_USE_PROHIBITED"></a>Macro: XML_SCHEMAS_ATTR_USE_PROHIBITED</h3><pre>#define XML_SCHEMAS_ATTR_USE_PROHIBITED</pre><p>Used by wildcards. The <a href="libxml-SAX.html#attribute">attribute</a> is prohibited.</p>
<h3><a name="XML_SCHEMAS_ATTR_USE_REQUIRED" id="XML_SCHEMAS_ATTR_USE_REQUIRED"></a>Macro: XML_SCHEMAS_ATTR_USE_REQUIRED</h3><pre>#define XML_SCHEMAS_ATTR_USE_REQUIRED</pre><p>The <a href="libxml-SAX.html#attribute">attribute</a> is required.</p>
<h3><a name="XML_SCHEMAS_BLOCK_DEFAULT_EXTENSION" id="XML_SCHEMAS_BLOCK_DEFAULT_EXTENSION"></a>Macro: XML_SCHEMAS_BLOCK_DEFAULT_EXTENSION</h3><pre>#define XML_SCHEMAS_BLOCK_DEFAULT_EXTENSION</pre><p>the schema has "extension" in the set of blockDefault.</p>
<h3><a name="XML_SCHEMAS_BLOCK_DEFAULT_RESTRICTION" id="XML_SCHEMAS_BLOCK_DEFAULT_RESTRICTION"></a>Macro: XML_SCHEMAS_BLOCK_DEFAULT_RESTRICTION</h3><pre>#define XML_SCHEMAS_BLOCK_DEFAULT_RESTRICTION</pre><p>the schema has "restriction" in the set of blockDefault.</p>
<h3><a name="XML_SCHEMAS_BLOCK_DEFAULT_SUBSTITUTION" id="XML_SCHEMAS_BLOCK_DEFAULT_SUBSTITUTION"></a>Macro: XML_SCHEMAS_BLOCK_DEFAULT_SUBSTITUTION</h3><pre>#define XML_SCHEMAS_BLOCK_DEFAULT_SUBSTITUTION</pre><p>the schema has "substitution" in the set of blockDefault.</p>
<h3><a name="XML_SCHEMAS_ELEM_ABSTRACT" id="XML_SCHEMAS_ELEM_ABSTRACT"></a>Macro: XML_SCHEMAS_ELEM_ABSTRACT</h3><pre>#define XML_SCHEMAS_ELEM_ABSTRACT</pre><p>the element is abstract</p>
<h3><a name="XML_SCHEMAS_ELEM_BLOCK_ABSENT" id="XML_SCHEMAS_ELEM_BLOCK_ABSENT"></a>Macro: XML_SCHEMAS_ELEM_BLOCK_ABSENT</h3><pre>#define XML_SCHEMAS_ELEM_BLOCK_ABSENT</pre><p>the "block" <a href="libxml-SAX.html#attribute">attribute</a> is absent</p>
<h3><a name="XML_SCHEMAS_ELEM_BLOCK_EXTENSION" id="XML_SCHEMAS_ELEM_BLOCK_EXTENSION"></a>Macro: XML_SCHEMAS_ELEM_BLOCK_EXTENSION</h3><pre>#define XML_SCHEMAS_ELEM_BLOCK_EXTENSION</pre><p>disallowed substitutions are absent</p>
<h3><a name="XML_SCHEMAS_ELEM_BLOCK_RESTRICTION" id="XML_SCHEMAS_ELEM_BLOCK_RESTRICTION"></a>Macro: XML_SCHEMAS_ELEM_BLOCK_RESTRICTION</h3><pre>#define XML_SCHEMAS_ELEM_BLOCK_RESTRICTION</pre><p>disallowed substitutions: "restriction"</p>
<h3><a name="XML_SCHEMAS_ELEM_BLOCK_SUBSTITUTION" id="XML_SCHEMAS_ELEM_BLOCK_SUBSTITUTION"></a>Macro: XML_SCHEMAS_ELEM_BLOCK_SUBSTITUTION</h3><pre>#define XML_SCHEMAS_ELEM_BLOCK_SUBSTITUTION</pre><p>disallowed substitutions: "substituion"</p>
<h3><a name="XML_SCHEMAS_ELEM_CIRCULAR" id="XML_SCHEMAS_ELEM_CIRCULAR"></a>Macro: XML_SCHEMAS_ELEM_CIRCULAR</h3><pre>#define XML_SCHEMAS_ELEM_CIRCULAR</pre><p>a helper flag for the search of circular references.</p>
<h3><a name="XML_SCHEMAS_ELEM_DEFAULT" id="XML_SCHEMAS_ELEM_DEFAULT"></a>Macro: XML_SCHEMAS_ELEM_DEFAULT</h3><pre>#define XML_SCHEMAS_ELEM_DEFAULT</pre><p>the element has a default value</p>
<h3><a name="XML_SCHEMAS_ELEM_FINAL_ABSENT" id="XML_SCHEMAS_ELEM_FINAL_ABSENT"></a>Macro: XML_SCHEMAS_ELEM_FINAL_ABSENT</h3><pre>#define XML_SCHEMAS_ELEM_FINAL_ABSENT</pre><p>substitution group exclusions are absent</p>
<h3><a name="XML_SCHEMAS_ELEM_FINAL_EXTENSION" id="XML_SCHEMAS_ELEM_FINAL_EXTENSION"></a>Macro: XML_SCHEMAS_ELEM_FINAL_EXTENSION</h3><pre>#define XML_SCHEMAS_ELEM_FINAL_EXTENSION</pre><p>substitution group exclusions: "extension"</p>
<h3><a name="XML_SCHEMAS_ELEM_FINAL_RESTRICTION" id="XML_SCHEMAS_ELEM_FINAL_RESTRICTION"></a>Macro: XML_SCHEMAS_ELEM_FINAL_RESTRICTION</h3><pre>#define XML_SCHEMAS_ELEM_FINAL_RESTRICTION</pre><p>substitution group exclusions: "restriction"</p>
<h3><a name="XML_SCHEMAS_ELEM_FIXED" id="XML_SCHEMAS_ELEM_FIXED"></a>Macro: XML_SCHEMAS_ELEM_FIXED</h3><pre>#define XML_SCHEMAS_ELEM_FIXED</pre><p>the element has a fixed value</p>
<h3><a name="XML_SCHEMAS_ELEM_GLOBAL" id="XML_SCHEMAS_ELEM_GLOBAL"></a>Macro: XML_SCHEMAS_ELEM_GLOBAL</h3><pre>#define XML_SCHEMAS_ELEM_GLOBAL</pre><p>the element is global</p>
<h3><a name="XML_SCHEMAS_ELEM_INTERNAL_CHECKED" id="XML_SCHEMAS_ELEM_INTERNAL_CHECKED"></a>Macro: XML_SCHEMAS_ELEM_INTERNAL_CHECKED</h3><pre>#define XML_SCHEMAS_ELEM_INTERNAL_CHECKED</pre><p>this is set when the elem decl has been checked against all constraints</p>
<h3><a name="XML_SCHEMAS_ELEM_INTERNAL_RESOLVED" id="XML_SCHEMAS_ELEM_INTERNAL_RESOLVED"></a>Macro: XML_SCHEMAS_ELEM_INTERNAL_RESOLVED</h3><pre>#define XML_SCHEMAS_ELEM_INTERNAL_RESOLVED</pre><p>this is set when "type", "ref", "substitutionGroup" references have been resolved.</p>
<h3><a name="XML_SCHEMAS_ELEM_NILLABLE" id="XML_SCHEMAS_ELEM_NILLABLE"></a>Macro: XML_SCHEMAS_ELEM_NILLABLE</h3><pre>#define XML_SCHEMAS_ELEM_NILLABLE</pre><p>the element is nillable</p>
<h3><a name="XML_SCHEMAS_ELEM_NSDEFAULT" id="XML_SCHEMAS_ELEM_NSDEFAULT"></a>Macro: XML_SCHEMAS_ELEM_NSDEFAULT</h3><pre>#define XML_SCHEMAS_ELEM_NSDEFAULT</pre><p>allow elements in no namespace Obsolete, not used anymore.</p>
<h3><a name="XML_SCHEMAS_ELEM_REF" id="XML_SCHEMAS_ELEM_REF"></a>Macro: XML_SCHEMAS_ELEM_REF</h3><pre>#define XML_SCHEMAS_ELEM_REF</pre><p>the element is a <a href="libxml-SAX.html#reference">reference</a> to a type</p>
<h3><a name="XML_SCHEMAS_ELEM_SUBST_GROUP_HEAD" id="XML_SCHEMAS_ELEM_SUBST_GROUP_HEAD"></a>Macro: XML_SCHEMAS_ELEM_SUBST_GROUP_HEAD</h3><pre>#define XML_SCHEMAS_ELEM_SUBST_GROUP_HEAD</pre><p>the declaration is a substitution group head</p>
<h3><a name="XML_SCHEMAS_ELEM_TOPLEVEL" id="XML_SCHEMAS_ELEM_TOPLEVEL"></a>Macro: XML_SCHEMAS_ELEM_TOPLEVEL</h3><pre>#define XML_SCHEMAS_ELEM_TOPLEVEL</pre><p>the element is top level obsolete: use <a href="libxml-schemasInternals.html#XML_SCHEMAS_ELEM_GLOBAL">XML_SCHEMAS_ELEM_GLOBAL</a> instead</p>
<h3><a name="XML_SCHEMAS_FACET_COLLAPSE" id="XML_SCHEMAS_FACET_COLLAPSE"></a>Macro: XML_SCHEMAS_FACET_COLLAPSE</h3><pre>#define XML_SCHEMAS_FACET_COLLAPSE</pre><p>collapse the types of the facet</p>
<h3><a name="XML_SCHEMAS_FACET_PRESERVE" id="XML_SCHEMAS_FACET_PRESERVE"></a>Macro: XML_SCHEMAS_FACET_PRESERVE</h3><pre>#define XML_SCHEMAS_FACET_PRESERVE</pre><p>preserve the type of the facet</p>
<h3><a name="XML_SCHEMAS_FACET_REPLACE" id="XML_SCHEMAS_FACET_REPLACE"></a>Macro: XML_SCHEMAS_FACET_REPLACE</h3><pre>#define XML_SCHEMAS_FACET_REPLACE</pre><p>replace the type of the facet</p>
<h3><a name="XML_SCHEMAS_FACET_UNKNOWN" id="XML_SCHEMAS_FACET_UNKNOWN"></a>Macro: XML_SCHEMAS_FACET_UNKNOWN</h3><pre>#define XML_SCHEMAS_FACET_UNKNOWN</pre><p>unknown facet handling</p>
<h3><a name="XML_SCHEMAS_FINAL_DEFAULT_EXTENSION" id="XML_SCHEMAS_FINAL_DEFAULT_EXTENSION"></a>Macro: XML_SCHEMAS_FINAL_DEFAULT_EXTENSION</h3><pre>#define XML_SCHEMAS_FINAL_DEFAULT_EXTENSION</pre><p>the schema has "extension" in the set of finalDefault.</p>
<h3><a name="XML_SCHEMAS_FINAL_DEFAULT_LIST" id="XML_SCHEMAS_FINAL_DEFAULT_LIST"></a>Macro: XML_SCHEMAS_FINAL_DEFAULT_LIST</h3><pre>#define XML_SCHEMAS_FINAL_DEFAULT_LIST</pre><p>the cshema has "list" in the set of finalDefault.</p>
<h3><a name="XML_SCHEMAS_FINAL_DEFAULT_RESTRICTION" id="XML_SCHEMAS_FINAL_DEFAULT_RESTRICTION"></a>Macro: XML_SCHEMAS_FINAL_DEFAULT_RESTRICTION</h3><pre>#define XML_SCHEMAS_FINAL_DEFAULT_RESTRICTION</pre><p>the schema has "restriction" in the set of finalDefault.</p>
<h3><a name="XML_SCHEMAS_FINAL_DEFAULT_UNION" id="XML_SCHEMAS_FINAL_DEFAULT_UNION"></a>Macro: XML_SCHEMAS_FINAL_DEFAULT_UNION</h3><pre>#define XML_SCHEMAS_FINAL_DEFAULT_UNION</pre><p>the schema has "union" in the set of finalDefault.</p>
<h3><a name="XML_SCHEMAS_INCLUDING_CONVERT_NS" id="XML_SCHEMAS_INCLUDING_CONVERT_NS"></a>Macro: XML_SCHEMAS_INCLUDING_CONVERT_NS</h3><pre>#define XML_SCHEMAS_INCLUDING_CONVERT_NS</pre><p>the schema is currently including an other schema with no target namespace.</p>
<h3><a name="XML_SCHEMAS_QUALIF_ATTR" id="XML_SCHEMAS_QUALIF_ATTR"></a>Macro: XML_SCHEMAS_QUALIF_ATTR</h3><pre>#define XML_SCHEMAS_QUALIF_ATTR</pre><p>Reflects attributeFormDefault == qualified in an XML schema document.</p>
<h3><a name="XML_SCHEMAS_QUALIF_ELEM" id="XML_SCHEMAS_QUALIF_ELEM"></a>Macro: XML_SCHEMAS_QUALIF_ELEM</h3><pre>#define XML_SCHEMAS_QUALIF_ELEM</pre><p>Reflects elementFormDefault == qualified in an XML schema document.</p>
<h3><a name="XML_SCHEMAS_TYPE_ABSTRACT" id="XML_SCHEMAS_TYPE_ABSTRACT"></a>Macro: XML_SCHEMAS_TYPE_ABSTRACT</h3><pre>#define XML_SCHEMAS_TYPE_ABSTRACT</pre><p>the simple/complexType is abstract.</p>
<h3><a name="XML_SCHEMAS_TYPE_BLOCK_DEFAULT" id="XML_SCHEMAS_TYPE_BLOCK_DEFAULT"></a>Macro: XML_SCHEMAS_TYPE_BLOCK_DEFAULT</h3><pre>#define XML_SCHEMAS_TYPE_BLOCK_DEFAULT</pre><p>the complexType did not specify 'block' so use the default of the &lt;schema&gt; item.</p>
<h3><a name="XML_SCHEMAS_TYPE_BLOCK_EXTENSION" id="XML_SCHEMAS_TYPE_BLOCK_EXTENSION"></a>Macro: XML_SCHEMAS_TYPE_BLOCK_EXTENSION</h3><pre>#define XML_SCHEMAS_TYPE_BLOCK_EXTENSION</pre><p>the complexType has a 'block' of "extension".</p>
<h3><a name="XML_SCHEMAS_TYPE_BLOCK_RESTRICTION" id="XML_SCHEMAS_TYPE_BLOCK_RESTRICTION"></a>Macro: XML_SCHEMAS_TYPE_BLOCK_RESTRICTION</h3><pre>#define XML_SCHEMAS_TYPE_BLOCK_RESTRICTION</pre><p>the complexType has a 'block' of "restriction".</p>
<h3><a name="XML_SCHEMAS_TYPE_BUILTIN_PRIMITIVE" id="XML_SCHEMAS_TYPE_BUILTIN_PRIMITIVE"></a>Macro: XML_SCHEMAS_TYPE_BUILTIN_PRIMITIVE</h3><pre>#define XML_SCHEMAS_TYPE_BUILTIN_PRIMITIVE</pre><p>Marks the item as a builtin primitive.</p>
<h3><a name="XML_SCHEMAS_TYPE_DERIVATION_METHOD_EXTENSION" id="XML_SCHEMAS_TYPE_DERIVATION_METHOD_EXTENSION"></a>Macro: XML_SCHEMAS_TYPE_DERIVATION_METHOD_EXTENSION</h3><pre>#define XML_SCHEMAS_TYPE_DERIVATION_METHOD_EXTENSION</pre><p>the simple or complex type has a derivation method of "extension".</p>
<h3><a name="XML_SCHEMAS_TYPE_DERIVATION_METHOD_RESTRICTION" id="XML_SCHEMAS_TYPE_DERIVATION_METHOD_RESTRICTION"></a>Macro: XML_SCHEMAS_TYPE_DERIVATION_METHOD_RESTRICTION</h3><pre>#define XML_SCHEMAS_TYPE_DERIVATION_METHOD_RESTRICTION</pre><p>the simple or complex type has a derivation method of "restriction".</p>
<h3><a name="XML_SCHEMAS_TYPE_FACETSNEEDVALUE" id="XML_SCHEMAS_TYPE_FACETSNEEDVALUE"></a>Macro: XML_SCHEMAS_TYPE_FACETSNEEDVALUE</h3><pre>#define XML_SCHEMAS_TYPE_FACETSNEEDVALUE</pre><p>indicates if the facets need a computed value</p>
<h3><a name="XML_SCHEMAS_TYPE_FINAL_DEFAULT" id="XML_SCHEMAS_TYPE_FINAL_DEFAULT"></a>Macro: XML_SCHEMAS_TYPE_FINAL_DEFAULT</h3><pre>#define XML_SCHEMAS_TYPE_FINAL_DEFAULT</pre><p>the simpleType has a final of "default".</p>
<h3><a name="XML_SCHEMAS_TYPE_FINAL_EXTENSION" id="XML_SCHEMAS_TYPE_FINAL_EXTENSION"></a>Macro: XML_SCHEMAS_TYPE_FINAL_EXTENSION</h3><pre>#define XML_SCHEMAS_TYPE_FINAL_EXTENSION</pre><p>the complexType has a final of "extension".</p>
<h3><a name="XML_SCHEMAS_TYPE_FINAL_LIST" id="XML_SCHEMAS_TYPE_FINAL_LIST"></a>Macro: XML_SCHEMAS_TYPE_FINAL_LIST</h3><pre>#define XML_SCHEMAS_TYPE_FINAL_LIST</pre><p>the simpleType has a final of "list".</p>
<h3><a name="XML_SCHEMAS_TYPE_FINAL_RESTRICTION" id="XML_SCHEMAS_TYPE_FINAL_RESTRICTION"></a>Macro: XML_SCHEMAS_TYPE_FINAL_RESTRICTION</h3><pre>#define XML_SCHEMAS_TYPE_FINAL_RESTRICTION</pre><p>the simpleType/complexType has a final of "restriction".</p>
<h3><a name="XML_SCHEMAS_TYPE_FINAL_UNION" id="XML_SCHEMAS_TYPE_FINAL_UNION"></a>Macro: XML_SCHEMAS_TYPE_FINAL_UNION</h3><pre>#define XML_SCHEMAS_TYPE_FINAL_UNION</pre><p>the simpleType has a final of "union".</p>
<h3><a name="XML_SCHEMAS_TYPE_FIXUP_1" id="XML_SCHEMAS_TYPE_FIXUP_1"></a>Macro: XML_SCHEMAS_TYPE_FIXUP_1</h3><pre>#define XML_SCHEMAS_TYPE_FIXUP_1</pre><p>First stage of fixup was done.</p>
<h3><a name="XML_SCHEMAS_TYPE_GLOBAL" id="XML_SCHEMAS_TYPE_GLOBAL"></a>Macro: XML_SCHEMAS_TYPE_GLOBAL</h3><pre>#define XML_SCHEMAS_TYPE_GLOBAL</pre><p>the type is global</p>
<h3><a name="XML_SCHEMAS_TYPE_HAS_FACETS" id="XML_SCHEMAS_TYPE_HAS_FACETS"></a>Macro: XML_SCHEMAS_TYPE_HAS_FACETS</h3><pre>#define XML_SCHEMAS_TYPE_HAS_FACETS</pre><p>has facets</p>
<h3><a name="XML_SCHEMAS_TYPE_INTERNAL_INVALID" id="XML_SCHEMAS_TYPE_INTERNAL_INVALID"></a>Macro: XML_SCHEMAS_TYPE_INTERNAL_INVALID</h3><pre>#define XML_SCHEMAS_TYPE_INTERNAL_INVALID</pre><p>indicates that the type is invalid</p>
<h3><a name="XML_SCHEMAS_TYPE_INTERNAL_RESOLVED" id="XML_SCHEMAS_TYPE_INTERNAL_RESOLVED"></a>Macro: XML_SCHEMAS_TYPE_INTERNAL_RESOLVED</h3><pre>#define XML_SCHEMAS_TYPE_INTERNAL_RESOLVED</pre><p>indicates that the type was typefixed</p>
<h3><a name="XML_SCHEMAS_TYPE_MARKED" id="XML_SCHEMAS_TYPE_MARKED"></a>Macro: XML_SCHEMAS_TYPE_MARKED</h3><pre>#define XML_SCHEMAS_TYPE_MARKED</pre><p>Marks the item as marked; used for circular checks.</p>
<h3><a name="XML_SCHEMAS_TYPE_MIXED" id="XML_SCHEMAS_TYPE_MIXED"></a>Macro: XML_SCHEMAS_TYPE_MIXED</h3><pre>#define XML_SCHEMAS_TYPE_MIXED</pre><p>the element content type is mixed</p>
<h3><a name="XML_SCHEMAS_TYPE_NORMVALUENEEDED" id="XML_SCHEMAS_TYPE_NORMVALUENEEDED"></a>Macro: XML_SCHEMAS_TYPE_NORMVALUENEEDED</h3><pre>#define XML_SCHEMAS_TYPE_NORMVALUENEEDED</pre><p>indicates if the facets (pattern) need a normalized value</p>
<h3><a name="XML_SCHEMAS_TYPE_OWNED_ATTR_WILDCARD" id="XML_SCHEMAS_TYPE_OWNED_ATTR_WILDCARD"></a>Macro: XML_SCHEMAS_TYPE_OWNED_ATTR_WILDCARD</h3><pre>#define XML_SCHEMAS_TYPE_OWNED_ATTR_WILDCARD</pre><p>the complexType owns an <a href="libxml-SAX.html#attribute">attribute</a> wildcard, i.e. it can be freed by the complexType</p>
<h3><a name="XML_SCHEMAS_TYPE_REDEFINED" id="XML_SCHEMAS_TYPE_REDEFINED"></a>Macro: XML_SCHEMAS_TYPE_REDEFINED</h3><pre>#define XML_SCHEMAS_TYPE_REDEFINED</pre><p>The type was redefined.</p>
<h3><a name="XML_SCHEMAS_TYPE_VARIETY_ABSENT" id="XML_SCHEMAS_TYPE_VARIETY_ABSENT"></a>Macro: XML_SCHEMAS_TYPE_VARIETY_ABSENT</h3><pre>#define XML_SCHEMAS_TYPE_VARIETY_ABSENT</pre><p>the simpleType has a variety of "absent". TODO: Actually not necessary :-/, since if none of the variety flags occur then it's automatically absent.</p>
<h3><a name="XML_SCHEMAS_TYPE_VARIETY_ATOMIC" id="XML_SCHEMAS_TYPE_VARIETY_ATOMIC"></a>Macro: XML_SCHEMAS_TYPE_VARIETY_ATOMIC</h3><pre>#define XML_SCHEMAS_TYPE_VARIETY_ATOMIC</pre><p>the simpleType has a variety of "union".</p>
<h3><a name="XML_SCHEMAS_TYPE_VARIETY_LIST" id="XML_SCHEMAS_TYPE_VARIETY_LIST"></a>Macro: XML_SCHEMAS_TYPE_VARIETY_LIST</h3><pre>#define XML_SCHEMAS_TYPE_VARIETY_LIST</pre><p>the simpleType has a variety of "list".</p>
<h3><a name="XML_SCHEMAS_TYPE_VARIETY_UNION" id="XML_SCHEMAS_TYPE_VARIETY_UNION"></a>Macro: XML_SCHEMAS_TYPE_VARIETY_UNION</h3><pre>#define XML_SCHEMAS_TYPE_VARIETY_UNION</pre><p>the simpleType has a variety of "union".</p>
<h3><a name="XML_SCHEMAS_TYPE_WHITESPACE_COLLAPSE" id="XML_SCHEMAS_TYPE_WHITESPACE_COLLAPSE"></a>Macro: XML_SCHEMAS_TYPE_WHITESPACE_COLLAPSE</h3><pre>#define XML_SCHEMAS_TYPE_WHITESPACE_COLLAPSE</pre><p>a whitespace-facet value of "collapse"</p>
<h3><a name="XML_SCHEMAS_TYPE_WHITESPACE_PRESERVE" id="XML_SCHEMAS_TYPE_WHITESPACE_PRESERVE"></a>Macro: XML_SCHEMAS_TYPE_WHITESPACE_PRESERVE</h3><pre>#define XML_SCHEMAS_TYPE_WHITESPACE_PRESERVE</pre><p>a whitespace-facet value of "preserve"</p>
<h3><a name="XML_SCHEMAS_TYPE_WHITESPACE_REPLACE" id="XML_SCHEMAS_TYPE_WHITESPACE_REPLACE"></a>Macro: XML_SCHEMAS_TYPE_WHITESPACE_REPLACE</h3><pre>#define XML_SCHEMAS_TYPE_WHITESPACE_REPLACE</pre><p>a whitespace-facet value of "replace"</p>
<h3><a name="XML_SCHEMAS_WILDCARD_COMPLETE" id="XML_SCHEMAS_WILDCARD_COMPLETE"></a>Macro: XML_SCHEMAS_WILDCARD_COMPLETE</h3><pre>#define XML_SCHEMAS_WILDCARD_COMPLETE</pre><p>If the wildcard is complete.</p>
<h3><a name="xmlSchemaAnnot" id="xmlSchemaAnnot">Structure xmlSchemaAnnot</a></h3><pre class="programlisting">Structure xmlSchemaAnnot<br />struct _xmlSchemaAnnot {
    struct _xmlSchemaAnnot *	next
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	content	: the annotation
}</pre><h3><a name="xmlSchemaAttribute" id="xmlSchemaAttribute">Structure xmlSchemaAttribute</a></h3><pre class="programlisting">Structure xmlSchemaAttribute<br />struct _xmlSchemaAttribute {
    <a href="libxml-schemasInternals.html#xmlSchemaTypeType">xmlSchemaTypeType</a>	type
    struct _xmlSchemaAttribute *	next	: the next <a href="libxml-SAX.html#attribute">attribute</a> (not used?)
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: the name of the declaration
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	id	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	ref	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	refNs	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	typeName	: the local name of the type definition
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	typeNs	: the ns URI of the type definition
    <a href="libxml-schemasInternals.html#xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>	annot
    <a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a>	base	: Deprecated; not used
    int	occurs	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	defValue	: The initial value of the value constrai
    <a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a>	subtypes	: the type definition
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	targetNamespace
    int	flags
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	refPrefix	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaValPtr">xmlSchemaValPtr</a>	defVal	: The compiled value constraint
    <a href="libxml-schemasInternals.html#xmlSchemaAttributePtr">xmlSchemaAttributePtr</a>	refDecl	: Deprecated; not used
}</pre><h3><a name="xmlSchemaAttributeGroup" id="xmlSchemaAttributeGroup">Structure xmlSchemaAttributeGroup</a></h3><pre class="programlisting">Structure xmlSchemaAttributeGroup<br />struct _xmlSchemaAttributeGroup {
    <a href="libxml-schemasInternals.html#xmlSchemaTypeType">xmlSchemaTypeType</a>	type	: The kind of type
    struct _xmlSchemaAttribute *	next	: the next <a href="libxml-SAX.html#attribute">attribute</a> if in a group ...
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	id
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	ref	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	refNs	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>	annot
    <a href="libxml-schemasInternals.html#xmlSchemaAttributePtr">xmlSchemaAttributePtr</a>	attributes	: Deprecated; not used
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node
    int	flags
    <a href="libxml-schemasInternals.html#xmlSchemaWildcardPtr">xmlSchemaWildcardPtr</a>	attributeWildcard
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	refPrefix	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaAttributeGroupPtr">xmlSchemaAttributeGroupPtr</a>	refItem	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	targetNamespace
    void *	attrUses
}</pre><h3><a name="xmlSchemaAttributeLink" id="xmlSchemaAttributeLink">Structure xmlSchemaAttributeLink</a></h3><pre class="programlisting">Structure xmlSchemaAttributeLink<br />struct _xmlSchemaAttributeLink {
    struct _xmlSchemaAttributeLink *	next	: the next <a href="libxml-SAX.html#attribute">attribute</a> link ...
    struct _xmlSchemaAttribute *	attr	: the linked <a href="libxml-SAX.html#attribute">attribute</a>
}</pre><h3>Enum <a name="xmlSchemaContentType" id="xmlSchemaContentType">xmlSchemaContentType</a></h3><pre class="programlisting">Enum xmlSchemaContentType {
    <a name="XML_SCHEMA_CONTENT_UNKNOWN" id="XML_SCHEMA_CONTENT_UNKNOWN">XML_SCHEMA_CONTENT_UNKNOWN</a> = 0
    <a name="XML_SCHEMA_CONTENT_EMPTY" id="XML_SCHEMA_CONTENT_EMPTY">XML_SCHEMA_CONTENT_EMPTY</a> = 1
    <a name="XML_SCHEMA_CONTENT_ELEMENTS" id="XML_SCHEMA_CONTENT_ELEMENTS">XML_SCHEMA_CONTENT_ELEMENTS</a> = 2
    <a name="XML_SCHEMA_CONTENT_MIXED" id="XML_SCHEMA_CONTENT_MIXED">XML_SCHEMA_CONTENT_MIXED</a> = 3
    <a name="XML_SCHEMA_CONTENT_SIMPLE" id="XML_SCHEMA_CONTENT_SIMPLE">XML_SCHEMA_CONTENT_SIMPLE</a> = 4
    <a name="XML_SCHEMA_CONTENT_MIXED_OR_ELEMENTS" id="XML_SCHEMA_CONTENT_MIXED_OR_ELEMENTS">XML_SCHEMA_CONTENT_MIXED_OR_ELEMENTS</a> = 5 : Obsolete
    <a name="XML_SCHEMA_CONTENT_BASIC" id="XML_SCHEMA_CONTENT_BASIC">XML_SCHEMA_CONTENT_BASIC</a> = 6
    <a name="XML_SCHEMA_CONTENT_ANY" id="XML_SCHEMA_CONTENT_ANY">XML_SCHEMA_CONTENT_ANY</a> = 7
}
</pre><h3><a name="xmlSchemaElement" id="xmlSchemaElement">Structure xmlSchemaElement</a></h3><pre class="programlisting">Structure xmlSchemaElement<br />struct _xmlSchemaElement {
    <a href="libxml-schemasInternals.html#xmlSchemaTypeType">xmlSchemaTypeType</a>	type	: The kind of type
    struct _xmlSchemaType *	next	: Not used?
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	id	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	ref	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	refNs	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>	annot
    <a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a>	subtypes	: the type definition
    <a href="libxml-schemasInternals.html#xmlSchemaAttributePtr">xmlSchemaAttributePtr</a>	attributes
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node
    int	minOccurs	: Deprecated; not used
    int	maxOccurs	: Deprecated; not used
    int	flags
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	targetNamespace
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	namedType
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	namedTypeNs
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	substGroup
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	substGroupNs
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	scope
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	value	: The original value of the value constra
    struct _xmlSchemaElement *	refDecl	: This will now be used for the substitut
    <a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	contModel	: Obsolete for WXS, maybe used for RelaxN
    <a href="libxml-schemasInternals.html#xmlSchemaContentType">xmlSchemaContentType</a>	contentType
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	refPrefix	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaValPtr">xmlSchemaValPtr</a>	defVal	: The compiled value contraint.
    void *	idcs	: The identity-constraint defs
}</pre><h3><a name="xmlSchemaFacet" id="xmlSchemaFacet">Structure xmlSchemaFacet</a></h3><pre class="programlisting">Structure xmlSchemaFacet<br />struct _xmlSchemaFacet {
    <a href="libxml-schemasInternals.html#xmlSchemaTypeType">xmlSchemaTypeType</a>	type	: The kind of type
    struct _xmlSchemaFacet *	next	: the next type if in a sequence ...
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	value	: The original value
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	id	: Obsolete
    <a href="libxml-schemasInternals.html#xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>	annot
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node
    int	fixed	: XML_SCHEMAS_FACET_PRESERVE, etc.
    int	whitespace
    <a href="libxml-schemasInternals.html#xmlSchemaValPtr">xmlSchemaValPtr</a>	val	: The compiled value
    <a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	regexp	: The regex for patterns
}</pre><h3><a name="xmlSchemaFacetLink" id="xmlSchemaFacetLink">Structure xmlSchemaFacetLink</a></h3><pre class="programlisting">Structure xmlSchemaFacetLink<br />struct _xmlSchemaFacetLink {
    struct _xmlSchemaFacetLink *	next	: the next facet link ...
    <a href="libxml-schemasInternals.html#xmlSchemaFacetPtr">xmlSchemaFacetPtr</a>	facet	: the linked facet
}</pre><h3><a name="xmlSchemaNotation" id="xmlSchemaNotation">Structure xmlSchemaNotation</a></h3><pre class="programlisting">Structure xmlSchemaNotation<br />struct _xmlSchemaNotation {
    <a href="libxml-schemasInternals.html#xmlSchemaTypeType">xmlSchemaTypeType</a>	type	: The kind of type
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name
    <a href="libxml-schemasInternals.html#xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>	annot
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	identifier
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	targetNamespace
}</pre><h3><a name="xmlSchemaType" id="xmlSchemaType">Structure xmlSchemaType</a></h3><pre class="programlisting">Structure xmlSchemaType<br />struct _xmlSchemaType {
    <a href="libxml-schemasInternals.html#xmlSchemaTypeType">xmlSchemaTypeType</a>	type	: The kind of type
    struct _xmlSchemaType *	next	: the next type if in a sequence ...
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	id	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	ref	: Deprecated; not used
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	refNs	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>	annot
    <a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a>	subtypes
    <a href="libxml-schemasInternals.html#xmlSchemaAttributePtr">xmlSchemaAttributePtr</a>	attributes	: Deprecated; not used
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node
    int	minOccurs	: Deprecated; not used
    int	maxOccurs	: Deprecated; not used
    int	flags
    <a href="libxml-schemasInternals.html#xmlSchemaContentType">xmlSchemaContentType</a>	contentType
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	base	: Base type's local name
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	baseNs	: Base type's target namespace
    <a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a>	baseType	: The base type component
    <a href="libxml-schemasInternals.html#xmlSchemaFacetPtr">xmlSchemaFacetPtr</a>	facets	: Local facets
    struct _xmlSchemaType *	redef	: Deprecated; not used
    int	recurse	: Obsolete
    <a href="libxml-schemasInternals.html#xmlSchemaAttributeLinkPtr">xmlSchemaAttributeLinkPtr</a> *	attributeUses	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaWildcardPtr">xmlSchemaWildcardPtr</a>	attributeWildcard
    int	builtInType	: Type of built-in types.
    <a href="libxml-schemasInternals.html#xmlSchemaTypeLinkPtr">xmlSchemaTypeLinkPtr</a>	memberTypes	: member-types if a union type.
    <a href="libxml-schemasInternals.html#xmlSchemaFacetLinkPtr">xmlSchemaFacetLinkPtr</a>	facetSet	: All facets (incl. inherited)
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	refPrefix	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a>	contentTypeDef	: Used for the simple content of complex
    <a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	contModel	: Holds the automaton of the content mode
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	targetNamespace
    void *	attrUses
}</pre><h3><a name="xmlSchemaTypeLink" id="xmlSchemaTypeLink">Structure xmlSchemaTypeLink</a></h3><pre class="programlisting">Structure xmlSchemaTypeLink<br />struct _xmlSchemaTypeLink {
    struct _xmlSchemaTypeLink *	next	: the next type link ...
    <a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a>	type	: the linked type
}</pre><h3>Enum <a name="xmlSchemaTypeType" id="xmlSchemaTypeType">xmlSchemaTypeType</a></h3><pre class="programlisting">Enum xmlSchemaTypeType {
    <a name="XML_SCHEMA_TYPE_BASIC" id="XML_SCHEMA_TYPE_BASIC">XML_SCHEMA_TYPE_BASIC</a> = 1 : A built-in datatype
    <a name="XML_SCHEMA_TYPE_ANY" id="XML_SCHEMA_TYPE_ANY">XML_SCHEMA_TYPE_ANY</a> = 2
    <a name="XML_SCHEMA_TYPE_FACET" id="XML_SCHEMA_TYPE_FACET">XML_SCHEMA_TYPE_FACET</a> = 3
    <a name="XML_SCHEMA_TYPE_SIMPLE" id="XML_SCHEMA_TYPE_SIMPLE">XML_SCHEMA_TYPE_SIMPLE</a> = 4
    <a name="XML_SCHEMA_TYPE_COMPLEX" id="XML_SCHEMA_TYPE_COMPLEX">XML_SCHEMA_TYPE_COMPLEX</a> = 5
    <a name="XML_SCHEMA_TYPE_SEQUENCE" id="XML_SCHEMA_TYPE_SEQUENCE">XML_SCHEMA_TYPE_SEQUENCE</a> = 6
    <a name="XML_SCHEMA_TYPE_CHOICE" id="XML_SCHEMA_TYPE_CHOICE">XML_SCHEMA_TYPE_CHOICE</a> = 7
    <a name="XML_SCHEMA_TYPE_ALL" id="XML_SCHEMA_TYPE_ALL">XML_SCHEMA_TYPE_ALL</a> = 8
    <a name="XML_SCHEMA_TYPE_SIMPLE_CONTENT" id="XML_SCHEMA_TYPE_SIMPLE_CONTENT">XML_SCHEMA_TYPE_SIMPLE_CONTENT</a> = 9
    <a name="XML_SCHEMA_TYPE_COMPLEX_CONTENT" id="XML_SCHEMA_TYPE_COMPLEX_CONTENT">XML_SCHEMA_TYPE_COMPLEX_CONTENT</a> = 10
    <a name="XML_SCHEMA_TYPE_UR" id="XML_SCHEMA_TYPE_UR">XML_SCHEMA_TYPE_UR</a> = 11
    <a name="XML_SCHEMA_TYPE_RESTRICTION" id="XML_SCHEMA_TYPE_RESTRICTION">XML_SCHEMA_TYPE_RESTRICTION</a> = 12
    <a name="XML_SCHEMA_TYPE_EXTENSION" id="XML_SCHEMA_TYPE_EXTENSION">XML_SCHEMA_TYPE_EXTENSION</a> = 13
    <a name="XML_SCHEMA_TYPE_ELEMENT" id="XML_SCHEMA_TYPE_ELEMENT">XML_SCHEMA_TYPE_ELEMENT</a> = 14
    <a name="XML_SCHEMA_TYPE_ATTRIBUTE" id="XML_SCHEMA_TYPE_ATTRIBUTE">XML_SCHEMA_TYPE_ATTRIBUTE</a> = 15
    <a name="XML_SCHEMA_TYPE_ATTRIBUTEGROUP" id="XML_SCHEMA_TYPE_ATTRIBUTEGROUP">XML_SCHEMA_TYPE_ATTRIBUTEGROUP</a> = 16
    <a name="XML_SCHEMA_TYPE_GROUP" id="XML_SCHEMA_TYPE_GROUP">XML_SCHEMA_TYPE_GROUP</a> = 17
    <a name="XML_SCHEMA_TYPE_NOTATION" id="XML_SCHEMA_TYPE_NOTATION">XML_SCHEMA_TYPE_NOTATION</a> = 18
    <a name="XML_SCHEMA_TYPE_LIST" id="XML_SCHEMA_TYPE_LIST">XML_SCHEMA_TYPE_LIST</a> = 19
    <a name="XML_SCHEMA_TYPE_UNION" id="XML_SCHEMA_TYPE_UNION">XML_SCHEMA_TYPE_UNION</a> = 20
    <a name="XML_SCHEMA_TYPE_ANY_ATTRIBUTE" id="XML_SCHEMA_TYPE_ANY_ATTRIBUTE">XML_SCHEMA_TYPE_ANY_ATTRIBUTE</a> = 21
    <a name="XML_SCHEMA_TYPE_IDC_UNIQUE" id="XML_SCHEMA_TYPE_IDC_UNIQUE">XML_SCHEMA_TYPE_IDC_UNIQUE</a> = 22
    <a name="XML_SCHEMA_TYPE_IDC_KEY" id="XML_SCHEMA_TYPE_IDC_KEY">XML_SCHEMA_TYPE_IDC_KEY</a> = 23
    <a name="XML_SCHEMA_TYPE_IDC_KEYREF" id="XML_SCHEMA_TYPE_IDC_KEYREF">XML_SCHEMA_TYPE_IDC_KEYREF</a> = 24
    <a name="XML_SCHEMA_TYPE_PARTICLE" id="XML_SCHEMA_TYPE_PARTICLE">XML_SCHEMA_TYPE_PARTICLE</a> = 25
    <a name="XML_SCHEMA_TYPE_ATTRIBUTE_USE" id="XML_SCHEMA_TYPE_ATTRIBUTE_USE">XML_SCHEMA_TYPE_ATTRIBUTE_USE</a> = 26
    <a name="XML_SCHEMA_FACET_MININCLUSIVE" id="XML_SCHEMA_FACET_MININCLUSIVE">XML_SCHEMA_FACET_MININCLUSIVE</a> = 1000
    <a name="XML_SCHEMA_FACET_MINEXCLUSIVE" id="XML_SCHEMA_FACET_MINEXCLUSIVE">XML_SCHEMA_FACET_MINEXCLUSIVE</a> = 1001
    <a name="XML_SCHEMA_FACET_MAXINCLUSIVE" id="XML_SCHEMA_FACET_MAXINCLUSIVE">XML_SCHEMA_FACET_MAXINCLUSIVE</a> = 1002
    <a name="XML_SCHEMA_FACET_MAXEXCLUSIVE" id="XML_SCHEMA_FACET_MAXEXCLUSIVE">XML_SCHEMA_FACET_MAXEXCLUSIVE</a> = 1003
    <a name="XML_SCHEMA_FACET_TOTALDIGITS" id="XML_SCHEMA_FACET_TOTALDIGITS">XML_SCHEMA_FACET_TOTALDIGITS</a> = 1004
    <a name="XML_SCHEMA_FACET_FRACTIONDIGITS" id="XML_SCHEMA_FACET_FRACTIONDIGITS">XML_SCHEMA_FACET_FRACTIONDIGITS</a> = 1005
    <a name="XML_SCHEMA_FACET_PATTERN" id="XML_SCHEMA_FACET_PATTERN">XML_SCHEMA_FACET_PATTERN</a> = 1006
    <a name="XML_SCHEMA_FACET_ENUMERATION" id="XML_SCHEMA_FACET_ENUMERATION">XML_SCHEMA_FACET_ENUMERATION</a> = 1007
    <a name="XML_SCHEMA_FACET_WHITESPACE" id="XML_SCHEMA_FACET_WHITESPACE">XML_SCHEMA_FACET_WHITESPACE</a> = 1008
    <a name="XML_SCHEMA_FACET_LENGTH" id="XML_SCHEMA_FACET_LENGTH">XML_SCHEMA_FACET_LENGTH</a> = 1009
    <a name="XML_SCHEMA_FACET_MAXLENGTH" id="XML_SCHEMA_FACET_MAXLENGTH">XML_SCHEMA_FACET_MAXLENGTH</a> = 1010
    <a name="XML_SCHEMA_FACET_MINLENGTH" id="XML_SCHEMA_FACET_MINLENGTH">XML_SCHEMA_FACET_MINLENGTH</a> = 1011
    <a name="XML_SCHEMA_EXTRA_QNAMEREF" id="XML_SCHEMA_EXTRA_QNAMEREF">XML_SCHEMA_EXTRA_QNAMEREF</a> = 2000
    <a name="XML_SCHEMA_EXTRA_ATTR_USE_PROHIB" id="XML_SCHEMA_EXTRA_ATTR_USE_PROHIB">XML_SCHEMA_EXTRA_ATTR_USE_PROHIB</a> = 2001
}
</pre><h3><a name="xmlSchemaVal" id="xmlSchemaVal">Structure xmlSchemaVal</a></h3><pre class="programlisting">Structure xmlSchemaVal<br />struct _xmlSchemaVal {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlSchemaValType" id="xmlSchemaValType">xmlSchemaValType</a></h3><pre class="programlisting">Enum xmlSchemaValType {
    <a name="XML_SCHEMAS_UNKNOWN" id="XML_SCHEMAS_UNKNOWN">XML_SCHEMAS_UNKNOWN</a> = 0
    <a name="XML_SCHEMAS_STRING" id="XML_SCHEMAS_STRING">XML_SCHEMAS_STRING</a> = 1
    <a name="XML_SCHEMAS_NORMSTRING" id="XML_SCHEMAS_NORMSTRING">XML_SCHEMAS_NORMSTRING</a> = 2
    <a name="XML_SCHEMAS_DECIMAL" id="XML_SCHEMAS_DECIMAL">XML_SCHEMAS_DECIMAL</a> = 3
    <a name="XML_SCHEMAS_TIME" id="XML_SCHEMAS_TIME">XML_SCHEMAS_TIME</a> = 4
    <a name="XML_SCHEMAS_GDAY" id="XML_SCHEMAS_GDAY">XML_SCHEMAS_GDAY</a> = 5
    <a name="XML_SCHEMAS_GMONTH" id="XML_SCHEMAS_GMONTH">XML_SCHEMAS_GMONTH</a> = 6
    <a name="XML_SCHEMAS_GMONTHDAY" id="XML_SCHEMAS_GMONTHDAY">XML_SCHEMAS_GMONTHDAY</a> = 7
    <a name="XML_SCHEMAS_GYEAR" id="XML_SCHEMAS_GYEAR">XML_SCHEMAS_GYEAR</a> = 8
    <a name="XML_SCHEMAS_GYEARMONTH" id="XML_SCHEMAS_GYEARMONTH">XML_SCHEMAS_GYEARMONTH</a> = 9
    <a name="XML_SCHEMAS_DATE" id="XML_SCHEMAS_DATE">XML_SCHEMAS_DATE</a> = 10
    <a name="XML_SCHEMAS_DATETIME" id="XML_SCHEMAS_DATETIME">XML_SCHEMAS_DATETIME</a> = 11
    <a name="XML_SCHEMAS_DURATION" id="XML_SCHEMAS_DURATION">XML_SCHEMAS_DURATION</a> = 12
    <a name="XML_SCHEMAS_FLOAT" id="XML_SCHEMAS_FLOAT">XML_SCHEMAS_FLOAT</a> = 13
    <a name="XML_SCHEMAS_DOUBLE" id="XML_SCHEMAS_DOUBLE">XML_SCHEMAS_DOUBLE</a> = 14
    <a name="XML_SCHEMAS_BOOLEAN" id="XML_SCHEMAS_BOOLEAN">XML_SCHEMAS_BOOLEAN</a> = 15
    <a name="XML_SCHEMAS_TOKEN" id="XML_SCHEMAS_TOKEN">XML_SCHEMAS_TOKEN</a> = 16
    <a name="XML_SCHEMAS_LANGUAGE" id="XML_SCHEMAS_LANGUAGE">XML_SCHEMAS_LANGUAGE</a> = 17
    <a name="XML_SCHEMAS_NMTOKEN" id="XML_SCHEMAS_NMTOKEN">XML_SCHEMAS_NMTOKEN</a> = 18
    <a name="XML_SCHEMAS_NMTOKENS" id="XML_SCHEMAS_NMTOKENS">XML_SCHEMAS_NMTOKENS</a> = 19
    <a name="XML_SCHEMAS_NAME" id="XML_SCHEMAS_NAME">XML_SCHEMAS_NAME</a> = 20
    <a name="XML_SCHEMAS_QNAME" id="XML_SCHEMAS_QNAME">XML_SCHEMAS_QNAME</a> = 21
    <a name="XML_SCHEMAS_NCNAME" id="XML_SCHEMAS_NCNAME">XML_SCHEMAS_NCNAME</a> = 22
    <a name="XML_SCHEMAS_ID" id="XML_SCHEMAS_ID">XML_SCHEMAS_ID</a> = 23
    <a name="XML_SCHEMAS_IDREF" id="XML_SCHEMAS_IDREF">XML_SCHEMAS_IDREF</a> = 24
    <a name="XML_SCHEMAS_IDREFS" id="XML_SCHEMAS_IDREFS">XML_SCHEMAS_IDREFS</a> = 25
    <a name="XML_SCHEMAS_ENTITY" id="XML_SCHEMAS_ENTITY">XML_SCHEMAS_ENTITY</a> = 26
    <a name="XML_SCHEMAS_ENTITIES" id="XML_SCHEMAS_ENTITIES">XML_SCHEMAS_ENTITIES</a> = 27
    <a name="XML_SCHEMAS_NOTATION" id="XML_SCHEMAS_NOTATION">XML_SCHEMAS_NOTATION</a> = 28
    <a name="XML_SCHEMAS_ANYURI" id="XML_SCHEMAS_ANYURI">XML_SCHEMAS_ANYURI</a> = 29
    <a name="XML_SCHEMAS_INTEGER" id="XML_SCHEMAS_INTEGER">XML_SCHEMAS_INTEGER</a> = 30
    <a name="XML_SCHEMAS_NPINTEGER" id="XML_SCHEMAS_NPINTEGER">XML_SCHEMAS_NPINTEGER</a> = 31
    <a name="XML_SCHEMAS_NINTEGER" id="XML_SCHEMAS_NINTEGER">XML_SCHEMAS_NINTEGER</a> = 32
    <a name="XML_SCHEMAS_NNINTEGER" id="XML_SCHEMAS_NNINTEGER">XML_SCHEMAS_NNINTEGER</a> = 33
    <a name="XML_SCHEMAS_PINTEGER" id="XML_SCHEMAS_PINTEGER">XML_SCHEMAS_PINTEGER</a> = 34
    <a name="XML_SCHEMAS_INT" id="XML_SCHEMAS_INT">XML_SCHEMAS_INT</a> = 35
    <a name="XML_SCHEMAS_UINT" id="XML_SCHEMAS_UINT">XML_SCHEMAS_UINT</a> = 36
    <a name="XML_SCHEMAS_LONG" id="XML_SCHEMAS_LONG">XML_SCHEMAS_LONG</a> = 37
    <a name="XML_SCHEMAS_ULONG" id="XML_SCHEMAS_ULONG">XML_SCHEMAS_ULONG</a> = 38
    <a name="XML_SCHEMAS_SHORT" id="XML_SCHEMAS_SHORT">XML_SCHEMAS_SHORT</a> = 39
    <a name="XML_SCHEMAS_USHORT" id="XML_SCHEMAS_USHORT">XML_SCHEMAS_USHORT</a> = 40
    <a name="XML_SCHEMAS_BYTE" id="XML_SCHEMAS_BYTE">XML_SCHEMAS_BYTE</a> = 41
    <a name="XML_SCHEMAS_UBYTE" id="XML_SCHEMAS_UBYTE">XML_SCHEMAS_UBYTE</a> = 42
    <a name="XML_SCHEMAS_HEXBINARY" id="XML_SCHEMAS_HEXBINARY">XML_SCHEMAS_HEXBINARY</a> = 43
    <a name="XML_SCHEMAS_BASE64BINARY" id="XML_SCHEMAS_BASE64BINARY">XML_SCHEMAS_BASE64BINARY</a> = 44
    <a name="XML_SCHEMAS_ANYTYPE" id="XML_SCHEMAS_ANYTYPE">XML_SCHEMAS_ANYTYPE</a> = 45
    <a name="XML_SCHEMAS_ANYSIMPLETYPE" id="XML_SCHEMAS_ANYSIMPLETYPE">XML_SCHEMAS_ANYSIMPLETYPE</a> = 46
}
</pre><h3><a name="xmlSchemaWildcard" id="xmlSchemaWildcard">Structure xmlSchemaWildcard</a></h3><pre class="programlisting">Structure xmlSchemaWildcard<br />struct _xmlSchemaWildcard {
    <a href="libxml-schemasInternals.html#xmlSchemaTypeType">xmlSchemaTypeType</a>	type	: The kind of type
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	id	: Deprecated; not used
    <a href="libxml-schemasInternals.html#xmlSchemaAnnotPtr">xmlSchemaAnnotPtr</a>	annot
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node
    int	minOccurs	: Deprecated; not used
    int	maxOccurs	: Deprecated; not used
    int	processContents
    int	any	: Indicates if the ns constraint is of ##
    <a href="libxml-schemasInternals.html#xmlSchemaWildcardNsPtr">xmlSchemaWildcardNsPtr</a>	nsSet	: The list of allowed namespaces
    <a href="libxml-schemasInternals.html#xmlSchemaWildcardNsPtr">xmlSchemaWildcardNsPtr</a>	negNsSet	: The negated namespace
    int	flags
}</pre><h3><a name="xmlSchemaWildcardNs" id="xmlSchemaWildcardNs">Structure xmlSchemaWildcardNs</a></h3><pre class="programlisting">Structure xmlSchemaWildcardNs<br />struct _xmlSchemaWildcardNs {
    struct _xmlSchemaWildcardNs *	next	: the next constraint link ...
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	value	: the value
}</pre><h3><a name="xmlSchemaFreeType" id="xmlSchemaFreeType"></a>Function: xmlSchemaFreeType</h3><pre class="programlisting">void	xmlSchemaFreeType		(<a href="libxml-schemasInternals.html#xmlSchemaTypePtr">xmlSchemaTypePtr</a> type)<br />
</pre><p>Deallocate a Schema Type structure.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>a schema type structure</td></tr></tbody></table></div><h3><a name="xmlSchemaFreeWildcard" id="xmlSchemaFreeWildcard"></a>Function: xmlSchemaFreeWildcard</h3><pre class="programlisting">void	xmlSchemaFreeWildcard		(<a href="libxml-schemasInternals.html#xmlSchemaWildcardPtr">xmlSchemaWildcardPtr</a> wildcard)<br />
</pre><p>Deallocates a wildcard structure.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>wildcard</tt></i>:</span></td><td>a wildcard structure</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
