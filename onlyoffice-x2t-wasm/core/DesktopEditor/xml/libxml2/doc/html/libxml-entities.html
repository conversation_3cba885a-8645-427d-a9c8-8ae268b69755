<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module entities from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module entities from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-encoding.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-encoding.html">encoding</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-globals.html">globals</a></th><td><a accesskey="n" href="libxml-globals.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>this module provides some of the entity API needed for the parser and applications. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlEntitiesTable">xmlEntitiesTable</a><br />struct _xmlHashTable
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-entities.html#xmlEntitiesTable">xmlEntitiesTable</a> * <a name="xmlEntitiesTablePtr" id="xmlEntitiesTablePtr">xmlEntitiesTablePtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlEntityType">xmlEntityType</a>
</pre><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlAddDocEntity">xmlAddDocEntity</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlAddDtdEntity">xmlAddDtdEntity</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">void	<a href="#xmlCleanupPredefinedEntities">xmlCleanupPredefinedEntities</a>	(void)</pre>
<pre class="programlisting"><a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a>	<a href="#xmlCopyEntitiesTable">xmlCopyEntitiesTable</a>	(<a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a> table)</pre>
<pre class="programlisting"><a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a>	<a href="#xmlCreateEntitiesTable">xmlCreateEntitiesTable</a>	(void)</pre>
<pre class="programlisting">void	<a href="#xmlDumpEntitiesTable">xmlDumpEntitiesTable</a>		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a> table)</pre>
<pre class="programlisting">void	<a href="#xmlDumpEntityDecl">xmlDumpEntityDecl</a>		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a> ent)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlEncodeEntities">xmlEncodeEntities</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * input)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlEncodeEntitiesReentrant">xmlEncodeEntitiesReentrant</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * input)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlEncodeSpecialChars">xmlEncodeSpecialChars</a>	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * input)</pre>
<pre class="programlisting">void	<a href="#xmlFreeEntitiesTable">xmlFreeEntitiesTable</a>		(<a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a> table)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlGetDocEntity">xmlGetDocEntity</a>		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlGetDtdEntity">xmlGetDtdEntity</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlGetParameterEntity">xmlGetParameterEntity</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlGetPredefinedEntity">xmlGetPredefinedEntity</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void	<a href="#xmlInitializePredefinedEntities">xmlInitializePredefinedEntities</a>	(void)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlNewEntity">xmlNewEntity</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<h2>Description</h2>
<h3><a name="xmlEntitiesTable" id="xmlEntitiesTable">Structure xmlEntitiesTable</a></h3><pre class="programlisting">Structure xmlEntitiesTable<br />struct _xmlHashTable {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlEntityType" id="xmlEntityType">xmlEntityType</a></h3><pre class="programlisting">Enum xmlEntityType {
    <a name="XML_INTERNAL_GENERAL_ENTITY" id="XML_INTERNAL_GENERAL_ENTITY">XML_INTERNAL_GENERAL_ENTITY</a> = 1
    <a name="XML_EXTERNAL_GENERAL_PARSED_ENTITY" id="XML_EXTERNAL_GENERAL_PARSED_ENTITY">XML_EXTERNAL_GENERAL_PARSED_ENTITY</a> = 2
    <a name="XML_EXTERNAL_GENERAL_UNPARSED_ENTITY" id="XML_EXTERNAL_GENERAL_UNPARSED_ENTITY">XML_EXTERNAL_GENERAL_UNPARSED_ENTITY</a> = 3
    <a name="XML_INTERNAL_PARAMETER_ENTITY" id="XML_INTERNAL_PARAMETER_ENTITY">XML_INTERNAL_PARAMETER_ENTITY</a> = 4
    <a name="XML_EXTERNAL_PARAMETER_ENTITY" id="XML_EXTERNAL_PARAMETER_ENTITY">XML_EXTERNAL_PARAMETER_ENTITY</a> = 5
    <a name="XML_INTERNAL_PREDEFINED_ENTITY" id="XML_INTERNAL_PREDEFINED_ENTITY">XML_INTERNAL_PREDEFINED_ENTITY</a> = 6
}
</pre><h3><a name="xmlAddDocEntity" id="xmlAddDocEntity"></a>Function: xmlAddDocEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlAddDocEntity		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Register a new entity for this document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the entity type XML_xxx_yyy_ENTITY</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the entity external ID if available</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the entity system ID if available</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the entity content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the entity or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlAddDtdEntity" id="xmlAddDtdEntity"></a>Function: xmlAddDtdEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlAddDtdEntity		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Register a new entity for this document DTD external subset.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the entity type XML_xxx_yyy_ENTITY</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the entity external ID if available</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the entity system ID if available</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the entity content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the entity or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlCleanupPredefinedEntities" id="xmlCleanupPredefinedEntities"></a>Function: xmlCleanupPredefinedEntities</h3><pre class="programlisting">void	xmlCleanupPredefinedEntities	(void)<br />
</pre><p>Cleanup up the predefined entities table. Deprecated call</p>
<h3><a name="xmlCopyEntitiesTable" id="xmlCopyEntitiesTable"></a>Function: xmlCopyEntitiesTable</h3><pre class="programlisting"><a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a>	xmlCopyEntitiesTable	(<a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a> table)<br />
</pre><p>Build a copy of an entity table.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>An entity table</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a> or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCreateEntitiesTable" id="xmlCreateEntitiesTable"></a>Function: xmlCreateEntitiesTable</h3><pre class="programlisting"><a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a>	xmlCreateEntitiesTable	(void)<br />
</pre><p>create and initialize an empty entities hash table. This really doesn't make sense and should be deprecated</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a> just created or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlDumpEntitiesTable" id="xmlDumpEntitiesTable"></a>Function: xmlDumpEntitiesTable</h3><pre class="programlisting">void	xmlDumpEntitiesTable		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a> table)<br />
</pre><p>This will dump the content of the entity table as an XML DTD definition</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>An XML buffer.</td></tr><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>An entity table</td></tr></tbody></table></div><h3><a name="xmlDumpEntityDecl" id="xmlDumpEntityDecl"></a>Function: xmlDumpEntityDecl</h3><pre class="programlisting">void	xmlDumpEntityDecl		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a> ent)<br />
</pre><p>This will dump the content of the entity table as an XML DTD definition</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>An XML buffer.</td></tr><tr><td><span class="term"><i><tt>ent</tt></i>:</span></td><td>An entity table</td></tr></tbody></table></div><h3><a name="xmlEncodeEntities" id="xmlEncodeEntities"></a>Function: xmlEncodeEntities</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlEncodeEntities	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * input)<br />
</pre><p>TODO: remove xmlEncodeEntities, once we are not afraid of breaking binary compatibility People must migrate their code to <a href="libxml-entities.html#xmlEncodeEntitiesReentrant">xmlEncodeEntitiesReentrant</a> ! This routine will issue a warning when encountered.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document containing the string</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>A string to convert to XML.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL</td></tr></tbody></table></div><h3><a name="xmlEncodeEntitiesReentrant" id="xmlEncodeEntitiesReentrant"></a>Function: xmlEncodeEntitiesReentrant</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlEncodeEntitiesReentrant	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * input)<br />
</pre><p>Do a global encoding of a string, replacing the predefined entities and non ASCII values with their entities and CharRef counterparts. Contrary to xmlEncodeEntities, this routine is reentrant, and result must be deallocated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document containing the string</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>A string to convert to XML.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>A newly allocated string with the substitution done.</td></tr></tbody></table></div><h3><a name="xmlEncodeSpecialChars" id="xmlEncodeSpecialChars"></a>Function: xmlEncodeSpecialChars</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlEncodeSpecialChars	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * input)<br />
</pre><p>Do a global encoding of a string, replacing the predefined entities this routine is reentrant, and result must be deallocated.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document containing the string</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>A string to convert to XML.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>A newly allocated string with the substitution done.</td></tr></tbody></table></div><h3><a name="xmlFreeEntitiesTable" id="xmlFreeEntitiesTable"></a>Function: xmlFreeEntitiesTable</h3><pre class="programlisting">void	xmlFreeEntitiesTable		(<a href="libxml-entities.html#xmlEntitiesTablePtr">xmlEntitiesTablePtr</a> table)<br />
</pre><p>Deallocate the memory used by an entities hash table.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>table</tt></i>:</span></td><td>An entity table</td></tr></tbody></table></div><h3><a name="xmlGetDocEntity" id="xmlGetDocEntity"></a>Function: xmlGetDocEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlGetDocEntity		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Do an entity lookup in the document entity hash table and</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document referencing the entity</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the corresponding entity, otherwise a lookup is done in the predefined entities too. Returns A pointer to the entity structure or NULL if not found.</td></tr></tbody></table></div><h3><a name="xmlGetDtdEntity" id="xmlGetDtdEntity"></a>Function: xmlGetDtdEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlGetDtdEntity		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Do an entity lookup in the DTD entity hash table and</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document referencing the entity</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the corresponding entity, if found. Note: the first argument is the document node, not the DTD node. Returns A pointer to the entity structure or NULL if not found.</td></tr></tbody></table></div><h3><a name="xmlGetParameterEntity" id="xmlGetParameterEntity"></a>Function: xmlGetParameterEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlGetParameterEntity	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Do an entity lookup in the internal and external subsets and</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document referencing the entity</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the corresponding parameter entity, if found. Returns A pointer to the entity structure or NULL if not found.</td></tr></tbody></table></div><h3><a name="xmlGetPredefinedEntity" id="xmlGetPredefinedEntity"></a>Function: xmlGetPredefinedEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlGetPredefinedEntity	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Check whether this name is an predefined entity.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL if not, otherwise the entity</td></tr></tbody></table></div><h3><a name="xmlInitializePredefinedEntities" id="xmlInitializePredefinedEntities"></a>Function: xmlInitializePredefinedEntities</h3><pre class="programlisting">void	xmlInitializePredefinedEntities	(void)<br />
</pre><p>Set up the predefined entities. Deprecated call</p>
<h3><a name="xmlNewEntity" id="xmlNewEntity"></a>Function: xmlNewEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlNewEntity		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Create a new entity, this differs from xmlAddDocEntity() that if the document is NULL or has no internal subset defined, then an unlinked entity structure will be returned, it is then the responsability of the caller to link it to the document later or free it when not needed anymore.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the entity type XML_xxx_yyy_ENTITY</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the entity external ID if available</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the entity system ID if available</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the entity content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the entity or NULL in case of error</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
