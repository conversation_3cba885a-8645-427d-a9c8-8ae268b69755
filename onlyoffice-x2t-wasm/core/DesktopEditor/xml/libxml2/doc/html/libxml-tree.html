<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module tree from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module tree from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-threads.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-threads.html">threads</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-uri.html">uri</a></th><td><a accesskey="n" href="libxml-uri.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>this module describes the structures found in an tree resulting from an XML or HTML parsing, as well as the API provided for various processing on that tree </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#BASE_BUFFER_SIZE">BASE_BUFFER_SIZE</a></pre><pre class="programlisting">#define <a href="#LIBXML2_NEW_BUFFER">LIBXML2_NEW_BUFFER</a></pre><pre class="programlisting">#define <a href="#XML_GET_CONTENT">XML_GET_CONTENT</a></pre><pre class="programlisting">#define <a href="#XML_GET_LINE">XML_GET_LINE</a></pre><pre class="programlisting">#define <a href="#XML_LOCAL_NAMESPACE">XML_LOCAL_NAMESPACE</a></pre><pre class="programlisting">#define <a href="#XML_XML_ID">XML_XML_ID</a></pre><pre class="programlisting">#define <a href="#XML_XML_NAMESPACE">XML_XML_NAMESPACE</a></pre><pre class="programlisting">#define <a href="#xmlChildrenNode">xmlChildrenNode</a></pre><pre class="programlisting">#define <a href="#xmlRootNode">xmlRootNode</a></pre><pre class="programlisting">Structure <a href="#xmlAttr">xmlAttr</a><br />struct _xmlAttr
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlAttr">xmlAttr</a> * <a name="xmlAttrPtr" id="xmlAttrPtr">xmlAttrPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlAttribute">xmlAttribute</a><br />struct _xmlAttribute
</pre><pre class="programlisting">Enum <a href="#xmlAttributeDefault">xmlAttributeDefault</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlAttribute">xmlAttribute</a> * <a name="xmlAttributePtr" id="xmlAttributePtr">xmlAttributePtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlAttributeType">xmlAttributeType</a>
</pre><pre class="programlisting">Structure <a href="#xmlBuf">xmlBuf</a><br />struct _xmlBuf
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlBuf">xmlBuf</a> * <a name="xmlBufPtr" id="xmlBufPtr">xmlBufPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlBuffer">xmlBuffer</a><br />struct _xmlBuffer
</pre><pre class="programlisting">Enum <a href="#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> * <a name="xmlBufferPtr" id="xmlBufferPtr">xmlBufferPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlDOMWrapCtxt">xmlDOMWrapCtxt</a><br />struct _xmlDOMWrapCtxt
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlDOMWrapCtxt">xmlDOMWrapCtxt</a> * <a name="xmlDOMWrapCtxtPtr" id="xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlDoc">xmlDoc</a><br />struct _xmlDoc
</pre><pre class="programlisting">Enum <a href="#xmlDocProperties">xmlDocProperties</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * <a name="xmlDocPtr" id="xmlDocPtr">xmlDocPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlDtd">xmlDtd</a><br />struct _xmlDtd
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlDtd">xmlDtd</a> * <a name="xmlDtdPtr" id="xmlDtdPtr">xmlDtdPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlElement">xmlElement</a><br />struct _xmlElement
</pre><pre class="programlisting">Structure <a href="#xmlElementContent">xmlElementContent</a><br />struct _xmlElementContent
</pre><pre class="programlisting">Enum <a href="#xmlElementContentOccur">xmlElementContentOccur</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlElementContent">xmlElementContent</a> * <a name="xmlElementContentPtr" id="xmlElementContentPtr">xmlElementContentPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlElementContentType">xmlElementContentType</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlElement">xmlElement</a> * <a name="xmlElementPtr" id="xmlElementPtr">xmlElementPtr</a>
</pre><pre class="programlisting">Enum <a href="#xmlElementType">xmlElementType</a>
</pre><pre class="programlisting">Enum <a href="#xmlElementTypeVal">xmlElementTypeVal</a>
</pre><pre class="programlisting">Structure <a href="#xmlEntity">xmlEntity</a><br />struct _xmlEntity
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlEntity">xmlEntity</a> * <a name="xmlEntityPtr" id="xmlEntityPtr">xmlEntityPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlEnumeration">xmlEnumeration</a><br />struct _xmlEnumeration
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlEnumeration">xmlEnumeration</a> * <a name="xmlEnumerationPtr" id="xmlEnumerationPtr">xmlEnumerationPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlID">xmlID</a><br />struct _xmlID
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlID">xmlID</a> * <a name="xmlIDPtr" id="xmlIDPtr">xmlIDPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlNode">xmlNode</a><br />struct _xmlNode
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlNode">xmlNode</a> * <a name="xmlNodePtr" id="xmlNodePtr">xmlNodePtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlNotation">xmlNotation</a><br />struct _xmlNotation
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlNotation">xmlNotation</a> * <a name="xmlNotationPtr" id="xmlNotationPtr">xmlNotationPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlNs">xmlNs</a><br />struct _xmlNs
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlNs">xmlNs</a> * <a name="xmlNsPtr" id="xmlNsPtr">xmlNsPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlElementType">xmlElementType</a> <a name="xmlNsType" id="xmlNsType">xmlNsType</a>
</pre><pre class="programlisting">Structure <a href="#xmlOutputBuffer">xmlOutputBuffer</a><br />struct _xmlOutputBuffer
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlOutputBuffer">xmlOutputBuffer</a> * <a name="xmlOutputBufferPtr" id="xmlOutputBufferPtr">xmlOutputBufferPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlParserCtxt">xmlParserCtxt</a><br />struct _xmlParserCtxt
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlParserCtxt">xmlParserCtxt</a> * <a name="xmlParserCtxtPtr" id="xmlParserCtxtPtr">xmlParserCtxtPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlParserInput">xmlParserInput</a><br />struct _xmlParserInput
</pre><pre class="programlisting">Structure <a href="#xmlParserInputBuffer">xmlParserInputBuffer</a><br />struct _xmlParserInputBuffer
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlParserInputBuffer">xmlParserInputBuffer</a> * <a name="xmlParserInputBufferPtr" id="xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlParserInput">xmlParserInput</a> * <a name="xmlParserInputPtr" id="xmlParserInputPtr">xmlParserInputPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlRef">xmlRef</a><br />struct _xmlRef
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlRef">xmlRef</a> * <a name="xmlRefPtr" id="xmlRefPtr">xmlRefPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSAXHandler">xmlSAXHandler</a><br />struct _xmlSAXHandler
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * <a name="xmlSAXHandlerPtr" id="xmlSAXHandlerPtr">xmlSAXHandlerPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlSAXLocator">xmlSAXLocator</a><br />struct _xmlSAXLocator
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlSAXLocator">xmlSAXLocator</a> * <a name="xmlSAXLocatorPtr" id="xmlSAXLocatorPtr">xmlSAXLocatorPtr</a>
</pre><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlAddChild">xmlAddChild</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlAddChildList">xmlAddChildList</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlAddNextSibling">xmlAddNextSibling</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlAddPrevSibling">xmlAddPrevSibling</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlAddSibling">xmlAddSibling</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem)</pre>
<pre class="programlisting">void	<a href="#xmlAttrSerializeTxtContent">xmlAttrSerializeTxtContent</a>	(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlBufContent">xmlBufContent</a>		(const <a href="libxml-tree.html#xmlBuf">xmlBuf</a> * buf)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlBufEnd">xmlBufEnd</a>		(<a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf)</pre>
<pre class="programlisting">int	<a href="#xmlBufGetNodeContent">xmlBufGetNodeContent</a>		(<a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)</pre>
<pre class="programlisting">size_t	<a href="#xmlBufNodeDump">xmlBufNodeDump</a>			(<a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 int level, <br />					 int format)</pre>
<pre class="programlisting">size_t	<a href="#xmlBufShrink">xmlBufShrink</a>			(<a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf, <br />					 size_t len)</pre>
<pre class="programlisting">size_t	<a href="#xmlBufUse">xmlBufUse</a>			(const <a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf)</pre>
<pre class="programlisting">int	<a href="#xmlBufferAdd">xmlBufferAdd</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlBufferAddHead">xmlBufferAddHead</a>		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlBufferCCat">xmlBufferCCat</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const char * str)</pre>
<pre class="programlisting">int	<a href="#xmlBufferCat">xmlBufferCat</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlBufferContent">xmlBufferContent</a>	(const <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> * buf)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a>	<a href="#xmlBufferCreate">xmlBufferCreate</a>		(void)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a>	<a href="#xmlBufferCreateSize">xmlBufferCreateSize</a>	(size_t size)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a>	<a href="#xmlBufferCreateStatic">xmlBufferCreateStatic</a>	(void * mem, <br />					 size_t size)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlBufferDetach">xmlBufferDetach</a>		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf)</pre>
<pre class="programlisting">int	<a href="#xmlBufferDump">xmlBufferDump</a>			(FILE * file, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf)</pre>
<pre class="programlisting">void	<a href="#xmlBufferEmpty">xmlBufferEmpty</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf)</pre>
<pre class="programlisting">void	<a href="#xmlBufferFree">xmlBufferFree</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf)</pre>
<pre class="programlisting">int	<a href="#xmlBufferGrow">xmlBufferGrow</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 unsigned int len)</pre>
<pre class="programlisting">int	<a href="#xmlBufferLength">xmlBufferLength</a>			(const <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> * buf)</pre>
<pre class="programlisting">int	<a href="#xmlBufferResize">xmlBufferResize</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 unsigned int size)</pre>
<pre class="programlisting">void	<a href="#xmlBufferSetAllocationScheme">xmlBufferSetAllocationScheme</a>	(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> scheme)</pre>
<pre class="programlisting">int	<a href="#xmlBufferShrink">xmlBufferShrink</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 unsigned int len)</pre>
<pre class="programlisting">void	<a href="#xmlBufferWriteCHAR">xmlBufferWriteCHAR</a>		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string)</pre>
<pre class="programlisting">void	<a href="#xmlBufferWriteChar">xmlBufferWriteChar</a>		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const char * string)</pre>
<pre class="programlisting">void	<a href="#xmlBufferWriteQuotedString">xmlBufferWriteQuotedString</a>	(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlBuildQName">xmlBuildQName</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ncname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * memory, <br />					 int len)</pre>
<pre class="programlisting">unsigned long	<a href="#xmlChildElementCount">xmlChildElementCount</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlCopyDoc">xmlCopyDoc</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int recursive)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	<a href="#xmlCopyDtd">xmlCopyDtd</a>		(<a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> dtd)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#xmlCopyNamespace">xmlCopyNamespace</a>	(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#xmlCopyNamespaceList">xmlCopyNamespaceList</a>	(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlCopyNode">xmlCopyNode</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int extended)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlCopyNodeList">xmlCopyNodeList</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlCopyProp">xmlCopyProp</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> target, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlCopyPropList">xmlCopyPropList</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> target, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	<a href="#xmlCreateIntSubset">xmlCreateIntSubset</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)</pre>
<pre class="programlisting">Function type: <a href="#xmlDOMWrapAcquireNsFunction">xmlDOMWrapAcquireNsFunction</a>
<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#xmlDOMWrapAcquireNsFunction">xmlDOMWrapAcquireNsFunction</a>	(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nsName, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nsPrefix)
</pre>
<pre class="programlisting">int	<a href="#xmlDOMWrapAdoptNode">xmlDOMWrapAdoptNode</a>		(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> sourceDoc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> destDoc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> destParent, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlDOMWrapCloneNode">xmlDOMWrapCloneNode</a>		(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> sourceDoc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * resNode, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> destDoc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> destParent, <br />					 int deep, <br />					 int options)</pre>
<pre class="programlisting">void	<a href="#xmlDOMWrapFreeCtxt">xmlDOMWrapFreeCtxt</a>		(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a>	<a href="#xmlDOMWrapNewCtxt">xmlDOMWrapNewCtxt</a>	(void)</pre>
<pre class="programlisting">int	<a href="#xmlDOMWrapReconcileNamespaces">xmlDOMWrapReconcileNamespaces</a>	(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlDOMWrapRemoveNode">xmlDOMWrapRemoveNode</a>		(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int options)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlDocCopyNode">xmlDocCopyNode</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int extended)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlDocCopyNodeList">xmlDocCopyNodeList</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">int	<a href="#xmlDocDump">xmlDocDump</a>			(FILE * f, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#xmlDocDumpFormatMemory">xmlDocDumpFormatMemory</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** mem, <br />					 int * size, <br />					 int format)</pre>
<pre class="programlisting">void	<a href="#xmlDocDumpFormatMemoryEnc">xmlDocDumpFormatMemoryEnc</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> out_doc, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** doc_txt_ptr, <br />					 int * doc_txt_len, <br />					 const char * txt_encoding, <br />					 int format)</pre>
<pre class="programlisting">void	<a href="#xmlDocDumpMemory">xmlDocDumpMemory</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** mem, <br />					 int * size)</pre>
<pre class="programlisting">void	<a href="#xmlDocDumpMemoryEnc">xmlDocDumpMemoryEnc</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> out_doc, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** doc_txt_ptr, <br />					 int * doc_txt_len, <br />					 const char * txt_encoding)</pre>
<pre class="programlisting">int	<a href="#xmlDocFormatDump">xmlDocFormatDump</a>		(FILE * f, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 int format)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlDocGetRootElement">xmlDocGetRootElement</a>	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlDocSetRootElement">xmlDocSetRootElement</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> root)</pre>
<pre class="programlisting">void	<a href="#xmlElemDump">xmlElemDump</a>			(FILE * f, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlFirstElementChild">xmlFirstElementChild</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent)</pre>
<pre class="programlisting">void	<a href="#xmlFreeDoc">xmlFreeDoc</a>			(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#xmlFreeDtd">xmlFreeDtd</a>			(<a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#xmlFreeNode">xmlFreeNode</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#xmlFreeNodeList">xmlFreeNodeList</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#xmlFreeNs">xmlFreeNs</a>			(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#xmlFreeNsList">xmlFreeNsList</a>			(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#xmlFreeProp">xmlFreeProp</a>			(<a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)</pre>
<pre class="programlisting">void	<a href="#xmlFreePropList">xmlFreePropList</a>			(<a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	<a href="#xmlGetBufferAllocationScheme">xmlGetBufferAllocationScheme</a>	(void)</pre>
<pre class="programlisting">int	<a href="#xmlGetCompressMode">xmlGetCompressMode</a>		(void)</pre>
<pre class="programlisting">int	<a href="#xmlGetDocCompressMode">xmlGetDocCompressMode</a>		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	<a href="#xmlGetIntSubset">xmlGetIntSubset</a>		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlGetLastChild">xmlGetLastChild</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * parent)</pre>
<pre class="programlisting">long	<a href="#xmlGetLineNo">xmlGetLineNo</a>			(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlGetNoNsProp">xmlGetNoNsProp</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlGetNodePath">xmlGetNodePath</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> *	<a href="#xmlGetNsList">xmlGetNsList</a>		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlGetNsProp">xmlGetNsProp</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nameSpace)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlGetProp">xmlGetProp</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlHasNsProp">xmlHasNsProp</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nameSpace)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlHasProp">xmlHasProp</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlIsBlankNode">xmlIsBlankNode</a>			(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)</pre>
<pre class="programlisting">int	<a href="#xmlIsXHTML">xmlIsXHTML</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicID)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlLastElementChild">xmlLastElementChild</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewCDataBlock">xmlNewCDataBlock</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewCharRef">xmlNewCharRef</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewChild">xmlNewChild</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewComment">xmlNewComment</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlNewDoc">xmlNewDoc</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * version)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewDocComment">xmlNewDocComment</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewDocFragment">xmlNewDocFragment</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewDocNode">xmlNewDocNode</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewDocNodeEatName">xmlNewDocNodeEatName</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewDocPI">xmlNewDocPI</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlNewDocProp">xmlNewDocProp</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewDocRawNode">xmlNewDocRawNode</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewDocText">xmlNewDocText</a>		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewDocTextLen">xmlNewDocTextLen</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	<a href="#xmlNewDtd">xmlNewDtd</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#xmlNewGlobalNs">xmlNewGlobalNs</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewNode">xmlNewNode</a>		(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewNodeEatName">xmlNewNodeEatName</a>	(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#xmlNewNs">xmlNewNs</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlNewNsProp">xmlNewNsProp</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlNewNsPropEatName">xmlNewNsPropEatName</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewPI">xmlNewPI</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlNewProp">xmlNewProp</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewReference">xmlNewReference</a>		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewText">xmlNewText</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewTextChild">xmlNewTextChild</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNewTextLen">xmlNewTextLen</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlNextElementSibling">xmlNextElementSibling</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">void	<a href="#xmlNodeAddContent">xmlNodeAddContent</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">void	<a href="#xmlNodeAddContentLen">xmlNodeAddContentLen</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlNodeBufGetContent">xmlNodeBufGetContent</a>		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buffer, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)</pre>
<pre class="programlisting">int	<a href="#xmlNodeDump">xmlNodeDump</a>			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 int level, <br />					 int format)</pre>
<pre class="programlisting">void	<a href="#xmlNodeDumpOutput">xmlNodeDumpOutput</a>		(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 int level, <br />					 int format, <br />					 const char * encoding)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNodeGetBase">xmlNodeGetBase</a>		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNodeGetContent">xmlNodeGetContent</a>	(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNodeGetLang">xmlNodeGetLang</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)</pre>
<pre class="programlisting">int	<a href="#xmlNodeGetSpacePreserve">xmlNodeGetSpacePreserve</a>		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)</pre>
<pre class="programlisting">int	<a href="#xmlNodeIsText">xmlNodeIsText</a>			(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNodeListGetRawString">xmlNodeListGetRawString</a>	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * list, <br />					 int inLine)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNodeListGetString">xmlNodeListGetString</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * list, <br />					 int inLine)</pre>
<pre class="programlisting">void	<a href="#xmlNodeSetBase">xmlNodeSetBase</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * uri)</pre>
<pre class="programlisting">void	<a href="#xmlNodeSetContent">xmlNodeSetContent</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">void	<a href="#xmlNodeSetContentLen">xmlNodeSetContentLen</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)</pre>
<pre class="programlisting">void	<a href="#xmlNodeSetLang">xmlNodeSetLang</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * lang)</pre>
<pre class="programlisting">void	<a href="#xmlNodeSetName">xmlNodeSetName</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void	<a href="#xmlNodeSetSpacePreserve">xmlNodeSetSpacePreserve</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 int val)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlPreviousElementSibling">xmlPreviousElementSibling</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)</pre>
<pre class="programlisting">int	<a href="#xmlReconciliateNs">xmlReconciliateNs</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree)</pre>
<pre class="programlisting">int	<a href="#xmlRemoveProp">xmlRemoveProp</a>			(<a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlReplaceNode">xmlReplaceNode</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> old, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting">int	<a href="#xmlSaveFile">xmlSaveFile</a>			(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)</pre>
<pre class="programlisting">int	<a href="#xmlSaveFileEnc">xmlSaveFileEnc</a>			(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding)</pre>
<pre class="programlisting">int	<a href="#xmlSaveFileTo">xmlSaveFileTo</a>			(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding)</pre>
<pre class="programlisting">int	<a href="#xmlSaveFormatFile">xmlSaveFormatFile</a>		(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 int format)</pre>
<pre class="programlisting">int	<a href="#xmlSaveFormatFileEnc">xmlSaveFormatFileEnc</a>		(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding, <br />					 int format)</pre>
<pre class="programlisting">int	<a href="#xmlSaveFormatFileTo">xmlSaveFormatFileTo</a>		(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding, <br />					 int format)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#xmlSearchNs">xmlSearchNs</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nameSpace)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#xmlSearchNsByHref">xmlSearchNsByHref</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href)</pre>
<pre class="programlisting">void	<a href="#xmlSetBufferAllocationScheme">xmlSetBufferAllocationScheme</a>	(<a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> scheme)</pre>
<pre class="programlisting">void	<a href="#xmlSetCompressMode">xmlSetCompressMode</a>		(int mode)</pre>
<pre class="programlisting">void	<a href="#xmlSetDocCompressMode">xmlSetDocCompressMode</a>		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int mode)</pre>
<pre class="programlisting">void	<a href="#xmlSetListDoc">xmlSetListDoc</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> list, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting">void	<a href="#xmlSetNs">xmlSetNs</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlSetNsProp">xmlSetNsProp</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	<a href="#xmlSetProp">xmlSetProp</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting">void	<a href="#xmlSetTreeDoc">xmlSetTreeDoc</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlSplitQName2">xmlSplitQName2</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** prefix)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlSplitQName3">xmlSplitQName3</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int * len)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlStringGetNodeList">xmlStringGetNodeList</a>	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlStringLenGetNodeList">xmlStringLenGetNodeList</a>	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int len)</pre>
<pre class="programlisting">int	<a href="#xmlTextConcat">xmlTextConcat</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlTextMerge">xmlTextMerge</a>		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> first, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> second)</pre>
<pre class="programlisting">void	<a href="#xmlUnlinkNode">xmlUnlinkNode</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)</pre>
<pre class="programlisting">int	<a href="#xmlUnsetNsProp">xmlUnsetNsProp</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlUnsetProp">xmlUnsetProp</a>			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlValidateNCName">xmlValidateNCName</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int space)</pre>
<pre class="programlisting">int	<a href="#xmlValidateNMToken">xmlValidateNMToken</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int space)</pre>
<pre class="programlisting">int	<a href="#xmlValidateName">xmlValidateName</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int space)</pre>
<pre class="programlisting">int	<a href="#xmlValidateQName">xmlValidateQName</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int space)</pre>
<h2>Description</h2>
<h3><a name="BASE_BUFFER_SIZE" id="BASE_BUFFER_SIZE"></a>Macro: BASE_BUFFER_SIZE</h3><pre>#define BASE_BUFFER_SIZE</pre><p>default buffer size 4000.</p>
<h3><a name="LIBXML2_NEW_BUFFER" id="LIBXML2_NEW_BUFFER"></a>Macro: LIBXML2_NEW_BUFFER</h3><pre>#define LIBXML2_NEW_BUFFER</pre><p>Macro used to express that the API use the new buffers for <a href="libxml-tree.html#xmlParserInputBuffer">xmlParserInputBuffer</a> and xmlOutputBuffer. The change was introduced in 2.9.0.</p>
<h3><a name="XML_GET_CONTENT" id="XML_GET_CONTENT"></a>Macro: XML_GET_CONTENT</h3><pre>#define XML_GET_CONTENT</pre><p>Macro to extract the content pointer of a node.</p>
<h3><a name="XML_GET_LINE" id="XML_GET_LINE"></a>Macro: XML_GET_LINE</h3><pre>#define XML_GET_LINE</pre><p>Macro to extract the line number of an element node.</p>
<h3><a name="XML_LOCAL_NAMESPACE" id="XML_LOCAL_NAMESPACE"></a>Macro: XML_LOCAL_NAMESPACE</h3><pre>#define XML_LOCAL_NAMESPACE</pre><p>A namespace declaration node.</p>
<h3><a name="XML_XML_ID" id="XML_XML_ID"></a>Macro: XML_XML_ID</h3><pre>#define XML_XML_ID</pre><p>This is the name for the special xml:id <a href="libxml-SAX.html#attribute">attribute</a></p>
<h3><a name="XML_XML_NAMESPACE" id="XML_XML_NAMESPACE"></a>Macro: XML_XML_NAMESPACE</h3><pre>#define XML_XML_NAMESPACE</pre><p>This is the namespace for the special xml: prefix predefined in the XML Namespace specification.</p>
<h3><a name="xmlChildrenNode" id="xmlChildrenNode"></a>Macro: xmlChildrenNode</h3><pre>#define xmlChildrenNode</pre><p>Macro for compatibility naming layer with libxml1. Maps to "children."</p>
<h3><a name="xmlRootNode" id="xmlRootNode"></a>Macro: xmlRootNode</h3><pre>#define xmlRootNode</pre><p>Macro for compatibility naming layer with libxml1. Maps to "children".</p>
<h3><a name="xmlAttr" id="xmlAttr">Structure xmlAttr</a></h3><pre class="programlisting">Structure xmlAttr<br />struct _xmlAttr {
    void *	_private	: application data
    <a href="libxml-tree.html#xmlElementType">xmlElementType</a>	type	: XML_ATTRIBUTE_NODE, must be second !
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: the name of the property
    struct _xmlNode *	children	: the value of the property
    struct _xmlNode *	last	: NULL
    struct _xmlNode *	parent	: child-&gt;parent link
    struct _xmlAttr *	next	: next sibling link
    struct _xmlAttr *	prev	: previous sibling link
    struct _xmlDoc *	doc	: the containing document
    <a href="libxml-tree.html#xmlNs">xmlNs</a> *	ns	: pointer to the associated namespace
    <a href="libxml-tree.html#xmlAttributeType">xmlAttributeType</a>	atype	: the <a href="libxml-SAX.html#attribute">attribute</a> type if validating
    void *	psvi	: for type/PSVI informations
}</pre><h3><a name="xmlAttribute" id="xmlAttribute">Structure xmlAttribute</a></h3><pre class="programlisting">Structure xmlAttribute<br />struct _xmlAttribute {
    void *	_private	: application data
    <a href="libxml-tree.html#xmlElementType">xmlElementType</a>	type	: XML_ATTRIBUTE_DECL, must be second !
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: Attribute name
    struct _xmlNode *	children	: NULL
    struct _xmlNode *	last	: NULL
    struct _xmlDtd *	parent	: -&gt; DTD
    struct _xmlNode *	next	: next sibling link
    struct _xmlNode *	prev	: previous sibling link
    struct _xmlDoc *	doc	: the containing document
    struct _xmlAttribute *	nexth	: next in hash table
    <a href="libxml-tree.html#xmlAttributeType">xmlAttributeType</a>	atype	: The <a href="libxml-SAX.html#attribute">attribute</a> type
    <a href="libxml-tree.html#xmlAttributeDefault">xmlAttributeDefault</a>	def	: the default
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	defaultValue	: or the default value
    <a href="libxml-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a>	tree	: or the enumeration tree if any
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	prefix	: the namespace prefix if any
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	elem	: Element holding the <a href="libxml-SAX.html#attribute">attribute</a>
}</pre><h3>Enum <a name="xmlAttributeDefault" id="xmlAttributeDefault">xmlAttributeDefault</a></h3><pre class="programlisting">Enum xmlAttributeDefault {
    <a name="XML_ATTRIBUTE_NONE" id="XML_ATTRIBUTE_NONE">XML_ATTRIBUTE_NONE</a> = 1
    <a name="XML_ATTRIBUTE_REQUIRED" id="XML_ATTRIBUTE_REQUIRED">XML_ATTRIBUTE_REQUIRED</a> = 2
    <a name="XML_ATTRIBUTE_IMPLIED" id="XML_ATTRIBUTE_IMPLIED">XML_ATTRIBUTE_IMPLIED</a> = 3
    <a name="XML_ATTRIBUTE_FIXED" id="XML_ATTRIBUTE_FIXED">XML_ATTRIBUTE_FIXED</a> = 4
}
</pre><h3>Enum <a name="xmlAttributeType" id="xmlAttributeType">xmlAttributeType</a></h3><pre class="programlisting">Enum xmlAttributeType {
    <a name="XML_ATTRIBUTE_CDATA" id="XML_ATTRIBUTE_CDATA">XML_ATTRIBUTE_CDATA</a> = 1
    <a name="XML_ATTRIBUTE_ID" id="XML_ATTRIBUTE_ID">XML_ATTRIBUTE_ID</a> = 2
    <a name="XML_ATTRIBUTE_IDREF" id="XML_ATTRIBUTE_IDREF">XML_ATTRIBUTE_IDREF</a> = 3
    <a name="XML_ATTRIBUTE_IDREFS" id="XML_ATTRIBUTE_IDREFS">XML_ATTRIBUTE_IDREFS</a> = 4
    <a name="XML_ATTRIBUTE_ENTITY" id="XML_ATTRIBUTE_ENTITY">XML_ATTRIBUTE_ENTITY</a> = 5
    <a name="XML_ATTRIBUTE_ENTITIES" id="XML_ATTRIBUTE_ENTITIES">XML_ATTRIBUTE_ENTITIES</a> = 6
    <a name="XML_ATTRIBUTE_NMTOKEN" id="XML_ATTRIBUTE_NMTOKEN">XML_ATTRIBUTE_NMTOKEN</a> = 7
    <a name="XML_ATTRIBUTE_NMTOKENS" id="XML_ATTRIBUTE_NMTOKENS">XML_ATTRIBUTE_NMTOKENS</a> = 8
    <a name="XML_ATTRIBUTE_ENUMERATION" id="XML_ATTRIBUTE_ENUMERATION">XML_ATTRIBUTE_ENUMERATION</a> = 9
    <a name="XML_ATTRIBUTE_NOTATION" id="XML_ATTRIBUTE_NOTATION">XML_ATTRIBUTE_NOTATION</a> = 10
}
</pre><h3><a name="xmlBuf" id="xmlBuf">Structure xmlBuf</a></h3><pre class="programlisting">Structure xmlBuf<br />struct _xmlBuf {
The content of this structure is not made public by the API.
}</pre>
      A pointer to a buffer structure, the actual structure internals are not public
    <h3><a name="xmlBuffer" id="xmlBuffer">Structure xmlBuffer</a></h3><pre class="programlisting">Structure xmlBuffer<br />struct _xmlBuffer {
    <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	content	: The buffer content UTF8
    unsigned int	use	: The buffer size used
    unsigned int	size	: The buffer size
    <a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	alloc	: The realloc method
    <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	contentIO	: in IO mode we may have a different base
}</pre><h3>Enum <a name="xmlBufferAllocationScheme" id="xmlBufferAllocationScheme">xmlBufferAllocationScheme</a></h3><pre class="programlisting">Enum xmlBufferAllocationScheme {
    <a name="XML_BUFFER_ALLOC_DOUBLEIT" id="XML_BUFFER_ALLOC_DOUBLEIT">XML_BUFFER_ALLOC_DOUBLEIT</a> = 1 : double each time one need to grow
    <a name="XML_BUFFER_ALLOC_EXACT" id="XML_BUFFER_ALLOC_EXACT">XML_BUFFER_ALLOC_EXACT</a> = 2 : grow only to the minimal size
    <a name="XML_BUFFER_ALLOC_IMMUTABLE" id="XML_BUFFER_ALLOC_IMMUTABLE">XML_BUFFER_ALLOC_IMMUTABLE</a> = 3 : immutable buffer
    <a name="XML_BUFFER_ALLOC_IO" id="XML_BUFFER_ALLOC_IO">XML_BUFFER_ALLOC_IO</a> = 4 : special allocation scheme used for I/O
    <a name="XML_BUFFER_ALLOC_HYBRID" id="XML_BUFFER_ALLOC_HYBRID">XML_BUFFER_ALLOC_HYBRID</a> = 5 : exact up to a threshold, and doubleit thereafter
    <a name="XML_BUFFER_ALLOC_BOUNDED" id="XML_BUFFER_ALLOC_BOUNDED">XML_BUFFER_ALLOC_BOUNDED</a> = 6 : limit the upper size of the buffer
}
</pre><h3><a name="xmlDOMWrapCtxt" id="xmlDOMWrapCtxt">Structure xmlDOMWrapCtxt</a></h3><pre class="programlisting">Structure xmlDOMWrapCtxt<br />struct _xmlDOMWrapCtxt {
    void *	_private	: * The type of this context, just in case
    int	type	: * Internal namespace map used for variou
    void *	namespaceMap	: * Use this one to acquire an <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> in
    <a href="libxml-tree.html#xmlDOMWrapAcquireNsFunction">xmlDOMWrapAcquireNsFunction</a>	getNsForNodeFunc
}</pre><h3><a name="xmlDoc" id="xmlDoc">Structure xmlDoc</a></h3><pre class="programlisting">Structure xmlDoc<br />struct _xmlDoc {
    void *	_private	: application data
    <a href="libxml-tree.html#xmlElementType">xmlElementType</a>	type	: XML_DOCUMENT_NODE, must be second !
    char *	name	: name/filename/URI of the document
    struct _xmlNode *	children	: the document tree
    struct _xmlNode *	last	: last child link
    struct _xmlNode *	parent	: child-&gt;parent link
    struct _xmlNode *	next	: next sibling link
    struct _xmlNode *	prev	: previous sibling link
    struct _xmlDoc *	doc	: autoreference to itself End of common p
    int	compression	: level of zlib compression
    int	standalone	: standalone document (no external refs)
    struct _xmlDtd *	intSubset	: the document internal subset
    struct _xmlDtd *	extSubset	: the document external subset
    struct _xmlNs *	oldNs	: Global namespace, the old way
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	version	: the XML version string
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	encoding	: external initial encoding, if any
    void *	ids	: Hash table for ID attributes if any
    void *	refs	: Hash table for IDREFs attributes if any
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	URL	: The URI for that document
    int	charset	: encoding of the in-memory content actua
    struct _xmlDict *	dict	: dict used to allocate names or NULL
    void *	psvi	: for type/PSVI informations
    int	parseFlags	: set of <a href="libxml-parser.html#xmlParserOption">xmlParserOption</a> used to parse th
    int	properties	: set of <a href="libxml-tree.html#xmlDocProperties">xmlDocProperties</a> for this docume
}</pre><h3>Enum <a name="xmlDocProperties" id="xmlDocProperties">xmlDocProperties</a></h3><pre class="programlisting">Enum xmlDocProperties {
    <a name="XML_DOC_WELLFORMED" id="XML_DOC_WELLFORMED">XML_DOC_WELLFORMED</a> = 1 : document is XML well formed
    <a name="XML_DOC_NSVALID" id="XML_DOC_NSVALID">XML_DOC_NSVALID</a> = 2 : document is Namespace valid
    <a name="XML_DOC_OLD10" id="XML_DOC_OLD10">XML_DOC_OLD10</a> = 4 : parsed with old XML-1.0 parser
    <a name="XML_DOC_DTDVALID" id="XML_DOC_DTDVALID">XML_DOC_DTDVALID</a> = 8 : DTD validation was successful
    <a name="XML_DOC_XINCLUDE" id="XML_DOC_XINCLUDE">XML_DOC_XINCLUDE</a> = 16 : XInclude substitution was done
    <a name="XML_DOC_USERBUILT" id="XML_DOC_USERBUILT">XML_DOC_USERBUILT</a> = 32 : Document was built using the API and not by parsing an instance
    <a name="XML_DOC_INTERNAL" id="XML_DOC_INTERNAL">XML_DOC_INTERNAL</a> = 64 : built for internal processing
    <a name="XML_DOC_HTML" id="XML_DOC_HTML">XML_DOC_HTML</a> = 128 : parsed or built HTML document
}
</pre><h3><a name="xmlDtd" id="xmlDtd">Structure xmlDtd</a></h3><pre class="programlisting">Structure xmlDtd<br />struct _xmlDtd {
    void *	_private	: application data
    <a href="libxml-tree.html#xmlElementType">xmlElementType</a>	type	: XML_DTD_NODE, must be second !
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: Name of the DTD
    struct _xmlNode *	children	: the value of the property link
    struct _xmlNode *	last	: last child link
    struct _xmlDoc *	parent	: child-&gt;parent link
    struct _xmlNode *	next	: next sibling link
    struct _xmlNode *	prev	: previous sibling link
    struct _xmlDoc *	doc	: the containing document End of common p
    void *	notations	: Hash table for notations if any
    void *	elements	: Hash table for elements if any
    void *	attributes	: Hash table for attributes if any
    void *	entities	: Hash table for entities if any
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	ExternalID	: External identifier for PUBLIC DTD
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	SystemID	: URI for a SYSTEM or PUBLIC DTD
    void *	pentities	: Hash table for param entities if any
}</pre><h3><a name="xmlElement" id="xmlElement">Structure xmlElement</a></h3><pre class="programlisting">Structure xmlElement<br />struct _xmlElement {
    void *	_private	: application data
    <a href="libxml-tree.html#xmlElementType">xmlElementType</a>	type	: XML_ELEMENT_DECL, must be second !
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: Element name
    struct _xmlNode *	children	: NULL
    struct _xmlNode *	last	: NULL
    struct _xmlDtd *	parent	: -&gt; DTD
    struct _xmlNode *	next	: next sibling link
    struct _xmlNode *	prev	: previous sibling link
    struct _xmlDoc *	doc	: the containing document
    <a href="libxml-tree.html#xmlElementTypeVal">xmlElementTypeVal</a>	etype	: The type
    <a href="libxml-tree.html#xmlElementContentPtr">xmlElementContentPtr</a>	content	: the allowed element content
    <a href="libxml-tree.html#xmlAttributePtr">xmlAttributePtr</a>	attributes	: List of the declared attributes
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	prefix	: the namespace prefix if any
    <a href="libxml-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	contModel	: the validating regexp
    void *	contModel
}</pre><h3><a name="xmlElementContent" id="xmlElementContent">Structure xmlElementContent</a></h3><pre class="programlisting">Structure xmlElementContent<br />struct _xmlElementContent {
    <a href="libxml-tree.html#xmlElementContentType">xmlElementContentType</a>	type	: PCDATA, ELEMENT, SEQ or OR
    <a href="libxml-tree.html#xmlElementContentOccur">xmlElementContentOccur</a>	ocur	: ONCE, OPT, MULT or PLUS
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: Element name
    struct _xmlElementContent *	c1	: first child
    struct _xmlElementContent *	c2	: second child
    struct _xmlElementContent *	parent	: parent
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	prefix	: Namespace prefix
}</pre><h3>Enum <a name="xmlElementContentOccur" id="xmlElementContentOccur">xmlElementContentOccur</a></h3><pre class="programlisting">Enum xmlElementContentOccur {
    <a name="XML_ELEMENT_CONTENT_ONCE" id="XML_ELEMENT_CONTENT_ONCE">XML_ELEMENT_CONTENT_ONCE</a> = 1
    <a name="XML_ELEMENT_CONTENT_OPT" id="XML_ELEMENT_CONTENT_OPT">XML_ELEMENT_CONTENT_OPT</a> = 2
    <a name="XML_ELEMENT_CONTENT_MULT" id="XML_ELEMENT_CONTENT_MULT">XML_ELEMENT_CONTENT_MULT</a> = 3
    <a name="XML_ELEMENT_CONTENT_PLUS" id="XML_ELEMENT_CONTENT_PLUS">XML_ELEMENT_CONTENT_PLUS</a> = 4
}
</pre><h3>Enum <a name="xmlElementContentType" id="xmlElementContentType">xmlElementContentType</a></h3><pre class="programlisting">Enum xmlElementContentType {
    <a name="XML_ELEMENT_CONTENT_PCDATA" id="XML_ELEMENT_CONTENT_PCDATA">XML_ELEMENT_CONTENT_PCDATA</a> = 1
    <a name="XML_ELEMENT_CONTENT_ELEMENT" id="XML_ELEMENT_CONTENT_ELEMENT">XML_ELEMENT_CONTENT_ELEMENT</a> = 2
    <a name="XML_ELEMENT_CONTENT_SEQ" id="XML_ELEMENT_CONTENT_SEQ">XML_ELEMENT_CONTENT_SEQ</a> = 3
    <a name="XML_ELEMENT_CONTENT_OR" id="XML_ELEMENT_CONTENT_OR">XML_ELEMENT_CONTENT_OR</a> = 4
}
</pre><h3>Enum <a name="xmlElementType" id="xmlElementType">xmlElementType</a></h3><pre class="programlisting">Enum xmlElementType {
    <a name="XML_ELEMENT_NODE" id="XML_ELEMENT_NODE">XML_ELEMENT_NODE</a> = 1
    <a name="XML_ATTRIBUTE_NODE" id="XML_ATTRIBUTE_NODE">XML_ATTRIBUTE_NODE</a> = 2
    <a name="XML_TEXT_NODE" id="XML_TEXT_NODE">XML_TEXT_NODE</a> = 3
    <a name="XML_CDATA_SECTION_NODE" id="XML_CDATA_SECTION_NODE">XML_CDATA_SECTION_NODE</a> = 4
    <a name="XML_ENTITY_REF_NODE" id="XML_ENTITY_REF_NODE">XML_ENTITY_REF_NODE</a> = 5
    <a name="XML_ENTITY_NODE" id="XML_ENTITY_NODE">XML_ENTITY_NODE</a> = 6
    <a name="XML_PI_NODE" id="XML_PI_NODE">XML_PI_NODE</a> = 7
    <a name="XML_COMMENT_NODE" id="XML_COMMENT_NODE">XML_COMMENT_NODE</a> = 8
    <a name="XML_DOCUMENT_NODE" id="XML_DOCUMENT_NODE">XML_DOCUMENT_NODE</a> = 9
    <a name="XML_DOCUMENT_TYPE_NODE" id="XML_DOCUMENT_TYPE_NODE">XML_DOCUMENT_TYPE_NODE</a> = 10
    <a name="XML_DOCUMENT_FRAG_NODE" id="XML_DOCUMENT_FRAG_NODE">XML_DOCUMENT_FRAG_NODE</a> = 11
    <a name="XML_NOTATION_NODE" id="XML_NOTATION_NODE">XML_NOTATION_NODE</a> = 12
    <a name="XML_HTML_DOCUMENT_NODE" id="XML_HTML_DOCUMENT_NODE">XML_HTML_DOCUMENT_NODE</a> = 13
    <a name="XML_DTD_NODE" id="XML_DTD_NODE">XML_DTD_NODE</a> = 14
    <a name="XML_ELEMENT_DECL" id="XML_ELEMENT_DECL">XML_ELEMENT_DECL</a> = 15
    <a name="XML_ATTRIBUTE_DECL" id="XML_ATTRIBUTE_DECL">XML_ATTRIBUTE_DECL</a> = 16
    <a name="XML_ENTITY_DECL" id="XML_ENTITY_DECL">XML_ENTITY_DECL</a> = 17
    <a name="XML_NAMESPACE_DECL" id="XML_NAMESPACE_DECL">XML_NAMESPACE_DECL</a> = 18
    <a name="XML_XINCLUDE_START" id="XML_XINCLUDE_START">XML_XINCLUDE_START</a> = 19
    <a name="XML_XINCLUDE_END" id="XML_XINCLUDE_END">XML_XINCLUDE_END</a> = 20
    <a name="XML_DOCB_DOCUMENT_NODE" id="XML_DOCB_DOCUMENT_NODE">XML_DOCB_DOCUMENT_NODE</a> = 21
}
</pre><h3>Enum <a name="xmlElementTypeVal" id="xmlElementTypeVal">xmlElementTypeVal</a></h3><pre class="programlisting">Enum xmlElementTypeVal {
    <a name="XML_ELEMENT_TYPE_UNDEFINED" id="XML_ELEMENT_TYPE_UNDEFINED">XML_ELEMENT_TYPE_UNDEFINED</a> = 0
    <a name="XML_ELEMENT_TYPE_EMPTY" id="XML_ELEMENT_TYPE_EMPTY">XML_ELEMENT_TYPE_EMPTY</a> = 1
    <a name="XML_ELEMENT_TYPE_ANY" id="XML_ELEMENT_TYPE_ANY">XML_ELEMENT_TYPE_ANY</a> = 2
    <a name="XML_ELEMENT_TYPE_MIXED" id="XML_ELEMENT_TYPE_MIXED">XML_ELEMENT_TYPE_MIXED</a> = 3
    <a name="XML_ELEMENT_TYPE_ELEMENT" id="XML_ELEMENT_TYPE_ELEMENT">XML_ELEMENT_TYPE_ELEMENT</a> = 4
}
</pre><h3><a name="xmlEntity" id="xmlEntity">Structure xmlEntity</a></h3><pre class="programlisting">Structure xmlEntity<br />struct _xmlEntity {
    void *	_private	: application data
    <a href="libxml-tree.html#xmlElementType">xmlElementType</a>	type	: XML_ENTITY_DECL, must be second !
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: Entity name
    struct _xmlNode *	children	: First child link
    struct _xmlNode *	last	: Last child link
    struct _xmlDtd *	parent	: -&gt; DTD
    struct _xmlNode *	next	: next sibling link
    struct _xmlNode *	prev	: previous sibling link
    struct _xmlDoc *	doc	: the containing document
    <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	orig	: content without ref substitution
    <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	content	: content or ndata if unparsed
    int	length	: the content length
    <a href="libxml-entities.html#xmlEntityType">xmlEntityType</a>	etype	: The entity type
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	ExternalID	: External identifier for PUBLIC
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	SystemID	: URI for a SYSTEM or PUBLIC Entity
    struct _xmlEntity *	nexte	: unused
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	URI	: the full URI as computed
    int	owner	: does the entity own the childrens
    int	checked	: was the entity content checked this is
}</pre><h3><a name="xmlEnumeration" id="xmlEnumeration">Structure xmlEnumeration</a></h3><pre class="programlisting">Structure xmlEnumeration<br />struct _xmlEnumeration {
    struct _xmlEnumeration *	next	: next one
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: Enumeration name
}</pre><h3><a name="xmlID" id="xmlID">Structure xmlID</a></h3><pre class="programlisting">Structure xmlID<br />struct _xmlID {
    struct _xmlID *	next	: next ID
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	value	: The ID name
    <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	attr	: The <a href="libxml-SAX.html#attribute">attribute</a> holding it
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: The <a href="libxml-SAX.html#attribute">attribute</a> if attr is not available
    int	lineno	: The line number if attr is not availabl
    struct _xmlDoc *	doc	: The document holding the ID
}</pre><h3><a name="xmlNode" id="xmlNode">Structure xmlNode</a></h3><pre class="programlisting">Structure xmlNode<br />struct _xmlNode {
    void *	_private	: application data
    <a href="libxml-tree.html#xmlElementType">xmlElementType</a>	type	: type number, must be second !
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: the name of the node, or the entity
    struct _xmlNode *	children	: parent-&gt;childs link
    struct _xmlNode *	last	: last child link
    struct _xmlNode *	parent	: child-&gt;parent link
    struct _xmlNode *	next	: next sibling link
    struct _xmlNode *	prev	: previous sibling link
    struct _xmlDoc *	doc	: the containing document End of common p
    <a href="libxml-tree.html#xmlNs">xmlNs</a> *	ns	: pointer to the associated namespace
    <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	content	: the content
    struct _xmlAttr *	properties	: properties list
    <a href="libxml-tree.html#xmlNs">xmlNs</a> *	nsDef	: namespace definitions on this node
    void *	psvi	: for type/PSVI informations
    unsigned short	line	: line number
    unsigned short	extra	: extra data for XPath/XSLT
}</pre><h3><a name="xmlNotation" id="xmlNotation">Structure xmlNotation</a></h3><pre class="programlisting">Structure xmlNotation<br />struct _xmlNotation {
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: Notation name
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	PublicID	: Public identifier, if any
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	SystemID	: System identifier, if any
}</pre><h3><a name="xmlNs" id="xmlNs">Structure xmlNs</a></h3><pre class="programlisting">Structure xmlNs<br />struct _xmlNs {
    struct _xmlNs *	next	: next Ns link for this node
    <a href="libxml-tree.html#xmlNsType">xmlNsType</a>	type	: global or local
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	href	: URL for the namespace
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	prefix	: prefix for the namespace
    void *	_private	: application data
    struct _xmlDoc *	context	: normally an <a href="libxml-tree.html#xmlDoc">xmlDoc</a>
}</pre><h3><a name="xmlOutputBuffer" id="xmlOutputBuffer">Structure xmlOutputBuffer</a></h3><pre class="programlisting">Structure xmlOutputBuffer<br />struct _xmlOutputBuffer {
    void *	context
    <a href="libxml-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a>	writecallback
    <a href="libxml-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a>	closecallback
    <a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	encoder	: I18N conversions to UTF-8
    <a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a>	buffer	: Local buffer encoded in UTF-8 or ISOLat
    <a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a>	conv	: if encoder != NULL buffer for output
    int	written	: total number of byte written
    int	error
}</pre><h3><a name="xmlParserCtxt" id="xmlParserCtxt">Structure xmlParserCtxt</a></h3><pre class="programlisting">Structure xmlParserCtxt<br />struct _xmlParserCtxt {
    struct _xmlSAXHandler *	sax	: The SAX handler
    void *	userData	: For SAX interface only, used by DOM bui
    <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	myDoc	: the document being built
    int	wellFormed	: is the document well formed
    int	replaceEntities	: shall we replace entities ?
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	version	: the XML version string
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	encoding	: the declared encoding, if any
    int	standalone	: standalone document
    int	html	: an HTML(1)/Docbook(2) document * 3 is H
    <a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	input	: Current input stream
    int	inputNr	: Number of current input streams
    int	inputMax	: Max number of input streams
    <a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> *	inputTab	: stack of inputs Node analysis stack onl
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	node	: Current parsed Node
    int	nodeNr	: Depth of the parsing stack
    int	nodeMax	: Max depth of the parsing stack
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> *	nodeTab	: array of nodes
    int	record_info	: Whether node info should be kept
    <a href="libxml-parser.html#xmlParserNodeInfoSeq">xmlParserNodeInfoSeq</a>	node_seq	: info about each node parsed
    int	errNo	: error code
    int	hasExternalSubset	: <a href="libxml-SAX.html#reference">reference</a> and external subset
    int	hasPErefs	: the internal subset has PE refs
    int	external	: are we parsing an external entity
    int	valid	: is the document valid
    int	validate	: shall we try to validate ?
    <a href="libxml-valid.html#xmlValidCtxt">xmlValidCtxt</a>	vctxt	: The validity context
    <a href="libxml-parser.html#xmlParserInputState">xmlParserInputState</a>	instate	: current type of input
    int	token	: next char look-ahead
    char *	directory	: the data directory Node name stack
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: Current parsed Node
    int	nameNr	: Depth of the parsing stack
    int	nameMax	: Max depth of the parsing stack
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * *	nameTab	: array of nodes
    long	nbChars	: number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> processed
    long	checkIndex	: used by progressive parsing lookup
    int	keepBlanks	: ugly but ...
    int	disableSAX	: SAX callbacks are disabled
    int	inSubset	: Parsing is in int 1/ext 2 subset
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	intSubName	: name of subset
    <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	extSubURI	: URI of external subset
    <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	extSubSystem	: SYSTEM ID of external subset xml:space
    int *	space	: Should the parser preserve spaces
    int	spaceNr	: Depth of the parsing stack
    int	spaceMax	: Max depth of the parsing stack
    int *	spaceTab	: array of space infos
    int	depth	: to prevent entity substitution loops
    <a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	entity	: used to check entities boundaries
    int	charset	: encoding of the in-memory content actua
    int	nodelen	: Those two fields are there to
    int	nodemem	: Speed up large node parsing
    int	pedantic	: signal pedantic warnings
    void *	_private	: For user data, libxml won't touch it
    int	loadsubset	: should the external subset be loaded
    int	linenumbers	: set line number in element content
    void *	catalogs	: document's own catalog
    int	recovery	: run in recovery mode
    int	progressive	: is this a progressive parsing
    <a href="libxml-dict.html#xmlDictPtr">xmlDictPtr</a>	dict	: dictionary for the parser
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * *	atts	: array for the attributes callbacks
    int	maxatts	: the size of the array
    int	docdict	: * pre-interned strings *
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	str_xml
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	str_xmlns
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	str_xml_ns	: * Everything below is used only by the n
    int	sax2	: operating in the new SAX mode
    int	nsNr	: the number of inherited namespaces
    int	nsMax	: the size of the arrays
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * *	nsTab	: the array of prefix/namespace name
    int *	attallocs	: which <a href="libxml-SAX.html#attribute">attribute</a> were allocated
    void * *	pushTab	: array of data for push
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	attsDefault	: defaulted attributes if any
    <a href="libxml-hash.html#xmlHashTablePtr">xmlHashTablePtr</a>	attsSpecial	: non-CDATA attributes if any
    int	nsWellFormed	: is the document XML Nanespace okay
    int	options	: * Those fields are needed only for tream
    int	dictNames	: Use dictionary names for the tree
    int	freeElemsNr	: number of freed element nodes
    <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	freeElems	: List of freed element nodes
    int	freeAttrsNr	: number of freed attributes nodes
    <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	freeAttrs	: * the complete error informations for th
    <a href="libxml-xmlerror.html#xmlError">xmlError</a>	lastError
    <a href="libxml-parser.html#xmlParserMode">xmlParserMode</a>	parseMode	: the parser mode
    unsigned long	nbentities	: number of entities references
    unsigned long	sizeentities	: size of parsed entities for use by HTML
    <a href="libxml-parser.html#xmlParserNodeInfo">xmlParserNodeInfo</a> *	nodeInfo	: Current NodeInfo
    int	nodeInfoNr	: Depth of the parsing stack
    int	nodeInfoMax	: Max depth of the parsing stack
    <a href="libxml-parser.html#xmlParserNodeInfo">xmlParserNodeInfo</a> *	nodeInfoTab	: array of nodeInfos
    int	input_id	: we need to label inputs
    unsigned long	sizeentcopy	: volume of entity copy
}</pre><h3><a name="xmlParserInput" id="xmlParserInput">Structure xmlParserInput</a></h3><pre class="programlisting">Structure xmlParserInput<br />struct _xmlParserInput {
    <a href="libxml-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	buf	: UTF-8 encoded buffer
    const char *	filename	: The file analyzed, if any
    const char *	directory	: the directory/base of the file
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	base	: Base of the array to parse
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	cur	: Current char being parsed
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	end	: end of the array to parse
    int	length	: length if known
    int	line	: Current line
    int	col	: * NOTE: consumed is only tested for equa
    unsigned long	consumed	: How many xmlChars already consumed
    <a href="libxml-parser.html#xmlParserInputDeallocate">xmlParserInputDeallocate</a>	free	: function to deallocate the base
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	encoding	: the encoding string for entity
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	version	: the version string for entity
    int	standalone	: Was that entity marked standalone
    int	id	: an unique identifier for the entity
}</pre><h3><a name="xmlParserInputBuffer" id="xmlParserInputBuffer">Structure xmlParserInputBuffer</a></h3><pre class="programlisting">Structure xmlParserInputBuffer<br />struct _xmlParserInputBuffer {
    void *	context
    <a href="libxml-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a>	readcallback
    <a href="libxml-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a>	closecallback
    <a href="libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	encoder	: I18N conversions to UTF-8
    <a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a>	buffer	: Local buffer encoded in UTF-8
    <a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a>	raw	: if encoder != NULL buffer for raw input
    int	compressed	: -1=unknown, 0=not compressed, 1=compres
    int	error
    unsigned long	rawconsumed	: amount consumed from raw
}</pre><h3><a name="xmlRef" id="xmlRef">Structure xmlRef</a></h3><pre class="programlisting">Structure xmlRef<br />struct _xmlRef {
    struct _xmlRef *	next	: next Ref
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	value	: The Ref name
    <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	attr	: The <a href="libxml-SAX.html#attribute">attribute</a> holding it
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	name	: The <a href="libxml-SAX.html#attribute">attribute</a> if attr is not available
    int	lineno	: The line number if attr is not availabl
}</pre><h3><a name="xmlSAXHandler" id="xmlSAXHandler">Structure xmlSAXHandler</a></h3><pre class="programlisting">Structure xmlSAXHandler<br />struct _xmlSAXHandler {
    <a href="libxml-parser.html#internalSubsetSAXFunc">internalSubsetSAXFunc</a>	internalSubset
    <a href="libxml-parser.html#isStandaloneSAXFunc">isStandaloneSAXFunc</a>	isStandalone
    <a href="libxml-parser.html#hasInternalSubsetSAXFunc">hasInternalSubsetSAXFunc</a>	hasInternalSubset
    <a href="libxml-parser.html#hasExternalSubsetSAXFunc">hasExternalSubsetSAXFunc</a>	hasExternalSubset
    <a href="libxml-parser.html#resolveEntitySAXFunc">resolveEntitySAXFunc</a>	resolveEntity
    <a href="libxml-parser.html#getEntitySAXFunc">getEntitySAXFunc</a>	getEntity
    <a href="libxml-parser.html#entityDeclSAXFunc">entityDeclSAXFunc</a>	entityDecl
    <a href="libxml-parser.html#notationDeclSAXFunc">notationDeclSAXFunc</a>	notationDecl
    <a href="libxml-parser.html#attributeDeclSAXFunc">attributeDeclSAXFunc</a>	attributeDecl
    <a href="libxml-parser.html#elementDeclSAXFunc">elementDeclSAXFunc</a>	elementDecl
    <a href="libxml-parser.html#unparsedEntityDeclSAXFunc">unparsedEntityDeclSAXFunc</a>	unparsedEntityDecl
    <a href="libxml-parser.html#setDocumentLocatorSAXFunc">setDocumentLocatorSAXFunc</a>	setDocumentLocator
    <a href="libxml-parser.html#startDocumentSAXFunc">startDocumentSAXFunc</a>	startDocument
    <a href="libxml-parser.html#endDocumentSAXFunc">endDocumentSAXFunc</a>	endDocument
    <a href="libxml-parser.html#startElementSAXFunc">startElementSAXFunc</a>	startElement
    <a href="libxml-parser.html#endElementSAXFunc">endElementSAXFunc</a>	endElement
    <a href="libxml-parser.html#referenceSAXFunc">referenceSAXFunc</a>	reference
    <a href="libxml-parser.html#charactersSAXFunc">charactersSAXFunc</a>	characters
    <a href="libxml-parser.html#ignorableWhitespaceSAXFunc">ignorableWhitespaceSAXFunc</a>	ignorableWhitespace
    <a href="libxml-parser.html#processingInstructionSAXFunc">processingInstructionSAXFunc</a>	processingInstruction
    <a href="libxml-parser.html#commentSAXFunc">commentSAXFunc</a>	comment
    <a href="libxml-parser.html#warningSAXFunc">warningSAXFunc</a>	warning
    <a href="libxml-parser.html#errorSAXFunc">errorSAXFunc</a>	error
    <a href="libxml-parser.html#fatalErrorSAXFunc">fatalErrorSAXFunc</a>	fatalError	: unused error() get all the errors
    <a href="libxml-parser.html#getParameterEntitySAXFunc">getParameterEntitySAXFunc</a>	getParameterEntity
    <a href="libxml-parser.html#cdataBlockSAXFunc">cdataBlockSAXFunc</a>	cdataBlock
    <a href="libxml-parser.html#externalSubsetSAXFunc">externalSubsetSAXFunc</a>	externalSubset
    unsigned int	initialized	: The following fields are extensions ava
    void *	_private
    <a href="libxml-parser.html#startElementNsSAX2Func">startElementNsSAX2Func</a>	startElementNs
    <a href="libxml-parser.html#endElementNsSAX2Func">endElementNsSAX2Func</a>	endElementNs
    <a href="libxml-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a>	serror
}</pre><h3><a name="xmlSAXLocator" id="xmlSAXLocator">Structure xmlSAXLocator</a></h3><pre class="programlisting">Structure xmlSAXLocator<br />struct _xmlSAXLocator {
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *(*getPublicId)	getPublicId
    const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *(*getSystemId)	getSystemId
    int(*getLineNumber)	getLineNumber
    int(*getColumnNumber)	getColumnNumber
}</pre><h3><a name="xmlAddChild" id="xmlAddChild"></a>Function: xmlAddChild</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlAddChild		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Add a new node to @parent, at the end of the child (or property) list merging adjacent TEXT nodes (in which case @cur is freed) If the new node is ATTRIBUTE, it is added into properties instead of children. If there is an <a href="libxml-SAX.html#attribute">attribute</a> with equal name, it is first destroyed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the child node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the child or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlAddChildList" id="xmlAddChildList"></a>Function: xmlAddChildList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlAddChildList		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Add a list of node at the end of the child list of the parent merging adjacent TEXT nodes (@cur may be freed)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the first node in the list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the last child or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlAddNextSibling" id="xmlAddNextSibling"></a>Function: xmlAddNextSibling</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlAddNextSibling	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem)<br />
</pre><p>Add a new node @elem as the next sibling of @cur If the new node was already inserted in a document it is first unlinked from its existing context. As a result of text merging @elem may be freed. If the new node is ATTRIBUTE, it is added into properties instead of children. If there is an <a href="libxml-SAX.html#attribute">attribute</a> with equal name, it is first destroyed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the child node</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>the new node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new node or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlAddPrevSibling" id="xmlAddPrevSibling"></a>Function: xmlAddPrevSibling</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlAddPrevSibling	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem)<br />
</pre><p>Add a new node @elem as the previous sibling of @cur merging adjacent TEXT nodes (@elem may be freed) If the new node was already inserted in a document it is first unlinked from its existing context. If the new node is ATTRIBUTE, it is added into properties instead of children. If there is an <a href="libxml-SAX.html#attribute">attribute</a> with equal name, it is first destroyed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the child node</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>the new node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new node or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlAddSibling" id="xmlAddSibling"></a>Function: xmlAddSibling</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlAddSibling		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem)<br />
</pre><p>Add a new element @elem to the list of siblings of @cur merging adjacent TEXT nodes (@elem may be freed) If the new element was already inserted in a document it is first unlinked from its existing context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the child node</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>the new node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new element or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlAttrSerializeTxtContent" id="xmlAttrSerializeTxtContent"></a>Function: xmlAttrSerializeTxtContent</h3><pre class="programlisting">void	xmlAttrSerializeTxtContent	(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string)<br />
</pre><p>Serialize text <a href="libxml-SAX.html#attribute">attribute</a> values to an xml simple buffer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the XML buffer output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>attr</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> node</td></tr><tr><td><span class="term"><i><tt>string</tt></i>:</span></td><td>the text content</td></tr></tbody></table></div><h3><a name="xmlBufContent" id="xmlBufContent"></a>Function: xmlBufContent</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlBufContent		(const <a href="libxml-tree.html#xmlBuf">xmlBuf</a> * buf)<br />
</pre><p>Function to extract the content of a buffer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the internal content</td></tr></tbody></table></div><h3><a name="xmlBufEnd" id="xmlBufEnd"></a>Function: xmlBufEnd</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlBufEnd		(<a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf)<br />
</pre><p>Function to extract the end of the content of a buffer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the end of the internal content or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlBufGetNodeContent" id="xmlBufGetNodeContent"></a>Function: xmlBufGetNodeContent</h3><pre class="programlisting">int	xmlBufGetNodeContent		(<a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)<br />
</pre><p>Read the value of a node @cur, this can be either the text carried directly by this node if it's a TEXT node or the aggregate string of the values carried by this node child's (TEXT and ENTITY_REF). Entity references are substituted. Fills up the buffer @buf with this value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>a buffer <a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a></td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being read</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlBufNodeDump" id="xmlBufNodeDump"></a>Function: xmlBufNodeDump</h3><pre class="programlisting">size_t	xmlBufNodeDump			(<a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 int level, <br />					 int format)<br />
</pre><p>Dump an XML node, recursive behaviour,children are printed too. Note that @format = 1 provide node indenting only if <a href="libxml-globals.html#xmlIndentTreeOutput">xmlIndentTreeOutput</a> = 1 or xmlKeepBlanksDefault(0) was called</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the XML buffer output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>level</tt></i>:</span></td><td>the imbrication level for indenting</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>is formatting allowed</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written to the buffer, in case of error 0 is returned or @buf stores the error</td></tr></tbody></table></div><h3><a name="xmlBufShrink" id="xmlBufShrink"></a>Function: xmlBufShrink</h3><pre class="programlisting">size_t	xmlBufShrink			(<a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf, <br />					 size_t len)<br />
</pre><p>Remove the beginning of an XML buffer. NOTE that this routine behaviour differs from xmlBufferShrink() as it will return 0 on error instead of -1 due to size_t being used as the return type.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to dump</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> to remove</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte removed or 0 in case of failure</td></tr></tbody></table></div><h3><a name="xmlBufUse" id="xmlBufUse"></a>Function: xmlBufUse</h3><pre class="programlisting">size_t	xmlBufUse			(const <a href="libxml-tree.html#xmlBufPtr">xmlBufPtr</a> buf)<br />
</pre><p>Function to get the length of a buffer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the length of data in the internal content</td></tr></tbody></table></div><h3><a name="xmlBufferAdd" id="xmlBufferAdd"></a>Function: xmlBufferAdd</h3><pre class="programlisting">int	xmlBufferAdd			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 int len)<br />
</pre><p>Add a string range to an XML buffer. if len == -1, the length of str is recomputed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to dump</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the #xmlChar string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of #xmlChar to add</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 successful, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlBufferAddHead" id="xmlBufferAddHead"></a>Function: xmlBufferAddHead</h3><pre class="programlisting">int	xmlBufferAddHead		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 int len)<br />
</pre><p>Add a string range to the beginning of an XML buffer. if len == -1, the length of @str is recomputed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the #xmlChar string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of #xmlChar to add</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 successful, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlBufferCCat" id="xmlBufferCCat"></a>Function: xmlBufferCCat</h3><pre class="programlisting">int	xmlBufferCCat			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const char * str)<br />
</pre><p>Append a zero terminated C string to an XML buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to dump</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the C char string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 successful, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlBufferCat" id="xmlBufferCat"></a>Function: xmlBufferCat</h3><pre class="programlisting">int	xmlBufferCat			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str)<br />
</pre><p>Append a zero terminated string to an XML buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to add to</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the #xmlChar string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 successful, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlBufferContent" id="xmlBufferContent"></a>Function: xmlBufferContent</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlBufferContent	(const <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> * buf)<br />
</pre><p>Function to extract the content of a buffer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the internal content</td></tr></tbody></table></div><h3><a name="xmlBufferCreate" id="xmlBufferCreate"></a>Function: xmlBufferCreate</h3><pre class="programlisting"><a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a>	xmlBufferCreate		(void)<br />
</pre><p>routine to create an XML buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new structure.</td></tr></tbody></table></div><h3><a name="xmlBufferCreateSize" id="xmlBufferCreateSize"></a>Function: xmlBufferCreateSize</h3><pre class="programlisting"><a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a>	xmlBufferCreateSize	(size_t size)<br />
</pre><p>routine to create an XML buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>initial size of buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new structure.</td></tr></tbody></table></div><h3><a name="xmlBufferCreateStatic" id="xmlBufferCreateStatic"></a>Function: xmlBufferCreateStatic</h3><pre class="programlisting"><a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a>	xmlBufferCreateStatic	(void * mem, <br />					 size_t size)<br />
</pre><p>routine to create an XML buffer from an immutable memory area. The area won't be modified nor copied, and is expected to be present until the end of the buffer lifetime.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>the memory area</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size in byte</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new structure.</td></tr></tbody></table></div><h3><a name="xmlBufferDetach" id="xmlBufferDetach"></a>Function: xmlBufferDetach</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlBufferDetach		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf)<br />
</pre><p>Remove the string contained in a buffer and gie it back to the caller. The buffer is reset to an empty content. This doesn't work with immutable buffers as they can't be reset.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the previous string contained by the buffer.</td></tr></tbody></table></div><h3><a name="xmlBufferDump" id="xmlBufferDump"></a>Function: xmlBufferDump</h3><pre class="programlisting">int	xmlBufferDump			(FILE * file, <br />					 <a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf)<br />
</pre><p>Dumps an XML buffer to a FILE *.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>the file output</td></tr><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to dump</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of #xmlChar written</td></tr></tbody></table></div><h3><a name="xmlBufferEmpty" id="xmlBufferEmpty"></a>Function: xmlBufferEmpty</h3><pre class="programlisting">void	xmlBufferEmpty			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf)<br />
</pre><p>empty a buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr></tbody></table></div><h3><a name="xmlBufferFree" id="xmlBufferFree"></a>Function: xmlBufferFree</h3><pre class="programlisting">void	xmlBufferFree			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf)<br />
</pre><p>Frees an XML buffer. It frees both the content and the structure which encapsulate it.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to free</td></tr></tbody></table></div><h3><a name="xmlBufferGrow" id="xmlBufferGrow"></a>Function: xmlBufferGrow</h3><pre class="programlisting">int	xmlBufferGrow			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 unsigned int len)<br />
</pre><p>Grow the available space of an XML buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the minimum free size to allocate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new available space or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlBufferLength" id="xmlBufferLength"></a>Function: xmlBufferLength</h3><pre class="programlisting">int	xmlBufferLength			(const <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> * buf)<br />
</pre><p>Function to get the length of a buffer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the length of data in the internal content</td></tr></tbody></table></div><h3><a name="xmlBufferResize" id="xmlBufferResize"></a>Function: xmlBufferResize</h3><pre class="programlisting">int	xmlBufferResize			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 unsigned int size)<br />
</pre><p>Resize a buffer to accommodate minimum size of @size.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to resize</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the desired size</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of problems, 1 otherwise</td></tr></tbody></table></div><h3><a name="xmlBufferSetAllocationScheme" id="xmlBufferSetAllocationScheme"></a>Function: xmlBufferSetAllocationScheme</h3><pre class="programlisting">void	xmlBufferSetAllocationScheme	(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> scheme)<br />
</pre><p>Sets the allocation scheme for this buffer</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to tune</td></tr><tr><td><span class="term"><i><tt>scheme</tt></i>:</span></td><td>allocation scheme to use</td></tr></tbody></table></div><h3><a name="xmlBufferShrink" id="xmlBufferShrink"></a>Function: xmlBufferShrink</h3><pre class="programlisting">int	xmlBufferShrink			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 unsigned int len)<br />
</pre><p>Remove the beginning of an XML buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the buffer to dump</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> to remove</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of #xmlChar removed, or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlBufferWriteCHAR" id="xmlBufferWriteCHAR"></a>Function: xmlBufferWriteCHAR</h3><pre class="programlisting">void	xmlBufferWriteCHAR		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string)<br />
</pre><p>routine which manages and grows an output buffer. This one adds xmlChars at the end of the buffer.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the XML buffer</td></tr><tr><td><span class="term"><i><tt>string</tt></i>:</span></td><td>the string to add</td></tr></tbody></table></div><h3><a name="xmlBufferWriteChar" id="xmlBufferWriteChar"></a>Function: xmlBufferWriteChar</h3><pre class="programlisting">void	xmlBufferWriteChar		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const char * string)<br />
</pre><p>routine which manage and grows an output buffer. This one add C chars at the end of the array.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the XML buffer output</td></tr><tr><td><span class="term"><i><tt>string</tt></i>:</span></td><td>the string to add</td></tr></tbody></table></div><h3><a name="xmlBufferWriteQuotedString" id="xmlBufferWriteQuotedString"></a>Function: xmlBufferWriteQuotedString</h3><pre class="programlisting">void	xmlBufferWriteQuotedString	(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * string)<br />
</pre><p>routine which manage and grows an output buffer. This one writes a quoted or double quoted #xmlChar string, checking first if it holds quote or double-quotes internally</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the XML buffer output</td></tr><tr><td><span class="term"><i><tt>string</tt></i>:</span></td><td>the string to add</td></tr></tbody></table></div><h3><a name="xmlBuildQName" id="xmlBuildQName"></a>Function: xmlBuildQName</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlBuildQName		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ncname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * memory, <br />					 int len)<br />
</pre><p>Builds the QName @prefix:@ncname in @memory if there is enough space and prefix is not NULL nor empty, otherwise allocate a new string. If prefix is NULL or empty it returns ncname.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ncname</tt></i>:</span></td><td>the Name</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the prefix</td></tr><tr><td><span class="term"><i><tt>memory</tt></i>:</span></td><td>preallocated memory</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>preallocated memory length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new string which must be freed by the caller if different from @memory and @ncname or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlChildElementCount" id="xmlChildElementCount"></a>Function: xmlChildElementCount</h3><pre class="programlisting">unsigned long	xmlChildElementCount	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent)<br />
</pre><p>Finds the current number of child nodes of that element which are element nodes. Note the handling of entities references is different than in the W3C DOM element traversal spec since we don't have back <a href="libxml-SAX.html#reference">reference</a> from entities content to entities references.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the count of element child or 0 if not available</td></tr></tbody></table></div><h3><a name="xmlCopyDoc" id="xmlCopyDoc"></a>Function: xmlCopyDoc</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlCopyDoc		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int recursive)<br />
</pre><p>Do a copy of the document info. If recursive, the content tree will be copied too as well as DTD, namespaces and entities.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>recursive</tt></i>:</span></td><td>if not zero do a recursive copy.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlDocPtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCopyDtd" id="xmlCopyDtd"></a>Function: xmlCopyDtd</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	xmlCopyDtd		(<a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> dtd)<br />
</pre><p>Do a copy of the dtd.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>dtd</tt></i>:</span></td><td>the dtd</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlDtdPtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCopyNamespace" id="xmlCopyNamespace"></a>Function: xmlCopyNamespace</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	xmlCopyNamespace	(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> cur)<br />
</pre><p>Do a copy of the namespace.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the namespace</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlNsPtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCopyNamespaceList" id="xmlCopyNamespaceList"></a>Function: xmlCopyNamespaceList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	xmlCopyNamespaceList	(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> cur)<br />
</pre><p>Do a copy of an namespace list.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the first namespace</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlNsPtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCopyNode" id="xmlCopyNode"></a>Function: xmlCopyNode</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlCopyNode		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int extended)<br />
</pre><p>Do a copy of the node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>extended</tt></i>:</span></td><td>if 1 do a recursive copy (properties, namespaces and children when applicable) if 2 copy properties and namespaces (when applicable)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlNodePtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCopyNodeList" id="xmlCopyNodeList"></a>Function: xmlCopyNodeList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlCopyNodeList		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Do a recursive copy of the node list. Use xmlDocCopyNodeList() if possible to ensure string interning.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the first node in the list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlNodePtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCopyProp" id="xmlCopyProp"></a>Function: xmlCopyProp</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlCopyProp		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> target, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)<br />
</pre><p>Do a copy of the attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>the element where the <a href="libxml-SAX.html#attribute">attribute</a> will be grafted</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlAttrPtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCopyPropList" id="xmlCopyPropList"></a>Function: xmlCopyPropList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlCopyPropList		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> target, <br />					 <a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)<br />
</pre><p>Do a copy of an <a href="libxml-SAX.html#attribute">attribute</a> list.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>the element where the attributes will be grafted</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the first <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlAttrPtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlCreateIntSubset" id="xmlCreateIntSubset"></a>Function: xmlCreateIntSubset</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	xmlCreateIntSubset	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br />
</pre><p>Create the internal subset of a document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document pointer</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the DTD name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external (PUBLIC) ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the system ID</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new DTD structure</td></tr></tbody></table></div><h3><a name="xmlDOMWrapAcquireNsFunction" id="xmlDOMWrapAcquireNsFunction"></a>Function type: xmlDOMWrapAcquireNsFunction</h3><pre class="programlisting">Function type: xmlDOMWrapAcquireNsFunction
<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	xmlDOMWrapAcquireNsFunction	(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nsName, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nsPrefix)
</pre><p>A function called to acquire namespaces (xmlNs) from the wrapper.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a DOM wrapper context</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the context node (element or attribute)</td></tr><tr><td><span class="term"><i><tt>nsName</tt></i>:</span></td><td>the requested namespace name</td></tr><tr><td><span class="term"><i><tt>nsPrefix</tt></i>:</span></td><td>the requested namespace prefix</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> or NULL in case of an error.</td></tr></tbody></table></div><br />
<h3><a name="xmlDOMWrapAdoptNode" id="xmlDOMWrapAdoptNode"></a>Function: xmlDOMWrapAdoptNode</h3><pre class="programlisting">int	xmlDOMWrapAdoptNode		(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> sourceDoc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> destDoc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> destParent, <br />					 int options)<br />
</pre><p>References of out-of scope ns-decls are remapped to point to @destDoc: 1) If @destParent is given, then nsDef entries on element-nodes are used 2) If *no* @destParent is given, then @destDoc-&gt;oldNs entries are used This is the case when you have an unlinked node and just want to move it to the context of If @destParent is given, it ensures that the tree is namespace wellformed by creating additional ns-decls where needed. Note that, since prefixes of already existent ns-decls can be shadowed by this process, it could break QNames in <a href="libxml-SAX.html#attribute">attribute</a> values or element content. NOTE: This function was not intensively tested.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the optional context for custom processing</td></tr><tr><td><span class="term"><i><tt>sourceDoc</tt></i>:</span></td><td>the optional sourceDoc</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to start with</td></tr><tr><td><span class="term"><i><tt>destDoc</tt></i>:</span></td><td>the destination doc</td></tr><tr><td><span class="term"><i><tt>destParent</tt></i>:</span></td><td>the optional new parent of @node in @destDoc</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>option flags</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the operation succeeded, 1 if a node of unsupported type was given, 2 if a node of not yet supported type was given and -1 on API/internal errors.</td></tr></tbody></table></div><h3><a name="xmlDOMWrapCloneNode" id="xmlDOMWrapCloneNode"></a>Function: xmlDOMWrapCloneNode</h3><pre class="programlisting">int	xmlDOMWrapCloneNode		(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> sourceDoc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> * resNode, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> destDoc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> destParent, <br />					 int deep, <br />					 int options)<br />
</pre><p>References of out-of scope ns-decls are remapped to point to @destDoc: 1) If @destParent is given, then nsDef entries on element-nodes are used 2) If *no* @destParent is given, then @destDoc-&gt;oldNs entries are used. This is the case when you don't know already where the cloned branch will be added to. If @destParent is given, it ensures that the tree is namespace wellformed by creating additional ns-decls where needed. Note that, since prefixes of already existent ns-decls can be shadowed by this process, it could break QNames in <a href="libxml-SAX.html#attribute">attribute</a> values or element content. TODO: 1) What to do with XInclude? Currently this returns an error for XInclude.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the optional context for custom processing</td></tr><tr><td><span class="term"><i><tt>sourceDoc</tt></i>:</span></td><td>the optional sourceDoc</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to start with</td></tr><tr><td><span class="term"><i><tt>resNode</tt></i>:</span></td><td>the clone of the given @node</td></tr><tr><td><span class="term"><i><tt>destDoc</tt></i>:</span></td><td>the destination doc</td></tr><tr><td><span class="term"><i><tt>destParent</tt></i>:</span></td><td>the optional new parent of @node in @destDoc</td></tr><tr><td><span class="term"><i><tt>deep</tt></i>:</span></td><td>descend into child if set</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>option flags</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the operation succeeded, 1 if a node of unsupported (or not yet supported) type was given, -1 on API/internal errors.</td></tr></tbody></table></div><h3><a name="xmlDOMWrapFreeCtxt" id="xmlDOMWrapFreeCtxt"></a>Function: xmlDOMWrapFreeCtxt</h3><pre class="programlisting">void	xmlDOMWrapFreeCtxt		(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt)<br />
</pre><p>Frees the DOM-wrapper context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the DOM-wrapper context</td></tr></tbody></table></div><h3><a name="xmlDOMWrapNewCtxt" id="xmlDOMWrapNewCtxt"></a>Function: xmlDOMWrapNewCtxt</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a>	xmlDOMWrapNewCtxt	(void)<br />
</pre><p>Allocates and initializes a new DOM-wrapper context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> or NULL in case of an internal error.</td></tr></tbody></table></div><h3><a name="xmlDOMWrapReconcileNamespaces" id="xmlDOMWrapReconcileNamespaces"></a>Function: xmlDOMWrapReconcileNamespaces</h3><pre class="programlisting">int	xmlDOMWrapReconcileNamespaces	(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> elem, <br />					 int options)<br />
</pre><p>Ensures that ns-references point to ns-decls hold on element-nodes. Ensures that the tree is namespace wellformed by creating additional ns-decls where needed. Note that, since prefixes of already existent ns-decls can be shadowed by this process, it could break QNames in <a href="libxml-SAX.html#attribute">attribute</a> values or element content. NOTE: This function was not intensively tested.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>DOM wrapper context, unused at the moment</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>the element-node</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>option flags</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if succeeded, -1 otherwise and on API/internal errors.</td></tr></tbody></table></div><h3><a name="xmlDOMWrapRemoveNode" id="xmlDOMWrapRemoveNode"></a>Function: xmlDOMWrapRemoveNode</h3><pre class="programlisting">int	xmlDOMWrapRemoveNode		(<a href="libxml-tree.html#xmlDOMWrapCtxtPtr">xmlDOMWrapCtxtPtr</a> ctxt, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 int options)<br />
</pre><p>Unlinks the given node from its owner. This will substitute ns-references to node-&gt;nsDef for ns-references to doc-&gt;oldNs, thus ensuring the removed branch to be autark wrt ns-references. NOTE: This function was not intensively tested.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a DOM wrapper context</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the doc</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to be removed.</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>set of options, unused at the moment</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 on success, 1 if the node is not supported, -1 on API and internal errors.</td></tr></tbody></table></div><h3><a name="xmlDocCopyNode" id="xmlDocCopyNode"></a>Function: xmlDocCopyNode</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlDocCopyNode		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int extended)<br />
</pre><p>Do a copy of the node to a given document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>extended</tt></i>:</span></td><td>if 1 do a recursive copy (properties, namespaces and children when applicable) if 2 copy properties and namespaces (when applicable)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlNodePtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlDocCopyNodeList" id="xmlDocCopyNodeList"></a>Function: xmlDocCopyNodeList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlDocCopyNodeList	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Do a recursive copy of the node list.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the target document</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the first node in the list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlNodePtr, or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlDocDump" id="xmlDocDump"></a>Function: xmlDocDump</h3><pre class="programlisting">int	xmlDocDump			(FILE * f, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)<br />
</pre><p>Dump an XML document to an open FILE.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the FILE*</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlDocDumpFormatMemory" id="xmlDocDumpFormatMemory"></a>Function: xmlDocDumpFormatMemory</h3><pre class="programlisting">void	xmlDocDumpFormatMemory		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** mem, <br />					 int * size, <br />					 int format)<br />
</pre><p>Dump an XML document in memory and return the #xmlChar * and it's size. It's up to the caller to free the memory with xmlFree(). Note that @format = 1 provide node indenting only if <a href="libxml-globals.html#xmlIndentTreeOutput">xmlIndentTreeOutput</a> = 1 or xmlKeepBlanksDefault(0) was called</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>OUT: the memory pointer</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>OUT: the memory length</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr></tbody></table></div><h3><a name="xmlDocDumpFormatMemoryEnc" id="xmlDocDumpFormatMemoryEnc"></a>Function: xmlDocDumpFormatMemoryEnc</h3><pre class="programlisting">void	xmlDocDumpFormatMemoryEnc	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> out_doc, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** doc_txt_ptr, <br />					 int * doc_txt_len, <br />					 const char * txt_encoding, <br />					 int format)<br />
</pre><p>Dump the current DOM tree into memory using the character encoding specified by the caller. Note it is up to the caller of this function to free the allocated memory with xmlFree(). Note that @format = 1 provide node indenting only if <a href="libxml-globals.html#xmlIndentTreeOutput">xmlIndentTreeOutput</a> = 1 or xmlKeepBlanksDefault(0) was called</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out_doc</tt></i>:</span></td><td>Document to generate XML text from</td></tr><tr><td><span class="term"><i><tt>doc_txt_ptr</tt></i>:</span></td><td>Memory pointer for allocated XML text</td></tr><tr><td><span class="term"><i><tt>doc_txt_len</tt></i>:</span></td><td>Length of the generated XML text</td></tr><tr><td><span class="term"><i><tt>txt_encoding</tt></i>:</span></td><td>Character encoding to use when generating XML text</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr></tbody></table></div><h3><a name="xmlDocDumpMemory" id="xmlDocDumpMemory"></a>Function: xmlDocDumpMemory</h3><pre class="programlisting">void	xmlDocDumpMemory		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** mem, <br />					 int * size)<br />
</pre><p>Dump an XML document in memory and return the #xmlChar * and it's size in bytes. It's up to the caller to free the memory with xmlFree(). The resulting byte array is zero terminated, though the last 0 is not included in the returned size.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>OUT: the memory pointer</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>OUT: the memory length</td></tr></tbody></table></div><h3><a name="xmlDocDumpMemoryEnc" id="xmlDocDumpMemoryEnc"></a>Function: xmlDocDumpMemoryEnc</h3><pre class="programlisting">void	xmlDocDumpMemoryEnc		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> out_doc, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** doc_txt_ptr, <br />					 int * doc_txt_len, <br />					 const char * txt_encoding)<br />
</pre><p>Dump the current DOM tree into memory using the character encoding specified by the caller. Note it is up to the caller of this function to free the allocated memory with xmlFree().</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out_doc</tt></i>:</span></td><td>Document to generate XML text from</td></tr><tr><td><span class="term"><i><tt>doc_txt_ptr</tt></i>:</span></td><td>Memory pointer for allocated XML text</td></tr><tr><td><span class="term"><i><tt>doc_txt_len</tt></i>:</span></td><td>Length of the generated XML text</td></tr><tr><td><span class="term"><i><tt>txt_encoding</tt></i>:</span></td><td>Character encoding to use when generating XML text</td></tr></tbody></table></div><h3><a name="xmlDocFormatDump" id="xmlDocFormatDump"></a>Function: xmlDocFormatDump</h3><pre class="programlisting">int	xmlDocFormatDump		(FILE * f, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 int format)<br />
</pre><p>Dump an XML document to an open FILE.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the FILE*</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of failure. Note that @format = 1 provide node indenting only if <a href="libxml-globals.html#xmlIndentTreeOutput">xmlIndentTreeOutput</a> = 1 or xmlKeepBlanksDefault(0) was called</td></tr></tbody></table></div><h3><a name="xmlDocGetRootElement" id="xmlDocGetRootElement"></a>Function: xmlDocGetRootElement</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlDocGetRootElement	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc)<br />
</pre><p>Get the root element of the document (doc-&gt;children is a list containing possibly comments, PIs, etc ...).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the #xmlNodePtr for the root or NULL</td></tr></tbody></table></div><h3><a name="xmlDocSetRootElement" id="xmlDocSetRootElement"></a>Function: xmlDocSetRootElement</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlDocSetRootElement	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> root)<br />
</pre><p>Set the root element of the document (doc-&gt;children is a list containing possibly comments, PIs, etc ...).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>root</tt></i>:</span></td><td>the new document root element, if root is NULL no action is taken, to remove a node from a document use xmlUnlinkNode(root) instead.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the old root element if any was found, NULL if root was NULL</td></tr></tbody></table></div><h3><a name="xmlElemDump" id="xmlElemDump"></a>Function: xmlElemDump</h3><pre class="programlisting">void	xmlElemDump			(FILE * f, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Dump an XML/HTML node, recursive behaviour, children are printed too.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr></tbody></table></div><h3><a name="xmlFirstElementChild" id="xmlFirstElementChild"></a>Function: xmlFirstElementChild</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlFirstElementChild	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent)<br />
</pre><p>Finds the first child node of that element which is a Element node Note the handling of entities references is different than in the W3C DOM element traversal spec since we don't have back <a href="libxml-SAX.html#reference">reference</a> from entities content to entities references.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the first element child or NULL if not available</td></tr></tbody></table></div><h3><a name="xmlFreeDoc" id="xmlFreeDoc"></a>Function: xmlFreeDoc</h3><pre class="programlisting">void	xmlFreeDoc			(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)<br />
</pre><p>Free up all the structures used by a document, tree included.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>pointer to the document</td></tr></tbody></table></div><h3><a name="xmlFreeDtd" id="xmlFreeDtd"></a>Function: xmlFreeDtd</h3><pre class="programlisting">void	xmlFreeDtd			(<a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a> cur)<br />
</pre><p>Free a DTD structure.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the DTD structure to free up</td></tr></tbody></table></div><h3><a name="xmlFreeNode" id="xmlFreeNode"></a>Function: xmlFreeNode</h3><pre class="programlisting">void	xmlFreeNode			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Free a node, this is a recursive behaviour, all the children are freed too. This doesn't unlink the child from the list, use xmlUnlinkNode() first.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node</td></tr></tbody></table></div><h3><a name="xmlFreeNodeList" id="xmlFreeNodeList"></a>Function: xmlFreeNodeList</h3><pre class="programlisting">void	xmlFreeNodeList			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Free a node and all its siblings, this is a recursive behaviour, all the children are freed too.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the first node in the list</td></tr></tbody></table></div><h3><a name="xmlFreeNs" id="xmlFreeNs"></a>Function: xmlFreeNs</h3><pre class="programlisting">void	xmlFreeNs			(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> cur)<br />
</pre><p>Free up the structures associated to a namespace</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the namespace pointer</td></tr></tbody></table></div><h3><a name="xmlFreeNsList" id="xmlFreeNsList"></a>Function: xmlFreeNsList</h3><pre class="programlisting">void	xmlFreeNsList			(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> cur)<br />
</pre><p>Free up all the structures associated to the chained namespaces.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the first namespace pointer</td></tr></tbody></table></div><h3><a name="xmlFreeProp" id="xmlFreeProp"></a>Function: xmlFreeProp</h3><pre class="programlisting">void	xmlFreeProp			(<a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)<br />
</pre><p>Free one attribute, all the content is freed too</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>an <a href="libxml-SAX.html#attribute">attribute</a></td></tr></tbody></table></div><h3><a name="xmlFreePropList" id="xmlFreePropList"></a>Function: xmlFreePropList</h3><pre class="programlisting">void	xmlFreePropList			(<a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)<br />
</pre><p>Free a property and all its siblings, all the children are freed too.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the first property in the list</td></tr></tbody></table></div><h3><a name="xmlGetBufferAllocationScheme" id="xmlGetBufferAllocationScheme"></a>Function: xmlGetBufferAllocationScheme</h3><pre class="programlisting"><a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	xmlGetBufferAllocationScheme	(void)<br />
</pre><p>Types are <a href="libxml-tree.html#XML_BUFFER_ALLOC_EXACT">XML_BUFFER_ALLOC_EXACT</a> - use exact sizes, keeps memory usage down <a href="libxml-tree.html#XML_BUFFER_ALLOC_DOUBLEIT">XML_BUFFER_ALLOC_DOUBLEIT</a> - double buffer when extra needed, improves performance <a href="libxml-tree.html#XML_BUFFER_ALLOC_HYBRID">XML_BUFFER_ALLOC_HYBRID</a> - use exact sizes on small strings to keep memory usage tight in normal usage, and doubleit on large strings to avoid pathological performance.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current allocation scheme</td></tr></tbody></table></div><h3><a name="xmlGetCompressMode" id="xmlGetCompressMode"></a>Function: xmlGetCompressMode</h3><pre class="programlisting">int	xmlGetCompressMode		(void)<br />
</pre><p>get the default compression mode used, ZLIB based.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 (uncompressed) to 9 (max compression)</td></tr></tbody></table></div><h3><a name="xmlGetDocCompressMode" id="xmlGetDocCompressMode"></a>Function: xmlGetDocCompressMode</h3><pre class="programlisting">int	xmlGetDocCompressMode		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc)<br />
</pre><p>get the compression ratio for a document, ZLIB based</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 (uncompressed) to 9 (max compression)</td></tr></tbody></table></div><h3><a name="xmlGetIntSubset" id="xmlGetIntSubset"></a>Function: xmlGetIntSubset</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	xmlGetIntSubset		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc)<br />
</pre><p>Get the internal subset of a document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document pointer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the DTD structure or NULL if not found</td></tr></tbody></table></div><h3><a name="xmlGetLastChild" id="xmlGetLastChild"></a>Function: xmlGetLastChild</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlGetLastChild		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * parent)<br />
</pre><p>Search the last child of a node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the last child or NULL if none.</td></tr></tbody></table></div><h3><a name="xmlGetLineNo" id="xmlGetLineNo"></a>Function: xmlGetLineNo</h3><pre class="programlisting">long	xmlGetLineNo			(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)<br />
</pre><p>Get line number of @node. Try to override the limitation of lines being store in 16 bits ints if <a href="libxml-parser.html#XML_PARSE_BIG_LINES">XML_PARSE_BIG_LINES</a> parser option was used</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>valid node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the line number if successful, -1 otherwise</td></tr></tbody></table></div><h3><a name="xmlGetNoNsProp" id="xmlGetNoNsProp"></a>Function: xmlGetNoNsProp</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlGetNoNsProp		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Search and get the value of an <a href="libxml-SAX.html#attribute">attribute</a> associated to a node This does the entity substitution. This function looks in DTD <a href="libxml-SAX.html#attribute">attribute</a> declaration for #FIXED or default declaration values unless DTD use has been turned off. This function is similar to <a href="libxml-tree.html#xmlGetProp">xmlGetProp</a> except it will accept only an <a href="libxml-SAX.html#attribute">attribute</a> in no namespace.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> value or NULL if not found. It's up to the caller to free the memory with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlGetNodePath" id="xmlGetNodePath"></a>Function: xmlGetNodePath</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlGetNodePath		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)<br />
</pre><p>Build a structure based Path for the given node</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new path or NULL in case of error. The caller must free the returned string</td></tr></tbody></table></div><h3><a name="xmlGetNsList" id="xmlGetNsList"></a>Function: xmlGetNsList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> *	xmlGetNsList		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)<br />
</pre><p>Search all the namespace applying to a given element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an NULL terminated array of all the #xmlNsPtr found that need to be freed by the caller or NULL if no namespace if defined</td></tr></tbody></table></div><h3><a name="xmlGetNsProp" id="xmlGetNsProp"></a>Function: xmlGetNsProp</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlGetNsProp		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nameSpace)<br />
</pre><p>Search and get the value of an <a href="libxml-SAX.html#attribute">attribute</a> associated to a node This <a href="libxml-SAX.html#attribute">attribute</a> has to be anchored in the namespace specified. This does the entity substitution. This function looks in DTD <a href="libxml-SAX.html#attribute">attribute</a> declaration for #FIXED or default declaration values unless DTD use has been turned off.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>nameSpace</tt></i>:</span></td><td>the URI of the namespace</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> value or NULL if not found. It's up to the caller to free the memory with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlGetProp" id="xmlGetProp"></a>Function: xmlGetProp</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlGetProp		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Search and get the value of an <a href="libxml-SAX.html#attribute">attribute</a> associated to a node This does the entity substitution. This function looks in DTD <a href="libxml-SAX.html#attribute">attribute</a> declaration for #FIXED or default declaration values unless DTD use has been turned off. NOTE: this function acts independently of namespaces associated to the attribute. Use xmlGetNsProp() or xmlGetNoNsProp() for namespace aware processing.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> value or NULL if not found. It's up to the caller to free the memory with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlHasNsProp" id="xmlHasNsProp"></a>Function: xmlHasNsProp</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlHasNsProp		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nameSpace)<br />
</pre><p>Search for an <a href="libxml-SAX.html#attribute">attribute</a> associated to a node This <a href="libxml-SAX.html#attribute">attribute</a> has to be anchored in the namespace specified. This does the entity substitution. This function looks in DTD <a href="libxml-SAX.html#attribute">attribute</a> declaration for #FIXED or default declaration values unless DTD use has been turned off. Note that a namespace of NULL indicates to use the default namespace.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>nameSpace</tt></i>:</span></td><td>the URI of the namespace</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> or the <a href="libxml-SAX.html#attribute">attribute</a> declaration or NULL if neither was found.</td></tr></tbody></table></div><h3><a name="xmlHasProp" id="xmlHasProp"></a>Function: xmlHasProp</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlHasProp		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Search an <a href="libxml-SAX.html#attribute">attribute</a> associated to a node This function also looks in DTD <a href="libxml-SAX.html#attribute">attribute</a> declaration for #FIXED or default declaration values unless DTD use has been turned off.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> or the <a href="libxml-SAX.html#attribute">attribute</a> declaration or NULL if neither was found.</td></tr></tbody></table></div><h3><a name="xmlIsBlankNode" id="xmlIsBlankNode"></a>Function: xmlIsBlankNode</h3><pre class="programlisting">int	xmlIsBlankNode			(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)<br />
</pre><p>Checks whether this node is an empty or whitespace only (and possibly ignorable) text-node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 yes, 0 no</td></tr></tbody></table></div><h3><a name="xmlIsXHTML" id="xmlIsXHTML"></a>Function: xmlIsXHTML</h3><pre class="programlisting">int	xmlIsXHTML			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicID)<br />
</pre><p>Try to find if the document correspond to an XHTML DTD</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>systemID</tt></i>:</span></td><td>the system identifier</td></tr><tr><td><span class="term"><i><tt>publicID</tt></i>:</span></td><td>the public identifier</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if not and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlLastElementChild" id="xmlLastElementChild"></a>Function: xmlLastElementChild</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlLastElementChild	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent)<br />
</pre><p>Finds the last child node of that element which is a Element node Note the handling of entities references is different than in the W3C DOM element traversal spec since we don't have back <a href="libxml-SAX.html#reference">reference</a> from entities content to entities references.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the last element child or NULL if not available</td></tr></tbody></table></div><h3><a name="xmlNewCDataBlock" id="xmlNewCDataBlock"></a>Function: xmlNewCDataBlock</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewCDataBlock	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)<br />
</pre><p>Creation of a new node containing a CDATA block.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the CDATA block content content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of the block</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewCharRef" id="xmlNewCharRef"></a>Function: xmlNewCharRef</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewCharRef		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Creation of a new character <a href="libxml-SAX.html#reference">reference</a> node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the char ref string, starting with # or "&amp;# ... ;"</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewChild" id="xmlNewChild"></a>Function: xmlNewChild</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewChild		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new child element, added at the end of @parent children list. @ns and @content parameters are optional (NULL). If @ns is NULL, the newly created element inherits the namespace of @parent. If @content is non NULL, a child list containing the TEXTs and ENTITY_REFs node will be created. NOTE: @content is supposed to be a piece of XML CDATA, so it allows entity references. XML special chars must be escaped first by using xmlEncodeEntitiesReentrant(), or xmlNewTextChild() should be used.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a namespace if any</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the child</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the XML content of the child if any.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewComment" id="xmlNewComment"></a>Function: xmlNewComment</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewComment		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new node containing a comment.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the <a href="libxml-SAX.html#comment">comment</a> content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDoc" id="xmlNewDoc"></a>Function: xmlNewDoc</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlNewDoc		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * version)<br />
</pre><p>Creates a new XML document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>version</tt></i>:</span></td><td><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string giving the version of XML "1.0"</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new document</td></tr></tbody></table></div><h3><a name="xmlNewDocComment" id="xmlNewDocComment"></a>Function: xmlNewDocComment</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewDocComment	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new node containing a <a href="libxml-SAX.html#comment">comment</a> within a document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the <a href="libxml-SAX.html#comment">comment</a> content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDocFragment" id="xmlNewDocFragment"></a>Function: xmlNewDocFragment</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewDocFragment	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>Creation of a new Fragment node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document owning the fragment</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDocNode" id="xmlNewDocNode"></a>Function: xmlNewDocNode</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewDocNode		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new node element within a document. @ns and @content are optional (NULL). NOTE: @content is supposed to be a piece of XML CDATA, so it allow entities references, but XML special chars need to be escaped first by using xmlEncodeEntitiesReentrant(). Use xmlNewDocRawNode() if you don't need entities support.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>namespace if any</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the node name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the XML text content if any</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDocNodeEatName" id="xmlNewDocNodeEatName"></a>Function: xmlNewDocNodeEatName</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewDocNodeEatName	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new node element within a document. @ns and @content are optional (NULL). NOTE: @content is supposed to be a piece of XML CDATA, so it allow entities references, but XML special chars need to be escaped first by using xmlEncodeEntitiesReentrant(). Use xmlNewDocRawNode() if you don't need entities support.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>namespace if any</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the node name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the XML text content if any</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDocPI" id="xmlNewDocPI"></a>Function: xmlNewDocPI</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewDocPI		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a processing instruction element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the target document</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the processing instruction name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the PI content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDocProp" id="xmlNewDocProp"></a>Function: xmlNewDocProp</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlNewDocProp		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Create a new property carried by a document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the <a href="libxml-SAX.html#attribute">attribute</a></td></tr></tbody></table></div><h3><a name="xmlNewDocRawNode" id="xmlNewDocRawNode"></a>Function: xmlNewDocRawNode</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewDocRawNode	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new node element within a document. @ns and @content are optional (NULL).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>namespace if any</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the node name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the text content if any</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDocText" id="xmlNewDocText"></a>Function: xmlNewDocText</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewDocText		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new text node within a document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the text content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDocTextLen" id="xmlNewDocTextLen"></a>Function: xmlNewDocTextLen</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewDocTextLen	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)<br />
</pre><p>Creation of a new text node with an extra content length parameter. The text node pertain to a given document.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the text content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the text len.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewDtd" id="xmlNewDtd"></a>Function: xmlNewDtd</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDtdPtr">xmlDtdPtr</a>	xmlNewDtd		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br />
</pre><p>Creation of a new DTD for the external subset. To create an internal subset, use xmlCreateIntSubset().</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document pointer</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the DTD name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the system ID</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new DTD structure</td></tr></tbody></table></div><h3><a name="xmlNewGlobalNs" id="xmlNewGlobalNs"></a>Function: xmlNewGlobalNs</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	xmlNewGlobalNs		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)<br />
</pre><p>Creation of a Namespace, the old way using PI and without scoping DEPRECATED !!!</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document carrying the namespace</td></tr><tr><td><span class="term"><i><tt>href</tt></i>:</span></td><td>the URI associated</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the prefix for the namespace</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL this functionality had been removed</td></tr></tbody></table></div><h3><a name="xmlNewNode" id="xmlNewNode"></a>Function: xmlNewNode</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewNode		(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Creation of a new node element. @ns is optional (NULL).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>namespace if any</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the node name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object. Uses xmlStrdup() to make copy of @name.</td></tr></tbody></table></div><h3><a name="xmlNewNodeEatName" id="xmlNewNodeEatName"></a>Function: xmlNewNodeEatName</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewNodeEatName	(<a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Creation of a new node element. @ns is optional (NULL).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>namespace if any</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the node name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object, with pointer @name as new node's name. Use xmlNewNode() if a copy of @name string is is needed as new node's name.</td></tr></tbody></table></div><h3><a name="xmlNewNs" id="xmlNewNs"></a>Function: xmlNewNs</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	xmlNewNs		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix)<br />
</pre><p>Creation of a new Namespace. This function will refuse to create a namespace with a similar prefix than an existing one present on this node. Note that for a default namespace, @prefix should be NULL. We use href==NULL in the case of an element creation where the namespace was not defined.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the element carrying the namespace</td></tr><tr><td><span class="term"><i><tt>href</tt></i>:</span></td><td>the URI associated</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the prefix for the namespace</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new namespace pointer or NULL</td></tr></tbody></table></div><h3><a name="xmlNewNsProp" id="xmlNewNsProp"></a>Function: xmlNewNsProp</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlNewNsProp		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Create a new property tagged with a namespace and carried by a node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the holding node</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>the namespace</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the <a href="libxml-SAX.html#attribute">attribute</a></td></tr></tbody></table></div><h3><a name="xmlNewNsPropEatName" id="xmlNewNsPropEatName"></a>Function: xmlNewNsPropEatName</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlNewNsPropEatName	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Create a new property tagged with a namespace and carried by a node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the holding node</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>the namespace</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the <a href="libxml-SAX.html#attribute">attribute</a></td></tr></tbody></table></div><h3><a name="xmlNewPI" id="xmlNewPI"></a>Function: xmlNewPI</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewPI		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a processing instruction element. Use xmlDocNewPI preferably to get string interning</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the processing instruction name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the PI content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewProp" id="xmlNewProp"></a>Function: xmlNewProp</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlNewProp		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Create a new property carried by a node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the holding node</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the <a href="libxml-SAX.html#attribute">attribute</a></td></tr></tbody></table></div><h3><a name="xmlNewReference" id="xmlNewReference"></a>Function: xmlNewReference</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewReference		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Creation of a new <a href="libxml-SAX.html#reference">reference</a> node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#reference">reference</a> name, or the <a href="libxml-SAX.html#reference">reference</a> string with &amp; and ;</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewText" id="xmlNewText"></a>Function: xmlNewText</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewText		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new text node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the text content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewTextChild" id="xmlNewTextChild"></a>Function: xmlNewTextChild</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewTextChild		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> parent, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Creation of a new child element, added at the end of @parent children list. @ns and @content parameters are optional (NULL). If @ns is NULL, the newly created element inherits the namespace of @parent. If @content is non NULL, a child TEXT node will be created containing the string @content. NOTE: Use xmlNewChild() if @content will contain entities that need to be preserved. Use this function, xmlNewTextChild(), if you need to ensure that reserved XML chars that might appear in @content, such as the ampersand, greater-than or less-than signs, are automatically replaced by their XML escaped entity representations.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>parent</tt></i>:</span></td><td>the parent node</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a namespace if any</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the child</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the text content of the child if any.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNewTextLen" id="xmlNewTextLen"></a>Function: xmlNewTextLen</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNewTextLen		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)<br />
</pre><p>Creation of a new text node with an extra parameter for the content's length</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the text content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the text len.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the new node object.</td></tr></tbody></table></div><h3><a name="xmlNextElementSibling" id="xmlNextElementSibling"></a>Function: xmlNextElementSibling</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlNextElementSibling	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Finds the first closest next sibling of the node which is an element node. Note the handling of entities references is different than in the W3C DOM element traversal spec since we don't have back <a href="libxml-SAX.html#reference">reference</a> from entities content to entities references.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element sibling or NULL if not available</td></tr></tbody></table></div><h3><a name="xmlNodeAddContent" id="xmlNodeAddContent"></a>Function: xmlNodeAddContent</h3><pre class="programlisting">void	xmlNodeAddContent		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Append the extra substring to the node content. NOTE: In contrast to xmlNodeSetContent(), @content is supposed to be raw text, so unescaped XML special chars are allowed, entity references are not supported.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being modified</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>extra content</td></tr></tbody></table></div><h3><a name="xmlNodeAddContentLen" id="xmlNodeAddContentLen"></a>Function: xmlNodeAddContentLen</h3><pre class="programlisting">void	xmlNodeAddContentLen		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)<br />
</pre><p>Append the extra substring to the node content. NOTE: In contrast to xmlNodeSetContentLen(), @content is supposed to be raw text, so unescaped XML special chars are allowed, entity references are not supported.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being modified</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>extra content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the size of @content</td></tr></tbody></table></div><h3><a name="xmlNodeBufGetContent" id="xmlNodeBufGetContent"></a>Function: xmlNodeBufGetContent</h3><pre class="programlisting">int	xmlNodeBufGetContent		(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buffer, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)<br />
</pre><p>Read the value of a node @cur, this can be either the text carried directly by this node if it's a TEXT node or the aggregate string of the values carried by this node child's (TEXT and ENTITY_REF). Entity references are substituted. Fills up the buffer @buffer with this value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a buffer</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being read</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlNodeDump" id="xmlNodeDump"></a>Function: xmlNodeDump</h3><pre class="programlisting">int	xmlNodeDump			(<a href="libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 int level, <br />					 int format)<br />
</pre><p>Dump an XML node, recursive behaviour,children are printed too. Note that @format = 1 provide node indenting only if <a href="libxml-globals.html#xmlIndentTreeOutput">xmlIndentTreeOutput</a> = 1 or xmlKeepBlanksDefault(0) was called Since this is using <a href="libxml-tree.html#xmlBuffer">xmlBuffer</a> structures it is limited to 2GB and somehow deprecated, use xmlBufNodeDump() instead.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the XML buffer output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>level</tt></i>:</span></td><td>the imbrication level for indenting</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>is formatting allowed</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written to the buffer or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlNodeDumpOutput" id="xmlNodeDumpOutput"></a>Function: xmlNodeDumpOutput</h3><pre class="programlisting">void	xmlNodeDumpOutput		(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 int level, <br />					 int format, <br />					 const char * encoding)<br />
</pre><p>Dump an XML node, recursive behaviour, children are printed too. Note that @format = 1 provide node indenting only if <a href="libxml-globals.html#xmlIndentTreeOutput">xmlIndentTreeOutput</a> = 1 or xmlKeepBlanksDefault(0) was called</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>the XML buffer output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>level</tt></i>:</span></td><td>the imbrication level for indenting</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>is formatting allowed</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>an optional encoding string</td></tr></tbody></table></div><h3><a name="xmlNodeGetBase" id="xmlNodeGetBase"></a>Function: xmlNodeGetBase</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlNodeGetBase		(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)<br />
</pre><p>Searches for the BASE URL. The code should work on both XML and HTML document even if base mechanisms are completely different. It returns the base as defined in RFC 2396 sections 5.1.1. Base URI within Document Content and 5.1.2. Base URI from the Encapsulating Entity However it does not return the document base (5.1.3), use doc-&gt;URL in this case</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document the node pertains to</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being checked</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the base URL, or NULL if not found It's up to the caller to free the memory with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlNodeGetContent" id="xmlNodeGetContent"></a>Function: xmlNodeGetContent</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlNodeGetContent	(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)<br />
</pre><p>Read the value of a node, this can be either the text carried directly by this node if it's a TEXT node or the aggregate string of the values carried by this node child's (TEXT and ENTITY_REF). Entity references are substituted.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being read</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new #xmlChar * or NULL if no content is available. It's up to the caller to free the memory with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlNodeGetLang" id="xmlNodeGetLang"></a>Function: xmlNodeGetLang</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlNodeGetLang		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)<br />
</pre><p>Searches the language of a node, i.e. the values of the xml:lang <a href="libxml-SAX.html#attribute">attribute</a> or the one carried by the nearest ancestor.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being checked</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the lang value, or NULL if not found It's up to the caller to free the memory with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlNodeGetSpacePreserve" id="xmlNodeGetSpacePreserve"></a>Function: xmlNodeGetSpacePreserve</h3><pre class="programlisting">int	xmlNodeGetSpacePreserve		(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * cur)<br />
</pre><p>Searches the space preserving behaviour of a node, i.e. the values of the xml:space <a href="libxml-SAX.html#attribute">attribute</a> or the one carried by the nearest ancestor.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being checked</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 if xml:space is not inherited, 0 if "default", 1 if "preserve"</td></tr></tbody></table></div><h3><a name="xmlNodeIsText" id="xmlNodeIsText"></a>Function: xmlNodeIsText</h3><pre class="programlisting">int	xmlNodeIsText			(const <a href="libxml-tree.html#xmlNode">xmlNode</a> * node)<br />
</pre><p>Is this node a Text node ?</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 yes, 0 no</td></tr></tbody></table></div><h3><a name="xmlNodeListGetRawString" id="xmlNodeListGetRawString"></a>Function: xmlNodeListGetRawString</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlNodeListGetRawString	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * list, <br />					 int inLine)<br />
</pre><p>Builds the string equivalent to the text contained in the Node list made of TEXTs and ENTITY_REFs, contrary to xmlNodeListGetString() this function doesn't do any character encoding handling.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>list</tt></i>:</span></td><td>a Node list</td></tr><tr><td><span class="term"><i><tt>inLine</tt></i>:</span></td><td>should we replace entity contents or show their external form</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the string copy, the caller must free it with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlNodeListGetString" id="xmlNodeListGetString"></a>Function: xmlNodeListGetString</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlNodeListGetString	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 const <a href="libxml-tree.html#xmlNode">xmlNode</a> * list, <br />					 int inLine)<br />
</pre><p>Build the string equivalent to the text contained in the Node list made of TEXTs and ENTITY_REFs</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>list</tt></i>:</span></td><td>a Node list</td></tr><tr><td><span class="term"><i><tt>inLine</tt></i>:</span></td><td>should we replace entity contents or show their external form</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the string copy, the caller must free it with xmlFree().</td></tr></tbody></table></div><h3><a name="xmlNodeSetBase" id="xmlNodeSetBase"></a>Function: xmlNodeSetBase</h3><pre class="programlisting">void	xmlNodeSetBase			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * uri)<br />
</pre><p>Set (or reset) the base URI of a node, i.e. the value of the xml:base attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being changed</td></tr><tr><td><span class="term"><i><tt>uri</tt></i>:</span></td><td>the new base URI</td></tr></tbody></table></div><h3><a name="xmlNodeSetContent" id="xmlNodeSetContent"></a>Function: xmlNodeSetContent</h3><pre class="programlisting">void	xmlNodeSetContent		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>Replace the content of a node. NOTE: @content is supposed to be a piece of XML CDATA, so it allows entity references, but XML special chars need to be escaped first by using xmlEncodeEntitiesReentrant() resp. xmlEncodeSpecialChars().</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being modified</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the new value of the content</td></tr></tbody></table></div><h3><a name="xmlNodeSetContentLen" id="xmlNodeSetContentLen"></a>Function: xmlNodeSetContentLen</h3><pre class="programlisting">void	xmlNodeSetContentLen		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)<br />
</pre><p>Replace the content of a node. NOTE: @content is supposed to be a piece of XML CDATA, so it allows entity references, but XML special chars need to be escaped first by using xmlEncodeEntitiesReentrant() resp. xmlEncodeSpecialChars().</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being modified</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the new value of the content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the size of @content</td></tr></tbody></table></div><h3><a name="xmlNodeSetLang" id="xmlNodeSetLang"></a>Function: xmlNodeSetLang</h3><pre class="programlisting">void	xmlNodeSetLang			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * lang)<br />
</pre><p>Set the language of a node, i.e. the values of the xml:lang attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being changed</td></tr><tr><td><span class="term"><i><tt>lang</tt></i>:</span></td><td>the language description</td></tr></tbody></table></div><h3><a name="xmlNodeSetName" id="xmlNodeSetName"></a>Function: xmlNodeSetName</h3><pre class="programlisting">void	xmlNodeSetName			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Set (or reset) the name of a node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being changed</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the new tag name</td></tr></tbody></table></div><h3><a name="xmlNodeSetSpacePreserve" id="xmlNodeSetSpacePreserve"></a>Function: xmlNodeSetSpacePreserve</h3><pre class="programlisting">void	xmlNodeSetSpacePreserve		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur, <br />					 int val)<br />
</pre><p>Set (or reset) the space preserving behaviour of a node, i.e. the value of the xml:space attribute.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node being changed</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the xml:space value ("0": default, 1: "preserve")</td></tr></tbody></table></div><h3><a name="xmlPreviousElementSibling" id="xmlPreviousElementSibling"></a>Function: xmlPreviousElementSibling</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlPreviousElementSibling	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br />
</pre><p>Finds the first closest previous sibling of the node which is an element node. Note the handling of entities references is different than in the W3C DOM element traversal spec since we don't have back <a href="libxml-SAX.html#reference">reference</a> from entities content to entities references.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the previous element sibling or NULL if not available</td></tr></tbody></table></div><h3><a name="xmlReconciliateNs" id="xmlReconciliateNs"></a>Function: xmlReconciliateNs</h3><pre class="programlisting">int	xmlReconciliateNs		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree)<br />
</pre><p>This function checks that all the namespaces declared within the given tree are properly declared. This is needed for example after Copy or Cut and then paste operations. The subtree may still hold pointers to namespace declarations outside the subtree or invalid/masked. As much as possible the function try to reuse the existing namespaces found in the new environment. If not possible the new namespaces are redeclared on @tree at the top of the given subtree.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>a node defining the subtree to reconciliate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of namespace declarations created or -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlRemoveProp" id="xmlRemoveProp"></a>Function: xmlRemoveProp</h3><pre class="programlisting">int	xmlRemoveProp			(<a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a> cur)<br />
</pre><p>Unlink and free one attribute, all the content is freed too Note this doesn't work for namespace definition attributes</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>an <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if success and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlReplaceNode" id="xmlReplaceNode"></a>Function: xmlReplaceNode</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlReplaceNode		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> old, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Unlink the old node from its current context, prune the new one at the same place. If @cur was already inserted in a document it is first unlinked from its existing context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>old</tt></i>:</span></td><td>the old node</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the @old node</td></tr></tbody></table></div><h3><a name="xmlSaveFile" id="xmlSaveFile"></a>Function: xmlSaveFile</h3><pre class="programlisting">int	xmlSaveFile			(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur)<br />
</pre><p>Dump an XML document to a file. Will use compression if compiled in and enabled. If @filename is "-" the stdout file is used.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename (or URL)</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlSaveFileEnc" id="xmlSaveFileEnc"></a>Function: xmlSaveFileEnc</h3><pre class="programlisting">int	xmlSaveFileEnc			(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding)<br />
</pre><p>Dump an XML document, converting it to the given encoding</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename (or URL)</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the name of an encoding (or NULL)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlSaveFileTo" id="xmlSaveFileTo"></a>Function: xmlSaveFileTo</h3><pre class="programlisting">int	xmlSaveFileTo			(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding)<br />
</pre><p>Dump an XML document to an I/O buffer. Warning ! This call xmlOutputBufferClose() on buf which is not available after this call.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>an output I/O buffer</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding if any assuming the I/O layer handles the trancoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlSaveFormatFile" id="xmlSaveFormatFile"></a>Function: xmlSaveFormatFile</h3><pre class="programlisting">int	xmlSaveFormatFile		(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 int format)<br />
</pre><p>Dump an XML document to a file. Will use compression if compiled in and enabled. If @filename is "-" the stdout file is used. If @format is set then the document will be indented on output. Note that @format = 1 provide node indenting only if <a href="libxml-globals.html#xmlIndentTreeOutput">xmlIndentTreeOutput</a> = 1 or xmlKeepBlanksDefault(0) was called</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename (or URL)</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlSaveFormatFileEnc" id="xmlSaveFormatFileEnc"></a>Function: xmlSaveFormatFileEnc</h3><pre class="programlisting">int	xmlSaveFormatFileEnc		(const char * filename, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding, <br />					 int format)<br />
</pre><p>Dump an XML document to a file or an URL.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename or URL to output</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document being saved</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the name of the encoding to use or NULL.</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces be added.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of error. Note that @format = 1 provide node indenting only if <a href="libxml-globals.html#xmlIndentTreeOutput">xmlIndentTreeOutput</a> = 1 or xmlKeepBlanksDefault(0) was called</td></tr></tbody></table></div><h3><a name="xmlSaveFormatFileTo" id="xmlSaveFormatFileTo"></a>Function: xmlSaveFormatFileTo</h3><pre class="programlisting">int	xmlSaveFormatFileTo		(<a href="libxml-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> buf, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> cur, <br />					 const char * encoding, <br />					 int format)<br />
</pre><p>Dump an XML document to an I/O buffer. Warning ! This call xmlOutputBufferClose() on buf which is not available after this call.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>an output I/O buffer</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding if any assuming the I/O layer handles the trancoding</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>should formatting spaces been added</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of failure.</td></tr></tbody></table></div><h3><a name="xmlSearchNs" id="xmlSearchNs"></a>Function: xmlSearchNs</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	xmlSearchNs		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * nameSpace)<br />
</pre><p>Search a Ns registered under a given name space for a document. recurse on the parents until it finds the defined namespace or return NULL otherwise. @nameSpace can be NULL, this is a search for the default namespace. We don't allow to cross entities boundaries. If you don't declare the namespace within those you will be in troubles !!! A warning is generated to cover this case.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>nameSpace</tt></i>:</span></td><td>the namespace prefix</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the namespace pointer or NULL.</td></tr></tbody></table></div><h3><a name="xmlSearchNsByHref" id="xmlSearchNsByHref"></a>Function: xmlSearchNsByHref</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a>	xmlSearchNsByHref	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * href)<br />
</pre><p>Search a Ns aliasing a given URI. Recurse on the parents until it finds the defined namespace or return NULL otherwise.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr><tr><td><span class="term"><i><tt>href</tt></i>:</span></td><td>the namespace value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the namespace pointer or NULL.</td></tr></tbody></table></div><h3><a name="xmlSetBufferAllocationScheme" id="xmlSetBufferAllocationScheme"></a>Function: xmlSetBufferAllocationScheme</h3><pre class="programlisting">void	xmlSetBufferAllocationScheme	(<a href="libxml-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> scheme)<br />
</pre><p>Set the buffer allocation method. Types are <a href="libxml-tree.html#XML_BUFFER_ALLOC_EXACT">XML_BUFFER_ALLOC_EXACT</a> - use exact sizes, keeps memory usage down <a href="libxml-tree.html#XML_BUFFER_ALLOC_DOUBLEIT">XML_BUFFER_ALLOC_DOUBLEIT</a> - double buffer when extra needed, improves performance</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>scheme</tt></i>:</span></td><td>allocation method to use</td></tr></tbody></table></div><h3><a name="xmlSetCompressMode" id="xmlSetCompressMode"></a>Function: xmlSetCompressMode</h3><pre class="programlisting">void	xmlSetCompressMode		(int mode)<br />
</pre><p>set the default compression mode used, ZLIB based Correct values: 0 (uncompressed) to 9 (max compression)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>mode</tt></i>:</span></td><td>the compression ratio</td></tr></tbody></table></div><h3><a name="xmlSetDocCompressMode" id="xmlSetDocCompressMode"></a>Function: xmlSetDocCompressMode</h3><pre class="programlisting">void	xmlSetDocCompressMode		(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />					 int mode)<br />
</pre><p>set the compression ratio for a document, ZLIB based Correct values: 0 (uncompressed) to 9 (max compression)</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>mode</tt></i>:</span></td><td>the compression ratio</td></tr></tbody></table></div><h3><a name="xmlSetListDoc" id="xmlSetListDoc"></a>Function: xmlSetListDoc</h3><pre class="programlisting">void	xmlSetListDoc			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> list, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>update all nodes in the list to point to the right document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>list</tt></i>:</span></td><td>the first element</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr></tbody></table></div><h3><a name="xmlSetNs" id="xmlSetNs"></a>Function: xmlSetNs</h3><pre class="programlisting">void	xmlSetNs			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns)<br />
</pre><p>Associate a namespace to a node, a posteriori.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node in the document</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a namespace pointer</td></tr></tbody></table></div><h3><a name="xmlSetNsProp" id="xmlSetNsProp"></a>Function: xmlSetNsProp</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlSetNsProp		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Set (or reset) an <a href="libxml-SAX.html#attribute">attribute</a> carried by a node. The ns structure must be in scope, this is not checked</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>the namespace definition</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> pointer.</td></tr></tbody></table></div><h3><a name="xmlSetProp" id="xmlSetProp"></a>Function: xmlSetProp</h3><pre class="programlisting"><a href="libxml-tree.html#xmlAttrPtr">xmlAttrPtr</a>	xmlSetProp		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Set (or reset) an <a href="libxml-SAX.html#attribute">attribute</a> carried by a node. If @name has a prefix, then the corresponding namespace-binding will be used, if in scope; it is an error it there's no such ns-binding for the prefix in scope.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name (a QName)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> pointer.</td></tr></tbody></table></div><h3><a name="xmlSetTreeDoc" id="xmlSetTreeDoc"></a>Function: xmlSetTreeDoc</h3><pre class="programlisting">void	xmlSetTreeDoc			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br />					 <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br />
</pre><p>update all nodes under the tree to point to the right document</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>the top element</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr></tbody></table></div><h3><a name="xmlSplitQName2" id="xmlSplitQName2"></a>Function: xmlSplitQName2</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlSplitQName2		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** prefix)<br />
</pre><p>parse an XML qualified name string [NS 5] QName ::= (Prefix ':')? LocalPart [NS 6] Prefix ::= NCName [NS 7] LocalPart ::= NCName</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the full QName</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> **</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL if not a QName, otherwise the local part, and prefix is updated to get the Prefix if any.</td></tr></tbody></table></div><h3><a name="xmlSplitQName3" id="xmlSplitQName3"></a>Function: xmlSplitQName3</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlSplitQName3		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int * len)<br />
</pre><p>parse an XML qualified name string,i</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the full QName</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>an int *</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL if it is not a Qualified Name, otherwise, update len with the length in byte of the prefix and return a pointer to the start of the name without the prefix</td></tr></tbody></table></div><h3><a name="xmlStringGetNodeList" id="xmlStringGetNodeList"></a>Function: xmlStringGetNodeList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlStringGetNodeList	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Parse the value string and build the node list associated. Should produce a flat tree with only TEXTs and ENTITY_REFs.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value of the <a href="libxml-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the first child</td></tr></tbody></table></div><h3><a name="xmlStringLenGetNodeList" id="xmlStringLenGetNodeList"></a>Function: xmlStringLenGetNodeList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlStringLenGetNodeList	(const <a href="libxml-tree.html#xmlDoc">xmlDoc</a> * doc, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int len)<br />
</pre><p>Parse the value string and build the node list associated. Should produce a flat tree with only TEXTs and ENTITY_REFs.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value of the text</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of the string value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the first child</td></tr></tbody></table></div><h3><a name="xmlTextConcat" id="xmlTextConcat"></a>Function: xmlTextConcat</h3><pre class="programlisting">int	xmlTextConcat			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content, <br />					 int len)<br />
</pre><p>Concat the given string at the end of the existing node content</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>@content length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlTextMerge" id="xmlTextMerge"></a>Function: xmlTextMerge</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlTextMerge		(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> first, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> second)<br />
</pre><p>Merge two text nodes into one</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>first</tt></i>:</span></td><td>the first text node</td></tr><tr><td><span class="term"><i><tt>second</tt></i>:</span></td><td>the second text node being merged</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the first text node augmented</td></tr></tbody></table></div><h3><a name="xmlUnlinkNode" id="xmlUnlinkNode"></a>Function: xmlUnlinkNode</h3><pre class="programlisting">void	xmlUnlinkNode			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br />
</pre><p>Unlink a node from it's current context, the node is not freed If one need to free the node, use xmlFreeNode() routine after the unlink to discard it. Note that namespace nodes can't be unlinked as they do not have pointer to their parent.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node</td></tr></tbody></table></div><h3><a name="xmlUnsetNsProp" id="xmlUnsetNsProp"></a>Function: xmlUnsetNsProp</h3><pre class="programlisting">int	xmlUnsetNsProp			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 <a href="libxml-tree.html#xmlNsPtr">xmlNsPtr</a> ns, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Remove an <a href="libxml-SAX.html#attribute">attribute</a> carried by a node.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>the namespace definition</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if successful, -1 if not found</td></tr></tbody></table></div><h3><a name="xmlUnsetProp" id="xmlUnsetProp"></a>Function: xmlUnsetProp</h3><pre class="programlisting">int	xmlUnsetProp			(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Remove an <a href="libxml-SAX.html#attribute">attribute</a> carried by a node. This handles only attributes in no namespace.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if successful, -1 if not found</td></tr></tbody></table></div><h3><a name="xmlValidateNCName" id="xmlValidateNCName"></a>Function: xmlValidateNCName</h3><pre class="programlisting">int	xmlValidateNCName		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int space)<br />
</pre><p>Check that a value conforms to the lexical space of NCName</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value to check</td></tr><tr><td><span class="term"><i><tt>space</tt></i>:</span></td><td>allow spaces in front and end of the string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if this validates, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlValidateNMToken" id="xmlValidateNMToken"></a>Function: xmlValidateNMToken</h3><pre class="programlisting">int	xmlValidateNMToken		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int space)<br />
</pre><p>Check that a value conforms to the lexical space of NMToken</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value to check</td></tr><tr><td><span class="term"><i><tt>space</tt></i>:</span></td><td>allow spaces in front and end of the string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if this validates, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlValidateName" id="xmlValidateName"></a>Function: xmlValidateName</h3><pre class="programlisting">int	xmlValidateName			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int space)<br />
</pre><p>Check that a value conforms to the lexical space of Name</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value to check</td></tr><tr><td><span class="term"><i><tt>space</tt></i>:</span></td><td>allow spaces in front and end of the string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if this validates, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><h3><a name="xmlValidateQName" id="xmlValidateQName"></a>Function: xmlValidateQName</h3><pre class="programlisting">int	xmlValidateQName		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int space)<br />
</pre><p>Check that a value conforms to the lexical space of QName</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value to check</td></tr><tr><td><span class="term"><i><tt>space</tt></i>:</span></td><td>allow spaces in front and end of the string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if this validates, a positive error code number otherwise and -1 in case of internal or API error.</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
