<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module chvalid from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module chvalid from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-catalog.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-catalog.html">catalog</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-debugXML.html">debugXML</a></th><td><a accesskey="n" href="libxml-debugXML.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>this module exports interfaces for the character range validation APIs  This file is automatically generated from the cvs source definition files using the genChRanges.py Python script </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#xmlIsBaseCharQ">xmlIsBaseCharQ</a></pre><pre class="programlisting">#define <a href="#xmlIsBaseChar_ch">xmlIsBaseChar_ch</a></pre><pre class="programlisting">#define <a href="#xmlIsBlankQ">xmlIsBlankQ</a></pre><pre class="programlisting">#define <a href="#xmlIsBlank_ch">xmlIsBlank_ch</a></pre><pre class="programlisting">#define <a href="#xmlIsCharQ">xmlIsCharQ</a></pre><pre class="programlisting">#define <a href="#xmlIsChar_ch">xmlIsChar_ch</a></pre><pre class="programlisting">#define <a href="#xmlIsCombiningQ">xmlIsCombiningQ</a></pre><pre class="programlisting">#define <a href="#xmlIsDigitQ">xmlIsDigitQ</a></pre><pre class="programlisting">#define <a href="#xmlIsDigit_ch">xmlIsDigit_ch</a></pre><pre class="programlisting">#define <a href="#xmlIsExtenderQ">xmlIsExtenderQ</a></pre><pre class="programlisting">#define <a href="#xmlIsExtender_ch">xmlIsExtender_ch</a></pre><pre class="programlisting">#define <a href="#xmlIsIdeographicQ">xmlIsIdeographicQ</a></pre><pre class="programlisting">#define <a href="#xmlIsPubidCharQ">xmlIsPubidCharQ</a></pre><pre class="programlisting">#define <a href="#xmlIsPubidChar_ch">xmlIsPubidChar_ch</a></pre><pre class="programlisting">Structure <a href="#xmlChLRange">xmlChLRange</a><br />struct _xmlChLRange
</pre><pre class="programlisting">Typedef <a href="libxml-chvalid.html#xmlChLRange">xmlChLRange</a> * <a name="xmlChLRangePtr" id="xmlChLRangePtr">xmlChLRangePtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlChRangeGroup">xmlChRangeGroup</a><br />struct _xmlChRangeGroup
</pre><pre class="programlisting">Typedef <a href="libxml-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> * <a name="xmlChRangeGroupPtr" id="xmlChRangeGroupPtr">xmlChRangeGroupPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlChSRange">xmlChSRange</a><br />struct _xmlChSRange
</pre><pre class="programlisting">Typedef <a href="libxml-chvalid.html#xmlChSRange">xmlChSRange</a> * <a name="xmlChSRangePtr" id="xmlChSRangePtr">xmlChSRangePtr</a>
</pre><pre class="programlisting">int	<a href="#xmlCharInRange">xmlCharInRange</a>			(unsigned int val, <br />					 const <a href="libxml-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> * rptr)</pre>
<pre class="programlisting">int	<a href="#xmlIsBaseChar">xmlIsBaseChar</a>			(unsigned int ch)</pre>
<pre class="programlisting">int	<a href="#xmlIsBlank">xmlIsBlank</a>			(unsigned int ch)</pre>
<pre class="programlisting">int	<a href="#xmlIsChar">xmlIsChar</a>			(unsigned int ch)</pre>
<pre class="programlisting">int	<a href="#xmlIsCombining">xmlIsCombining</a>			(unsigned int ch)</pre>
<pre class="programlisting">int	<a href="#xmlIsDigit">xmlIsDigit</a>			(unsigned int ch)</pre>
<pre class="programlisting">int	<a href="#xmlIsExtender">xmlIsExtender</a>			(unsigned int ch)</pre>
<pre class="programlisting">int	<a href="#xmlIsIdeographic">xmlIsIdeographic</a>		(unsigned int ch)</pre>
<pre class="programlisting">int	<a href="#xmlIsPubidChar">xmlIsPubidChar</a>			(unsigned int ch)</pre>
<h2>Description</h2>
<h3><a name="xmlIsBaseCharQ" id="xmlIsBaseCharQ"></a>Macro: xmlIsBaseCharQ</h3><pre>#define xmlIsBaseCharQ</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsBaseChar_ch" id="xmlIsBaseChar_ch"></a>Macro: xmlIsBaseChar_ch</h3><pre>#define xmlIsBaseChar_ch</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsBlankQ" id="xmlIsBlankQ"></a>Macro: xmlIsBlankQ</h3><pre>#define xmlIsBlankQ</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsBlank_ch" id="xmlIsBlank_ch"></a>Macro: xmlIsBlank_ch</h3><pre>#define xmlIsBlank_ch</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsCharQ" id="xmlIsCharQ"></a>Macro: xmlIsCharQ</h3><pre>#define xmlIsCharQ</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsChar_ch" id="xmlIsChar_ch"></a>Macro: xmlIsChar_ch</h3><pre>#define xmlIsChar_ch</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsCombiningQ" id="xmlIsCombiningQ"></a>Macro: xmlIsCombiningQ</h3><pre>#define xmlIsCombiningQ</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsDigitQ" id="xmlIsDigitQ"></a>Macro: xmlIsDigitQ</h3><pre>#define xmlIsDigitQ</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsDigit_ch" id="xmlIsDigit_ch"></a>Macro: xmlIsDigit_ch</h3><pre>#define xmlIsDigit_ch</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsExtenderQ" id="xmlIsExtenderQ"></a>Macro: xmlIsExtenderQ</h3><pre>#define xmlIsExtenderQ</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsExtender_ch" id="xmlIsExtender_ch"></a>Macro: xmlIsExtender_ch</h3><pre>#define xmlIsExtender_ch</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsIdeographicQ" id="xmlIsIdeographicQ"></a>Macro: xmlIsIdeographicQ</h3><pre>#define xmlIsIdeographicQ</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsPubidCharQ" id="xmlIsPubidCharQ"></a>Macro: xmlIsPubidCharQ</h3><pre>#define xmlIsPubidCharQ</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlIsPubidChar_ch" id="xmlIsPubidChar_ch"></a>Macro: xmlIsPubidChar_ch</h3><pre>#define xmlIsPubidChar_ch</pre><p>Automatically generated by genChRanges.py</p>
<h3><a name="xmlChLRange" id="xmlChLRange">Structure xmlChLRange</a></h3><pre class="programlisting">Structure xmlChLRange<br />struct _xmlChLRange {
    unsigned int	low
    unsigned int	high
}</pre><h3><a name="xmlChRangeGroup" id="xmlChRangeGroup">Structure xmlChRangeGroup</a></h3><pre class="programlisting">Structure xmlChRangeGroup<br />struct _xmlChRangeGroup {
    int	nbShortRange
    int	nbLongRange
    const <a href="libxml-chvalid.html#xmlChSRange">xmlChSRange</a> *	shortRange	: points to an array of ranges
    const <a href="libxml-chvalid.html#xmlChLRange">xmlChLRange</a> *	longRange
}</pre><h3><a name="xmlChSRange" id="xmlChSRange">Structure xmlChSRange</a></h3><pre class="programlisting">Structure xmlChSRange<br />struct _xmlChSRange {
    unsigned short	low
    unsigned short	high
}</pre><h3><a name="xmlCharInRange" id="xmlCharInRange"></a>Function: xmlCharInRange</h3><pre class="programlisting">int	xmlCharInRange			(unsigned int val, <br />					 const <a href="libxml-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> * rptr)<br />
</pre><p>Does a binary search of the range table to determine if char is valid</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>character to be validated</td></tr><tr><td><span class="term"><i><tt>rptr</tt></i>:</span></td><td>pointer to range to be used to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if character valid, false otherwise</td></tr></tbody></table></div><h3><a name="xmlIsBaseChar" id="xmlIsBaseChar"></a>Function: xmlIsBaseChar</h3><pre class="programlisting">int	xmlIsBaseChar			(unsigned int ch)<br />
</pre><p>This function is DEPRECATED. Use <a href="libxml-chvalid.html#xmlIsBaseChar_ch">xmlIsBaseChar_ch</a> or <a href="libxml-chvalid.html#xmlIsBaseCharQ">xmlIsBaseCharQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div><h3><a name="xmlIsBlank" id="xmlIsBlank"></a>Function: xmlIsBlank</h3><pre class="programlisting">int	xmlIsBlank			(unsigned int ch)<br />
</pre><p>This function is DEPRECATED. Use <a href="libxml-chvalid.html#xmlIsBlank_ch">xmlIsBlank_ch</a> or <a href="libxml-chvalid.html#xmlIsBlankQ">xmlIsBlankQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div><h3><a name="xmlIsChar" id="xmlIsChar"></a>Function: xmlIsChar</h3><pre class="programlisting">int	xmlIsChar			(unsigned int ch)<br />
</pre><p>This function is DEPRECATED. Use <a href="libxml-chvalid.html#xmlIsChar_ch">xmlIsChar_ch</a> or <a href="libxml-chvalid.html#xmlIsCharQ">xmlIsCharQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div><h3><a name="xmlIsCombining" id="xmlIsCombining"></a>Function: xmlIsCombining</h3><pre class="programlisting">int	xmlIsCombining			(unsigned int ch)<br />
</pre><p>This function is DEPRECATED. Use <a href="libxml-chvalid.html#xmlIsCombiningQ">xmlIsCombiningQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div><h3><a name="xmlIsDigit" id="xmlIsDigit"></a>Function: xmlIsDigit</h3><pre class="programlisting">int	xmlIsDigit			(unsigned int ch)<br />
</pre><p>This function is DEPRECATED. Use <a href="libxml-chvalid.html#xmlIsDigit_ch">xmlIsDigit_ch</a> or <a href="libxml-chvalid.html#xmlIsDigitQ">xmlIsDigitQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div><h3><a name="xmlIsExtender" id="xmlIsExtender"></a>Function: xmlIsExtender</h3><pre class="programlisting">int	xmlIsExtender			(unsigned int ch)<br />
</pre><p>This function is DEPRECATED. Use <a href="libxml-chvalid.html#xmlIsExtender_ch">xmlIsExtender_ch</a> or <a href="libxml-chvalid.html#xmlIsExtenderQ">xmlIsExtenderQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div><h3><a name="xmlIsIdeographic" id="xmlIsIdeographic"></a>Function: xmlIsIdeographic</h3><pre class="programlisting">int	xmlIsIdeographic		(unsigned int ch)<br />
</pre><p>This function is DEPRECATED. Use <a href="libxml-chvalid.html#xmlIsIdeographicQ">xmlIsIdeographicQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div><h3><a name="xmlIsPubidChar" id="xmlIsPubidChar"></a>Function: xmlIsPubidChar</h3><pre class="programlisting">int	xmlIsPubidChar			(unsigned int ch)<br />
</pre><p>This function is DEPRECATED. Use <a href="libxml-chvalid.html#xmlIsPubidChar_ch">xmlIsPubidChar_ch</a> or <a href="libxml-chvalid.html#xmlIsPubidCharQ">xmlIsPubidCharQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
