<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xmlmodule from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xmlmodule from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xmlmemory.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xmlmemory.html">xmlmemory</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-xmlreader.html">xmlreader</a></th><td><a accesskey="n" href="libxml-xmlreader.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>basic API for dynamic module loading, used by libexslt added in 2.6.17 </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlModule">xmlModule</a><br />struct _xmlModule
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Enum <a href="#xmlModuleOption">xmlModuleOption</a>
</pre><pre class="programlisting">Typedef <a href="libxml-xmlmodule.html#xmlModule">xmlModule</a> * <a name="xmlModulePtr" id="xmlModulePtr">xmlModulePtr</a>
</pre><pre class="programlisting">int	<a href="#xmlModuleClose">xmlModuleClose</a>			(<a href="libxml-xmlmodule.html#xmlModulePtr">xmlModulePtr</a> module)</pre>
<pre class="programlisting">int	<a href="#xmlModuleFree">xmlModuleFree</a>			(<a href="libxml-xmlmodule.html#xmlModulePtr">xmlModulePtr</a> module)</pre>
<pre class="programlisting"><a href="libxml-xmlmodule.html#xmlModulePtr">xmlModulePtr</a>	<a href="#xmlModuleOpen">xmlModuleOpen</a>		(const char * name, <br />					 int options)</pre>
<pre class="programlisting">int	<a href="#xmlModuleSymbol">xmlModuleSymbol</a>			(<a href="libxml-xmlmodule.html#xmlModulePtr">xmlModulePtr</a> module, <br />					 const char * name, <br />					 void ** symbol)</pre>
<h2>Description</h2>
<h3><a name="xmlModule" id="xmlModule">Structure xmlModule</a></h3><pre class="programlisting">Structure xmlModule<br />struct _xmlModule {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlModuleOption" id="xmlModuleOption">xmlModuleOption</a></h3><pre class="programlisting">Enum xmlModuleOption {
    <a name="XML_MODULE_LAZY" id="XML_MODULE_LAZY">XML_MODULE_LAZY</a> = 1 : lazy binding
    <a name="XML_MODULE_LOCAL" id="XML_MODULE_LOCAL">XML_MODULE_LOCAL</a> = 2 : local binding
}
</pre>
      A handle to a dynamically loaded module
    <h3><a name="xmlModuleClose" id="xmlModuleClose"></a>Function: xmlModuleClose</h3><pre class="programlisting">int	xmlModuleClose			(<a href="libxml-xmlmodule.html#xmlModulePtr">xmlModulePtr</a> module)<br />
</pre><p>The close operations unload the associated module and free the data associated to the module.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>module</tt></i>:</span></td><td>the module handle</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of argument error and -2 if the module could not be closed/unloaded.</td></tr></tbody></table></div><h3><a name="xmlModuleFree" id="xmlModuleFree"></a>Function: xmlModuleFree</h3><pre class="programlisting">int	xmlModuleFree			(<a href="libxml-xmlmodule.html#xmlModulePtr">xmlModulePtr</a> module)<br />
</pre><p>The free operations free the data associated to the module but does not unload the associated shared library which may still be in use.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>module</tt></i>:</span></td><td>the module handle</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of argument error</td></tr></tbody></table></div><h3><a name="xmlModuleOpen" id="xmlModuleOpen"></a>Function: xmlModuleOpen</h3><pre class="programlisting"><a href="libxml-xmlmodule.html#xmlModulePtr">xmlModulePtr</a>	xmlModuleOpen		(const char * name, <br />					 int options)<br />
</pre><p>Opens a module/shared library given its name or path NOTE: that due to portability issues, behaviour can only be guaranteed with @name using ASCII. We canot guarantee that an UTF-8 string would work, which is why name is a const char * and not a const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * . TODO: options are not yet implemented.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the module name</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of <a href="libxml-xmlmodule.html#xmlModuleOption">xmlModuleOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a handle for the module or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlModuleSymbol" id="xmlModuleSymbol"></a>Function: xmlModuleSymbol</h3><pre class="programlisting">int	xmlModuleSymbol			(<a href="libxml-xmlmodule.html#xmlModulePtr">xmlModulePtr</a> module, <br />					 const char * name, <br />					 void ** symbol)<br />
</pre><p>Lookup for a symbol address in the given module NOTE: that due to portability issues, behaviour can only be guaranteed with @name using ASCII. We canot guarantee that an UTF-8 string would work, which is why name is a const char * and not a const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * .</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>module</tt></i>:</span></td><td>the module</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the symbol</td></tr><tr><td><span class="term"><i><tt>symbol</tt></i>:</span></td><td>the resulting symbol address</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if the symbol was found, or -1 in case of error</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
