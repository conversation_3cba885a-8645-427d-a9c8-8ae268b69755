<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module list from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module list from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-hash.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-hash.html">hash</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-nanoftp.html">nanoftp</a></th><td><a accesskey="n" href="libxml-nanoftp.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>this module implement the list support used in various place in the library. </p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlLink">xmlLink</a><br />struct _xmlLink
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-list.html#xmlLink">xmlLink</a> * <a name="xmlLinkPtr" id="xmlLinkPtr">xmlLinkPtr</a>
</pre><pre class="programlisting">Structure <a href="#xmlList">xmlList</a><br />struct _xmlList
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Typedef <a href="libxml-list.html#xmlList">xmlList</a> * <a name="xmlListPtr" id="xmlListPtr">xmlListPtr</a>
</pre><pre class="programlisting">void *	<a href="#xmlLinkGetData">xmlLinkGetData</a>			(<a href="libxml-list.html#xmlLinkPtr">xmlLinkPtr</a> lk)</pre>
<pre class="programlisting">int	<a href="#xmlListAppend">xmlListAppend</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">void	<a href="#xmlListClear">xmlListClear</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting">int	<a href="#xmlListCopy">xmlListCopy</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> cur, <br />					 const <a href="libxml-list.html#xmlListPtr">xmlListPtr</a> old)</pre>
<pre class="programlisting"><a href="libxml-list.html#xmlListPtr">xmlListPtr</a>	<a href="#xmlListCreate">xmlListCreate</a>		(<a href="libxml-list.html#xmlListDeallocator">xmlListDeallocator</a> deallocator, <br />					 <a href="libxml-list.html#xmlListDataCompare">xmlListDataCompare</a> compare)</pre>
<pre class="programlisting">Function type: <a href="#xmlListDataCompare">xmlListDataCompare</a>
int	<a href="#xmlListDataCompare">xmlListDataCompare</a>		(const void * data0, <br />					 const void * data1)
</pre>
<pre class="programlisting">Function type: <a href="#xmlListDeallocator">xmlListDeallocator</a>
void	<a href="#xmlListDeallocator">xmlListDeallocator</a>		(<a href="libxml-list.html#xmlLinkPtr">xmlLinkPtr</a> lk)
</pre>
<pre class="programlisting">void	<a href="#xmlListDelete">xmlListDelete</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting"><a href="libxml-list.html#xmlListPtr">xmlListPtr</a>	<a href="#xmlListDup">xmlListDup</a>		(const <a href="libxml-list.html#xmlListPtr">xmlListPtr</a> old)</pre>
<pre class="programlisting">int	<a href="#xmlListEmpty">xmlListEmpty</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting"><a href="libxml-list.html#xmlLinkPtr">xmlLinkPtr</a>	<a href="#xmlListEnd">xmlListEnd</a>		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting"><a href="libxml-list.html#xmlLinkPtr">xmlLinkPtr</a>	<a href="#xmlListFront">xmlListFront</a>		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting">int	<a href="#xmlListInsert">xmlListInsert</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">void	<a href="#xmlListMerge">xmlListMerge</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l1, <br />					 <a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l2)</pre>
<pre class="programlisting">void	<a href="#xmlListPopBack">xmlListPopBack</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting">void	<a href="#xmlListPopFront">xmlListPopFront</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting">int	<a href="#xmlListPushBack">xmlListPushBack</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlListPushFront">xmlListPushFront</a>		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlListRemoveAll">xmlListRemoveAll</a>		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlListRemoveFirst">xmlListRemoveFirst</a>		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlListRemoveLast">xmlListRemoveLast</a>		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">void	<a href="#xmlListReverse">xmlListReverse</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting">void *	<a href="#xmlListReverseSearch">xmlListReverseSearch</a>		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">void	<a href="#xmlListReverseWalk">xmlListReverseWalk</a>		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 <a href="libxml-list.html#xmlListWalker">xmlListWalker</a> walker, <br />					 const void * user)</pre>
<pre class="programlisting">void *	<a href="#xmlListSearch">xmlListSearch</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)</pre>
<pre class="programlisting">int	<a href="#xmlListSize">xmlListSize</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting">void	<a href="#xmlListSort">xmlListSort</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)</pre>
<pre class="programlisting">void	<a href="#xmlListWalk">xmlListWalk</a>			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 <a href="libxml-list.html#xmlListWalker">xmlListWalker</a> walker, <br />					 const void * user)</pre>
<pre class="programlisting">Function type: <a href="#xmlListWalker">xmlListWalker</a>
int	<a href="#xmlListWalker">xmlListWalker</a>			(const void * data, <br />					 const void * user)
</pre>
<h2>Description</h2>
<h3><a name="xmlLink" id="xmlLink">Structure xmlLink</a></h3><pre class="programlisting">Structure xmlLink<br />struct _xmlLink {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlList" id="xmlList">Structure xmlList</a></h3><pre class="programlisting">Structure xmlList<br />struct _xmlList {
The content of this structure is not made public by the API.
}</pre><h3><a name="xmlLinkGetData" id="xmlLinkGetData"></a>Function: xmlLinkGetData</h3><pre class="programlisting">void *	xmlLinkGetData			(<a href="libxml-list.html#xmlLinkPtr">xmlLinkPtr</a> lk)<br />
</pre><p>See Returns.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>lk</tt></i>:</span></td><td>a link</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the data referenced from this link</td></tr></tbody></table></div><h3><a name="xmlListAppend" id="xmlListAppend"></a>Function: xmlListAppend</h3><pre class="programlisting">int	xmlListAppend			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>Insert data in the ordered list at the end for this value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, 1 in case of failure</td></tr></tbody></table></div><h3><a name="xmlListClear" id="xmlListClear"></a>Function: xmlListClear</h3><pre class="programlisting">void	xmlListClear			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Remove the all data in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr></tbody></table></div><h3><a name="xmlListCopy" id="xmlListCopy"></a>Function: xmlListCopy</h3><pre class="programlisting">int	xmlListCopy			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> cur, <br />					 const <a href="libxml-list.html#xmlListPtr">xmlListPtr</a> old)<br />
</pre><p>Move all the element from the old list in the new list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the new list</td></tr><tr><td><span class="term"><i><tt>old</tt></i>:</span></td><td>the old list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success 1 in case of error</td></tr></tbody></table></div><h3><a name="xmlListCreate" id="xmlListCreate"></a>Function: xmlListCreate</h3><pre class="programlisting"><a href="libxml-list.html#xmlListPtr">xmlListPtr</a>	xmlListCreate		(<a href="libxml-list.html#xmlListDeallocator">xmlListDeallocator</a> deallocator, <br />					 <a href="libxml-list.html#xmlListDataCompare">xmlListDataCompare</a> compare)<br />
</pre><p>Create a new list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>deallocator</tt></i>:</span></td><td>an optional deallocator function</td></tr><tr><td><span class="term"><i><tt>compare</tt></i>:</span></td><td>an optional comparison function</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new list or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlListDataCompare" id="xmlListDataCompare"></a>Function type: xmlListDataCompare</h3><pre class="programlisting">Function type: xmlListDataCompare
int	xmlListDataCompare		(const void * data0, <br />					 const void * data1)
</pre><p>Callback function used to compare 2 data.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>data0</tt></i>:</span></td><td>the first data</td></tr><tr><td><span class="term"><i><tt>data1</tt></i>:</span></td><td>the second data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 is equality, -1 or 1 otherwise depending on the ordering.</td></tr></tbody></table></div><br />
<h3><a name="xmlListDeallocator" id="xmlListDeallocator"></a>Function type: xmlListDeallocator</h3><pre class="programlisting">Function type: xmlListDeallocator
void	xmlListDeallocator		(<a href="libxml-list.html#xmlLinkPtr">xmlLinkPtr</a> lk)
</pre><p>Callback function used to free data from a list.</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>lk</tt></i>:</span></td><td>the data to deallocate</td></tr></tbody></table></div><br />
<h3><a name="xmlListDelete" id="xmlListDelete"></a>Function: xmlListDelete</h3><pre class="programlisting">void	xmlListDelete			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Deletes the list and its associated data</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr></tbody></table></div><h3><a name="xmlListDup" id="xmlListDup"></a>Function: xmlListDup</h3><pre class="programlisting"><a href="libxml-list.html#xmlListPtr">xmlListPtr</a>	xmlListDup		(const <a href="libxml-list.html#xmlListPtr">xmlListPtr</a> old)<br />
</pre><p>Duplicate the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>old</tt></i>:</span></td><td>the list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new copy of the list or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlListEmpty" id="xmlListEmpty"></a>Function: xmlListEmpty</h3><pre class="programlisting">int	xmlListEmpty			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Is the list empty ?</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the list is empty, 0 if not empty and -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlListEnd" id="xmlListEnd"></a>Function: xmlListEnd</h3><pre class="programlisting"><a href="libxml-list.html#xmlLinkPtr">xmlLinkPtr</a>	xmlListEnd		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Get the last element in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the last element in the list, or NULL</td></tr></tbody></table></div><h3><a name="xmlListFront" id="xmlListFront"></a>Function: xmlListFront</h3><pre class="programlisting"><a href="libxml-list.html#xmlLinkPtr">xmlLinkPtr</a>	xmlListFront		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Get the first element in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the first element in the list, or NULL</td></tr></tbody></table></div><h3><a name="xmlListInsert" id="xmlListInsert"></a>Function: xmlListInsert</h3><pre class="programlisting">int	xmlListInsert			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>Insert data in the ordered list at the beginning for this value</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, 1 in case of failure</td></tr></tbody></table></div><h3><a name="xmlListMerge" id="xmlListMerge"></a>Function: xmlListMerge</h3><pre class="programlisting">void	xmlListMerge			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l1, <br />					 <a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l2)<br />
</pre><p>include all the elements of the second list in the first one and clear the second list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l1</tt></i>:</span></td><td>the original list</td></tr><tr><td><span class="term"><i><tt>l2</tt></i>:</span></td><td>the new list</td></tr></tbody></table></div><h3><a name="xmlListPopBack" id="xmlListPopBack"></a>Function: xmlListPopBack</h3><pre class="programlisting">void	xmlListPopBack			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Removes the last element in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr></tbody></table></div><h3><a name="xmlListPopFront" id="xmlListPopFront"></a>Function: xmlListPopFront</h3><pre class="programlisting">void	xmlListPopFront			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Removes the first element in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr></tbody></table></div><h3><a name="xmlListPushBack" id="xmlListPushBack"></a>Function: xmlListPushBack</h3><pre class="programlisting">int	xmlListPushBack			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>add the new data at the end of the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>new data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if successful, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlListPushFront" id="xmlListPushFront"></a>Function: xmlListPushFront</h3><pre class="programlisting">int	xmlListPushFront		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>add the new data at the beginning of the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>new data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if successful, 0 otherwise</td></tr></tbody></table></div><h3><a name="xmlListRemoveAll" id="xmlListRemoveAll"></a>Function: xmlListRemoveAll</h3><pre class="programlisting">int	xmlListRemoveAll		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>Remove the all instance associated to data in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>list data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of deallocation, or 0 if not found</td></tr></tbody></table></div><h3><a name="xmlListRemoveFirst" id="xmlListRemoveFirst"></a>Function: xmlListRemoveFirst</h3><pre class="programlisting">int	xmlListRemoveFirst		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>Remove the first instance associated to data in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>list data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if a deallocation occured, or 0 if not found</td></tr></tbody></table></div><h3><a name="xmlListRemoveLast" id="xmlListRemoveLast"></a>Function: xmlListRemoveLast</h3><pre class="programlisting">int	xmlListRemoveLast		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>Remove the last instance associated to data in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>list data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if a deallocation occured, or 0 if not found</td></tr></tbody></table></div><h3><a name="xmlListReverse" id="xmlListReverse"></a>Function: xmlListReverse</h3><pre class="programlisting">void	xmlListReverse			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Reverse the order of the elements in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr></tbody></table></div><h3><a name="xmlListReverseSearch" id="xmlListReverseSearch"></a>Function: xmlListReverseSearch</h3><pre class="programlisting">void *	xmlListReverseSearch		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>Search the list in reverse order for an existing value of @data</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>a search value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the value associated to @data or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlListReverseWalk" id="xmlListReverseWalk"></a>Function: xmlListReverseWalk</h3><pre class="programlisting">void	xmlListReverseWalk		(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 <a href="libxml-list.html#xmlListWalker">xmlListWalker</a> walker, <br />					 const void * user)<br />
</pre><p>Walk all the element of the list in reverse order and apply the walker function to it</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>walker</tt></i>:</span></td><td>a processing function</td></tr><tr><td><span class="term"><i><tt>user</tt></i>:</span></td><td>a user parameter passed to the walker function</td></tr></tbody></table></div><h3><a name="xmlListSearch" id="xmlListSearch"></a>Function: xmlListSearch</h3><pre class="programlisting">void *	xmlListSearch			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 void * data)<br />
</pre><p>Search the list for an existing value of @data</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>a search value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the value associated to @data or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlListSize" id="xmlListSize"></a>Function: xmlListSize</h3><pre class="programlisting">int	xmlListSize			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Get the number of elements in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of elements in the list or -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlListSort" id="xmlListSort"></a>Function: xmlListSort</h3><pre class="programlisting">void	xmlListSort			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l)<br />
</pre><p>Sort all the elements in the list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr></tbody></table></div><h3><a name="xmlListWalk" id="xmlListWalk"></a>Function: xmlListWalk</h3><pre class="programlisting">void	xmlListWalk			(<a href="libxml-list.html#xmlListPtr">xmlListPtr</a> l, <br />					 <a href="libxml-list.html#xmlListWalker">xmlListWalker</a> walker, <br />					 const void * user)<br />
</pre><p>Walk all the element of the first from first to last and apply the walker function to it</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>l</tt></i>:</span></td><td>a list</td></tr><tr><td><span class="term"><i><tt>walker</tt></i>:</span></td><td>a processing function</td></tr><tr><td><span class="term"><i><tt>user</tt></i>:</span></td><td>a user parameter passed to the walker function</td></tr></tbody></table></div><h3><a name="xmlListWalker" id="xmlListWalker"></a>Function type: xmlListWalker</h3><pre class="programlisting">Function type: xmlListWalker
int	xmlListWalker			(const void * data, <br />					 const void * user)
</pre><p>Callback function used when walking a list with xmlListWalk().</p><div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the data found in the list</td></tr><tr><td><span class="term"><i><tt>user</tt></i>:</span></td><td>extra user provided data to the walker</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 to stop walking the list, 1 otherwise.</td></tr></tbody></table></div><br />
<p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
