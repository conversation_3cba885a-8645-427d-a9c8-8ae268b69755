<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module xpointer from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module xpointer from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-xpathInternals.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-xpathInternals.html">xpathInternals</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th></tr></table><p>API to handle XML Pointers Base implementation was made accordingly to W3C Candidate Recommendation 7 June 2000</p><h2>Table of Contents</h2><pre class="programlisting">Structure <a href="#xmlLocationSet">xmlLocationSet</a><br />struct _xmlLocationSet
</pre><pre class="programlisting">Typedef <a href="libxml-xpointer.html#xmlLocationSet">xmlLocationSet</a> * <a name="xmlLocationSetPtr" id="xmlLocationSetPtr">xmlLocationSetPtr</a>
</pre><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPtrBuildNodeList">xmlXPtrBuildNodeList</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> obj)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrEval">xmlXPtrEval</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)</pre>
<pre class="programlisting">void	<a href="#xmlXPtrEvalRangePredicate">xmlXPtrEvalRangePredicate</a>	(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)</pre>
<pre class="programlisting">void	<a href="#xmlXPtrFreeLocationSet">xmlXPtrFreeLocationSet</a>		(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> obj)</pre>
<pre class="programlisting">void	<a href="#xmlXPtrLocationSetAdd">xmlXPtrLocationSetAdd</a>		(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> cur, <br />					 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting"><a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a>	<a href="#xmlXPtrLocationSetCreate">xmlXPtrLocationSetCreate</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting">void	<a href="#xmlXPtrLocationSetDel">xmlXPtrLocationSetDel</a>		(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> cur, <br />					 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)</pre>
<pre class="programlisting"><a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a>	<a href="#xmlXPtrLocationSetMerge">xmlXPtrLocationSetMerge</a>	(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> val1, <br />						 <a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> val2)</pre>
<pre class="programlisting">void	<a href="#xmlXPtrLocationSetRemove">xmlXPtrLocationSetRemove</a>	(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> cur, <br />					 int val)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewCollapsedRange">xmlXPtrNewCollapsedRange</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a>	<a href="#xmlXPtrNewContext">xmlXPtrNewContext</a>	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> here, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> origin)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewLocationSetNodeSet">xmlXPtrNewLocationSetNodeSet</a>	(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> set)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewLocationSetNodes">xmlXPtrNewLocationSetNodes</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />							 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> end)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewRange">xmlXPtrNewRange</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />					 int startindex, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> end, <br />					 int endindex)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewRangeNodeObject">xmlXPtrNewRangeNodeObject</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />							 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> end)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewRangeNodePoint">xmlXPtrNewRangeNodePoint</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />							 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> end)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewRangeNodes">xmlXPtrNewRangeNodes</a>	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> end)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewRangePointNode">xmlXPtrNewRangePointNode</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> start, <br />							 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> end)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrNewRangePoints">xmlXPtrNewRangePoints</a>	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> start, <br />						 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> end)</pre>
<pre class="programlisting">void	<a href="#xmlXPtrRangeToFunction">xmlXPtrRangeToFunction</a>		(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br />					 int nargs)</pre>
<pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPtrWrapLocationSet">xmlXPtrWrapLocationSet</a>	(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> val)</pre>
<h2>Description</h2>
<h3><a name="xmlLocationSet" id="xmlLocationSet">Structure xmlLocationSet</a></h3><pre class="programlisting">Structure xmlLocationSet<br />struct _xmlLocationSet {
    int	locNr	: number of locations in the set
    int	locMax	: size of the array as allocated
    <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> *	locTab	: array of locations
}</pre><h3><a name="xmlXPtrBuildNodeList" id="xmlXPtrBuildNodeList"></a>Function: xmlXPtrBuildNodeList</h3><pre class="programlisting"><a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPtrBuildNodeList	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> obj)<br />
</pre><p>Build a node list tree copy of the XPointer result. This will drop Attributes and Namespace declarations.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>obj</tt></i>:</span></td><td>the XPointer result from the evaluation.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> list or NULL. the caller has to free the node tree.</td></tr></tbody></table></div><h3><a name="xmlXPtrEval" id="xmlXPtrEval"></a>Function: xmlXPtrEval</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrEval	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * str, <br />					 <a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctx)<br />
</pre><p>Evaluate the XPath Location Path in the given context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the XPointer expression</td></tr><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the XPointer context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> resulting from the evaluation or NULL. the caller has to free the object.</td></tr></tbody></table></div><h3><a name="xmlXPtrEvalRangePredicate" id="xmlXPtrEvalRangePredicate"></a>Function: xmlXPtrEvalRangePredicate</h3><pre class="programlisting">void	xmlXPtrEvalRangePredicate	(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br />
</pre><p>[8] Predicate ::= '[' PredicateExpr ']' [9] PredicateExpr ::= Expr Evaluate a predicate as in xmlXPathEvalPredicate() but for a Location Set instead of a node set</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPointer Parser context</td></tr></tbody></table></div><h3><a name="xmlXPtrFreeLocationSet" id="xmlXPtrFreeLocationSet"></a>Function: xmlXPtrFreeLocationSet</h3><pre class="programlisting">void	xmlXPtrFreeLocationSet		(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> obj)<br />
</pre><p>Free the LocationSet compound (not the actual ranges !).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>obj</tt></i>:</span></td><td>the <a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> to free</td></tr></tbody></table></div><h3><a name="xmlXPtrLocationSetAdd" id="xmlXPtrLocationSetAdd"></a>Function: xmlXPtrLocationSetAdd</h3><pre class="programlisting">void	xmlXPtrLocationSetAdd		(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> cur, <br />					 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>add a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> to an existing LocationSet If the location already exist in the set @val is freed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the initial range set</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a></td></tr></tbody></table></div><h3><a name="xmlXPtrLocationSetCreate" id="xmlXPtrLocationSetCreate"></a>Function: xmlXPtrLocationSetCreate</h3><pre class="programlisting"><a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a>	xmlXPtrLocationSetCreate	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>Create a new <a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> of type double and of value @val</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an initial xmlXPathObjectPtr, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrLocationSetDel" id="xmlXPtrLocationSetDel"></a>Function: xmlXPtrLocationSetDel</h3><pre class="programlisting">void	xmlXPtrLocationSetDel		(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> cur, <br />					 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> val)<br />
</pre><p>Removes an <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> from an existing LocationSet</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the initial range set</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a></td></tr></tbody></table></div><h3><a name="xmlXPtrLocationSetMerge" id="xmlXPtrLocationSetMerge"></a>Function: xmlXPtrLocationSetMerge</h3><pre class="programlisting"><a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a>	xmlXPtrLocationSetMerge	(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> val1, <br />						 <a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> val2)<br />
</pre><p>Merges two rangesets, all ranges from @val2 are added to @val1</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val1</tt></i>:</span></td><td>the first LocationSet</td></tr><tr><td><span class="term"><i><tt>val2</tt></i>:</span></td><td>the second LocationSet</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>val1 once extended or NULL in case of error.</td></tr></tbody></table></div><h3><a name="xmlXPtrLocationSetRemove" id="xmlXPtrLocationSetRemove"></a>Function: xmlXPtrLocationSetRemove</h3><pre class="programlisting">void	xmlXPtrLocationSetRemove	(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> cur, <br />					 int val)<br />
</pre><p>Removes an entry from an existing LocationSet list.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the initial range set</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the index to remove</td></tr></tbody></table></div><h3><a name="xmlXPtrNewCollapsedRange" id="xmlXPtrNewCollapsedRange"></a>Function: xmlXPtrNewCollapsedRange</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewCollapsedRange	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type range using a single nodes</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the starting and ending node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewContext" id="xmlXPtrNewContext"></a>Function: xmlXPtrNewContext</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a>	xmlXPtrNewContext	(<a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> here, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> origin)<br />
</pre><p>Create a new XPointer context</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the XML document</td></tr><tr><td><span class="term"><i><tt>here</tt></i>:</span></td><td>the node that directly contains the XPointer being evaluated or NULL</td></tr><tr><td><span class="term"><i><tt>origin</tt></i>:</span></td><td>the element from which a user or program initiated traversal of the link, or NULL.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-xpath.html#xmlXPathContext">xmlXPathContext</a> just allocated.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewLocationSetNodeSet" id="xmlXPtrNewLocationSetNodeSet"></a>Function: xmlXPtrNewLocationSetNodeSet</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewLocationSetNodeSet	(<a href="libxml-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> set)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type LocationSet and initialize it with all the nodes from @set</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>set</tt></i>:</span></td><td>a node set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewLocationSetNodes" id="xmlXPtrNewLocationSetNodes"></a>Function: xmlXPtrNewLocationSetNodes</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewLocationSetNodes	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />							 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> end)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type LocationSet and initialize it with the single range made of the two nodes @start and @end</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the start NodePtr value</td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>the end NodePtr value or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewRange" id="xmlXPtrNewRange"></a>Function: xmlXPtrNewRange</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewRange	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />					 int startindex, <br />					 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> end, <br />					 int endindex)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type range</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the starting node</td></tr><tr><td><span class="term"><i><tt>startindex</tt></i>:</span></td><td>the start index</td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>the ending point</td></tr><tr><td><span class="term"><i><tt>endindex</tt></i>:</span></td><td>the ending index</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewRangeNodeObject" id="xmlXPtrNewRangeNodeObject"></a>Function: xmlXPtrNewRangeNodeObject</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewRangeNodeObject	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />							 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> end)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type range from a not to an object</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the starting node</td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>the ending object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewRangeNodePoint" id="xmlXPtrNewRangeNodePoint"></a>Function: xmlXPtrNewRangeNodePoint</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewRangeNodePoint	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />							 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> end)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type range from a node to a point</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the starting node</td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>the ending point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewRangeNodes" id="xmlXPtrNewRangeNodes"></a>Function: xmlXPtrNewRangeNodes</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewRangeNodes	(<a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> start, <br />						 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> end)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type range using 2 nodes</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the starting node</td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>the ending node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewRangePointNode" id="xmlXPtrNewRangePointNode"></a>Function: xmlXPtrNewRangePointNode</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewRangePointNode	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> start, <br />							 <a href="libxml-tree.html#xmlNodePtr">xmlNodePtr</a> end)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type range from a point to a node</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the starting point</td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>the ending node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrNewRangePoints" id="xmlXPtrNewRangePoints"></a>Function: xmlXPtrNewRangePoints</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrNewRangePoints	(<a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> start, <br />						 <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> end)<br />
</pre><p>Create a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type range using 2 Points</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the starting point</td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>the ending point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><h3><a name="xmlXPtrRangeToFunction" id="xmlXPtrRangeToFunction"></a>Function: xmlXPtrRangeToFunction</h3><pre class="programlisting">void	xmlXPtrRangeToFunction		(<a href="libxml-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br />					 int nargs)<br />
</pre><p>Implement the range-to() XPointer function</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPointer Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of args</td></tr></tbody></table></div><h3><a name="xmlXPtrWrapLocationSet" id="xmlXPtrWrapLocationSet"></a>Function: xmlXPtrWrapLocationSet</h3><pre class="programlisting"><a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPtrWrapLocationSet	(<a href="libxml-xpointer.html#xmlLocationSetPtr">xmlLocationSetPtr</a> val)<br />
</pre><p>Wrap the LocationSet @val in a new <a href="libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the LocationSet value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
