<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module DOCBparser from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module DOCBparser from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-HTMLparser.html">HTMLparser</a></th><td><a accesskey="n" href="libxml-HTMLparser.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><h2 style="font-weight:bold;color:red;text-align:center">This module is deprecated</h2><p>interface for a DocBook SGML non-verifying parser This code is DEPRECATED, and should not be used anymore. </p><div class="deprecated"><h2>Table of Contents</h2><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> <a name="docbParserInputPtr" id="docbParserInputPtr">docbParserInputPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlParserCtxt">xmlParserCtxt</a> <a name="docbParserCtxt" id="docbParserCtxt">docbParserCtxt</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> <a name="docbParserCtxtPtr" id="docbParserCtxtPtr">docbParserCtxtPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlParserInput">xmlParserInput</a> <a name="docbParserInput" id="docbParserInput">docbParserInput</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a> <a name="docbDocPtr" id="docbDocPtr">docbDocPtr</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> <a name="docbSAXHandler" id="docbSAXHandler">docbSAXHandler</a>
</pre><pre class="programlisting">Typedef <a href="libxml-tree.html#xmlSAXHandlerPtr">xmlSAXHandlerPtr</a> <a name="docbSAXHandlerPtr" id="docbSAXHandlerPtr">docbSAXHandlerPtr</a>
</pre><pre class="programlisting">void	<a href="#docbFreeParserCtxt">docbFreeParserCtxt</a>		(<a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting"><a href="libxml-DOCBparser.html#docbDocPtr">docbDocPtr</a>	<a href="#docbParseDoc">docbParseDoc</a>		(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * encoding)</pre>
<pre class="programlisting"><a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a>	<a href="#docbCreateFileParserCtxt">docbCreateFileParserCtxt</a>	(const char * filename, <br />							 const char * encoding)</pre>
<pre class="programlisting"><a href="libxml-DOCBparser.html#docbDocPtr">docbDocPtr</a>	<a href="#docbSAXParseFile">docbSAXParseFile</a>	(const char * filename, <br />					 const char * encoding, <br />					 <a href="libxml-DOCBparser.html#docbSAXHandlerPtr">docbSAXHandlerPtr</a> sax, <br />					 void * userData)</pre>
<pre class="programlisting"><a href="libxml-DOCBparser.html#docbDocPtr">docbDocPtr</a>	<a href="#docbSAXParseDoc">docbSAXParseDoc</a>		(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * encoding, <br />					 <a href="libxml-DOCBparser.html#docbSAXHandlerPtr">docbSAXHandlerPtr</a> sax, <br />					 void * userData)</pre>
<pre class="programlisting"><a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a>	<a href="#docbCreatePushParserCtxt">docbCreatePushParserCtxt</a>	(<a href="libxml-DOCBparser.html#docbSAXHandlerPtr">docbSAXHandlerPtr</a> sax, <br />							 void * user_data, <br />							 const char * chunk, <br />							 int size, <br />							 const char * filename, <br />							 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)</pre>
<pre class="programlisting">int	<a href="#docbEncodeEntities">docbEncodeEntities</a>		(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen, <br />					 int quoteChar)</pre>
<pre class="programlisting"><a href="libxml-DOCBparser.html#docbDocPtr">docbDocPtr</a>	<a href="#docbParseFile">docbParseFile</a>		(const char * filename, <br />					 const char * encoding)</pre>
<pre class="programlisting">int	<a href="#docbParseDocument">docbParseDocument</a>		(<a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a> ctxt)</pre>
<pre class="programlisting">int	<a href="#docbParseChunk">docbParseChunk</a>			(<a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a> ctxt, <br />					 const char * chunk, <br />					 int size, <br />					 int terminate)</pre>
<h2>Description</h2>
<h3><a name="docbFreeParserCtxt" id="docbFreeParserCtxt"></a>Function: docbFreeParserCtxt</h3><pre class="programlisting">void	docbFreeParserCtxt		(<a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a> ctxt)<br />
</pre><p>Free all the memory used by a parser context. However the parsed document in ctxt-&gt;myDoc is not freed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an SGML parser context</td></tr></tbody></table></div><h3><a name="docbParseDoc" id="docbParseDoc"></a>Function: docbParseDoc</h3><pre class="programlisting"><a href="libxml-DOCBparser.html#docbDocPtr">docbDocPtr</a>	docbParseDoc		(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * encoding)<br />
</pre><p>parse an SGML in-memory document and build a tree.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to an array of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>a free form C string describing the SGML document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="docbCreateFileParserCtxt" id="docbCreateFileParserCtxt"></a>Function: docbCreateFileParserCtxt</h3><pre class="programlisting"><a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a>	docbCreateFileParserCtxt	(const char * filename, <br />							 const char * encoding)<br />
</pre><p>Create a parser context for a file content. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the SGML document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div><h3><a name="docbSAXParseFile" id="docbSAXParseFile"></a>Function: docbSAXParseFile</h3><pre class="programlisting"><a href="libxml-DOCBparser.html#docbDocPtr">docbDocPtr</a>	docbSAXParseFile	(const char * filename, <br />					 const char * encoding, <br />					 <a href="libxml-DOCBparser.html#docbSAXHandlerPtr">docbSAXHandlerPtr</a> sax, <br />					 void * userData)<br />
</pre><p>parse an SGML file and build a tree. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time. It use the given SAX function block to handle the parsing callback. If sax is NULL, fallback to the default DOM tree building routines.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>a free form C string describing the SGML document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>userData</tt></i>:</span></td><td>if using SAX, this pointer will be provided on callbacks.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="docbSAXParseDoc" id="docbSAXParseDoc"></a>Function: docbSAXParseDoc</h3><pre class="programlisting"><a href="libxml-DOCBparser.html#docbDocPtr">docbDocPtr</a>	docbSAXParseDoc		(<a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * cur, <br />					 const char * encoding, <br />					 <a href="libxml-DOCBparser.html#docbSAXHandlerPtr">docbSAXHandlerPtr</a> sax, <br />					 void * userData)<br />
</pre><p>parse an SGML in-memory document and build a tree. It use the given SAX function block to handle the parsing callback. If sax is NULL, fallback to the default DOM tree building routines.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>a pointer to an array of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>a free form C string describing the SGML document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>the SAX handler block</td></tr><tr><td><span class="term"><i><tt>userData</tt></i>:</span></td><td>if using SAX, this pointer will be provided on callbacks.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="docbCreatePushParserCtxt" id="docbCreatePushParserCtxt"></a>Function: docbCreatePushParserCtxt</h3><pre class="programlisting"><a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a>	docbCreatePushParserCtxt	(<a href="libxml-DOCBparser.html#docbSAXHandlerPtr">docbSAXHandlerPtr</a> sax, <br />							 void * user_data, <br />							 const char * chunk, <br />							 int size, <br />							 const char * filename, <br />							 <a href="libxml-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br />
</pre><p>Create a parser context for using the DocBook SGML parser in push mode To allow content encoding detection, @size should be &gt;= 4 The value of @filename is used for fetching external entities and error/warning reports.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sax</tt></i>:</span></td><td>a SAX handler</td></tr><tr><td><span class="term"><i><tt>user_data</tt></i>:</span></td><td>The user data returned on SAX callbacks</td></tr><tr><td><span class="term"><i><tt>chunk</tt></i>:</span></td><td>a pointer to an array of chars</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>number of chars in the array</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>an optional file name or URI</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>an optional encoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div><h3><a name="docbEncodeEntities" id="docbEncodeEntities"></a>Function: docbEncodeEntities</h3><pre class="programlisting">int	docbEncodeEntities		(unsigned char * out, <br />					 int * outlen, <br />					 const unsigned char * in, <br />					 int * inlen, <br />					 int quoteChar)<br />
</pre><p>Take a block of UTF-8 chars in and try to convert it to an ASCII plus SGML entities block of chars out.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of UTF-8 chars</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>quoteChar</tt></i>:</span></td><td>the quote character to escape (' or ") or zero.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if success, -2 if the transcoding fails, or -1 otherwise The value of @inlen after return is the number of octets consumed as the return value is positive, else unpredictable. The value of @outlen after return is the number of octets consumed.</td></tr></tbody></table></div><h3><a name="docbParseFile" id="docbParseFile"></a>Function: docbParseFile</h3><pre class="programlisting"><a href="libxml-DOCBparser.html#docbDocPtr">docbDocPtr</a>	docbParseFile		(const char * filename, <br />					 const char * encoding)<br />
</pre><p>parse a Docbook SGML file and build a tree. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>a free form C string describing document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree</td></tr></tbody></table></div><h3><a name="docbParseDocument" id="docbParseDocument"></a>Function: docbParseDocument</h3><pre class="programlisting">int	docbParseDocument		(<a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a> ctxt)<br />
</pre><p>parse an SGML document (and build a tree if using the standard SAX interface).</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an SGML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0, -1 in case of error. the parser context is augmented as a result of the parsing.</td></tr></tbody></table></div><h3><a name="docbParseChunk" id="docbParseChunk"></a>Function: docbParseChunk</h3><pre class="programlisting">int	docbParseChunk			(<a href="libxml-DOCBparser.html#docbParserCtxtPtr">docbParserCtxtPtr</a> ctxt, <br />					 const char * chunk, <br />					 int size, <br />					 int terminate)<br />
</pre><p>Parse a Chunk of memory</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>chunk</tt></i>:</span></td><td>an char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size in byte of the chunk</td></tr><tr><td><span class="term"><i><tt>terminate</tt></i>:</span></td><td>last chunk indicator</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>zero if no error, the <a href="libxml-xmlerror.html#xmlParserErrors">xmlParserErrors</a> otherwise.</td></tr></tbody></table></div></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
