<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module SAX2 from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module SAX2 from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-SAX.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-SAX.html">SAX</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-c14n.html">c14n</a></th><td><a accesskey="n" href="libxml-c14n.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>those are the default SAX2 interfaces used by the library when building DOM tree. </p><h2>Table of Contents</h2><pre class="programlisting">void	<a href="#docbDefaultSAXHandlerInit">docbDefaultSAXHandlerInit</a>	(void)</pre>
<pre class="programlisting">void	<a href="#htmlDefaultSAXHandlerInit">htmlDefaultSAXHandlerInit</a>	(void)</pre>
<pre class="programlisting">void	<a href="#xmlDefaultSAXHandlerInit">xmlDefaultSAXHandlerInit</a>	(void)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2AttributeDecl">xmlSAX2AttributeDecl</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * elem, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 int type, <br />					 int def, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * defaultValue, <br />					 <a href="libxml-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> tree)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2CDataBlock">xmlSAX2CDataBlock</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int len)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2Characters">xmlSAX2Characters</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2Comment">xmlSAX2Comment</a>			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2ElementDecl">xmlSAX2ElementDecl</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 <a href="libxml-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> content)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2EndDocument">xmlSAX2EndDocument</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2EndElement">xmlSAX2EndElement</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2EndElementNs">xmlSAX2EndElementNs</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2EntityDecl">xmlSAX2EntityDecl</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2ExternalSubset">xmlSAX2ExternalSubset</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)</pre>
<pre class="programlisting">int	<a href="#xmlSAX2GetColumnNumber">xmlSAX2GetColumnNumber</a>		(void * ctx)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlSAX2GetEntity">xmlSAX2GetEntity</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">int	<a href="#xmlSAX2GetLineNumber">xmlSAX2GetLineNumber</a>		(void * ctx)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlSAX2GetParameterEntity">xmlSAX2GetParameterEntity</a>	(void * ctx, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlSAX2GetPublicId">xmlSAX2GetPublicId</a>	(void * ctx)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlSAX2GetSystemId">xmlSAX2GetSystemId</a>	(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlSAX2HasExternalSubset">xmlSAX2HasExternalSubset</a>	(void * ctx)</pre>
<pre class="programlisting">int	<a href="#xmlSAX2HasInternalSubset">xmlSAX2HasInternalSubset</a>	(void * ctx)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2IgnorableWhitespace">xmlSAX2IgnorableWhitespace</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2InitDefaultSAXHandler">xmlSAX2InitDefaultSAXHandler</a>	(<a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * hdlr, <br />					 int warning)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2InitDocbDefaultSAXHandler">xmlSAX2InitDocbDefaultSAXHandler</a>	(<a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * hdlr)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2InitHtmlDefaultSAXHandler">xmlSAX2InitHtmlDefaultSAXHandler</a>	(<a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * hdlr)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2InternalSubset">xmlSAX2InternalSubset</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)</pre>
<pre class="programlisting">int	<a href="#xmlSAX2IsStandalone">xmlSAX2IsStandalone</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2NotationDecl">xmlSAX2NotationDecl</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2ProcessingInstruction">xmlSAX2ProcessingInstruction</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * data)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2Reference">xmlSAX2Reference</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlSAX2ResolveEntity">xmlSAX2ResolveEntity</a>	(void * ctx, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2SetDocumentLocator">xmlSAX2SetDocumentLocator</a>	(void * ctx, <br />					 <a href="libxml-tree.html#xmlSAXLocatorPtr">xmlSAXLocatorPtr</a> loc)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2StartDocument">xmlSAX2StartDocument</a>		(void * ctx)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2StartElement">xmlSAX2StartElement</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** atts)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2StartElementNs">xmlSAX2StartElementNs</a>		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 int nb_namespaces, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** namespaces, <br />					 int nb_attributes, <br />					 int nb_defaulted, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** attributes)</pre>
<pre class="programlisting">void	<a href="#xmlSAX2UnparsedEntityDecl">xmlSAX2UnparsedEntityDecl</a>	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * notationName)</pre>
<pre class="programlisting">int	<a href="#xmlSAXDefaultVersion">xmlSAXDefaultVersion</a>		(int version)</pre>
<pre class="programlisting">int	<a href="#xmlSAXVersion">xmlSAXVersion</a>			(<a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * hdlr, <br />					 int version)</pre>
<h2>Description</h2>
<h3><a name="docbDefaultSAXHandlerInit" id="docbDefaultSAXHandlerInit"></a>Function: docbDefaultSAXHandlerInit</h3><pre class="programlisting">void	docbDefaultSAXHandlerInit	(void)<br />
</pre><p>Initialize the default SAX handler</p>
<h3><a name="htmlDefaultSAXHandlerInit" id="htmlDefaultSAXHandlerInit"></a>Function: htmlDefaultSAXHandlerInit</h3><pre class="programlisting">void	htmlDefaultSAXHandlerInit	(void)<br />
</pre><p>Initialize the default SAX handler</p>
<h3><a name="xmlDefaultSAXHandlerInit" id="xmlDefaultSAXHandlerInit"></a>Function: xmlDefaultSAXHandlerInit</h3><pre class="programlisting">void	xmlDefaultSAXHandlerInit	(void)<br />
</pre><p>Initialize the default SAX2 handler</p>
<h3><a name="xmlSAX2AttributeDecl" id="xmlSAX2AttributeDecl"></a>Function: xmlSAX2AttributeDecl</h3><pre class="programlisting">void	xmlSAX2AttributeDecl		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * elem, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 int type, <br />					 int def, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * defaultValue, <br />					 <a href="libxml-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> tree)<br />
</pre><p>An <a href="libxml-SAX.html#attribute">attribute</a> definition has been parsed</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>the name of the element</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> type</td></tr><tr><td><span class="term"><i><tt>def</tt></i>:</span></td><td>the type of default value</td></tr><tr><td><span class="term"><i><tt>defaultValue</tt></i>:</span></td><td>the <a href="libxml-SAX.html#attribute">attribute</a> default value</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>the tree of enumerated value set</td></tr></tbody></table></div><h3><a name="xmlSAX2CDataBlock" id="xmlSAX2CDataBlock"></a>Function: xmlSAX2CDataBlock</h3><pre class="programlisting">void	xmlSAX2CDataBlock		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value, <br />					 int len)<br />
</pre><p>called when a pcdata block has been parsed</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>The pcdata content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the block length</td></tr></tbody></table></div><h3><a name="xmlSAX2Characters" id="xmlSAX2Characters"></a>Function: xmlSAX2Characters</h3><pre class="programlisting">void	xmlSAX2Characters		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)<br />
</pre><p>receiving some chars from the parser.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr></tbody></table></div><h3><a name="xmlSAX2Comment" id="xmlSAX2Comment"></a>Function: xmlSAX2Comment</h3><pre class="programlisting">void	xmlSAX2Comment			(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>A <a href="libxml-SAX2.html#xmlSAX2Comment">xmlSAX2Comment</a> has been parsed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the <a href="libxml-SAX2.html#xmlSAX2Comment">xmlSAX2Comment</a> content</td></tr></tbody></table></div><h3><a name="xmlSAX2ElementDecl" id="xmlSAX2ElementDecl"></a>Function: xmlSAX2ElementDecl</h3><pre class="programlisting">void	xmlSAX2ElementDecl		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 <a href="libxml-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> content)<br />
</pre><p>An element definition has been parsed</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the element name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the element type</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the element value tree</td></tr></tbody></table></div><h3><a name="xmlSAX2EndDocument" id="xmlSAX2EndDocument"></a>Function: xmlSAX2EndDocument</h3><pre class="programlisting">void	xmlSAX2EndDocument		(void * ctx)<br />
</pre><p>called when the document end has been detected.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr></tbody></table></div><h3><a name="xmlSAX2EndElement" id="xmlSAX2EndElement"></a>Function: xmlSAX2EndElement</h3><pre class="programlisting">void	xmlSAX2EndElement		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>called when the end of an element has been detected.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The element name</td></tr></tbody></table></div><h3><a name="xmlSAX2EndElementNs" id="xmlSAX2EndElementNs"></a>Function: xmlSAX2EndElementNs</h3><pre class="programlisting">void	xmlSAX2EndElementNs		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)<br />
</pre><p>SAX2 callback when an element end has been detected by the parser. It provides the namespace informations for the element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>localname</tt></i>:</span></td><td>the local name of the element</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the element namespace prefix if available</td></tr><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the element namespace name if available</td></tr></tbody></table></div><h3><a name="xmlSAX2EntityDecl" id="xmlSAX2EntityDecl"></a>Function: xmlSAX2EntityDecl</h3><pre class="programlisting">void	xmlSAX2EntityDecl		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 int type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * content)<br />
</pre><p>An entity definition has been parsed</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the entity type</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the entity value (without processing).</td></tr></tbody></table></div><h3><a name="xmlSAX2ExternalSubset" id="xmlSAX2ExternalSubset"></a>Function: xmlSAX2ExternalSubset</h3><pre class="programlisting">void	xmlSAX2ExternalSubset		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br />
</pre><p>Callback on external subset declaration.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the root element name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the SYSTEM ID (e.g. filename or URL)</td></tr></tbody></table></div><h3><a name="xmlSAX2GetColumnNumber" id="xmlSAX2GetColumnNumber"></a>Function: xmlSAX2GetColumnNumber</h3><pre class="programlisting">int	xmlSAX2GetColumnNumber		(void * ctx)<br />
</pre><p>Provide the column number of the current parsing point.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int</td></tr></tbody></table></div><h3><a name="xmlSAX2GetEntity" id="xmlSAX2GetEntity"></a>Function: xmlSAX2GetEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlSAX2GetEntity	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Get an entity by name</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found.</td></tr></tbody></table></div><h3><a name="xmlSAX2GetLineNumber" id="xmlSAX2GetLineNumber"></a>Function: xmlSAX2GetLineNumber</h3><pre class="programlisting">int	xmlSAX2GetLineNumber		(void * ctx)<br />
</pre><p>Provide the line number of the current parsing point.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int</td></tr></tbody></table></div><h3><a name="xmlSAX2GetParameterEntity" id="xmlSAX2GetParameterEntity"></a>Function: xmlSAX2GetParameterEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlSAX2GetParameterEntity	(void * ctx, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>Get a parameter entity by name</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found.</td></tr></tbody></table></div><h3><a name="xmlSAX2GetPublicId" id="xmlSAX2GetPublicId"></a>Function: xmlSAX2GetPublicId</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlSAX2GetPublicId	(void * ctx)<br />
</pre><p>Provides the public ID e.g. "-//SGMLSOURCE//DTD DEMO//EN"</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr></tbody></table></div><h3><a name="xmlSAX2GetSystemId" id="xmlSAX2GetSystemId"></a>Function: xmlSAX2GetSystemId</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlSAX2GetSystemId	(void * ctx)<br />
</pre><p>Provides the system ID, basically URL or filename e.g. http://www.sgmlsource.com/dtds/memo.dtd</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *</td></tr></tbody></table></div><h3><a name="xmlSAX2HasExternalSubset" id="xmlSAX2HasExternalSubset"></a>Function: xmlSAX2HasExternalSubset</h3><pre class="programlisting">int	xmlSAX2HasExternalSubset	(void * ctx)<br />
</pre><p>Does this document has an external subset</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><h3><a name="xmlSAX2HasInternalSubset" id="xmlSAX2HasInternalSubset"></a>Function: xmlSAX2HasInternalSubset</h3><pre class="programlisting">int	xmlSAX2HasInternalSubset	(void * ctx)<br />
</pre><p>Does this document has an internal subset</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><h3><a name="xmlSAX2IgnorableWhitespace" id="xmlSAX2IgnorableWhitespace"></a>Function: xmlSAX2IgnorableWhitespace</h3><pre class="programlisting">void	xmlSAX2IgnorableWhitespace	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ch, <br />					 int len)<br />
</pre><p>receiving some ignorable whitespaces from the parser. UNUSED: by default the DOM building will use <a href="libxml-SAX2.html#xmlSAX2Characters">xmlSAX2Characters</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>a <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml-xmlstring.html#xmlChar">xmlChar</a></td></tr></tbody></table></div><h3><a name="xmlSAX2InitDefaultSAXHandler" id="xmlSAX2InitDefaultSAXHandler"></a>Function: xmlSAX2InitDefaultSAXHandler</h3><pre class="programlisting">void	xmlSAX2InitDefaultSAXHandler	(<a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * hdlr, <br />					 int warning)<br />
</pre><p>Initialize the default XML SAX2 handler</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr><tr><td><span class="term"><i><tt>warning</tt></i>:</span></td><td>flag if non-zero sets the handler warning procedure</td></tr></tbody></table></div><h3><a name="xmlSAX2InitDocbDefaultSAXHandler" id="xmlSAX2InitDocbDefaultSAXHandler"></a>Function: xmlSAX2InitDocbDefaultSAXHandler</h3><pre class="programlisting">void	xmlSAX2InitDocbDefaultSAXHandler	(<a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * hdlr)<br />
</pre><p>Initialize the default DocBook SAX2 handler</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr></tbody></table></div><h3><a name="xmlSAX2InitHtmlDefaultSAXHandler" id="xmlSAX2InitHtmlDefaultSAXHandler"></a>Function: xmlSAX2InitHtmlDefaultSAXHandler</h3><pre class="programlisting">void	xmlSAX2InitHtmlDefaultSAXHandler	(<a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * hdlr)<br />
</pre><p>Initialize the default HTML SAX2 handler</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr></tbody></table></div><h3><a name="xmlSAX2InternalSubset" id="xmlSAX2InternalSubset"></a>Function: xmlSAX2InternalSubset</h3><pre class="programlisting">void	xmlSAX2InternalSubset		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br />
</pre><p>Callback on internal subset declaration.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the root element name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the SYSTEM ID (e.g. filename or URL)</td></tr></tbody></table></div><h3><a name="xmlSAX2IsStandalone" id="xmlSAX2IsStandalone"></a>Function: xmlSAX2IsStandalone</h3><pre class="programlisting">int	xmlSAX2IsStandalone		(void * ctx)<br />
</pre><p>Is this document tagged standalone ?</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div><h3><a name="xmlSAX2NotationDecl" id="xmlSAX2NotationDecl"></a>Function: xmlSAX2NotationDecl</h3><pre class="programlisting">void	xmlSAX2NotationDecl		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)<br />
</pre><p>What to do when a notation declaration has been parsed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The name of the notation</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr></tbody></table></div><h3><a name="xmlSAX2ProcessingInstruction" id="xmlSAX2ProcessingInstruction"></a>Function: xmlSAX2ProcessingInstruction</h3><pre class="programlisting">void	xmlSAX2ProcessingInstruction	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * target, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * data)<br />
</pre><p>A processing instruction has been parsed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>the target name</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the PI data's</td></tr></tbody></table></div><h3><a name="xmlSAX2Reference" id="xmlSAX2Reference"></a>Function: xmlSAX2Reference</h3><pre class="programlisting">void	xmlSAX2Reference		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name)<br />
</pre><p>called when an entity <a href="libxml-SAX2.html#xmlSAX2Reference">xmlSAX2Reference</a> is detected.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr></tbody></table></div><h3><a name="xmlSAX2ResolveEntity" id="xmlSAX2ResolveEntity"></a>Function: xmlSAX2ResolveEntity</h3><pre class="programlisting"><a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlSAX2ResolveEntity	(void * ctx, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId)<br />
</pre><p>The entity loader, to control the loading of external entities, the application can either: - override this xmlSAX2ResolveEntity() callback in the SAX block - or better use the xmlSetExternalEntityLoader() function to set up it's own entity resolution routine</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> if inlined or NULL for DOM behaviour.</td></tr></tbody></table></div><h3><a name="xmlSAX2SetDocumentLocator" id="xmlSAX2SetDocumentLocator"></a>Function: xmlSAX2SetDocumentLocator</h3><pre class="programlisting">void	xmlSAX2SetDocumentLocator	(void * ctx, <br />					 <a href="libxml-tree.html#xmlSAXLocatorPtr">xmlSAXLocatorPtr</a> loc)<br />
</pre><p>Receive the document locator at startup, actually <a href="libxml-globals.html#xmlDefaultSAXLocator">xmlDefaultSAXLocator</a> Everything is available on the context, so this is useless in our case.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>loc</tt></i>:</span></td><td>A SAX Locator</td></tr></tbody></table></div><h3><a name="xmlSAX2StartDocument" id="xmlSAX2StartDocument"></a>Function: xmlSAX2StartDocument</h3><pre class="programlisting">void	xmlSAX2StartDocument		(void * ctx)<br />
</pre><p>called when the document start being processed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr></tbody></table></div><h3><a name="xmlSAX2StartElement" id="xmlSAX2StartElement"></a>Function: xmlSAX2StartElement</h3><pre class="programlisting">void	xmlSAX2StartElement		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** atts)<br />
</pre><p>called when an opening tag has been processed.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>The element name, including namespace prefix</td></tr><tr><td><span class="term"><i><tt>atts</tt></i>:</span></td><td>An array of name/value attributes pairs, NULL terminated</td></tr></tbody></table></div><h3><a name="xmlSAX2StartElementNs" id="xmlSAX2StartElementNs"></a>Function: xmlSAX2StartElementNs</h3><pre class="programlisting">void	xmlSAX2StartElementNs		(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * localname, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI, <br />					 int nb_namespaces, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** namespaces, <br />					 int nb_attributes, <br />					 int nb_defaulted, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> ** attributes)<br />
</pre><p>SAX2 callback when an element start has been detected by the parser. It provides the namespace informations for the element, as well as the new namespace declarations on the element.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>localname</tt></i>:</span></td><td>the local name of the element</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the element namespace prefix if available</td></tr><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the element namespace name if available</td></tr><tr><td><span class="term"><i><tt>nb_namespaces</tt></i>:</span></td><td>number of namespace definitions on that node</td></tr><tr><td><span class="term"><i><tt>namespaces</tt></i>:</span></td><td>pointer to the array of prefix/URI pairs namespace definitions</td></tr><tr><td><span class="term"><i><tt>nb_attributes</tt></i>:</span></td><td>the number of attributes on that node</td></tr><tr><td><span class="term"><i><tt>nb_defaulted</tt></i>:</span></td><td>the number of defaulted attributes.</td></tr><tr><td><span class="term"><i><tt>attributes</tt></i>:</span></td><td>pointer to the array of (localname/prefix/URI/value/end) <a href="libxml-SAX.html#attribute">attribute</a> values.</td></tr></tbody></table></div><h3><a name="xmlSAX2UnparsedEntityDecl" id="xmlSAX2UnparsedEntityDecl"></a>Function: xmlSAX2UnparsedEntityDecl</h3><pre class="programlisting">void	xmlSAX2UnparsedEntityDecl	(void * ctx, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * name, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * notationName)<br />
</pre><p>What to do when an unparsed entity declaration is parsed</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The name of the entity</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>notationName</tt></i>:</span></td><td>the name of the notation</td></tr></tbody></table></div><h3><a name="xmlSAXDefaultVersion" id="xmlSAXDefaultVersion"></a>Function: xmlSAXDefaultVersion</h3><pre class="programlisting">int	xmlSAXDefaultVersion		(int version)<br />
</pre><p>Set the default version of SAX used globally by the library. By default, during initialization the default is set to 2. Note that it is generally a better coding style to use xmlSAXVersion() to set up the version explicitly for a given parsing context.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>version</tt></i>:</span></td><td>the version, 1 or 2</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the previous value in case of success and -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlSAXVersion" id="xmlSAXVersion"></a>Function: xmlSAXVersion</h3><pre class="programlisting">int	xmlSAXVersion			(<a href="libxml-tree.html#xmlSAXHandler">xmlSAXHandler</a> * hdlr, <br />					 int version)<br />
</pre><p>Initialize the default XML SAX handler according to the version</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr><tr><td><span class="term"><i><tt>version</tt></i>:</span></td><td>the version, 1 or 2</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error.</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
