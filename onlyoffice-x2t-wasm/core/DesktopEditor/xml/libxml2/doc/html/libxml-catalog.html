<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><style type="text/css">
      div.deprecated pre.programlisting {border-style: double;border-color:red}
      pre.programlisting {border-style: double;background: #EECFA1}
    </style><title>Module catalog from libxml2</title></head><body bgcolor="#8b7765" text="#000000" link="#a06060" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Module catalog from libxml2</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a style="font-weight:bold" href="../index.html">Main Menu</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../examples/index.html">Code Examples</a></li><li><a style="font-weight:bold" href="index.html">API Menu</a></li><li><a href="libxml-parser.html">Parser API</a></li><li><a href="libxml-tree.html">Tree API</a></li><li><a href="libxml-xmlreader.html">Reader API</a></li><li><a href="../guidelines.html">XML Guidelines</a></li><li><a href="../ChangeLog.html">ChangeLog</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>API Indexes</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="../APIchunk0.html">Alphabetic</a></li><li><a href="../APIconstructors.html">Constructors</a></li><li><a href="../APIfunctions.html">Functions/Types</a></li><li><a href="../APIfiles.html">Modules</a></li><li><a href="../APIsymbols.html">Symbols</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2"><tr valign="middle"><td><a accesskey="p" href="libxml-c14n.html"><img src="left.png" width="24" height="24" border="0" alt="Prev" /></a></td><th align="left"><a href="libxml-c14n.html">c14n</a></th><td><a accesskey="u" href="index.html"><img src="up.png" width="24" height="24" border="0" alt="Up" /></a></td><th align="left"><a href="index.html">API documentation</a></th><td><a accesskey="h" href="../index.html"><img src="home.png" width="24" height="24" border="0" alt="Home" /></a></td><th align="center"><a href="../index.html">The XML C parser and toolkit of Gnome</a></th><th align="right"><a href="libxml-chvalid.html">chvalid</a></th><td><a accesskey="n" href="libxml-chvalid.html"><img src="right.png" width="24" height="24" border="0" alt="Next" /></a></td></tr></table><p>the catalog module implements the support for XML Catalogs and SGML catalogs </p><h2>Table of Contents</h2><pre class="programlisting">#define <a href="#XML_CATALOGS_NAMESPACE">XML_CATALOGS_NAMESPACE</a></pre><pre class="programlisting">#define <a href="#XML_CATALOG_PI">XML_CATALOG_PI</a></pre><pre class="programlisting">Structure <a href="#xmlCatalog">xmlCatalog</a><br />struct _xmlCatalog
The content of this structure is not made public by the API.
</pre><pre class="programlisting">Enum <a href="#xmlCatalogAllow">xmlCatalogAllow</a>
</pre><pre class="programlisting">Enum <a href="#xmlCatalogPrefer">xmlCatalogPrefer</a>
</pre><pre class="programlisting">Typedef <a href="libxml-catalog.html#xmlCatalog">xmlCatalog</a> * <a name="xmlCatalogPtr" id="xmlCatalogPtr">xmlCatalogPtr</a>
</pre><pre class="programlisting">int	<a href="#xmlACatalogAdd">xmlACatalogAdd</a>			(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * orig, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * replace)</pre>
<pre class="programlisting">void	<a href="#xmlACatalogDump">xmlACatalogDump</a>			(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 FILE * out)</pre>
<pre class="programlisting">int	<a href="#xmlACatalogRemove">xmlACatalogRemove</a>		(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlACatalogResolve">xmlACatalogResolve</a>	(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlACatalogResolvePublic">xmlACatalogResolvePublic</a>	(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlACatalogResolveSystem">xmlACatalogResolveSystem</a>	(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlACatalogResolveURI">xmlACatalogResolveURI</a>	(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)</pre>
<pre class="programlisting">int	<a href="#xmlCatalogAdd">xmlCatalogAdd</a>			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * orig, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * replace)</pre>
<pre class="programlisting">void *	<a href="#xmlCatalogAddLocal">xmlCatalogAddLocal</a>		(void * catalogs, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URL)</pre>
<pre class="programlisting">void	<a href="#xmlCatalogCleanup">xmlCatalogCleanup</a>		(void)</pre>
<pre class="programlisting">int	<a href="#xmlCatalogConvert">xmlCatalogConvert</a>		(void)</pre>
<pre class="programlisting">void	<a href="#xmlCatalogDump">xmlCatalogDump</a>			(FILE * out)</pre>
<pre class="programlisting">void	<a href="#xmlCatalogFreeLocal">xmlCatalogFreeLocal</a>		(void * catalogs)</pre>
<pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogAllow">xmlCatalogAllow</a>	<a href="#xmlCatalogGetDefaults">xmlCatalogGetDefaults</a>	(void)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCatalogGetPublic">xmlCatalogGetPublic</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID)</pre>
<pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCatalogGetSystem">xmlCatalogGetSystem</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)</pre>
<pre class="programlisting">int	<a href="#xmlCatalogIsEmpty">xmlCatalogIsEmpty</a>		(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCatalogLocalResolve">xmlCatalogLocalResolve</a>	(void * catalogs, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCatalogLocalResolveURI">xmlCatalogLocalResolveURI</a>	(void * catalogs, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)</pre>
<pre class="programlisting">int	<a href="#xmlCatalogRemove">xmlCatalogRemove</a>		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCatalogResolve">xmlCatalogResolve</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCatalogResolvePublic">xmlCatalogResolvePublic</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCatalogResolveSystem">xmlCatalogResolveSystem</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)</pre>
<pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlCatalogResolveURI">xmlCatalogResolveURI</a>	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)</pre>
<pre class="programlisting">int	<a href="#xmlCatalogSetDebug">xmlCatalogSetDebug</a>		(int level)</pre>
<pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogPrefer">xmlCatalogPrefer</a>	<a href="#xmlCatalogSetDefaultPrefer">xmlCatalogSetDefaultPrefer</a>	(<a href="libxml-catalog.html#xmlCatalogPrefer">xmlCatalogPrefer</a> prefer)</pre>
<pre class="programlisting">void	<a href="#xmlCatalogSetDefaults">xmlCatalogSetDefaults</a>		(<a href="libxml-catalog.html#xmlCatalogAllow">xmlCatalogAllow</a> allow)</pre>
<pre class="programlisting">int	<a href="#xmlConvertSGMLCatalog">xmlConvertSGMLCatalog</a>		(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal)</pre>
<pre class="programlisting">void	<a href="#xmlFreeCatalog">xmlFreeCatalog</a>			(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal)</pre>
<pre class="programlisting">void	<a href="#xmlInitializeCatalog">xmlInitializeCatalog</a>		(void)</pre>
<pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a>	<a href="#xmlLoadACatalog">xmlLoadACatalog</a>		(const char * filename)</pre>
<pre class="programlisting">int	<a href="#xmlLoadCatalog">xmlLoadCatalog</a>			(const char * filename)</pre>
<pre class="programlisting">void	<a href="#xmlLoadCatalogs">xmlLoadCatalogs</a>			(const char * pathss)</pre>
<pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a>	<a href="#xmlLoadSGMLSuperCatalog">xmlLoadSGMLSuperCatalog</a>	(const char * filename)</pre>
<pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a>	<a href="#xmlNewCatalog">xmlNewCatalog</a>		(int sgml)</pre>
<pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	<a href="#xmlParseCatalogFile">xmlParseCatalogFile</a>	(const char * filename)</pre>
<h2>Description</h2>
<h3><a name="XML_CATALOGS_NAMESPACE" id="XML_CATALOGS_NAMESPACE"></a>Macro: XML_CATALOGS_NAMESPACE</h3><pre>#define XML_CATALOGS_NAMESPACE</pre><p>The namespace for the XML Catalogs elements.</p>
<h3><a name="XML_CATALOG_PI" id="XML_CATALOG_PI"></a>Macro: XML_CATALOG_PI</h3><pre>#define XML_CATALOG_PI</pre><p>The specific XML Catalog Processing Instuction name.</p>
<h3><a name="xmlCatalog" id="xmlCatalog">Structure xmlCatalog</a></h3><pre class="programlisting">Structure xmlCatalog<br />struct _xmlCatalog {
The content of this structure is not made public by the API.
}</pre><h3>Enum <a name="xmlCatalogAllow" id="xmlCatalogAllow">xmlCatalogAllow</a></h3><pre class="programlisting">Enum xmlCatalogAllow {
    <a name="XML_CATA_ALLOW_NONE" id="XML_CATA_ALLOW_NONE">XML_CATA_ALLOW_NONE</a> = 0
    <a name="XML_CATA_ALLOW_GLOBAL" id="XML_CATA_ALLOW_GLOBAL">XML_CATA_ALLOW_GLOBAL</a> = 1
    <a name="XML_CATA_ALLOW_DOCUMENT" id="XML_CATA_ALLOW_DOCUMENT">XML_CATA_ALLOW_DOCUMENT</a> = 2
    <a name="XML_CATA_ALLOW_ALL" id="XML_CATA_ALLOW_ALL">XML_CATA_ALLOW_ALL</a> = 3
}
</pre><h3>Enum <a name="xmlCatalogPrefer" id="xmlCatalogPrefer">xmlCatalogPrefer</a></h3><pre class="programlisting">Enum xmlCatalogPrefer {
    <a name="XML_CATA_PREFER_NONE" id="XML_CATA_PREFER_NONE">XML_CATA_PREFER_NONE</a> = 0
    <a name="XML_CATA_PREFER_PUBLIC" id="XML_CATA_PREFER_PUBLIC">XML_CATA_PREFER_PUBLIC</a> = 1
    <a name="XML_CATA_PREFER_SYSTEM" id="XML_CATA_PREFER_SYSTEM">XML_CATA_PREFER_SYSTEM</a> = 2
}
</pre><h3><a name="xmlACatalogAdd" id="xmlACatalogAdd"></a>Function: xmlACatalogAdd</h3><pre class="programlisting">int	xmlACatalogAdd			(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * orig, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * replace)<br />
</pre><p>Add an entry in the catalog, it may overwrite existing but different entries.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>a Catalog</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the type of record to add to the catalog</td></tr><tr><td><span class="term"><i><tt>orig</tt></i>:</span></td><td>the system, public or prefix to match</td></tr><tr><td><span class="term"><i><tt>replace</tt></i>:</span></td><td>the replacement value for the match</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if successful, -1 otherwise</td></tr></tbody></table></div><h3><a name="xmlACatalogDump" id="xmlACatalogDump"></a>Function: xmlACatalogDump</h3><pre class="programlisting">void	xmlACatalogDump			(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 FILE * out)<br />
</pre><p>Dump the given catalog to the given file.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>a Catalog</td></tr><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>the file.</td></tr></tbody></table></div><h3><a name="xmlACatalogRemove" id="xmlACatalogRemove"></a>Function: xmlACatalogRemove</h3><pre class="programlisting">int	xmlACatalogRemove		(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Remove an entry from the catalog</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>a Catalog</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value to remove</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of entries removed if successful, -1 otherwise</td></tr></tbody></table></div><h3><a name="xmlACatalogResolve" id="xmlACatalogResolve"></a>Function: xmlACatalogResolve</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlACatalogResolve	(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)<br />
</pre><p>Do a complete resolution lookup of an External Identifier</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>a Catalog</td></tr><tr><td><span class="term"><i><tt>pubID</tt></i>:</span></td><td>the public ID string</td></tr><tr><td><span class="term"><i><tt>sysID</tt></i>:</span></td><td>the system ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the URI of the resource or NULL if not found, it must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlACatalogResolvePublic" id="xmlACatalogResolvePublic"></a>Function: xmlACatalogResolvePublic</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlACatalogResolvePublic	(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID)<br />
</pre><p>Try to lookup the catalog local <a href="libxml-SAX.html#reference">reference</a> associated to a public ID in that catalog</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>a Catalog</td></tr><tr><td><span class="term"><i><tt>pubID</tt></i>:</span></td><td>the public ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the local resource if found or NULL otherwise, the value returned must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlACatalogResolveSystem" id="xmlACatalogResolveSystem"></a>Function: xmlACatalogResolveSystem</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlACatalogResolveSystem	(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)<br />
</pre><p>Try to lookup the catalog resource for a system ID</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>a Catalog</td></tr><tr><td><span class="term"><i><tt>sysID</tt></i>:</span></td><td>the system ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resource if found or NULL otherwise, the value returned must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlACatalogResolveURI" id="xmlACatalogResolveURI"></a>Function: xmlACatalogResolveURI</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlACatalogResolveURI	(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)<br />
</pre><p>Do a complete resolution lookup of an URI</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>a Catalog</td></tr><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the URI of the resource or NULL if not found, it must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlCatalogAdd" id="xmlCatalogAdd"></a>Function: xmlCatalogAdd</h3><pre class="programlisting">int	xmlCatalogAdd			(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * type, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * orig, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * replace)<br />
</pre><p>Add an entry in the catalog, it may overwrite existing but different entries. If called before any other catalog routine, allows to override the default shared catalog put in place by xmlInitializeCatalog();</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the type of record to add to the catalog</td></tr><tr><td><span class="term"><i><tt>orig</tt></i>:</span></td><td>the system, public or prefix to match</td></tr><tr><td><span class="term"><i><tt>replace</tt></i>:</span></td><td>the replacement value for the match</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if successful, -1 otherwise</td></tr></tbody></table></div><h3><a name="xmlCatalogAddLocal" id="xmlCatalogAddLocal"></a>Function: xmlCatalogAddLocal</h3><pre class="programlisting">void *	xmlCatalogAddLocal		(void * catalogs, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URL)<br />
</pre><p>Add the new entry to the catalog list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catalogs</tt></i>:</span></td><td>a document's list of catalogs</td></tr><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the URL to a new local catalog</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the updated list</td></tr></tbody></table></div><h3><a name="xmlCatalogCleanup" id="xmlCatalogCleanup"></a>Function: xmlCatalogCleanup</h3><pre class="programlisting">void	xmlCatalogCleanup		(void)<br />
</pre><p>Free up all the memory associated with catalogs</p>
<h3><a name="xmlCatalogConvert" id="xmlCatalogConvert"></a>Function: xmlCatalogConvert</h3><pre class="programlisting">int	xmlCatalogConvert		(void)<br />
</pre><p>Convert all the SGML catalog entries as XML ones</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of entries converted if successful, -1 otherwise</td></tr></tbody></table></div><h3><a name="xmlCatalogDump" id="xmlCatalogDump"></a>Function: xmlCatalogDump</h3><pre class="programlisting">void	xmlCatalogDump			(FILE * out)<br />
</pre><p>Dump all the global catalog content to the given file.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>the file.</td></tr></tbody></table></div><h3><a name="xmlCatalogFreeLocal" id="xmlCatalogFreeLocal"></a>Function: xmlCatalogFreeLocal</h3><pre class="programlisting">void	xmlCatalogFreeLocal		(void * catalogs)<br />
</pre><p>Free up the memory associated to the catalog list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catalogs</tt></i>:</span></td><td>a document's list of catalogs</td></tr></tbody></table></div><h3><a name="xmlCatalogGetDefaults" id="xmlCatalogGetDefaults"></a>Function: xmlCatalogGetDefaults</h3><pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogAllow">xmlCatalogAllow</a>	xmlCatalogGetDefaults	(void)<br />
</pre><p>Used to get the user preference w.r.t. to what catalogs should be accepted</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current <a href="libxml-catalog.html#xmlCatalogAllow">xmlCatalogAllow</a> value</td></tr></tbody></table></div><h3><a name="xmlCatalogGetPublic" id="xmlCatalogGetPublic"></a>Function: xmlCatalogGetPublic</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCatalogGetPublic	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID)<br />
</pre><p>Try to lookup the catalog <a href="libxml-SAX.html#reference">reference</a> associated to a public ID DEPRECATED, use xmlCatalogResolvePublic()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>pubID</tt></i>:</span></td><td>the public ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resource if found or NULL otherwise.</td></tr></tbody></table></div><h3><a name="xmlCatalogGetSystem" id="xmlCatalogGetSystem"></a>Function: xmlCatalogGetSystem</h3><pre class="programlisting">const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCatalogGetSystem	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)<br />
</pre><p>Try to lookup the catalog <a href="libxml-SAX.html#reference">reference</a> associated to a system ID DEPRECATED, use xmlCatalogResolveSystem()</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sysID</tt></i>:</span></td><td>the system ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resource if found or NULL otherwise.</td></tr></tbody></table></div><h3><a name="xmlCatalogIsEmpty" id="xmlCatalogIsEmpty"></a>Function: xmlCatalogIsEmpty</h3><pre class="programlisting">int	xmlCatalogIsEmpty		(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal)<br />
</pre><p>Check is a catalog is empty</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>should this create an SGML catalog</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the catalog is empty, 0 if not, amd -1 in case of error.</td></tr></tbody></table></div><h3><a name="xmlCatalogLocalResolve" id="xmlCatalogLocalResolve"></a>Function: xmlCatalogLocalResolve</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCatalogLocalResolve	(void * catalogs, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)<br />
</pre><p>Do a complete resolution lookup of an External Identifier using a document's private catalog list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catalogs</tt></i>:</span></td><td>a document's list of catalogs</td></tr><tr><td><span class="term"><i><tt>pubID</tt></i>:</span></td><td>the public ID string</td></tr><tr><td><span class="term"><i><tt>sysID</tt></i>:</span></td><td>the system ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the URI of the resource or NULL if not found, it must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlCatalogLocalResolveURI" id="xmlCatalogLocalResolveURI"></a>Function: xmlCatalogLocalResolveURI</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCatalogLocalResolveURI	(void * catalogs, <br />						 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)<br />
</pre><p>Do a complete resolution lookup of an URI using a document's private catalog list</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catalogs</tt></i>:</span></td><td>a document's list of catalogs</td></tr><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the URI of the resource or NULL if not found, it must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlCatalogRemove" id="xmlCatalogRemove"></a>Function: xmlCatalogRemove</h3><pre class="programlisting">int	xmlCatalogRemove		(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * value)<br />
</pre><p>Remove an entry from the catalog</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the value to remove</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of entries removed if successful, -1 otherwise</td></tr></tbody></table></div><h3><a name="xmlCatalogResolve" id="xmlCatalogResolve"></a>Function: xmlCatalogResolve</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCatalogResolve	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID, <br />					 const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)<br />
</pre><p>Do a complete resolution lookup of an External Identifier</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>pubID</tt></i>:</span></td><td>the public ID string</td></tr><tr><td><span class="term"><i><tt>sysID</tt></i>:</span></td><td>the system ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the URI of the resource or NULL if not found, it must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlCatalogResolvePublic" id="xmlCatalogResolvePublic"></a>Function: xmlCatalogResolvePublic</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCatalogResolvePublic	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * pubID)<br />
</pre><p>Try to lookup the catalog <a href="libxml-SAX.html#reference">reference</a> associated to a public ID</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>pubID</tt></i>:</span></td><td>the public ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resource if found or NULL otherwise, the value returned must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlCatalogResolveSystem" id="xmlCatalogResolveSystem"></a>Function: xmlCatalogResolveSystem</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCatalogResolveSystem	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * sysID)<br />
</pre><p>Try to lookup the catalog resource for a system ID</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sysID</tt></i>:</span></td><td>the system ID string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resource if found or NULL otherwise, the value returned must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlCatalogResolveURI" id="xmlCatalogResolveURI"></a>Function: xmlCatalogResolveURI</h3><pre class="programlisting"><a href="libxml-xmlstring.html#xmlChar">xmlChar</a> *	xmlCatalogResolveURI	(const <a href="libxml-xmlstring.html#xmlChar">xmlChar</a> * URI)<br />
</pre><p>Do a complete resolution lookup of an URI</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the URI of the resource or NULL if not found, it must be freed by the caller.</td></tr></tbody></table></div><h3><a name="xmlCatalogSetDebug" id="xmlCatalogSetDebug"></a>Function: xmlCatalogSetDebug</h3><pre class="programlisting">int	xmlCatalogSetDebug		(int level)<br />
</pre><p>Used to set the debug level for catalog operation, 0 disable debugging, 1 enable it</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>level</tt></i>:</span></td><td>the debug level of catalogs required</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the previous value of the catalog debugging level</td></tr></tbody></table></div><h3><a name="xmlCatalogSetDefaultPrefer" id="xmlCatalogSetDefaultPrefer"></a>Function: xmlCatalogSetDefaultPrefer</h3><pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogPrefer">xmlCatalogPrefer</a>	xmlCatalogSetDefaultPrefer	(<a href="libxml-catalog.html#xmlCatalogPrefer">xmlCatalogPrefer</a> prefer)<br />
</pre><p>Allows to set the preference between public and system for deletion in XML Catalog resolution. C.f. section 4.1.1 of the spec Values accepted are <a href="libxml-catalog.html#XML_CATA_PREFER_PUBLIC">XML_CATA_PREFER_PUBLIC</a> or <a href="libxml-catalog.html#XML_CATA_PREFER_SYSTEM">XML_CATA_PREFER_SYSTEM</a></p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>prefer</tt></i>:</span></td><td>the default preference for delegation</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the previous value of the default preference for delegation</td></tr></tbody></table></div><h3><a name="xmlCatalogSetDefaults" id="xmlCatalogSetDefaults"></a>Function: xmlCatalogSetDefaults</h3><pre class="programlisting">void	xmlCatalogSetDefaults		(<a href="libxml-catalog.html#xmlCatalogAllow">xmlCatalogAllow</a> allow)<br />
</pre><p>Used to set the user preference w.r.t. to what catalogs should be accepted</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>allow</tt></i>:</span></td><td>what catalogs should be accepted</td></tr></tbody></table></div><h3><a name="xmlConvertSGMLCatalog" id="xmlConvertSGMLCatalog"></a>Function: xmlConvertSGMLCatalog</h3><pre class="programlisting">int	xmlConvertSGMLCatalog		(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal)<br />
</pre><p>Convert all the SGML catalog entries as XML ones</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>the catalog</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of entries converted if successful, -1 otherwise</td></tr></tbody></table></div><h3><a name="xmlFreeCatalog" id="xmlFreeCatalog"></a>Function: xmlFreeCatalog</h3><pre class="programlisting">void	xmlFreeCatalog			(<a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> catal)<br />
</pre><p>Free the memory allocated to a Catalog</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>catal</tt></i>:</span></td><td>a Catalog</td></tr></tbody></table></div><h3><a name="xmlInitializeCatalog" id="xmlInitializeCatalog"></a>Function: xmlInitializeCatalog</h3><pre class="programlisting">void	xmlInitializeCatalog		(void)<br />
</pre><p>Do the catalog initialization. this function is not thread safe, catalog initialization should preferably be done once at startup</p>
<h3><a name="xmlLoadACatalog" id="xmlLoadACatalog"></a>Function: xmlLoadACatalog</h3><pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a>	xmlLoadACatalog		(const char * filename)<br />
</pre><p>Load the catalog and build the associated data structures. This can be either an XML Catalog or an SGML Catalog It will recurse in SGML CATALOG entries. On the other hand XML Catalogs are not handled recursively.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file path</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the catalog parsed or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlLoadCatalog" id="xmlLoadCatalog"></a>Function: xmlLoadCatalog</h3><pre class="programlisting">int	xmlLoadCatalog			(const char * filename)<br />
</pre><p>Load the catalog and makes its definitions effective for the default external entity loader. It will recurse in SGML CATALOG entries. this function is not thread safe, catalog initialization should preferably be done once at startup</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file path</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success -1 in case of error</td></tr></tbody></table></div><h3><a name="xmlLoadCatalogs" id="xmlLoadCatalogs"></a>Function: xmlLoadCatalogs</h3><pre class="programlisting">void	xmlLoadCatalogs			(const char * pathss)<br />
</pre><p>Load the catalogs and makes their definitions effective for the default external entity loader. this function is not thread safe, catalog initialization should preferably be done once at startup</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>pathss</tt></i>:</span></td><td>a list of directories separated by a colon or a space.</td></tr></tbody></table></div><h3><a name="xmlLoadSGMLSuperCatalog" id="xmlLoadSGMLSuperCatalog"></a>Function: xmlLoadSGMLSuperCatalog</h3><pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a>	xmlLoadSGMLSuperCatalog	(const char * filename)<br />
</pre><p>Load an SGML super catalog. It won't expand CATALOG or DELEGATE references. This is only needed for manipulating SGML Super Catalogs like adding and removing CATALOG or DELEGATE entries.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file path</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the catalog parsed or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlNewCatalog" id="xmlNewCatalog"></a>Function: xmlNewCatalog</h3><pre class="programlisting"><a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a>	xmlNewCatalog		(int sgml)<br />
</pre><p>create a new Catalog.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>sgml</tt></i>:</span></td><td>should this create an SGML catalog</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml-catalog.html#xmlCatalogPtr">xmlCatalogPtr</a> or NULL in case of error</td></tr></tbody></table></div><h3><a name="xmlParseCatalogFile" id="xmlParseCatalogFile"></a>Function: xmlParseCatalogFile</h3><pre class="programlisting"><a href="libxml-tree.html#xmlDocPtr">xmlDocPtr</a>	xmlParseCatalogFile	(const char * filename)<br />
</pre><p>parse an XML file and build a tree. It's like xmlParseFile() except it bypass all catalog lookups.</p>
<div class="variablelist"><table border="0"><col align="left" /><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting document tree or NULL in case of error</td></tr></tbody></table></div><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
