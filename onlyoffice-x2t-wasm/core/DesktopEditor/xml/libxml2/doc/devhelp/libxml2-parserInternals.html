<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>parserInternals: internals routines and limits exported by the parser.</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-parser.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-pattern.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">parserInternals</span>
    </h2>
    <p>parserInternals - internals routines and limits exported by the parser.</p>
    <p>this module exports a number of internal parsing routines they are not really all intended for applications but can prove useful doing low level processing. </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">#define <a href="#XML_SUBSTITUTE_REF">XML_SUBSTITUTE_REF</a>;
#define <a href="#IS_BLANK">IS_BLANK</a>(c);
#define <a href="#IS_BYTE_CHAR">IS_BYTE_CHAR</a>(c);
#define <a href="#IS_PUBIDCHAR">IS_PUBIDCHAR</a>(c);
#define <a href="#IS_DIGIT_CH">IS_DIGIT_CH</a>(c);
#define <a href="#IS_EXTENDER">IS_EXTENDER</a>(c);
#define <a href="#IS_ASCII_DIGIT">IS_ASCII_DIGIT</a>(c);
#define <a href="#IS_COMBINING_CH">IS_COMBINING_CH</a>(c);
#define <a href="#IS_CHAR">IS_CHAR</a>(c);
#define <a href="#IS_LETTER">IS_LETTER</a>(c);
#define <a href="#IS_IDEOGRAPHIC">IS_IDEOGRAPHIC</a>(c);
#define <a href="#MOVETO_STARTTAG">MOVETO_STARTTAG</a>(p);
#define <a href="#XML_MAX_NAME_LENGTH">XML_MAX_NAME_LENGTH</a>;
#define <a href="#IS_ASCII_LETTER">IS_ASCII_LETTER</a>(c);
#define <a href="#IS_DIGIT">IS_DIGIT</a>(c);
#define <a href="#XML_MAX_DICTIONARY_LIMIT">XML_MAX_DICTIONARY_LIMIT</a>;
#define <a href="#XML_SUBSTITUTE_PEREF">XML_SUBSTITUTE_PEREF</a>;
#define <a href="#MOVETO_ENDTAG">MOVETO_ENDTAG</a>(p);
#define <a href="#SKIP_EOL">SKIP_EOL</a>(p);
#define <a href="#IS_EXTENDER_CH">IS_EXTENDER_CH</a>(c);
#define <a href="#IS_BLANK_CH">IS_BLANK_CH</a>(c);
#define <a href="#IS_LETTER_CH">IS_LETTER_CH</a>(c);
#define <a href="#XML_MAX_LOOKUP_LIMIT">XML_MAX_LOOKUP_LIMIT</a>;
#define <a href="#XML_MAX_TEXT_LENGTH">XML_MAX_TEXT_LENGTH</a>;
#define <a href="#XML_SUBSTITUTE_NONE">XML_SUBSTITUTE_NONE</a>;
#define <a href="#IS_COMBINING">IS_COMBINING</a>(c);
#define <a href="#XML_MAX_NAMELEN">XML_MAX_NAMELEN</a>;
#define <a href="#IS_BASECHAR">IS_BASECHAR</a>(c);
#define <a href="#INPUT_CHUNK">INPUT_CHUNK</a>;
#define <a href="#IS_PUBIDCHAR_CH">IS_PUBIDCHAR_CH</a>(c);
#define <a href="#IS_CHAR_CH">IS_CHAR_CH</a>(c);
#define <a href="#XML_SUBSTITUTE_BOTH">XML_SUBSTITUTE_BOTH</a>;
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#nodePop">nodePop</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseNotationDecl">xmlParseNotationDecl</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseExternalSubset">xmlParseExternalSubset</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * SystemID);
void	<a href="#xmlParseMisc">xmlParseMisc</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlSwitchInputEncoding">xmlSwitchInputEncoding</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler);
<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlNewStringInputStream">xmlNewStringInputStream</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * buffer);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseExternalID">xmlParseExternalID</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** publicID, <br/>					 int strict);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlScanName">xmlScanName</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlParseElementDecl">xmlParseElementDecl</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseMarkupDecl">xmlParseMarkupDecl</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-HTMLparser.html#htmlParserCtxtPtr">htmlParserCtxtPtr</a>	<a href="#htmlCreateFileParserCtxt">htmlCreateFileParserCtxt</a>	(const char * filename, <br/>							 const char * encoding);
int	<a href="#inputPush">inputPush</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> value);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStringLenDecodeEntities">xmlStringLenDecodeEntities</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>						 int len, <br/>						 int what, <br/>						 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end, <br/>						 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end2, <br/>						 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end3);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#namePop">namePop</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseContent">xmlParseContent</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlNewInputStream">xmlNewInputStream</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNamespaceParseQName">xmlNamespaceParseQName</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** prefix);
<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlNewInputFromFile">xmlNewInputFromFile</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 const char * filename);
void	<a href="#xmlParserHandlePEReference">xmlParserHandlePEReference</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlStringDecodeEntities">xmlStringDecodeEntities</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>					 int what, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end2, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end3);
<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlCreateFileParserCtxt">xmlCreateFileParserCtxt</a>	(const char * filename);
int	<a href="#xmlParseCharRef">xmlParseCharRef</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseElement">xmlParseElement</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseTextDecl">xmlParseTextDecl</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlNewEntityInputStream">xmlNewEntityInputStream</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> entity);
int	<a href="#xmlCopyCharMultiByte">xmlCopyCharMultiByte</a>		(<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * out, <br/>					 int val);
<a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a>	<a href="#xmlParseElementChildrenContentDecl">xmlParseElementChildrenContentDecl</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>							 int inputchk);
void	<a href="#xmlParseCharData">xmlParseCharData</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 int cdata);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseVersionInfo">xmlParseVersionInfo</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlParseSDDecl">xmlParseSDDecl</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlParseEnumeratedType">xmlParseEnumeratedType</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> * tree);
void	<a href="#xmlHandleEntity">xmlHandleEntity</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> entity);
int	<a href="#xmlCurrentChar">xmlCurrentChar</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 int * len);
int	<a href="#xmlSkipBlankChars">xmlSkipBlankChars</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a>	<a href="#xmlParseNotationType">xmlParseNotationType</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParserInputShrink">xmlParserInputShrink</a>		(<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> in);
void	<a href="#xmlSetEntityReferenceFunc">xmlSetEntityReferenceFunc</a>	(<a href="libxml2-parserInternals.html#xmlEntityReferenceFunc">xmlEntityReferenceFunc</a> func);
void	<a href="#xmlFreeInputStream">xmlFreeInputStream</a>		(<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input);
void	<a href="#xmlParsePEReference">xmlParsePEReference</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlCreateURLParserCtxt">xmlCreateURLParserCtxt</a>	(const char * filename, <br/>						 int options);
int	<a href="#xmlIsLetter">xmlIsLetter</a>			(int c);
int	<a href="#xmlCheckLanguageID">xmlCheckLanguageID</a>		(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * lang);
void	<a href="#xmlNextChar">xmlNextChar</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a>	<a href="#xmlParseEnumerationType">xmlParseEnumerationType</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlParseAttributeType">xmlParseAttributeType</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> * tree);
int	<a href="#xmlParseDefaultDecl">xmlParseDefaultDecl</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** value);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseSystemLiteral">xmlParseSystemLiteral</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseAttValue">xmlParseAttValue</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlCreateMemoryParserCtxt">xmlCreateMemoryParserCtxt</a>	(const char * buffer, <br/>							 int size);
void	<a href="#xmlParseAttributeListDecl">xmlParseAttributeListDecl</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseName">xmlParseName</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseEncName">xmlParseEncName</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#nodePush">nodePush</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> value);
int	<a href="#xmlSwitchEncoding">xmlSwitchEncoding</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
int	<a href="#xmlSwitchToEncoding">xmlSwitchToEncoding</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler);
<a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#xmlParseEntityRef">xmlParseEntityRef</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseAttribute">xmlParseAttribute</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** value);
void	<a href="#xmlParseEndTag">xmlParseEndTag</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseEncodingDecl">xmlParseEncodingDecl</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#htmlInitAutoClose">htmlInitAutoClose</a>		(void);
<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	<a href="#xmlCreateEntityParserCtxt">xmlCreateEntityParserCtxt</a>	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * URL, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ID, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * base);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlSplitQName">xmlSplitQName</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** prefix);
void	<a href="#xmlParserHandleReference">xmlParserHandleReference</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParsePITarget">xmlParsePITarget</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlParseElementContentDecl">xmlParseElementContentDecl</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 <a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> * result);
<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#inputPop">inputPop</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a>	<a href="#xmlPopInput">xmlPopInput</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParsePubidLiteral">xmlParsePubidLiteral</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlPushInput">xmlPushInput</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseEntityValue">xmlParseEntityValue</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** orig);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlDecodeEntities">xmlDecodeEntities</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 int len, <br/>					 int what, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end2, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end3);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseVersionNum">xmlParseVersionNum</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseXMLDecl">xmlParseXMLDecl</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
typedef void <a href="#xmlEntityReferenceFunc">xmlEntityReferenceFunc</a>		(<a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> ent, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> firstNode, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> lastNode);
<a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a>	<a href="#xmlParseElementMixedContentDecl">xmlParseElementMixedContentDecl</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>							 int inputchk);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseQuotedString">xmlParseQuotedString</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNamespaceParseNSDef">xmlNamespaceParseNSDef</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseCDSect">xmlParseCDSect</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlStringCurrentChar">xmlStringCurrentChar</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * cur, <br/>					 int * len);
void	<a href="#xmlParseComment">xmlParseComment</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlErrMemory">xmlErrMemory</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const char * extra);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNamespaceParseNCName">xmlNamespaceParseNCName</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseNmtoken">xmlParseNmtoken</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseReference">xmlParseReference</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#namePush">namePush</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value);
void	<a href="#xmlParseNamespace">xmlParseNamespace</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
int	<a href="#xmlCopyChar">xmlCopyChar</a>			(int len, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * out, <br/>					 int val);
void	<a href="#xmlParsePI">xmlParsePI</a>			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseDocTypeDecl">xmlParseDocTypeDecl</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlParseStartTag">xmlParseStartTag</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
void	<a href="#xmlParseEntityDecl">xmlParseEntityDecl</a>		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="INPUT_CHUNK">Macro </a>INPUT_CHUNK</h3><pre class="programlisting">#define <a href="#INPUT_CHUNK">INPUT_CHUNK</a>;
</pre><p>The parser tries to always have that amount of input ready. One of the point is providing context when reporting errors.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_ASCII_DIGIT">Macro </a>IS_ASCII_DIGIT</h3><pre class="programlisting">#define <a href="#IS_ASCII_DIGIT">IS_ASCII_DIGIT</a>(c);
</pre><p>Macro to check [0-9]</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> value</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_ASCII_LETTER">Macro </a>IS_ASCII_LETTER</h3><pre class="programlisting">#define <a href="#IS_ASCII_LETTER">IS_ASCII_LETTER</a>(c);
</pre><p>Macro to check [a-zA-Z]</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> value</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_BASECHAR">Macro </a>IS_BASECHAR</h3><pre class="programlisting">#define <a href="#IS_BASECHAR">IS_BASECHAR</a>(c);
</pre><p>Macro to check the following production in the XML spec: [85] BaseChar ::= ... long list see REC ...</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_BLANK">Macro </a>IS_BLANK</h3><pre class="programlisting">#define <a href="#IS_BLANK">IS_BLANK</a>(c);
</pre><p>Macro to check the following production in the XML spec: [3] S ::= (#x20 | #x9 | #xD | #xA)+</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_BLANK_CH">Macro </a>IS_BLANK_CH</h3><pre class="programlisting">#define <a href="#IS_BLANK_CH">IS_BLANK_CH</a>(c);
</pre><p>Behaviour same as <a href="libxml2-parserInternals.html#IS_BLANK">IS_BLANK</a></p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> value (normally unsigned char)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_BYTE_CHAR">Macro </a>IS_BYTE_CHAR</h3><pre class="programlisting">#define <a href="#IS_BYTE_CHAR">IS_BYTE_CHAR</a>(c);
</pre><p>Macro to check the following production in the XML spec: [2] Char ::= #x9 | #xA | #xD | [#x20...] any byte character in the accepted range</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an byte value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_CHAR">Macro </a>IS_CHAR</h3><pre class="programlisting">#define <a href="#IS_CHAR">IS_CHAR</a>(c);
</pre><p>Macro to check the following production in the XML spec: [2] Char ::= #x9 | #xA | #xD | [#x20-#xD7FF] | [#xE000-#xFFFD] | [#x10000-#x10FFFF] any Unicode character, excluding the surrogate blocks, FFFE, and FFFF.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_CHAR_CH">Macro </a>IS_CHAR_CH</h3><pre class="programlisting">#define <a href="#IS_CHAR_CH">IS_CHAR_CH</a>(c);
</pre><p>Behaves like <a href="libxml2-parserInternals.html#IS_CHAR">IS_CHAR</a> on single-byte value</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> (usually an unsigned char)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_COMBINING">Macro </a>IS_COMBINING</h3><pre class="programlisting">#define <a href="#IS_COMBINING">IS_COMBINING</a>(c);
</pre><p>Macro to check the following production in the XML spec: [87] CombiningChar ::= ... long list see REC ...</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_COMBINING_CH">Macro </a>IS_COMBINING_CH</h3><pre class="programlisting">#define <a href="#IS_COMBINING_CH">IS_COMBINING_CH</a>(c);
</pre><p>Always false (all combining chars &gt; 0xff)</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> (usually an unsigned char)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_DIGIT">Macro </a>IS_DIGIT</h3><pre class="programlisting">#define <a href="#IS_DIGIT">IS_DIGIT</a>(c);
</pre><p>Macro to check the following production in the XML spec: [88] Digit ::= ... long list see REC ...</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_DIGIT_CH">Macro </a>IS_DIGIT_CH</h3><pre class="programlisting">#define <a href="#IS_DIGIT_CH">IS_DIGIT_CH</a>(c);
</pre><p>Behaves like <a href="libxml2-parserInternals.html#IS_DIGIT">IS_DIGIT</a> but with a single byte argument</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> value (usually an unsigned char)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_EXTENDER">Macro </a>IS_EXTENDER</h3><pre class="programlisting">#define <a href="#IS_EXTENDER">IS_EXTENDER</a>(c);
</pre><p>Macro to check the following production in the XML spec: [89] Extender ::= #x00B7 | #x02D0 | #x02D1 | #x0387 | #x0640 | #x0E46 | #x0EC6 | #x3005 | [#x3031-#x3035] | [#x309D-#x309E] | [#x30FC-#x30FE]</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_EXTENDER_CH">Macro </a>IS_EXTENDER_CH</h3><pre class="programlisting">#define <a href="#IS_EXTENDER_CH">IS_EXTENDER_CH</a>(c);
</pre><p>Behaves like <a href="libxml2-parserInternals.html#IS_EXTENDER">IS_EXTENDER</a> but with a single-byte argument</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> value (usually an unsigned char)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_IDEOGRAPHIC">Macro </a>IS_IDEOGRAPHIC</h3><pre class="programlisting">#define <a href="#IS_IDEOGRAPHIC">IS_IDEOGRAPHIC</a>(c);
</pre><p>Macro to check the following production in the XML spec: [86] Ideographic ::= [#x4E00-#x9FA5] | #x3007 | [#x3021-#x3029]</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_LETTER">Macro </a>IS_LETTER</h3><pre class="programlisting">#define <a href="#IS_LETTER">IS_LETTER</a>(c);
</pre><p>Macro to check the following production in the XML spec: [84] Letter ::= BaseChar | Ideographic</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_LETTER_CH">Macro </a>IS_LETTER_CH</h3><pre class="programlisting">#define <a href="#IS_LETTER_CH">IS_LETTER_CH</a>(c);
</pre><p>Macro behaves like IS_LETTER, but only check base chars</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> value (normally unsigned char)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_PUBIDCHAR">Macro </a>IS_PUBIDCHAR</h3><pre class="programlisting">#define <a href="#IS_PUBIDCHAR">IS_PUBIDCHAR</a>(c);
</pre><p>Macro to check the following production in the XML spec: [13] PubidChar ::= #x20 | #xD | #xA | [a-zA-Z0-9] | [-'()+,./:=?;!*#@$_%]</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an UNICODE value (int)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="IS_PUBIDCHAR_CH">Macro </a>IS_PUBIDCHAR_CH</h3><pre class="programlisting">#define <a href="#IS_PUBIDCHAR_CH">IS_PUBIDCHAR_CH</a>(c);
</pre><p>Same as <a href="libxml2-parserInternals.html#IS_PUBIDCHAR">IS_PUBIDCHAR</a> but for single-byte value</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> value (normally unsigned char)</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="MOVETO_ENDTAG">Macro </a>MOVETO_ENDTAG</h3><pre class="programlisting">#define <a href="#MOVETO_ENDTAG">MOVETO_ENDTAG</a>(p);
</pre><p>Skips to the next '&gt;' char.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>p</tt></i>:</span></td><td>and UTF8 string pointer</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="MOVETO_STARTTAG">Macro </a>MOVETO_STARTTAG</h3><pre class="programlisting">#define <a href="#MOVETO_STARTTAG">MOVETO_STARTTAG</a>(p);
</pre><p>Skips to the next '&lt;' char.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>p</tt></i>:</span></td><td>and UTF8 string pointer</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="SKIP_EOL">Macro </a>SKIP_EOL</h3><pre class="programlisting">#define <a href="#SKIP_EOL">SKIP_EOL</a>(p);
</pre><p>Skips the end of line chars.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>p</tt></i>:</span></td><td>and UTF8 string pointer</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_MAX_DICTIONARY_LIMIT">Macro </a>XML_MAX_DICTIONARY_LIMIT</h3><pre class="programlisting">#define <a href="#XML_MAX_DICTIONARY_LIMIT">XML_MAX_DICTIONARY_LIMIT</a>;
</pre><p>Maximum size allowed by the parser for a dictionary by default This is not a limitation of the parser but a safety boundary feature, use <a href="libxml2-parser.html#XML_PARSE_HUGE">XML_PARSE_HUGE</a> option to override it. Introduced in 2.9.0</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_MAX_LOOKUP_LIMIT">Macro </a>XML_MAX_LOOKUP_LIMIT</h3><pre class="programlisting">#define <a href="#XML_MAX_LOOKUP_LIMIT">XML_MAX_LOOKUP_LIMIT</a>;
</pre><p>Maximum size allowed by the parser for ahead lookup This is an upper boundary enforced by the parser to avoid bad behaviour on "unfriendly' content Introduced in 2.9.0</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_MAX_NAMELEN">Macro </a>XML_MAX_NAMELEN</h3><pre class="programlisting">#define <a href="#XML_MAX_NAMELEN">XML_MAX_NAMELEN</a>;
</pre><p>Identifiers can be longer, but this will be more costly at runtime.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_MAX_NAME_LENGTH">Macro </a>XML_MAX_NAME_LENGTH</h3><pre class="programlisting">#define <a href="#XML_MAX_NAME_LENGTH">XML_MAX_NAME_LENGTH</a>;
</pre><p>Maximum size allowed for a markup identitier This is not a limitation of the parser but a safety boundary feature, use <a href="libxml2-parser.html#XML_PARSE_HUGE">XML_PARSE_HUGE</a> option to override it. Note that with the use of parsing dictionaries overriding the limit may result in more runtime memory usage in face of "unfriendly' content Introduced in 2.9.0</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_MAX_TEXT_LENGTH">Macro </a>XML_MAX_TEXT_LENGTH</h3><pre class="programlisting">#define <a href="#XML_MAX_TEXT_LENGTH">XML_MAX_TEXT_LENGTH</a>;
</pre><p>Maximum size allowed for a single text node when building a tree. This is not a limitation of the parser but a safety boundary feature, use <a href="libxml2-parser.html#XML_PARSE_HUGE">XML_PARSE_HUGE</a> option to override it. Introduced in 2.9.0</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_SUBSTITUTE_BOTH">Macro </a>XML_SUBSTITUTE_BOTH</h3><pre class="programlisting">#define <a href="#XML_SUBSTITUTE_BOTH">XML_SUBSTITUTE_BOTH</a>;
</pre><p>Both general and parameter entities need to be substituted.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_SUBSTITUTE_NONE">Macro </a>XML_SUBSTITUTE_NONE</h3><pre class="programlisting">#define <a href="#XML_SUBSTITUTE_NONE">XML_SUBSTITUTE_NONE</a>;
</pre><p>If no entities need to be substituted.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_SUBSTITUTE_PEREF">Macro </a>XML_SUBSTITUTE_PEREF</h3><pre class="programlisting">#define <a href="#XML_SUBSTITUTE_PEREF">XML_SUBSTITUTE_PEREF</a>;
</pre><p>Whether parameter entities need to be substituted.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XML_SUBSTITUTE_REF">Macro </a>XML_SUBSTITUTE_REF</h3><pre class="programlisting">#define <a href="#XML_SUBSTITUTE_REF">XML_SUBSTITUTE_REF</a>;
</pre><p>Whether general entities need to be substituted.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlEntityReferenceFunc"/>Function type xmlEntityReferenceFunc</h3><pre class="programlisting">void	xmlEntityReferenceFunc		(<a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> ent, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> firstNode, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> lastNode)<br/>
</pre><p>Callback function used when one needs to be able to track back the provenance of a chunk of nodes inherited from an entity replacement.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ent</tt></i>:</span></td><td>the entity</td></tr><tr><td><span class="term"><i><tt>firstNode</tt></i>:</span></td><td>the fist node in the chunk</td></tr><tr><td><span class="term"><i><tt>lastNode</tt></i>:</span></td><td>the last nod in the chunk</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserMaxDepth">Variable </a>xmlParserMaxDepth</h3><pre class="programlisting">unsigned int xmlParserMaxDepth;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStringComment">Variable </a>xmlStringComment</h3><pre class="programlisting">const xmlCharxmlStringComment[] xmlStringComment;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStringText">Variable </a>xmlStringText</h3><pre class="programlisting">const xmlCharxmlStringText[] xmlStringText;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStringTextNoenc">Variable </a>xmlStringTextNoenc</h3><pre class="programlisting">const xmlCharxmlStringTextNoenc[] xmlStringTextNoenc;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="htmlCreateFileParserCtxt"/>htmlCreateFileParserCtxt ()</h3><pre class="programlisting"><a href="libxml2-HTMLparser.html#htmlParserCtxtPtr">htmlParserCtxtPtr</a>	htmlCreateFileParserCtxt	(const char * filename, <br/>							 const char * encoding)<br/>
</pre><p>Create a parser context for a file content. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>a free form C string describing the HTML document encoding, or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="htmlInitAutoClose"/>htmlInitAutoClose ()</h3><pre class="programlisting">void	htmlInitAutoClose		(void)<br/>
</pre><p>Initialize the htmlStartCloseIndex for fast lookup of closing tags names. This is not reentrant. Call xmlInitParser() once before processing in case of use in multithreaded programs.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="inputPop"/>inputPop ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	inputPop	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Pops the top parser input from the input stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the input just removed</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="inputPush"/>inputPush ()</h3><pre class="programlisting">int	inputPush			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> value)<br/>
</pre><p>Pushes a new parser input on top of the input stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the parser input</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, the index in the stack otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="namePop"/>namePop ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	namePop			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Pops the top element name from the name stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the name just removed</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="namePush"/>namePush ()</h3><pre class="programlisting">int	namePush			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value)<br/>
</pre><p>Pushes a new element name on top of the name stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the element name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, the index in the stack otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="nodePop"/>nodePop ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	nodePop			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Pops the top element node from the node stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node just removed</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="nodePush"/>nodePush ()</h3><pre class="programlisting">int	nodePush			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> value)<br/>
</pre><p>Pushes a new element node on top of the node stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the element node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error, the index in the stack otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCheckLanguageID"/>xmlCheckLanguageID ()</h3><pre class="programlisting">int	xmlCheckLanguageID		(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * lang)<br/>
</pre><p>Checks that the value conforms to the LanguageID production: NOTE: this is somewhat deprecated, those productions were removed from the XML Second edition. [33] LanguageID ::= Langcode ('-' Subcode)* [34] Langcode ::= ISO639Code | IanaCode | UserCode [35] ISO639Code ::= ([a-z] | [A-Z]) ([a-z] | [A-Z]) [36] IanaCode ::= ('i' | 'I') '-' ([a-z] | [A-Z])+ [37] UserCode ::= ('x' | 'X') '-' ([a-z] | [A-Z])+ [38] Subcode ::= ([a-z] | [A-Z])+ The current REC <a href="libxml2-SAX.html#reference">reference</a> the sucessors of RFC 1766, currently 5646 http://www.rfc-editor.org/rfc/rfc5646.txt langtag = language ["-" script] ["-" region] *("-" variant) *("-" extension) ["-" privateuse] language = 2*3ALPHA ; shortest ISO 639 code ["-" extlang] ; sometimes followed by ; extended language subtags / 4ALPHA ; or reserved for future use / 5*8ALPHA ; or registered language subtag extlang = 3ALPHA ; selected ISO 639 codes *2("-" 3ALPHA) ; permanently reserved script = 4ALPHA ; ISO 15924 code region = 2ALPHA ; ISO 3166-1 code / 3DIGIT ; UN M.49 code variant = 5*8alphanum ; registered variants / (DIGIT 3alphanum) extension = singleton 1*("-" (2*8alphanum)) ; Single alphanumerics ; "x" reserved for private use singleton = DIGIT ; 0 - 9 / %x41-57 ; A - W / %x59-5A ; Y - Z / %x61-77 ; a - w / %x79-7A ; y - z it sounds right to still allow Irregular i-xxx IANA and user codes too The parser below doesn't try to cope with extension or privateuse that could be added but that's not interoperable anyway</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>lang</tt></i>:</span></td><td>pointer to the string value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if correct 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCopyChar"/>xmlCopyChar ()</h3><pre class="programlisting">int	xmlCopyChar			(int len, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * out, <br/>					 int val)<br/>
</pre><p>append the char value in the array</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>Ignored, compatibility</td></tr><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>pointer to an array of <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a></td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the char value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> written</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCopyCharMultiByte"/>xmlCopyCharMultiByte ()</h3><pre class="programlisting">int	xmlCopyCharMultiByte		(<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * out, <br/>					 int val)<br/>
</pre><p>append the char value in the array</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>pointer to an array of <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a></td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the char value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> written</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCreateEntityParserCtxt"/>xmlCreateEntityParserCtxt ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlCreateEntityParserCtxt	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * URL, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ID, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * base)<br/>
</pre><p>Create a parser context for an external entity Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the entity URL</td></tr><tr><td><span class="term"><i><tt>ID</tt></i>:</span></td><td>the entity PUBLIC ID</td></tr><tr><td><span class="term"><i><tt>base</tt></i>:</span></td><td>a possible base for the target URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCreateFileParserCtxt"/>xmlCreateFileParserCtxt ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlCreateFileParserCtxt	(const char * filename)<br/>
</pre><p>Create a parser context for a file content. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCreateMemoryParserCtxt"/>xmlCreateMemoryParserCtxt ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlCreateMemoryParserCtxt	(const char * buffer, <br/>							 int size)<br/>
</pre><p>Create a parser context for an XML in-memory document.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a pointer to a char array</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the size of the array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCreateURLParserCtxt"/>xmlCreateURLParserCtxt ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a>	xmlCreateURLParserCtxt	(const char * filename, <br/>						 int options)<br/>
</pre><p>Create a parser context for a file or URL content. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time and for file accesses</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename or URL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a combination of <a href="libxml2-parser.html#xmlParserOption">xmlParserOption</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser context or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCurrentChar"/>xmlCurrentChar ()</h3><pre class="programlisting">int	xmlCurrentChar			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 int * len)<br/>
</pre><p>The current char value, if using UTF-8 this may actually span multiple bytes in the input buffer. Implement the end of line normalization: 2.11 End-of-Line Handling Wherever an external parsed entity or the literal entity value of an internal parsed entity contains either the literal two-character sequence "#xD#xA" or a standalone literal #xD, an XML processor must pass to the application the single character #xA. This behavior can conveniently be produced by normalizing all line breaks to #xA on input, before parsing.)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XML parser context</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>pointer to the length of the char read</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current char value and its length</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDecodeEntities"/>xmlDecodeEntities ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlDecodeEntities	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 int len, <br/>					 int what, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end2, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end3)<br/>
</pre><p>This function is deprecated, we now always process entities content through <a href="libxml2-parserInternals.html#xmlStringDecodeEntities">xmlStringDecodeEntities</a> TODO: remove it in next major release. [67] Reference ::= EntityRef | CharRef [69] PEReference ::= '%' Name ';'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the parser context</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the len to decode (in bytes !), -1 for no size limit</td></tr><tr><td><span class="term"><i><tt>what</tt></i>:</span></td><td>combination of <a href="libxml2-parserInternals.html#XML_SUBSTITUTE_REF">XML_SUBSTITUTE_REF</a> and <a href="libxml2-parserInternals.html#XML_SUBSTITUTE_PEREF">XML_SUBSTITUTE_PEREF</a></td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>end2</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>end3</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>A newly allocated string with the substitution done. The caller must deallocate it !</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlErrMemory"/>xmlErrMemory ()</h3><pre class="programlisting">void	xmlErrMemory			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const char * extra)<br/>
</pre><p>Handle a redefinition of <a href="libxml2-SAX.html#attribute">attribute</a> error</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>extra</tt></i>:</span></td><td>extra informations</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFreeInputStream"/>xmlFreeInputStream ()</h3><pre class="programlisting">void	xmlFreeInputStream		(<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input)<br/>
</pre><p>Free up an input stream.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a></td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlHandleEntity"/>xmlHandleEntity ()</h3><pre class="programlisting">void	xmlHandleEntity			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> entity)<br/>
</pre><p>Default handling of defined entities, when should we define a new input stream ? When do we just handle that as a set of chars ? OBSOLETE: to be removed at some point.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>entity</tt></i>:</span></td><td>an XML entity pointer.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsLetter"/>xmlIsLetter ()</h3><pre class="programlisting">int	xmlIsLetter			(int c)<br/>
</pre><p>Check whether the character is allowed by the production [84] Letter ::= BaseChar | Ideographic</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>an unicode character (int)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if not, non-zero otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNamespaceParseNCName"/>xmlNamespaceParseNCName ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlNamespaceParseNCName	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML namespace name. TODO: this seems not in use anymore, the namespace handling is done on top of the SAX interfaces, i.e. not on raw input. [NS 3] NCName ::= (Letter | '_') (NCNameChar)* [NS 4] NCNameChar ::= Letter | Digit | '.' | '-' | '_' | CombiningChar | Extender</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the namespace name or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNamespaceParseNSDef"/>xmlNamespaceParseNSDef ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlNamespaceParseNSDef	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse a namespace prefix declaration TODO: this seems not in use anymore, the namespace handling is done on top of the SAX interfaces, i.e. not on raw input. [NS 1] NSDef ::= PrefixDef Eq SystemLiteral [NS 2] PrefixDef ::= 'xmlns' (':' NCName)?</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the namespace name</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNamespaceParseQName"/>xmlNamespaceParseQName ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlNamespaceParseQName	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** prefix)<br/>
</pre><p>TODO: this seems not in use anymore, the namespace handling is done on top of the SAX interfaces, i.e. not on raw input. parse an XML qualified name [NS 5] QName ::= (Prefix ':')? LocalPart [NS 6] Prefix ::= NCName [NS 7] LocalPart ::= NCName</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>a <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> **</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the local part, and prefix is updated to get the Prefix if any.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewEntityInputStream"/>xmlNewEntityInputStream ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlNewEntityInputStream	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> entity)<br/>
</pre><p>Create a new input stream based on an <a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a></p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>entity</tt></i>:</span></td><td>an Entity pointer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new input stream or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewInputFromFile"/>xmlNewInputFromFile ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlNewInputFromFile	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 const char * filename)<br/>
</pre><p>Create a new input stream based on a file or an URL.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename to use as entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new input stream or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewInputStream"/>xmlNewInputStream ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlNewInputStream	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Create a new input stream structure.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new input stream or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewStringInputStream"/>xmlNewStringInputStream ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlNewStringInputStream	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * buffer)<br/>
</pre><p>Create a new input stream based on a memory buffer.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>an memory buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new input stream</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNextChar"/>xmlNextChar ()</h3><pre class="programlisting">void	xmlNextChar			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Skip to the next char input char.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseAttValue"/>xmlParseAttValue ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseAttValue	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse a value for an <a href="libxml2-SAX.html#attribute">attribute</a> Note: the parser won't do substitution of entities here, this will be handled later in <a href="libxml2-tree.html#xmlStringGetNodeList">xmlStringGetNodeList</a> [10] AttValue ::= '"' ([^&lt;&amp;"] | Reference)* '"' | "'" ([^&lt;&amp;'] | Reference)* "'" 3.3.3 Attribute-Value Normalization: Before the value of an <a href="libxml2-SAX.html#attribute">attribute</a> is passed to the application or checked for validity, the XML processor must normalize it as follows: - a character <a href="libxml2-SAX.html#reference">reference</a> is processed by appending the referenced character to the <a href="libxml2-SAX.html#attribute">attribute</a> value - an entity <a href="libxml2-SAX.html#reference">reference</a> is processed by recursively processing the replacement text of the entity - a whitespace character (#x20, #xD, #xA, #x9) is processed by appending #x20 to the normalized value, except that only a single #x20 is appended for a "#xD#xA" sequence that is part of an external parsed entity or the literal entity value of an internal parsed entity - other <a href="libxml2-SAX.html#characters">characters</a> are processed by appending them to the normalized value If the declared value is not CDATA, then the XML processor must further process the normalized <a href="libxml2-SAX.html#attribute">attribute</a> value by discarding any leading and trailing space (#x20) characters, and by replacing sequences of space (#x20) <a href="libxml2-SAX.html#characters">characters</a> by a single space (#x20) character. All attributes for which no declaration has been read should be treated by a non-validating parser as if declared CDATA.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the AttValue parsed or NULL. The value has to be freed by the caller.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseAttribute"/>xmlParseAttribute ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseAttribute	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** value)<br/>
</pre><p>parse an <a href="libxml2-SAX.html#attribute">attribute</a> [41] Attribute ::= Name Eq AttValue [ WFC: No External Entity References ] Attribute values cannot contain direct or indirect entity references to external entities. [ WFC: No &lt; in Attribute Values ] The replacement text of any entity referred to directly or indirectly in an <a href="libxml2-SAX.html#attribute">attribute</a> value (other than "&amp;lt;") must not contain a &lt;. [ VC: Attribute Value Type ] The <a href="libxml2-SAX.html#attribute">attribute</a> must have been declared; the value must be of the type declared for it. [25] Eq ::= S? '=' S? With namespace: [NS 11] Attribute ::= QName Eq AttValue Also the case QName == xmlns:??? is handled independently as a namespace definition.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>a <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** used to store the value of the <a href="libxml2-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-SAX.html#attribute">attribute</a> name, and the value in *value.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseAttributeListDecl"/>xmlParseAttributeListDecl ()</h3><pre class="programlisting">void	xmlParseAttributeListDecl	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>: parse the Attribute list def for an element [52] AttlistDecl ::= '&lt;!ATTLIST' S Name AttDef* S? '&gt;' [53] AttDef ::= S Name S AttType S DefaultDecl</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseAttributeType"/>xmlParseAttributeType ()</h3><pre class="programlisting">int	xmlParseAttributeType		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> * tree)<br/>
</pre><p>parse the Attribute list def for an element [54] AttType ::= StringType | TokenizedType | EnumeratedType [55] StringType ::= 'CDATA' [56] TokenizedType ::= 'ID' | 'IDREF' | 'IDREFS' | 'ENTITY' | 'ENTITIES' | 'NMTOKEN' | 'NMTOKENS' Validity constraints for <a href="libxml2-SAX.html#attribute">attribute</a> values syntax are checked in xmlValidateAttributeValue() [ VC: ID ] Values of type ID must match the Name production. A name must not appear more than once in an XML document as a value of this type; i.e., ID values must uniquely identify the elements which bear them. [ VC: One ID per Element Type ] No element type may have more than one ID <a href="libxml2-SAX.html#attribute">attribute</a> specified. [ VC: ID Attribute Default ] An ID <a href="libxml2-SAX.html#attribute">attribute</a> must have a declared default of #IMPLIED or #REQUIRED. [ VC: IDREF ] Values of type IDREF must match the Name production, and values of type IDREFS must match Names; each IDREF Name must match the value of an ID <a href="libxml2-SAX.html#attribute">attribute</a> on some element in the XML document; i.e. IDREF values must match the value of some ID attribute. [ VC: Entity Name ] Values of type ENTITY must match the Name production, values of type ENTITIES must match Names; each Entity Name must match the name of an unparsed entity declared in the DTD. [ VC: Name Token ] Values of type NMTOKEN must match the Nmtoken production; values of type NMTOKENS must match Nmtokens.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>the enumeration tree built while parsing</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-SAX.html#attribute">attribute</a> type</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseCDSect"/>xmlParseCDSect ()</h3><pre class="programlisting">void	xmlParseCDSect			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Parse escaped pure raw content. [18] CDSect ::= CDStart CData CDEnd [19] CDStart ::= '&lt;![CDATA[' [20] Data ::= (Char* - (Char* ']]&gt;' Char*)) [21] CDEnd ::= ']]&gt;'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseCharData"/>xmlParseCharData ()</h3><pre class="programlisting">void	xmlParseCharData		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 int cdata)<br/>
</pre><p>parse a CharData section. if we are within a CDATA section ']]&gt;' marks an end of section. The right angle bracket (&gt;) may be represented using the string "&amp;gt;", and must, for compatibility, be escaped using "&amp;gt;" or a character <a href="libxml2-SAX.html#reference">reference</a> when it appears in the string "]]&gt;" in content, when that string is not marking the end of a CDATA section. [14] CharData ::= [^&lt;&amp;]* - ([^&lt;&amp;]* ']]&gt;' [^&lt;&amp;]*)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>cdata</tt></i>:</span></td><td>int indicating whether we are within a CDATA section</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseCharRef"/>xmlParseCharRef ()</h3><pre class="programlisting">int	xmlParseCharRef			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse Reference declarations [66] CharRef ::= '&amp;#' [0-9]+ ';' | '&amp;#x' [0-9a-fA-F]+ ';' [ WFC: Legal Character ] Characters referred to using character references must match the production for Char.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the value parsed (as an int), 0 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseComment"/>xmlParseComment ()</h3><pre class="programlisting">void	xmlParseComment			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Skip an XML (SGML) <a href="libxml2-SAX.html#comment">comment</a> &lt;!-- .... --&gt; The spec says that "For compatibility, the string "--" (double-hyphen) must not occur within comments. " [15] Comment ::= '&lt;!--' ((Char - '-') | ('-' (Char - '-')))* '--&gt;'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseContent"/>xmlParseContent ()</h3><pre class="programlisting">void	xmlParseContent			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Parse a content: [43] content ::= (element | CharData | Reference | CDSect | PI | Comment)*</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseDefaultDecl"/>xmlParseDefaultDecl ()</h3><pre class="programlisting">int	xmlParseDefaultDecl		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** value)<br/>
</pre><p>Parse an <a href="libxml2-SAX.html#attribute">attribute</a> default declaration [60] DefaultDecl ::= '#REQUIRED' | '#IMPLIED' | (('#FIXED' S)? AttValue) [ VC: Required Attribute ] if the default declaration is the keyword #REQUIRED, then the <a href="libxml2-SAX.html#attribute">attribute</a> must be specified for all elements of the type in the attribute-list declaration. [ VC: Attribute Default Legal ] The declared default value must meet the lexical constraints of the declared <a href="libxml2-SAX.html#attribute">attribute</a> type c.f. xmlValidateAttributeDecl() [ VC: Fixed Attribute Default ] if an <a href="libxml2-SAX.html#attribute">attribute</a> has a default value declared with the #FIXED keyword, instances of that <a href="libxml2-SAX.html#attribute">attribute</a> must match the default value. [ WFC: No &lt; in Attribute Values ] handled in xmlParseAttValue()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>Receive a possible fixed default value for the <a href="libxml2-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>XML_ATTRIBUTE_NONE, XML_ATTRIBUTE_REQUIRED, <a href="libxml2-tree.html#XML_ATTRIBUTE_IMPLIED">XML_ATTRIBUTE_IMPLIED</a> or XML_ATTRIBUTE_FIXED.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseDocTypeDecl"/>xmlParseDocTypeDecl ()</h3><pre class="programlisting">void	xmlParseDocTypeDecl		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse a DOCTYPE declaration [28] doctypedecl ::= '&lt;!DOCTYPE' S Name (S ExternalID)? S? ('[' (markupdecl | PEReference | S)* ']' S?)? '&gt;' [ VC: Root Element Type ] The Name in the document type declaration must match the element type of the root element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseElement"/>xmlParseElement ()</h3><pre class="programlisting">void	xmlParseElement			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML element, this is highly recursive [39] element ::= EmptyElemTag | STag content ETag [ WFC: Element Type Match ] The Name in an element's end-tag must match the element type in the start-tag.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseElementChildrenContentDecl"/>xmlParseElementChildrenContentDecl ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a>	xmlParseElementChildrenContentDecl	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>							 int inputchk)<br/>
</pre><p>parse the declaration for a Mixed Element content The leading '(' and spaces have been skipped in <a href="libxml2-parserInternals.html#xmlParseElementContentDecl">xmlParseElementContentDecl</a> [47] children ::= (choice | seq) ('?' | '*' | '+')? [48] cp ::= (Name | choice | seq) ('?' | '*' | '+')? [49] choice ::= '(' S? cp ( S? '|' S? cp )* S? ')' [50] seq ::= '(' S? cp ( S? ',' S? cp )* S? ')' [ VC: Proper Group/PE Nesting ] applies to [49] and [50] TODO Parameter-entity replacement text must be properly nested with parenthesized groups. That is to say, if either of the opening or closing parentheses in a choice, seq, or Mixed construct is contained in the replacement text for a parameter entity, both must be contained in the same replacement text. For interoperability, if a parameter-entity <a href="libxml2-SAX.html#reference">reference</a> appears in a choice, seq, or Mixed construct, its replacement text should not be empty, and neither the first nor last non-blank character of the replacement text should be a connector (| or ,).</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>inputchk</tt></i>:</span></td><td>the input used for the current entity, needed for boundary checks</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the tree of <a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> describing the element hierarchy.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseElementContentDecl"/>xmlParseElementContentDecl ()</h3><pre class="programlisting">int	xmlParseElementContentDecl	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 <a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> * result)<br/>
</pre><p>parse the declaration for an Element content either Mixed or Children, the cases EMPTY and ANY are handled directly in <a href="libxml2-parserInternals.html#xmlParseElementDecl">xmlParseElementDecl</a> [46] contentspec ::= 'EMPTY' | 'ANY' | Mixed | children</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the element being defined.</td></tr><tr><td><span class="term"><i><tt>result</tt></i>:</span></td><td>the Element Content pointer will be stored here if any</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the type of element content XML_ELEMENT_TYPE_xxx</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseElementDecl"/>xmlParseElementDecl ()</h3><pre class="programlisting">int	xmlParseElementDecl		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an Element declaration. [45] elementdecl ::= '&lt;!ELEMENT' S Name S contentspec S? '&gt;' [ VC: Unique Element Type Declaration ] No element type may be declared more than once</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the type of the element, or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseElementMixedContentDecl"/>xmlParseElementMixedContentDecl ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a>	xmlParseElementMixedContentDecl	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>							 int inputchk)<br/>
</pre><p>parse the declaration for a Mixed Element content The leading '(' and spaces have been skipped in <a href="libxml2-parserInternals.html#xmlParseElementContentDecl">xmlParseElementContentDecl</a> [51] Mixed ::= '(' S? '#PCDATA' (S? '|' S? Name)* S? ')*' | '(' S? '#PCDATA' S? ')' [ VC: Proper Group/PE Nesting ] applies to [51] too (see [49]) [ VC: No Duplicate Types ] The same name must not appear more than once in a single mixed-content declaration.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>inputchk</tt></i>:</span></td><td>the input used for the current entity, needed for boundary checks</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the list of the <a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> describing the element choices</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseEncName"/>xmlParseEncName ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseEncName		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse the XML encoding name [81] EncName ::= [A-Za-z] ([A-Za-z0-9._] | '-')*</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the encoding name value or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseEncodingDecl"/>xmlParseEncodingDecl ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseEncodingDecl	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse the XML encoding declaration [80] EncodingDecl ::= S 'encoding' Eq ('"' EncName '"' | "'" EncName "'") this setups the conversion filters.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the encoding value or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseEndTag"/>xmlParseEndTag ()</h3><pre class="programlisting">void	xmlParseEndTag			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an end of tag [42] ETag ::= '&lt;/' Name S? '&gt;' With namespace [NS 9] ETag ::= '&lt;/' QName S? '&gt;'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseEntityDecl"/>xmlParseEntityDecl ()</h3><pre class="programlisting">void	xmlParseEntityDecl		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse &lt;!ENTITY declarations [70] EntityDecl ::= GEDecl | PEDecl [71] GEDecl ::= '&lt;!ENTITY' S Name S EntityDef S? '&gt;' [72] PEDecl ::= '&lt;!ENTITY' S '%' S Name S PEDef S? '&gt;' [73] EntityDef ::= EntityValue | (ExternalID NDataDecl?) [74] PEDef ::= EntityValue | ExternalID [76] NDataDecl ::= S 'NDATA' S Name [ VC: Notation Declared ] The Name must match the declared name of a notation.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseEntityRef"/>xmlParseEntityRef ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a>	xmlParseEntityRef	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse ENTITY references declarations [68] EntityRef ::= '&amp;' Name ';' [ WFC: Entity Declared ] In a document without any DTD, a document with only an internal DTD subset which contains no parameter entity references, or a document with "standalone='yes'", the Name given in the entity <a href="libxml2-SAX.html#reference">reference</a> must match that in an entity declaration, except that well-formed documents need not declare any of the following entities: amp, lt, gt, apos, quot. The declaration of a parameter entity must precede any <a href="libxml2-SAX.html#reference">reference</a> to it. Similarly, the declaration of a general entity must precede any <a href="libxml2-SAX.html#reference">reference</a> to it which appears in a default value in an attribute-list declaration. Note that if entities are declared in the external subset or in external parameter entities, a non-validating processor is not obligated to read and process their declarations; for such documents, the rule that an entity must be declared is a well-formedness constraint only if standalone='yes'. [ WFC: Parsed Entity ] An entity <a href="libxml2-SAX.html#reference">reference</a> must not contain the name of an unparsed entity</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found, or NULL otherwise.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseEntityValue"/>xmlParseEntityValue ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseEntityValue	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** orig)<br/>
</pre><p>parse a value for ENTITY declarations [9] EntityValue ::= '"' ([^%&amp;"] | PEReference | Reference)* '"' | "'" ([^%&amp;'] | PEReference | Reference)* "'"</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>orig</tt></i>:</span></td><td>if non-NULL store a copy of the original entity value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the EntityValue parsed with <a href="libxml2-SAX.html#reference">reference</a> substituted or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseEnumeratedType"/>xmlParseEnumeratedType ()</h3><pre class="programlisting">int	xmlParseEnumeratedType		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> * tree)<br/>
</pre><p>parse an Enumerated <a href="libxml2-SAX.html#attribute">attribute</a> type. [57] EnumeratedType ::= NotationType | Enumeration [58] NotationType ::= 'NOTATION' S '(' S? Name (S? '|' S? Name)* S? ')'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>the enumeration tree built while parsing</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td><a href="libxml2-tree.html#XML_ATTRIBUTE_ENUMERATION">XML_ATTRIBUTE_ENUMERATION</a> or <a href="libxml2-tree.html#XML_ATTRIBUTE_NOTATION">XML_ATTRIBUTE_NOTATION</a></td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseEnumerationType"/>xmlParseEnumerationType ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a>	xmlParseEnumerationType	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an Enumeration <a href="libxml2-SAX.html#attribute">attribute</a> type. [59] Enumeration ::= '(' S? Nmtoken (S? '|' S? Nmtoken)* S? ')' [ VC: Enumeration ] Values of this type must match one of the Nmtoken tokens in the declaration</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the enumeration <a href="libxml2-SAX.html#attribute">attribute</a> tree built while parsing</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseExternalID"/>xmlParseExternalID ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseExternalID	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** publicID, <br/>					 int strict)<br/>
</pre><p>Parse an External ID or a Public ID NOTE: Productions [75] and [83] interact badly since [75] can generate 'PUBLIC' S PubidLiteral S SystemLiteral [75] ExternalID ::= 'SYSTEM' S SystemLiteral | 'PUBLIC' S PubidLiteral S SystemLiteral [83] PublicID ::= 'PUBLIC' S PubidLiteral</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>publicID</tt></i>:</span></td><td>a xmlChar** receiving PubidLiteral</td></tr><tr><td><span class="term"><i><tt>strict</tt></i>:</span></td><td>indicate whether we should restrict parsing to only production [75], see NOTE below</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the function returns SystemLiteral and in the second case publicID receives PubidLiteral, is strict is off it is possible to return NULL and have publicID set.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseExternalSubset"/>xmlParseExternalSubset ()</h3><pre class="programlisting">void	xmlParseExternalSubset		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br/>
</pre><p>parse Markup declarations from an external subset [30] extSubset ::= textDecl? extSubsetDecl [31] extSubsetDecl ::= (markupdecl | conditionalSect | PEReference | S) *</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external identifier</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the system identifier (or URL)</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseMarkupDecl"/>xmlParseMarkupDecl ()</h3><pre class="programlisting">void	xmlParseMarkupDecl		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse Markup declarations [29] markupdecl ::= elementdecl | AttlistDecl | EntityDecl | NotationDecl | PI | Comment [ VC: Proper Declaration/PE Nesting ] Parameter-entity replacement text must be properly nested with markup declarations. That is to say, if either the first character or the last character of a markup declaration (markupdecl above) is contained in the replacement text for a parameter-entity reference, both must be contained in the same replacement text. [ WFC: PEs in Internal Subset ] In the internal DTD subset, parameter-entity references can occur only where markup declarations can occur, not within markup declarations. (This does not apply to references that occur in external parameter entities or to the external subset.)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseMisc"/>xmlParseMisc ()</h3><pre class="programlisting">void	xmlParseMisc			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML Misc* optional field. [27] Misc ::= Comment | PI | S</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseName"/>xmlParseName ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseName		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML name. [4] NameChar ::= Letter | Digit | '.' | '-' | '_' | ':' | CombiningChar | Extender [5] Name ::= (Letter | '_' | ':') (NameChar)* [6] Names ::= Name (#x20 Name)*</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the Name parsed or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseNamespace"/>xmlParseNamespace ()</h3><pre class="programlisting">void	xmlParseNamespace		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>xmlParseNamespace: parse specific PI '&lt;?namespace ...' constructs. This is what the older xml-name Working Draft specified, a bunch of other stuff may still rely on it, so support is still here as if it was declared on the root of the Tree:-( TODO: remove from library To be removed at next drop of binary compatibility</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseNmtoken"/>xmlParseNmtoken ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseNmtoken		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML Nmtoken. [7] Nmtoken ::= (NameChar)+ [8] Nmtokens ::= Nmtoken (#x20 Nmtoken)*</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the Nmtoken parsed or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseNotationDecl"/>xmlParseNotationDecl ()</h3><pre class="programlisting">void	xmlParseNotationDecl		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse a notation declaration [82] NotationDecl ::= '&lt;!NOTATION' S Name S (ExternalID | PublicID) S? '&gt;' Hence there is actually 3 choices: 'PUBLIC' S PubidLiteral 'PUBLIC' S PubidLiteral S SystemLiteral and 'SYSTEM' S SystemLiteral See the NOTE on xmlParseExternalID().</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseNotationType"/>xmlParseNotationType ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a>	xmlParseNotationType	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an Notation <a href="libxml2-SAX.html#attribute">attribute</a> type. Note: the leading 'NOTATION' S part has already being parsed... [58] NotationType ::= 'NOTATION' S '(' S? Name (S? '|' S? Name)* S? ')' [ VC: Notation Attributes ] Values of this type must match one of the notation names included in the declaration; all notation names in the declaration must be declared.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the notation <a href="libxml2-SAX.html#attribute">attribute</a> tree built while parsing</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParsePEReference"/>xmlParsePEReference ()</h3><pre class="programlisting">void	xmlParsePEReference		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse PEReference declarations The entity content is handled directly by pushing it's content as a new input stream. [69] PEReference ::= '%' Name ';' [ WFC: No Recursion ] A parsed entity must not contain a recursive <a href="libxml2-SAX.html#reference">reference</a> to itself, either directly or indirectly. [ WFC: Entity Declared ] In a document without any DTD, a document with only an internal DTD subset which contains no parameter entity references, or a document with "standalone='yes'", ... ... The declaration of a parameter entity must precede any <a href="libxml2-SAX.html#reference">reference</a> to it... [ VC: Entity Declared ] In a document with an external subset or external parameter entities with "standalone='no'", ... ... The declaration of a parameter entity must precede any <a href="libxml2-SAX.html#reference">reference</a> to it... [ WFC: In DTD ] Parameter-entity references may only appear in the DTD. NOTE: misleading but this is handled.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParsePI"/>xmlParsePI ()</h3><pre class="programlisting">void	xmlParsePI			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML Processing Instruction. [16] PI ::= '&lt;?' PITarget (S (Char* - (Char* '?&gt;' Char*)))? '?&gt;' The processing is transfered to SAX once parsed.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParsePITarget"/>xmlParsePITarget ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParsePITarget	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse the name of a PI [17] PITarget ::= Name - (('X' | 'x') ('M' | 'm') ('L' | 'l'))</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the PITarget name or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParsePubidLiteral"/>xmlParsePubidLiteral ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParsePubidLiteral	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML public literal [12] PubidLiteral ::= '"' PubidChar* '"' | "'" (PubidChar - "'")* "'"</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the PubidLiteral parsed or NULL.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseQuotedString"/>xmlParseQuotedString ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseQuotedString	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Parse and return a string between quotes or doublequotes TODO: Deprecated, to be removed at next drop of binary compatibility</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the string parser or NULL.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseReference"/>xmlParseReference ()</h3><pre class="programlisting">void	xmlParseReference		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse and handle entity references in content, depending on the SAX interface, this may end-up in a call to character() if this is a CharRef, a predefined entity, if there is no reference() callback. or if the parser was asked to switch to that mode. [67] Reference ::= EntityRef | CharRef</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseSDDecl"/>xmlParseSDDecl ()</h3><pre class="programlisting">int	xmlParseSDDecl			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse the XML standalone declaration [32] SDDecl ::= S 'standalone' Eq (("'" ('yes' | 'no') "'") | ('"' ('yes' | 'no')'"')) [ VC: Standalone Document Declaration ] TODO The standalone document declaration must have the value "no" if any external markup declarations contain declarations of: - attributes with default values, if elements to which these attributes apply appear in the document without specifications of values for these attributes, or - entities (other than amp, lt, gt, apos, quot), if references to those entities appear in the document, or - attributes with values subject to normalization, where the <a href="libxml2-SAX.html#attribute">attribute</a> appears in the document with a value which will change as a result of normalization, or - element types with element content, if white space occurs directly within any instance of those types.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if standalone="yes" 0 if standalone="no" -2 if standalone <a href="libxml2-SAX.html#attribute">attribute</a> is missing or invalid (A standalone value of -2 means that the XML declaration was found, but no value was specified for the standalone attribute).</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseStartTag"/>xmlParseStartTag ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseStartTag	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse a start of tag either for rule element or EmptyElement. In both case we don't parse the tag closing chars. [40] STag ::= '&lt;' Name (S Attribute)* S? '&gt;' [ WFC: Unique Att Spec ] No <a href="libxml2-SAX.html#attribute">attribute</a> name may appear more than once in the same start-tag or empty-element tag. [44] EmptyElemTag ::= '&lt;' Name (S Attribute)* S? '/&gt;' [ WFC: Unique Att Spec ] No <a href="libxml2-SAX.html#attribute">attribute</a> name may appear more than once in the same start-tag or empty-element tag. With namespace: [NS 8] STag ::= '&lt;' QName (S Attribute)* S? '&gt;' [NS 10] EmptyElement ::= '&lt;' QName (S Attribute)* S? '/&gt;'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the element name parsed</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseSystemLiteral"/>xmlParseSystemLiteral ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseSystemLiteral	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML Literal [11] SystemLiteral ::= ('"' [^"]* '"') | ("'" [^']* "'")</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the SystemLiteral parsed or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseTextDecl"/>xmlParseTextDecl ()</h3><pre class="programlisting">void	xmlParseTextDecl		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML declaration header for external entities [77] TextDecl ::= '&lt;?xml' VersionInfo? EncodingDecl S? '?&gt;'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseVersionInfo"/>xmlParseVersionInfo ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseVersionInfo	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse the XML version. [24] VersionInfo ::= S 'version' Eq (' VersionNum ' | " VersionNum ") [25] Eq ::= S? '=' S?</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the version string, e.g. "1.0"</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseVersionNum"/>xmlParseVersionNum ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlParseVersionNum	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse the XML version value. [26] VersionNum ::= '1.' [0-9]+ In practice allow [0-9].[0-9]+ at that level</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the string giving the XML version number, or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseXMLDecl"/>xmlParseXMLDecl ()</h3><pre class="programlisting">void	xmlParseXMLDecl			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>parse an XML declaration header [23] XMLDecl ::= '&lt;?xml' VersionInfo EncodingDecl? SDDecl? S? '?&gt;'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserHandlePEReference"/>xmlParserHandlePEReference ()</h3><pre class="programlisting">void	xmlParserHandlePEReference	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>[69] PEReference ::= '%' Name ';' [ WFC: No Recursion ] A parsed entity must not contain a recursive <a href="libxml2-SAX.html#reference">reference</a> to itself, either directly or indirectly. [ WFC: Entity Declared ] In a document without any DTD, a document with only an internal DTD subset which contains no parameter entity references, or a document with "standalone='yes'", ... ... The declaration of a parameter entity must precede any <a href="libxml2-SAX.html#reference">reference</a> to it... [ VC: Entity Declared ] In a document with an external subset or external parameter entities with "standalone='no'", ... ... The declaration of a parameter entity must precede any <a href="libxml2-SAX.html#reference">reference</a> to it... [ WFC: In DTD ] Parameter-entity references may only appear in the DTD. NOTE: misleading but this is handled. A PEReference may have been detected in the current input stream the handling is done accordingly to http://www.w3.org/TR/REC-xml#entproc i.e. - Included in literal in entity values - Included as Parameter Entity <a href="libxml2-SAX.html#reference">reference</a> within DTDs</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserHandleReference"/>xmlParserHandleReference ()</h3><pre class="programlisting">void	xmlParserHandleReference	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>TODO: Remove, now deprecated ... the test is done directly in the content parsing routines. [67] Reference ::= EntityRef | CharRef [68] EntityRef ::= '&amp;' Name ';' [ WFC: Entity Declared ] the Name given in the entity <a href="libxml2-SAX.html#reference">reference</a> must match that in an entity declaration, except that well-formed documents need not declare any of the following entities: amp, lt, gt, apos, quot. [ WFC: Parsed Entity ] An entity <a href="libxml2-SAX.html#reference">reference</a> must not contain the name of an unparsed entity [66] CharRef ::= '&amp;#' [0-9]+ ';' | '&amp;#x' [0-9a-fA-F]+ ';' A PEReference may have been detected in the current input stream the handling is done accordingly to http://www.w3.org/TR/REC-xml#entproc</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputShrink"/>xmlParserInputShrink ()</h3><pre class="programlisting">void	xmlParserInputShrink		(<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> in)<br/>
</pre><p>This function removes used input for the parser.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an XML parser input</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlPopInput"/>xmlPopInput ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a>	xmlPopInput			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>xmlPopInput: the current input pointed by ctxt-&gt;input came to an end pop it and return the next char.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> in the parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlPushInput"/>xmlPushInput ()</h3><pre class="programlisting">int	xmlPushInput			(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input)<br/>
</pre><p>xmlPushInput: switch to a new input stream which is stacked on top of the previous one(s).</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>an XML parser input fragment (entity, XML fragment ...).</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of error or the index in the input stack</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlScanName"/>xmlScanName ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlScanName		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>Trickery: parse an XML name but without consuming the input flow Needed for rollback cases. Used only when parsing entities references. TODO: seems deprecated now, only used in the default part of <a href="libxml2-parserInternals.html#xmlParserHandleReference">xmlParserHandleReference</a> [4] NameChar ::= Letter | Digit | '.' | '-' | '_' | ':' | CombiningChar | Extender [5] Name ::= (Letter | '_' | ':') (NameChar)* [6] Names ::= Name (S Name)*</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the Name parsed or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSetEntityReferenceFunc"/>xmlSetEntityReferenceFunc ()</h3><pre class="programlisting">void	xmlSetEntityReferenceFunc	(<a href="libxml2-parserInternals.html#xmlEntityReferenceFunc">xmlEntityReferenceFunc</a> func)<br/>
</pre><p>Set the function to call call back when a xml <a href="libxml2-SAX.html#reference">reference</a> has been made</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>A valid function</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSkipBlankChars"/>xmlSkipBlankChars ()</h3><pre class="programlisting">int	xmlSkipBlankChars		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>skip all blanks character found at that point in the input streams. It pops up finished entities in the process if allowable at that point.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of space chars skipped</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSplitQName"/>xmlSplitQName ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlSplitQName		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** prefix)<br/>
</pre><p>parse an UTF8 encoded XML qualified name string [NS 5] QName ::= (Prefix ':')? LocalPart [NS 6] Prefix ::= NCName [NS 7] LocalPart ::= NCName</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>a <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> **</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the local part, and prefix is updated to get the Prefix if any.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStringCurrentChar"/>xmlStringCurrentChar ()</h3><pre class="programlisting">int	xmlStringCurrentChar		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * cur, <br/>					 int * len)<br/>
</pre><p>The current char value, if using UTF-8 this may actually span multiple bytes in the input buffer.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XML parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>pointer to the beginning of the char</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>pointer to the length of the char read</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the current char value and its length</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStringDecodeEntities"/>xmlStringDecodeEntities ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlStringDecodeEntities	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>					 int what, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end2, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end3)<br/>
</pre><p>Takes a entity string content and process to do the adequate substitutions. [67] Reference ::= EntityRef | CharRef [69] PEReference ::= '%' Name ';'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the parser context</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the input string</td></tr><tr><td><span class="term"><i><tt>what</tt></i>:</span></td><td>combination of <a href="libxml2-parserInternals.html#XML_SUBSTITUTE_REF">XML_SUBSTITUTE_REF</a> and <a href="libxml2-parserInternals.html#XML_SUBSTITUTE_PEREF">XML_SUBSTITUTE_PEREF</a></td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>end2</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>end3</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>A newly allocated string with the substitution done. The caller must deallocate it !</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStringLenDecodeEntities"/>xmlStringLenDecodeEntities ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlStringLenDecodeEntities	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>						 int len, <br/>						 int what, <br/>						 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end, <br/>						 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end2, <br/>						 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> end3)<br/>
</pre><p>Takes a entity string content and process to do the adequate substitutions. [67] Reference ::= EntityRef | CharRef [69] PEReference ::= '%' Name ';'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the parser context</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the input string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the string length</td></tr><tr><td><span class="term"><i><tt>what</tt></i>:</span></td><td>combination of <a href="libxml2-parserInternals.html#XML_SUBSTITUTE_REF">XML_SUBSTITUTE_REF</a> and <a href="libxml2-parserInternals.html#XML_SUBSTITUTE_PEREF">XML_SUBSTITUTE_PEREF</a></td></tr><tr><td><span class="term"><i><tt>end</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>end2</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>end3</tt></i>:</span></td><td>an end marker xmlChar, 0 if none</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>A newly allocated string with the substitution done. The caller must deallocate it !</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSwitchEncoding"/>xmlSwitchEncoding ()</h3><pre class="programlisting">int	xmlSwitchEncoding		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>change the input functions when discovering the character encoding of a given entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the parser context</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the encoding value (number)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSwitchInputEncoding"/>xmlSwitchInputEncoding ()</h3><pre class="programlisting">int	xmlSwitchInputEncoding		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler)<br/>
</pre><p>change the input functions when discovering the character encoding of a given entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the parser context</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the input stream</td></tr><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>the encoding handler</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSwitchToEncoding"/>xmlSwitchToEncoding ()</h3><pre class="programlisting">int	xmlSwitchToEncoding		(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler)<br/>
</pre><p>change the input functions when discovering the character encoding of a given entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the parser context</td></tr><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>the encoding handler</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 otherwise</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
