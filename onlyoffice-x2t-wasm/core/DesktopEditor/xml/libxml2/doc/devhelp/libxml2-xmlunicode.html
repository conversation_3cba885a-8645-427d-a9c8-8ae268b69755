<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xmlunicode: Unicode character APIs</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xmlstring.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-xmlversion.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xmlunicode</span>
    </h2>
    <p>xmlunicode - Unicode character APIs</p>
    <p>API for the Unicode character APIs  This file is automatically generated from the UCS description files of the Unicode Character Database</p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">int	<a href="#xmlUCSIsBlockElements">xmlUCSIsBlockElements</a>		(int code);
int	<a href="#xmlUCSIsBopomofo">xmlUCSIsBopomofo</a>		(int code);
int	<a href="#xmlUCSIsDingbats">xmlUCSIsDingbats</a>		(int code);
int	<a href="#xmlUCSIsSuperscriptsandSubscripts">xmlUCSIsSuperscriptsandSubscripts</a>	(int code);
int	<a href="#xmlUCSIsCombiningHalfMarks">xmlUCSIsCombiningHalfMarks</a>	(int code);
int	<a href="#xmlUCSIsTibetan">xmlUCSIsTibetan</a>			(int code);
int	<a href="#xmlUCSIsYiRadicals">xmlUCSIsYiRadicals</a>		(int code);
int	<a href="#xmlUCSIsCombiningMarksforSymbols">xmlUCSIsCombiningMarksforSymbols</a>	(int code);
int	<a href="#xmlUCSIsHangulSyllables">xmlUCSIsHangulSyllables</a>		(int code);
int	<a href="#xmlUCSIsBasicLatin">xmlUCSIsBasicLatin</a>		(int code);
int	<a href="#xmlUCSIsCatSc">xmlUCSIsCatSc</a>			(int code);
int	<a href="#xmlUCSIsCatSo">xmlUCSIsCatSo</a>			(int code);
int	<a href="#xmlUCSIsLimbu">xmlUCSIsLimbu</a>			(int code);
int	<a href="#xmlUCSIsCatSm">xmlUCSIsCatSm</a>			(int code);
int	<a href="#xmlUCSIsCatSk">xmlUCSIsCatSk</a>			(int code);
int	<a href="#xmlUCSIsKhmerSymbols">xmlUCSIsKhmerSymbols</a>		(int code);
int	<a href="#xmlUCSIsMongolian">xmlUCSIsMongolian</a>		(int code);
int	<a href="#xmlUCSIsMalayalam">xmlUCSIsMalayalam</a>		(int code);
int	<a href="#xmlUCSIsMathematicalAlphanumericSymbols">xmlUCSIsMathematicalAlphanumericSymbols</a>	(int code);
int	<a href="#xmlUCSIsThaana">xmlUCSIsThaana</a>			(int code);
int	<a href="#xmlUCSIsMyanmar">xmlUCSIsMyanmar</a>			(int code);
int	<a href="#xmlUCSIsTags">xmlUCSIsTags</a>			(int code);
int	<a href="#xmlUCSIsCJKCompatibilityIdeographs">xmlUCSIsCJKCompatibilityIdeographs</a>	(int code);
int	<a href="#xmlUCSIsTelugu">xmlUCSIsTelugu</a>			(int code);
int	<a href="#xmlUCSIsLowSurrogates">xmlUCSIsLowSurrogates</a>		(int code);
int	<a href="#xmlUCSIsOsmanya">xmlUCSIsOsmanya</a>			(int code);
int	<a href="#xmlUCSIsSyriac">xmlUCSIsSyriac</a>			(int code);
int	<a href="#xmlUCSIsEthiopic">xmlUCSIsEthiopic</a>		(int code);
int	<a href="#xmlUCSIsBoxDrawing">xmlUCSIsBoxDrawing</a>		(int code);
int	<a href="#xmlUCSIsGreekExtended">xmlUCSIsGreekExtended</a>		(int code);
int	<a href="#xmlUCSIsGreekandCoptic">xmlUCSIsGreekandCoptic</a>		(int code);
int	<a href="#xmlUCSIsKannada">xmlUCSIsKannada</a>			(int code);
int	<a href="#xmlUCSIsByzantineMusicalSymbols">xmlUCSIsByzantineMusicalSymbols</a>	(int code);
int	<a href="#xmlUCSIsEnclosedCJKLettersandMonths">xmlUCSIsEnclosedCJKLettersandMonths</a>	(int code);
int	<a href="#xmlUCSIsCJKCompatibilityForms">xmlUCSIsCJKCompatibilityForms</a>	(int code);
int	<a href="#xmlUCSIsCatCs">xmlUCSIsCatCs</a>			(int code);
int	<a href="#xmlUCSIsCJKRadicalsSupplement">xmlUCSIsCJKRadicalsSupplement</a>	(int code);
int	<a href="#xmlUCSIsCatCf">xmlUCSIsCatCf</a>			(int code);
int	<a href="#xmlUCSIsSmallFormVariants">xmlUCSIsSmallFormVariants</a>	(int code);
int	<a href="#xmlUCSIsHangulCompatibilityJamo">xmlUCSIsHangulCompatibilityJamo</a>	(int code);
int	<a href="#xmlUCSIsCatCc">xmlUCSIsCatCc</a>			(int code);
int	<a href="#xmlUCSIsCatCo">xmlUCSIsCatCo</a>			(int code);
int	<a href="#xmlUCSIsCherokee">xmlUCSIsCherokee</a>		(int code);
int	<a href="#xmlUCSIsGothic">xmlUCSIsGothic</a>			(int code);
int	<a href="#xmlUCSIsKhmer">xmlUCSIsKhmer</a>			(int code);
int	<a href="#xmlUCSIsCombiningDiacriticalMarksforSymbols">xmlUCSIsCombiningDiacriticalMarksforSymbols</a>	(int code);
int	<a href="#xmlUCSIsOgham">xmlUCSIsOgham</a>			(int code);
int	<a href="#xmlUCSIsOriya">xmlUCSIsOriya</a>			(int code);
int	<a href="#xmlUCSIsLinearBIdeograms">xmlUCSIsLinearBIdeograms</a>	(int code);
int	<a href="#xmlUCSIsBlock">xmlUCSIsBlock</a>			(int code, <br/>					 const char * block);
int	<a href="#xmlUCSIsBopomofoExtended">xmlUCSIsBopomofoExtended</a>	(int code);
int	<a href="#xmlUCSIsHangulJamo">xmlUCSIsHangulJamo</a>		(int code);
int	<a href="#xmlUCSIsTagbanwa">xmlUCSIsTagbanwa</a>		(int code);
int	<a href="#xmlUCSIsGeneralPunctuation">xmlUCSIsGeneralPunctuation</a>	(int code);
int	<a href="#xmlUCSIsCyrillic">xmlUCSIsCyrillic</a>		(int code);
int	<a href="#xmlUCSIsArrows">xmlUCSIsArrows</a>			(int code);
int	<a href="#xmlUCSIsControlPictures">xmlUCSIsControlPictures</a>		(int code);
int	<a href="#xmlUCSIsCJKUnifiedIdeographs">xmlUCSIsCJKUnifiedIdeographs</a>	(int code);
int	<a href="#xmlUCSIsCatNl">xmlUCSIsCatNl</a>			(int code);
int	<a href="#xmlUCSIsCatNo">xmlUCSIsCatNo</a>			(int code);
int	<a href="#xmlUCSIsYijingHexagramSymbols">xmlUCSIsYijingHexagramSymbols</a>	(int code);
int	<a href="#xmlUCSIsVariationSelectorsSupplement">xmlUCSIsVariationSelectorsSupplement</a>	(int code);
int	<a href="#xmlUCSIsBengali">xmlUCSIsBengali</a>			(int code);
int	<a href="#xmlUCSIsPrivateUse">xmlUCSIsPrivateUse</a>		(int code);
int	<a href="#xmlUCSIsMusicalSymbols">xmlUCSIsMusicalSymbols</a>		(int code);
int	<a href="#xmlUCSIsMiscellaneousSymbols">xmlUCSIsMiscellaneousSymbols</a>	(int code);
int	<a href="#xmlUCSIsCJKCompatibility">xmlUCSIsCJKCompatibility</a>	(int code);
int	<a href="#xmlUCSIsAegeanNumbers">xmlUCSIsAegeanNumbers</a>		(int code);
int	<a href="#xmlUCSIsDevanagari">xmlUCSIsDevanagari</a>		(int code);
int	<a href="#xmlUCSIsSupplementalArrowsA">xmlUCSIsSupplementalArrowsA</a>	(int code);
int	<a href="#xmlUCSIsSupplementalArrowsB">xmlUCSIsSupplementalArrowsB</a>	(int code);
int	<a href="#xmlUCSIsNumberForms">xmlUCSIsNumberForms</a>		(int code);
int	<a href="#xmlUCSIsSpacingModifierLetters">xmlUCSIsSpacingModifierLetters</a>	(int code);
int	<a href="#xmlUCSIsOpticalCharacterRecognition">xmlUCSIsOpticalCharacterRecognition</a>	(int code);
int	<a href="#xmlUCSIsCatPc">xmlUCSIsCatPc</a>			(int code);
int	<a href="#xmlUCSIsCatPf">xmlUCSIsCatPf</a>			(int code);
int	<a href="#xmlUCSIsCyrillicSupplement">xmlUCSIsCyrillicSupplement</a>	(int code);
int	<a href="#xmlUCSIsCatPd">xmlUCSIsCatPd</a>			(int code);
int	<a href="#xmlUCSIsCatPi">xmlUCSIsCatPi</a>			(int code);
int	<a href="#xmlUCSIsCatPo">xmlUCSIsCatPo</a>			(int code);
int	<a href="#xmlUCSIsHighPrivateUseSurrogates">xmlUCSIsHighPrivateUseSurrogates</a>	(int code);
int	<a href="#xmlUCSIsLatinExtendedAdditional">xmlUCSIsLatinExtendedAdditional</a>	(int code);
int	<a href="#xmlUCSIsCatPs">xmlUCSIsCatPs</a>			(int code);
int	<a href="#xmlUCSIsHighSurrogates">xmlUCSIsHighSurrogates</a>		(int code);
int	<a href="#xmlUCSIsLao">xmlUCSIsLao</a>			(int code);
int	<a href="#xmlUCSIsBraillePatterns">xmlUCSIsBraillePatterns</a>		(int code);
int	<a href="#xmlUCSIsDeseret">xmlUCSIsDeseret</a>			(int code);
int	<a href="#xmlUCSIsEnclosedAlphanumerics">xmlUCSIsEnclosedAlphanumerics</a>	(int code);
int	<a href="#xmlUCSIsCombiningDiacriticalMarks">xmlUCSIsCombiningDiacriticalMarks</a>	(int code);
int	<a href="#xmlUCSIsIdeographicDescriptionCharacters">xmlUCSIsIdeographicDescriptionCharacters</a>	(int code);
int	<a href="#xmlUCSIsPrivateUseArea">xmlUCSIsPrivateUseArea</a>		(int code);
int	<a href="#xmlUCSIsCat">xmlUCSIsCat</a>			(int code, <br/>					 const char * cat);
int	<a href="#xmlUCSIsCatLu">xmlUCSIsCatLu</a>			(int code);
int	<a href="#xmlUCSIsCatLt">xmlUCSIsCatLt</a>			(int code);
int	<a href="#xmlUCSIsYiSyllables">xmlUCSIsYiSyllables</a>		(int code);
int	<a href="#xmlUCSIsShavian">xmlUCSIsShavian</a>			(int code);
int	<a href="#xmlUCSIsCatLo">xmlUCSIsCatLo</a>			(int code);
int	<a href="#xmlUCSIsCatLm">xmlUCSIsCatLm</a>			(int code);
int	<a href="#xmlUCSIsCatLl">xmlUCSIsCatLl</a>			(int code);
int	<a href="#xmlUCSIsMiscellaneousTechnical">xmlUCSIsMiscellaneousTechnical</a>	(int code);
int	<a href="#xmlUCSIsUgaritic">xmlUCSIsUgaritic</a>		(int code);
int	<a href="#xmlUCSIsCJKCompatibilityIdeographsSupplement">xmlUCSIsCJKCompatibilityIdeographsSupplement</a>	(int code);
int	<a href="#xmlUCSIsCypriotSyllabary">xmlUCSIsCypriotSyllabary</a>	(int code);
int	<a href="#xmlUCSIsTamil">xmlUCSIsTamil</a>			(int code);
int	<a href="#xmlUCSIsCatC">xmlUCSIsCatC</a>			(int code);
int	<a href="#xmlUCSIsCatN">xmlUCSIsCatN</a>			(int code);
int	<a href="#xmlUCSIsCatL">xmlUCSIsCatL</a>			(int code);
int	<a href="#xmlUCSIsCatM">xmlUCSIsCatM</a>			(int code);
int	<a href="#xmlUCSIsUnifiedCanadianAboriginalSyllabics">xmlUCSIsUnifiedCanadianAboriginalSyllabics</a>	(int code);
int	<a href="#xmlUCSIsCatS">xmlUCSIsCatS</a>			(int code);
int	<a href="#xmlUCSIsCatP">xmlUCSIsCatP</a>			(int code);
int	<a href="#xmlUCSIsSinhala">xmlUCSIsSinhala</a>			(int code);
int	<a href="#xmlUCSIsGeorgian">xmlUCSIsGeorgian</a>		(int code);
int	<a href="#xmlUCSIsCatZ">xmlUCSIsCatZ</a>			(int code);
int	<a href="#xmlUCSIsIPAExtensions">xmlUCSIsIPAExtensions</a>		(int code);
int	<a href="#xmlUCSIsKangxiRadicals">xmlUCSIsKangxiRadicals</a>		(int code);
int	<a href="#xmlUCSIsGreek">xmlUCSIsGreek</a>			(int code);
int	<a href="#xmlUCSIsCatPe">xmlUCSIsCatPe</a>			(int code);
int	<a href="#xmlUCSIsHanunoo">xmlUCSIsHanunoo</a>			(int code);
int	<a href="#xmlUCSIsArmenian">xmlUCSIsArmenian</a>		(int code);
int	<a href="#xmlUCSIsSupplementaryPrivateUseAreaB">xmlUCSIsSupplementaryPrivateUseAreaB</a>	(int code);
int	<a href="#xmlUCSIsSupplementaryPrivateUseAreaA">xmlUCSIsSupplementaryPrivateUseAreaA</a>	(int code);
int	<a href="#xmlUCSIsKatakanaPhoneticExtensions">xmlUCSIsKatakanaPhoneticExtensions</a>	(int code);
int	<a href="#xmlUCSIsLetterlikeSymbols">xmlUCSIsLetterlikeSymbols</a>	(int code);
int	<a href="#xmlUCSIsPhoneticExtensions">xmlUCSIsPhoneticExtensions</a>	(int code);
int	<a href="#xmlUCSIsArabic">xmlUCSIsArabic</a>			(int code);
int	<a href="#xmlUCSIsHebrew">xmlUCSIsHebrew</a>			(int code);
int	<a href="#xmlUCSIsOldItalic">xmlUCSIsOldItalic</a>		(int code);
int	<a href="#xmlUCSIsArabicPresentationFormsA">xmlUCSIsArabicPresentationFormsA</a>	(int code);
int	<a href="#xmlUCSIsCatZp">xmlUCSIsCatZp</a>			(int code);
int	<a href="#xmlUCSIsCatZs">xmlUCSIsCatZs</a>			(int code);
int	<a href="#xmlUCSIsArabicPresentationFormsB">xmlUCSIsArabicPresentationFormsB</a>	(int code);
int	<a href="#xmlUCSIsGeometricShapes">xmlUCSIsGeometricShapes</a>		(int code);
int	<a href="#xmlUCSIsCatZl">xmlUCSIsCatZl</a>			(int code);
int	<a href="#xmlUCSIsTagalog">xmlUCSIsTagalog</a>			(int code);
int	<a href="#xmlUCSIsSpecials">xmlUCSIsSpecials</a>		(int code);
int	<a href="#xmlUCSIsGujarati">xmlUCSIsGujarati</a>		(int code);
int	<a href="#xmlUCSIsKatakana">xmlUCSIsKatakana</a>		(int code);
int	<a href="#xmlUCSIsHalfwidthandFullwidthForms">xmlUCSIsHalfwidthandFullwidthForms</a>	(int code);
int	<a href="#xmlUCSIsLatinExtendedB">xmlUCSIsLatinExtendedB</a>		(int code);
int	<a href="#xmlUCSIsLatinExtendedA">xmlUCSIsLatinExtendedA</a>		(int code);
int	<a href="#xmlUCSIsBuhid">xmlUCSIsBuhid</a>			(int code);
int	<a href="#xmlUCSIsMiscellaneousSymbolsandArrows">xmlUCSIsMiscellaneousSymbolsandArrows</a>	(int code);
int	<a href="#xmlUCSIsTaiLe">xmlUCSIsTaiLe</a>			(int code);
int	<a href="#xmlUCSIsCJKSymbolsandPunctuation">xmlUCSIsCJKSymbolsandPunctuation</a>	(int code);
int	<a href="#xmlUCSIsTaiXuanJingSymbols">xmlUCSIsTaiXuanJingSymbols</a>	(int code);
int	<a href="#xmlUCSIsGurmukhi">xmlUCSIsGurmukhi</a>		(int code);
int	<a href="#xmlUCSIsMathematicalOperators">xmlUCSIsMathematicalOperators</a>	(int code);
int	<a href="#xmlUCSIsAlphabeticPresentationForms">xmlUCSIsAlphabeticPresentationForms</a>	(int code);
int	<a href="#xmlUCSIsCurrencySymbols">xmlUCSIsCurrencySymbols</a>		(int code);
int	<a href="#xmlUCSIsSupplementalMathematicalOperators">xmlUCSIsSupplementalMathematicalOperators</a>	(int code);
int	<a href="#xmlUCSIsCJKUnifiedIdeographsExtensionA">xmlUCSIsCJKUnifiedIdeographsExtensionA</a>	(int code);
int	<a href="#xmlUCSIsKanbun">xmlUCSIsKanbun</a>			(int code);
int	<a href="#xmlUCSIsCJKUnifiedIdeographsExtensionB">xmlUCSIsCJKUnifiedIdeographsExtensionB</a>	(int code);
int	<a href="#xmlUCSIsThai">xmlUCSIsThai</a>			(int code);
int	<a href="#xmlUCSIsRunic">xmlUCSIsRunic</a>			(int code);
int	<a href="#xmlUCSIsCatNd">xmlUCSIsCatNd</a>			(int code);
int	<a href="#xmlUCSIsLatin1Supplement">xmlUCSIsLatin1Supplement</a>	(int code);
int	<a href="#xmlUCSIsLinearBSyllabary">xmlUCSIsLinearBSyllabary</a>	(int code);
int	<a href="#xmlUCSIsHiragana">xmlUCSIsHiragana</a>		(int code);
int	<a href="#xmlUCSIsMiscellaneousMathematicalSymbolsB">xmlUCSIsMiscellaneousMathematicalSymbolsB</a>	(int code);
int	<a href="#xmlUCSIsMiscellaneousMathematicalSymbolsA">xmlUCSIsMiscellaneousMathematicalSymbolsA</a>	(int code);
int	<a href="#xmlUCSIsCatMn">xmlUCSIsCatMn</a>			(int code);
int	<a href="#xmlUCSIsVariationSelectors">xmlUCSIsVariationSelectors</a>	(int code);
int	<a href="#xmlUCSIsCatMc">xmlUCSIsCatMc</a>			(int code);
int	<a href="#xmlUCSIsCatMe">xmlUCSIsCatMe</a>			(int code);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsAegeanNumbers"/>xmlUCSIsAegeanNumbers ()</h3><pre class="programlisting">int	xmlUCSIsAegeanNumbers		(int code)<br/>
</pre><p>Check whether the character is part of AegeanNumbers UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsAlphabeticPresentationForms"/>xmlUCSIsAlphabeticPresentationForms ()</h3><pre class="programlisting">int	xmlUCSIsAlphabeticPresentationForms	(int code)<br/>
</pre><p>Check whether the character is part of AlphabeticPresentationForms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsArabic"/>xmlUCSIsArabic ()</h3><pre class="programlisting">int	xmlUCSIsArabic			(int code)<br/>
</pre><p>Check whether the character is part of Arabic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsArabicPresentationFormsA"/>xmlUCSIsArabicPresentationFormsA ()</h3><pre class="programlisting">int	xmlUCSIsArabicPresentationFormsA	(int code)<br/>
</pre><p>Check whether the character is part of ArabicPresentationForms-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsArabicPresentationFormsB"/>xmlUCSIsArabicPresentationFormsB ()</h3><pre class="programlisting">int	xmlUCSIsArabicPresentationFormsB	(int code)<br/>
</pre><p>Check whether the character is part of ArabicPresentationForms-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsArmenian"/>xmlUCSIsArmenian ()</h3><pre class="programlisting">int	xmlUCSIsArmenian		(int code)<br/>
</pre><p>Check whether the character is part of Armenian UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsArrows"/>xmlUCSIsArrows ()</h3><pre class="programlisting">int	xmlUCSIsArrows			(int code)<br/>
</pre><p>Check whether the character is part of Arrows UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBasicLatin"/>xmlUCSIsBasicLatin ()</h3><pre class="programlisting">int	xmlUCSIsBasicLatin		(int code)<br/>
</pre><p>Check whether the character is part of BasicLatin UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBengali"/>xmlUCSIsBengali ()</h3><pre class="programlisting">int	xmlUCSIsBengali			(int code)<br/>
</pre><p>Check whether the character is part of Bengali UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBlock"/>xmlUCSIsBlock ()</h3><pre class="programlisting">int	xmlUCSIsBlock			(int code, <br/>					 const char * block)<br/>
</pre><p>Check whether the character is part of the UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>block</tt></i>:</span></td><td>UCS block name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if false and -1 on unknown block</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBlockElements"/>xmlUCSIsBlockElements ()</h3><pre class="programlisting">int	xmlUCSIsBlockElements		(int code)<br/>
</pre><p>Check whether the character is part of BlockElements UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBopomofo"/>xmlUCSIsBopomofo ()</h3><pre class="programlisting">int	xmlUCSIsBopomofo		(int code)<br/>
</pre><p>Check whether the character is part of Bopomofo UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBopomofoExtended"/>xmlUCSIsBopomofoExtended ()</h3><pre class="programlisting">int	xmlUCSIsBopomofoExtended	(int code)<br/>
</pre><p>Check whether the character is part of BopomofoExtended UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBoxDrawing"/>xmlUCSIsBoxDrawing ()</h3><pre class="programlisting">int	xmlUCSIsBoxDrawing		(int code)<br/>
</pre><p>Check whether the character is part of BoxDrawing UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBraillePatterns"/>xmlUCSIsBraillePatterns ()</h3><pre class="programlisting">int	xmlUCSIsBraillePatterns		(int code)<br/>
</pre><p>Check whether the character is part of BraillePatterns UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsBuhid"/>xmlUCSIsBuhid ()</h3><pre class="programlisting">int	xmlUCSIsBuhid			(int code)<br/>
</pre><p>Check whether the character is part of Buhid UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsByzantineMusicalSymbols"/>xmlUCSIsByzantineMusicalSymbols ()</h3><pre class="programlisting">int	xmlUCSIsByzantineMusicalSymbols	(int code)<br/>
</pre><p>Check whether the character is part of ByzantineMusicalSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKCompatibility"/>xmlUCSIsCJKCompatibility ()</h3><pre class="programlisting">int	xmlUCSIsCJKCompatibility	(int code)<br/>
</pre><p>Check whether the character is part of CJKCompatibility UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKCompatibilityForms"/>xmlUCSIsCJKCompatibilityForms ()</h3><pre class="programlisting">int	xmlUCSIsCJKCompatibilityForms	(int code)<br/>
</pre><p>Check whether the character is part of CJKCompatibilityForms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKCompatibilityIdeographs"/>xmlUCSIsCJKCompatibilityIdeographs ()</h3><pre class="programlisting">int	xmlUCSIsCJKCompatibilityIdeographs	(int code)<br/>
</pre><p>Check whether the character is part of CJKCompatibilityIdeographs UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKCompatibilityIdeographsSupplement"/>xmlUCSIsCJKCompatibilityIdeographsSupplement ()</h3><pre class="programlisting">int	xmlUCSIsCJKCompatibilityIdeographsSupplement	(int code)<br/>
</pre><p>Check whether the character is part of CJKCompatibilityIdeographsSupplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKRadicalsSupplement"/>xmlUCSIsCJKRadicalsSupplement ()</h3><pre class="programlisting">int	xmlUCSIsCJKRadicalsSupplement	(int code)<br/>
</pre><p>Check whether the character is part of CJKRadicalsSupplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKSymbolsandPunctuation"/>xmlUCSIsCJKSymbolsandPunctuation ()</h3><pre class="programlisting">int	xmlUCSIsCJKSymbolsandPunctuation	(int code)<br/>
</pre><p>Check whether the character is part of CJKSymbolsandPunctuation UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKUnifiedIdeographs"/>xmlUCSIsCJKUnifiedIdeographs ()</h3><pre class="programlisting">int	xmlUCSIsCJKUnifiedIdeographs	(int code)<br/>
</pre><p>Check whether the character is part of CJKUnifiedIdeographs UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKUnifiedIdeographsExtensionA"/>xmlUCSIsCJKUnifiedIdeographsExtensionA ()</h3><pre class="programlisting">int	xmlUCSIsCJKUnifiedIdeographsExtensionA	(int code)<br/>
</pre><p>Check whether the character is part of CJKUnifiedIdeographsExtensionA UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCJKUnifiedIdeographsExtensionB"/>xmlUCSIsCJKUnifiedIdeographsExtensionB ()</h3><pre class="programlisting">int	xmlUCSIsCJKUnifiedIdeographsExtensionB	(int code)<br/>
</pre><p>Check whether the character is part of CJKUnifiedIdeographsExtensionB UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCat"/>xmlUCSIsCat ()</h3><pre class="programlisting">int	xmlUCSIsCat			(int code, <br/>					 const char * cat)<br/>
</pre><p>Check whether the character is part of the UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>cat</tt></i>:</span></td><td>UCS Category name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true, 0 if false and -1 on unknown category</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatC"/>xmlUCSIsCatC ()</h3><pre class="programlisting">int	xmlUCSIsCatC			(int code)<br/>
</pre><p>Check whether the character is part of C UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatCc"/>xmlUCSIsCatCc ()</h3><pre class="programlisting">int	xmlUCSIsCatCc			(int code)<br/>
</pre><p>Check whether the character is part of Cc UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatCf"/>xmlUCSIsCatCf ()</h3><pre class="programlisting">int	xmlUCSIsCatCf			(int code)<br/>
</pre><p>Check whether the character is part of Cf UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatCo"/>xmlUCSIsCatCo ()</h3><pre class="programlisting">int	xmlUCSIsCatCo			(int code)<br/>
</pre><p>Check whether the character is part of Co UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatCs"/>xmlUCSIsCatCs ()</h3><pre class="programlisting">int	xmlUCSIsCatCs			(int code)<br/>
</pre><p>Check whether the character is part of Cs UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatL"/>xmlUCSIsCatL ()</h3><pre class="programlisting">int	xmlUCSIsCatL			(int code)<br/>
</pre><p>Check whether the character is part of L UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatLl"/>xmlUCSIsCatLl ()</h3><pre class="programlisting">int	xmlUCSIsCatLl			(int code)<br/>
</pre><p>Check whether the character is part of Ll UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatLm"/>xmlUCSIsCatLm ()</h3><pre class="programlisting">int	xmlUCSIsCatLm			(int code)<br/>
</pre><p>Check whether the character is part of Lm UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatLo"/>xmlUCSIsCatLo ()</h3><pre class="programlisting">int	xmlUCSIsCatLo			(int code)<br/>
</pre><p>Check whether the character is part of Lo UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatLt"/>xmlUCSIsCatLt ()</h3><pre class="programlisting">int	xmlUCSIsCatLt			(int code)<br/>
</pre><p>Check whether the character is part of Lt UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatLu"/>xmlUCSIsCatLu ()</h3><pre class="programlisting">int	xmlUCSIsCatLu			(int code)<br/>
</pre><p>Check whether the character is part of Lu UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatM"/>xmlUCSIsCatM ()</h3><pre class="programlisting">int	xmlUCSIsCatM			(int code)<br/>
</pre><p>Check whether the character is part of M UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatMc"/>xmlUCSIsCatMc ()</h3><pre class="programlisting">int	xmlUCSIsCatMc			(int code)<br/>
</pre><p>Check whether the character is part of Mc UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatMe"/>xmlUCSIsCatMe ()</h3><pre class="programlisting">int	xmlUCSIsCatMe			(int code)<br/>
</pre><p>Check whether the character is part of Me UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatMn"/>xmlUCSIsCatMn ()</h3><pre class="programlisting">int	xmlUCSIsCatMn			(int code)<br/>
</pre><p>Check whether the character is part of Mn UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatN"/>xmlUCSIsCatN ()</h3><pre class="programlisting">int	xmlUCSIsCatN			(int code)<br/>
</pre><p>Check whether the character is part of N UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatNd"/>xmlUCSIsCatNd ()</h3><pre class="programlisting">int	xmlUCSIsCatNd			(int code)<br/>
</pre><p>Check whether the character is part of Nd UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatNl"/>xmlUCSIsCatNl ()</h3><pre class="programlisting">int	xmlUCSIsCatNl			(int code)<br/>
</pre><p>Check whether the character is part of Nl UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatNo"/>xmlUCSIsCatNo ()</h3><pre class="programlisting">int	xmlUCSIsCatNo			(int code)<br/>
</pre><p>Check whether the character is part of No UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatP"/>xmlUCSIsCatP ()</h3><pre class="programlisting">int	xmlUCSIsCatP			(int code)<br/>
</pre><p>Check whether the character is part of P UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatPc"/>xmlUCSIsCatPc ()</h3><pre class="programlisting">int	xmlUCSIsCatPc			(int code)<br/>
</pre><p>Check whether the character is part of Pc UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatPd"/>xmlUCSIsCatPd ()</h3><pre class="programlisting">int	xmlUCSIsCatPd			(int code)<br/>
</pre><p>Check whether the character is part of Pd UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatPe"/>xmlUCSIsCatPe ()</h3><pre class="programlisting">int	xmlUCSIsCatPe			(int code)<br/>
</pre><p>Check whether the character is part of Pe UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatPf"/>xmlUCSIsCatPf ()</h3><pre class="programlisting">int	xmlUCSIsCatPf			(int code)<br/>
</pre><p>Check whether the character is part of Pf UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatPi"/>xmlUCSIsCatPi ()</h3><pre class="programlisting">int	xmlUCSIsCatPi			(int code)<br/>
</pre><p>Check whether the character is part of Pi UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatPo"/>xmlUCSIsCatPo ()</h3><pre class="programlisting">int	xmlUCSIsCatPo			(int code)<br/>
</pre><p>Check whether the character is part of Po UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatPs"/>xmlUCSIsCatPs ()</h3><pre class="programlisting">int	xmlUCSIsCatPs			(int code)<br/>
</pre><p>Check whether the character is part of Ps UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatS"/>xmlUCSIsCatS ()</h3><pre class="programlisting">int	xmlUCSIsCatS			(int code)<br/>
</pre><p>Check whether the character is part of S UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatSc"/>xmlUCSIsCatSc ()</h3><pre class="programlisting">int	xmlUCSIsCatSc			(int code)<br/>
</pre><p>Check whether the character is part of Sc UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatSk"/>xmlUCSIsCatSk ()</h3><pre class="programlisting">int	xmlUCSIsCatSk			(int code)<br/>
</pre><p>Check whether the character is part of Sk UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatSm"/>xmlUCSIsCatSm ()</h3><pre class="programlisting">int	xmlUCSIsCatSm			(int code)<br/>
</pre><p>Check whether the character is part of Sm UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatSo"/>xmlUCSIsCatSo ()</h3><pre class="programlisting">int	xmlUCSIsCatSo			(int code)<br/>
</pre><p>Check whether the character is part of So UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatZ"/>xmlUCSIsCatZ ()</h3><pre class="programlisting">int	xmlUCSIsCatZ			(int code)<br/>
</pre><p>Check whether the character is part of Z UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatZl"/>xmlUCSIsCatZl ()</h3><pre class="programlisting">int	xmlUCSIsCatZl			(int code)<br/>
</pre><p>Check whether the character is part of Zl UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatZp"/>xmlUCSIsCatZp ()</h3><pre class="programlisting">int	xmlUCSIsCatZp			(int code)<br/>
</pre><p>Check whether the character is part of Zp UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCatZs"/>xmlUCSIsCatZs ()</h3><pre class="programlisting">int	xmlUCSIsCatZs			(int code)<br/>
</pre><p>Check whether the character is part of Zs UCS Category</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCherokee"/>xmlUCSIsCherokee ()</h3><pre class="programlisting">int	xmlUCSIsCherokee		(int code)<br/>
</pre><p>Check whether the character is part of Cherokee UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCombiningDiacriticalMarks"/>xmlUCSIsCombiningDiacriticalMarks ()</h3><pre class="programlisting">int	xmlUCSIsCombiningDiacriticalMarks	(int code)<br/>
</pre><p>Check whether the character is part of CombiningDiacriticalMarks UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCombiningDiacriticalMarksforSymbols"/>xmlUCSIsCombiningDiacriticalMarksforSymbols ()</h3><pre class="programlisting">int	xmlUCSIsCombiningDiacriticalMarksforSymbols	(int code)<br/>
</pre><p>Check whether the character is part of CombiningDiacriticalMarksforSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCombiningHalfMarks"/>xmlUCSIsCombiningHalfMarks ()</h3><pre class="programlisting">int	xmlUCSIsCombiningHalfMarks	(int code)<br/>
</pre><p>Check whether the character is part of CombiningHalfMarks UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCombiningMarksforSymbols"/>xmlUCSIsCombiningMarksforSymbols ()</h3><pre class="programlisting">int	xmlUCSIsCombiningMarksforSymbols	(int code)<br/>
</pre><p>Check whether the character is part of CombiningMarksforSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsControlPictures"/>xmlUCSIsControlPictures ()</h3><pre class="programlisting">int	xmlUCSIsControlPictures		(int code)<br/>
</pre><p>Check whether the character is part of ControlPictures UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCurrencySymbols"/>xmlUCSIsCurrencySymbols ()</h3><pre class="programlisting">int	xmlUCSIsCurrencySymbols		(int code)<br/>
</pre><p>Check whether the character is part of CurrencySymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCypriotSyllabary"/>xmlUCSIsCypriotSyllabary ()</h3><pre class="programlisting">int	xmlUCSIsCypriotSyllabary	(int code)<br/>
</pre><p>Check whether the character is part of CypriotSyllabary UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCyrillic"/>xmlUCSIsCyrillic ()</h3><pre class="programlisting">int	xmlUCSIsCyrillic		(int code)<br/>
</pre><p>Check whether the character is part of Cyrillic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsCyrillicSupplement"/>xmlUCSIsCyrillicSupplement ()</h3><pre class="programlisting">int	xmlUCSIsCyrillicSupplement	(int code)<br/>
</pre><p>Check whether the character is part of CyrillicSupplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsDeseret"/>xmlUCSIsDeseret ()</h3><pre class="programlisting">int	xmlUCSIsDeseret			(int code)<br/>
</pre><p>Check whether the character is part of Deseret UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsDevanagari"/>xmlUCSIsDevanagari ()</h3><pre class="programlisting">int	xmlUCSIsDevanagari		(int code)<br/>
</pre><p>Check whether the character is part of Devanagari UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsDingbats"/>xmlUCSIsDingbats ()</h3><pre class="programlisting">int	xmlUCSIsDingbats		(int code)<br/>
</pre><p>Check whether the character is part of Dingbats UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsEnclosedAlphanumerics"/>xmlUCSIsEnclosedAlphanumerics ()</h3><pre class="programlisting">int	xmlUCSIsEnclosedAlphanumerics	(int code)<br/>
</pre><p>Check whether the character is part of EnclosedAlphanumerics UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsEnclosedCJKLettersandMonths"/>xmlUCSIsEnclosedCJKLettersandMonths ()</h3><pre class="programlisting">int	xmlUCSIsEnclosedCJKLettersandMonths	(int code)<br/>
</pre><p>Check whether the character is part of EnclosedCJKLettersandMonths UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsEthiopic"/>xmlUCSIsEthiopic ()</h3><pre class="programlisting">int	xmlUCSIsEthiopic		(int code)<br/>
</pre><p>Check whether the character is part of Ethiopic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGeneralPunctuation"/>xmlUCSIsGeneralPunctuation ()</h3><pre class="programlisting">int	xmlUCSIsGeneralPunctuation	(int code)<br/>
</pre><p>Check whether the character is part of GeneralPunctuation UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGeometricShapes"/>xmlUCSIsGeometricShapes ()</h3><pre class="programlisting">int	xmlUCSIsGeometricShapes		(int code)<br/>
</pre><p>Check whether the character is part of GeometricShapes UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGeorgian"/>xmlUCSIsGeorgian ()</h3><pre class="programlisting">int	xmlUCSIsGeorgian		(int code)<br/>
</pre><p>Check whether the character is part of Georgian UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGothic"/>xmlUCSIsGothic ()</h3><pre class="programlisting">int	xmlUCSIsGothic			(int code)<br/>
</pre><p>Check whether the character is part of Gothic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGreek"/>xmlUCSIsGreek ()</h3><pre class="programlisting">int	xmlUCSIsGreek			(int code)<br/>
</pre><p>Check whether the character is part of Greek UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGreekExtended"/>xmlUCSIsGreekExtended ()</h3><pre class="programlisting">int	xmlUCSIsGreekExtended		(int code)<br/>
</pre><p>Check whether the character is part of GreekExtended UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGreekandCoptic"/>xmlUCSIsGreekandCoptic ()</h3><pre class="programlisting">int	xmlUCSIsGreekandCoptic		(int code)<br/>
</pre><p>Check whether the character is part of GreekandCoptic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGujarati"/>xmlUCSIsGujarati ()</h3><pre class="programlisting">int	xmlUCSIsGujarati		(int code)<br/>
</pre><p>Check whether the character is part of Gujarati UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsGurmukhi"/>xmlUCSIsGurmukhi ()</h3><pre class="programlisting">int	xmlUCSIsGurmukhi		(int code)<br/>
</pre><p>Check whether the character is part of Gurmukhi UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHalfwidthandFullwidthForms"/>xmlUCSIsHalfwidthandFullwidthForms ()</h3><pre class="programlisting">int	xmlUCSIsHalfwidthandFullwidthForms	(int code)<br/>
</pre><p>Check whether the character is part of HalfwidthandFullwidthForms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHangulCompatibilityJamo"/>xmlUCSIsHangulCompatibilityJamo ()</h3><pre class="programlisting">int	xmlUCSIsHangulCompatibilityJamo	(int code)<br/>
</pre><p>Check whether the character is part of HangulCompatibilityJamo UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHangulJamo"/>xmlUCSIsHangulJamo ()</h3><pre class="programlisting">int	xmlUCSIsHangulJamo		(int code)<br/>
</pre><p>Check whether the character is part of HangulJamo UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHangulSyllables"/>xmlUCSIsHangulSyllables ()</h3><pre class="programlisting">int	xmlUCSIsHangulSyllables		(int code)<br/>
</pre><p>Check whether the character is part of HangulSyllables UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHanunoo"/>xmlUCSIsHanunoo ()</h3><pre class="programlisting">int	xmlUCSIsHanunoo			(int code)<br/>
</pre><p>Check whether the character is part of Hanunoo UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHebrew"/>xmlUCSIsHebrew ()</h3><pre class="programlisting">int	xmlUCSIsHebrew			(int code)<br/>
</pre><p>Check whether the character is part of Hebrew UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHighPrivateUseSurrogates"/>xmlUCSIsHighPrivateUseSurrogates ()</h3><pre class="programlisting">int	xmlUCSIsHighPrivateUseSurrogates	(int code)<br/>
</pre><p>Check whether the character is part of HighPrivateUseSurrogates UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHighSurrogates"/>xmlUCSIsHighSurrogates ()</h3><pre class="programlisting">int	xmlUCSIsHighSurrogates		(int code)<br/>
</pre><p>Check whether the character is part of HighSurrogates UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsHiragana"/>xmlUCSIsHiragana ()</h3><pre class="programlisting">int	xmlUCSIsHiragana		(int code)<br/>
</pre><p>Check whether the character is part of Hiragana UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsIPAExtensions"/>xmlUCSIsIPAExtensions ()</h3><pre class="programlisting">int	xmlUCSIsIPAExtensions		(int code)<br/>
</pre><p>Check whether the character is part of IPAExtensions UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsIdeographicDescriptionCharacters"/>xmlUCSIsIdeographicDescriptionCharacters ()</h3><pre class="programlisting">int	xmlUCSIsIdeographicDescriptionCharacters	(int code)<br/>
</pre><p>Check whether the character is part of IdeographicDescriptionCharacters UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsKanbun"/>xmlUCSIsKanbun ()</h3><pre class="programlisting">int	xmlUCSIsKanbun			(int code)<br/>
</pre><p>Check whether the character is part of Kanbun UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsKangxiRadicals"/>xmlUCSIsKangxiRadicals ()</h3><pre class="programlisting">int	xmlUCSIsKangxiRadicals		(int code)<br/>
</pre><p>Check whether the character is part of KangxiRadicals UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsKannada"/>xmlUCSIsKannada ()</h3><pre class="programlisting">int	xmlUCSIsKannada			(int code)<br/>
</pre><p>Check whether the character is part of Kannada UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsKatakana"/>xmlUCSIsKatakana ()</h3><pre class="programlisting">int	xmlUCSIsKatakana		(int code)<br/>
</pre><p>Check whether the character is part of Katakana UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsKatakanaPhoneticExtensions"/>xmlUCSIsKatakanaPhoneticExtensions ()</h3><pre class="programlisting">int	xmlUCSIsKatakanaPhoneticExtensions	(int code)<br/>
</pre><p>Check whether the character is part of KatakanaPhoneticExtensions UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsKhmer"/>xmlUCSIsKhmer ()</h3><pre class="programlisting">int	xmlUCSIsKhmer			(int code)<br/>
</pre><p>Check whether the character is part of Khmer UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsKhmerSymbols"/>xmlUCSIsKhmerSymbols ()</h3><pre class="programlisting">int	xmlUCSIsKhmerSymbols		(int code)<br/>
</pre><p>Check whether the character is part of KhmerSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLao"/>xmlUCSIsLao ()</h3><pre class="programlisting">int	xmlUCSIsLao			(int code)<br/>
</pre><p>Check whether the character is part of Lao UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLatin1Supplement"/>xmlUCSIsLatin1Supplement ()</h3><pre class="programlisting">int	xmlUCSIsLatin1Supplement	(int code)<br/>
</pre><p>Check whether the character is part of Latin-1Supplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLatinExtendedA"/>xmlUCSIsLatinExtendedA ()</h3><pre class="programlisting">int	xmlUCSIsLatinExtendedA		(int code)<br/>
</pre><p>Check whether the character is part of LatinExtended-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLatinExtendedAdditional"/>xmlUCSIsLatinExtendedAdditional ()</h3><pre class="programlisting">int	xmlUCSIsLatinExtendedAdditional	(int code)<br/>
</pre><p>Check whether the character is part of LatinExtendedAdditional UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLatinExtendedB"/>xmlUCSIsLatinExtendedB ()</h3><pre class="programlisting">int	xmlUCSIsLatinExtendedB		(int code)<br/>
</pre><p>Check whether the character is part of LatinExtended-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLetterlikeSymbols"/>xmlUCSIsLetterlikeSymbols ()</h3><pre class="programlisting">int	xmlUCSIsLetterlikeSymbols	(int code)<br/>
</pre><p>Check whether the character is part of LetterlikeSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLimbu"/>xmlUCSIsLimbu ()</h3><pre class="programlisting">int	xmlUCSIsLimbu			(int code)<br/>
</pre><p>Check whether the character is part of Limbu UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLinearBIdeograms"/>xmlUCSIsLinearBIdeograms ()</h3><pre class="programlisting">int	xmlUCSIsLinearBIdeograms	(int code)<br/>
</pre><p>Check whether the character is part of LinearBIdeograms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLinearBSyllabary"/>xmlUCSIsLinearBSyllabary ()</h3><pre class="programlisting">int	xmlUCSIsLinearBSyllabary	(int code)<br/>
</pre><p>Check whether the character is part of LinearBSyllabary UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsLowSurrogates"/>xmlUCSIsLowSurrogates ()</h3><pre class="programlisting">int	xmlUCSIsLowSurrogates		(int code)<br/>
</pre><p>Check whether the character is part of LowSurrogates UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMalayalam"/>xmlUCSIsMalayalam ()</h3><pre class="programlisting">int	xmlUCSIsMalayalam		(int code)<br/>
</pre><p>Check whether the character is part of Malayalam UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMathematicalAlphanumericSymbols"/>xmlUCSIsMathematicalAlphanumericSymbols ()</h3><pre class="programlisting">int	xmlUCSIsMathematicalAlphanumericSymbols	(int code)<br/>
</pre><p>Check whether the character is part of MathematicalAlphanumericSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMathematicalOperators"/>xmlUCSIsMathematicalOperators ()</h3><pre class="programlisting">int	xmlUCSIsMathematicalOperators	(int code)<br/>
</pre><p>Check whether the character is part of MathematicalOperators UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMiscellaneousMathematicalSymbolsA"/>xmlUCSIsMiscellaneousMathematicalSymbolsA ()</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousMathematicalSymbolsA	(int code)<br/>
</pre><p>Check whether the character is part of MiscellaneousMathematicalSymbols-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMiscellaneousMathematicalSymbolsB"/>xmlUCSIsMiscellaneousMathematicalSymbolsB ()</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousMathematicalSymbolsB	(int code)<br/>
</pre><p>Check whether the character is part of MiscellaneousMathematicalSymbols-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMiscellaneousSymbols"/>xmlUCSIsMiscellaneousSymbols ()</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousSymbols	(int code)<br/>
</pre><p>Check whether the character is part of MiscellaneousSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMiscellaneousSymbolsandArrows"/>xmlUCSIsMiscellaneousSymbolsandArrows ()</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousSymbolsandArrows	(int code)<br/>
</pre><p>Check whether the character is part of MiscellaneousSymbolsandArrows UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMiscellaneousTechnical"/>xmlUCSIsMiscellaneousTechnical ()</h3><pre class="programlisting">int	xmlUCSIsMiscellaneousTechnical	(int code)<br/>
</pre><p>Check whether the character is part of MiscellaneousTechnical UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMongolian"/>xmlUCSIsMongolian ()</h3><pre class="programlisting">int	xmlUCSIsMongolian		(int code)<br/>
</pre><p>Check whether the character is part of Mongolian UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMusicalSymbols"/>xmlUCSIsMusicalSymbols ()</h3><pre class="programlisting">int	xmlUCSIsMusicalSymbols		(int code)<br/>
</pre><p>Check whether the character is part of MusicalSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsMyanmar"/>xmlUCSIsMyanmar ()</h3><pre class="programlisting">int	xmlUCSIsMyanmar			(int code)<br/>
</pre><p>Check whether the character is part of Myanmar UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsNumberForms"/>xmlUCSIsNumberForms ()</h3><pre class="programlisting">int	xmlUCSIsNumberForms		(int code)<br/>
</pre><p>Check whether the character is part of NumberForms UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsOgham"/>xmlUCSIsOgham ()</h3><pre class="programlisting">int	xmlUCSIsOgham			(int code)<br/>
</pre><p>Check whether the character is part of Ogham UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsOldItalic"/>xmlUCSIsOldItalic ()</h3><pre class="programlisting">int	xmlUCSIsOldItalic		(int code)<br/>
</pre><p>Check whether the character is part of OldItalic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsOpticalCharacterRecognition"/>xmlUCSIsOpticalCharacterRecognition ()</h3><pre class="programlisting">int	xmlUCSIsOpticalCharacterRecognition	(int code)<br/>
</pre><p>Check whether the character is part of OpticalCharacterRecognition UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsOriya"/>xmlUCSIsOriya ()</h3><pre class="programlisting">int	xmlUCSIsOriya			(int code)<br/>
</pre><p>Check whether the character is part of Oriya UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsOsmanya"/>xmlUCSIsOsmanya ()</h3><pre class="programlisting">int	xmlUCSIsOsmanya			(int code)<br/>
</pre><p>Check whether the character is part of Osmanya UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsPhoneticExtensions"/>xmlUCSIsPhoneticExtensions ()</h3><pre class="programlisting">int	xmlUCSIsPhoneticExtensions	(int code)<br/>
</pre><p>Check whether the character is part of PhoneticExtensions UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsPrivateUse"/>xmlUCSIsPrivateUse ()</h3><pre class="programlisting">int	xmlUCSIsPrivateUse		(int code)<br/>
</pre><p>Check whether the character is part of PrivateUse UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsPrivateUseArea"/>xmlUCSIsPrivateUseArea ()</h3><pre class="programlisting">int	xmlUCSIsPrivateUseArea		(int code)<br/>
</pre><p>Check whether the character is part of PrivateUseArea UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsRunic"/>xmlUCSIsRunic ()</h3><pre class="programlisting">int	xmlUCSIsRunic			(int code)<br/>
</pre><p>Check whether the character is part of Runic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsShavian"/>xmlUCSIsShavian ()</h3><pre class="programlisting">int	xmlUCSIsShavian			(int code)<br/>
</pre><p>Check whether the character is part of Shavian UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSinhala"/>xmlUCSIsSinhala ()</h3><pre class="programlisting">int	xmlUCSIsSinhala			(int code)<br/>
</pre><p>Check whether the character is part of Sinhala UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSmallFormVariants"/>xmlUCSIsSmallFormVariants ()</h3><pre class="programlisting">int	xmlUCSIsSmallFormVariants	(int code)<br/>
</pre><p>Check whether the character is part of SmallFormVariants UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSpacingModifierLetters"/>xmlUCSIsSpacingModifierLetters ()</h3><pre class="programlisting">int	xmlUCSIsSpacingModifierLetters	(int code)<br/>
</pre><p>Check whether the character is part of SpacingModifierLetters UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSpecials"/>xmlUCSIsSpecials ()</h3><pre class="programlisting">int	xmlUCSIsSpecials		(int code)<br/>
</pre><p>Check whether the character is part of Specials UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSuperscriptsandSubscripts"/>xmlUCSIsSuperscriptsandSubscripts ()</h3><pre class="programlisting">int	xmlUCSIsSuperscriptsandSubscripts	(int code)<br/>
</pre><p>Check whether the character is part of SuperscriptsandSubscripts UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSupplementalArrowsA"/>xmlUCSIsSupplementalArrowsA ()</h3><pre class="programlisting">int	xmlUCSIsSupplementalArrowsA	(int code)<br/>
</pre><p>Check whether the character is part of SupplementalArrows-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSupplementalArrowsB"/>xmlUCSIsSupplementalArrowsB ()</h3><pre class="programlisting">int	xmlUCSIsSupplementalArrowsB	(int code)<br/>
</pre><p>Check whether the character is part of SupplementalArrows-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSupplementalMathematicalOperators"/>xmlUCSIsSupplementalMathematicalOperators ()</h3><pre class="programlisting">int	xmlUCSIsSupplementalMathematicalOperators	(int code)<br/>
</pre><p>Check whether the character is part of SupplementalMathematicalOperators UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSupplementaryPrivateUseAreaA"/>xmlUCSIsSupplementaryPrivateUseAreaA ()</h3><pre class="programlisting">int	xmlUCSIsSupplementaryPrivateUseAreaA	(int code)<br/>
</pre><p>Check whether the character is part of SupplementaryPrivateUseArea-A UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSupplementaryPrivateUseAreaB"/>xmlUCSIsSupplementaryPrivateUseAreaB ()</h3><pre class="programlisting">int	xmlUCSIsSupplementaryPrivateUseAreaB	(int code)<br/>
</pre><p>Check whether the character is part of SupplementaryPrivateUseArea-B UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsSyriac"/>xmlUCSIsSyriac ()</h3><pre class="programlisting">int	xmlUCSIsSyriac			(int code)<br/>
</pre><p>Check whether the character is part of Syriac UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsTagalog"/>xmlUCSIsTagalog ()</h3><pre class="programlisting">int	xmlUCSIsTagalog			(int code)<br/>
</pre><p>Check whether the character is part of Tagalog UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsTagbanwa"/>xmlUCSIsTagbanwa ()</h3><pre class="programlisting">int	xmlUCSIsTagbanwa		(int code)<br/>
</pre><p>Check whether the character is part of Tagbanwa UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsTags"/>xmlUCSIsTags ()</h3><pre class="programlisting">int	xmlUCSIsTags			(int code)<br/>
</pre><p>Check whether the character is part of Tags UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsTaiLe"/>xmlUCSIsTaiLe ()</h3><pre class="programlisting">int	xmlUCSIsTaiLe			(int code)<br/>
</pre><p>Check whether the character is part of TaiLe UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsTaiXuanJingSymbols"/>xmlUCSIsTaiXuanJingSymbols ()</h3><pre class="programlisting">int	xmlUCSIsTaiXuanJingSymbols	(int code)<br/>
</pre><p>Check whether the character is part of TaiXuanJingSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsTamil"/>xmlUCSIsTamil ()</h3><pre class="programlisting">int	xmlUCSIsTamil			(int code)<br/>
</pre><p>Check whether the character is part of Tamil UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsTelugu"/>xmlUCSIsTelugu ()</h3><pre class="programlisting">int	xmlUCSIsTelugu			(int code)<br/>
</pre><p>Check whether the character is part of Telugu UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsThaana"/>xmlUCSIsThaana ()</h3><pre class="programlisting">int	xmlUCSIsThaana			(int code)<br/>
</pre><p>Check whether the character is part of Thaana UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsThai"/>xmlUCSIsThai ()</h3><pre class="programlisting">int	xmlUCSIsThai			(int code)<br/>
</pre><p>Check whether the character is part of Thai UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsTibetan"/>xmlUCSIsTibetan ()</h3><pre class="programlisting">int	xmlUCSIsTibetan			(int code)<br/>
</pre><p>Check whether the character is part of Tibetan UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsUgaritic"/>xmlUCSIsUgaritic ()</h3><pre class="programlisting">int	xmlUCSIsUgaritic		(int code)<br/>
</pre><p>Check whether the character is part of Ugaritic UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsUnifiedCanadianAboriginalSyllabics"/>xmlUCSIsUnifiedCanadianAboriginalSyllabics ()</h3><pre class="programlisting">int	xmlUCSIsUnifiedCanadianAboriginalSyllabics	(int code)<br/>
</pre><p>Check whether the character is part of UnifiedCanadianAboriginalSyllabics UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsVariationSelectors"/>xmlUCSIsVariationSelectors ()</h3><pre class="programlisting">int	xmlUCSIsVariationSelectors	(int code)<br/>
</pre><p>Check whether the character is part of VariationSelectors UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsVariationSelectorsSupplement"/>xmlUCSIsVariationSelectorsSupplement ()</h3><pre class="programlisting">int	xmlUCSIsVariationSelectorsSupplement	(int code)<br/>
</pre><p>Check whether the character is part of VariationSelectorsSupplement UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsYiRadicals"/>xmlUCSIsYiRadicals ()</h3><pre class="programlisting">int	xmlUCSIsYiRadicals		(int code)<br/>
</pre><p>Check whether the character is part of YiRadicals UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsYiSyllables"/>xmlUCSIsYiSyllables ()</h3><pre class="programlisting">int	xmlUCSIsYiSyllables		(int code)<br/>
</pre><p>Check whether the character is part of YiSyllables UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlUCSIsYijingHexagramSymbols"/>xmlUCSIsYijingHexagramSymbols ()</h3><pre class="programlisting">int	xmlUCSIsYijingHexagramSymbols	(int code)<br/>
</pre><p>Check whether the character is part of YijingHexagramSymbols UCS Block</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>code</tt></i>:</span></td><td>UCS code point</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
