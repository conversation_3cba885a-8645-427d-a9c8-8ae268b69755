<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xpathInternals: internal interfaces for XML Path Language implementation</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xpath.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-xpointer.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xpathInternals</span>
    </h2>
    <p>xpathInternals - internal interfaces for XML Path Language implementation</p>
    <p>internal interfaces for XML Path Language implementation used to build new modules on top of XPath like XPointer and XSLT </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">#define <a href="#xmlXPathStackIsExternal">xmlXPathStackIsExternal</a>(ctxt);
#define <a href="#xmlXPathReturnEmptyString">xmlXPathReturnEmptyString</a>(ctxt);
#define <a href="#XP_ERROR0">XP_ERROR0</a>(X);
#define <a href="#xmlXPathSetTypeError">xmlXPathSetTypeError</a>(ctxt);
#define <a href="#xmlXPathReturnEmptyNodeSet">xmlXPathReturnEmptyNodeSet</a>(ctxt);
#define <a href="#xmlXPathReturnTrue">xmlXPathReturnTrue</a>(ctxt);
#define <a href="#xmlXPathReturnBoolean">xmlXPathReturnBoolean</a>(ctxt, val);
#define <a href="#xmlXPathGetContextNode">xmlXPathGetContextNode</a>(ctxt);
#define <a href="#CAST_TO_NUMBER">CAST_TO_NUMBER</a>;
#define <a href="#CHECK_ARITY">CHECK_ARITY</a>(x);
#define <a href="#CHECK_TYPE0">CHECK_TYPE0</a>(typeval);
#define <a href="#CAST_TO_STRING">CAST_TO_STRING</a>;
#define <a href="#xmlXPathReturnExternal">xmlXPathReturnExternal</a>(ctxt, val);
#define <a href="#xmlXPathStackIsNodeSet">xmlXPathStackIsNodeSet</a>(ctxt);
#define <a href="#xmlXPathCheckError">xmlXPathCheckError</a>(ctxt);
#define <a href="#xmlXPathSetError">xmlXPathSetError</a>(ctxt, err);
#define <a href="#CHECK_ERROR">CHECK_ERROR</a>;
#define <a href="#xmlXPathReturnString">xmlXPathReturnString</a>(ctxt, str);
#define <a href="#CAST_TO_BOOLEAN">CAST_TO_BOOLEAN</a>;
#define <a href="#xmlXPathSetArityError">xmlXPathSetArityError</a>(ctxt);
#define <a href="#CHECK_TYPE">CHECK_TYPE</a>(typeval);
#define <a href="#xmlXPathReturnFalse">xmlXPathReturnFalse</a>(ctxt);
#define <a href="#xmlXPathReturnNumber">xmlXPathReturnNumber</a>(ctxt, val);
#define <a href="#CHECK_ERROR0">CHECK_ERROR0</a>;
#define <a href="#xmlXPathGetDocument">xmlXPathGetDocument</a>(ctxt);
#define <a href="#xmlXPathGetError">xmlXPathGetError</a>(ctxt);
#define <a href="#XP_ERROR">XP_ERROR</a>(X);
#define <a href="#xmlXPathEmptyNodeSet">xmlXPathEmptyNodeSet</a>(ns);
#define <a href="#xmlXPathReturnNodeSet">xmlXPathReturnNodeSet</a>(ctxt, ns);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathNodeSetMerge">xmlXPathNodeSetMerge</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> val1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> val2);
void	<a href="#xmlXPathNumberFunction">xmlXPathNumberFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathNsLookup">xmlXPathNsLookup</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathNewNodeSet">xmlXPathNewNodeSet</a>	(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextAncestorOrSelf">xmlXPathNextAncestorOrSelf</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
void	<a href="#xmlXPathNodeSetRemove">xmlXPathNodeSetRemove</a>		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 int val);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathNewNodeSetList">xmlXPathNewNodeSetList</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> val);
int	<a href="#xmlXPathPopBoolean">xmlXPathPopBoolean</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathNewString">xmlXPathNewString</a>	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * val);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathNodeLeadingSorted">xmlXPathNodeLeadingSorted</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextChild">xmlXPathNextChild</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextFollowingSibling">xmlXPathNextFollowingSibling</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathPopString">xmlXPathPopString</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathNamespaceURIFunction">xmlXPathNamespaceURIFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
int	<a href="#xmlXPathCompareValues">xmlXPathCompareValues</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int inf, <br/>					 int strict);
void	<a href="#xmlXPathConcatFunction">xmlXPathConcatFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
int	<a href="#xmlXPathNodeSetContains">xmlXPathNodeSetContains</a>		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val);
void	<a href="#xmlXPatherror">xmlXPatherror</a>			(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 const char * file, <br/>					 int line, <br/>					 int no);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextAncestor">xmlXPathNextAncestor</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
void	<a href="#xmlXPathBooleanFunction">xmlXPathBooleanFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathRoot">xmlXPathRoot</a>			(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathRegisteredNsCleanup">xmlXPathRegisteredNsCleanup</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt);
int	<a href="#xmlXPathIsNodeType">xmlXPathIsNodeType</a>		(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextFollowing">xmlXPathNextFollowing</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
void	<a href="#xmlXPathAddValues">xmlXPathAddValues</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
int	<a href="#xmlXPathRegisterVariable">xmlXPathRegisterVariable</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> value);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#valuePop">valuePop</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathCeilingFunction">xmlXPathCeilingFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathIntersection">xmlXPathIntersection</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2);
void	<a href="#xmlXPathContainsFunction">xmlXPathContainsFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathNodeSetSort">xmlXPathNodeSetSort</a>		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> set);
void	<a href="#xmlXPathStartsWithFunction">xmlXPathStartsWithFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathNodeLeading">xmlXPathNodeLeading</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
void	<a href="#xmlXPathSumFunction">xmlXPathSumFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
int	<a href="#xmlXPathNotEqualValues">xmlXPathNotEqualValues</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
int	<a href="#xmlXPathEvaluatePredicateResult">xmlXPathEvaluatePredicateResult</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> res);
void	<a href="#xmlXPathErr">xmlXPathErr</a>			(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int error);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextPreceding">xmlXPathNextPreceding</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
<a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a>	<a href="#xmlXPathFunctionLookupNS">xmlXPathFunctionLookupNS</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri);
void	<a href="#xmlXPathRegisteredFuncsCleanup">xmlXPathRegisteredFuncsCleanup</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt);
void	<a href="#xmlXPathRegisterAllFunctions">xmlXPathRegisterAllFunctions</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt);
<a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a>	<a href="#xmlXPathFunctionLookup">xmlXPathFunctionLookup</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
void	<a href="#xmlXPathPositionFunction">xmlXPathPositionFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathSubstringBeforeFunction">xmlXPathSubstringBeforeFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathRegisteredVariablesCleanup">xmlXPathRegisteredVariablesCleanup</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathNewFloat">xmlXPathNewFloat</a>	(double val);
int	<a href="#xmlXPathRegisterNs">xmlXPathRegisterNs</a>		(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextParent">xmlXPathNextParent</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
void	<a href="#xmlXPathNodeSetFreeNs">xmlXPathNodeSetFreeNs</a>		(<a href="libxml2-tree.html#xmlNsPtr">xmlNsPtr</a> ns);
int	<a href="#xmlXPathHasSameNodes">xmlXPathHasSameNodes</a>		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathDistinctSorted">xmlXPathDistinctSorted</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes);
int	<a href="#valuePush">valuePush</a>			(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> value);
void	<a href="#xmlXPathSubstringFunction">xmlXPathSubstringFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathStringFunction">xmlXPathStringFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathFloorFunction">xmlXPathFloorFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathIdFunction">xmlXPathIdFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathLastFunction">xmlXPathLastFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathStringLengthFunction">xmlXPathStringLengthFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextSelf">xmlXPathNextSelf</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
void	<a href="#xmlXPathDebugDumpCompExpr">xmlXPathDebugDumpCompExpr</a>	(FILE * output, <br/>					 <a href="libxml2-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> comp, <br/>					 int depth);
void	<a href="#xmlXPathFalseFunction">xmlXPathFalseFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathValueFlipSign">xmlXPathValueFlipSign</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathTranslateFunction">xmlXPathTranslateFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathRegisterFuncLookup">xmlXPathRegisterFuncLookup</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 <a href="libxml2-xpath.html#xmlXPathFuncLookupFunc">xmlXPathFuncLookupFunc</a> f, <br/>					 void * funcCtxt);
double	<a href="#xmlXPathPopNumber">xmlXPathPopNumber</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathRoundFunction">xmlXPathRoundFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathPopNodeSet">xmlXPathPopNodeSet</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathCountFunction">xmlXPathCountFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathParseNCName">xmlXPathParseNCName</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathTrueFunction">xmlXPathTrueFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathNewBoolean">xmlXPathNewBoolean</a>	(int val);
void	<a href="#xmlXPathSubValues">xmlXPathSubValues</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
int	<a href="#xmlXPathEqualValues">xmlXPathEqualValues</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathTrailingSorted">xmlXPathTrailingSorted</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2);
void	<a href="#xmlXPathMultValues">xmlXPathMultValues</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathModValues">xmlXPathModValues</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a>	<a href="#xmlXPathNewParserContext">xmlXPathNewParserContext</a>	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>							 <a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathWrapNodeSet">xmlXPathWrapNodeSet</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> val);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathWrapString">xmlXPathWrapString</a>	(<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * val);
void	<a href="#xmlXPathLangFunction">xmlXPathLangFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
int	<a href="#xmlXPathRegisterFuncNS">xmlXPathRegisterFuncNS</a>		(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri, <br/>					 <a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a> f);
int	<a href="#xmlXPathNodeSetAddNs">xmlXPathNodeSetAddNs</a>		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNsPtr">xmlNsPtr</a> ns);
void	<a href="#xmlXPathLocalNameFunction">xmlXPathLocalNameFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathDifference">xmlXPathDifference</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathLeadingSorted">xmlXPathLeadingSorted</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextPrecedingSibling">xmlXPathNextPrecedingSibling</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
void *	<a href="#xmlXPathPopExternal">xmlXPathPopExternal</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathNormalizeFunction">xmlXPathNormalizeFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
void	<a href="#xmlXPathRegisterVariableLookup">xmlXPathRegisterVariableLookup</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 <a href="libxml2-xpath.html#xmlXPathVariableLookupFunc">xmlXPathVariableLookupFunc</a> f, <br/>					 void * data);
void	<a href="#xmlXPathNodeSetDel">xmlXPathNodeSetDel</a>		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathNodeTrailingSorted">xmlXPathNodeTrailingSorted</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathNewCString">xmlXPathNewCString</a>	(const char * val);
int	<a href="#xmlXPathRegisterFunc">xmlXPathRegisterFunc</a>		(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 <a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a> f);
void	<a href="#xmlXPathSubstringAfterFunction">xmlXPathSubstringAfterFunction</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextDescendant">xmlXPathNextDescendant</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextNamespace">xmlXPathNextNamespace</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathWrapCString">xmlXPathWrapCString</a>	(char * val);
void	<a href="#xmlXPathFreeParserContext">xmlXPathFreeParserContext</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathNodeTrailing">xmlXPathNodeTrailing</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
int	<a href="#xmlXPathNodeSetAdd">xmlXPathNodeSetAdd</a>		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val);
int	<a href="#xmlXPathNodeSetAddUnique">xmlXPathNodeSetAddUnique</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathNewValueTree">xmlXPathNewValueTree</a>	(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathDistinct">xmlXPathDistinct</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathVariableLookup">xmlXPathVariableLookup</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
void	<a href="#xmlXPathNotFunction">xmlXPathNotFunction</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs);
double	<a href="#xmlXPathStringEvalNumber">xmlXPathStringEvalNumber</a>	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextDescendantOrSelf">xmlXPathNextDescendantOrSelf</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathWrapExternal">xmlXPathWrapExternal</a>	(void * val);
<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	<a href="#xmlXPathNextAttribute">xmlXPathNextAttribute</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur);
void	<a href="#xmlXPathDivValues">xmlXPathDivValues</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	<a href="#xmlXPathVariableLookupNS">xmlXPathVariableLookupNS</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri);
int	<a href="#xmlXPathRegisterVariableNS">xmlXPathRegisterVariableNS</a>	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> value);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathTrailing">xmlXPathTrailing</a>	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2);
void	<a href="#xmlXPathEvalExpr">xmlXPathEvalExpr</a>		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
void	<a href="#xmlXPathDebugDumpObject">xmlXPathDebugDumpObject</a>		(FILE * output, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> cur, <br/>					 int depth);
<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	<a href="#xmlXPathLeading">xmlXPathLeading</a>		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlXPathParseName">xmlXPathParseName</a>	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="CAST_TO_BOOLEAN">Macro </a>CAST_TO_BOOLEAN</h3><pre class="programlisting">#define <a href="#CAST_TO_BOOLEAN">CAST_TO_BOOLEAN</a>;
</pre><p>Macro to try to cast the value on the top of the XPath stack to a boolean.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="CAST_TO_NUMBER">Macro </a>CAST_TO_NUMBER</h3><pre class="programlisting">#define <a href="#CAST_TO_NUMBER">CAST_TO_NUMBER</a>;
</pre><p>Macro to try to cast the value on the top of the XPath stack to a number.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="CAST_TO_STRING">Macro </a>CAST_TO_STRING</h3><pre class="programlisting">#define <a href="#CAST_TO_STRING">CAST_TO_STRING</a>;
</pre><p>Macro to try to cast the value on the top of the XPath stack to a string.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="CHECK_ARITY">Macro </a>CHECK_ARITY</h3><pre class="programlisting">#define <a href="#CHECK_ARITY">CHECK_ARITY</a>(x);
</pre><p>Macro to check that the number of args passed to an XPath function matches.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>x</tt></i>:</span></td><td>the number of expected args</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="CHECK_ERROR">Macro </a>CHECK_ERROR</h3><pre class="programlisting">#define <a href="#CHECK_ERROR">CHECK_ERROR</a>;
</pre><p>Macro to return from the function if an XPath error was detected.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="CHECK_ERROR0">Macro </a>CHECK_ERROR0</h3><pre class="programlisting">#define <a href="#CHECK_ERROR0">CHECK_ERROR0</a>;
</pre><p>Macro to return 0 from the function if an XPath error was detected.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="CHECK_TYPE">Macro </a>CHECK_TYPE</h3><pre class="programlisting">#define <a href="#CHECK_TYPE">CHECK_TYPE</a>(typeval);
</pre><p>Macro to check that the value on top of the XPath stack is of a given type.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>typeval</tt></i>:</span></td><td>the XPath type</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="CHECK_TYPE0">Macro </a>CHECK_TYPE0</h3><pre class="programlisting">#define <a href="#CHECK_TYPE0">CHECK_TYPE0</a>(typeval);
</pre><p>Macro to check that the value on top of the XPath stack is of a given type. Return(0) in case of failure</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>typeval</tt></i>:</span></td><td>the XPath type</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XP_ERROR">Macro </a>XP_ERROR</h3><pre class="programlisting">#define <a href="#XP_ERROR">XP_ERROR</a>(X);
</pre><p>Macro to raise an XPath error and return.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>X</tt></i>:</span></td><td>the error code</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XP_ERROR0">Macro </a>XP_ERROR0</h3><pre class="programlisting">#define <a href="#XP_ERROR0">XP_ERROR0</a>(X);
</pre><p>Macro to raise an XPath error and return 0.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>X</tt></i>:</span></td><td>the error code</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathCheckError">Macro </a>xmlXPathCheckError</h3><pre class="programlisting">#define <a href="#xmlXPathCheckError">xmlXPathCheckError</a>(ctxt);
</pre><p>Check if an XPath error was raised. Returns true if an error has been raised, false otherwise.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathEmptyNodeSet">Macro </a>xmlXPathEmptyNodeSet</h3><pre class="programlisting">#define <a href="#xmlXPathEmptyNodeSet">xmlXPathEmptyNodeSet</a>(ns);
</pre><p>Empties a node-set.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a node-set</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathGetContextNode">Macro </a>xmlXPathGetContextNode</h3><pre class="programlisting">#define <a href="#xmlXPathGetContextNode">xmlXPathGetContextNode</a>(ctxt);
</pre><p>Get the context node of an XPath context. Returns the context node.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathGetDocument">Macro </a>xmlXPathGetDocument</h3><pre class="programlisting">#define <a href="#xmlXPathGetDocument">xmlXPathGetDocument</a>(ctxt);
</pre><p>Get the document of an XPath context. Returns the context document.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathGetError">Macro </a>xmlXPathGetError</h3><pre class="programlisting">#define <a href="#xmlXPathGetError">xmlXPathGetError</a>(ctxt);
</pre><p>Get the error code of an XPath context. Returns the context error.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnBoolean">Macro </a>xmlXPathReturnBoolean</h3><pre class="programlisting">#define <a href="#xmlXPathReturnBoolean">xmlXPathReturnBoolean</a>(ctxt, val);
</pre><p>Pushes the boolean @val on the context stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a boolean</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnEmptyNodeSet">Macro </a>xmlXPathReturnEmptyNodeSet</h3><pre class="programlisting">#define <a href="#xmlXPathReturnEmptyNodeSet">xmlXPathReturnEmptyNodeSet</a>(ctxt);
</pre><p>Pushes an empty node-set on the context stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnEmptyString">Macro </a>xmlXPathReturnEmptyString</h3><pre class="programlisting">#define <a href="#xmlXPathReturnEmptyString">xmlXPathReturnEmptyString</a>(ctxt);
</pre><p>Pushes an empty string on the stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnExternal">Macro </a>xmlXPathReturnExternal</h3><pre class="programlisting">#define <a href="#xmlXPathReturnExternal">xmlXPathReturnExternal</a>(ctxt, val);
</pre><p>Pushes user data on the context stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>user data</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnFalse">Macro </a>xmlXPathReturnFalse</h3><pre class="programlisting">#define <a href="#xmlXPathReturnFalse">xmlXPathReturnFalse</a>(ctxt);
</pre><p>Pushes false on the context stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnNodeSet">Macro </a>xmlXPathReturnNodeSet</h3><pre class="programlisting">#define <a href="#xmlXPathReturnNodeSet">xmlXPathReturnNodeSet</a>(ctxt, ns);
</pre><p>Pushes the node-set @ns on the context stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a node-set</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnNumber">Macro </a>xmlXPathReturnNumber</h3><pre class="programlisting">#define <a href="#xmlXPathReturnNumber">xmlXPathReturnNumber</a>(ctxt, val);
</pre><p>Pushes the double @val on the context stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a double</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnString">Macro </a>xmlXPathReturnString</h3><pre class="programlisting">#define <a href="#xmlXPathReturnString">xmlXPathReturnString</a>(ctxt, str);
</pre><p>Pushes the string @str on the context stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>a string</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathReturnTrue">Macro </a>xmlXPathReturnTrue</h3><pre class="programlisting">#define <a href="#xmlXPathReturnTrue">xmlXPathReturnTrue</a>(ctxt);
</pre><p>Pushes true on the context stack.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathSetArityError">Macro </a>xmlXPathSetArityError</h3><pre class="programlisting">#define <a href="#xmlXPathSetArityError">xmlXPathSetArityError</a>(ctxt);
</pre><p>Raises an <a href="libxml2-xpath.html#XPATH_INVALID_ARITY">XPATH_INVALID_ARITY</a> error.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathSetError">Macro </a>xmlXPathSetError</h3><pre class="programlisting">#define <a href="#xmlXPathSetError">xmlXPathSetError</a>(ctxt, err);
</pre><p>Raises an error.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>err</tt></i>:</span></td><td>an <a href="libxml2-xpath.html#xmlXPathError">xmlXPathError</a> code</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathSetTypeError">Macro </a>xmlXPathSetTypeError</h3><pre class="programlisting">#define <a href="#xmlXPathSetTypeError">xmlXPathSetTypeError</a>(ctxt);
</pre><p>Raises an <a href="libxml2-xpath.html#XPATH_INVALID_TYPE">XPATH_INVALID_TYPE</a> error.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathStackIsExternal">Macro </a>xmlXPathStackIsExternal</h3><pre class="programlisting">#define <a href="#xmlXPathStackIsExternal">xmlXPathStackIsExternal</a>(ctxt);
</pre><p>Checks if the current value on the XPath stack is an external object. Returns true if the current object on the stack is an external object.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathStackIsNodeSet">Macro </a>xmlXPathStackIsNodeSet</h3><pre class="programlisting">#define <a href="#xmlXPathStackIsNodeSet">xmlXPathStackIsNodeSet</a>(ctxt);
</pre><p>Check if the current value on the XPath stack is a node set or an XSLT value tree. Returns true if the current object on the stack is a node-set.</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="valuePop"/>valuePop ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	valuePop	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Pops the top XPath object from the value stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath evaluation context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the XPath object just removed</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="valuePush"/>valuePush ()</h3><pre class="programlisting">int	valuePush			(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> value)<br/>
</pre><p>Pushes a new XPath object on top of the value stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath evaluation context</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the XPath object</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of items on the value stack</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathAddValues"/>xmlXPathAddValues ()</h3><pre class="programlisting">void	xmlXPathAddValues		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Implement the add operation on XPath objects: The numeric operators convert their operands to numbers as if by calling the number function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathBooleanFunction"/>xmlXPathBooleanFunction ()</h3><pre class="programlisting">void	xmlXPathBooleanFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the boolean() XPath function boolean boolean(object) The boolean function converts its argument to a boolean as follows: - a number is true if and only if it is neither positive or negative zero nor NaN - a node-set is true if and only if it is non-empty - a string is true if and only if its length is non-zero</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathCeilingFunction"/>xmlXPathCeilingFunction ()</h3><pre class="programlisting">void	xmlXPathCeilingFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the ceiling() XPath function number ceiling(number) The ceiling function returns the smallest (closest to negative infinity) number that is not less than the argument and that is an integer.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathCompareValues"/>xmlXPathCompareValues ()</h3><pre class="programlisting">int	xmlXPathCompareValues		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int inf, <br/>					 int strict)<br/>
</pre><p>Implement the compare operation on XPath objects: @arg1 &lt; @arg2 (1, 1, ... @arg1 &lt;= @arg2 (1, 0, ... @arg1 &gt; @arg2 (0, 1, ... @arg1 &gt;= @arg2 (0, 0, ... When neither object to be compared is a node-set and the operator is &lt;=, &lt;, &gt;=, &gt;, then the objects are compared by converted both objects to numbers and comparing the numbers according to IEEE 754. The &lt; comparison will be true if and only if the first number is less than the second number. The &lt;= comparison will be true if and only if the first number is less than or equal to the second number. The &gt; comparison will be true if and only if the first number is greater than the second number. The &gt;= comparison will be true if and only if the first number is greater than or equal to the second number.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>inf</tt></i>:</span></td><td>less than (1) or greater than (0)</td></tr><tr><td><span class="term"><i><tt>strict</tt></i>:</span></td><td>is the comparison strict</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the comparison succeeded, 0 if it failed</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathConcatFunction"/>xmlXPathConcatFunction ()</h3><pre class="programlisting">void	xmlXPathConcatFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the concat() XPath function string concat(string, string, string*) The concat function returns the concatenation of its arguments.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathContainsFunction"/>xmlXPathContainsFunction ()</h3><pre class="programlisting">void	xmlXPathContainsFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the contains() XPath function boolean contains(string, string) The contains function returns true if the first argument string contains the second argument string, and otherwise returns false.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathCountFunction"/>xmlXPathCountFunction ()</h3><pre class="programlisting">void	xmlXPathCountFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the count() XPath function number count(node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathDebugDumpCompExpr"/>xmlXPathDebugDumpCompExpr ()</h3><pre class="programlisting">void	xmlXPathDebugDumpCompExpr	(FILE * output, <br/>					 <a href="libxml2-xpath.html#xmlXPathCompExprPtr">xmlXPathCompExprPtr</a> comp, <br/>					 int depth)<br/>
</pre><p>Dumps the tree of the compiled XPath expression.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the precompiled XPath expression</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathDebugDumpObject"/>xmlXPathDebugDumpObject ()</h3><pre class="programlisting">void	xmlXPathDebugDumpObject		(FILE * output, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> cur, <br/>					 int depth)<br/>
</pre><p>Dump the content of the object for debugging purposes</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * to dump the output</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the object to inspect</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>indentation level</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathDifference"/>xmlXPathDifference ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathDifference	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2)<br/>
</pre><p>Implements the EXSLT - Sets difference() function: node-set set:difference (node-set, node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes1</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>nodes2</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the difference between the two node sets, or nodes1 if nodes2 is empty</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathDistinct"/>xmlXPathDistinct ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathDistinct	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes)<br/>
</pre><p>Implements the EXSLT - Sets distinct() function: node-set set:distinct (node-set) @nodes is sorted by document order, then #exslSetsDistinctSorted is called with the sorted node-set</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a subset of the nodes contained in @nodes, or @nodes if it is empty</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathDistinctSorted"/>xmlXPathDistinctSorted ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathDistinctSorted	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes)<br/>
</pre><p>Implements the EXSLT - Sets distinct() function: node-set set:distinct (node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>a node-set, sorted by document order</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a subset of the nodes contained in @nodes, or @nodes if it is empty</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathDivValues"/>xmlXPathDivValues ()</h3><pre class="programlisting">void	xmlXPathDivValues		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Implement the div operation on XPath objects @arg1 / @arg2: The numeric operators convert their operands to numbers as if by calling the number function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathEqualValues"/>xmlXPathEqualValues ()</h3><pre class="programlisting">int	xmlXPathEqualValues		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Implement the equal operation on XPath objects content: @arg1 == @arg2</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or 1 depending on the results of the test.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathErr"/>xmlXPathErr ()</h3><pre class="programlisting">void	xmlXPathErr			(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int error)<br/>
</pre><p>Handle an XPath error</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a XPath parser context</td></tr><tr><td><span class="term"><i><tt>error</tt></i>:</span></td><td>the error code</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathEvalExpr"/>xmlXPathEvalExpr ()</h3><pre class="programlisting">void	xmlXPathEvalExpr		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Parse and evaluate an XPath expression in the given context, then push the result on the context stack</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathEvaluatePredicateResult"/>xmlXPathEvaluatePredicateResult ()</h3><pre class="programlisting">int	xmlXPathEvaluatePredicateResult	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> res)<br/>
</pre><p>Evaluate a predicate result for the current node. A PredicateExpr is evaluated by evaluating the Expr and converting the result to a boolean. If the result is a number, the result will be converted to true if the number is equal to the position of the context node in the context node list (as returned by the position function) and will be converted to false otherwise; if the result is not a number, then the result will be converted as if by a call to the boolean function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>res</tt></i>:</span></td><td>the Predicate Expression evaluation result</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if predicate is true, 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathFalseFunction"/>xmlXPathFalseFunction ()</h3><pre class="programlisting">void	xmlXPathFalseFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the false() XPath function boolean false()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathFloorFunction"/>xmlXPathFloorFunction ()</h3><pre class="programlisting">void	xmlXPathFloorFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the floor() XPath function number floor(number) The floor function returns the largest (closest to positive infinity) number that is not greater than the argument and that is an integer.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathFreeParserContext"/>xmlXPathFreeParserContext ()</h3><pre class="programlisting">void	xmlXPathFreeParserContext	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Free up an <a href="libxml2-xpath.html#xmlXPathParserContext">xmlXPathParserContext</a></p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the context to free</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathFunctionLookup"/>xmlXPathFunctionLookup ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a>	xmlXPathFunctionLookup	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Search in the Function array of the context for the given function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the function name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a> or NULL if not found</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathFunctionLookupNS"/>xmlXPathFunctionLookupNS ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a>	xmlXPathFunctionLookupNS	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri)<br/>
</pre><p>Search in the Function array of the context for the given function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the function name</td></tr><tr><td><span class="term"><i><tt>ns_uri</tt></i>:</span></td><td>the function namespace URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a> or NULL if not found</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathHasSameNodes"/>xmlXPathHasSameNodes ()</h3><pre class="programlisting">int	xmlXPathHasSameNodes		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2)<br/>
</pre><p>Implements the EXSLT - Sets has-same-nodes function: boolean set:has-same-node(node-set, node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes1</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>nodes2</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true (1) if @nodes1 shares any node with @nodes2, false (0) otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathIdFunction"/>xmlXPathIdFunction ()</h3><pre class="programlisting">void	xmlXPathIdFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the id() XPath function node-set id(object) The id function selects elements by their unique ID (see [5.2.1 Unique IDs]). When the argument to id is of type node-set, then the result is the union of the result of applying id to the string value of each of the nodes in the argument node-set. When the argument to id is of any other type, the argument is converted to a string as if by a call to the string function; the string is split into a whitespace-separated list of tokens (whitespace is any sequence of <a href="libxml2-SAX.html#characters">characters</a> matching the production S); the result is a node-set containing the elements in the same document as the context node that have a unique ID equal to any of the tokens in the list.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathIntersection"/>xmlXPathIntersection ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathIntersection	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2)<br/>
</pre><p>Implements the EXSLT - Sets intersection() function: node-set set:intersection (node-set, node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes1</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>nodes2</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a node set comprising the nodes that are within both the node sets passed as arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathIsNodeType"/>xmlXPathIsNodeType ()</h3><pre class="programlisting">int	xmlXPathIsNodeType		(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Is the name given a NodeType one. [38] NodeType ::= 'comment' | 'text' | 'processing-instruction' | 'node'</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>a name string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathLangFunction"/>xmlXPathLangFunction ()</h3><pre class="programlisting">void	xmlXPathLangFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the lang() XPath function boolean lang(string) The lang function returns true or false depending on whether the language of the context node as specified by xml:lang attributes is the same as or is a sublanguage of the language specified by the argument string. The language of the context node is determined by the value of the xml:lang <a href="libxml2-SAX.html#attribute">attribute</a> on the context node, or, if the context node has no xml:lang attribute, by the value of the xml:lang <a href="libxml2-SAX.html#attribute">attribute</a> on the nearest ancestor of the context node that has an xml:lang attribute. If there is no such attribute, then lang</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathLastFunction"/>xmlXPathLastFunction ()</h3><pre class="programlisting">void	xmlXPathLastFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the last() XPath function number last() The last function returns the number of nodes in the context node list.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathLeading"/>xmlXPathLeading ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathLeading		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2)<br/>
</pre><p>Implements the EXSLT - Sets leading() function: node-set set:leading (node-set, node-set) @nodes1 and @nodes2 are sorted by document order, then #exslSetsLeadingSorted is called.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes1</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>nodes2</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the nodes in @nodes1 that precede the first node in @nodes2 in document order, @nodes1 if @nodes2 is NULL or empty or an empty node-set if @nodes1 doesn't contain @nodes2</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathLeadingSorted"/>xmlXPathLeadingSorted ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathLeadingSorted	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2)<br/>
</pre><p>Implements the EXSLT - Sets leading() function: node-set set:leading (node-set, node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes1</tt></i>:</span></td><td>a node-set, sorted by document order</td></tr><tr><td><span class="term"><i><tt>nodes2</tt></i>:</span></td><td>a node-set, sorted by document order</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the nodes in @nodes1 that precede the first node in @nodes2 in document order, @nodes1 if @nodes2 is NULL or empty or an empty node-set if @nodes1 doesn't contain @nodes2</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathLocalNameFunction"/>xmlXPathLocalNameFunction ()</h3><pre class="programlisting">void	xmlXPathLocalNameFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the local-name() XPath function string local-name(node-set?) The local-name function returns a string containing the local part of the name of the node in the argument node-set that is first in document order. If the node-set is empty or the first node has no name, an empty string is returned. If the argument is omitted it defaults to the context node.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathModValues"/>xmlXPathModValues ()</h3><pre class="programlisting">void	xmlXPathModValues		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Implement the mod operation on XPath objects: @arg1 / @arg2 The numeric operators convert their operands to numbers as if by calling the number function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathMultValues"/>xmlXPathMultValues ()</h3><pre class="programlisting">void	xmlXPathMultValues		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Implement the multiply operation on XPath objects: The numeric operators convert their operands to numbers as if by calling the number function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNamespaceURIFunction"/>xmlXPathNamespaceURIFunction ()</h3><pre class="programlisting">void	xmlXPathNamespaceURIFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the namespace-uri() XPath function string namespace-uri(node-set?) The namespace-uri function returns a string containing the namespace URI of the expanded name of the node in the argument node-set that is first in document order. If the node-set is empty, the first node has no name, or the expanded name has no namespace URI, an empty string is returned. If the argument is omitted it defaults to the context node.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNewBoolean"/>xmlXPathNewBoolean ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathNewBoolean	(int val)<br/>
</pre><p>Create a new <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type boolean and of value @val</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the boolean value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNewCString"/>xmlXPathNewCString ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathNewCString	(const char * val)<br/>
</pre><p>Create a new <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type string and of value @val</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the char * value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNewFloat"/>xmlXPathNewFloat ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathNewFloat	(double val)<br/>
</pre><p>Create a new <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type double and of value @val</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the double value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNewNodeSet"/>xmlXPathNewNodeSet ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathNewNodeSet	(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val)<br/>
</pre><p>Create a new <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type NodeSet and initialize it with the single Node @val</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the NodePtr value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNewNodeSetList"/>xmlXPathNewNodeSetList ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathNewNodeSetList	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> val)<br/>
</pre><p>Create a new <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type NodeSet and initialize it with the Nodeset @val</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an existing NodeSet</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNewParserContext"/>xmlXPathNewParserContext ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a>	xmlXPathNewParserContext	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>							 <a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)<br/>
</pre><p>Create a new <a href="libxml2-xpath.html#xmlXPathParserContext">xmlXPathParserContext</a></p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the XPath expression</td></tr><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-xpath.html#xmlXPathParserContext">xmlXPathParserContext</a> just allocated.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNewString"/>xmlXPathNewString ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathNewString	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * val)<br/>
</pre><p>Create a new <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type string and of value @val</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNewValueTree"/>xmlXPathNewValueTree ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathNewValueTree	(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val)<br/>
</pre><p>Create a new <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> of type Value Tree (XSLT) and initialize it with the tree root @val</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the NodePtr value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextAncestor"/>xmlXPathNextAncestor ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextAncestor	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "ancestor" direction the ancestor axis contains the ancestors of the context node; the ancestors of the context node consist of the parent of context node and the parent's parent and so on; the nodes are ordered in reverse document order; thus the parent is the first node on the axis, and the parent's parent is the second node on the axis</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextAncestorOrSelf"/>xmlXPathNextAncestorOrSelf ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextAncestorOrSelf	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "ancestor-or-self" direction he ancestor-or-self axis contains the context node and ancestors of the context node in reverse document order; thus the context node is the first node on the axis, and the context node's parent the second; parent here is defined the same as with the parent axis.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextAttribute"/>xmlXPathNextAttribute ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextAttribute	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "attribute" direction TODO: support DTD inherited default attributes</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current <a href="libxml2-SAX.html#attribute">attribute</a> in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextChild"/>xmlXPathNextChild ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextChild	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "child" direction The child axis contains the children of the context node in document order.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextDescendant"/>xmlXPathNextDescendant ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextDescendant	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "descendant" direction the descendant axis contains the descendants of the context node in document order; a descendant is a child or a child of a child and so on.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextDescendantOrSelf"/>xmlXPathNextDescendantOrSelf ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextDescendantOrSelf	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "descendant-or-self" direction the descendant-or-self axis contains the context node and the descendants of the context node in document order; thus the context node is the first node on the axis, and the first child of the context node is the second node on the axis</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextFollowing"/>xmlXPathNextFollowing ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextFollowing	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "following" direction The following axis contains all nodes in the same document as the context node that are after the context node in document order, excluding any descendants and excluding <a href="libxml2-SAX.html#attribute">attribute</a> nodes and namespace nodes; the nodes are ordered in document order</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextFollowingSibling"/>xmlXPathNextFollowingSibling ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextFollowingSibling	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "following-sibling" direction The following-sibling axis contains the following siblings of the context node in document order.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextNamespace"/>xmlXPathNextNamespace ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextNamespace	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "namespace" direction the namespace axis contains the namespace nodes of the context node; the order of nodes on this axis is implementation-defined; the axis will be empty unless the context node is an element We keep the XML namespace node at the end of the list.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current <a href="libxml2-SAX.html#attribute">attribute</a> in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextParent"/>xmlXPathNextParent ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextParent	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "parent" direction The parent axis contains the parent of the context node, if there is one.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextPreceding"/>xmlXPathNextPreceding ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextPreceding	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "preceding" direction the preceding axis contains all nodes in the same document as the context node that are before the context node in document order, excluding any ancestors and excluding <a href="libxml2-SAX.html#attribute">attribute</a> nodes and namespace nodes; the nodes are ordered in reverse document order</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextPrecedingSibling"/>xmlXPathNextPrecedingSibling ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextPrecedingSibling	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "preceding-sibling" direction The preceding-sibling axis contains the preceding siblings of the context node in reverse document order; the first preceding sibling is first on the axis; the sibling preceding that node is the second on the axis and so on.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNextSelf"/>xmlXPathNextSelf ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	xmlXPathNextSelf	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> cur)<br/>
</pre><p>Traversal function for the "self" direction The self axis contains just the context node itself</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the current node in the traversal</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the next element following that axis</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeLeading"/>xmlXPathNodeLeading ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathNodeLeading	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Implements the EXSLT - Sets leading() function: node-set set:leading (node-set, node-set) @nodes is sorted by document order, then #exslSetsNodeLeadingSorted is called.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the nodes in @nodes that precede @node in document order, @nodes if @node is NULL or an empty node-set if @nodes doesn't contain @node</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeLeadingSorted"/>xmlXPathNodeLeadingSorted ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathNodeLeadingSorted	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Implements the EXSLT - Sets leading() function: node-set set:leading (node-set, node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>a node-set, sorted by document order</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the nodes in @nodes that precede @node in document order, @nodes if @node is NULL or an empty node-set if @nodes doesn't contain @node</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetAdd"/>xmlXPathNodeSetAdd ()</h3><pre class="programlisting">int	xmlXPathNodeSetAdd		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val)<br/>
</pre><p>add a new <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> to an existing NodeSet</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the initial node set</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a new <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, and -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetAddNs"/>xmlXPathNodeSetAddNs ()</h3><pre class="programlisting">int	xmlXPathNodeSetAddNs		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNsPtr">xmlNsPtr</a> ns)<br/>
</pre><p>add a new namespace node to an existing NodeSet</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the initial node set</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the hosting node</td></tr><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>a the namespace node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetAddUnique"/>xmlXPathNodeSetAddUnique ()</h3><pre class="programlisting">int	xmlXPathNodeSetAddUnique	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val)<br/>
</pre><p>add a new <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> to an existing NodeSet, optimized version when we are sure the node is not already in the set.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the initial node set</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>a new <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of failure</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetContains"/>xmlXPathNodeSetContains ()</h3><pre class="programlisting">int	xmlXPathNodeSetContains		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val)<br/>
</pre><p>checks whether @cur contains @val</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the node-set</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true (1) if @cur contains @val, false (0) otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetDel"/>xmlXPathNodeSetDel ()</h3><pre class="programlisting">void	xmlXPathNodeSetDel		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> val)<br/>
</pre><p>Removes an <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> from an existing NodeSet</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the initial node set</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a></td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetFreeNs"/>xmlXPathNodeSetFreeNs ()</h3><pre class="programlisting">void	xmlXPathNodeSetFreeNs		(<a href="libxml2-tree.html#xmlNsPtr">xmlNsPtr</a> ns)<br/>
</pre><p>Namespace nodes in libxml don't match the XPath semantic. In a node set the namespace nodes are duplicated and the next pointer is set to the parent node in the XPath semantic. Check if such a node needs to be freed</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ns</tt></i>:</span></td><td>the XPath namespace node found in a nodeset.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetMerge"/>xmlXPathNodeSetMerge ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathNodeSetMerge	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> val1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> val2)<br/>
</pre><p>Merges two nodesets, all nodes from @val2 are added to @val1 if @val1 is NULL, a new set is created and copied from @val2</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val1</tt></i>:</span></td><td>the first NodeSet or NULL</td></tr><tr><td><span class="term"><i><tt>val2</tt></i>:</span></td><td>the second NodeSet</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>@val1 once extended or NULL in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetRemove"/>xmlXPathNodeSetRemove ()</h3><pre class="programlisting">void	xmlXPathNodeSetRemove		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> cur, <br/>					 int val)<br/>
</pre><p>Removes an entry from an existing NodeSet list.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>cur</tt></i>:</span></td><td>the initial node set</td></tr><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the index to remove</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeSetSort"/>xmlXPathNodeSetSort ()</h3><pre class="programlisting">void	xmlXPathNodeSetSort		(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> set)<br/>
</pre><p>Sort the node set in document order</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>set</tt></i>:</span></td><td>the node set</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeTrailing"/>xmlXPathNodeTrailing ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathNodeTrailing	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Implements the EXSLT - Sets trailing() function: node-set set:trailing (node-set, node-set) @nodes is sorted by document order, then #xmlXPathNodeTrailingSorted is called.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the nodes in @nodes that follow @node in document order, @nodes if @node is NULL or an empty node-set if @nodes doesn't contain @node</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNodeTrailingSorted"/>xmlXPathNodeTrailingSorted ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathNodeTrailingSorted	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Implements the EXSLT - Sets trailing() function: node-set set:trailing (node-set, node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes</tt></i>:</span></td><td>a node-set, sorted by document order</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the nodes in @nodes that follow @node in document order, @nodes if @node is NULL or an empty node-set if @nodes doesn't contain @node</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNormalizeFunction"/>xmlXPathNormalizeFunction ()</h3><pre class="programlisting">void	xmlXPathNormalizeFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the normalize-space() XPath function string normalize-space(string?) The normalize-space function returns the argument string with white space normalized by stripping leading and trailing whitespace and replacing sequences of whitespace <a href="libxml2-SAX.html#characters">characters</a> by a single space. Whitespace <a href="libxml2-SAX.html#characters">characters</a> are the same allowed by the S production in XML. If the argument is omitted, it defaults to the context node converted to a string, in other words the value of the context node.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNotEqualValues"/>xmlXPathNotEqualValues ()</h3><pre class="programlisting">int	xmlXPathNotEqualValues		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Implement the equal operation on XPath objects content: @arg1 == @arg2</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or 1 depending on the results of the test.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNotFunction"/>xmlXPathNotFunction ()</h3><pre class="programlisting">void	xmlXPathNotFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the not() XPath function boolean not(boolean) The not function returns true if its argument is false, and false otherwise.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNsLookup"/>xmlXPathNsLookup ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathNsLookup	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix)<br/>
</pre><p>Search in the namespace declaration array of the context for the given namespace name associated to the given prefix</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the namespace prefix value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the value or NULL if not found</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathNumberFunction"/>xmlXPathNumberFunction ()</h3><pre class="programlisting">void	xmlXPathNumberFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the number() XPath function number number(object?)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathParseNCName"/>xmlXPathParseNCName ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathParseNCName	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>parse an XML namespace non qualified name. [NS 3] NCName ::= (Letter | '_') (NCNameChar)* [NS 4] NCNameChar ::= Letter | Digit | '.' | '-' | '_' | CombiningChar | Extender</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the namespace name or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathParseName"/>xmlXPathParseName ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathParseName	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>parse an XML name [4] NameChar ::= Letter | Digit | '.' | '-' | '_' | ':' | CombiningChar | Extender [5] Name ::= (Letter | '_' | ':') (NameChar)*</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the namespace name or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathPopBoolean"/>xmlXPathPopBoolean ()</h3><pre class="programlisting">int	xmlXPathPopBoolean		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Pops a boolean from the stack, handling conversion if needed. Check error with #xmlXPathCheckError.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the boolean</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathPopExternal"/>xmlXPathPopExternal ()</h3><pre class="programlisting">void *	xmlXPathPopExternal		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Pops an external object from the stack, handling conversion if needed. Check error with #xmlXPathCheckError.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the object</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathPopNodeSet"/>xmlXPathPopNodeSet ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathPopNodeSet	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Pops a node-set from the stack, handling conversion if needed. Check error with #xmlXPathCheckError.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node-set</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathPopNumber"/>xmlXPathPopNumber ()</h3><pre class="programlisting">double	xmlXPathPopNumber		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Pops a number from the stack, handling conversion if needed. Check error with #xmlXPathCheckError.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathPopString"/>xmlXPathPopString ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlXPathPopString	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Pops a string from the stack, handling conversion if needed. Check error with #xmlXPathCheckError.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XPath parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the string</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathPositionFunction"/>xmlXPathPositionFunction ()</h3><pre class="programlisting">void	xmlXPathPositionFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the position() XPath function number position() The position function returns the position of the context node in the context node list. The first position is 1, and so the last position will be equal to last().</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisterAllFunctions"/>xmlXPathRegisterAllFunctions ()</h3><pre class="programlisting">void	xmlXPathRegisterAllFunctions	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)<br/>
</pre><p>Registers all default XPath functions in this context</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisterFunc"/>xmlXPathRegisterFunc ()</h3><pre class="programlisting">int	xmlXPathRegisterFunc		(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 <a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a> f)<br/>
</pre><p>Register a new function. If @f is NULL it unregisters the function</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the function name</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the function implementation or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisterFuncLookup"/>xmlXPathRegisterFuncLookup ()</h3><pre class="programlisting">void	xmlXPathRegisterFuncLookup	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 <a href="libxml2-xpath.html#xmlXPathFuncLookupFunc">xmlXPathFuncLookupFunc</a> f, <br/>					 void * funcCtxt)<br/>
</pre><p>Registers an external mechanism to do function lookup.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the lookup function</td></tr><tr><td><span class="term"><i><tt>funcCtxt</tt></i>:</span></td><td>the lookup data</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisterFuncNS"/>xmlXPathRegisterFuncNS ()</h3><pre class="programlisting">int	xmlXPathRegisterFuncNS		(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri, <br/>					 <a href="libxml2-xpath.html#xmlXPathFunction">xmlXPathFunction</a> f)<br/>
</pre><p>Register a new function. If @f is NULL it unregisters the function</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the function name</td></tr><tr><td><span class="term"><i><tt>ns_uri</tt></i>:</span></td><td>the function namespace URI</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the function implementation or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisterNs"/>xmlXPathRegisterNs ()</h3><pre class="programlisting">int	xmlXPathRegisterNs		(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri)<br/>
</pre><p>Register a new namespace. If @ns_uri is NULL it unregisters the namespace</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the namespace prefix cannot be NULL or empty string</td></tr><tr><td><span class="term"><i><tt>ns_uri</tt></i>:</span></td><td>the namespace name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisterVariable"/>xmlXPathRegisterVariable ()</h3><pre class="programlisting">int	xmlXPathRegisterVariable	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> value)<br/>
</pre><p>Register a new variable value. If @value is NULL it unregisters the variable</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the variable name</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the variable value or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisterVariableLookup"/>xmlXPathRegisterVariableLookup ()</h3><pre class="programlisting">void	xmlXPathRegisterVariableLookup	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 <a href="libxml2-xpath.html#xmlXPathVariableLookupFunc">xmlXPathVariableLookupFunc</a> f, <br/>					 void * data)<br/>
</pre><p>register an external mechanism to do variable lookup</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>f</tt></i>:</span></td><td>the lookup function</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the lookup data</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisterVariableNS"/>xmlXPathRegisterVariableNS ()</h3><pre class="programlisting">int	xmlXPathRegisterVariableNS	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri, <br/>					 <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> value)<br/>
</pre><p>Register a new variable value. If @value is NULL it unregisters the variable</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the variable name</td></tr><tr><td><span class="term"><i><tt>ns_uri</tt></i>:</span></td><td>the variable namespace URI</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the variable value or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisteredFuncsCleanup"/>xmlXPathRegisteredFuncsCleanup ()</h3><pre class="programlisting">void	xmlXPathRegisteredFuncsCleanup	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)<br/>
</pre><p>Cleanup the XPath context data associated to registered functions</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisteredNsCleanup"/>xmlXPathRegisteredNsCleanup ()</h3><pre class="programlisting">void	xmlXPathRegisteredNsCleanup	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)<br/>
</pre><p>Cleanup the XPath context data associated to registered variables</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRegisteredVariablesCleanup"/>xmlXPathRegisteredVariablesCleanup ()</h3><pre class="programlisting">void	xmlXPathRegisteredVariablesCleanup	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt)<br/>
</pre><p>Cleanup the XPath context data associated to registered variables</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRoot"/>xmlXPathRoot ()</h3><pre class="programlisting">void	xmlXPathRoot			(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Initialize the context to the root of the document</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathRoundFunction"/>xmlXPathRoundFunction ()</h3><pre class="programlisting">void	xmlXPathRoundFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the round() XPath function number round(number) The round function returns the number that is closest to the argument and that is an integer. If there are two such numbers, then the one that is even is returned.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathStartsWithFunction"/>xmlXPathStartsWithFunction ()</h3><pre class="programlisting">void	xmlXPathStartsWithFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the starts-with() XPath function boolean starts-with(string, string) The starts-with function returns true if the first argument string starts with the second argument string, and otherwise returns false.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathStringEvalNumber"/>xmlXPathStringEvalNumber ()</h3><pre class="programlisting">double	xmlXPathStringEvalNumber	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str)<br/>
</pre><p>[30a] Float ::= Number ('e' Digits?)? [30] Number ::= Digits ('.' Digits?)? | '.' Digits [31] Digits ::= [0-9]+ Compile a Number in the string In complement of the Number expression, this function also handles negative values : '-' Number.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>A string to scan</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the double value.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathStringFunction"/>xmlXPathStringFunction ()</h3><pre class="programlisting">void	xmlXPathStringFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the string() XPath function string string(object?) The string function converts an object to a string as follows: - A node-set is converted to a string by returning the value of the node in the node-set that is first in document order. If the node-set is empty, an empty string is returned. - A number is converted to a string as follows + NaN is converted to the string NaN + positive zero is converted to the string 0 + negative zero is converted to the string 0 + positive infinity is converted to the string Infinity + negative infinity is converted to the string -Infinity + if the number is an integer, the number is represented in decimal form as a Number with no decimal point and no leading zeros, preceded by a minus sign (-) if the number is negative + otherwise, the number is represented in decimal form as a Number including a decimal point with at least one digit before the decimal point and at least one digit after the decimal point, preceded by a minus sign (-) if the number is negative; there must be no leading zeros before the decimal point apart possibly from the one required digit immediately before the decimal point; beyond the one required digit after the decimal point there must be as many, but only as many, more digits as are needed to uniquely distinguish the number from all other IEEE 754 numeric values. - The boolean false value is converted to the string false. The boolean true value is converted to the string true. If the argument is omitted, it defaults to a node-set with the context node as its only member.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathStringLengthFunction"/>xmlXPathStringLengthFunction ()</h3><pre class="programlisting">void	xmlXPathStringLengthFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the string-length() XPath function number string-length(string?) The string-length returns the number of <a href="libxml2-SAX.html#characters">characters</a> in the string (see [3.6 Strings]). If the argument is omitted, it defaults to the context node converted to a string, in other words the value of the context node.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathSubValues"/>xmlXPathSubValues ()</h3><pre class="programlisting">void	xmlXPathSubValues		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Implement the subtraction operation on XPath objects: The numeric operators convert their operands to numbers as if by calling the number function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathSubstringAfterFunction"/>xmlXPathSubstringAfterFunction ()</h3><pre class="programlisting">void	xmlXPathSubstringAfterFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the substring-after() XPath function string substring-after(string, string) The substring-after function returns the substring of the first argument string that follows the first occurrence of the second argument string in the first argument string, or the empty stringi if the first argument string does not contain the second argument string. For example, substring-after("1999/04/01","/") returns 04/01, and substring-after("1999/04/01","19") returns 99/04/01.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathSubstringBeforeFunction"/>xmlXPathSubstringBeforeFunction ()</h3><pre class="programlisting">void	xmlXPathSubstringBeforeFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the substring-before() XPath function string substring-before(string, string) The substring-before function returns the substring of the first argument string that precedes the first occurrence of the second argument string in the first argument string, or the empty string if the first argument string does not contain the second argument string. For example, substring-before("1999/04/01","/") returns 1999.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathSubstringFunction"/>xmlXPathSubstringFunction ()</h3><pre class="programlisting">void	xmlXPathSubstringFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the substring() XPath function string substring(string, number, number?) The substring function returns the substring of the first argument starting at the position specified in the second argument with length specified in the third argument. For example, substring("12345",2,3) returns "234". If the third argument is not specified, it returns the substring starting at the position specified in the second argument and continuing to the end of the string. For example, substring("12345",2) returns "2345". More precisely, each character in the string (see [3.6 Strings]) is considered to have a numeric position: the position of the first character is 1, the position of the second character is 2 and so on. The returned substring contains those <a href="libxml2-SAX.html#characters">characters</a> for which the position of the character is greater than or equal to the second argument and, if the third argument is specified, less than the sum of the second and third arguments; the comparisons and addition used for the above follow the standard IEEE 754 rules. Thus: - substring("12345", 1.5, 2.6) returns "234" - substring("12345", 0, 3) returns "12" - substring("12345", 0 div 0, 3) returns "" - substring("12345", 1, 0 div 0) returns "" - substring("12345", -42, 1 div 0) returns "12345" - substring("12345", -1 div 0, 1 div 0) returns ""</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathSumFunction"/>xmlXPathSumFunction ()</h3><pre class="programlisting">void	xmlXPathSumFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the sum() XPath function number sum(node-set) The sum function returns the sum of the values of the nodes in the argument node-set.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathTrailing"/>xmlXPathTrailing ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathTrailing	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2)<br/>
</pre><p>Implements the EXSLT - Sets trailing() function: node-set set:trailing (node-set, node-set) @nodes1 and @nodes2 are sorted by document order, then #xmlXPathTrailingSorted is called.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes1</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>nodes2</tt></i>:</span></td><td>a node-set</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the nodes in @nodes1 that follow the first node in @nodes2 in document order, @nodes1 if @nodes2 is NULL or empty or an empty node-set if @nodes1 doesn't contain @nodes2</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathTrailingSorted"/>xmlXPathTrailingSorted ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a>	xmlXPathTrailingSorted	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes1, <br/>					 <a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> nodes2)<br/>
</pre><p>Implements the EXSLT - Sets trailing() function: node-set set:trailing (node-set, node-set)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>nodes1</tt></i>:</span></td><td>a node-set, sorted by document order</td></tr><tr><td><span class="term"><i><tt>nodes2</tt></i>:</span></td><td>a node-set, sorted by document order</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the nodes in @nodes1 that follow the first node in @nodes2 in document order, @nodes1 if @nodes2 is NULL or empty or an empty node-set if @nodes1 doesn't contain @nodes2</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathTranslateFunction"/>xmlXPathTranslateFunction ()</h3><pre class="programlisting">void	xmlXPathTranslateFunction	(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the translate() XPath function string translate(string, string, string) The translate function returns the first argument string with occurrences of <a href="libxml2-SAX.html#characters">characters</a> in the second argument string replaced by the character at the corresponding position in the third argument string. For example, translate("bar","abc","ABC") returns the string BAr. If there is a character in the second argument string with no character at a corresponding position in the third argument string (because the second argument string is longer than the third argument string), then occurrences of that character in the first argument string are removed. For example, translate("--aaa--","abc-","ABC")</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathTrueFunction"/>xmlXPathTrueFunction ()</h3><pre class="programlisting">void	xmlXPathTrueFunction		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 int nargs)<br/>
</pre><p>Implement the true() XPath function boolean true()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>nargs</tt></i>:</span></td><td>the number of arguments</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathValueFlipSign"/>xmlXPathValueFlipSign ()</h3><pre class="programlisting">void	xmlXPathValueFlipSign		(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt)<br/>
</pre><p>Implement the unary - operation on an XPath object The numeric operators convert their operands to numbers as if by calling the number function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathVariableLookup"/>xmlXPathVariableLookup ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathVariableLookup	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Search in the Variable array of the context for the given variable value.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the variable name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a copy of the value or NULL if not found</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathVariableLookupNS"/>xmlXPathVariableLookupNS ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathVariableLookupNS	(<a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> ctxt, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ns_uri)<br/>
</pre><p>Search in the Variable array of the context for the given variable value.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the variable name</td></tr><tr><td><span class="term"><i><tt>ns_uri</tt></i>:</span></td><td>the variable namespace URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the a copy of the value or NULL if not found</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathWrapCString"/>xmlXPathWrapCString ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathWrapCString	(char * val)<br/>
</pre><p>Wraps a string into an XPath object.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the char * value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathWrapExternal"/>xmlXPathWrapExternal ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathWrapExternal	(void * val)<br/>
</pre><p>Wraps the @val data into an XPath object.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the user data</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathWrapNodeSet"/>xmlXPathWrapNodeSet ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathWrapNodeSet	(<a href="libxml2-xpath.html#xmlNodeSetPtr">xmlNodeSetPtr</a> val)<br/>
</pre><p>Wrap the Nodeset @val in a new <a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a></p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the NodePtr value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPathWrapString"/>xmlXPathWrapString ()</h3><pre class="programlisting"><a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a>	xmlXPathWrapString	(<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * val)<br/>
</pre><p>Wraps the @val string into an XPath object.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>the <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the newly created object.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlXPatherror"/>xmlXPatherror ()</h3><pre class="programlisting">void	xmlXPatherror			(<a href="libxml2-xpath.html#xmlXPathParserContextPtr">xmlXPathParserContextPtr</a> ctxt, <br/>					 const char * file, <br/>					 int line, <br/>					 int no)<br/>
</pre><p>Formats an error message.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the XPath Parser context</td></tr><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>the file name</td></tr><tr><td><span class="term"><i><tt>line</tt></i>:</span></td><td>the line number</td></tr><tr><td><span class="term"><i><tt>no</tt></i>:</span></td><td>the error number</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
