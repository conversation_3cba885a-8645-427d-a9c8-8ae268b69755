<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>globals: interface for all global variables of the library</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-entities.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-hash.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">globals</span>
    </h2>
    <p>globals - interface for all global variables of the library</p>
    <p>all the global variables and thread handling for those variables is handled by this module.  The bottom of this file is automatically generated by build_glob.py based on the description file global.data </p>
    <p>Author(s): Gary Pennington &lt;<EMAIL>&gt;, Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">typedef <a href="libxml2-globals.html#xmlGlobalState">xmlGlobalState</a> * <a href="#xmlGlobalStatePtr">xmlGlobalStatePtr</a>;
typedef struct _xmlGlobalState <a href="#xmlGlobalState">xmlGlobalState</a>;
void	<a href="#xmlThrDefSetStructuredErrorFunc">xmlThrDefSetStructuredErrorFunc</a>	(void * ctx, <br/>					 <a href="libxml2-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> handler);
void	<a href="#xmlInitializeGlobalState">xmlInitializeGlobalState</a>	(<a href="libxml2-globals.html#xmlGlobalStatePtr">xmlGlobalStatePtr</a> gs);
<a href="libxml2-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	<a href="#xmlThrDefBufferAllocScheme">xmlThrDefBufferAllocScheme</a>	(<a href="libxml2-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> v);
int	<a href="#xmlThrDefPedanticParserDefaultValue">xmlThrDefPedanticParserDefaultValue</a>	(int v);
<a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	<a href="#xmlRegisterNodeDefault">xmlRegisterNodeDefault</a>	(<a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> func);
typedef <a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> <a href="#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	(const char * URI, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
<a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	<a href="#xmlThrDefOutputBufferCreateFilenameDefault">xmlThrDefOutputBufferCreateFilenameDefault</a>	(<a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> func);
<a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	<a href="#xmlDeregisterNodeDefault">xmlDeregisterNodeDefault</a>	(<a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> func);
int	<a href="#xmlThrDefDefaultBufferSize">xmlThrDefDefaultBufferSize</a>	(int v);
<a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	<a href="#xmlOutputBufferCreateFilenameDefault">xmlOutputBufferCreateFilenameDefault</a>	(<a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> func);
int	<a href="#xmlThrDefLoadExtDtdDefaultValue">xmlThrDefLoadExtDtdDefaultValue</a>	(int v);
<a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	<a href="#xmlThrDefRegisterNodeDefault">xmlThrDefRegisterNodeDefault</a>	(<a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> func);
int	<a href="#xmlThrDefKeepBlanksDefaultValue">xmlThrDefKeepBlanksDefaultValue</a>	(int v);
typedef void <a href="#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>		(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
int	<a href="#xmlThrDefParserDebugEntities">xmlThrDefParserDebugEntities</a>	(int v);
<a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	<a href="#xmlThrDefParserInputBufferCreateFilenameDefault">xmlThrDefParserInputBufferCreateFilenameDefault</a>	(<a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> func);
void	<a href="#xmlThrDefSetGenericErrorFunc">xmlThrDefSetGenericErrorFunc</a>	(void * ctx, <br/>					 <a href="libxml2-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> handler);
<a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	<a href="#xmlParserInputBufferCreateFilenameDefault">xmlParserInputBufferCreateFilenameDefault</a>	(<a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> func);
int	<a href="#xmlThrDefDoValidityCheckingDefaultValue">xmlThrDefDoValidityCheckingDefaultValue</a>	(int v);
void	<a href="#xmlCleanupGlobals">xmlCleanupGlobals</a>		(void);
int	<a href="#xmlThrDefGetWarningsDefaultValue">xmlThrDefGetWarningsDefaultValue</a>	(int v);
<a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	<a href="#xmlThrDefDeregisterNodeDefault">xmlThrDefDeregisterNodeDefault</a>	(<a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> func);
int	<a href="#xmlThrDefSubstituteEntitiesDefaultValue">xmlThrDefSubstituteEntitiesDefaultValue</a>	(int v);
typedef void <a href="#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>		(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
int	<a href="#xmlThrDefSaveNoEmptyTags">xmlThrDefSaveNoEmptyTags</a>	(int v);
int	<a href="#xmlThrDefIndentTreeOutput">xmlThrDefIndentTreeOutput</a>	(int v);
typedef <a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> <a href="#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	(const char * URI, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder, <br/>							 int compression);
void	<a href="#xmlInitGlobals">xmlInitGlobals</a>			(void);
int	<a href="#xmlThrDefLineNumbersDefaultValue">xmlThrDefLineNumbersDefaultValue</a>	(int v);
const char *	<a href="#xmlThrDefTreeIndentString">xmlThrDefTreeIndentString</a>	(const char * v);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlGlobalState">Structure </a>xmlGlobalState</h3><pre class="programlisting">struct _xmlGlobalState {
    const char *	xmlParserVersion
    <a href="libxml2-tree.html#xmlSAXLocator">xmlSAXLocator</a>	xmlDefaultSAXLocator
    <a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a>	xmlDefaultSAXHandler
    <a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a>	docbDefaultSAXHandler
    <a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a>	htmlDefaultSAXHandler
    <a href="libxml2-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a>	xmlFree
    <a href="libxml2-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a>	xmlMalloc
    <a href="libxml2-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a>	xmlMemStrdup
    <a href="libxml2-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a>	xmlRealloc
    <a href="libxml2-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a>	xmlGenericError
    <a href="libxml2-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a>	xmlStructuredError
    void *	xmlGenericErrorContext
    int	oldXMLWDcompatibility
    <a href="libxml2-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	xmlBufferAllocScheme
    int	xmlDefaultBufferSize
    int	xmlSubstituteEntitiesDefaultValue
    int	xmlDoValidityCheckingDefaultValue
    int	xmlGetWarningsDefaultValue
    int	xmlKeepBlanksDefaultValue
    int	xmlLineNumbersDefaultValue
    int	xmlLoadExtDtdDefaultValue
    int	xmlParserDebugEntities
    int	xmlPedanticParserDefaultValue
    int	xmlSaveNoEmptyTags
    int	xmlIndentTreeOutput
    const char *	xmlTreeIndentString
    <a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	xmlRegisterNodeDefaultValue
    <a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	xmlDeregisterNodeDefaultValue
    <a href="libxml2-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a>	xmlMallocAtomic
    <a href="libxml2-xmlerror.html#xmlError">xmlError</a>	xmlLastError
    <a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	xmlParserInputBufferCreateFilenameValue
    <a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	xmlOutputBufferCreateFilenameValue
    void *	xmlStructuredErrorContext
} xmlGlobalState;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGlobalStatePtr">Typedef </a>xmlGlobalStatePtr</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlGlobalState">xmlGlobalState</a> * xmlGlobalStatePtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDeregisterNodeFunc"/>Function type xmlDeregisterNodeFunc</h3><pre class="programlisting">void	xmlDeregisterNodeFunc		(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Signature for the deregistration callback of a discarded node</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferCreateFilenameFunc"/>Function type xmlOutputBufferCreateFilenameFunc</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	xmlOutputBufferCreateFilenameFunc	(const char * URI, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder, <br/>							 int compression)<br/>
</pre><p>Signature for the function doing the lookup for a suitable output method corresponding to an URI.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI to write to</td></tr><tr><td><span class="term"><i><tt>encoder</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> in case of success or NULL if no method was found.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateFilenameFunc"/>Function type xmlParserInputBufferCreateFilenameFunc</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlParserInputBufferCreateFilenameFunc	(const char * URI, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Signature for the function doing the lookup for a suitable input method corresponding to an URI.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>the URI to read from</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the requested source encoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in case of success or NULL if no method was found.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterNodeFunc"/>Function type xmlRegisterNodeFunc</h3><pre class="programlisting">void	xmlRegisterNodeFunc		(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Signature for the registration callback of a created node</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the current node</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="docbDefaultSAXHandler">Variable </a>docbDefaultSAXHandler</h3><pre class="programlisting"><a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> docbDefaultSAXHandler;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="htmlDefaultSAXHandler">Variable </a>htmlDefaultSAXHandler</h3><pre class="programlisting"><a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> htmlDefaultSAXHandler;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="oldXMLWDcompatibility">Variable </a>oldXMLWDcompatibility</h3><pre class="programlisting">int oldXMLWDcompatibility;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlBufferAllocScheme">Variable </a>xmlBufferAllocScheme</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> xmlBufferAllocScheme;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDefaultBufferSize">Variable </a>xmlDefaultBufferSize</h3><pre class="programlisting">int xmlDefaultBufferSize;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDefaultSAXHandler">Variable </a>xmlDefaultSAXHandler</h3><pre class="programlisting"><a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> xmlDefaultSAXHandler;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDefaultSAXLocator">Variable </a>xmlDefaultSAXLocator</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlSAXLocator">xmlSAXLocator</a> xmlDefaultSAXLocator;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDeregisterNodeDefaultValue">Variable </a>xmlDeregisterNodeDefaultValue</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> xmlDeregisterNodeDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDoValidityCheckingDefaultValue">Variable </a>xmlDoValidityCheckingDefaultValue</h3><pre class="programlisting">int xmlDoValidityCheckingDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFree">Variable </a>xmlFree</h3><pre class="programlisting"><a href="libxml2-xmlmemory.html#xmlFreeFunc">xmlFreeFunc</a> xmlFree;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGenericError">Variable </a>xmlGenericError</h3><pre class="programlisting"><a href="libxml2-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> xmlGenericError;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGenericErrorContext">Variable </a>xmlGenericErrorContext</h3><pre class="programlisting">void * xmlGenericErrorContext;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGetWarningsDefaultValue">Variable </a>xmlGetWarningsDefaultValue</h3><pre class="programlisting">int xmlGetWarningsDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIndentTreeOutput">Variable </a>xmlIndentTreeOutput</h3><pre class="programlisting">int xmlIndentTreeOutput;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlKeepBlanksDefaultValue">Variable </a>xmlKeepBlanksDefaultValue</h3><pre class="programlisting">int xmlKeepBlanksDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlLastError">Variable </a>xmlLastError</h3><pre class="programlisting"><a href="libxml2-xmlerror.html#xmlError">xmlError</a> xmlLastError;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlLineNumbersDefaultValue">Variable </a>xmlLineNumbersDefaultValue</h3><pre class="programlisting">int xmlLineNumbersDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlLoadExtDtdDefaultValue">Variable </a>xmlLoadExtDtdDefaultValue</h3><pre class="programlisting">int xmlLoadExtDtdDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlMalloc">Variable </a>xmlMalloc</h3><pre class="programlisting"><a href="libxml2-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> xmlMalloc;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlMallocAtomic">Variable </a>xmlMallocAtomic</h3><pre class="programlisting"><a href="libxml2-xmlmemory.html#xmlMallocFunc">xmlMallocFunc</a> xmlMallocAtomic;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlMemStrdup">Variable </a>xmlMemStrdup</h3><pre class="programlisting"><a href="libxml2-xmlmemory.html#xmlStrdupFunc">xmlStrdupFunc</a> xmlMemStrdup;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferCreateFilenameValue">Variable </a>xmlOutputBufferCreateFilenameValue</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> xmlOutputBufferCreateFilenameValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserDebugEntities">Variable </a>xmlParserDebugEntities</h3><pre class="programlisting">int xmlParserDebugEntities;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateFilenameValue">Variable </a>xmlParserInputBufferCreateFilenameValue</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> xmlParserInputBufferCreateFilenameValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserVersion">Variable </a>xmlParserVersion</h3><pre class="programlisting">const char * xmlParserVersion;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlPedanticParserDefaultValue">Variable </a>xmlPedanticParserDefaultValue</h3><pre class="programlisting">int xmlPedanticParserDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRealloc">Variable </a>xmlRealloc</h3><pre class="programlisting"><a href="libxml2-xmlmemory.html#xmlReallocFunc">xmlReallocFunc</a> xmlRealloc;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterNodeDefaultValue">Variable </a>xmlRegisterNodeDefaultValue</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> xmlRegisterNodeDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveNoEmptyTags">Variable </a>xmlSaveNoEmptyTags</h3><pre class="programlisting">int xmlSaveNoEmptyTags;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStructuredError">Variable </a>xmlStructuredError</h3><pre class="programlisting"><a href="libxml2-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> xmlStructuredError;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStructuredErrorContext">Variable </a>xmlStructuredErrorContext</h3><pre class="programlisting">void * xmlStructuredErrorContext;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSubstituteEntitiesDefaultValue">Variable </a>xmlSubstituteEntitiesDefaultValue</h3><pre class="programlisting">int xmlSubstituteEntitiesDefaultValue;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTreeIndentString">Variable </a>xmlTreeIndentString</h3><pre class="programlisting">const char * xmlTreeIndentString;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCleanupGlobals"/>xmlCleanupGlobals ()</h3><pre class="programlisting">void	xmlCleanupGlobals		(void)<br/>
</pre><p>Additional cleanup for multi-threading</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDeregisterNodeDefault"/>xmlDeregisterNodeDefault ()</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	xmlDeregisterNodeDefault	(<a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> func)<br/>
</pre><p>Registers a callback for node destruction</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>function pointer to the new DeregisterNodeFunc</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the previous value of the deregistration function</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlInitGlobals"/>xmlInitGlobals ()</h3><pre class="programlisting">void	xmlInitGlobals			(void)<br/>
</pre><p>Additional initialisation for multi-threading</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlInitializeGlobalState"/>xmlInitializeGlobalState ()</h3><pre class="programlisting">void	xmlInitializeGlobalState	(<a href="libxml2-globals.html#xmlGlobalStatePtr">xmlGlobalStatePtr</a> gs)<br/>
</pre><p>xmlInitializeGlobalState() initialize a global state with all the default values of the library.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>gs</tt></i>:</span></td><td>a pointer to a newly allocated global state</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferCreateFilenameDefault"/>xmlOutputBufferCreateFilenameDefault ()</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	xmlOutputBufferCreateFilenameDefault	(<a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> func)<br/>
</pre><p>Registers a callback for URI output file handling</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>function pointer to the new OutputBufferCreateFilenameFunc</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the old value of the registration function</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateFilenameDefault"/>xmlParserInputBufferCreateFilenameDefault ()</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	xmlParserInputBufferCreateFilenameDefault	(<a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> func)<br/>
</pre><p>Registers a callback for URI input file handling</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>function pointer to the new ParserInputBufferCreateFilenameFunc</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the old value of the registration function</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterNodeDefault"/>xmlRegisterNodeDefault ()</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	xmlRegisterNodeDefault	(<a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> func)<br/>
</pre><p>Registers a callback for node creation</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td>function pointer to the new RegisterNodeFunc</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the old value of the registration function</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefBufferAllocScheme"/>xmlThrDefBufferAllocScheme ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a>	xmlThrDefBufferAllocScheme	(<a href="libxml2-tree.html#xmlBufferAllocationScheme">xmlBufferAllocationScheme</a> v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefDefaultBufferSize"/>xmlThrDefDefaultBufferSize ()</h3><pre class="programlisting">int	xmlThrDefDefaultBufferSize	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefDeregisterNodeDefault"/>xmlThrDefDeregisterNodeDefault ()</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a>	xmlThrDefDeregisterNodeDefault	(<a href="libxml2-globals.html#xmlDeregisterNodeFunc">xmlDeregisterNodeFunc</a> func)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefDoValidityCheckingDefaultValue"/>xmlThrDefDoValidityCheckingDefaultValue ()</h3><pre class="programlisting">int	xmlThrDefDoValidityCheckingDefaultValue	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefGetWarningsDefaultValue"/>xmlThrDefGetWarningsDefaultValue ()</h3><pre class="programlisting">int	xmlThrDefGetWarningsDefaultValue	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefIndentTreeOutput"/>xmlThrDefIndentTreeOutput ()</h3><pre class="programlisting">int	xmlThrDefIndentTreeOutput	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefKeepBlanksDefaultValue"/>xmlThrDefKeepBlanksDefaultValue ()</h3><pre class="programlisting">int	xmlThrDefKeepBlanksDefaultValue	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefLineNumbersDefaultValue"/>xmlThrDefLineNumbersDefaultValue ()</h3><pre class="programlisting">int	xmlThrDefLineNumbersDefaultValue	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefLoadExtDtdDefaultValue"/>xmlThrDefLoadExtDtdDefaultValue ()</h3><pre class="programlisting">int	xmlThrDefLoadExtDtdDefaultValue	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefOutputBufferCreateFilenameDefault"/>xmlThrDefOutputBufferCreateFilenameDefault ()</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a>	xmlThrDefOutputBufferCreateFilenameDefault	(<a href="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc">xmlOutputBufferCreateFilenameFunc</a> func)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefParserDebugEntities"/>xmlThrDefParserDebugEntities ()</h3><pre class="programlisting">int	xmlThrDefParserDebugEntities	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefParserInputBufferCreateFilenameDefault"/>xmlThrDefParserInputBufferCreateFilenameDefault ()</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a>	xmlThrDefParserInputBufferCreateFilenameDefault	(<a href="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc">xmlParserInputBufferCreateFilenameFunc</a> func)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefPedanticParserDefaultValue"/>xmlThrDefPedanticParserDefaultValue ()</h3><pre class="programlisting">int	xmlThrDefPedanticParserDefaultValue	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefRegisterNodeDefault"/>xmlThrDefRegisterNodeDefault ()</h3><pre class="programlisting"><a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a>	xmlThrDefRegisterNodeDefault	(<a href="libxml2-globals.html#xmlRegisterNodeFunc">xmlRegisterNodeFunc</a> func)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>func</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefSaveNoEmptyTags"/>xmlThrDefSaveNoEmptyTags ()</h3><pre class="programlisting">int	xmlThrDefSaveNoEmptyTags	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefSetGenericErrorFunc"/>xmlThrDefSetGenericErrorFunc ()</h3><pre class="programlisting">void	xmlThrDefSetGenericErrorFunc	(void * ctx, <br/>					 <a href="libxml2-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> handler)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefSetStructuredErrorFunc"/>xmlThrDefSetStructuredErrorFunc ()</h3><pre class="programlisting">void	xmlThrDefSetStructuredErrorFunc	(void * ctx, <br/>					 <a href="libxml2-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> handler)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefSubstituteEntitiesDefaultValue"/>xmlThrDefSubstituteEntitiesDefaultValue ()</h3><pre class="programlisting">int	xmlThrDefSubstituteEntitiesDefaultValue	(int v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlThrDefTreeIndentString"/>xmlThrDefTreeIndentString ()</h3><pre class="programlisting">const char *	xmlThrDefTreeIndentString	(const char * v)<br/>
</pre><p/>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>v</tt></i>:</span></td><td/></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td/></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
