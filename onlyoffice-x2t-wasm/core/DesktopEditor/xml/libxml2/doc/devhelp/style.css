.synopsis, .classsynopsis 
{
  background: #eeeeee;
  border: solid 1px #aaaaaa;
  padding: 0.5em;
}
.programlisting 
{
  background: #eeeeff;
  border: solid 1px #aaaaff;
  padding: 0.5em;
}
.variablelist 
{
  padding: 4px;
  margin-left: 3em;
}
.variablelist td:first-child
{
  vertical-align: top;
}
table.navigation 
{
  background: #ffeeee;
  border: solid 1px #ffaaaa;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.navigation a 
{
  color: #770000;
}
.navigation a:visited 
{
  color: #550000;
}
.navigation .title 
{
  font-size: 200%;
}
div.refnamediv 
{
  margin-top: 2em;
}
div.gallery-float 
{
  float: left;
  padding: 10px;
}
div.gallery-float img 
{
  border-style: none;
}
div.gallery-spacer 
{
  clear: both;
}
a
{
  text-decoration: none;
}
a:hover
{
  text-decoration: underline;
  color: #FF0000;
}
