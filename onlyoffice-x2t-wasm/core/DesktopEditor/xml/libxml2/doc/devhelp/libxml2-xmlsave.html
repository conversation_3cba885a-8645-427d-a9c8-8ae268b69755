<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xmlsave: the XML document serializer</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xmlregexp.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-xmlschemas.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xmlsave</span>
    </h2>
    <p>xmlsave - the XML document serializer</p>
    <p>API to save document or subtree of document </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">typedef enum <a href="#xmlSaveOption">xmlSaveOption</a>;
typedef struct _xmlSaveCtxt <a href="#xmlSaveCtxt">xmlSaveCtxt</a>;
typedef <a href="libxml2-xmlsave.html#xmlSaveCtxt">xmlSaveCtxt</a> * <a href="#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>;
<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	<a href="#xmlSaveToIO">xmlSaveToIO</a>		(<a href="libxml2-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a> iowrite, <br/>					 <a href="libxml2-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a> ioclose, <br/>					 void * ioctx, <br/>					 const char * encoding, <br/>					 int options);
<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	<a href="#xmlSaveToFd">xmlSaveToFd</a>		(int fd, <br/>					 const char * encoding, <br/>					 int options);
int	<a href="#xmlSaveClose">xmlSaveClose</a>			(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt);
int	<a href="#xmlSaveSetAttrEscape">xmlSaveSetAttrEscape</a>		(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escape);
<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	<a href="#xmlSaveToBuffer">xmlSaveToBuffer</a>		(<a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> buffer, <br/>					 const char * encoding, <br/>					 int options);
<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	<a href="#xmlSaveToFilename">xmlSaveToFilename</a>	(const char * filename, <br/>					 const char * encoding, <br/>					 int options);
int	<a href="#xmlSaveFlush">xmlSaveFlush</a>			(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt);
long	<a href="#xmlSaveDoc">xmlSaveDoc</a>			(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc);
int	<a href="#xmlSaveSetEscape">xmlSaveSetEscape</a>		(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escape);
long	<a href="#xmlSaveTree">xmlSaveTree</a>			(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlSaveCtxt">Structure </a>xmlSaveCtxt</h3><pre class="programlisting">struct _xmlSaveCtxt {
The content of this structure is not made public by the API.
} xmlSaveCtxt;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveCtxtPtr">Typedef </a>xmlSaveCtxtPtr</h3><pre class="programlisting"><a href="libxml2-xmlsave.html#xmlSaveCtxt">xmlSaveCtxt</a> * xmlSaveCtxtPtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveOption">Enum </a>xmlSaveOption</h3><pre class="programlisting">enum <a href="#xmlSaveOption">xmlSaveOption</a> {
    <a name="XML_SAVE_FORMAT">XML_SAVE_FORMAT</a> = 1 /* format save output */
    <a name="XML_SAVE_NO_DECL">XML_SAVE_NO_DECL</a> = 2 /* drop the xml declaration */
    <a name="XML_SAVE_NO_EMPTY">XML_SAVE_NO_EMPTY</a> = 4 /* no empty tags */
    <a name="XML_SAVE_NO_XHTML">XML_SAVE_NO_XHTML</a> = 8 /* disable XHTML1 specific rules */
    <a name="XML_SAVE_XHTML">XML_SAVE_XHTML</a> = 16 /* force XHTML1 specific rules */
    <a name="XML_SAVE_AS_XML">XML_SAVE_AS_XML</a> = 32 /* force XML serialization on HTML doc */
    <a name="XML_SAVE_AS_HTML">XML_SAVE_AS_HTML</a> = 64 /* force HTML serialization on XML doc */
    <a name="XML_SAVE_WSNONSIG">XML_SAVE_WSNONSIG</a> = 128 /*  format with non-significant whitespace */
};
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveClose"/>xmlSaveClose ()</h3><pre class="programlisting">int	xmlSaveClose			(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt)<br/>
</pre><p>Close a document saving context, i.e. make sure that all bytes have been output and free the associated data.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveDoc"/>xmlSaveDoc ()</h3><pre class="programlisting">long	xmlSaveDoc			(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br/>
</pre><p>Save a full document to a saving context TODO: The function is not fully implemented yet as it does not return the byte count but 0 instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>a document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveFlush"/>xmlSaveFlush ()</h3><pre class="programlisting">int	xmlSaveFlush			(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt)<br/>
</pre><p>Flush a document saving context, i.e. make sure that all bytes have been output.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveSetAttrEscape"/>xmlSaveSetAttrEscape ()</h3><pre class="programlisting">int	xmlSaveSetAttrEscape		(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escape)<br/>
</pre><p>Set a custom escaping function to be used for text in <a href="libxml2-SAX.html#attribute">attribute</a> content</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>escape</tt></i>:</span></td><td>the escaping function</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if successful or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveSetEscape"/>xmlSaveSetEscape ()</h3><pre class="programlisting">int	xmlSaveSetEscape		(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escape)<br/>
</pre><p>Set a custom escaping function to be used for text in element content</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>escape</tt></i>:</span></td><td>the escaping function</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if successful or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveToBuffer"/>xmlSaveToBuffer ()</h3><pre class="programlisting"><a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	xmlSaveToBuffer		(<a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> buffer, <br/>					 const char * encoding, <br/>					 int options)<br/>
</pre><p>Create a document saving context serializing to a buffer with the encoding and the options given</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a buffer</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding name to use or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of xmlSaveOptions</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new serialization context or NULL in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveToFd"/>xmlSaveToFd ()</h3><pre class="programlisting"><a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	xmlSaveToFd		(int fd, <br/>					 const char * encoding, <br/>					 int options)<br/>
</pre><p>Create a document saving context serializing to a file descriptor with the encoding and the options given.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>fd</tt></i>:</span></td><td>a file descriptor number</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding name to use or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of xmlSaveOptions</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new serialization context or NULL in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveToFilename"/>xmlSaveToFilename ()</h3><pre class="programlisting"><a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	xmlSaveToFilename	(const char * filename, <br/>					 const char * encoding, <br/>					 int options)<br/>
</pre><p>Create a document saving context serializing to a filename or possibly to an URL (but this is less reliable) with the encoding and the options given.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>a file name or an URL</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding name to use or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of xmlSaveOptions</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new serialization context or NULL in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveToIO"/>xmlSaveToIO ()</h3><pre class="programlisting"><a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a>	xmlSaveToIO		(<a href="libxml2-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a> iowrite, <br/>					 <a href="libxml2-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a> ioclose, <br/>					 void * ioctx, <br/>					 const char * encoding, <br/>					 int options)<br/>
</pre><p>Create a document saving context serializing to a file descriptor with the encoding and the options given</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>iowrite</tt></i>:</span></td><td>an I/O write function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding name to use or NULL</td></tr><tr><td><span class="term"><i><tt>options</tt></i>:</span></td><td>a set of xmlSaveOptions</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new serialization context or NULL in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSaveTree"/>xmlSaveTree ()</h3><pre class="programlisting">long	xmlSaveTree			(<a href="libxml2-xmlsave.html#xmlSaveCtxtPtr">xmlSaveCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Save a subtree starting at the node parameter to a saving context TODO: The function is not fully implemented yet as it does not return the byte count but 0 instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a document saving context</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the top node of the subtree to save</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
