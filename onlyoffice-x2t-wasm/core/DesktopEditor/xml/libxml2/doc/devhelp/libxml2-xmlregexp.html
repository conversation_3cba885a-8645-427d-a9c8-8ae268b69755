<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xmlregexp: regular expressions handling</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xmlreader.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-xmlsave.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xmlregexp</span>
    </h2>
    <p>xmlregexp - regular expressions handling</p>
    <p>basic API for libxml regular expressions handling used for XML Schemas and validation. </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">typedef <a href="libxml2-xmlregexp.html#xmlRegExecCtxt">xmlRegExecCtxt</a> * <a href="#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a>;
typedef struct _xmlExpCtxt <a href="#xmlExpCtxt">xmlExpCtxt</a>;
typedef <a href="libxml2-xmlregexp.html#xmlExpNode">xmlExpNode</a> * <a href="#xmlExpNodePtr">xmlExpNodePtr</a>;
typedef <a href="libxml2-xmlregexp.html#xmlExpCtxt">xmlExpCtxt</a> * <a href="#xmlExpCtxtPtr">xmlExpCtxtPtr</a>;
typedef enum <a href="#xmlExpNodeType">xmlExpNodeType</a>;
typedef struct _xmlRegExecCtxt <a href="#xmlRegExecCtxt">xmlRegExecCtxt</a>;
typedef struct _xmlExpNode <a href="#xmlExpNode">xmlExpNode</a>;
typedef struct _xmlRegexp <a href="#xmlRegexp">xmlRegexp</a>;
typedef <a href="libxml2-xmlregexp.html#xmlRegexp">xmlRegexp</a> * <a href="#xmlRegexpPtr">xmlRegexpPtr</a>;
typedef void <a href="#xmlRegExecCallbacks">xmlRegExecCallbacks</a>		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * token, <br/>					 void * transdata, <br/>					 void * inputdata);
<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a>	<a href="#xmlRegNewExecCtxt">xmlRegNewExecCtxt</a>	(<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp, <br/>						 <a href="libxml2-xmlregexp.html#xmlRegExecCallbacks">xmlRegExecCallbacks</a> callback, <br/>						 void * data);
<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpNewOr">xmlExpNewOr</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> left, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> right);
void	<a href="#xmlRegFreeRegexp">xmlRegFreeRegexp</a>		(<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> regexp);
void	<a href="#xmlExpRef">xmlExpRef</a>			(<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp);
int	<a href="#xmlRegexpIsDeterminist">xmlRegexpIsDeterminist</a>		(<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp);
int	<a href="#xmlRegExecErrInfo">xmlRegExecErrInfo</a>		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** string, <br/>					 int * nbval, <br/>					 int * nbneg, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** values, <br/>					 int * terminal);
void	<a href="#xmlRegFreeExecCtxt">xmlRegFreeExecCtxt</a>		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec);
int	<a href="#xmlExpCtxtNbCons">xmlExpCtxtNbCons</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt);
int	<a href="#xmlExpSubsume">xmlExpSubsume</a>			(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> sub);
int	<a href="#xmlRegExecPushString2">xmlRegExecPushString2</a>		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value2, <br/>					 void * data);
int	<a href="#xmlRegExecNextValues">xmlRegExecNextValues</a>		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 int * nbval, <br/>					 int * nbneg, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** values, <br/>					 int * terminal);
<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpExpDerive">xmlExpExpDerive</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> sub);
int	<a href="#xmlExpIsNillable">xmlExpIsNillable</a>		(<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp);
void	<a href="#xmlExpFreeCtxt">xmlExpFreeCtxt</a>			(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt);
void	<a href="#xmlExpDump">xmlExpDump</a>			(<a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> expr);
<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpNewSeq">xmlExpNewSeq</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> left, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> right);
void	<a href="#xmlExpFree">xmlExpFree</a>			(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp);
<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpNewRange">xmlExpNewRange</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> subset, <br/>					 int min, <br/>					 int max);
<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	<a href="#xmlRegexpCompile">xmlRegexpCompile</a>	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * regexp);
<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpNewAtom">xmlExpNewAtom</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 int len);
int	<a href="#xmlRegexpExec">xmlRegexpExec</a>			(<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlRegExecPushString">xmlRegExecPushString</a>		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value, <br/>					 void * data);
int	<a href="#xmlExpGetStart">xmlExpGetStart</a>			(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** tokList, <br/>					 int len);
<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpParse">xmlExpParse</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 const char * expr);
<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a>	<a href="#xmlExpNewCtxt">xmlExpNewCtxt</a>		(int maxNodes, <br/>					 <a href="libxml2-dict.html#xmlDictPtr">xmlDictPtr</a> dict);
int	<a href="#xmlExpGetLanguage">xmlExpGetLanguage</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** langList, <br/>					 int len);
<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	<a href="#xmlExpStringDerive">xmlExpStringDerive</a>	(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>					 int len);
int	<a href="#xmlExpCtxtNbNodes">xmlExpCtxtNbNodes</a>		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt);
int	<a href="#xmlExpMaxToken">xmlExpMaxToken</a>			(<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> expr);
void	<a href="#xmlRegexpPrint">xmlRegexpPrint</a>			(FILE * output, <br/>					 <a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> regexp);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlExpCtxt">Structure </a>xmlExpCtxt</h3><pre class="programlisting">struct _xmlExpCtxt {
The content of this structure is not made public by the API.
} xmlExpCtxt;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpCtxtPtr">Typedef </a>xmlExpCtxtPtr</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpCtxt">xmlExpCtxt</a> * xmlExpCtxtPtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpNode">Structure </a>xmlExpNode</h3><pre class="programlisting">struct _xmlExpNode {
The content of this structure is not made public by the API.
} xmlExpNode;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpNodePtr">Typedef </a>xmlExpNodePtr</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNode">xmlExpNode</a> * xmlExpNodePtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpNodeType">Enum </a>xmlExpNodeType</h3><pre class="programlisting">enum <a href="#xmlExpNodeType">xmlExpNodeType</a> {
    <a name="XML_EXP_EMPTY">XML_EXP_EMPTY</a> = 0
    <a name="XML_EXP_FORBID">XML_EXP_FORBID</a> = 1
    <a name="XML_EXP_ATOM">XML_EXP_ATOM</a> = 2
    <a name="XML_EXP_SEQ">XML_EXP_SEQ</a> = 3
    <a name="XML_EXP_OR">XML_EXP_OR</a> = 4
    <a name="XML_EXP_COUNT">XML_EXP_COUNT</a> = 5
};
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegExecCtxt">Structure </a>xmlRegExecCtxt</h3><pre class="programlisting">struct _xmlRegExecCtxt {
The content of this structure is not made public by the API.
} xmlRegExecCtxt;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegExecCtxtPtr">Typedef </a>xmlRegExecCtxtPtr</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlRegExecCtxt">xmlRegExecCtxt</a> * xmlRegExecCtxtPtr;
</pre><p>A libxml progressive regular expression evaluation context</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegexp">Structure </a>xmlRegexp</h3><pre class="programlisting">struct _xmlRegexp {
The content of this structure is not made public by the API.
} xmlRegexp;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegexpPtr">Typedef </a>xmlRegexpPtr</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlRegexp">xmlRegexp</a> * xmlRegexpPtr;
</pre><p>A libxml regular expression, they can actually be far more complex thank the POSIX regex expressions.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegExecCallbacks"/>Function type xmlRegExecCallbacks</h3><pre class="programlisting">void	xmlRegExecCallbacks		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * token, <br/>					 void * transdata, <br/>					 void * inputdata)<br/>
</pre><p>Callback function when doing a transition in the automata</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>the regular expression context</td></tr><tr><td><span class="term"><i><tt>token</tt></i>:</span></td><td>the current token string</td></tr><tr><td><span class="term"><i><tt>transdata</tt></i>:</span></td><td>transition data</td></tr><tr><td><span class="term"><i><tt>inputdata</tt></i>:</span></td><td>input data</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="emptyExp">Variable </a>emptyExp</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> emptyExp;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="forbiddenExp">Variable </a>forbiddenExp</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> forbiddenExp;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpCtxtNbCons"/>xmlExpCtxtNbCons ()</h3><pre class="programlisting">int	xmlExpCtxtNbCons		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)<br/>
</pre><p>Debugging facility provides the number of allocated nodes over lifetime</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an expression context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of nodes ever allocated or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpCtxtNbNodes"/>xmlExpCtxtNbNodes ()</h3><pre class="programlisting">int	xmlExpCtxtNbNodes		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)<br/>
</pre><p>Debugging facility provides the number of allocated nodes at a that point</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an expression context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of nodes in use or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpDump"/>xmlExpDump ()</h3><pre class="programlisting">void	xmlExpDump			(<a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> expr)<br/>
</pre><p>Serialize the expression as compiled to the buffer</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>a buffer to receive the output</td></tr><tr><td><span class="term"><i><tt>expr</tt></i>:</span></td><td>the compiled expression</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpExpDerive"/>xmlExpExpDerive ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpExpDerive		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> sub)<br/>
</pre><p>Evaluates the expression resulting from @exp consuming a sub expression @sub Based on algebraic derivation and sometimes direct Brzozowski derivation it usually tatkes less than linear time and can handle expressions generating infinite languages.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expressions context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the englobing expression</td></tr><tr><td><span class="term"><i><tt>sub</tt></i>:</span></td><td>the subexpression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting expression or NULL in case of internal error, the result must be freed</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpFree"/>xmlExpFree ()</h3><pre class="programlisting">void	xmlExpFree			(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)<br/>
</pre><p>Dereference the expression</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpFreeCtxt"/>xmlExpFreeCtxt ()</h3><pre class="programlisting">void	xmlExpFreeCtxt			(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt)<br/>
</pre><p>Free an expression context</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an expression context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpGetLanguage"/>xmlExpGetLanguage ()</h3><pre class="programlisting">int	xmlExpGetLanguage		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** langList, <br/>					 int len)<br/>
</pre><p>Find all the strings used in @exp and store them in @list</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr><tr><td><span class="term"><i><tt>langList</tt></i>:</span></td><td>where to store the tokens</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the allocated length of @list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of unique strings found, -1 in case of errors and -2 if there is more than @len strings</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpGetStart"/>xmlExpGetStart ()</h3><pre class="programlisting">int	xmlExpGetStart			(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** tokList, <br/>					 int len)<br/>
</pre><p>Find all the strings that appears at the start of the languages accepted by @exp and store them in @list. E.g. for (a, b) | c it will return the list [a, c]</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr><tr><td><span class="term"><i><tt>tokList</tt></i>:</span></td><td>where to store the tokens</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the allocated length of @list</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of unique strings found, -1 in case of errors and -2 if there is more than @len strings</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpIsNillable"/>xmlExpIsNillable ()</h3><pre class="programlisting">int	xmlExpIsNillable		(<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)<br/>
</pre><p>Finds if the expression is nillable, i.e. if it accepts the empty sequqnce</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if nillable, 0 if not and -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpMaxToken"/>xmlExpMaxToken ()</h3><pre class="programlisting">int	xmlExpMaxToken			(<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> expr)<br/>
</pre><p>Indicate the maximum number of input a expression can accept</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>expr</tt></i>:</span></td><td>a compiled expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the maximum length or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpNewAtom"/>xmlExpNewAtom ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpNewAtom		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 int len)<br/>
</pre><p>Get the atom associated to this name from that context</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the atom name</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the atom name length in byte (or -1);</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpNewCtxt"/>xmlExpNewCtxt ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a>	xmlExpNewCtxt		(int maxNodes, <br/>					 <a href="libxml2-dict.html#xmlDictPtr">xmlDictPtr</a> dict)<br/>
</pre><p>Creates a new context for manipulating expressions</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>maxNodes</tt></i>:</span></td><td>the maximum number of nodes</td></tr><tr><td><span class="term"><i><tt>dict</tt></i>:</span></td><td>optional dictionary to use internally</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the context or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpNewOr"/>xmlExpNewOr ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpNewOr		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> left, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> right)<br/>
</pre><p>Get the atom associated to the choice @left | @right Note that @left and @right are consumed in the operation, to keep an handle on them use xmlExpRef() and use xmlExpFree() to release them, this is true even in case of failure (unless ctxt == NULL).</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>left</tt></i>:</span></td><td>left expression</td></tr><tr><td><span class="term"><i><tt>right</tt></i>:</span></td><td>right expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpNewRange"/>xmlExpNewRange ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpNewRange		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> subset, <br/>					 int min, <br/>					 int max)<br/>
</pre><p>Get the atom associated to the range (@subset){@min, @max} Note that @subset is consumed in the operation, to keep an handle on it use xmlExpRef() and use xmlExpFree() to release it, this is true even in case of failure (unless ctxt == NULL).</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>subset</tt></i>:</span></td><td>the expression to be repeated</td></tr><tr><td><span class="term"><i><tt>min</tt></i>:</span></td><td>the lower bound for the repetition</td></tr><tr><td><span class="term"><i><tt>max</tt></i>:</span></td><td>the upper bound for the repetition, -1 means infinite</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpNewSeq"/>xmlExpNewSeq ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpNewSeq		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> left, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> right)<br/>
</pre><p>Get the atom associated to the sequence @left , @right Note that @left and @right are consumed in the operation, to keep an handle on them use xmlExpRef() and use xmlExpFree() to release them, this is true even in case of failure (unless ctxt == NULL).</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>left</tt></i>:</span></td><td>left expression</td></tr><tr><td><span class="term"><i><tt>right</tt></i>:</span></td><td>right expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the node or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpParse"/>xmlExpParse ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpParse		(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 const char * expr)<br/>
</pre><p>Minimal parser for regexps, it understand the following constructs - string terminals - choice operator | - sequence operator , - subexpressions (...) - usual cardinality operators + * and ? - finite sequences { min, max } - infinite sequences { min, * } There is minimal checkings made especially no checking on strings values</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expressions context</td></tr><tr><td><span class="term"><i><tt>expr</tt></i>:</span></td><td>the 0 terminated string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new expression or NULL in case of failure</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpRef"/>xmlExpRef ()</h3><pre class="programlisting">void	xmlExpRef			(<a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp)<br/>
</pre><p>Increase the <a href="libxml2-SAX.html#reference">reference</a> count of the expression</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpStringDerive"/>xmlExpStringDerive ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a>	xmlExpStringDerive	(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>					 int len)<br/>
</pre><p>Do one step of Brzozowski derivation of the expression @exp with respect to the input string</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expression context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the expression</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the string len in bytes if available</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the resulting expression or NULL in case of internal error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlExpSubsume"/>xmlExpSubsume ()</h3><pre class="programlisting">int	xmlExpSubsume			(<a href="libxml2-xmlregexp.html#xmlExpCtxtPtr">xmlExpCtxtPtr</a> ctxt, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> exp, <br/>					 <a href="libxml2-xmlregexp.html#xmlExpNodePtr">xmlExpNodePtr</a> sub)<br/>
</pre><p>Check whether @exp accepts all the languages accexpted by @sub the input being a subexpression.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the expressions context</td></tr><tr><td><span class="term"><i><tt>exp</tt></i>:</span></td><td>the englobing expression</td></tr><tr><td><span class="term"><i><tt>sub</tt></i>:</span></td><td>the subexpression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 if false and -1 in case of failure.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegExecErrInfo"/>xmlRegExecErrInfo ()</h3><pre class="programlisting">int	xmlRegExecErrInfo		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** string, <br/>					 int * nbval, <br/>					 int * nbneg, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** values, <br/>					 int * terminal)<br/>
</pre><p>Extract error informations from the regexp execution, the parameter @string will be updated with the value pushed and not accepted, the parameter @values must point to an array of @nbval string pointers on return nbval will contain the number of possible strings in that state and the @values array will be updated with them. The string values</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regexp execution context generating an error</td></tr><tr><td><span class="term"><i><tt>string</tt></i>:</span></td><td>return value for the error string</td></tr><tr><td><span class="term"><i><tt>nbval</tt></i>:</span></td><td>pointer to the number of accepted values IN/OUT</td></tr><tr><td><span class="term"><i><tt>nbneg</tt></i>:</span></td><td>return number of negative transitions</td></tr><tr><td><span class="term"><i><tt>values</tt></i>:</span></td><td>pointer to the array of acceptable values</td></tr><tr><td><span class="term"><i><tt>terminal</tt></i>:</span></td><td>return value if this was a terminal state</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>will be freed with the @exec context and don't need to be deallocated. Returns: 0 in case of success or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegExecNextValues"/>xmlRegExecNextValues ()</h3><pre class="programlisting">int	xmlRegExecNextValues		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 int * nbval, <br/>					 int * nbneg, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** values, <br/>					 int * terminal)<br/>
</pre><p>Extract informations from the regexp execution, the parameter @values must point to an array of @nbval string pointers on return nbval will contain the number of possible strings in that state and the @values array will be updated with them. The string values</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regexp execution context</td></tr><tr><td><span class="term"><i><tt>nbval</tt></i>:</span></td><td>pointer to the number of accepted values IN/OUT</td></tr><tr><td><span class="term"><i><tt>nbneg</tt></i>:</span></td><td>return number of negative transitions</td></tr><tr><td><span class="term"><i><tt>values</tt></i>:</span></td><td>pointer to the array of acceptable values</td></tr><tr><td><span class="term"><i><tt>terminal</tt></i>:</span></td><td>return value if this was a terminal state</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>will be freed with the @exec context and don't need to be deallocated. Returns: 0 in case of success or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegExecPushString"/>xmlRegExecPushString ()</h3><pre class="programlisting">int	xmlRegExecPushString		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value, <br/>					 void * data)<br/>
</pre><p>Push one input token in the execution context</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regexp execution context or NULL to indicate the end</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>a string token input</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data associated to the token to reuse in callbacks</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the regexp reached a final state, 0 if non-final, and a negative value in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegExecPushString2"/>xmlRegExecPushString2 ()</h3><pre class="programlisting">int	xmlRegExecPushString2		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value2, <br/>					 void * data)<br/>
</pre><p>Push one input token in the execution context</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regexp execution context or NULL to indicate the end</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the first string token input</td></tr><tr><td><span class="term"><i><tt>value2</tt></i>:</span></td><td>the second string token input</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>data associated to the token to reuse in callbacks</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if the regexp reached a final state, 0 if non-final, and a negative value in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegFreeExecCtxt"/>xmlRegFreeExecCtxt ()</h3><pre class="programlisting">void	xmlRegFreeExecCtxt		(<a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a> exec)<br/>
</pre><p>Free the structures associated to a regular expression evaulation context.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>exec</tt></i>:</span></td><td>a regular expression evaulation context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegFreeRegexp"/>xmlRegFreeRegexp ()</h3><pre class="programlisting">void	xmlRegFreeRegexp		(<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> regexp)<br/>
</pre><p>Free a regexp</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>regexp</tt></i>:</span></td><td>the regexp</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegNewExecCtxt"/>xmlRegNewExecCtxt ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlRegExecCtxtPtr">xmlRegExecCtxtPtr</a>	xmlRegNewExecCtxt	(<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp, <br/>						 <a href="libxml2-xmlregexp.html#xmlRegExecCallbacks">xmlRegExecCallbacks</a> callback, <br/>						 void * data)<br/>
</pre><p>Build a context used for progressive evaluation of a regexp.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>a precompiled regular expression</td></tr><tr><td><span class="term"><i><tt>callback</tt></i>:</span></td><td>a callback function used for handling progresses in the automata matching phase</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the context data associated to the callback in this context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegexpCompile"/>xmlRegexpCompile ()</h3><pre class="programlisting"><a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a>	xmlRegexpCompile	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * regexp)<br/>
</pre><p>Parses a regular expression conforming to XML Schemas Part 2 Datatype Appendix F and builds an automata suitable for testing strings against that regular expression</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>regexp</tt></i>:</span></td><td>a regular expression string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the compiled expression or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegexpExec"/>xmlRegexpExec ()</h3><pre class="programlisting">int	xmlRegexpExec			(<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Check if the regular expression generates the value</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the compiled regular expression</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the value to check against the regular expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if it matches, 0 if not and a negative value in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegexpIsDeterminist"/>xmlRegexpIsDeterminist ()</h3><pre class="programlisting">int	xmlRegexpIsDeterminist		(<a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> comp)<br/>
</pre><p>Check if the regular expression is determinist</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>comp</tt></i>:</span></td><td>the compiled regular expression</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if it yes, 0 if not and a negative value in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegexpPrint"/>xmlRegexpPrint ()</h3><pre class="programlisting">void	xmlRegexpPrint			(FILE * output, <br/>					 <a href="libxml2-xmlregexp.html#xmlRegexpPtr">xmlRegexpPtr</a> regexp)<br/>
</pre><p>Print the content of the compiled regular expression</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the file for the output debug</td></tr><tr><td><span class="term"><i><tt>regexp</tt></i>:</span></td><td>the compiled regexp</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
