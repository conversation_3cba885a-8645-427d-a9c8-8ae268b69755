<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xmlerror: error handling</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xmlautomata.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-xmlexports.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xmlerror</span>
    </h2>
    <p>xmlerror - error handling</p>
    <p>the API used to report errors </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">typedef <a href="libxml2-xmlerror.html#xmlError">xmlError</a> * <a href="#xmlErrorPtr">xmlErrorPtr</a>;
typedef enum <a href="#xmlErrorLevel">xmlErrorLevel</a>;
typedef enum <a href="#xmlParserErrors">xmlParserErrors</a>;
typedef enum <a href="#xmlErrorDomain">xmlErrorDomain</a>;
typedef struct _xmlError <a href="#xmlError">xmlError</a>;
void	<a href="#xmlParserValidityError">xmlParserValidityError</a>		(void * ctx, <br/>					 const char * msg, <br/>					 ... ...);
typedef void <a href="#xmlGenericErrorFunc">xmlGenericErrorFunc</a>		(void * ctx, <br/>					 const char * msg, <br/>					 ... ...);
void	<a href="#xmlSetGenericErrorFunc">xmlSetGenericErrorFunc</a>		(void * ctx, <br/>					 <a href="libxml2-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> handler);
void	<a href="#xmlParserPrintFileInfo">xmlParserPrintFileInfo</a>		(<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input);
void	<a href="#xmlCtxtResetLastError">xmlCtxtResetLastError</a>		(void * ctx);
void	<a href="#xmlResetLastError">xmlResetLastError</a>		(void);
void	<a href="#initGenericErrorDefaultFunc">initGenericErrorDefaultFunc</a>	(<a href="libxml2-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> * handler);
int	<a href="#xmlCopyError">xmlCopyError</a>			(<a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a> from, <br/>					 <a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a> to);
void	<a href="#xmlParserValidityWarning">xmlParserValidityWarning</a>	(void * ctx, <br/>					 const char * msg, <br/>					 ... ...);
void	<a href="#xmlParserPrintFileContext">xmlParserPrintFileContext</a>	(<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input);
void	<a href="#xmlParserError">xmlParserError</a>			(void * ctx, <br/>					 const char * msg, <br/>					 ... ...);
void	<a href="#xmlParserWarning">xmlParserWarning</a>		(void * ctx, <br/>					 const char * msg, <br/>					 ... ...);
typedef void <a href="#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a>		(void * userData, <br/>					 <a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a> error);
void	<a href="#xmlSetStructuredErrorFunc">xmlSetStructuredErrorFunc</a>	(void * ctx, <br/>					 <a href="libxml2-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> handler);
void	<a href="#xmlResetError">xmlResetError</a>			(<a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a> err);
<a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a>	<a href="#xmlGetLastError">xmlGetLastError</a>		(void);
<a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a>	<a href="#xmlCtxtGetLastError">xmlCtxtGetLastError</a>	(void * ctx);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlError">Structure </a>xmlError</h3><pre class="programlisting">struct _xmlError {
    int	domain	: What part of the library raised this error
    int	code	: The error code, e.g. an <a href="libxml2-xmlerror.html#xmlParserError">xmlParserError</a>
    char *	message	: human-readable informative error message
    <a href="libxml2-xmlerror.html#xmlErrorLevel">xmlErrorLevel</a>	level	: how consequent is the error
    char *	file	: the filename
    int	line	: the line number if available
    char *	str1	: extra string information
    char *	str2	: extra string information
    char *	str3	: extra string information
    int	int1	: extra number information
    int	int2	: error column # or 0 if N/A (todo: rename field when we would brk ABI)
    void *	ctxt	: the parser context if available
    void *	node	: the node in the tree
} xmlError;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlErrorDomain">Enum </a>xmlErrorDomain</h3><pre class="programlisting">enum <a href="#xmlErrorDomain">xmlErrorDomain</a> {
    <a name="XML_FROM_NONE">XML_FROM_NONE</a> = 0
    <a name="XML_FROM_PARSER">XML_FROM_PARSER</a> = 1 /* The XML parser */
    <a name="XML_FROM_TREE">XML_FROM_TREE</a> = 2 /* The tree module */
    <a name="XML_FROM_NAMESPACE">XML_FROM_NAMESPACE</a> = 3 /* The XML Namespace module */
    <a name="XML_FROM_DTD">XML_FROM_DTD</a> = 4 /* The XML DTD validation with parser contex */
    <a name="XML_FROM_HTML">XML_FROM_HTML</a> = 5 /* The HTML parser */
    <a name="XML_FROM_MEMORY">XML_FROM_MEMORY</a> = 6 /* The memory allocator */
    <a name="XML_FROM_OUTPUT">XML_FROM_OUTPUT</a> = 7 /* The serialization code */
    <a name="XML_FROM_IO">XML_FROM_IO</a> = 8 /* The Input/Output stack */
    <a name="XML_FROM_FTP">XML_FROM_FTP</a> = 9 /* The FTP module */
    <a name="XML_FROM_HTTP">XML_FROM_HTTP</a> = 10 /* The HTTP module */
    <a name="XML_FROM_XINCLUDE">XML_FROM_XINCLUDE</a> = 11 /* The XInclude processing */
    <a name="XML_FROM_XPATH">XML_FROM_XPATH</a> = 12 /* The XPath module */
    <a name="XML_FROM_XPOINTER">XML_FROM_XPOINTER</a> = 13 /* The XPointer module */
    <a name="XML_FROM_REGEXP">XML_FROM_REGEXP</a> = 14 /* The regular expressions module */
    <a name="XML_FROM_DATATYPE">XML_FROM_DATATYPE</a> = 15 /* The W3C XML Schemas Datatype module */
    <a name="XML_FROM_SCHEMASP">XML_FROM_SCHEMASP</a> = 16 /* The W3C XML Schemas parser module */
    <a name="XML_FROM_SCHEMASV">XML_FROM_SCHEMASV</a> = 17 /* The W3C XML Schemas validation module */
    <a name="XML_FROM_RELAXNGP">XML_FROM_RELAXNGP</a> = 18 /* The Relax-NG parser module */
    <a name="XML_FROM_RELAXNGV">XML_FROM_RELAXNGV</a> = 19 /* The Relax-NG validator module */
    <a name="XML_FROM_CATALOG">XML_FROM_CATALOG</a> = 20 /* The Catalog module */
    <a name="XML_FROM_C14N">XML_FROM_C14N</a> = 21 /* The Canonicalization module */
    <a name="XML_FROM_XSLT">XML_FROM_XSLT</a> = 22 /* The XSLT engine from libxslt */
    <a name="XML_FROM_VALID">XML_FROM_VALID</a> = 23 /* The XML DTD validation with valid context */
    <a name="XML_FROM_CHECK">XML_FROM_CHECK</a> = 24 /* The error checking module */
    <a name="XML_FROM_WRITER">XML_FROM_WRITER</a> = 25 /* The xmlwriter module */
    <a name="XML_FROM_MODULE">XML_FROM_MODULE</a> = 26 /* The dynamically loaded module modul */
    <a name="XML_FROM_I18N">XML_FROM_I18N</a> = 27 /* The module handling character conversion */
    <a name="XML_FROM_SCHEMATRONV">XML_FROM_SCHEMATRONV</a> = 28 /* The Schematron validator module */
    <a name="XML_FROM_BUFFER">XML_FROM_BUFFER</a> = 29 /* The buffers module */
    <a name="XML_FROM_URI">XML_FROM_URI</a> = 30 /*  The URI module */
};
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlErrorLevel">Enum </a>xmlErrorLevel</h3><pre class="programlisting">enum <a href="#xmlErrorLevel">xmlErrorLevel</a> {
    <a name="XML_ERR_NONE">XML_ERR_NONE</a> = 0
    <a name="XML_ERR_WARNING">XML_ERR_WARNING</a> = 1 /* A simple warning */
    <a name="XML_ERR_ERROR">XML_ERR_ERROR</a> = 2 /* A recoverable error */
    <a name="XML_ERR_FATAL">XML_ERR_FATAL</a> = 3 /*  A fatal error */
};
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlErrorPtr">Typedef </a>xmlErrorPtr</h3><pre class="programlisting"><a href="libxml2-xmlerror.html#xmlError">xmlError</a> * xmlErrorPtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserErrors">Enum </a>xmlParserErrors</h3><pre class="programlisting">enum <a href="#xmlParserErrors">xmlParserErrors</a> {
    <a name="XML_ERR_OK">XML_ERR_OK</a> = 0
    <a name="XML_ERR_INTERNAL_ERROR">XML_ERR_INTERNAL_ERROR</a> = 1 /* 1 */
    <a name="XML_ERR_NO_MEMORY">XML_ERR_NO_MEMORY</a> = 2 /* 2 */
    <a name="XML_ERR_DOCUMENT_START">XML_ERR_DOCUMENT_START</a> = 3 /* 3 */
    <a name="XML_ERR_DOCUMENT_EMPTY">XML_ERR_DOCUMENT_EMPTY</a> = 4 /* 4 */
    <a name="XML_ERR_DOCUMENT_END">XML_ERR_DOCUMENT_END</a> = 5 /* 5 */
    <a name="XML_ERR_INVALID_HEX_CHARREF">XML_ERR_INVALID_HEX_CHARREF</a> = 6 /* 6 */
    <a name="XML_ERR_INVALID_DEC_CHARREF">XML_ERR_INVALID_DEC_CHARREF</a> = 7 /* 7 */
    <a name="XML_ERR_INVALID_CHARREF">XML_ERR_INVALID_CHARREF</a> = 8 /* 8 */
    <a name="XML_ERR_INVALID_CHAR">XML_ERR_INVALID_CHAR</a> = 9 /* 9 */
    <a name="XML_ERR_CHARREF_AT_EOF">XML_ERR_CHARREF_AT_EOF</a> = 10 /* 10 */
    <a name="XML_ERR_CHARREF_IN_PROLOG">XML_ERR_CHARREF_IN_PROLOG</a> = 11 /* 11 */
    <a name="XML_ERR_CHARREF_IN_EPILOG">XML_ERR_CHARREF_IN_EPILOG</a> = 12 /* 12 */
    <a name="XML_ERR_CHARREF_IN_DTD">XML_ERR_CHARREF_IN_DTD</a> = 13 /* 13 */
    <a name="XML_ERR_ENTITYREF_AT_EOF">XML_ERR_ENTITYREF_AT_EOF</a> = 14 /* 14 */
    <a name="XML_ERR_ENTITYREF_IN_PROLOG">XML_ERR_ENTITYREF_IN_PROLOG</a> = 15 /* 15 */
    <a name="XML_ERR_ENTITYREF_IN_EPILOG">XML_ERR_ENTITYREF_IN_EPILOG</a> = 16 /* 16 */
    <a name="XML_ERR_ENTITYREF_IN_DTD">XML_ERR_ENTITYREF_IN_DTD</a> = 17 /* 17 */
    <a name="XML_ERR_PEREF_AT_EOF">XML_ERR_PEREF_AT_EOF</a> = 18 /* 18 */
    <a name="XML_ERR_PEREF_IN_PROLOG">XML_ERR_PEREF_IN_PROLOG</a> = 19 /* 19 */
    <a name="XML_ERR_PEREF_IN_EPILOG">XML_ERR_PEREF_IN_EPILOG</a> = 20 /* 20 */
    <a name="XML_ERR_PEREF_IN_INT_SUBSET">XML_ERR_PEREF_IN_INT_SUBSET</a> = 21 /* 21 */
    <a name="XML_ERR_ENTITYREF_NO_NAME">XML_ERR_ENTITYREF_NO_NAME</a> = 22 /* 22 */
    <a name="XML_ERR_ENTITYREF_SEMICOL_MISSING">XML_ERR_ENTITYREF_SEMICOL_MISSING</a> = 23 /* 23 */
    <a name="XML_ERR_PEREF_NO_NAME">XML_ERR_PEREF_NO_NAME</a> = 24 /* 24 */
    <a name="XML_ERR_PEREF_SEMICOL_MISSING">XML_ERR_PEREF_SEMICOL_MISSING</a> = 25 /* 25 */
    <a name="XML_ERR_UNDECLARED_ENTITY">XML_ERR_UNDECLARED_ENTITY</a> = 26 /* 26 */
    <a name="XML_WAR_UNDECLARED_ENTITY">XML_WAR_UNDECLARED_ENTITY</a> = 27 /* 27 */
    <a name="XML_ERR_UNPARSED_ENTITY">XML_ERR_UNPARSED_ENTITY</a> = 28 /* 28 */
    <a name="XML_ERR_ENTITY_IS_EXTERNAL">XML_ERR_ENTITY_IS_EXTERNAL</a> = 29 /* 29 */
    <a name="XML_ERR_ENTITY_IS_PARAMETER">XML_ERR_ENTITY_IS_PARAMETER</a> = 30 /* 30 */
    <a name="XML_ERR_UNKNOWN_ENCODING">XML_ERR_UNKNOWN_ENCODING</a> = 31 /* 31 */
    <a name="XML_ERR_UNSUPPORTED_ENCODING">XML_ERR_UNSUPPORTED_ENCODING</a> = 32 /* 32 */
    <a name="XML_ERR_STRING_NOT_STARTED">XML_ERR_STRING_NOT_STARTED</a> = 33 /* 33 */
    <a name="XML_ERR_STRING_NOT_CLOSED">XML_ERR_STRING_NOT_CLOSED</a> = 34 /* 34 */
    <a name="XML_ERR_NS_DECL_ERROR">XML_ERR_NS_DECL_ERROR</a> = 35 /* 35 */
    <a name="XML_ERR_ENTITY_NOT_STARTED">XML_ERR_ENTITY_NOT_STARTED</a> = 36 /* 36 */
    <a name="XML_ERR_ENTITY_NOT_FINISHED">XML_ERR_ENTITY_NOT_FINISHED</a> = 37 /* 37 */
    <a name="XML_ERR_LT_IN_ATTRIBUTE">XML_ERR_LT_IN_ATTRIBUTE</a> = 38 /* 38 */
    <a name="XML_ERR_ATTRIBUTE_NOT_STARTED">XML_ERR_ATTRIBUTE_NOT_STARTED</a> = 39 /* 39 */
    <a name="XML_ERR_ATTRIBUTE_NOT_FINISHED">XML_ERR_ATTRIBUTE_NOT_FINISHED</a> = 40 /* 40 */
    <a name="XML_ERR_ATTRIBUTE_WITHOUT_VALUE">XML_ERR_ATTRIBUTE_WITHOUT_VALUE</a> = 41 /* 41 */
    <a name="XML_ERR_ATTRIBUTE_REDEFINED">XML_ERR_ATTRIBUTE_REDEFINED</a> = 42 /* 42 */
    <a name="XML_ERR_LITERAL_NOT_STARTED">XML_ERR_LITERAL_NOT_STARTED</a> = 43 /* 43 */
    <a name="XML_ERR_LITERAL_NOT_FINISHED">XML_ERR_LITERAL_NOT_FINISHED</a> = 44 /* 44 */
    <a name="XML_ERR_COMMENT_NOT_FINISHED">XML_ERR_COMMENT_NOT_FINISHED</a> = 45 /* 45 */
    <a name="XML_ERR_PI_NOT_STARTED">XML_ERR_PI_NOT_STARTED</a> = 46 /* 46 */
    <a name="XML_ERR_PI_NOT_FINISHED">XML_ERR_PI_NOT_FINISHED</a> = 47 /* 47 */
    <a name="XML_ERR_NOTATION_NOT_STARTED">XML_ERR_NOTATION_NOT_STARTED</a> = 48 /* 48 */
    <a name="XML_ERR_NOTATION_NOT_FINISHED">XML_ERR_NOTATION_NOT_FINISHED</a> = 49 /* 49 */
    <a name="XML_ERR_ATTLIST_NOT_STARTED">XML_ERR_ATTLIST_NOT_STARTED</a> = 50 /* 50 */
    <a name="XML_ERR_ATTLIST_NOT_FINISHED">XML_ERR_ATTLIST_NOT_FINISHED</a> = 51 /* 51 */
    <a name="XML_ERR_MIXED_NOT_STARTED">XML_ERR_MIXED_NOT_STARTED</a> = 52 /* 52 */
    <a name="XML_ERR_MIXED_NOT_FINISHED">XML_ERR_MIXED_NOT_FINISHED</a> = 53 /* 53 */
    <a name="XML_ERR_ELEMCONTENT_NOT_STARTED">XML_ERR_ELEMCONTENT_NOT_STARTED</a> = 54 /* 54 */
    <a name="XML_ERR_ELEMCONTENT_NOT_FINISHED">XML_ERR_ELEMCONTENT_NOT_FINISHED</a> = 55 /* 55 */
    <a name="XML_ERR_XMLDECL_NOT_STARTED">XML_ERR_XMLDECL_NOT_STARTED</a> = 56 /* 56 */
    <a name="XML_ERR_XMLDECL_NOT_FINISHED">XML_ERR_XMLDECL_NOT_FINISHED</a> = 57 /* 57 */
    <a name="XML_ERR_CONDSEC_NOT_STARTED">XML_ERR_CONDSEC_NOT_STARTED</a> = 58 /* 58 */
    <a name="XML_ERR_CONDSEC_NOT_FINISHED">XML_ERR_CONDSEC_NOT_FINISHED</a> = 59 /* 59 */
    <a name="XML_ERR_EXT_SUBSET_NOT_FINISHED">XML_ERR_EXT_SUBSET_NOT_FINISHED</a> = 60 /* 60 */
    <a name="XML_ERR_DOCTYPE_NOT_FINISHED">XML_ERR_DOCTYPE_NOT_FINISHED</a> = 61 /* 61 */
    <a name="XML_ERR_MISPLACED_CDATA_END">XML_ERR_MISPLACED_CDATA_END</a> = 62 /* 62 */
    <a name="XML_ERR_CDATA_NOT_FINISHED">XML_ERR_CDATA_NOT_FINISHED</a> = 63 /* 63 */
    <a name="XML_ERR_RESERVED_XML_NAME">XML_ERR_RESERVED_XML_NAME</a> = 64 /* 64 */
    <a name="XML_ERR_SPACE_REQUIRED">XML_ERR_SPACE_REQUIRED</a> = 65 /* 65 */
    <a name="XML_ERR_SEPARATOR_REQUIRED">XML_ERR_SEPARATOR_REQUIRED</a> = 66 /* 66 */
    <a name="XML_ERR_NMTOKEN_REQUIRED">XML_ERR_NMTOKEN_REQUIRED</a> = 67 /* 67 */
    <a name="XML_ERR_NAME_REQUIRED">XML_ERR_NAME_REQUIRED</a> = 68 /* 68 */
    <a name="XML_ERR_PCDATA_REQUIRED">XML_ERR_PCDATA_REQUIRED</a> = 69 /* 69 */
    <a name="XML_ERR_URI_REQUIRED">XML_ERR_URI_REQUIRED</a> = 70 /* 70 */
    <a name="XML_ERR_PUBID_REQUIRED">XML_ERR_PUBID_REQUIRED</a> = 71 /* 71 */
    <a name="XML_ERR_LT_REQUIRED">XML_ERR_LT_REQUIRED</a> = 72 /* 72 */
    <a name="XML_ERR_GT_REQUIRED">XML_ERR_GT_REQUIRED</a> = 73 /* 73 */
    <a name="XML_ERR_LTSLASH_REQUIRED">XML_ERR_LTSLASH_REQUIRED</a> = 74 /* 74 */
    <a name="XML_ERR_EQUAL_REQUIRED">XML_ERR_EQUAL_REQUIRED</a> = 75 /* 75 */
    <a name="XML_ERR_TAG_NAME_MISMATCH">XML_ERR_TAG_NAME_MISMATCH</a> = 76 /* 76 */
    <a name="XML_ERR_TAG_NOT_FINISHED">XML_ERR_TAG_NOT_FINISHED</a> = 77 /* 77 */
    <a name="XML_ERR_STANDALONE_VALUE">XML_ERR_STANDALONE_VALUE</a> = 78 /* 78 */
    <a name="XML_ERR_ENCODING_NAME">XML_ERR_ENCODING_NAME</a> = 79 /* 79 */
    <a name="XML_ERR_HYPHEN_IN_COMMENT">XML_ERR_HYPHEN_IN_COMMENT</a> = 80 /* 80 */
    <a name="XML_ERR_INVALID_ENCODING">XML_ERR_INVALID_ENCODING</a> = 81 /* 81 */
    <a name="XML_ERR_EXT_ENTITY_STANDALONE">XML_ERR_EXT_ENTITY_STANDALONE</a> = 82 /* 82 */
    <a name="XML_ERR_CONDSEC_INVALID">XML_ERR_CONDSEC_INVALID</a> = 83 /* 83 */
    <a name="XML_ERR_VALUE_REQUIRED">XML_ERR_VALUE_REQUIRED</a> = 84 /* 84 */
    <a name="XML_ERR_NOT_WELL_BALANCED">XML_ERR_NOT_WELL_BALANCED</a> = 85 /* 85 */
    <a name="XML_ERR_EXTRA_CONTENT">XML_ERR_EXTRA_CONTENT</a> = 86 /* 86 */
    <a name="XML_ERR_ENTITY_CHAR_ERROR">XML_ERR_ENTITY_CHAR_ERROR</a> = 87 /* 87 */
    <a name="XML_ERR_ENTITY_PE_INTERNAL">XML_ERR_ENTITY_PE_INTERNAL</a> = 88 /* 88 */
    <a name="XML_ERR_ENTITY_LOOP">XML_ERR_ENTITY_LOOP</a> = 89 /* 89 */
    <a name="XML_ERR_ENTITY_BOUNDARY">XML_ERR_ENTITY_BOUNDARY</a> = 90 /* 90 */
    <a name="XML_ERR_INVALID_URI">XML_ERR_INVALID_URI</a> = 91 /* 91 */
    <a name="XML_ERR_URI_FRAGMENT">XML_ERR_URI_FRAGMENT</a> = 92 /* 92 */
    <a name="XML_WAR_CATALOG_PI">XML_WAR_CATALOG_PI</a> = 93 /* 93 */
    <a name="XML_ERR_NO_DTD">XML_ERR_NO_DTD</a> = 94 /* 94 */
    <a name="XML_ERR_CONDSEC_INVALID_KEYWORD">XML_ERR_CONDSEC_INVALID_KEYWORD</a> = 95 /* 95 */
    <a name="XML_ERR_VERSION_MISSING">XML_ERR_VERSION_MISSING</a> = 96 /* 96 */
    <a name="XML_WAR_UNKNOWN_VERSION">XML_WAR_UNKNOWN_VERSION</a> = 97 /* 97 */
    <a name="XML_WAR_LANG_VALUE">XML_WAR_LANG_VALUE</a> = 98 /* 98 */
    <a name="XML_WAR_NS_URI">XML_WAR_NS_URI</a> = 99 /* 99 */
    <a name="XML_WAR_NS_URI_RELATIVE">XML_WAR_NS_URI_RELATIVE</a> = 100 /* 100 */
    <a name="XML_ERR_MISSING_ENCODING">XML_ERR_MISSING_ENCODING</a> = 101 /* 101 */
    <a name="XML_WAR_SPACE_VALUE">XML_WAR_SPACE_VALUE</a> = 102 /* 102 */
    <a name="XML_ERR_NOT_STANDALONE">XML_ERR_NOT_STANDALONE</a> = 103 /* 103 */
    <a name="XML_ERR_ENTITY_PROCESSING">XML_ERR_ENTITY_PROCESSING</a> = 104 /* 104 */
    <a name="XML_ERR_NOTATION_PROCESSING">XML_ERR_NOTATION_PROCESSING</a> = 105 /* 105 */
    <a name="XML_WAR_NS_COLUMN">XML_WAR_NS_COLUMN</a> = 106 /* 106 */
    <a name="XML_WAR_ENTITY_REDEFINED">XML_WAR_ENTITY_REDEFINED</a> = 107 /* 107 */
    <a name="XML_ERR_UNKNOWN_VERSION">XML_ERR_UNKNOWN_VERSION</a> = 108 /* 108 */
    <a name="XML_ERR_VERSION_MISMATCH">XML_ERR_VERSION_MISMATCH</a> = 109 /* 109 */
    <a name="XML_ERR_NAME_TOO_LONG">XML_ERR_NAME_TOO_LONG</a> = 110 /* 110 */
    <a name="XML_ERR_USER_STOP">XML_ERR_USER_STOP</a> = 111 /* 111 */
    <a name="XML_NS_ERR_XML_NAMESPACE">XML_NS_ERR_XML_NAMESPACE</a> = 200
    <a name="XML_NS_ERR_UNDEFINED_NAMESPACE">XML_NS_ERR_UNDEFINED_NAMESPACE</a> = 201 /* 201 */
    <a name="XML_NS_ERR_QNAME">XML_NS_ERR_QNAME</a> = 202 /* 202 */
    <a name="XML_NS_ERR_ATTRIBUTE_REDEFINED">XML_NS_ERR_ATTRIBUTE_REDEFINED</a> = 203 /* 203 */
    <a name="XML_NS_ERR_EMPTY">XML_NS_ERR_EMPTY</a> = 204 /* 204 */
    <a name="XML_NS_ERR_COLON">XML_NS_ERR_COLON</a> = 205 /* 205 */
    <a name="XML_DTD_ATTRIBUTE_DEFAULT">XML_DTD_ATTRIBUTE_DEFAULT</a> = 500
    <a name="XML_DTD_ATTRIBUTE_REDEFINED">XML_DTD_ATTRIBUTE_REDEFINED</a> = 501 /* 501 */
    <a name="XML_DTD_ATTRIBUTE_VALUE">XML_DTD_ATTRIBUTE_VALUE</a> = 502 /* 502 */
    <a name="XML_DTD_CONTENT_ERROR">XML_DTD_CONTENT_ERROR</a> = 503 /* 503 */
    <a name="XML_DTD_CONTENT_MODEL">XML_DTD_CONTENT_MODEL</a> = 504 /* 504 */
    <a name="XML_DTD_CONTENT_NOT_DETERMINIST">XML_DTD_CONTENT_NOT_DETERMINIST</a> = 505 /* 505 */
    <a name="XML_DTD_DIFFERENT_PREFIX">XML_DTD_DIFFERENT_PREFIX</a> = 506 /* 506 */
    <a name="XML_DTD_ELEM_DEFAULT_NAMESPACE">XML_DTD_ELEM_DEFAULT_NAMESPACE</a> = 507 /* 507 */
    <a name="XML_DTD_ELEM_NAMESPACE">XML_DTD_ELEM_NAMESPACE</a> = 508 /* 508 */
    <a name="XML_DTD_ELEM_REDEFINED">XML_DTD_ELEM_REDEFINED</a> = 509 /* 509 */
    <a name="XML_DTD_EMPTY_NOTATION">XML_DTD_EMPTY_NOTATION</a> = 510 /* 510 */
    <a name="XML_DTD_ENTITY_TYPE">XML_DTD_ENTITY_TYPE</a> = 511 /* 511 */
    <a name="XML_DTD_ID_FIXED">XML_DTD_ID_FIXED</a> = 512 /* 512 */
    <a name="XML_DTD_ID_REDEFINED">XML_DTD_ID_REDEFINED</a> = 513 /* 513 */
    <a name="XML_DTD_ID_SUBSET">XML_DTD_ID_SUBSET</a> = 514 /* 514 */
    <a name="XML_DTD_INVALID_CHILD">XML_DTD_INVALID_CHILD</a> = 515 /* 515 */
    <a name="XML_DTD_INVALID_DEFAULT">XML_DTD_INVALID_DEFAULT</a> = 516 /* 516 */
    <a name="XML_DTD_LOAD_ERROR">XML_DTD_LOAD_ERROR</a> = 517 /* 517 */
    <a name="XML_DTD_MISSING_ATTRIBUTE">XML_DTD_MISSING_ATTRIBUTE</a> = 518 /* 518 */
    <a name="XML_DTD_MIXED_CORRUPT">XML_DTD_MIXED_CORRUPT</a> = 519 /* 519 */
    <a name="XML_DTD_MULTIPLE_ID">XML_DTD_MULTIPLE_ID</a> = 520 /* 520 */
    <a name="XML_DTD_NO_DOC">XML_DTD_NO_DOC</a> = 521 /* 521 */
    <a name="XML_DTD_NO_DTD">XML_DTD_NO_DTD</a> = 522 /* 522 */
    <a name="XML_DTD_NO_ELEM_NAME">XML_DTD_NO_ELEM_NAME</a> = 523 /* 523 */
    <a name="XML_DTD_NO_PREFIX">XML_DTD_NO_PREFIX</a> = 524 /* 524 */
    <a name="XML_DTD_NO_ROOT">XML_DTD_NO_ROOT</a> = 525 /* 525 */
    <a name="XML_DTD_NOTATION_REDEFINED">XML_DTD_NOTATION_REDEFINED</a> = 526 /* 526 */
    <a name="XML_DTD_NOTATION_VALUE">XML_DTD_NOTATION_VALUE</a> = 527 /* 527 */
    <a name="XML_DTD_NOT_EMPTY">XML_DTD_NOT_EMPTY</a> = 528 /* 528 */
    <a name="XML_DTD_NOT_PCDATA">XML_DTD_NOT_PCDATA</a> = 529 /* 529 */
    <a name="XML_DTD_NOT_STANDALONE">XML_DTD_NOT_STANDALONE</a> = 530 /* 530 */
    <a name="XML_DTD_ROOT_NAME">XML_DTD_ROOT_NAME</a> = 531 /* 531 */
    <a name="XML_DTD_STANDALONE_WHITE_SPACE">XML_DTD_STANDALONE_WHITE_SPACE</a> = 532 /* 532 */
    <a name="XML_DTD_UNKNOWN_ATTRIBUTE">XML_DTD_UNKNOWN_ATTRIBUTE</a> = 533 /* 533 */
    <a name="XML_DTD_UNKNOWN_ELEM">XML_DTD_UNKNOWN_ELEM</a> = 534 /* 534 */
    <a name="XML_DTD_UNKNOWN_ENTITY">XML_DTD_UNKNOWN_ENTITY</a> = 535 /* 535 */
    <a name="XML_DTD_UNKNOWN_ID">XML_DTD_UNKNOWN_ID</a> = 536 /* 536 */
    <a name="XML_DTD_UNKNOWN_NOTATION">XML_DTD_UNKNOWN_NOTATION</a> = 537 /* 537 */
    <a name="XML_DTD_STANDALONE_DEFAULTED">XML_DTD_STANDALONE_DEFAULTED</a> = 538 /* 538 */
    <a name="XML_DTD_XMLID_VALUE">XML_DTD_XMLID_VALUE</a> = 539 /* 539 */
    <a name="XML_DTD_XMLID_TYPE">XML_DTD_XMLID_TYPE</a> = 540 /* 540 */
    <a name="XML_DTD_DUP_TOKEN">XML_DTD_DUP_TOKEN</a> = 541 /* 541 */
    <a name="XML_HTML_STRUCURE_ERROR">XML_HTML_STRUCURE_ERROR</a> = 800
    <a name="XML_HTML_UNKNOWN_TAG">XML_HTML_UNKNOWN_TAG</a> = 801 /* 801 */
    <a name="XML_RNGP_ANYNAME_ATTR_ANCESTOR">XML_RNGP_ANYNAME_ATTR_ANCESTOR</a> = 1000
    <a name="XML_RNGP_ATTR_CONFLICT">XML_RNGP_ATTR_CONFLICT</a> = 1001 /* 1001 */
    <a name="XML_RNGP_ATTRIBUTE_CHILDREN">XML_RNGP_ATTRIBUTE_CHILDREN</a> = 1002 /* 1002 */
    <a name="XML_RNGP_ATTRIBUTE_CONTENT">XML_RNGP_ATTRIBUTE_CONTENT</a> = 1003 /* 1003 */
    <a name="XML_RNGP_ATTRIBUTE_EMPTY">XML_RNGP_ATTRIBUTE_EMPTY</a> = 1004 /* 1004 */
    <a name="XML_RNGP_ATTRIBUTE_NOOP">XML_RNGP_ATTRIBUTE_NOOP</a> = 1005 /* 1005 */
    <a name="XML_RNGP_CHOICE_CONTENT">XML_RNGP_CHOICE_CONTENT</a> = 1006 /* 1006 */
    <a name="XML_RNGP_CHOICE_EMPTY">XML_RNGP_CHOICE_EMPTY</a> = 1007 /* 1007 */
    <a name="XML_RNGP_CREATE_FAILURE">XML_RNGP_CREATE_FAILURE</a> = 1008 /* 1008 */
    <a name="XML_RNGP_DATA_CONTENT">XML_RNGP_DATA_CONTENT</a> = 1009 /* 1009 */
    <a name="XML_RNGP_DEF_CHOICE_AND_INTERLEAVE">XML_RNGP_DEF_CHOICE_AND_INTERLEAVE</a> = 1010 /* 1010 */
    <a name="XML_RNGP_DEFINE_CREATE_FAILED">XML_RNGP_DEFINE_CREATE_FAILED</a> = 1011 /* 1011 */
    <a name="XML_RNGP_DEFINE_EMPTY">XML_RNGP_DEFINE_EMPTY</a> = 1012 /* 1012 */
    <a name="XML_RNGP_DEFINE_MISSING">XML_RNGP_DEFINE_MISSING</a> = 1013 /* 1013 */
    <a name="XML_RNGP_DEFINE_NAME_MISSING">XML_RNGP_DEFINE_NAME_MISSING</a> = 1014 /* 1014 */
    <a name="XML_RNGP_ELEM_CONTENT_EMPTY">XML_RNGP_ELEM_CONTENT_EMPTY</a> = 1015 /* 1015 */
    <a name="XML_RNGP_ELEM_CONTENT_ERROR">XML_RNGP_ELEM_CONTENT_ERROR</a> = 1016 /* 1016 */
    <a name="XML_RNGP_ELEMENT_EMPTY">XML_RNGP_ELEMENT_EMPTY</a> = 1017 /* 1017 */
    <a name="XML_RNGP_ELEMENT_CONTENT">XML_RNGP_ELEMENT_CONTENT</a> = 1018 /* 1018 */
    <a name="XML_RNGP_ELEMENT_NAME">XML_RNGP_ELEMENT_NAME</a> = 1019 /* 1019 */
    <a name="XML_RNGP_ELEMENT_NO_CONTENT">XML_RNGP_ELEMENT_NO_CONTENT</a> = 1020 /* 1020 */
    <a name="XML_RNGP_ELEM_TEXT_CONFLICT">XML_RNGP_ELEM_TEXT_CONFLICT</a> = 1021 /* 1021 */
    <a name="XML_RNGP_EMPTY">XML_RNGP_EMPTY</a> = 1022 /* 1022 */
    <a name="XML_RNGP_EMPTY_CONSTRUCT">XML_RNGP_EMPTY_CONSTRUCT</a> = 1023 /* 1023 */
    <a name="XML_RNGP_EMPTY_CONTENT">XML_RNGP_EMPTY_CONTENT</a> = 1024 /* 1024 */
    <a name="XML_RNGP_EMPTY_NOT_EMPTY">XML_RNGP_EMPTY_NOT_EMPTY</a> = 1025 /* 1025 */
    <a name="XML_RNGP_ERROR_TYPE_LIB">XML_RNGP_ERROR_TYPE_LIB</a> = 1026 /* 1026 */
    <a name="XML_RNGP_EXCEPT_EMPTY">XML_RNGP_EXCEPT_EMPTY</a> = 1027 /* 1027 */
    <a name="XML_RNGP_EXCEPT_MISSING">XML_RNGP_EXCEPT_MISSING</a> = 1028 /* 1028 */
    <a name="XML_RNGP_EXCEPT_MULTIPLE">XML_RNGP_EXCEPT_MULTIPLE</a> = 1029 /* 1029 */
    <a name="XML_RNGP_EXCEPT_NO_CONTENT">XML_RNGP_EXCEPT_NO_CONTENT</a> = 1030 /* 1030 */
    <a name="XML_RNGP_EXTERNALREF_EMTPY">XML_RNGP_EXTERNALREF_EMTPY</a> = 1031 /* 1031 */
    <a name="XML_RNGP_EXTERNAL_REF_FAILURE">XML_RNGP_EXTERNAL_REF_FAILURE</a> = 1032 /* 1032 */
    <a name="XML_RNGP_EXTERNALREF_RECURSE">XML_RNGP_EXTERNALREF_RECURSE</a> = 1033 /* 1033 */
    <a name="XML_RNGP_FORBIDDEN_ATTRIBUTE">XML_RNGP_FORBIDDEN_ATTRIBUTE</a> = 1034 /* 1034 */
    <a name="XML_RNGP_FOREIGN_ELEMENT">XML_RNGP_FOREIGN_ELEMENT</a> = 1035 /* 1035 */
    <a name="XML_RNGP_GRAMMAR_CONTENT">XML_RNGP_GRAMMAR_CONTENT</a> = 1036 /* 1036 */
    <a name="XML_RNGP_GRAMMAR_EMPTY">XML_RNGP_GRAMMAR_EMPTY</a> = 1037 /* 1037 */
    <a name="XML_RNGP_GRAMMAR_MISSING">XML_RNGP_GRAMMAR_MISSING</a> = 1038 /* 1038 */
    <a name="XML_RNGP_GRAMMAR_NO_START">XML_RNGP_GRAMMAR_NO_START</a> = 1039 /* 1039 */
    <a name="XML_RNGP_GROUP_ATTR_CONFLICT">XML_RNGP_GROUP_ATTR_CONFLICT</a> = 1040 /* 1040 */
    <a name="XML_RNGP_HREF_ERROR">XML_RNGP_HREF_ERROR</a> = 1041 /* 1041 */
    <a name="XML_RNGP_INCLUDE_EMPTY">XML_RNGP_INCLUDE_EMPTY</a> = 1042 /* 1042 */
    <a name="XML_RNGP_INCLUDE_FAILURE">XML_RNGP_INCLUDE_FAILURE</a> = 1043 /* 1043 */
    <a name="XML_RNGP_INCLUDE_RECURSE">XML_RNGP_INCLUDE_RECURSE</a> = 1044 /* 1044 */
    <a name="XML_RNGP_INTERLEAVE_ADD">XML_RNGP_INTERLEAVE_ADD</a> = 1045 /* 1045 */
    <a name="XML_RNGP_INTERLEAVE_CREATE_FAILED">XML_RNGP_INTERLEAVE_CREATE_FAILED</a> = 1046 /* 1046 */
    <a name="XML_RNGP_INTERLEAVE_EMPTY">XML_RNGP_INTERLEAVE_EMPTY</a> = 1047 /* 1047 */
    <a name="XML_RNGP_INTERLEAVE_NO_CONTENT">XML_RNGP_INTERLEAVE_NO_CONTENT</a> = 1048 /* 1048 */
    <a name="XML_RNGP_INVALID_DEFINE_NAME">XML_RNGP_INVALID_DEFINE_NAME</a> = 1049 /* 1049 */
    <a name="XML_RNGP_INVALID_URI">XML_RNGP_INVALID_URI</a> = 1050 /* 1050 */
    <a name="XML_RNGP_INVALID_VALUE">XML_RNGP_INVALID_VALUE</a> = 1051 /* 1051 */
    <a name="XML_RNGP_MISSING_HREF">XML_RNGP_MISSING_HREF</a> = 1052 /* 1052 */
    <a name="XML_RNGP_NAME_MISSING">XML_RNGP_NAME_MISSING</a> = 1053 /* 1053 */
    <a name="XML_RNGP_NEED_COMBINE">XML_RNGP_NEED_COMBINE</a> = 1054 /* 1054 */
    <a name="XML_RNGP_NOTALLOWED_NOT_EMPTY">XML_RNGP_NOTALLOWED_NOT_EMPTY</a> = 1055 /* 1055 */
    <a name="XML_RNGP_NSNAME_ATTR_ANCESTOR">XML_RNGP_NSNAME_ATTR_ANCESTOR</a> = 1056 /* 1056 */
    <a name="XML_RNGP_NSNAME_NO_NS">XML_RNGP_NSNAME_NO_NS</a> = 1057 /* 1057 */
    <a name="XML_RNGP_PARAM_FORBIDDEN">XML_RNGP_PARAM_FORBIDDEN</a> = 1058 /* 1058 */
    <a name="XML_RNGP_PARAM_NAME_MISSING">XML_RNGP_PARAM_NAME_MISSING</a> = 1059 /* 1059 */
    <a name="XML_RNGP_PARENTREF_CREATE_FAILED">XML_RNGP_PARENTREF_CREATE_FAILED</a> = 1060 /* 1060 */
    <a name="XML_RNGP_PARENTREF_NAME_INVALID">XML_RNGP_PARENTREF_NAME_INVALID</a> = 1061 /* 1061 */
    <a name="XML_RNGP_PARENTREF_NO_NAME">XML_RNGP_PARENTREF_NO_NAME</a> = 1062 /* 1062 */
    <a name="XML_RNGP_PARENTREF_NO_PARENT">XML_RNGP_PARENTREF_NO_PARENT</a> = 1063 /* 1063 */
    <a name="XML_RNGP_PARENTREF_NOT_EMPTY">XML_RNGP_PARENTREF_NOT_EMPTY</a> = 1064 /* 1064 */
    <a name="XML_RNGP_PARSE_ERROR">XML_RNGP_PARSE_ERROR</a> = 1065 /* 1065 */
    <a name="XML_RNGP_PAT_ANYNAME_EXCEPT_ANYNAME">XML_RNGP_PAT_ANYNAME_EXCEPT_ANYNAME</a> = 1066 /* 1066 */
    <a name="XML_RNGP_PAT_ATTR_ATTR">XML_RNGP_PAT_ATTR_ATTR</a> = 1067 /* 1067 */
    <a name="XML_RNGP_PAT_ATTR_ELEM">XML_RNGP_PAT_ATTR_ELEM</a> = 1068 /* 1068 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_ATTR">XML_RNGP_PAT_DATA_EXCEPT_ATTR</a> = 1069 /* 1069 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_ELEM">XML_RNGP_PAT_DATA_EXCEPT_ELEM</a> = 1070 /* 1070 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_EMPTY">XML_RNGP_PAT_DATA_EXCEPT_EMPTY</a> = 1071 /* 1071 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_GROUP">XML_RNGP_PAT_DATA_EXCEPT_GROUP</a> = 1072 /* 1072 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_INTERLEAVE">XML_RNGP_PAT_DATA_EXCEPT_INTERLEAVE</a> = 1073 /* 1073 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_LIST">XML_RNGP_PAT_DATA_EXCEPT_LIST</a> = 1074 /* 1074 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_ONEMORE">XML_RNGP_PAT_DATA_EXCEPT_ONEMORE</a> = 1075 /* 1075 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_REF">XML_RNGP_PAT_DATA_EXCEPT_REF</a> = 1076 /* 1076 */
    <a name="XML_RNGP_PAT_DATA_EXCEPT_TEXT">XML_RNGP_PAT_DATA_EXCEPT_TEXT</a> = 1077 /* 1077 */
    <a name="XML_RNGP_PAT_LIST_ATTR">XML_RNGP_PAT_LIST_ATTR</a> = 1078 /* 1078 */
    <a name="XML_RNGP_PAT_LIST_ELEM">XML_RNGP_PAT_LIST_ELEM</a> = 1079 /* 1079 */
    <a name="XML_RNGP_PAT_LIST_INTERLEAVE">XML_RNGP_PAT_LIST_INTERLEAVE</a> = 1080 /* 1080 */
    <a name="XML_RNGP_PAT_LIST_LIST">XML_RNGP_PAT_LIST_LIST</a> = 1081 /* 1081 */
    <a name="XML_RNGP_PAT_LIST_REF">XML_RNGP_PAT_LIST_REF</a> = 1082 /* 1082 */
    <a name="XML_RNGP_PAT_LIST_TEXT">XML_RNGP_PAT_LIST_TEXT</a> = 1083 /* 1083 */
    <a name="XML_RNGP_PAT_NSNAME_EXCEPT_ANYNAME">XML_RNGP_PAT_NSNAME_EXCEPT_ANYNAME</a> = 1084 /* 1084 */
    <a name="XML_RNGP_PAT_NSNAME_EXCEPT_NSNAME">XML_RNGP_PAT_NSNAME_EXCEPT_NSNAME</a> = 1085 /* 1085 */
    <a name="XML_RNGP_PAT_ONEMORE_GROUP_ATTR">XML_RNGP_PAT_ONEMORE_GROUP_ATTR</a> = 1086 /* 1086 */
    <a name="XML_RNGP_PAT_ONEMORE_INTERLEAVE_ATTR">XML_RNGP_PAT_ONEMORE_INTERLEAVE_ATTR</a> = 1087 /* 1087 */
    <a name="XML_RNGP_PAT_START_ATTR">XML_RNGP_PAT_START_ATTR</a> = 1088 /* 1088 */
    <a name="XML_RNGP_PAT_START_DATA">XML_RNGP_PAT_START_DATA</a> = 1089 /* 1089 */
    <a name="XML_RNGP_PAT_START_EMPTY">XML_RNGP_PAT_START_EMPTY</a> = 1090 /* 1090 */
    <a name="XML_RNGP_PAT_START_GROUP">XML_RNGP_PAT_START_GROUP</a> = 1091 /* 1091 */
    <a name="XML_RNGP_PAT_START_INTERLEAVE">XML_RNGP_PAT_START_INTERLEAVE</a> = 1092 /* 1092 */
    <a name="XML_RNGP_PAT_START_LIST">XML_RNGP_PAT_START_LIST</a> = 1093 /* 1093 */
    <a name="XML_RNGP_PAT_START_ONEMORE">XML_RNGP_PAT_START_ONEMORE</a> = 1094 /* 1094 */
    <a name="XML_RNGP_PAT_START_TEXT">XML_RNGP_PAT_START_TEXT</a> = 1095 /* 1095 */
    <a name="XML_RNGP_PAT_START_VALUE">XML_RNGP_PAT_START_VALUE</a> = 1096 /* 1096 */
    <a name="XML_RNGP_PREFIX_UNDEFINED">XML_RNGP_PREFIX_UNDEFINED</a> = 1097 /* 1097 */
    <a name="XML_RNGP_REF_CREATE_FAILED">XML_RNGP_REF_CREATE_FAILED</a> = 1098 /* 1098 */
    <a name="XML_RNGP_REF_CYCLE">XML_RNGP_REF_CYCLE</a> = 1099 /* 1099 */
    <a name="XML_RNGP_REF_NAME_INVALID">XML_RNGP_REF_NAME_INVALID</a> = 1100 /* 1100 */
    <a name="XML_RNGP_REF_NO_DEF">XML_RNGP_REF_NO_DEF</a> = 1101 /* 1101 */
    <a name="XML_RNGP_REF_NO_NAME">XML_RNGP_REF_NO_NAME</a> = 1102 /* 1102 */
    <a name="XML_RNGP_REF_NOT_EMPTY">XML_RNGP_REF_NOT_EMPTY</a> = 1103 /* 1103 */
    <a name="XML_RNGP_START_CHOICE_AND_INTERLEAVE">XML_RNGP_START_CHOICE_AND_INTERLEAVE</a> = 1104 /* 1104 */
    <a name="XML_RNGP_START_CONTENT">XML_RNGP_START_CONTENT</a> = 1105 /* 1105 */
    <a name="XML_RNGP_START_EMPTY">XML_RNGP_START_EMPTY</a> = 1106 /* 1106 */
    <a name="XML_RNGP_START_MISSING">XML_RNGP_START_MISSING</a> = 1107 /* 1107 */
    <a name="XML_RNGP_TEXT_EXPECTED">XML_RNGP_TEXT_EXPECTED</a> = 1108 /* 1108 */
    <a name="XML_RNGP_TEXT_HAS_CHILD">XML_RNGP_TEXT_HAS_CHILD</a> = 1109 /* 1109 */
    <a name="XML_RNGP_TYPE_MISSING">XML_RNGP_TYPE_MISSING</a> = 1110 /* 1110 */
    <a name="XML_RNGP_TYPE_NOT_FOUND">XML_RNGP_TYPE_NOT_FOUND</a> = 1111 /* 1111 */
    <a name="XML_RNGP_TYPE_VALUE">XML_RNGP_TYPE_VALUE</a> = 1112 /* 1112 */
    <a name="XML_RNGP_UNKNOWN_ATTRIBUTE">XML_RNGP_UNKNOWN_ATTRIBUTE</a> = 1113 /* 1113 */
    <a name="XML_RNGP_UNKNOWN_COMBINE">XML_RNGP_UNKNOWN_COMBINE</a> = 1114 /* 1114 */
    <a name="XML_RNGP_UNKNOWN_CONSTRUCT">XML_RNGP_UNKNOWN_CONSTRUCT</a> = 1115 /* 1115 */
    <a name="XML_RNGP_UNKNOWN_TYPE_LIB">XML_RNGP_UNKNOWN_TYPE_LIB</a> = 1116 /* 1116 */
    <a name="XML_RNGP_URI_FRAGMENT">XML_RNGP_URI_FRAGMENT</a> = 1117 /* 1117 */
    <a name="XML_RNGP_URI_NOT_ABSOLUTE">XML_RNGP_URI_NOT_ABSOLUTE</a> = 1118 /* 1118 */
    <a name="XML_RNGP_VALUE_EMPTY">XML_RNGP_VALUE_EMPTY</a> = 1119 /* 1119 */
    <a name="XML_RNGP_VALUE_NO_CONTENT">XML_RNGP_VALUE_NO_CONTENT</a> = 1120 /* 1120 */
    <a name="XML_RNGP_XMLNS_NAME">XML_RNGP_XMLNS_NAME</a> = 1121 /* 1121 */
    <a name="XML_RNGP_XML_NS">XML_RNGP_XML_NS</a> = 1122 /* 1122 */
    <a name="XML_XPATH_EXPRESSION_OK">XML_XPATH_EXPRESSION_OK</a> = 1200
    <a name="XML_XPATH_NUMBER_ERROR">XML_XPATH_NUMBER_ERROR</a> = 1201 /* 1201 */
    <a name="XML_XPATH_UNFINISHED_LITERAL_ERROR">XML_XPATH_UNFINISHED_LITERAL_ERROR</a> = 1202 /* 1202 */
    <a name="XML_XPATH_START_LITERAL_ERROR">XML_XPATH_START_LITERAL_ERROR</a> = 1203 /* 1203 */
    <a name="XML_XPATH_VARIABLE_REF_ERROR">XML_XPATH_VARIABLE_REF_ERROR</a> = 1204 /* 1204 */
    <a name="XML_XPATH_UNDEF_VARIABLE_ERROR">XML_XPATH_UNDEF_VARIABLE_ERROR</a> = 1205 /* 1205 */
    <a name="XML_XPATH_INVALID_PREDICATE_ERROR">XML_XPATH_INVALID_PREDICATE_ERROR</a> = 1206 /* 1206 */
    <a name="XML_XPATH_EXPR_ERROR">XML_XPATH_EXPR_ERROR</a> = 1207 /* 1207 */
    <a name="XML_XPATH_UNCLOSED_ERROR">XML_XPATH_UNCLOSED_ERROR</a> = 1208 /* 1208 */
    <a name="XML_XPATH_UNKNOWN_FUNC_ERROR">XML_XPATH_UNKNOWN_FUNC_ERROR</a> = 1209 /* 1209 */
    <a name="XML_XPATH_INVALID_OPERAND">XML_XPATH_INVALID_OPERAND</a> = 1210 /* 1210 */
    <a name="XML_XPATH_INVALID_TYPE">XML_XPATH_INVALID_TYPE</a> = 1211 /* 1211 */
    <a name="XML_XPATH_INVALID_ARITY">XML_XPATH_INVALID_ARITY</a> = 1212 /* 1212 */
    <a name="XML_XPATH_INVALID_CTXT_SIZE">XML_XPATH_INVALID_CTXT_SIZE</a> = 1213 /* 1213 */
    <a name="XML_XPATH_INVALID_CTXT_POSITION">XML_XPATH_INVALID_CTXT_POSITION</a> = 1214 /* 1214 */
    <a name="XML_XPATH_MEMORY_ERROR">XML_XPATH_MEMORY_ERROR</a> = 1215 /* 1215 */
    <a name="XML_XPTR_SYNTAX_ERROR">XML_XPTR_SYNTAX_ERROR</a> = 1216 /* 1216 */
    <a name="XML_XPTR_RESOURCE_ERROR">XML_XPTR_RESOURCE_ERROR</a> = 1217 /* 1217 */
    <a name="XML_XPTR_SUB_RESOURCE_ERROR">XML_XPTR_SUB_RESOURCE_ERROR</a> = 1218 /* 1218 */
    <a name="XML_XPATH_UNDEF_PREFIX_ERROR">XML_XPATH_UNDEF_PREFIX_ERROR</a> = 1219 /* 1219 */
    <a name="XML_XPATH_ENCODING_ERROR">XML_XPATH_ENCODING_ERROR</a> = 1220 /* 1220 */
    <a name="XML_XPATH_INVALID_CHAR_ERROR">XML_XPATH_INVALID_CHAR_ERROR</a> = 1221 /* 1221 */
    <a name="XML_TREE_INVALID_HEX">XML_TREE_INVALID_HEX</a> = 1300
    <a name="XML_TREE_INVALID_DEC">XML_TREE_INVALID_DEC</a> = 1301 /* 1301 */
    <a name="XML_TREE_UNTERMINATED_ENTITY">XML_TREE_UNTERMINATED_ENTITY</a> = 1302 /* 1302 */
    <a name="XML_TREE_NOT_UTF8">XML_TREE_NOT_UTF8</a> = 1303 /* 1303 */
    <a name="XML_SAVE_NOT_UTF8">XML_SAVE_NOT_UTF8</a> = 1400
    <a name="XML_SAVE_CHAR_INVALID">XML_SAVE_CHAR_INVALID</a> = 1401 /* 1401 */
    <a name="XML_SAVE_NO_DOCTYPE">XML_SAVE_NO_DOCTYPE</a> = 1402 /* 1402 */
    <a name="XML_SAVE_UNKNOWN_ENCODING">XML_SAVE_UNKNOWN_ENCODING</a> = 1403 /* 1403 */
    <a name="XML_REGEXP_COMPILE_ERROR">XML_REGEXP_COMPILE_ERROR</a> = 1450
    <a name="XML_IO_UNKNOWN">XML_IO_UNKNOWN</a> = 1500
    <a name="XML_IO_EACCES">XML_IO_EACCES</a> = 1501 /* 1501 */
    <a name="XML_IO_EAGAIN">XML_IO_EAGAIN</a> = 1502 /* 1502 */
    <a name="XML_IO_EBADF">XML_IO_EBADF</a> = 1503 /* 1503 */
    <a name="XML_IO_EBADMSG">XML_IO_EBADMSG</a> = 1504 /* 1504 */
    <a name="XML_IO_EBUSY">XML_IO_EBUSY</a> = 1505 /* 1505 */
    <a name="XML_IO_ECANCELED">XML_IO_ECANCELED</a> = 1506 /* 1506 */
    <a name="XML_IO_ECHILD">XML_IO_ECHILD</a> = 1507 /* 1507 */
    <a name="XML_IO_EDEADLK">XML_IO_EDEADLK</a> = 1508 /* 1508 */
    <a name="XML_IO_EDOM">XML_IO_EDOM</a> = 1509 /* 1509 */
    <a name="XML_IO_EEXIST">XML_IO_EEXIST</a> = 1510 /* 1510 */
    <a name="XML_IO_EFAULT">XML_IO_EFAULT</a> = 1511 /* 1511 */
    <a name="XML_IO_EFBIG">XML_IO_EFBIG</a> = 1512 /* 1512 */
    <a name="XML_IO_EINPROGRESS">XML_IO_EINPROGRESS</a> = 1513 /* 1513 */
    <a name="XML_IO_EINTR">XML_IO_EINTR</a> = 1514 /* 1514 */
    <a name="XML_IO_EINVAL">XML_IO_EINVAL</a> = 1515 /* 1515 */
    <a name="XML_IO_EIO">XML_IO_EIO</a> = 1516 /* 1516 */
    <a name="XML_IO_EISDIR">XML_IO_EISDIR</a> = 1517 /* 1517 */
    <a name="XML_IO_EMFILE">XML_IO_EMFILE</a> = 1518 /* 1518 */
    <a name="XML_IO_EMLINK">XML_IO_EMLINK</a> = 1519 /* 1519 */
    <a name="XML_IO_EMSGSIZE">XML_IO_EMSGSIZE</a> = 1520 /* 1520 */
    <a name="XML_IO_ENAMETOOLONG">XML_IO_ENAMETOOLONG</a> = 1521 /* 1521 */
    <a name="XML_IO_ENFILE">XML_IO_ENFILE</a> = 1522 /* 1522 */
    <a name="XML_IO_ENODEV">XML_IO_ENODEV</a> = 1523 /* 1523 */
    <a name="XML_IO_ENOENT">XML_IO_ENOENT</a> = 1524 /* 1524 */
    <a name="XML_IO_ENOEXEC">XML_IO_ENOEXEC</a> = 1525 /* 1525 */
    <a name="XML_IO_ENOLCK">XML_IO_ENOLCK</a> = 1526 /* 1526 */
    <a name="XML_IO_ENOMEM">XML_IO_ENOMEM</a> = 1527 /* 1527 */
    <a name="XML_IO_ENOSPC">XML_IO_ENOSPC</a> = 1528 /* 1528 */
    <a name="XML_IO_ENOSYS">XML_IO_ENOSYS</a> = 1529 /* 1529 */
    <a name="XML_IO_ENOTDIR">XML_IO_ENOTDIR</a> = 1530 /* 1530 */
    <a name="XML_IO_ENOTEMPTY">XML_IO_ENOTEMPTY</a> = 1531 /* 1531 */
    <a name="XML_IO_ENOTSUP">XML_IO_ENOTSUP</a> = 1532 /* 1532 */
    <a name="XML_IO_ENOTTY">XML_IO_ENOTTY</a> = 1533 /* 1533 */
    <a name="XML_IO_ENXIO">XML_IO_ENXIO</a> = 1534 /* 1534 */
    <a name="XML_IO_EPERM">XML_IO_EPERM</a> = 1535 /* 1535 */
    <a name="XML_IO_EPIPE">XML_IO_EPIPE</a> = 1536 /* 1536 */
    <a name="XML_IO_ERANGE">XML_IO_ERANGE</a> = 1537 /* 1537 */
    <a name="XML_IO_EROFS">XML_IO_EROFS</a> = 1538 /* 1538 */
    <a name="XML_IO_ESPIPE">XML_IO_ESPIPE</a> = 1539 /* 1539 */
    <a name="XML_IO_ESRCH">XML_IO_ESRCH</a> = 1540 /* 1540 */
    <a name="XML_IO_ETIMEDOUT">XML_IO_ETIMEDOUT</a> = 1541 /* 1541 */
    <a name="XML_IO_EXDEV">XML_IO_EXDEV</a> = 1542 /* 1542 */
    <a name="XML_IO_NETWORK_ATTEMPT">XML_IO_NETWORK_ATTEMPT</a> = 1543 /* 1543 */
    <a name="XML_IO_ENCODER">XML_IO_ENCODER</a> = 1544 /* 1544 */
    <a name="XML_IO_FLUSH">XML_IO_FLUSH</a> = 1545 /* 1545 */
    <a name="XML_IO_WRITE">XML_IO_WRITE</a> = 1546 /* 1546 */
    <a name="XML_IO_NO_INPUT">XML_IO_NO_INPUT</a> = 1547 /* 1547 */
    <a name="XML_IO_BUFFER_FULL">XML_IO_BUFFER_FULL</a> = 1548 /* 1548 */
    <a name="XML_IO_LOAD_ERROR">XML_IO_LOAD_ERROR</a> = 1549 /* 1549 */
    <a name="XML_IO_ENOTSOCK">XML_IO_ENOTSOCK</a> = 1550 /* 1550 */
    <a name="XML_IO_EISCONN">XML_IO_EISCONN</a> = 1551 /* 1551 */
    <a name="XML_IO_ECONNREFUSED">XML_IO_ECONNREFUSED</a> = 1552 /* 1552 */
    <a name="XML_IO_ENETUNREACH">XML_IO_ENETUNREACH</a> = 1553 /* 1553 */
    <a name="XML_IO_EADDRINUSE">XML_IO_EADDRINUSE</a> = 1554 /* 1554 */
    <a name="XML_IO_EALREADY">XML_IO_EALREADY</a> = 1555 /* 1555 */
    <a name="XML_IO_EAFNOSUPPORT">XML_IO_EAFNOSUPPORT</a> = 1556 /* 1556 */
    <a name="XML_XINCLUDE_RECURSION">XML_XINCLUDE_RECURSION</a> = 1600
    <a name="XML_XINCLUDE_PARSE_VALUE">XML_XINCLUDE_PARSE_VALUE</a> = 1601 /* 1601 */
    <a name="XML_XINCLUDE_ENTITY_DEF_MISMATCH">XML_XINCLUDE_ENTITY_DEF_MISMATCH</a> = 1602 /* 1602 */
    <a name="XML_XINCLUDE_NO_HREF">XML_XINCLUDE_NO_HREF</a> = 1603 /* 1603 */
    <a name="XML_XINCLUDE_NO_FALLBACK">XML_XINCLUDE_NO_FALLBACK</a> = 1604 /* 1604 */
    <a name="XML_XINCLUDE_HREF_URI">XML_XINCLUDE_HREF_URI</a> = 1605 /* 1605 */
    <a name="XML_XINCLUDE_TEXT_FRAGMENT">XML_XINCLUDE_TEXT_FRAGMENT</a> = 1606 /* 1606 */
    <a name="XML_XINCLUDE_TEXT_DOCUMENT">XML_XINCLUDE_TEXT_DOCUMENT</a> = 1607 /* 1607 */
    <a name="XML_XINCLUDE_INVALID_CHAR">XML_XINCLUDE_INVALID_CHAR</a> = 1608 /* 1608 */
    <a name="XML_XINCLUDE_BUILD_FAILED">XML_XINCLUDE_BUILD_FAILED</a> = 1609 /* 1609 */
    <a name="XML_XINCLUDE_UNKNOWN_ENCODING">XML_XINCLUDE_UNKNOWN_ENCODING</a> = 1610 /* 1610 */
    <a name="XML_XINCLUDE_MULTIPLE_ROOT">XML_XINCLUDE_MULTIPLE_ROOT</a> = 1611 /* 1611 */
    <a name="XML_XINCLUDE_XPTR_FAILED">XML_XINCLUDE_XPTR_FAILED</a> = 1612 /* 1612 */
    <a name="XML_XINCLUDE_XPTR_RESULT">XML_XINCLUDE_XPTR_RESULT</a> = 1613 /* 1613 */
    <a name="XML_XINCLUDE_INCLUDE_IN_INCLUDE">XML_XINCLUDE_INCLUDE_IN_INCLUDE</a> = 1614 /* 1614 */
    <a name="XML_XINCLUDE_FALLBACKS_IN_INCLUDE">XML_XINCLUDE_FALLBACKS_IN_INCLUDE</a> = 1615 /* 1615 */
    <a name="XML_XINCLUDE_FALLBACK_NOT_IN_INCLUDE">XML_XINCLUDE_FALLBACK_NOT_IN_INCLUDE</a> = 1616 /* 1616 */
    <a name="XML_XINCLUDE_DEPRECATED_NS">XML_XINCLUDE_DEPRECATED_NS</a> = 1617 /* 1617 */
    <a name="XML_XINCLUDE_FRAGMENT_ID">XML_XINCLUDE_FRAGMENT_ID</a> = 1618 /* 1618 */
    <a name="XML_CATALOG_MISSING_ATTR">XML_CATALOG_MISSING_ATTR</a> = 1650
    <a name="XML_CATALOG_ENTRY_BROKEN">XML_CATALOG_ENTRY_BROKEN</a> = 1651 /* 1651 */
    <a name="XML_CATALOG_PREFER_VALUE">XML_CATALOG_PREFER_VALUE</a> = 1652 /* 1652 */
    <a name="XML_CATALOG_NOT_CATALOG">XML_CATALOG_NOT_CATALOG</a> = 1653 /* 1653 */
    <a name="XML_CATALOG_RECURSION">XML_CATALOG_RECURSION</a> = 1654 /* 1654 */
    <a name="XML_SCHEMAP_PREFIX_UNDEFINED">XML_SCHEMAP_PREFIX_UNDEFINED</a> = 1700
    <a name="XML_SCHEMAP_ATTRFORMDEFAULT_VALUE">XML_SCHEMAP_ATTRFORMDEFAULT_VALUE</a> = 1701 /* 1701 */
    <a name="XML_SCHEMAP_ATTRGRP_NONAME_NOREF">XML_SCHEMAP_ATTRGRP_NONAME_NOREF</a> = 1702 /* 1702 */
    <a name="XML_SCHEMAP_ATTR_NONAME_NOREF">XML_SCHEMAP_ATTR_NONAME_NOREF</a> = 1703 /* 1703 */
    <a name="XML_SCHEMAP_COMPLEXTYPE_NONAME_NOREF">XML_SCHEMAP_COMPLEXTYPE_NONAME_NOREF</a> = 1704 /* 1704 */
    <a name="XML_SCHEMAP_ELEMFORMDEFAULT_VALUE">XML_SCHEMAP_ELEMFORMDEFAULT_VALUE</a> = 1705 /* 1705 */
    <a name="XML_SCHEMAP_ELEM_NONAME_NOREF">XML_SCHEMAP_ELEM_NONAME_NOREF</a> = 1706 /* 1706 */
    <a name="XML_SCHEMAP_EXTENSION_NO_BASE">XML_SCHEMAP_EXTENSION_NO_BASE</a> = 1707 /* 1707 */
    <a name="XML_SCHEMAP_FACET_NO_VALUE">XML_SCHEMAP_FACET_NO_VALUE</a> = 1708 /* 1708 */
    <a name="XML_SCHEMAP_FAILED_BUILD_IMPORT">XML_SCHEMAP_FAILED_BUILD_IMPORT</a> = 1709 /* 1709 */
    <a name="XML_SCHEMAP_GROUP_NONAME_NOREF">XML_SCHEMAP_GROUP_NONAME_NOREF</a> = 1710 /* 1710 */
    <a name="XML_SCHEMAP_IMPORT_NAMESPACE_NOT_URI">XML_SCHEMAP_IMPORT_NAMESPACE_NOT_URI</a> = 1711 /* 1711 */
    <a name="XML_SCHEMAP_IMPORT_REDEFINE_NSNAME">XML_SCHEMAP_IMPORT_REDEFINE_NSNAME</a> = 1712 /* 1712 */
    <a name="XML_SCHEMAP_IMPORT_SCHEMA_NOT_URI">XML_SCHEMAP_IMPORT_SCHEMA_NOT_URI</a> = 1713 /* 1713 */
    <a name="XML_SCHEMAP_INVALID_BOOLEAN">XML_SCHEMAP_INVALID_BOOLEAN</a> = 1714 /* 1714 */
    <a name="XML_SCHEMAP_INVALID_ENUM">XML_SCHEMAP_INVALID_ENUM</a> = 1715 /* 1715 */
    <a name="XML_SCHEMAP_INVALID_FACET">XML_SCHEMAP_INVALID_FACET</a> = 1716 /* 1716 */
    <a name="XML_SCHEMAP_INVALID_FACET_VALUE">XML_SCHEMAP_INVALID_FACET_VALUE</a> = 1717 /* 1717 */
    <a name="XML_SCHEMAP_INVALID_MAXOCCURS">XML_SCHEMAP_INVALID_MAXOCCURS</a> = 1718 /* 1718 */
    <a name="XML_SCHEMAP_INVALID_MINOCCURS">XML_SCHEMAP_INVALID_MINOCCURS</a> = 1719 /* 1719 */
    <a name="XML_SCHEMAP_INVALID_REF_AND_SUBTYPE">XML_SCHEMAP_INVALID_REF_AND_SUBTYPE</a> = 1720 /* 1720 */
    <a name="XML_SCHEMAP_INVALID_WHITE_SPACE">XML_SCHEMAP_INVALID_WHITE_SPACE</a> = 1721 /* 1721 */
    <a name="XML_SCHEMAP_NOATTR_NOREF">XML_SCHEMAP_NOATTR_NOREF</a> = 1722 /* 1722 */
    <a name="XML_SCHEMAP_NOTATION_NO_NAME">XML_SCHEMAP_NOTATION_NO_NAME</a> = 1723 /* 1723 */
    <a name="XML_SCHEMAP_NOTYPE_NOREF">XML_SCHEMAP_NOTYPE_NOREF</a> = 1724 /* 1724 */
    <a name="XML_SCHEMAP_REF_AND_SUBTYPE">XML_SCHEMAP_REF_AND_SUBTYPE</a> = 1725 /* 1725 */
    <a name="XML_SCHEMAP_RESTRICTION_NONAME_NOREF">XML_SCHEMAP_RESTRICTION_NONAME_NOREF</a> = 1726 /* 1726 */
    <a name="XML_SCHEMAP_SIMPLETYPE_NONAME">XML_SCHEMAP_SIMPLETYPE_NONAME</a> = 1727 /* 1727 */
    <a name="XML_SCHEMAP_TYPE_AND_SUBTYPE">XML_SCHEMAP_TYPE_AND_SUBTYPE</a> = 1728 /* 1728 */
    <a name="XML_SCHEMAP_UNKNOWN_ALL_CHILD">XML_SCHEMAP_UNKNOWN_ALL_CHILD</a> = 1729 /* 1729 */
    <a name="XML_SCHEMAP_UNKNOWN_ANYATTRIBUTE_CHILD">XML_SCHEMAP_UNKNOWN_ANYATTRIBUTE_CHILD</a> = 1730 /* 1730 */
    <a name="XML_SCHEMAP_UNKNOWN_ATTR_CHILD">XML_SCHEMAP_UNKNOWN_ATTR_CHILD</a> = 1731 /* 1731 */
    <a name="XML_SCHEMAP_UNKNOWN_ATTRGRP_CHILD">XML_SCHEMAP_UNKNOWN_ATTRGRP_CHILD</a> = 1732 /* 1732 */
    <a name="XML_SCHEMAP_UNKNOWN_ATTRIBUTE_GROUP">XML_SCHEMAP_UNKNOWN_ATTRIBUTE_GROUP</a> = 1733 /* 1733 */
    <a name="XML_SCHEMAP_UNKNOWN_BASE_TYPE">XML_SCHEMAP_UNKNOWN_BASE_TYPE</a> = 1734 /* 1734 */
    <a name="XML_SCHEMAP_UNKNOWN_CHOICE_CHILD">XML_SCHEMAP_UNKNOWN_CHOICE_CHILD</a> = 1735 /* 1735 */
    <a name="XML_SCHEMAP_UNKNOWN_COMPLEXCONTENT_CHILD">XML_SCHEMAP_UNKNOWN_COMPLEXCONTENT_CHILD</a> = 1736 /* 1736 */
    <a name="XML_SCHEMAP_UNKNOWN_COMPLEXTYPE_CHILD">XML_SCHEMAP_UNKNOWN_COMPLEXTYPE_CHILD</a> = 1737 /* 1737 */
    <a name="XML_SCHEMAP_UNKNOWN_ELEM_CHILD">XML_SCHEMAP_UNKNOWN_ELEM_CHILD</a> = 1738 /* 1738 */
    <a name="XML_SCHEMAP_UNKNOWN_EXTENSION_CHILD">XML_SCHEMAP_UNKNOWN_EXTENSION_CHILD</a> = 1739 /* 1739 */
    <a name="XML_SCHEMAP_UNKNOWN_FACET_CHILD">XML_SCHEMAP_UNKNOWN_FACET_CHILD</a> = 1740 /* 1740 */
    <a name="XML_SCHEMAP_UNKNOWN_FACET_TYPE">XML_SCHEMAP_UNKNOWN_FACET_TYPE</a> = 1741 /* 1741 */
    <a name="XML_SCHEMAP_UNKNOWN_GROUP_CHILD">XML_SCHEMAP_UNKNOWN_GROUP_CHILD</a> = 1742 /* 1742 */
    <a name="XML_SCHEMAP_UNKNOWN_IMPORT_CHILD">XML_SCHEMAP_UNKNOWN_IMPORT_CHILD</a> = 1743 /* 1743 */
    <a name="XML_SCHEMAP_UNKNOWN_LIST_CHILD">XML_SCHEMAP_UNKNOWN_LIST_CHILD</a> = 1744 /* 1744 */
    <a name="XML_SCHEMAP_UNKNOWN_NOTATION_CHILD">XML_SCHEMAP_UNKNOWN_NOTATION_CHILD</a> = 1745 /* 1745 */
    <a name="XML_SCHEMAP_UNKNOWN_PROCESSCONTENT_CHILD">XML_SCHEMAP_UNKNOWN_PROCESSCONTENT_CHILD</a> = 1746 /* 1746 */
    <a name="XML_SCHEMAP_UNKNOWN_REF">XML_SCHEMAP_UNKNOWN_REF</a> = 1747 /* 1747 */
    <a name="XML_SCHEMAP_UNKNOWN_RESTRICTION_CHILD">XML_SCHEMAP_UNKNOWN_RESTRICTION_CHILD</a> = 1748 /* 1748 */
    <a name="XML_SCHEMAP_UNKNOWN_SCHEMAS_CHILD">XML_SCHEMAP_UNKNOWN_SCHEMAS_CHILD</a> = 1749 /* 1749 */
    <a name="XML_SCHEMAP_UNKNOWN_SEQUENCE_CHILD">XML_SCHEMAP_UNKNOWN_SEQUENCE_CHILD</a> = 1750 /* 1750 */
    <a name="XML_SCHEMAP_UNKNOWN_SIMPLECONTENT_CHILD">XML_SCHEMAP_UNKNOWN_SIMPLECONTENT_CHILD</a> = 1751 /* 1751 */
    <a name="XML_SCHEMAP_UNKNOWN_SIMPLETYPE_CHILD">XML_SCHEMAP_UNKNOWN_SIMPLETYPE_CHILD</a> = 1752 /* 1752 */
    <a name="XML_SCHEMAP_UNKNOWN_TYPE">XML_SCHEMAP_UNKNOWN_TYPE</a> = 1753 /* 1753 */
    <a name="XML_SCHEMAP_UNKNOWN_UNION_CHILD">XML_SCHEMAP_UNKNOWN_UNION_CHILD</a> = 1754 /* 1754 */
    <a name="XML_SCHEMAP_ELEM_DEFAULT_FIXED">XML_SCHEMAP_ELEM_DEFAULT_FIXED</a> = 1755 /* 1755 */
    <a name="XML_SCHEMAP_REGEXP_INVALID">XML_SCHEMAP_REGEXP_INVALID</a> = 1756 /* 1756 */
    <a name="XML_SCHEMAP_FAILED_LOAD">XML_SCHEMAP_FAILED_LOAD</a> = 1757 /* 1757 */
    <a name="XML_SCHEMAP_NOTHING_TO_PARSE">XML_SCHEMAP_NOTHING_TO_PARSE</a> = 1758 /* 1758 */
    <a name="XML_SCHEMAP_NOROOT">XML_SCHEMAP_NOROOT</a> = 1759 /* 1759 */
    <a name="XML_SCHEMAP_REDEFINED_GROUP">XML_SCHEMAP_REDEFINED_GROUP</a> = 1760 /* 1760 */
    <a name="XML_SCHEMAP_REDEFINED_TYPE">XML_SCHEMAP_REDEFINED_TYPE</a> = 1761 /* 1761 */
    <a name="XML_SCHEMAP_REDEFINED_ELEMENT">XML_SCHEMAP_REDEFINED_ELEMENT</a> = 1762 /* 1762 */
    <a name="XML_SCHEMAP_REDEFINED_ATTRGROUP">XML_SCHEMAP_REDEFINED_ATTRGROUP</a> = 1763 /* 1763 */
    <a name="XML_SCHEMAP_REDEFINED_ATTR">XML_SCHEMAP_REDEFINED_ATTR</a> = 1764 /* 1764 */
    <a name="XML_SCHEMAP_REDEFINED_NOTATION">XML_SCHEMAP_REDEFINED_NOTATION</a> = 1765 /* 1765 */
    <a name="XML_SCHEMAP_FAILED_PARSE">XML_SCHEMAP_FAILED_PARSE</a> = 1766 /* 1766 */
    <a name="XML_SCHEMAP_UNKNOWN_PREFIX">XML_SCHEMAP_UNKNOWN_PREFIX</a> = 1767 /* 1767 */
    <a name="XML_SCHEMAP_DEF_AND_PREFIX">XML_SCHEMAP_DEF_AND_PREFIX</a> = 1768 /* 1768 */
    <a name="XML_SCHEMAP_UNKNOWN_INCLUDE_CHILD">XML_SCHEMAP_UNKNOWN_INCLUDE_CHILD</a> = 1769 /* 1769 */
    <a name="XML_SCHEMAP_INCLUDE_SCHEMA_NOT_URI">XML_SCHEMAP_INCLUDE_SCHEMA_NOT_URI</a> = 1770 /* 1770 */
    <a name="XML_SCHEMAP_INCLUDE_SCHEMA_NO_URI">XML_SCHEMAP_INCLUDE_SCHEMA_NO_URI</a> = 1771 /* 1771 */
    <a name="XML_SCHEMAP_NOT_SCHEMA">XML_SCHEMAP_NOT_SCHEMA</a> = 1772 /* 1772 */
    <a name="XML_SCHEMAP_UNKNOWN_MEMBER_TYPE">XML_SCHEMAP_UNKNOWN_MEMBER_TYPE</a> = 1773 /* 1773 */
    <a name="XML_SCHEMAP_INVALID_ATTR_USE">XML_SCHEMAP_INVALID_ATTR_USE</a> = 1774 /* 1774 */
    <a name="XML_SCHEMAP_RECURSIVE">XML_SCHEMAP_RECURSIVE</a> = 1775 /* 1775 */
    <a name="XML_SCHEMAP_SUPERNUMEROUS_LIST_ITEM_TYPE">XML_SCHEMAP_SUPERNUMEROUS_LIST_ITEM_TYPE</a> = 1776 /* 1776 */
    <a name="XML_SCHEMAP_INVALID_ATTR_COMBINATION">XML_SCHEMAP_INVALID_ATTR_COMBINATION</a> = 1777 /* 1777 */
    <a name="XML_SCHEMAP_INVALID_ATTR_INLINE_COMBINATION">XML_SCHEMAP_INVALID_ATTR_INLINE_COMBINATION</a> = 1778 /* 1778 */
    <a name="XML_SCHEMAP_MISSING_SIMPLETYPE_CHILD">XML_SCHEMAP_MISSING_SIMPLETYPE_CHILD</a> = 1779 /* 1779 */
    <a name="XML_SCHEMAP_INVALID_ATTR_NAME">XML_SCHEMAP_INVALID_ATTR_NAME</a> = 1780 /* 1780 */
    <a name="XML_SCHEMAP_REF_AND_CONTENT">XML_SCHEMAP_REF_AND_CONTENT</a> = 1781 /* 1781 */
    <a name="XML_SCHEMAP_CT_PROPS_CORRECT_1">XML_SCHEMAP_CT_PROPS_CORRECT_1</a> = 1782 /* 1782 */
    <a name="XML_SCHEMAP_CT_PROPS_CORRECT_2">XML_SCHEMAP_CT_PROPS_CORRECT_2</a> = 1783 /* 1783 */
    <a name="XML_SCHEMAP_CT_PROPS_CORRECT_3">XML_SCHEMAP_CT_PROPS_CORRECT_3</a> = 1784 /* 1784 */
    <a name="XML_SCHEMAP_CT_PROPS_CORRECT_4">XML_SCHEMAP_CT_PROPS_CORRECT_4</a> = 1785 /* 1785 */
    <a name="XML_SCHEMAP_CT_PROPS_CORRECT_5">XML_SCHEMAP_CT_PROPS_CORRECT_5</a> = 1786 /* 1786 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_1">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_1</a> = 1787 /* 1787 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_1">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_1</a> = 1788 /* 1788 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_2">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_2</a> = 1789 /* 1789 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_2">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_2</a> = 1790 /* 1790 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_3">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_3</a> = 1791 /* 1791 */
    <a name="XML_SCHEMAP_WILDCARD_INVALID_NS_MEMBER">XML_SCHEMAP_WILDCARD_INVALID_NS_MEMBER</a> = 1792 /* 1792 */
    <a name="XML_SCHEMAP_INTERSECTION_NOT_EXPRESSIBLE">XML_SCHEMAP_INTERSECTION_NOT_EXPRESSIBLE</a> = 1793 /* 1793 */
    <a name="XML_SCHEMAP_UNION_NOT_EXPRESSIBLE">XML_SCHEMAP_UNION_NOT_EXPRESSIBLE</a> = 1794 /* 1794 */
    <a name="XML_SCHEMAP_SRC_IMPORT_3_1">XML_SCHEMAP_SRC_IMPORT_3_1</a> = 1795 /* 1795 */
    <a name="XML_SCHEMAP_SRC_IMPORT_3_2">XML_SCHEMAP_SRC_IMPORT_3_2</a> = 1796 /* 1796 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_1">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_1</a> = 1797 /* 1797 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_2">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_2</a> = 1798 /* 1798 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_3">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_3</a> = 1799 /* 1799 */
    <a name="XML_SCHEMAP_COS_CT_EXTENDS_1_3">XML_SCHEMAP_COS_CT_EXTENDS_1_3</a> = 1800 /* 1800 */
    <a name="XML_SCHEMAV_NOROOT">XML_SCHEMAV_NOROOT</a> = 1801
    <a name="XML_SCHEMAV_UNDECLAREDELEM">XML_SCHEMAV_UNDECLAREDELEM</a> = 1802 /* 1802 */
    <a name="XML_SCHEMAV_NOTTOPLEVEL">XML_SCHEMAV_NOTTOPLEVEL</a> = 1803 /* 1803 */
    <a name="XML_SCHEMAV_MISSING">XML_SCHEMAV_MISSING</a> = 1804 /* 1804 */
    <a name="XML_SCHEMAV_WRONGELEM">XML_SCHEMAV_WRONGELEM</a> = 1805 /* 1805 */
    <a name="XML_SCHEMAV_NOTYPE">XML_SCHEMAV_NOTYPE</a> = 1806 /* 1806 */
    <a name="XML_SCHEMAV_NOROLLBACK">XML_SCHEMAV_NOROLLBACK</a> = 1807 /* 1807 */
    <a name="XML_SCHEMAV_ISABSTRACT">XML_SCHEMAV_ISABSTRACT</a> = 1808 /* 1808 */
    <a name="XML_SCHEMAV_NOTEMPTY">XML_SCHEMAV_NOTEMPTY</a> = 1809 /* 1809 */
    <a name="XML_SCHEMAV_ELEMCONT">XML_SCHEMAV_ELEMCONT</a> = 1810 /* 1810 */
    <a name="XML_SCHEMAV_HAVEDEFAULT">XML_SCHEMAV_HAVEDEFAULT</a> = 1811 /* 1811 */
    <a name="XML_SCHEMAV_NOTNILLABLE">XML_SCHEMAV_NOTNILLABLE</a> = 1812 /* 1812 */
    <a name="XML_SCHEMAV_EXTRACONTENT">XML_SCHEMAV_EXTRACONTENT</a> = 1813 /* 1813 */
    <a name="XML_SCHEMAV_INVALIDATTR">XML_SCHEMAV_INVALIDATTR</a> = 1814 /* 1814 */
    <a name="XML_SCHEMAV_INVALIDELEM">XML_SCHEMAV_INVALIDELEM</a> = 1815 /* 1815 */
    <a name="XML_SCHEMAV_NOTDETERMINIST">XML_SCHEMAV_NOTDETERMINIST</a> = 1816 /* 1816 */
    <a name="XML_SCHEMAV_CONSTRUCT">XML_SCHEMAV_CONSTRUCT</a> = 1817 /* 1817 */
    <a name="XML_SCHEMAV_INTERNAL">XML_SCHEMAV_INTERNAL</a> = 1818 /* 1818 */
    <a name="XML_SCHEMAV_NOTSIMPLE">XML_SCHEMAV_NOTSIMPLE</a> = 1819 /* 1819 */
    <a name="XML_SCHEMAV_ATTRUNKNOWN">XML_SCHEMAV_ATTRUNKNOWN</a> = 1820 /* 1820 */
    <a name="XML_SCHEMAV_ATTRINVALID">XML_SCHEMAV_ATTRINVALID</a> = 1821 /* 1821 */
    <a name="XML_SCHEMAV_VALUE">XML_SCHEMAV_VALUE</a> = 1822 /* 1822 */
    <a name="XML_SCHEMAV_FACET">XML_SCHEMAV_FACET</a> = 1823 /* 1823 */
    <a name="XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_1">XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_1</a> = 1824 /* 1824 */
    <a name="XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_2">XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_2</a> = 1825 /* 1825 */
    <a name="XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_3">XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_3</a> = 1826 /* 1826 */
    <a name="XML_SCHEMAV_CVC_TYPE_3_1_1">XML_SCHEMAV_CVC_TYPE_3_1_1</a> = 1827 /* 1827 */
    <a name="XML_SCHEMAV_CVC_TYPE_3_1_2">XML_SCHEMAV_CVC_TYPE_3_1_2</a> = 1828 /* 1828 */
    <a name="XML_SCHEMAV_CVC_FACET_VALID">XML_SCHEMAV_CVC_FACET_VALID</a> = 1829 /* 1829 */
    <a name="XML_SCHEMAV_CVC_LENGTH_VALID">XML_SCHEMAV_CVC_LENGTH_VALID</a> = 1830 /* 1830 */
    <a name="XML_SCHEMAV_CVC_MINLENGTH_VALID">XML_SCHEMAV_CVC_MINLENGTH_VALID</a> = 1831 /* 1831 */
    <a name="XML_SCHEMAV_CVC_MAXLENGTH_VALID">XML_SCHEMAV_CVC_MAXLENGTH_VALID</a> = 1832 /* 1832 */
    <a name="XML_SCHEMAV_CVC_MININCLUSIVE_VALID">XML_SCHEMAV_CVC_MININCLUSIVE_VALID</a> = 1833 /* 1833 */
    <a name="XML_SCHEMAV_CVC_MAXINCLUSIVE_VALID">XML_SCHEMAV_CVC_MAXINCLUSIVE_VALID</a> = 1834 /* 1834 */
    <a name="XML_SCHEMAV_CVC_MINEXCLUSIVE_VALID">XML_SCHEMAV_CVC_MINEXCLUSIVE_VALID</a> = 1835 /* 1835 */
    <a name="XML_SCHEMAV_CVC_MAXEXCLUSIVE_VALID">XML_SCHEMAV_CVC_MAXEXCLUSIVE_VALID</a> = 1836 /* 1836 */
    <a name="XML_SCHEMAV_CVC_TOTALDIGITS_VALID">XML_SCHEMAV_CVC_TOTALDIGITS_VALID</a> = 1837 /* 1837 */
    <a name="XML_SCHEMAV_CVC_FRACTIONDIGITS_VALID">XML_SCHEMAV_CVC_FRACTIONDIGITS_VALID</a> = 1838 /* 1838 */
    <a name="XML_SCHEMAV_CVC_PATTERN_VALID">XML_SCHEMAV_CVC_PATTERN_VALID</a> = 1839 /* 1839 */
    <a name="XML_SCHEMAV_CVC_ENUMERATION_VALID">XML_SCHEMAV_CVC_ENUMERATION_VALID</a> = 1840 /* 1840 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_2_1">XML_SCHEMAV_CVC_COMPLEX_TYPE_2_1</a> = 1841 /* 1841 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_2_2">XML_SCHEMAV_CVC_COMPLEX_TYPE_2_2</a> = 1842 /* 1842 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_2_3">XML_SCHEMAV_CVC_COMPLEX_TYPE_2_3</a> = 1843 /* 1843 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_2_4">XML_SCHEMAV_CVC_COMPLEX_TYPE_2_4</a> = 1844 /* 1844 */
    <a name="XML_SCHEMAV_CVC_ELT_1">XML_SCHEMAV_CVC_ELT_1</a> = 1845 /* 1845 */
    <a name="XML_SCHEMAV_CVC_ELT_2">XML_SCHEMAV_CVC_ELT_2</a> = 1846 /* 1846 */
    <a name="XML_SCHEMAV_CVC_ELT_3_1">XML_SCHEMAV_CVC_ELT_3_1</a> = 1847 /* 1847 */
    <a name="XML_SCHEMAV_CVC_ELT_3_2_1">XML_SCHEMAV_CVC_ELT_3_2_1</a> = 1848 /* 1848 */
    <a name="XML_SCHEMAV_CVC_ELT_3_2_2">XML_SCHEMAV_CVC_ELT_3_2_2</a> = 1849 /* 1849 */
    <a name="XML_SCHEMAV_CVC_ELT_4_1">XML_SCHEMAV_CVC_ELT_4_1</a> = 1850 /* 1850 */
    <a name="XML_SCHEMAV_CVC_ELT_4_2">XML_SCHEMAV_CVC_ELT_4_2</a> = 1851 /* 1851 */
    <a name="XML_SCHEMAV_CVC_ELT_4_3">XML_SCHEMAV_CVC_ELT_4_3</a> = 1852 /* 1852 */
    <a name="XML_SCHEMAV_CVC_ELT_5_1_1">XML_SCHEMAV_CVC_ELT_5_1_1</a> = 1853 /* 1853 */
    <a name="XML_SCHEMAV_CVC_ELT_5_1_2">XML_SCHEMAV_CVC_ELT_5_1_2</a> = 1854 /* 1854 */
    <a name="XML_SCHEMAV_CVC_ELT_5_2_1">XML_SCHEMAV_CVC_ELT_5_2_1</a> = 1855 /* 1855 */
    <a name="XML_SCHEMAV_CVC_ELT_5_2_2_1">XML_SCHEMAV_CVC_ELT_5_2_2_1</a> = 1856 /* 1856 */
    <a name="XML_SCHEMAV_CVC_ELT_5_2_2_2_1">XML_SCHEMAV_CVC_ELT_5_2_2_2_1</a> = 1857 /* 1857 */
    <a name="XML_SCHEMAV_CVC_ELT_5_2_2_2_2">XML_SCHEMAV_CVC_ELT_5_2_2_2_2</a> = 1858 /* 1858 */
    <a name="XML_SCHEMAV_CVC_ELT_6">XML_SCHEMAV_CVC_ELT_6</a> = 1859 /* 1859 */
    <a name="XML_SCHEMAV_CVC_ELT_7">XML_SCHEMAV_CVC_ELT_7</a> = 1860 /* 1860 */
    <a name="XML_SCHEMAV_CVC_ATTRIBUTE_1">XML_SCHEMAV_CVC_ATTRIBUTE_1</a> = 1861 /* 1861 */
    <a name="XML_SCHEMAV_CVC_ATTRIBUTE_2">XML_SCHEMAV_CVC_ATTRIBUTE_2</a> = 1862 /* 1862 */
    <a name="XML_SCHEMAV_CVC_ATTRIBUTE_3">XML_SCHEMAV_CVC_ATTRIBUTE_3</a> = 1863 /* 1863 */
    <a name="XML_SCHEMAV_CVC_ATTRIBUTE_4">XML_SCHEMAV_CVC_ATTRIBUTE_4</a> = 1864 /* 1864 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_3_1">XML_SCHEMAV_CVC_COMPLEX_TYPE_3_1</a> = 1865 /* 1865 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_3_2_1">XML_SCHEMAV_CVC_COMPLEX_TYPE_3_2_1</a> = 1866 /* 1866 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_3_2_2">XML_SCHEMAV_CVC_COMPLEX_TYPE_3_2_2</a> = 1867 /* 1867 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_4">XML_SCHEMAV_CVC_COMPLEX_TYPE_4</a> = 1868 /* 1868 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_5_1">XML_SCHEMAV_CVC_COMPLEX_TYPE_5_1</a> = 1869 /* 1869 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_5_2">XML_SCHEMAV_CVC_COMPLEX_TYPE_5_2</a> = 1870 /* 1870 */
    <a name="XML_SCHEMAV_ELEMENT_CONTENT">XML_SCHEMAV_ELEMENT_CONTENT</a> = 1871 /* 1871 */
    <a name="XML_SCHEMAV_DOCUMENT_ELEMENT_MISSING">XML_SCHEMAV_DOCUMENT_ELEMENT_MISSING</a> = 1872 /* 1872 */
    <a name="XML_SCHEMAV_CVC_COMPLEX_TYPE_1">XML_SCHEMAV_CVC_COMPLEX_TYPE_1</a> = 1873 /* 1873 */
    <a name="XML_SCHEMAV_CVC_AU">XML_SCHEMAV_CVC_AU</a> = 1874 /* 1874 */
    <a name="XML_SCHEMAV_CVC_TYPE_1">XML_SCHEMAV_CVC_TYPE_1</a> = 1875 /* 1875 */
    <a name="XML_SCHEMAV_CVC_TYPE_2">XML_SCHEMAV_CVC_TYPE_2</a> = 1876 /* 1876 */
    <a name="XML_SCHEMAV_CVC_IDC">XML_SCHEMAV_CVC_IDC</a> = 1877 /* 1877 */
    <a name="XML_SCHEMAV_CVC_WILDCARD">XML_SCHEMAV_CVC_WILDCARD</a> = 1878 /* 1878 */
    <a name="XML_SCHEMAV_MISC">XML_SCHEMAV_MISC</a> = 1879 /* 1879 */
    <a name="XML_XPTR_UNKNOWN_SCHEME">XML_XPTR_UNKNOWN_SCHEME</a> = 1900
    <a name="XML_XPTR_CHILDSEQ_START">XML_XPTR_CHILDSEQ_START</a> = 1901 /* 1901 */
    <a name="XML_XPTR_EVAL_FAILED">XML_XPTR_EVAL_FAILED</a> = 1902 /* 1902 */
    <a name="XML_XPTR_EXTRA_OBJECTS">XML_XPTR_EXTRA_OBJECTS</a> = 1903 /* 1903 */
    <a name="XML_C14N_CREATE_CTXT">XML_C14N_CREATE_CTXT</a> = 1950
    <a name="XML_C14N_REQUIRES_UTF8">XML_C14N_REQUIRES_UTF8</a> = 1951 /* 1951 */
    <a name="XML_C14N_CREATE_STACK">XML_C14N_CREATE_STACK</a> = 1952 /* 1952 */
    <a name="XML_C14N_INVALID_NODE">XML_C14N_INVALID_NODE</a> = 1953 /* 1953 */
    <a name="XML_C14N_UNKNOW_NODE">XML_C14N_UNKNOW_NODE</a> = 1954 /* 1954 */
    <a name="XML_C14N_RELATIVE_NAMESPACE">XML_C14N_RELATIVE_NAMESPACE</a> = 1955 /* 1955 */
    <a name="XML_FTP_PASV_ANSWER">XML_FTP_PASV_ANSWER</a> = 2000
    <a name="XML_FTP_EPSV_ANSWER">XML_FTP_EPSV_ANSWER</a> = 2001 /* 2001 */
    <a name="XML_FTP_ACCNT">XML_FTP_ACCNT</a> = 2002 /* 2002 */
    <a name="XML_FTP_URL_SYNTAX">XML_FTP_URL_SYNTAX</a> = 2003 /* 2003 */
    <a name="XML_HTTP_URL_SYNTAX">XML_HTTP_URL_SYNTAX</a> = 2020
    <a name="XML_HTTP_USE_IP">XML_HTTP_USE_IP</a> = 2021 /* 2021 */
    <a name="XML_HTTP_UNKNOWN_HOST">XML_HTTP_UNKNOWN_HOST</a> = 2022 /* 2022 */
    <a name="XML_SCHEMAP_SRC_SIMPLE_TYPE_1">XML_SCHEMAP_SRC_SIMPLE_TYPE_1</a> = 3000
    <a name="XML_SCHEMAP_SRC_SIMPLE_TYPE_2">XML_SCHEMAP_SRC_SIMPLE_TYPE_2</a> = 3001 /* 3001 */
    <a name="XML_SCHEMAP_SRC_SIMPLE_TYPE_3">XML_SCHEMAP_SRC_SIMPLE_TYPE_3</a> = 3002 /* 3002 */
    <a name="XML_SCHEMAP_SRC_SIMPLE_TYPE_4">XML_SCHEMAP_SRC_SIMPLE_TYPE_4</a> = 3003 /* 3003 */
    <a name="XML_SCHEMAP_SRC_RESOLVE">XML_SCHEMAP_SRC_RESOLVE</a> = 3004 /* 3004 */
    <a name="XML_SCHEMAP_SRC_RESTRICTION_BASE_OR_SIMPLETYPE">XML_SCHEMAP_SRC_RESTRICTION_BASE_OR_SIMPLETYPE</a> = 3005 /* 3005 */
    <a name="XML_SCHEMAP_SRC_LIST_ITEMTYPE_OR_SIMPLETYPE">XML_SCHEMAP_SRC_LIST_ITEMTYPE_OR_SIMPLETYPE</a> = 3006 /* 3006 */
    <a name="XML_SCHEMAP_SRC_UNION_MEMBERTYPES_OR_SIMPLETYPES">XML_SCHEMAP_SRC_UNION_MEMBERTYPES_OR_SIMPLETYPES</a> = 3007 /* 3007 */
    <a name="XML_SCHEMAP_ST_PROPS_CORRECT_1">XML_SCHEMAP_ST_PROPS_CORRECT_1</a> = 3008 /* 3008 */
    <a name="XML_SCHEMAP_ST_PROPS_CORRECT_2">XML_SCHEMAP_ST_PROPS_CORRECT_2</a> = 3009 /* 3009 */
    <a name="XML_SCHEMAP_ST_PROPS_CORRECT_3">XML_SCHEMAP_ST_PROPS_CORRECT_3</a> = 3010 /* 3010 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_1_1">XML_SCHEMAP_COS_ST_RESTRICTS_1_1</a> = 3011 /* 3011 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_1_2">XML_SCHEMAP_COS_ST_RESTRICTS_1_2</a> = 3012 /* 3012 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_1_3_1">XML_SCHEMAP_COS_ST_RESTRICTS_1_3_1</a> = 3013 /* 3013 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_1_3_2">XML_SCHEMAP_COS_ST_RESTRICTS_1_3_2</a> = 3014 /* 3014 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_2_1">XML_SCHEMAP_COS_ST_RESTRICTS_2_1</a> = 3015 /* 3015 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_1_1">XML_SCHEMAP_COS_ST_RESTRICTS_2_3_1_1</a> = 3016 /* 3016 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_1_2">XML_SCHEMAP_COS_ST_RESTRICTS_2_3_1_2</a> = 3017 /* 3017 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_1">XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_1</a> = 3018 /* 3018 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_2">XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_2</a> = 3019 /* 3019 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_3">XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_3</a> = 3020 /* 3020 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_4">XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_4</a> = 3021 /* 3021 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_5">XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_5</a> = 3022 /* 3022 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_3_1">XML_SCHEMAP_COS_ST_RESTRICTS_3_1</a> = 3023 /* 3023 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_1">XML_SCHEMAP_COS_ST_RESTRICTS_3_3_1</a> = 3024 /* 3024 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_1_2">XML_SCHEMAP_COS_ST_RESTRICTS_3_3_1_2</a> = 3025 /* 3025 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_2">XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_2</a> = 3026 /* 3026 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_1">XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_1</a> = 3027 /* 3027 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_3">XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_3</a> = 3028 /* 3028 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_4">XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_4</a> = 3029 /* 3029 */
    <a name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_5">XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_5</a> = 3030 /* 3030 */
    <a name="XML_SCHEMAP_COS_ST_DERIVED_OK_2_1">XML_SCHEMAP_COS_ST_DERIVED_OK_2_1</a> = 3031 /* 3031 */
    <a name="XML_SCHEMAP_COS_ST_DERIVED_OK_2_2">XML_SCHEMAP_COS_ST_DERIVED_OK_2_2</a> = 3032 /* 3032 */
    <a name="XML_SCHEMAP_S4S_ELEM_NOT_ALLOWED">XML_SCHEMAP_S4S_ELEM_NOT_ALLOWED</a> = 3033 /* 3033 */
    <a name="XML_SCHEMAP_S4S_ELEM_MISSING">XML_SCHEMAP_S4S_ELEM_MISSING</a> = 3034 /* 3034 */
    <a name="XML_SCHEMAP_S4S_ATTR_NOT_ALLOWED">XML_SCHEMAP_S4S_ATTR_NOT_ALLOWED</a> = 3035 /* 3035 */
    <a name="XML_SCHEMAP_S4S_ATTR_MISSING">XML_SCHEMAP_S4S_ATTR_MISSING</a> = 3036 /* 3036 */
    <a name="XML_SCHEMAP_S4S_ATTR_INVALID_VALUE">XML_SCHEMAP_S4S_ATTR_INVALID_VALUE</a> = 3037 /* 3037 */
    <a name="XML_SCHEMAP_SRC_ELEMENT_1">XML_SCHEMAP_SRC_ELEMENT_1</a> = 3038 /* 3038 */
    <a name="XML_SCHEMAP_SRC_ELEMENT_2_1">XML_SCHEMAP_SRC_ELEMENT_2_1</a> = 3039 /* 3039 */
    <a name="XML_SCHEMAP_SRC_ELEMENT_2_2">XML_SCHEMAP_SRC_ELEMENT_2_2</a> = 3040 /* 3040 */
    <a name="XML_SCHEMAP_SRC_ELEMENT_3">XML_SCHEMAP_SRC_ELEMENT_3</a> = 3041 /* 3041 */
    <a name="XML_SCHEMAP_P_PROPS_CORRECT_1">XML_SCHEMAP_P_PROPS_CORRECT_1</a> = 3042 /* 3042 */
    <a name="XML_SCHEMAP_P_PROPS_CORRECT_2_1">XML_SCHEMAP_P_PROPS_CORRECT_2_1</a> = 3043 /* 3043 */
    <a name="XML_SCHEMAP_P_PROPS_CORRECT_2_2">XML_SCHEMAP_P_PROPS_CORRECT_2_2</a> = 3044 /* 3044 */
    <a name="XML_SCHEMAP_E_PROPS_CORRECT_2">XML_SCHEMAP_E_PROPS_CORRECT_2</a> = 3045 /* 3045 */
    <a name="XML_SCHEMAP_E_PROPS_CORRECT_3">XML_SCHEMAP_E_PROPS_CORRECT_3</a> = 3046 /* 3046 */
    <a name="XML_SCHEMAP_E_PROPS_CORRECT_4">XML_SCHEMAP_E_PROPS_CORRECT_4</a> = 3047 /* 3047 */
    <a name="XML_SCHEMAP_E_PROPS_CORRECT_5">XML_SCHEMAP_E_PROPS_CORRECT_5</a> = 3048 /* 3048 */
    <a name="XML_SCHEMAP_E_PROPS_CORRECT_6">XML_SCHEMAP_E_PROPS_CORRECT_6</a> = 3049 /* 3049 */
    <a name="XML_SCHEMAP_SRC_INCLUDE">XML_SCHEMAP_SRC_INCLUDE</a> = 3050 /* 3050 */
    <a name="XML_SCHEMAP_SRC_ATTRIBUTE_1">XML_SCHEMAP_SRC_ATTRIBUTE_1</a> = 3051 /* 3051 */
    <a name="XML_SCHEMAP_SRC_ATTRIBUTE_2">XML_SCHEMAP_SRC_ATTRIBUTE_2</a> = 3052 /* 3052 */
    <a name="XML_SCHEMAP_SRC_ATTRIBUTE_3_1">XML_SCHEMAP_SRC_ATTRIBUTE_3_1</a> = 3053 /* 3053 */
    <a name="XML_SCHEMAP_SRC_ATTRIBUTE_3_2">XML_SCHEMAP_SRC_ATTRIBUTE_3_2</a> = 3054 /* 3054 */
    <a name="XML_SCHEMAP_SRC_ATTRIBUTE_4">XML_SCHEMAP_SRC_ATTRIBUTE_4</a> = 3055 /* 3055 */
    <a name="XML_SCHEMAP_NO_XMLNS">XML_SCHEMAP_NO_XMLNS</a> = 3056 /* 3056 */
    <a name="XML_SCHEMAP_NO_XSI">XML_SCHEMAP_NO_XSI</a> = 3057 /* 3057 */
    <a name="XML_SCHEMAP_COS_VALID_DEFAULT_1">XML_SCHEMAP_COS_VALID_DEFAULT_1</a> = 3058 /* 3058 */
    <a name="XML_SCHEMAP_COS_VALID_DEFAULT_2_1">XML_SCHEMAP_COS_VALID_DEFAULT_2_1</a> = 3059 /* 3059 */
    <a name="XML_SCHEMAP_COS_VALID_DEFAULT_2_2_1">XML_SCHEMAP_COS_VALID_DEFAULT_2_2_1</a> = 3060 /* 3060 */
    <a name="XML_SCHEMAP_COS_VALID_DEFAULT_2_2_2">XML_SCHEMAP_COS_VALID_DEFAULT_2_2_2</a> = 3061 /* 3061 */
    <a name="XML_SCHEMAP_CVC_SIMPLE_TYPE">XML_SCHEMAP_CVC_SIMPLE_TYPE</a> = 3062 /* 3062 */
    <a name="XML_SCHEMAP_COS_CT_EXTENDS_1_1">XML_SCHEMAP_COS_CT_EXTENDS_1_1</a> = 3063 /* 3063 */
    <a name="XML_SCHEMAP_SRC_IMPORT_1_1">XML_SCHEMAP_SRC_IMPORT_1_1</a> = 3064 /* 3064 */
    <a name="XML_SCHEMAP_SRC_IMPORT_1_2">XML_SCHEMAP_SRC_IMPORT_1_2</a> = 3065 /* 3065 */
    <a name="XML_SCHEMAP_SRC_IMPORT_2">XML_SCHEMAP_SRC_IMPORT_2</a> = 3066 /* 3066 */
    <a name="XML_SCHEMAP_SRC_IMPORT_2_1">XML_SCHEMAP_SRC_IMPORT_2_1</a> = 3067 /* 3067 */
    <a name="XML_SCHEMAP_SRC_IMPORT_2_2">XML_SCHEMAP_SRC_IMPORT_2_2</a> = 3068 /* 3068 */
    <a name="XML_SCHEMAP_INTERNAL">XML_SCHEMAP_INTERNAL</a> = 3069 /* 3069 non-W3C */
    <a name="XML_SCHEMAP_NOT_DETERMINISTIC">XML_SCHEMAP_NOT_DETERMINISTIC</a> = 3070 /* 3070 non-W3C */
    <a name="XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_1">XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_1</a> = 3071 /* 3071 */
    <a name="XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_2">XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_2</a> = 3072 /* 3072 */
    <a name="XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_3">XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_3</a> = 3073 /* 3073 */
    <a name="XML_SCHEMAP_MG_PROPS_CORRECT_1">XML_SCHEMAP_MG_PROPS_CORRECT_1</a> = 3074 /* 3074 */
    <a name="XML_SCHEMAP_MG_PROPS_CORRECT_2">XML_SCHEMAP_MG_PROPS_CORRECT_2</a> = 3075 /* 3075 */
    <a name="XML_SCHEMAP_SRC_CT_1">XML_SCHEMAP_SRC_CT_1</a> = 3076 /* 3076 */
    <a name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_3">XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_3</a> = 3077 /* 3077 */
    <a name="XML_SCHEMAP_AU_PROPS_CORRECT_2">XML_SCHEMAP_AU_PROPS_CORRECT_2</a> = 3078 /* 3078 */
    <a name="XML_SCHEMAP_A_PROPS_CORRECT_2">XML_SCHEMAP_A_PROPS_CORRECT_2</a> = 3079 /* 3079 */
    <a name="XML_SCHEMAP_C_PROPS_CORRECT">XML_SCHEMAP_C_PROPS_CORRECT</a> = 3080 /* 3080 */
    <a name="XML_SCHEMAP_SRC_REDEFINE">XML_SCHEMAP_SRC_REDEFINE</a> = 3081 /* 3081 */
    <a name="XML_SCHEMAP_SRC_IMPORT">XML_SCHEMAP_SRC_IMPORT</a> = 3082 /* 3082 */
    <a name="XML_SCHEMAP_WARN_SKIP_SCHEMA">XML_SCHEMAP_WARN_SKIP_SCHEMA</a> = 3083 /* 3083 */
    <a name="XML_SCHEMAP_WARN_UNLOCATED_SCHEMA">XML_SCHEMAP_WARN_UNLOCATED_SCHEMA</a> = 3084 /* 3084 */
    <a name="XML_SCHEMAP_WARN_ATTR_REDECL_PROH">XML_SCHEMAP_WARN_ATTR_REDECL_PROH</a> = 3085 /* 3085 */
    <a name="XML_SCHEMAP_WARN_ATTR_POINTLESS_PROH">XML_SCHEMAP_WARN_ATTR_POINTLESS_PROH</a> = 3086 /* 3085 */
    <a name="XML_SCHEMAP_AG_PROPS_CORRECT">XML_SCHEMAP_AG_PROPS_CORRECT</a> = 3087 /* 3086 */
    <a name="XML_SCHEMAP_COS_CT_EXTENDS_1_2">XML_SCHEMAP_COS_CT_EXTENDS_1_2</a> = 3088 /* 3087 */
    <a name="XML_SCHEMAP_AU_PROPS_CORRECT">XML_SCHEMAP_AU_PROPS_CORRECT</a> = 3089 /* 3088 */
    <a name="XML_SCHEMAP_A_PROPS_CORRECT_3">XML_SCHEMAP_A_PROPS_CORRECT_3</a> = 3090 /* 3089 */
    <a name="XML_SCHEMAP_COS_ALL_LIMITED">XML_SCHEMAP_COS_ALL_LIMITED</a> = 3091 /* 3090 */
    <a name="XML_SCHEMATRONV_ASSERT">XML_SCHEMATRONV_ASSERT</a> = 4000 /* 4000 */
    <a name="XML_SCHEMATRONV_REPORT">XML_SCHEMATRONV_REPORT</a> = 4001
    <a name="XML_MODULE_OPEN">XML_MODULE_OPEN</a> = 4900 /* 4900 */
    <a name="XML_MODULE_CLOSE">XML_MODULE_CLOSE</a> = 4901 /* 4901 */
    <a name="XML_CHECK_FOUND_ELEMENT">XML_CHECK_FOUND_ELEMENT</a> = 5000
    <a name="XML_CHECK_FOUND_ATTRIBUTE">XML_CHECK_FOUND_ATTRIBUTE</a> = 5001 /* 5001 */
    <a name="XML_CHECK_FOUND_TEXT">XML_CHECK_FOUND_TEXT</a> = 5002 /* 5002 */
    <a name="XML_CHECK_FOUND_CDATA">XML_CHECK_FOUND_CDATA</a> = 5003 /* 5003 */
    <a name="XML_CHECK_FOUND_ENTITYREF">XML_CHECK_FOUND_ENTITYREF</a> = 5004 /* 5004 */
    <a name="XML_CHECK_FOUND_ENTITY">XML_CHECK_FOUND_ENTITY</a> = 5005 /* 5005 */
    <a name="XML_CHECK_FOUND_PI">XML_CHECK_FOUND_PI</a> = 5006 /* 5006 */
    <a name="XML_CHECK_FOUND_COMMENT">XML_CHECK_FOUND_COMMENT</a> = 5007 /* 5007 */
    <a name="XML_CHECK_FOUND_DOCTYPE">XML_CHECK_FOUND_DOCTYPE</a> = 5008 /* 5008 */
    <a name="XML_CHECK_FOUND_FRAGMENT">XML_CHECK_FOUND_FRAGMENT</a> = 5009 /* 5009 */
    <a name="XML_CHECK_FOUND_NOTATION">XML_CHECK_FOUND_NOTATION</a> = 5010 /* 5010 */
    <a name="XML_CHECK_UNKNOWN_NODE">XML_CHECK_UNKNOWN_NODE</a> = 5011 /* 5011 */
    <a name="XML_CHECK_ENTITY_TYPE">XML_CHECK_ENTITY_TYPE</a> = 5012 /* 5012 */
    <a name="XML_CHECK_NO_PARENT">XML_CHECK_NO_PARENT</a> = 5013 /* 5013 */
    <a name="XML_CHECK_NO_DOC">XML_CHECK_NO_DOC</a> = 5014 /* 5014 */
    <a name="XML_CHECK_NO_NAME">XML_CHECK_NO_NAME</a> = 5015 /* 5015 */
    <a name="XML_CHECK_NO_ELEM">XML_CHECK_NO_ELEM</a> = 5016 /* 5016 */
    <a name="XML_CHECK_WRONG_DOC">XML_CHECK_WRONG_DOC</a> = 5017 /* 5017 */
    <a name="XML_CHECK_NO_PREV">XML_CHECK_NO_PREV</a> = 5018 /* 5018 */
    <a name="XML_CHECK_WRONG_PREV">XML_CHECK_WRONG_PREV</a> = 5019 /* 5019 */
    <a name="XML_CHECK_NO_NEXT">XML_CHECK_NO_NEXT</a> = 5020 /* 5020 */
    <a name="XML_CHECK_WRONG_NEXT">XML_CHECK_WRONG_NEXT</a> = 5021 /* 5021 */
    <a name="XML_CHECK_NOT_DTD">XML_CHECK_NOT_DTD</a> = 5022 /* 5022 */
    <a name="XML_CHECK_NOT_ATTR">XML_CHECK_NOT_ATTR</a> = 5023 /* 5023 */
    <a name="XML_CHECK_NOT_ATTR_DECL">XML_CHECK_NOT_ATTR_DECL</a> = 5024 /* 5024 */
    <a name="XML_CHECK_NOT_ELEM_DECL">XML_CHECK_NOT_ELEM_DECL</a> = 5025 /* 5025 */
    <a name="XML_CHECK_NOT_ENTITY_DECL">XML_CHECK_NOT_ENTITY_DECL</a> = 5026 /* 5026 */
    <a name="XML_CHECK_NOT_NS_DECL">XML_CHECK_NOT_NS_DECL</a> = 5027 /* 5027 */
    <a name="XML_CHECK_NO_HREF">XML_CHECK_NO_HREF</a> = 5028 /* 5028 */
    <a name="XML_CHECK_WRONG_PARENT">XML_CHECK_WRONG_PARENT</a> = 5029 /* 5029 */
    <a name="XML_CHECK_NS_SCOPE">XML_CHECK_NS_SCOPE</a> = 5030 /* 5030 */
    <a name="XML_CHECK_NS_ANCESTOR">XML_CHECK_NS_ANCESTOR</a> = 5031 /* 5031 */
    <a name="XML_CHECK_NOT_UTF8">XML_CHECK_NOT_UTF8</a> = 5032 /* 5032 */
    <a name="XML_CHECK_NO_DICT">XML_CHECK_NO_DICT</a> = 5033 /* 5033 */
    <a name="XML_CHECK_NOT_NCNAME">XML_CHECK_NOT_NCNAME</a> = 5034 /* 5034 */
    <a name="XML_CHECK_OUTSIDE_DICT">XML_CHECK_OUTSIDE_DICT</a> = 5035 /* 5035 */
    <a name="XML_CHECK_WRONG_NAME">XML_CHECK_WRONG_NAME</a> = 5036 /* 5036 */
    <a name="XML_CHECK_NAME_NOT_NULL">XML_CHECK_NAME_NOT_NULL</a> = 5037 /* 5037 */
    <a name="XML_I18N_NO_NAME">XML_I18N_NO_NAME</a> = 6000
    <a name="XML_I18N_NO_HANDLER">XML_I18N_NO_HANDLER</a> = 6001 /* 6001 */
    <a name="XML_I18N_EXCESS_HANDLER">XML_I18N_EXCESS_HANDLER</a> = 6002 /* 6002 */
    <a name="XML_I18N_CONV_FAILED">XML_I18N_CONV_FAILED</a> = 6003 /* 6003 */
    <a name="XML_I18N_NO_OUTPUT">XML_I18N_NO_OUTPUT</a> = 6004 /* 6004 */
    <a name="XML_BUF_OVERFLOW">XML_BUF_OVERFLOW</a> = 7000
};
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGenericErrorFunc"/>Function type xmlGenericErrorFunc</h3><pre class="programlisting">void	xmlGenericErrorFunc		(void * ctx, <br/>					 const char * msg, <br/>					 ... ...)<br/>
</pre><p>Signature of the function to use when there is an error and no parsing or validity context available .</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>a parsing context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>the extra arguments of the varags to format the message</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlStructuredErrorFunc"/>Function type xmlStructuredErrorFunc</h3><pre class="programlisting">void	xmlStructuredErrorFunc		(void * userData, <br/>					 <a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a> error)<br/>
</pre><p>Signature of the function to use when there is an error and the module handles the new error reporting mechanism.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>userData</tt></i>:</span></td><td>user provided data for the error callback</td></tr><tr><td><span class="term"><i><tt>error</tt></i>:</span></td><td>the error being raised.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="initGenericErrorDefaultFunc"/>initGenericErrorDefaultFunc ()</h3><pre class="programlisting">void	initGenericErrorDefaultFunc	(<a href="libxml2-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> * handler)<br/>
</pre><p>Set or reset (if NULL) the default handler for generic errors to the builtin error function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>the handler</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCopyError"/>xmlCopyError ()</h3><pre class="programlisting">int	xmlCopyError			(<a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a> from, <br/>					 <a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a> to)<br/>
</pre><p>Save the original error to the new place.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>from</tt></i>:</span></td><td>a source error</td></tr><tr><td><span class="term"><i><tt>to</tt></i>:</span></td><td>a target error</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success and -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCtxtGetLastError"/>xmlCtxtGetLastError ()</h3><pre class="programlisting"><a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a>	xmlCtxtGetLastError	(void * ctx)<br/>
</pre><p>Get the last parsing error registered.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL if no error occured or a pointer to the error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCtxtResetLastError"/>xmlCtxtResetLastError ()</h3><pre class="programlisting">void	xmlCtxtResetLastError		(void * ctx)<br/>
</pre><p>Cleanup the last global error registered. For parsing error this does not change the well-formedness result.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGetLastError"/>xmlGetLastError ()</h3><pre class="programlisting"><a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a>	xmlGetLastError		(void)<br/>
</pre><p>Get the last global error registered. This is per thread if compiled with thread support.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL if no error occured or a pointer to the error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserError"/>xmlParserError ()</h3><pre class="programlisting">void	xmlParserError			(void * ctx, <br/>					 const char * msg, <br/>					 ... ...)<br/>
</pre><p>Display and format an error messages, gives file, line, position and extra parameters.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message to display/transmit</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the message display</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserPrintFileContext"/>xmlParserPrintFileContext ()</h3><pre class="programlisting">void	xmlParserPrintFileContext	(<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input)<br/>
</pre><p>Displays current context within the input content for error tracking</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserPrintFileInfo"/>xmlParserPrintFileInfo ()</h3><pre class="programlisting">void	xmlParserPrintFileInfo		(<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input)<br/>
</pre><p>Displays the associated file and line informations for the current input</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> input</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserValidityError"/>xmlParserValidityError ()</h3><pre class="programlisting">void	xmlParserValidityError		(void * ctx, <br/>					 const char * msg, <br/>					 ... ...)<br/>
</pre><p>Display and format an validity error messages, gives file, line, position and extra parameters.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message to display/transmit</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the message display</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserValidityWarning"/>xmlParserValidityWarning ()</h3><pre class="programlisting">void	xmlParserValidityWarning	(void * ctx, <br/>					 const char * msg, <br/>					 ... ...)<br/>
</pre><p>Display and format a validity warning messages, gives file, line, position and extra parameters.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message to display/transmit</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the message display</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserWarning"/>xmlParserWarning ()</h3><pre class="programlisting">void	xmlParserWarning		(void * ctx, <br/>					 const char * msg, <br/>					 ... ...)<br/>
</pre><p>Display and format a warning messages, gives file, line, position and extra parameters.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>msg</tt></i>:</span></td><td>the message to display/transmit</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the message display</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlResetError"/>xmlResetError ()</h3><pre class="programlisting">void	xmlResetError			(<a href="libxml2-xmlerror.html#xmlErrorPtr">xmlErrorPtr</a> err)<br/>
</pre><p>Cleanup the error.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>err</tt></i>:</span></td><td>pointer to the error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlResetLastError"/>xmlResetLastError ()</h3><pre class="programlisting">void	xmlResetLastError		(void)<br/>
</pre><p>Cleanup the last global error registered. For parsing error this does not change the well-formedness result.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSetGenericErrorFunc"/>xmlSetGenericErrorFunc ()</h3><pre class="programlisting">void	xmlSetGenericErrorFunc		(void * ctx, <br/>					 <a href="libxml2-xmlerror.html#xmlGenericErrorFunc">xmlGenericErrorFunc</a> handler)<br/>
</pre><p>Function to reset the handler and the error context for out of context error messages. This simply means that @handler will be called for subsequent error messages while not parsing nor validating. And @ctx will be passed as first argument to @handler One can simply force messages to be emitted to another FILE * than stderr by setting @ctx to this file handle and @handler to NULL. For multi-threaded applications, this must be set separately for each thread.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the new error handling context</td></tr><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>the new handler function</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlSetStructuredErrorFunc"/>xmlSetStructuredErrorFunc ()</h3><pre class="programlisting">void	xmlSetStructuredErrorFunc	(void * ctx, <br/>					 <a href="libxml2-xmlerror.html#xmlStructuredErrorFunc">xmlStructuredErrorFunc</a> handler)<br/>
</pre><p>Function to reset the handler and the error context for out of context structured error messages. This simply means that @handler will be called for subsequent error messages while not parsing nor validating. And @ctx will be passed as first argument to @handler For multi-threaded applications, this must be set separately for each thread.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the new error handling context</td></tr><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>the new handler function</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
