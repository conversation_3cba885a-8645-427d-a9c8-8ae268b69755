<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>libxml2: </title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="index.html" title="libxml2 Reference Manual"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="u" href="index.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-DOCBparser.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">libxml2 API Modules</span>
    </h2>
    <p><a href="libxml2-DOCBparser.html">DOCBparser</a> - old DocBook SGML parser<br/><a href="libxml2-HTMLparser.html">HTMLparser</a> - interface for an HTML 4.0 non-verifying parser<br/><a href="libxml2-HTMLtree.html">HTMLtree</a> - specific APIs to process HTML tree, especially serialization<br/><a href="libxml2-SAX.html">SAX</a> - Old SAX version 1 handler, deprecated<br/><a href="libxml2-SAX2.html">SAX2</a> - SAX2 parser interface used to build the DOM tree<br/><a href="libxml2-c14n.html">c14n</a> - Provide Canonical XML and Exclusive XML Canonicalization<br/><a href="libxml2-catalog.html">catalog</a> - interfaces to the Catalog handling system<br/><a href="libxml2-chvalid.html">chvalid</a> - Unicode character range checking<br/><a href="libxml2-debugXML.html">debugXML</a> - Tree debugging APIs<br/><a href="libxml2-dict.html">dict</a> - string dictionary<br/><a href="libxml2-encoding.html">encoding</a> - interface for the encoding conversion functions<br/><a href="libxml2-entities.html">entities</a> - interface for the XML entities handling<br/><a href="libxml2-globals.html">globals</a> - interface for all global variables of the library<br/><a href="libxml2-hash.html">hash</a> - Chained hash tables<br/><a href="libxml2-list.html">list</a> - lists interfaces<br/><a href="libxml2-nanoftp.html">nanoftp</a> - minimal FTP implementation<br/><a href="libxml2-nanohttp.html">nanohttp</a> - minimal HTTP implementation<br/><a href="libxml2-parser.html">parser</a> - the core parser module<br/><a href="libxml2-parserInternals.html">parserInternals</a> - internals routines and limits exported by the parser.<br/><a href="libxml2-pattern.html">pattern</a> - pattern expression handling<br/><a href="libxml2-relaxng.html">relaxng</a> - implementation of the Relax-NG validation<br/><a href="libxml2-schemasInternals.html">schemasInternals</a> - internal interfaces for XML Schemas<br/><a href="libxml2-schematron.html">schematron</a> - XML Schemastron implementation<br/><a href="libxml2-threads.html">threads</a> - interfaces for thread handling<br/><a href="libxml2-tree.html">tree</a> - interfaces for tree manipulation<br/><a href="libxml2-uri.html">uri</a> - library of generic URI related routines<br/><a href="libxml2-valid.html">valid</a> - The DTD validation<br/><a href="libxml2-xinclude.html">xinclude</a> - implementation of XInclude<br/><a href="libxml2-xlink.html">xlink</a> - unfinished XLink detection module<br/><a href="libxml2-xmlIO.html">xmlIO</a> - interface for the I/O interfaces used by the parser<br/><a href="libxml2-xmlautomata.html">xmlautomata</a> - API to build regexp automata<br/><a href="libxml2-xmlerror.html">xmlerror</a> - error handling<br/><a href="libxml2-xmlexports.html">xmlexports</a> - macros for marking symbols as exportable/importable.<br/><a href="libxml2-xmlmemory.html">xmlmemory</a> - interface for the memory allocator<br/><a href="libxml2-xmlmodule.html">xmlmodule</a> - dynamic module loading<br/><a href="libxml2-xmlreader.html">xmlreader</a> - the XMLReader implementation<br/><a href="libxml2-xmlregexp.html">xmlregexp</a> - regular expressions handling<br/><a href="libxml2-xmlsave.html">xmlsave</a> - the XML document serializer<br/><a href="libxml2-xmlschemas.html">xmlschemas</a> - incomplete XML Schemas structure implementation<br/><a href="libxml2-xmlschemastypes.html">xmlschemastypes</a> - implementation of XML Schema Datatypes<br/><a href="libxml2-xmlstring.html">xmlstring</a> - set of routines to process strings<br/><a href="libxml2-xmlunicode.html">xmlunicode</a> - Unicode character APIs<br/><a href="libxml2-xmlversion.html">xmlversion</a> - compile-time version informations<br/><a href="libxml2-xmlwriter.html">xmlwriter</a> - text writing API for XML<br/><a href="libxml2-xpath.html">xpath</a> - XML Path Language implementation<br/><a href="libxml2-xpathInternals.html">xpathInternals</a> - internal interfaces for XML Path Language implementation<br/><a href="libxml2-xpointer.html">xpointer</a> - API to handle XML Pointers<br/></p>
  </body>
</html>
