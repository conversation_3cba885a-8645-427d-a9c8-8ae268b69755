<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>debugXML: Tree debugging APIs</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-chvalid.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-dict.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">debugXML</span>
    </h2>
    <p>debugXML - Tree debugging APIs</p>
    <p>Interfaces to a set of routines used for debugging the tree produced by the XML parser. </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">typedef struct _xmlShellCtxt <a href="#xmlShellCtxt">xmlShellCtxt</a>;
typedef <a href="libxml2-debugXML.html#xmlShellCtxt">xmlShellCtxt</a> * <a href="#xmlShellCtxtPtr">xmlShellCtxtPtr</a>;
void	<a href="#xmlDebugDumpAttrList">xmlDebugDumpAttrList</a>		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br/>					 int depth);
void	<a href="#xmlLsOneNode">xmlLsOneNode</a>			(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
typedef char * <a href="#xmlShellReadlineFunc">xmlShellReadlineFunc</a>		(char * prompt);
int	<a href="#xmlShellSave">xmlShellSave</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * filename, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
const char *	<a href="#xmlBoolToText">xmlBoolToText</a>		(int boolval);
int	<a href="#xmlShellWrite">xmlShellWrite</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * filename, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
int	<a href="#xmlShellDu">xmlShellDu</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
int	<a href="#xmlShellValidate">xmlShellValidate</a>		(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * dtd, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
int	<a href="#xmlDebugCheckDocument">xmlDebugCheckDocument</a>		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc);
void	<a href="#xmlShellPrintXPathResult">xmlShellPrintXPathResult</a>	(<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> list);
typedef int <a href="#xmlShellCmd">xmlShellCmd</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
int	<a href="#xmlShellLoad">xmlShellLoad</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * filename, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
void	<a href="#xmlDebugDumpString">xmlDebugDumpString</a>		(FILE * output, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str);
int	<a href="#xmlShellBase">xmlShellBase</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
int	<a href="#xmlShellCat">xmlShellCat</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
void	<a href="#xmlDebugDumpDTD">xmlDebugDumpDTD</a>			(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDtdPtr">xmlDtdPtr</a> dtd);
void	<a href="#xmlDebugDumpNode">xmlDebugDumpNode</a>		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 int depth);
void	<a href="#xmlDebugDumpEntities">xmlDebugDumpEntities</a>		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc);
void	<a href="#xmlShellPrintNode">xmlShellPrintNode</a>		(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
int	<a href="#xmlShellPwd">xmlShellPwd</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * buffer, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
void	<a href="#xmlDebugDumpNodeList">xmlDebugDumpNodeList</a>		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 int depth);
void	<a href="#xmlDebugDumpAttr">xmlDebugDumpAttr</a>		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br/>					 int depth);
void	<a href="#xmlDebugDumpDocument">xmlDebugDumpDocument</a>		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc);
int	<a href="#xmlLsCountNode">xmlLsCountNode</a>			(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node);
void	<a href="#xmlShellPrintXPathError">xmlShellPrintXPathError</a>		(int errorType, <br/>					 const char * arg);
int	<a href="#xmlShellDir">xmlShellDir</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
void	<a href="#xmlDebugDumpOneNode">xmlDebugDumpOneNode</a>		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 int depth);
int	<a href="#xmlShellList">xmlShellList</a>			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2);
void	<a href="#xmlDebugDumpDocumentHead">xmlDebugDumpDocumentHead</a>	(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc);
void	<a href="#xmlShell">xmlShell</a>			(<a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br/>					 char * filename, <br/>					 <a href="libxml2-debugXML.html#xmlShellReadlineFunc">xmlShellReadlineFunc</a> input, <br/>					 FILE * output);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlShellCtxt">Structure </a>xmlShellCtxt</h3><pre class="programlisting">struct _xmlShellCtxt {
    char *	filename
    <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a>	doc
    <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a>	node
    <a href="libxml2-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a>	pctxt
    int	loaded
    FILE *	output
    <a href="libxml2-debugXML.html#xmlShellReadlineFunc">xmlShellReadlineFunc</a>	input
} xmlShellCtxt;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellCtxtPtr">Typedef </a>xmlShellCtxtPtr</h3><pre class="programlisting"><a href="libxml2-debugXML.html#xmlShellCtxt">xmlShellCtxt</a> * xmlShellCtxtPtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellCmd"/>Function type xmlShellCmd</h3><pre class="programlisting">int	xmlShellCmd			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>This is a generic signature for the XML shell functions.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>a shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>a string argument</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a first node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>a second node</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int, negative returns indicating errors.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellReadlineFunc"/>Function type xmlShellReadlineFunc</h3><pre class="programlisting">char *	xmlShellReadlineFunc		(char * prompt)<br/>
</pre><p>This is a generic signature for the XML shell input function.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>prompt</tt></i>:</span></td><td>a string prompt</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a string which will be freed by the Shell.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlBoolToText"/>xmlBoolToText ()</h3><pre class="programlisting">const char *	xmlBoolToText		(int boolval)<br/>
</pre><p>Convenient way to turn bool into text</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>boolval</tt></i>:</span></td><td>a bool to turn into text</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to either "True" or "False"</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugCheckDocument"/>xmlDebugCheckDocument ()</h3><pre class="programlisting">int	xmlDebugCheckDocument		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br/>
</pre><p>Check the document for potential content problems, and output the errors to @output</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of errors found</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpAttr"/>xmlDebugDumpAttr ()</h3><pre class="programlisting">void	xmlDebugDumpAttr		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br/>					 int depth)<br/>
</pre><p>Dumps debug information for the <a href="libxml2-SAX.html#attribute">attribute</a></p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>attr</tt></i>:</span></td><td>the <a href="libxml2-SAX.html#attribute">attribute</a></td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpAttrList"/>xmlDebugDumpAttrList ()</h3><pre class="programlisting">void	xmlDebugDumpAttrList		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlAttrPtr">xmlAttrPtr</a> attr, <br/>					 int depth)<br/>
</pre><p>Dumps debug information for the <a href="libxml2-SAX.html#attribute">attribute</a> list</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>attr</tt></i>:</span></td><td>the <a href="libxml2-SAX.html#attribute">attribute</a> list</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpDTD"/>xmlDebugDumpDTD ()</h3><pre class="programlisting">void	xmlDebugDumpDTD			(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDtdPtr">xmlDtdPtr</a> dtd)<br/>
</pre><p>Dumps debug information for the DTD</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>dtd</tt></i>:</span></td><td>the DTD</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpDocument"/>xmlDebugDumpDocument ()</h3><pre class="programlisting">void	xmlDebugDumpDocument		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br/>
</pre><p>Dumps debug information for the document, it's recursive</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpDocumentHead"/>xmlDebugDumpDocumentHead ()</h3><pre class="programlisting">void	xmlDebugDumpDocumentHead	(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br/>
</pre><p>Dumps debug information cncerning the document, not recursive</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpEntities"/>xmlDebugDumpEntities ()</h3><pre class="programlisting">void	xmlDebugDumpEntities		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc)<br/>
</pre><p>Dumps debug information for all the entities in use by the document</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the document</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpNode"/>xmlDebugDumpNode ()</h3><pre class="programlisting">void	xmlDebugDumpNode		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 int depth)<br/>
</pre><p>Dumps debug information for the element node, it is recursive</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpNodeList"/>xmlDebugDumpNodeList ()</h3><pre class="programlisting">void	xmlDebugDumpNodeList		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 int depth)<br/>
</pre><p>Dumps debug information for the list of element node, it is recursive</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node list</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpOneNode"/>xmlDebugDumpOneNode ()</h3><pre class="programlisting">void	xmlDebugDumpOneNode		(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 int depth)<br/>
</pre><p>Dumps debug information for the element node, it is not recursive</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node</td></tr><tr><td><span class="term"><i><tt>depth</tt></i>:</span></td><td>the indentation level.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDebugDumpString"/>xmlDebugDumpString ()</h3><pre class="programlisting">void	xmlDebugDumpString		(FILE * output, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str)<br/>
</pre><p>Dumps informations about the string, shorten it if necessary</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the string</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlLsCountNode"/>xmlLsCountNode ()</h3><pre class="programlisting">int	xmlLsCountNode			(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Count the children of @node.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to count</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of children of @node.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlLsOneNode"/>xmlLsOneNode ()</h3><pre class="programlisting">void	xmlLsOneNode			(FILE * output, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Dump to @output the type and name of @node.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the FILE * for the output</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>the node to dump</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShell"/>xmlShell ()</h3><pre class="programlisting">void	xmlShell			(<a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br/>					 char * filename, <br/>					 <a href="libxml2-debugXML.html#xmlShellReadlineFunc">xmlShellReadlineFunc</a> input, <br/>					 FILE * output)<br/>
</pre><p>Implements the XML shell This allow to load, validate, view, modify and save a document using a environment similar to a UNIX commandline.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>the initial document</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the output buffer</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the line reading function</td></tr><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the output FILE*, defaults to stdout if NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellBase"/>xmlShellBase ()</h3><pre class="programlisting">int	xmlShellBase			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "base" dumps the current XML base of the node</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellCat"/>xmlShellCat ()</h3><pre class="programlisting">int	xmlShellCat			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "cat" dumps the serialization node content (XML or HTML).</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellDir"/>xmlShellDir ()</h3><pre class="programlisting">int	xmlShellDir			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "dir" dumps informations about the node (namespace, attributes, content).</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellDu"/>xmlShellDu ()</h3><pre class="programlisting">int	xmlShellDu			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> tree, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "du" show the structure of the subtree under node @tree If @tree is null, the command works on the current node.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>a node defining a subtree</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellList"/>xmlShellList ()</h3><pre class="programlisting">int	xmlShellList			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * arg, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "ls" Does an Unix like listing of the given node (like a directory)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellLoad"/>xmlShellLoad ()</h3><pre class="programlisting">int	xmlShellLoad			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * filename, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "load" loads a new document specified by the filename</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file name</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 if loading failed</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellPrintNode"/>xmlShellPrintNode ()</h3><pre class="programlisting">void	xmlShellPrintNode		(<a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node)<br/>
</pre><p>Print node to the output FILE</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a non-null node to print to the output FILE</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellPrintXPathError"/>xmlShellPrintXPathError ()</h3><pre class="programlisting">void	xmlShellPrintXPathError		(int errorType, <br/>					 const char * arg)<br/>
</pre><p>Print the xpath error to libxml default error channel</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>errorType</tt></i>:</span></td><td>valid xpath error id</td></tr><tr><td><span class="term"><i><tt>arg</tt></i>:</span></td><td>the argument that cause xpath to fail</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellPrintXPathResult"/>xmlShellPrintXPathResult ()</h3><pre class="programlisting">void	xmlShellPrintXPathResult	(<a href="libxml2-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> list)<br/>
</pre><p>Prints result to the output FILE</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>list</tt></i>:</span></td><td>a valid result generated by an xpath evaluation</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellPwd"/>xmlShellPwd ()</h3><pre class="programlisting">int	xmlShellPwd			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * buffer, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "pwd" Show the full path from the root to the node, if needed building thumblers when similar elements exists at a given ancestor level. The output is compatible with XPath commands.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>the output buffer</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellSave"/>xmlShellSave ()</h3><pre class="programlisting">int	xmlShellSave			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * filename, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "save" Write the current document to the filename, or it's original name</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file name (optional)</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellValidate"/>xmlShellValidate ()</h3><pre class="programlisting">int	xmlShellValidate		(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * dtd, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "validate" Validate the document, if a DTD path is provided, then the validation is done against the given DTD.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>dtd</tt></i>:</span></td><td>the DTD URI (optional)</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlShellWrite"/>xmlShellWrite ()</h3><pre class="programlisting">int	xmlShellWrite			(<a href="libxml2-debugXML.html#xmlShellCtxtPtr">xmlShellCtxtPtr</a> ctxt, <br/>					 char * filename, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>					 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node2)<br/>
</pre><p>Implements the XML shell function "write" Write the current node to the filename, it saves the serialization of the subtree under the @node specified</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the shell context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the file name</td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td>a node in the tree</td></tr><tr><td><span class="term"><i><tt>node2</tt></i>:</span></td><td>unused</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
