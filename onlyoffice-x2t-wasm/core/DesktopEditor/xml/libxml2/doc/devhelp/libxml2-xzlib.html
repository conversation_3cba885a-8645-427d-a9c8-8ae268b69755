<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xzlib: </title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xpointer.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xzlib</span>
    </h2>
    <p>xzlib - </p>
    <p/>
    <p>Author(s): </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">#define <a href="#LIBXML2_XZLIB_H">LIBXML2_XZLIB_H</a>;
typedef void * <a href="#xzFile">xzFile</a>;
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="LIBXML2_XZLIB_H">Macro </a>LIBXML2_XZLIB_H</h3><pre class="programlisting">#define <a href="#LIBXML2_XZLIB_H">LIBXML2_XZLIB_H</a>;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xzFile">Typedef </a>xzFile</h3><pre class="programlisting">void * xzFile;
</pre><p/>
</div>
        <hr/>
      </div>
    </div>
  </body>
</html>
