<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>SAX: Old SAX version 1 handler, deprecated</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-HTMLtree.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-SAX2.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">SAX</span>
    </h2>
    <p>SAX - Old SAX version 1 handler, deprecated</p>
    <p>DEPRECATED set of SAX version 1 interfaces used to build the DOM tree. </p>
    <p> WARNING: this module is deprecated !</p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">void	<a href="#comment">comment</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value);
int	<a href="#checkNamespace">checkNamespace</a>			(void * ctx, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespace);
int	<a href="#getColumnNumber">getColumnNumber</a>			(void * ctx);
void	<a href="#entityDecl">entityDecl</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 int type, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
void	<a href="#attribute">attribute</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value);
<a href="libxml2-tree.html#xmlNsPtr">xmlNsPtr</a>	<a href="#getNamespace">getNamespace</a>		(void * ctx);
void	<a href="#setDocumentLocator">setDocumentLocator</a>		(void * ctx, <br/>					 <a href="libxml2-tree.html#xmlSAXLocatorPtr">xmlSAXLocatorPtr</a> loc);
void	<a href="#initxmlDefaultSAXHandler">initxmlDefaultSAXHandler</a>	(<a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr, <br/>					 int warning);
void	<a href="#ignorableWhitespace">ignorableWhitespace</a>		(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ch, <br/>					 int len);
int	<a href="#hasExternalSubset">hasExternalSubset</a>		(void * ctx);
void	<a href="#unparsedEntityDecl">unparsedEntityDecl</a>		(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * notationName);
void	<a href="#globalNamespace">globalNamespace</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * href, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix);
int	<a href="#hasInternalSubset">hasInternalSubset</a>		(void * ctx);
void	<a href="#reference">reference</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
void	<a href="#notationDecl">notationDecl</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * systemId);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#getSystemId">getSystemId</a>		(void * ctx);
void	<a href="#externalSubset">externalSubset</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * SystemID);
<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#resolveEntity">resolveEntity</a>	(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * systemId);
void	<a href="#startDocument">startDocument</a>			(void * ctx);
void	<a href="#setNamespace">setNamespace</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
void	<a href="#cdataBlock">cdataBlock</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value, <br/>					 int len);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#getPublicId">getPublicId</a>		(void * ctx);
void	<a href="#inithtmlDefaultSAXHandler">inithtmlDefaultSAXHandler</a>	(<a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr);
void	<a href="#processingInstruction">processingInstruction</a>		(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * data);
void	<a href="#endElement">endElement</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
void	<a href="#namespaceDecl">namespaceDecl</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * href, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix);
void	<a href="#initdocbDefaultSAXHandler">initdocbDefaultSAXHandler</a>	(<a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr);
<a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#getEntity">getEntity</a>		(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
void	<a href="#characters">characters</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ch, <br/>					 int len);
void	<a href="#elementDecl">elementDecl</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 int type, <br/>					 <a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> content);
void	<a href="#startElement">startElement</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** atts);
<a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a>	<a href="#getParameterEntity">getParameterEntity</a>	(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
void	<a href="#attributeDecl">attributeDecl</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * elem, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br/>					 int type, <br/>					 int def, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * defaultValue, <br/>					 <a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> tree);
int	<a href="#isStandalone">isStandalone</a>			(void * ctx);
void	<a href="#internalSubset">internalSubset</a>			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * SystemID);
void	<a href="#endDocument">endDocument</a>			(void * ctx);
int	<a href="#getLineNumber">getLineNumber</a>			(void * ctx);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="attribute"/>attribute ()</h3><pre class="programlisting">void	attribute			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value)<br/>
</pre><p>Handle an <a href="libxml2-SAX.html#attribute">attribute</a> that has been read by the parser. The default handling is to convert the <a href="libxml2-SAX.html#attribute">attribute</a> into an DOM subtree and past it in a new <a href="libxml2-tree.html#xmlAttr">xmlAttr</a> element added to the element. DEPRECATED: use xmlSAX2Attribute()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>The <a href="libxml2-SAX.html#attribute">attribute</a> name, including namespace prefix</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>The <a href="libxml2-SAX.html#attribute">attribute</a> value</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="attributeDecl"/>attributeDecl ()</h3><pre class="programlisting">void	attributeDecl			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * elem, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br/>					 int type, <br/>					 int def, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * defaultValue, <br/>					 <a href="libxml2-tree.html#xmlEnumerationPtr">xmlEnumerationPtr</a> tree)<br/>
</pre><p>An <a href="libxml2-SAX.html#attribute">attribute</a> definition has been parsed DEPRECATED: use xmlSAX2AttributeDecl()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>elem</tt></i>:</span></td><td>the name of the element</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>the <a href="libxml2-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the <a href="libxml2-SAX.html#attribute">attribute</a> type</td></tr><tr><td><span class="term"><i><tt>def</tt></i>:</span></td><td>the type of default value</td></tr><tr><td><span class="term"><i><tt>defaultValue</tt></i>:</span></td><td>the <a href="libxml2-SAX.html#attribute">attribute</a> default value</td></tr><tr><td><span class="term"><i><tt>tree</tt></i>:</span></td><td>the tree of enumerated value set</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="cdataBlock"/>cdataBlock ()</h3><pre class="programlisting">void	cdataBlock			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value, <br/>					 int len)<br/>
</pre><p>called when a pcdata block has been parsed DEPRECATED: use xmlSAX2CDataBlock()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>The pcdata content</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the block length</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="characters"/>characters ()</h3><pre class="programlisting">void	characters			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ch, <br/>					 int len)<br/>
</pre><p>receiving some chars from the parser. DEPRECATED: use xmlSAX2Characters()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>a <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a></td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="checkNamespace"/>checkNamespace ()</h3><pre class="programlisting">int	checkNamespace			(void * ctx, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespace)<br/>
</pre><p>Check that the current element namespace is the same as the one read upon parsing. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>namespace</tt></i>:</span></td><td>the namespace to check against</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="comment"/>comment ()</h3><pre class="programlisting">void	comment			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * value)<br/>
</pre><p>A <a href="libxml2-SAX.html#comment">comment</a> has been parsed. DEPRECATED: use xmlSAX2Comment()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>value</tt></i>:</span></td><td>the <a href="libxml2-SAX.html#comment">comment</a> content</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="elementDecl"/>elementDecl ()</h3><pre class="programlisting">void	elementDecl			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 int type, <br/>					 <a href="libxml2-tree.html#xmlElementContentPtr">xmlElementContentPtr</a> content)<br/>
</pre><p>An element definition has been parsed DEPRECATED: use xmlSAX2ElementDecl()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the element name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the element type</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the element value tree</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="endDocument"/>endDocument ()</h3><pre class="programlisting">void	endDocument			(void * ctx)<br/>
</pre><p>called when the document end has been detected. DEPRECATED: use xmlSAX2EndDocument()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="endElement"/>endElement ()</h3><pre class="programlisting">void	endElement			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>called when the end of an element has been detected. DEPRECATED: use xmlSAX2EndElement()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The element name</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="entityDecl"/>entityDecl ()</h3><pre class="programlisting">void	entityDecl			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 int type, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>An entity definition has been parsed DEPRECATED: use xmlSAX2EntityDecl()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the entity name</td></tr><tr><td><span class="term"><i><tt>type</tt></i>:</span></td><td>the entity type</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>the entity value (without processing).</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="externalSubset"/>externalSubset ()</h3><pre class="programlisting">void	externalSubset			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br/>
</pre><p>Callback on external subset declaration. DEPRECATED: use xmlSAX2ExternalSubset()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the root element name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the SYSTEM ID (e.g. filename or URL)</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="getColumnNumber"/>getColumnNumber ()</h3><pre class="programlisting">int	getColumnNumber			(void * ctx)<br/>
</pre><p>Provide the column number of the current parsing point. DEPRECATED: use xmlSAX2GetColumnNumber()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="getEntity"/>getEntity ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a>	getEntity		(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Get an entity by name DEPRECATED: use xmlSAX2GetEntity()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="getLineNumber"/>getLineNumber ()</h3><pre class="programlisting">int	getLineNumber			(void * ctx)<br/>
</pre><p>Provide the line number of the current parsing point. DEPRECATED: use xmlSAX2GetLineNumber()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an int</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="getNamespace"/>getNamespace ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlNsPtr">xmlNsPtr</a>	getNamespace		(void * ctx)<br/>
</pre><p>Get the current element namespace. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-tree.html#xmlNsPtr">xmlNsPtr</a> or NULL if none</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="getParameterEntity"/>getParameterEntity ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a>	getParameterEntity	(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Get a parameter entity by name DEPRECATED: use xmlSAX2GetParameterEntity()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-tree.html#xmlEntityPtr">xmlEntityPtr</a> if found.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="getPublicId"/>getPublicId ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	getPublicId		(void * ctx)<br/>
</pre><p>Provides the public ID e.g. "-//SGMLSOURCE//DTD DEMO//EN" DEPRECATED: use xmlSAX2GetPublicId()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="getSystemId"/>getSystemId ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	getSystemId		(void * ctx)<br/>
</pre><p>Provides the system ID, basically URL or filename e.g. http://www.sgmlsource.com/dtds/memo.dtd DEPRECATED: use xmlSAX2GetSystemId()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="globalNamespace"/>globalNamespace ()</h3><pre class="programlisting">void	globalNamespace			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * href, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix)<br/>
</pre><p>An old global namespace has been parsed. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>href</tt></i>:</span></td><td>the namespace associated URN</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the namespace prefix</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="hasExternalSubset"/>hasExternalSubset ()</h3><pre class="programlisting">int	hasExternalSubset		(void * ctx)<br/>
</pre><p>Does this document has an external subset DEPRECATED: use xmlSAX2HasExternalSubset()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="hasInternalSubset"/>hasInternalSubset ()</h3><pre class="programlisting">int	hasInternalSubset		(void * ctx)<br/>
</pre><p>Does this document has an internal subset DEPRECATED: use xmlSAX2HasInternalSubset()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="ignorableWhitespace"/>ignorableWhitespace ()</h3><pre class="programlisting">void	ignorableWhitespace		(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ch, <br/>					 int len)<br/>
</pre><p>receiving some ignorable whitespaces from the parser. UNUSED: by default the DOM building will use <a href="libxml2-SAX.html#characters">characters</a> DEPRECATED: use xmlSAX2IgnorableWhitespace()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>a <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a></td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="initdocbDefaultSAXHandler"/>initdocbDefaultSAXHandler ()</h3><pre class="programlisting">void	initdocbDefaultSAXHandler	(<a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr)<br/>
</pre><p>Initialize the default DocBook SAX version 1 handler DEPRECATED: use xmlSAX2InitDocbDefaultSAXHandler() for the new SAX2 blocks</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="inithtmlDefaultSAXHandler"/>inithtmlDefaultSAXHandler ()</h3><pre class="programlisting">void	inithtmlDefaultSAXHandler	(<a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr)<br/>
</pre><p>Initialize the default HTML SAX version 1 handler DEPRECATED: use xmlSAX2InitHtmlDefaultSAXHandler() for the new SAX2 blocks</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="initxmlDefaultSAXHandler"/>initxmlDefaultSAXHandler ()</h3><pre class="programlisting">void	initxmlDefaultSAXHandler	(<a href="libxml2-parser.html#xmlSAXHandlerV1">xmlSAXHandlerV1</a> * hdlr, <br/>					 int warning)<br/>
</pre><p>Initialize the default XML SAX version 1 handler DEPRECATED: use xmlSAX2InitDefaultSAXHandler() for the new SAX2 blocks</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>hdlr</tt></i>:</span></td><td>the SAX handler</td></tr><tr><td><span class="term"><i><tt>warning</tt></i>:</span></td><td>flag if non-zero sets the handler warning procedure</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="internalSubset"/>internalSubset ()</h3><pre class="programlisting">void	internalSubset			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ExternalID, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * SystemID)<br/>
</pre><p>Callback on internal subset declaration. DEPRECATED: use xmlSAX2InternalSubset()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the root element name</td></tr><tr><td><span class="term"><i><tt>ExternalID</tt></i>:</span></td><td>the external ID</td></tr><tr><td><span class="term"><i><tt>SystemID</tt></i>:</span></td><td>the SYSTEM ID (e.g. filename or URL)</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="isStandalone"/>isStandalone ()</h3><pre class="programlisting">int	isStandalone			(void * ctx)<br/>
</pre><p>Is this document tagged standalone ? DEPRECATED: use xmlSAX2IsStandalone()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if true</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="namespaceDecl"/>namespaceDecl ()</h3><pre class="programlisting">void	namespaceDecl			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * href, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix)<br/>
</pre><p>A namespace has been parsed. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>href</tt></i>:</span></td><td>the namespace associated URN</td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>the namespace prefix</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="notationDecl"/>notationDecl ()</h3><pre class="programlisting">void	notationDecl			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * systemId)<br/>
</pre><p>What to do when a notation declaration has been parsed. DEPRECATED: use xmlSAX2NotationDecl()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The name of the notation</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="processingInstruction"/>processingInstruction ()</h3><pre class="programlisting">void	processingInstruction		(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * data)<br/>
</pre><p>A processing instruction has been parsed. DEPRECATED: use xmlSAX2ProcessingInstruction()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>the target name</td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>the PI data's</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="reference"/>reference ()</h3><pre class="programlisting">void	reference			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>called when an entity <a href="libxml2-SAX.html#reference">reference</a> is detected. DEPRECATED: use xmlSAX2Reference()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The entity name</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="resolveEntity"/>resolveEntity ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	resolveEntity	(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * systemId)<br/>
</pre><p>The entity loader, to control the loading of external entities, the application can either: - override this resolveEntity() callback in the SAX block - or better use the xmlSetExternalEntityLoader() function to set up it's own entity resolution routine DEPRECATED: use xmlSAX2ResolveEntity()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> if inlined or NULL for DOM behaviour.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="setDocumentLocator"/>setDocumentLocator ()</h3><pre class="programlisting">void	setDocumentLocator		(void * ctx, <br/>					 <a href="libxml2-tree.html#xmlSAXLocatorPtr">xmlSAXLocatorPtr</a> loc)<br/>
</pre><p>Receive the document locator at startup, actually <a href="libxml2-globals.html#xmlDefaultSAXLocator">xmlDefaultSAXLocator</a> Everything is available on the context, so this is useless in our case. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>loc</tt></i>:</span></td><td>A SAX Locator</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="setNamespace"/>setNamespace ()</h3><pre class="programlisting">void	setNamespace			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Set the current element namespace. DEPRECATED</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the namespace prefix</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="startDocument"/>startDocument ()</h3><pre class="programlisting">void	startDocument			(void * ctx)<br/>
</pre><p>called when the document start being processed. DEPRECATED: use xmlSAX2StartDocument()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="startElement"/>startElement ()</h3><pre class="programlisting">void	startElement			(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * fullname, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> ** atts)<br/>
</pre><p>called when an opening tag has been processed. DEPRECATED: use xmlSAX2StartElement()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>fullname</tt></i>:</span></td><td>The element name, including namespace prefix</td></tr><tr><td><span class="term"><i><tt>atts</tt></i>:</span></td><td>An array of name/value attributes pairs, NULL terminated</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="unparsedEntityDecl"/>unparsedEntityDecl ()</h3><pre class="programlisting">void	unparsedEntityDecl		(void * ctx, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * publicId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * systemId, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * notationName)<br/>
</pre><p>What to do when an unparsed entity declaration is parsed DEPRECATED: use xmlSAX2UnparsedEntityDecl()</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the user data (XML parser context)</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>The name of the entity</td></tr><tr><td><span class="term"><i><tt>publicId</tt></i>:</span></td><td>The public ID of the entity</td></tr><tr><td><span class="term"><i><tt>systemId</tt></i>:</span></td><td>The system ID of the entity</td></tr><tr><td><span class="term"><i><tt>notationName</tt></i>:</span></td><td>the name of the notation</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
