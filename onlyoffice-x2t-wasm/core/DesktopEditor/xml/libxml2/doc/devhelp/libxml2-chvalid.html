<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>chvalid: Unicode character range checking</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-catalog.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-debugXML.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">chvalid</span>
    </h2>
    <p>chvalid - Unicode character range checking</p>
    <p>this module exports interfaces for the character range validation APIs  This file is automatically generated from the cvs source definition files using the genChRanges.py Python script </p>
    <p>Author(s): William Brack &lt;<EMAIL>&gt; </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">#define <a href="#xmlIsExtender_ch">xmlIsExtender_ch</a>(c);
#define <a href="#xmlIsPubidCharQ">xmlIsPubidCharQ</a>(c);
#define <a href="#xmlIsPubidChar_ch">xmlIsPubidChar_ch</a>(c);
#define <a href="#xmlIsIdeographicQ">xmlIsIdeographicQ</a>(c);
#define <a href="#xmlIsExtenderQ">xmlIsExtenderQ</a>(c);
#define <a href="#xmlIsChar_ch">xmlIsChar_ch</a>(c);
#define <a href="#xmlIsDigitQ">xmlIsDigitQ</a>(c);
#define <a href="#xmlIsDigit_ch">xmlIsDigit_ch</a>(c);
#define <a href="#xmlIsBaseChar_ch">xmlIsBaseChar_ch</a>(c);
#define <a href="#xmlIsCombiningQ">xmlIsCombiningQ</a>(c);
#define <a href="#xmlIsBlankQ">xmlIsBlankQ</a>(c);
#define <a href="#xmlIsCharQ">xmlIsCharQ</a>(c);
#define <a href="#xmlIsBaseCharQ">xmlIsBaseCharQ</a>(c);
#define <a href="#xmlIsBlank_ch">xmlIsBlank_ch</a>(c);
typedef struct _xmlChLRange <a href="#xmlChLRange">xmlChLRange</a>;
typedef <a href="libxml2-chvalid.html#xmlChSRange">xmlChSRange</a> * <a href="#xmlChSRangePtr">xmlChSRangePtr</a>;
typedef <a href="libxml2-chvalid.html#xmlChLRange">xmlChLRange</a> * <a href="#xmlChLRangePtr">xmlChLRangePtr</a>;
typedef <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> * <a href="#xmlChRangeGroupPtr">xmlChRangeGroupPtr</a>;
typedef struct _xmlChSRange <a href="#xmlChSRange">xmlChSRange</a>;
typedef struct _xmlChRangeGroup <a href="#xmlChRangeGroup">xmlChRangeGroup</a>;
int	<a href="#xmlIsChar">xmlIsChar</a>			(unsigned int ch);
int	<a href="#xmlIsDigit">xmlIsDigit</a>			(unsigned int ch);
int	<a href="#xmlIsBlank">xmlIsBlank</a>			(unsigned int ch);
int	<a href="#xmlIsIdeographic">xmlIsIdeographic</a>		(unsigned int ch);
int	<a href="#xmlCharInRange">xmlCharInRange</a>			(unsigned int val, <br/>					 const <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> * rptr);
int	<a href="#xmlIsPubidChar">xmlIsPubidChar</a>			(unsigned int ch);
int	<a href="#xmlIsCombining">xmlIsCombining</a>			(unsigned int ch);
int	<a href="#xmlIsBaseChar">xmlIsBaseChar</a>			(unsigned int ch);
int	<a href="#xmlIsExtender">xmlIsExtender</a>			(unsigned int ch);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlIsBaseCharQ">Macro </a>xmlIsBaseCharQ</h3><pre class="programlisting">#define <a href="#xmlIsBaseCharQ">xmlIsBaseCharQ</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsBaseChar_ch">Macro </a>xmlIsBaseChar_ch</h3><pre class="programlisting">#define <a href="#xmlIsBaseChar_ch">xmlIsBaseChar_ch</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsBlankQ">Macro </a>xmlIsBlankQ</h3><pre class="programlisting">#define <a href="#xmlIsBlankQ">xmlIsBlankQ</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsBlank_ch">Macro </a>xmlIsBlank_ch</h3><pre class="programlisting">#define <a href="#xmlIsBlank_ch">xmlIsBlank_ch</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsCharQ">Macro </a>xmlIsCharQ</h3><pre class="programlisting">#define <a href="#xmlIsCharQ">xmlIsCharQ</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsChar_ch">Macro </a>xmlIsChar_ch</h3><pre class="programlisting">#define <a href="#xmlIsChar_ch">xmlIsChar_ch</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsCombiningQ">Macro </a>xmlIsCombiningQ</h3><pre class="programlisting">#define <a href="#xmlIsCombiningQ">xmlIsCombiningQ</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsDigitQ">Macro </a>xmlIsDigitQ</h3><pre class="programlisting">#define <a href="#xmlIsDigitQ">xmlIsDigitQ</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsDigit_ch">Macro </a>xmlIsDigit_ch</h3><pre class="programlisting">#define <a href="#xmlIsDigit_ch">xmlIsDigit_ch</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsExtenderQ">Macro </a>xmlIsExtenderQ</h3><pre class="programlisting">#define <a href="#xmlIsExtenderQ">xmlIsExtenderQ</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsExtender_ch">Macro </a>xmlIsExtender_ch</h3><pre class="programlisting">#define <a href="#xmlIsExtender_ch">xmlIsExtender_ch</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsIdeographicQ">Macro </a>xmlIsIdeographicQ</h3><pre class="programlisting">#define <a href="#xmlIsIdeographicQ">xmlIsIdeographicQ</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsPubidCharQ">Macro </a>xmlIsPubidCharQ</h3><pre class="programlisting">#define <a href="#xmlIsPubidCharQ">xmlIsPubidCharQ</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsPubidChar_ch">Macro </a>xmlIsPubidChar_ch</h3><pre class="programlisting">#define <a href="#xmlIsPubidChar_ch">xmlIsPubidChar_ch</a>(c);
</pre><p>Automatically generated by genChRanges.py</p><div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>c</tt></i>:</span></td><td>char to validate</td></tr></tbody></table></div>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlChLRange">Structure </a>xmlChLRange</h3><pre class="programlisting">struct _xmlChLRange {
    unsigned int	low
    unsigned int	high
} xmlChLRange;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlChLRangePtr">Typedef </a>xmlChLRangePtr</h3><pre class="programlisting"><a href="libxml2-chvalid.html#xmlChLRange">xmlChLRange</a> * xmlChLRangePtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlChRangeGroup">Structure </a>xmlChRangeGroup</h3><pre class="programlisting">struct _xmlChRangeGroup {
    int	nbShortRange
    int	nbLongRange
    const <a href="libxml2-chvalid.html#xmlChSRange">xmlChSRange</a> *	shortRange	: points to an array of ranges
    const <a href="libxml2-chvalid.html#xmlChLRange">xmlChLRange</a> *	longRange
} xmlChRangeGroup;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlChRangeGroupPtr">Typedef </a>xmlChRangeGroupPtr</h3><pre class="programlisting"><a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> * xmlChRangeGroupPtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlChSRange">Structure </a>xmlChSRange</h3><pre class="programlisting">struct _xmlChSRange {
    unsigned short	low
    unsigned short	high
} xmlChSRange;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlChSRangePtr">Typedef </a>xmlChSRangePtr</h3><pre class="programlisting"><a href="libxml2-chvalid.html#xmlChSRange">xmlChSRange</a> * xmlChSRangePtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsBaseCharGroup">Variable </a>xmlIsBaseCharGroup</h3><pre class="programlisting">const <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> xmlIsBaseCharGroup;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsCharGroup">Variable </a>xmlIsCharGroup</h3><pre class="programlisting">const <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> xmlIsCharGroup;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsCombiningGroup">Variable </a>xmlIsCombiningGroup</h3><pre class="programlisting">const <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> xmlIsCombiningGroup;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsDigitGroup">Variable </a>xmlIsDigitGroup</h3><pre class="programlisting">const <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> xmlIsDigitGroup;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsExtenderGroup">Variable </a>xmlIsExtenderGroup</h3><pre class="programlisting">const <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> xmlIsExtenderGroup;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsIdeographicGroup">Variable </a>xmlIsIdeographicGroup</h3><pre class="programlisting">const <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> xmlIsIdeographicGroup;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsPubidChar_tab">Variable </a>xmlIsPubidChar_tab</h3><pre class="programlisting">const unsigned charxmlIsPubidChar_tab[256] xmlIsPubidChar_tab;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharInRange"/>xmlCharInRange ()</h3><pre class="programlisting">int	xmlCharInRange			(unsigned int val, <br/>					 const <a href="libxml2-chvalid.html#xmlChRangeGroup">xmlChRangeGroup</a> * rptr)<br/>
</pre><p>Does a binary search of the range table to determine if char is valid</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>val</tt></i>:</span></td><td>character to be validated</td></tr><tr><td><span class="term"><i><tt>rptr</tt></i>:</span></td><td>pointer to range to be used to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if character valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsBaseChar"/>xmlIsBaseChar ()</h3><pre class="programlisting">int	xmlIsBaseChar			(unsigned int ch)<br/>
</pre><p>This function is DEPRECATED. Use <a href="libxml2-chvalid.html#xmlIsBaseChar_ch">xmlIsBaseChar_ch</a> or <a href="libxml2-chvalid.html#xmlIsBaseCharQ">xmlIsBaseCharQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsBlank"/>xmlIsBlank ()</h3><pre class="programlisting">int	xmlIsBlank			(unsigned int ch)<br/>
</pre><p>This function is DEPRECATED. Use <a href="libxml2-chvalid.html#xmlIsBlank_ch">xmlIsBlank_ch</a> or <a href="libxml2-chvalid.html#xmlIsBlankQ">xmlIsBlankQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsChar"/>xmlIsChar ()</h3><pre class="programlisting">int	xmlIsChar			(unsigned int ch)<br/>
</pre><p>This function is DEPRECATED. Use <a href="libxml2-chvalid.html#xmlIsChar_ch">xmlIsChar_ch</a> or <a href="libxml2-chvalid.html#xmlIsCharQ">xmlIsCharQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsCombining"/>xmlIsCombining ()</h3><pre class="programlisting">int	xmlIsCombining			(unsigned int ch)<br/>
</pre><p>This function is DEPRECATED. Use <a href="libxml2-chvalid.html#xmlIsCombiningQ">xmlIsCombiningQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsDigit"/>xmlIsDigit ()</h3><pre class="programlisting">int	xmlIsDigit			(unsigned int ch)<br/>
</pre><p>This function is DEPRECATED. Use <a href="libxml2-chvalid.html#xmlIsDigit_ch">xmlIsDigit_ch</a> or <a href="libxml2-chvalid.html#xmlIsDigitQ">xmlIsDigitQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsExtender"/>xmlIsExtender ()</h3><pre class="programlisting">int	xmlIsExtender			(unsigned int ch)<br/>
</pre><p>This function is DEPRECATED. Use <a href="libxml2-chvalid.html#xmlIsExtender_ch">xmlIsExtender_ch</a> or <a href="libxml2-chvalid.html#xmlIsExtenderQ">xmlIsExtenderQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsIdeographic"/>xmlIsIdeographic ()</h3><pre class="programlisting">int	xmlIsIdeographic		(unsigned int ch)<br/>
</pre><p>This function is DEPRECATED. Use <a href="libxml2-chvalid.html#xmlIsIdeographicQ">xmlIsIdeographicQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIsPubidChar"/>xmlIsPubidChar ()</h3><pre class="programlisting">int	xmlIsPubidChar			(unsigned int ch)<br/>
</pre><p>This function is DEPRECATED. Use <a href="libxml2-chvalid.html#xmlIsPubidChar_ch">xmlIsPubidChar_ch</a> or <a href="libxml2-chvalid.html#xmlIsPubidCharQ">xmlIsPubidCharQ</a> instead</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ch</tt></i>:</span></td><td>character to validate</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>true if argument valid, false otherwise</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
