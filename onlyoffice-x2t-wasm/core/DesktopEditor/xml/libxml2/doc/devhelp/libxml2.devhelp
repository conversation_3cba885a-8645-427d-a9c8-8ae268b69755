<?xml version="1.0" encoding="UTF-8"?>
<book xmlns="http://www.devhelp.net/book" title="libxml2 Reference Manual" link="index.html" author="" name="libxml2">
  <chapters>
    <sub name="API" link="general.html">
      <sub name="DOCBparser" link="libxml2-DOCBparser.html"/>
      <sub name="HTMLparser" link="libxml2-HTMLparser.html"/>
      <sub name="HTMLtree" link="libxml2-HTMLtree.html"/>
      <sub name="SAX" link="libxml2-SAX.html"/>
      <sub name="SAX2" link="libxml2-SAX2.html"/>
      <sub name="c14n" link="libxml2-c14n.html"/>
      <sub name="catalog" link="libxml2-catalog.html"/>
      <sub name="chvalid" link="libxml2-chvalid.html"/>
      <sub name="debugXML" link="libxml2-debugXML.html"/>
      <sub name="dict" link="libxml2-dict.html"/>
      <sub name="encoding" link="libxml2-encoding.html"/>
      <sub name="entities" link="libxml2-entities.html"/>
      <sub name="globals" link="libxml2-globals.html"/>
      <sub name="hash" link="libxml2-hash.html"/>
      <sub name="list" link="libxml2-list.html"/>
      <sub name="nanoftp" link="libxml2-nanoftp.html"/>
      <sub name="nanohttp" link="libxml2-nanohttp.html"/>
      <sub name="parser" link="libxml2-parser.html"/>
      <sub name="parserInternals" link="libxml2-parserInternals.html"/>
      <sub name="pattern" link="libxml2-pattern.html"/>
      <sub name="relaxng" link="libxml2-relaxng.html"/>
      <sub name="schemasInternals" link="libxml2-schemasInternals.html"/>
      <sub name="schematron" link="libxml2-schematron.html"/>
      <sub name="threads" link="libxml2-threads.html"/>
      <sub name="tree" link="libxml2-tree.html"/>
      <sub name="uri" link="libxml2-uri.html"/>
      <sub name="valid" link="libxml2-valid.html"/>
      <sub name="xinclude" link="libxml2-xinclude.html"/>
      <sub name="xlink" link="libxml2-xlink.html"/>
      <sub name="xmlIO" link="libxml2-xmlIO.html"/>
      <sub name="xmlautomata" link="libxml2-xmlautomata.html"/>
      <sub name="xmlerror" link="libxml2-xmlerror.html"/>
      <sub name="xmlexports" link="libxml2-xmlexports.html"/>
      <sub name="xmlmemory" link="libxml2-xmlmemory.html"/>
      <sub name="xmlmodule" link="libxml2-xmlmodule.html"/>
      <sub name="xmlreader" link="libxml2-xmlreader.html"/>
      <sub name="xmlregexp" link="libxml2-xmlregexp.html"/>
      <sub name="xmlsave" link="libxml2-xmlsave.html"/>
      <sub name="xmlschemas" link="libxml2-xmlschemas.html"/>
      <sub name="xmlschemastypes" link="libxml2-xmlschemastypes.html"/>
      <sub name="xmlstring" link="libxml2-xmlstring.html"/>
      <sub name="xmlunicode" link="libxml2-xmlunicode.html"/>
      <sub name="xmlversion" link="libxml2-xmlversion.html"/>
      <sub name="xmlwriter" link="libxml2-xmlwriter.html"/>
      <sub name="xpath" link="libxml2-xpath.html"/>
      <sub name="xpathInternals" link="libxml2-xpathInternals.html"/>
      <sub name="xpointer" link="libxml2-xpointer.html"/>
    </sub>
  </chapters>
  <functions>
    <function name="ATTRIBUTE_UNUSED" link="libxml2-xmlversion.html#ATTRIBUTE_UNUSED"/>
    <function name="BAD_CAST" link="libxml2-xmlstring.html#BAD_CAST"/>
    <function name="BASE_BUFFER_SIZE" link="libxml2-tree.html#BASE_BUFFER_SIZE"/>
    <function name="CAST_TO_BOOLEAN" link="libxml2-xpathInternals.html#CAST_TO_BOOLEAN"/>
    <function name="CAST_TO_NUMBER" link="libxml2-xpathInternals.html#CAST_TO_NUMBER"/>
    <function name="CAST_TO_STRING" link="libxml2-xpathInternals.html#CAST_TO_STRING"/>
    <function name="CHECK_ARITY" link="libxml2-xpathInternals.html#CHECK_ARITY"/>
    <function name="CHECK_ERROR" link="libxml2-xpathInternals.html#CHECK_ERROR"/>
    <function name="CHECK_ERROR0" link="libxml2-xpathInternals.html#CHECK_ERROR0"/>
    <function name="CHECK_TYPE" link="libxml2-xpathInternals.html#CHECK_TYPE"/>
    <function name="CHECK_TYPE0" link="libxml2-xpathInternals.html#CHECK_TYPE0"/>
    <function name="DEBUG_MEMORY" link="libxml2-xmlmemory.html#DEBUG_MEMORY"/>
    <function name="DEBUG_MEMORY_LOCATION" link="libxml2-xmlversion.html#DEBUG_MEMORY_LOCATION"/>
    <function name="HTML_COMMENT_NODE" link="libxml2-HTMLtree.html#HTML_COMMENT_NODE"/>
    <function name="HTML_ENTITY_REF_NODE" link="libxml2-HTMLtree.html#HTML_ENTITY_REF_NODE"/>
    <function name="HTML_PI_NODE" link="libxml2-HTMLtree.html#HTML_PI_NODE"/>
    <function name="HTML_PRESERVE_NODE" link="libxml2-HTMLtree.html#HTML_PRESERVE_NODE"/>
    <function name="HTML_TEXT_NODE" link="libxml2-HTMLtree.html#HTML_TEXT_NODE"/>
    <function name="INPUT_CHUNK" link="libxml2-parserInternals.html#INPUT_CHUNK"/>
    <function name="INVALID_SOCKET" link="libxml2-nanoftp.html#INVALID_SOCKET"/>
    <function name="IS_ASCII_DIGIT" link="libxml2-parserInternals.html#IS_ASCII_DIGIT"/>
    <function name="IS_ASCII_LETTER" link="libxml2-parserInternals.html#IS_ASCII_LETTER"/>
    <function name="IS_BASECHAR" link="libxml2-parserInternals.html#IS_BASECHAR"/>
    <function name="IS_BLANK" link="libxml2-parserInternals.html#IS_BLANK"/>
    <function name="IS_BLANK_CH" link="libxml2-parserInternals.html#IS_BLANK_CH"/>
    <function name="IS_BYTE_CHAR" link="libxml2-parserInternals.html#IS_BYTE_CHAR"/>
    <function name="IS_CHAR" link="libxml2-parserInternals.html#IS_CHAR"/>
    <function name="IS_CHAR_CH" link="libxml2-parserInternals.html#IS_CHAR_CH"/>
    <function name="IS_COMBINING" link="libxml2-parserInternals.html#IS_COMBINING"/>
    <function name="IS_COMBINING_CH" link="libxml2-parserInternals.html#IS_COMBINING_CH"/>
    <function name="IS_DIGIT" link="libxml2-parserInternals.html#IS_DIGIT"/>
    <function name="IS_DIGIT_CH" link="libxml2-parserInternals.html#IS_DIGIT_CH"/>
    <function name="IS_EXTENDER" link="libxml2-parserInternals.html#IS_EXTENDER"/>
    <function name="IS_EXTENDER_CH" link="libxml2-parserInternals.html#IS_EXTENDER_CH"/>
    <function name="IS_IDEOGRAPHIC" link="libxml2-parserInternals.html#IS_IDEOGRAPHIC"/>
    <function name="IS_LETTER" link="libxml2-parserInternals.html#IS_LETTER"/>
    <function name="IS_LETTER_CH" link="libxml2-parserInternals.html#IS_LETTER_CH"/>
    <function name="IS_PUBIDCHAR" link="libxml2-parserInternals.html#IS_PUBIDCHAR"/>
    <function name="IS_PUBIDCHAR_CH" link="libxml2-parserInternals.html#IS_PUBIDCHAR_CH"/>
    <function name="LIBXML2_NEW_BUFFER" link="libxml2-tree.html#LIBXML2_NEW_BUFFER"/>
    <function name="LIBXML_ATTR_ALLOC_SIZE" link="libxml2-xmlversion.html#LIBXML_ATTR_ALLOC_SIZE"/>
    <function name="LIBXML_ATTR_FORMAT" link="libxml2-xmlversion.html#LIBXML_ATTR_FORMAT"/>
    <function name="LIBXML_AUTOMATA_ENABLED" link="libxml2-xmlversion.html#LIBXML_AUTOMATA_ENABLED"/>
    <function name="LIBXML_C14N_ENABLED" link="libxml2-xmlversion.html#LIBXML_C14N_ENABLED"/>
    <function name="LIBXML_CATALOG_ENABLED" link="libxml2-xmlversion.html#LIBXML_CATALOG_ENABLED"/>
    <function name="LIBXML_DEBUG_ENABLED" link="libxml2-xmlversion.html#LIBXML_DEBUG_ENABLED"/>
    <function name="LIBXML_DEBUG_RUNTIME" link="libxml2-xmlversion.html#LIBXML_DEBUG_RUNTIME"/>
    <function name="LIBXML_DLL_IMPORT" link="libxml2-xmlexports.html#LIBXML_DLL_IMPORT"/>
    <function name="LIBXML_DOCB_ENABLED" link="libxml2-xmlversion.html#LIBXML_DOCB_ENABLED"/>
    <function name="LIBXML_DOTTED_VERSION" link="libxml2-xmlversion.html#LIBXML_DOTTED_VERSION"/>
    <function name="LIBXML_EXPR_ENABLED" link="libxml2-xmlversion.html#LIBXML_EXPR_ENABLED"/>
    <function name="LIBXML_FTP_ENABLED" link="libxml2-xmlversion.html#LIBXML_FTP_ENABLED"/>
    <function name="LIBXML_HTML_ENABLED" link="libxml2-xmlversion.html#LIBXML_HTML_ENABLED"/>
    <function name="LIBXML_HTTP_ENABLED" link="libxml2-xmlversion.html#LIBXML_HTTP_ENABLED"/>
    <function name="LIBXML_ICONV_ENABLED" link="libxml2-xmlversion.html#LIBXML_ICONV_ENABLED"/>
    <function name="LIBXML_ICU_ENABLED" link="libxml2-xmlversion.html#LIBXML_ICU_ENABLED"/>
    <function name="LIBXML_ISO8859X_ENABLED" link="libxml2-xmlversion.html#LIBXML_ISO8859X_ENABLED"/>
    <function name="LIBXML_LEGACY_ENABLED" link="libxml2-xmlversion.html#LIBXML_LEGACY_ENABLED"/>
    <function name="LIBXML_LZMA_ENABLED" link="libxml2-xmlversion.html#LIBXML_LZMA_ENABLED"/>
    <function name="LIBXML_MODULES_ENABLED" link="libxml2-xmlversion.html#LIBXML_MODULES_ENABLED"/>
    <function name="LIBXML_MODULE_EXTENSION" link="libxml2-xmlversion.html#LIBXML_MODULE_EXTENSION"/>
    <function name="LIBXML_OUTPUT_ENABLED" link="libxml2-xmlversion.html#LIBXML_OUTPUT_ENABLED"/>
    <function name="LIBXML_PATTERN_ENABLED" link="libxml2-xmlversion.html#LIBXML_PATTERN_ENABLED"/>
    <function name="LIBXML_PUSH_ENABLED" link="libxml2-xmlversion.html#LIBXML_PUSH_ENABLED"/>
    <function name="LIBXML_READER_ENABLED" link="libxml2-xmlversion.html#LIBXML_READER_ENABLED"/>
    <function name="LIBXML_REGEXP_ENABLED" link="libxml2-xmlversion.html#LIBXML_REGEXP_ENABLED"/>
    <function name="LIBXML_SAX1_ENABLED" link="libxml2-xmlversion.html#LIBXML_SAX1_ENABLED"/>
    <function name="LIBXML_SCHEMAS_ENABLED" link="libxml2-xmlversion.html#LIBXML_SCHEMAS_ENABLED"/>
    <function name="LIBXML_SCHEMATRON_ENABLED" link="libxml2-xmlversion.html#LIBXML_SCHEMATRON_ENABLED"/>
    <function name="LIBXML_TEST_VERSION" link="libxml2-xmlversion.html#LIBXML_TEST_VERSION"/>
    <function name="LIBXML_THREAD_ALLOC_ENABLED" link="libxml2-xmlversion.html#LIBXML_THREAD_ALLOC_ENABLED"/>
    <function name="LIBXML_THREAD_ENABLED" link="libxml2-xmlversion.html#LIBXML_THREAD_ENABLED"/>
    <function name="LIBXML_TREE_ENABLED" link="libxml2-xmlversion.html#LIBXML_TREE_ENABLED"/>
    <function name="LIBXML_UNICODE_ENABLED" link="libxml2-xmlversion.html#LIBXML_UNICODE_ENABLED"/>
    <function name="LIBXML_VALID_ENABLED" link="libxml2-xmlversion.html#LIBXML_VALID_ENABLED"/>
    <function name="LIBXML_VERSION" link="libxml2-xmlversion.html#LIBXML_VERSION"/>
    <function name="LIBXML_VERSION_EXTRA" link="libxml2-xmlversion.html#LIBXML_VERSION_EXTRA"/>
    <function name="LIBXML_VERSION_STRING" link="libxml2-xmlversion.html#LIBXML_VERSION_STRING"/>
    <function name="LIBXML_WRITER_ENABLED" link="libxml2-xmlversion.html#LIBXML_WRITER_ENABLED"/>
    <function name="LIBXML_XINCLUDE_ENABLED" link="libxml2-xmlversion.html#LIBXML_XINCLUDE_ENABLED"/>
    <function name="LIBXML_XPATH_ENABLED" link="libxml2-xmlversion.html#LIBXML_XPATH_ENABLED"/>
    <function name="LIBXML_XPTR_ENABLED" link="libxml2-xmlversion.html#LIBXML_XPTR_ENABLED"/>
    <function name="LIBXML_ZLIB_ENABLED" link="libxml2-xmlversion.html#LIBXML_ZLIB_ENABLED"/>
    <function name="MOVETO_ENDTAG" link="libxml2-parserInternals.html#MOVETO_ENDTAG"/>
    <function name="MOVETO_STARTTAG" link="libxml2-parserInternals.html#MOVETO_STARTTAG"/>
    <function name="SKIP_EOL" link="libxml2-parserInternals.html#SKIP_EOL"/>
    <function name="SOCKET" link="libxml2-nanoftp.html#SOCKET"/>
    <function name="WITHOUT_TRIO" link="libxml2-xmlversion.html#WITHOUT_TRIO"/>
    <function name="WITH_TRIO" link="libxml2-xmlversion.html#WITH_TRIO"/>
    <function name="XINCLUDE_FALLBACK" link="libxml2-xinclude.html#XINCLUDE_FALLBACK"/>
    <function name="XINCLUDE_HREF" link="libxml2-xinclude.html#XINCLUDE_HREF"/>
    <function name="XINCLUDE_NODE" link="libxml2-xinclude.html#XINCLUDE_NODE"/>
    <function name="XINCLUDE_NS" link="libxml2-xinclude.html#XINCLUDE_NS"/>
    <function name="XINCLUDE_OLD_NS" link="libxml2-xinclude.html#XINCLUDE_OLD_NS"/>
    <function name="XINCLUDE_PARSE" link="libxml2-xinclude.html#XINCLUDE_PARSE"/>
    <function name="XINCLUDE_PARSE_ENCODING" link="libxml2-xinclude.html#XINCLUDE_PARSE_ENCODING"/>
    <function name="XINCLUDE_PARSE_TEXT" link="libxml2-xinclude.html#XINCLUDE_PARSE_TEXT"/>
    <function name="XINCLUDE_PARSE_XML" link="libxml2-xinclude.html#XINCLUDE_PARSE_XML"/>
    <function name="XINCLUDE_PARSE_XPOINTER" link="libxml2-xinclude.html#XINCLUDE_PARSE_XPOINTER"/>
    <function name="XMLCALL" link="libxml2-xmlexports.html#XMLCALL"/>
    <function name="XMLCDECL" link="libxml2-xmlexports.html#XMLCDECL"/>
    <function name="XMLPUBFUN" link="libxml2-xmlexports.html#XMLPUBFUN"/>
    <function name="XMLPUBVAR" link="libxml2-xmlexports.html#XMLPUBVAR"/>
    <function name="XML_CAST_FPTR" link="libxml2-hash.html#XML_CAST_FPTR"/>
    <function name="XML_CATALOGS_NAMESPACE" link="libxml2-catalog.html#XML_CATALOGS_NAMESPACE"/>
    <function name="XML_CATALOG_PI" link="libxml2-catalog.html#XML_CATALOG_PI"/>
    <function name="XML_COMPLETE_ATTRS" link="libxml2-parser.html#XML_COMPLETE_ATTRS"/>
    <function name="XML_CTXT_FINISH_DTD_0" link="libxml2-valid.html#XML_CTXT_FINISH_DTD_0"/>
    <function name="XML_CTXT_FINISH_DTD_1" link="libxml2-valid.html#XML_CTXT_FINISH_DTD_1"/>
    <function name="XML_DEFAULT_VERSION" link="libxml2-parser.html#XML_DEFAULT_VERSION"/>
    <function name="XML_DETECT_IDS" link="libxml2-parser.html#XML_DETECT_IDS"/>
    <function name="XML_GET_CONTENT" link="libxml2-tree.html#XML_GET_CONTENT"/>
    <function name="XML_GET_LINE" link="libxml2-tree.html#XML_GET_LINE"/>
    <function name="XML_LOCAL_NAMESPACE" link="libxml2-tree.html#XML_LOCAL_NAMESPACE"/>
    <function name="XML_MAX_DICTIONARY_LIMIT" link="libxml2-parserInternals.html#XML_MAX_DICTIONARY_LIMIT"/>
    <function name="XML_MAX_LOOKUP_LIMIT" link="libxml2-parserInternals.html#XML_MAX_LOOKUP_LIMIT"/>
    <function name="XML_MAX_NAMELEN" link="libxml2-parserInternals.html#XML_MAX_NAMELEN"/>
    <function name="XML_MAX_NAME_LENGTH" link="libxml2-parserInternals.html#XML_MAX_NAME_LENGTH"/>
    <function name="XML_MAX_TEXT_LENGTH" link="libxml2-parserInternals.html#XML_MAX_TEXT_LENGTH"/>
    <function name="XML_SAX2_MAGIC" link="libxml2-parser.html#XML_SAX2_MAGIC"/>
    <function name="XML_SCHEMAS_ANYATTR_LAX" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANYATTR_LAX"/>
    <function name="XML_SCHEMAS_ANYATTR_SKIP" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANYATTR_SKIP"/>
    <function name="XML_SCHEMAS_ANYATTR_STRICT" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANYATTR_STRICT"/>
    <function name="XML_SCHEMAS_ANY_LAX" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANY_LAX"/>
    <function name="XML_SCHEMAS_ANY_SKIP" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANY_SKIP"/>
    <function name="XML_SCHEMAS_ANY_STRICT" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANY_STRICT"/>
    <function name="XML_SCHEMAS_ATTRGROUP_GLOBAL" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTRGROUP_GLOBAL"/>
    <function name="XML_SCHEMAS_ATTRGROUP_HAS_REFS" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTRGROUP_HAS_REFS"/>
    <function name="XML_SCHEMAS_ATTRGROUP_MARKED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTRGROUP_MARKED"/>
    <function name="XML_SCHEMAS_ATTRGROUP_REDEFINED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTRGROUP_REDEFINED"/>
    <function name="XML_SCHEMAS_ATTRGROUP_WILDCARD_BUILDED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTRGROUP_WILDCARD_BUILDED"/>
    <function name="XML_SCHEMAS_ATTR_FIXED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTR_FIXED"/>
    <function name="XML_SCHEMAS_ATTR_GLOBAL" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTR_GLOBAL"/>
    <function name="XML_SCHEMAS_ATTR_INTERNAL_RESOLVED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTR_INTERNAL_RESOLVED"/>
    <function name="XML_SCHEMAS_ATTR_NSDEFAULT" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTR_NSDEFAULT"/>
    <function name="XML_SCHEMAS_ATTR_USE_OPTIONAL" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTR_USE_OPTIONAL"/>
    <function name="XML_SCHEMAS_ATTR_USE_PROHIBITED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTR_USE_PROHIBITED"/>
    <function name="XML_SCHEMAS_ATTR_USE_REQUIRED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ATTR_USE_REQUIRED"/>
    <function name="XML_SCHEMAS_BLOCK_DEFAULT_EXTENSION" link="libxml2-schemasInternals.html#XML_SCHEMAS_BLOCK_DEFAULT_EXTENSION"/>
    <function name="XML_SCHEMAS_BLOCK_DEFAULT_RESTRICTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_BLOCK_DEFAULT_RESTRICTION"/>
    <function name="XML_SCHEMAS_BLOCK_DEFAULT_SUBSTITUTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_BLOCK_DEFAULT_SUBSTITUTION"/>
    <function name="XML_SCHEMAS_ELEM_ABSTRACT" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_ABSTRACT"/>
    <function name="XML_SCHEMAS_ELEM_BLOCK_ABSENT" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_BLOCK_ABSENT"/>
    <function name="XML_SCHEMAS_ELEM_BLOCK_EXTENSION" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_BLOCK_EXTENSION"/>
    <function name="XML_SCHEMAS_ELEM_BLOCK_RESTRICTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_BLOCK_RESTRICTION"/>
    <function name="XML_SCHEMAS_ELEM_BLOCK_SUBSTITUTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_BLOCK_SUBSTITUTION"/>
    <function name="XML_SCHEMAS_ELEM_CIRCULAR" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_CIRCULAR"/>
    <function name="XML_SCHEMAS_ELEM_DEFAULT" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_DEFAULT"/>
    <function name="XML_SCHEMAS_ELEM_FINAL_ABSENT" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_FINAL_ABSENT"/>
    <function name="XML_SCHEMAS_ELEM_FINAL_EXTENSION" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_FINAL_EXTENSION"/>
    <function name="XML_SCHEMAS_ELEM_FINAL_RESTRICTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_FINAL_RESTRICTION"/>
    <function name="XML_SCHEMAS_ELEM_FIXED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_FIXED"/>
    <function name="XML_SCHEMAS_ELEM_GLOBAL" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_GLOBAL"/>
    <function name="XML_SCHEMAS_ELEM_INTERNAL_CHECKED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_INTERNAL_CHECKED"/>
    <function name="XML_SCHEMAS_ELEM_INTERNAL_RESOLVED" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_INTERNAL_RESOLVED"/>
    <function name="XML_SCHEMAS_ELEM_NILLABLE" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_NILLABLE"/>
    <function name="XML_SCHEMAS_ELEM_NSDEFAULT" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_NSDEFAULT"/>
    <function name="XML_SCHEMAS_ELEM_REF" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_REF"/>
    <function name="XML_SCHEMAS_ELEM_SUBST_GROUP_HEAD" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_SUBST_GROUP_HEAD"/>
    <function name="XML_SCHEMAS_ELEM_TOPLEVEL" link="libxml2-schemasInternals.html#XML_SCHEMAS_ELEM_TOPLEVEL"/>
    <function name="XML_SCHEMAS_FACET_COLLAPSE" link="libxml2-schemasInternals.html#XML_SCHEMAS_FACET_COLLAPSE"/>
    <function name="XML_SCHEMAS_FACET_PRESERVE" link="libxml2-schemasInternals.html#XML_SCHEMAS_FACET_PRESERVE"/>
    <function name="XML_SCHEMAS_FACET_REPLACE" link="libxml2-schemasInternals.html#XML_SCHEMAS_FACET_REPLACE"/>
    <function name="XML_SCHEMAS_FACET_UNKNOWN" link="libxml2-schemasInternals.html#XML_SCHEMAS_FACET_UNKNOWN"/>
    <function name="XML_SCHEMAS_FINAL_DEFAULT_EXTENSION" link="libxml2-schemasInternals.html#XML_SCHEMAS_FINAL_DEFAULT_EXTENSION"/>
    <function name="XML_SCHEMAS_FINAL_DEFAULT_LIST" link="libxml2-schemasInternals.html#XML_SCHEMAS_FINAL_DEFAULT_LIST"/>
    <function name="XML_SCHEMAS_FINAL_DEFAULT_RESTRICTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_FINAL_DEFAULT_RESTRICTION"/>
    <function name="XML_SCHEMAS_FINAL_DEFAULT_UNION" link="libxml2-schemasInternals.html#XML_SCHEMAS_FINAL_DEFAULT_UNION"/>
    <function name="XML_SCHEMAS_INCLUDING_CONVERT_NS" link="libxml2-schemasInternals.html#XML_SCHEMAS_INCLUDING_CONVERT_NS"/>
    <function name="XML_SCHEMAS_QUALIF_ATTR" link="libxml2-schemasInternals.html#XML_SCHEMAS_QUALIF_ATTR"/>
    <function name="XML_SCHEMAS_QUALIF_ELEM" link="libxml2-schemasInternals.html#XML_SCHEMAS_QUALIF_ELEM"/>
    <function name="XML_SCHEMAS_TYPE_ABSTRACT" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_ABSTRACT"/>
    <function name="XML_SCHEMAS_TYPE_BLOCK_DEFAULT" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_BLOCK_DEFAULT"/>
    <function name="XML_SCHEMAS_TYPE_BLOCK_EXTENSION" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_BLOCK_EXTENSION"/>
    <function name="XML_SCHEMAS_TYPE_BLOCK_RESTRICTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_BLOCK_RESTRICTION"/>
    <function name="XML_SCHEMAS_TYPE_BUILTIN_PRIMITIVE" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_BUILTIN_PRIMITIVE"/>
    <function name="XML_SCHEMAS_TYPE_DERIVATION_METHOD_EXTENSION" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_DERIVATION_METHOD_EXTENSION"/>
    <function name="XML_SCHEMAS_TYPE_DERIVATION_METHOD_RESTRICTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_DERIVATION_METHOD_RESTRICTION"/>
    <function name="XML_SCHEMAS_TYPE_FACETSNEEDVALUE" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_FACETSNEEDVALUE"/>
    <function name="XML_SCHEMAS_TYPE_FINAL_DEFAULT" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_FINAL_DEFAULT"/>
    <function name="XML_SCHEMAS_TYPE_FINAL_EXTENSION" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_FINAL_EXTENSION"/>
    <function name="XML_SCHEMAS_TYPE_FINAL_LIST" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_FINAL_LIST"/>
    <function name="XML_SCHEMAS_TYPE_FINAL_RESTRICTION" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_FINAL_RESTRICTION"/>
    <function name="XML_SCHEMAS_TYPE_FINAL_UNION" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_FINAL_UNION"/>
    <function name="XML_SCHEMAS_TYPE_FIXUP_1" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_FIXUP_1"/>
    <function name="XML_SCHEMAS_TYPE_GLOBAL" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_GLOBAL"/>
    <function name="XML_SCHEMAS_TYPE_HAS_FACETS" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_HAS_FACETS"/>
    <function name="XML_SCHEMAS_TYPE_INTERNAL_INVALID" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_INTERNAL_INVALID"/>
    <function name="XML_SCHEMAS_TYPE_INTERNAL_RESOLVED" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_INTERNAL_RESOLVED"/>
    <function name="XML_SCHEMAS_TYPE_MARKED" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_MARKED"/>
    <function name="XML_SCHEMAS_TYPE_MIXED" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_MIXED"/>
    <function name="XML_SCHEMAS_TYPE_NORMVALUENEEDED" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_NORMVALUENEEDED"/>
    <function name="XML_SCHEMAS_TYPE_OWNED_ATTR_WILDCARD" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_OWNED_ATTR_WILDCARD"/>
    <function name="XML_SCHEMAS_TYPE_REDEFINED" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_REDEFINED"/>
    <function name="XML_SCHEMAS_TYPE_VARIETY_ABSENT" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_VARIETY_ABSENT"/>
    <function name="XML_SCHEMAS_TYPE_VARIETY_ATOMIC" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_VARIETY_ATOMIC"/>
    <function name="XML_SCHEMAS_TYPE_VARIETY_LIST" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_VARIETY_LIST"/>
    <function name="XML_SCHEMAS_TYPE_VARIETY_UNION" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_VARIETY_UNION"/>
    <function name="XML_SCHEMAS_TYPE_WHITESPACE_COLLAPSE" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_WHITESPACE_COLLAPSE"/>
    <function name="XML_SCHEMAS_TYPE_WHITESPACE_PRESERVE" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_WHITESPACE_PRESERVE"/>
    <function name="XML_SCHEMAS_TYPE_WHITESPACE_REPLACE" link="libxml2-schemasInternals.html#XML_SCHEMAS_TYPE_WHITESPACE_REPLACE"/>
    <function name="XML_SCHEMAS_WILDCARD_COMPLETE" link="libxml2-schemasInternals.html#XML_SCHEMAS_WILDCARD_COMPLETE"/>
    <function name="XML_SKIP_IDS" link="libxml2-parser.html#XML_SKIP_IDS"/>
    <function name="XML_SUBSTITUTE_BOTH" link="libxml2-parserInternals.html#XML_SUBSTITUTE_BOTH"/>
    <function name="XML_SUBSTITUTE_NONE" link="libxml2-parserInternals.html#XML_SUBSTITUTE_NONE"/>
    <function name="XML_SUBSTITUTE_PEREF" link="libxml2-parserInternals.html#XML_SUBSTITUTE_PEREF"/>
    <function name="XML_SUBSTITUTE_REF" link="libxml2-parserInternals.html#XML_SUBSTITUTE_REF"/>
    <function name="XML_XML_ID" link="libxml2-tree.html#XML_XML_ID"/>
    <function name="XML_XML_NAMESPACE" link="libxml2-tree.html#XML_XML_NAMESPACE"/>
    <function name="XML_XPATH_CHECKNS" link="libxml2-xpath.html#XML_XPATH_CHECKNS"/>
    <function name="XML_XPATH_NOVAR" link="libxml2-xpath.html#XML_XPATH_NOVAR"/>
    <function name="XP_ERROR" link="libxml2-xpathInternals.html#XP_ERROR"/>
    <function name="XP_ERROR0" link="libxml2-xpathInternals.html#XP_ERROR0"/>
    <function name="_REENTRANT" link="libxml2-xmlexports.html#_REENTRANT"/>
    <function name="htmlDefaultSubelement" link="libxml2-HTMLparser.html#htmlDefaultSubelement"/>
    <function name="htmlElementAllowedHereDesc" link="libxml2-HTMLparser.html#htmlElementAllowedHereDesc"/>
    <function name="htmlRequiredAttrs" link="libxml2-HTMLparser.html#htmlRequiredAttrs"/>
    <function name="xmlChildrenNode" link="libxml2-tree.html#xmlChildrenNode"/>
    <function name="xmlIsBaseCharQ" link="libxml2-chvalid.html#xmlIsBaseCharQ"/>
    <function name="xmlIsBaseChar_ch" link="libxml2-chvalid.html#xmlIsBaseChar_ch"/>
    <function name="xmlIsBlankQ" link="libxml2-chvalid.html#xmlIsBlankQ"/>
    <function name="xmlIsBlank_ch" link="libxml2-chvalid.html#xmlIsBlank_ch"/>
    <function name="xmlIsCharQ" link="libxml2-chvalid.html#xmlIsCharQ"/>
    <function name="xmlIsChar_ch" link="libxml2-chvalid.html#xmlIsChar_ch"/>
    <function name="xmlIsCombiningQ" link="libxml2-chvalid.html#xmlIsCombiningQ"/>
    <function name="xmlIsDigitQ" link="libxml2-chvalid.html#xmlIsDigitQ"/>
    <function name="xmlIsDigit_ch" link="libxml2-chvalid.html#xmlIsDigit_ch"/>
    <function name="xmlIsExtenderQ" link="libxml2-chvalid.html#xmlIsExtenderQ"/>
    <function name="xmlIsExtender_ch" link="libxml2-chvalid.html#xmlIsExtender_ch"/>
    <function name="xmlIsIdeographicQ" link="libxml2-chvalid.html#xmlIsIdeographicQ"/>
    <function name="xmlIsPubidCharQ" link="libxml2-chvalid.html#xmlIsPubidCharQ"/>
    <function name="xmlIsPubidChar_ch" link="libxml2-chvalid.html#xmlIsPubidChar_ch"/>
    <function name="xmlRootNode" link="libxml2-tree.html#xmlRootNode"/>
    <function name="xmlTextWriterWriteDocType" link="libxml2-xmlwriter.html#xmlTextWriterWriteDocType"/>
    <function name="xmlTextWriterWriteProcessingInstruction" link="libxml2-xmlwriter.html#xmlTextWriterWriteProcessingInstruction"/>
    <function name="xmlXPathCheckError" link="libxml2-xpathInternals.html#xmlXPathCheckError"/>
    <function name="xmlXPathEmptyNodeSet" link="libxml2-xpathInternals.html#xmlXPathEmptyNodeSet"/>
    <function name="xmlXPathGetContextNode" link="libxml2-xpathInternals.html#xmlXPathGetContextNode"/>
    <function name="xmlXPathGetDocument" link="libxml2-xpathInternals.html#xmlXPathGetDocument"/>
    <function name="xmlXPathGetError" link="libxml2-xpathInternals.html#xmlXPathGetError"/>
    <function name="xmlXPathNodeSetGetLength" link="libxml2-xpath.html#xmlXPathNodeSetGetLength"/>
    <function name="xmlXPathNodeSetIsEmpty" link="libxml2-xpath.html#xmlXPathNodeSetIsEmpty"/>
    <function name="xmlXPathNodeSetItem" link="libxml2-xpath.html#xmlXPathNodeSetItem"/>
    <function name="xmlXPathReturnBoolean" link="libxml2-xpathInternals.html#xmlXPathReturnBoolean"/>
    <function name="xmlXPathReturnEmptyNodeSet" link="libxml2-xpathInternals.html#xmlXPathReturnEmptyNodeSet"/>
    <function name="xmlXPathReturnEmptyString" link="libxml2-xpathInternals.html#xmlXPathReturnEmptyString"/>
    <function name="xmlXPathReturnExternal" link="libxml2-xpathInternals.html#xmlXPathReturnExternal"/>
    <function name="xmlXPathReturnFalse" link="libxml2-xpathInternals.html#xmlXPathReturnFalse"/>
    <function name="xmlXPathReturnNodeSet" link="libxml2-xpathInternals.html#xmlXPathReturnNodeSet"/>
    <function name="xmlXPathReturnNumber" link="libxml2-xpathInternals.html#xmlXPathReturnNumber"/>
    <function name="xmlXPathReturnString" link="libxml2-xpathInternals.html#xmlXPathReturnString"/>
    <function name="xmlXPathReturnTrue" link="libxml2-xpathInternals.html#xmlXPathReturnTrue"/>
    <function name="xmlXPathSetArityError" link="libxml2-xpathInternals.html#xmlXPathSetArityError"/>
    <function name="xmlXPathSetError" link="libxml2-xpathInternals.html#xmlXPathSetError"/>
    <function name="xmlXPathSetTypeError" link="libxml2-xpathInternals.html#xmlXPathSetTypeError"/>
    <function name="xmlXPathStackIsExternal" link="libxml2-xpathInternals.html#xmlXPathStackIsExternal"/>
    <function name="xmlXPathStackIsNodeSet" link="libxml2-xpathInternals.html#xmlXPathStackIsNodeSet"/>
    <function name="HTML_DEPRECATED" link="libxml2-HTMLparser.html#HTML_DEPRECATED"/>
    <function name="HTML_INVALID" link="libxml2-HTMLparser.html#HTML_INVALID"/>
    <function name="HTML_NA" link="libxml2-HTMLparser.html#HTML_NA"/>
    <function name="HTML_PARSE_COMPACT" link="libxml2-HTMLparser.html#HTML_PARSE_COMPACT"/>
    <function name="HTML_PARSE_IGNORE_ENC" link="libxml2-HTMLparser.html#HTML_PARSE_IGNORE_ENC"/>
    <function name="HTML_PARSE_NOBLANKS" link="libxml2-HTMLparser.html#HTML_PARSE_NOBLANKS"/>
    <function name="HTML_PARSE_NODEFDTD" link="libxml2-HTMLparser.html#HTML_PARSE_NODEFDTD"/>
    <function name="HTML_PARSE_NOERROR" link="libxml2-HTMLparser.html#HTML_PARSE_NOERROR"/>
    <function name="HTML_PARSE_NOIMPLIED" link="libxml2-HTMLparser.html#HTML_PARSE_NOIMPLIED"/>
    <function name="HTML_PARSE_NONET" link="libxml2-HTMLparser.html#HTML_PARSE_NONET"/>
    <function name="HTML_PARSE_NOWARNING" link="libxml2-HTMLparser.html#HTML_PARSE_NOWARNING"/>
    <function name="HTML_PARSE_PEDANTIC" link="libxml2-HTMLparser.html#HTML_PARSE_PEDANTIC"/>
    <function name="HTML_PARSE_RECOVER" link="libxml2-HTMLparser.html#HTML_PARSE_RECOVER"/>
    <function name="HTML_REQUIRED" link="libxml2-HTMLparser.html#HTML_REQUIRED"/>
    <function name="HTML_VALID" link="libxml2-HTMLparser.html#HTML_VALID"/>
    <function name="XLINK_ACTUATE_AUTO" link="libxml2-xlink.html#XLINK_ACTUATE_AUTO"/>
    <function name="XLINK_ACTUATE_NONE" link="libxml2-xlink.html#XLINK_ACTUATE_NONE"/>
    <function name="XLINK_ACTUATE_ONREQUEST" link="libxml2-xlink.html#XLINK_ACTUATE_ONREQUEST"/>
    <function name="XLINK_SHOW_EMBED" link="libxml2-xlink.html#XLINK_SHOW_EMBED"/>
    <function name="XLINK_SHOW_NEW" link="libxml2-xlink.html#XLINK_SHOW_NEW"/>
    <function name="XLINK_SHOW_NONE" link="libxml2-xlink.html#XLINK_SHOW_NONE"/>
    <function name="XLINK_SHOW_REPLACE" link="libxml2-xlink.html#XLINK_SHOW_REPLACE"/>
    <function name="XLINK_TYPE_EXTENDED" link="libxml2-xlink.html#XLINK_TYPE_EXTENDED"/>
    <function name="XLINK_TYPE_EXTENDED_SET" link="libxml2-xlink.html#XLINK_TYPE_EXTENDED_SET"/>
    <function name="XLINK_TYPE_NONE" link="libxml2-xlink.html#XLINK_TYPE_NONE"/>
    <function name="XLINK_TYPE_SIMPLE" link="libxml2-xlink.html#XLINK_TYPE_SIMPLE"/>
    <function name="XML_ATTRIBUTE_CDATA" link="libxml2-tree.html#XML_ATTRIBUTE_CDATA"/>
    <function name="XML_ATTRIBUTE_DECL" link="libxml2-tree.html#XML_ATTRIBUTE_DECL"/>
    <function name="XML_ATTRIBUTE_ENTITIES" link="libxml2-tree.html#XML_ATTRIBUTE_ENTITIES"/>
    <function name="XML_ATTRIBUTE_ENTITY" link="libxml2-tree.html#XML_ATTRIBUTE_ENTITY"/>
    <function name="XML_ATTRIBUTE_ENUMERATION" link="libxml2-tree.html#XML_ATTRIBUTE_ENUMERATION"/>
    <function name="XML_ATTRIBUTE_FIXED" link="libxml2-tree.html#XML_ATTRIBUTE_FIXED"/>
    <function name="XML_ATTRIBUTE_ID" link="libxml2-tree.html#XML_ATTRIBUTE_ID"/>
    <function name="XML_ATTRIBUTE_IDREF" link="libxml2-tree.html#XML_ATTRIBUTE_IDREF"/>
    <function name="XML_ATTRIBUTE_IDREFS" link="libxml2-tree.html#XML_ATTRIBUTE_IDREFS"/>
    <function name="XML_ATTRIBUTE_IMPLIED" link="libxml2-tree.html#XML_ATTRIBUTE_IMPLIED"/>
    <function name="XML_ATTRIBUTE_NMTOKEN" link="libxml2-tree.html#XML_ATTRIBUTE_NMTOKEN"/>
    <function name="XML_ATTRIBUTE_NMTOKENS" link="libxml2-tree.html#XML_ATTRIBUTE_NMTOKENS"/>
    <function name="XML_ATTRIBUTE_NODE" link="libxml2-tree.html#XML_ATTRIBUTE_NODE"/>
    <function name="XML_ATTRIBUTE_NONE" link="libxml2-tree.html#XML_ATTRIBUTE_NONE"/>
    <function name="XML_ATTRIBUTE_NOTATION" link="libxml2-tree.html#XML_ATTRIBUTE_NOTATION"/>
    <function name="XML_ATTRIBUTE_REQUIRED" link="libxml2-tree.html#XML_ATTRIBUTE_REQUIRED"/>
    <function name="XML_BUFFER_ALLOC_BOUNDED" link="libxml2-tree.html#XML_BUFFER_ALLOC_BOUNDED"/>
    <function name="XML_BUFFER_ALLOC_DOUBLEIT" link="libxml2-tree.html#XML_BUFFER_ALLOC_DOUBLEIT"/>
    <function name="XML_BUFFER_ALLOC_EXACT" link="libxml2-tree.html#XML_BUFFER_ALLOC_EXACT"/>
    <function name="XML_BUFFER_ALLOC_HYBRID" link="libxml2-tree.html#XML_BUFFER_ALLOC_HYBRID"/>
    <function name="XML_BUFFER_ALLOC_IMMUTABLE" link="libxml2-tree.html#XML_BUFFER_ALLOC_IMMUTABLE"/>
    <function name="XML_BUFFER_ALLOC_IO" link="libxml2-tree.html#XML_BUFFER_ALLOC_IO"/>
    <function name="XML_BUF_OVERFLOW" link="libxml2-xmlerror.html#XML_BUF_OVERFLOW"/>
    <function name="XML_C14N_1_0" link="libxml2-c14n.html#XML_C14N_1_0"/>
    <function name="XML_C14N_1_1" link="libxml2-c14n.html#XML_C14N_1_1"/>
    <function name="XML_C14N_CREATE_CTXT" link="libxml2-xmlerror.html#XML_C14N_CREATE_CTXT"/>
    <function name="XML_C14N_CREATE_STACK" link="libxml2-xmlerror.html#XML_C14N_CREATE_STACK"/>
    <function name="XML_C14N_EXCLUSIVE_1_0" link="libxml2-c14n.html#XML_C14N_EXCLUSIVE_1_0"/>
    <function name="XML_C14N_INVALID_NODE" link="libxml2-xmlerror.html#XML_C14N_INVALID_NODE"/>
    <function name="XML_C14N_RELATIVE_NAMESPACE" link="libxml2-xmlerror.html#XML_C14N_RELATIVE_NAMESPACE"/>
    <function name="XML_C14N_REQUIRES_UTF8" link="libxml2-xmlerror.html#XML_C14N_REQUIRES_UTF8"/>
    <function name="XML_C14N_UNKNOW_NODE" link="libxml2-xmlerror.html#XML_C14N_UNKNOW_NODE"/>
    <function name="XML_CATALOG_ENTRY_BROKEN" link="libxml2-xmlerror.html#XML_CATALOG_ENTRY_BROKEN"/>
    <function name="XML_CATALOG_MISSING_ATTR" link="libxml2-xmlerror.html#XML_CATALOG_MISSING_ATTR"/>
    <function name="XML_CATALOG_NOT_CATALOG" link="libxml2-xmlerror.html#XML_CATALOG_NOT_CATALOG"/>
    <function name="XML_CATALOG_PREFER_VALUE" link="libxml2-xmlerror.html#XML_CATALOG_PREFER_VALUE"/>
    <function name="XML_CATALOG_RECURSION" link="libxml2-xmlerror.html#XML_CATALOG_RECURSION"/>
    <function name="XML_CATA_ALLOW_ALL" link="libxml2-catalog.html#XML_CATA_ALLOW_ALL"/>
    <function name="XML_CATA_ALLOW_DOCUMENT" link="libxml2-catalog.html#XML_CATA_ALLOW_DOCUMENT"/>
    <function name="XML_CATA_ALLOW_GLOBAL" link="libxml2-catalog.html#XML_CATA_ALLOW_GLOBAL"/>
    <function name="XML_CATA_ALLOW_NONE" link="libxml2-catalog.html#XML_CATA_ALLOW_NONE"/>
    <function name="XML_CATA_PREFER_NONE" link="libxml2-catalog.html#XML_CATA_PREFER_NONE"/>
    <function name="XML_CATA_PREFER_PUBLIC" link="libxml2-catalog.html#XML_CATA_PREFER_PUBLIC"/>
    <function name="XML_CATA_PREFER_SYSTEM" link="libxml2-catalog.html#XML_CATA_PREFER_SYSTEM"/>
    <function name="XML_CDATA_SECTION_NODE" link="libxml2-tree.html#XML_CDATA_SECTION_NODE"/>
    <function name="XML_CHAR_ENCODING_2022_JP" link="libxml2-encoding.html#XML_CHAR_ENCODING_2022_JP"/>
    <function name="XML_CHAR_ENCODING_8859_1" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_1"/>
    <function name="XML_CHAR_ENCODING_8859_2" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_2"/>
    <function name="XML_CHAR_ENCODING_8859_3" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_3"/>
    <function name="XML_CHAR_ENCODING_8859_4" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_4"/>
    <function name="XML_CHAR_ENCODING_8859_5" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_5"/>
    <function name="XML_CHAR_ENCODING_8859_6" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_6"/>
    <function name="XML_CHAR_ENCODING_8859_7" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_7"/>
    <function name="XML_CHAR_ENCODING_8859_8" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_8"/>
    <function name="XML_CHAR_ENCODING_8859_9" link="libxml2-encoding.html#XML_CHAR_ENCODING_8859_9"/>
    <function name="XML_CHAR_ENCODING_ASCII" link="libxml2-encoding.html#XML_CHAR_ENCODING_ASCII"/>
    <function name="XML_CHAR_ENCODING_EBCDIC" link="libxml2-encoding.html#XML_CHAR_ENCODING_EBCDIC"/>
    <function name="XML_CHAR_ENCODING_ERROR" link="libxml2-encoding.html#XML_CHAR_ENCODING_ERROR"/>
    <function name="XML_CHAR_ENCODING_EUC_JP" link="libxml2-encoding.html#XML_CHAR_ENCODING_EUC_JP"/>
    <function name="XML_CHAR_ENCODING_NONE" link="libxml2-encoding.html#XML_CHAR_ENCODING_NONE"/>
    <function name="XML_CHAR_ENCODING_SHIFT_JIS" link="libxml2-encoding.html#XML_CHAR_ENCODING_SHIFT_JIS"/>
    <function name="XML_CHAR_ENCODING_UCS2" link="libxml2-encoding.html#XML_CHAR_ENCODING_UCS2"/>
    <function name="XML_CHAR_ENCODING_UCS4BE" link="libxml2-encoding.html#XML_CHAR_ENCODING_UCS4BE"/>
    <function name="XML_CHAR_ENCODING_UCS4LE" link="libxml2-encoding.html#XML_CHAR_ENCODING_UCS4LE"/>
    <function name="XML_CHAR_ENCODING_UCS4_2143" link="libxml2-encoding.html#XML_CHAR_ENCODING_UCS4_2143"/>
    <function name="XML_CHAR_ENCODING_UCS4_3412" link="libxml2-encoding.html#XML_CHAR_ENCODING_UCS4_3412"/>
    <function name="XML_CHAR_ENCODING_UTF16BE" link="libxml2-encoding.html#XML_CHAR_ENCODING_UTF16BE"/>
    <function name="XML_CHAR_ENCODING_UTF16LE" link="libxml2-encoding.html#XML_CHAR_ENCODING_UTF16LE"/>
    <function name="XML_CHAR_ENCODING_UTF8" link="libxml2-encoding.html#XML_CHAR_ENCODING_UTF8"/>
    <function name="XML_CHECK_ENTITY_TYPE" link="libxml2-xmlerror.html#XML_CHECK_ENTITY_TYPE"/>
    <function name="XML_CHECK_FOUND_ATTRIBUTE" link="libxml2-xmlerror.html#XML_CHECK_FOUND_ATTRIBUTE"/>
    <function name="XML_CHECK_FOUND_CDATA" link="libxml2-xmlerror.html#XML_CHECK_FOUND_CDATA"/>
    <function name="XML_CHECK_FOUND_COMMENT" link="libxml2-xmlerror.html#XML_CHECK_FOUND_COMMENT"/>
    <function name="XML_CHECK_FOUND_DOCTYPE" link="libxml2-xmlerror.html#XML_CHECK_FOUND_DOCTYPE"/>
    <function name="XML_CHECK_FOUND_ELEMENT" link="libxml2-xmlerror.html#XML_CHECK_FOUND_ELEMENT"/>
    <function name="XML_CHECK_FOUND_ENTITY" link="libxml2-xmlerror.html#XML_CHECK_FOUND_ENTITY"/>
    <function name="XML_CHECK_FOUND_ENTITYREF" link="libxml2-xmlerror.html#XML_CHECK_FOUND_ENTITYREF"/>
    <function name="XML_CHECK_FOUND_FRAGMENT" link="libxml2-xmlerror.html#XML_CHECK_FOUND_FRAGMENT"/>
    <function name="XML_CHECK_FOUND_NOTATION" link="libxml2-xmlerror.html#XML_CHECK_FOUND_NOTATION"/>
    <function name="XML_CHECK_FOUND_PI" link="libxml2-xmlerror.html#XML_CHECK_FOUND_PI"/>
    <function name="XML_CHECK_FOUND_TEXT" link="libxml2-xmlerror.html#XML_CHECK_FOUND_TEXT"/>
    <function name="XML_CHECK_NAME_NOT_NULL" link="libxml2-xmlerror.html#XML_CHECK_NAME_NOT_NULL"/>
    <function name="XML_CHECK_NOT_ATTR" link="libxml2-xmlerror.html#XML_CHECK_NOT_ATTR"/>
    <function name="XML_CHECK_NOT_ATTR_DECL" link="libxml2-xmlerror.html#XML_CHECK_NOT_ATTR_DECL"/>
    <function name="XML_CHECK_NOT_DTD" link="libxml2-xmlerror.html#XML_CHECK_NOT_DTD"/>
    <function name="XML_CHECK_NOT_ELEM_DECL" link="libxml2-xmlerror.html#XML_CHECK_NOT_ELEM_DECL"/>
    <function name="XML_CHECK_NOT_ENTITY_DECL" link="libxml2-xmlerror.html#XML_CHECK_NOT_ENTITY_DECL"/>
    <function name="XML_CHECK_NOT_NCNAME" link="libxml2-xmlerror.html#XML_CHECK_NOT_NCNAME"/>
    <function name="XML_CHECK_NOT_NS_DECL" link="libxml2-xmlerror.html#XML_CHECK_NOT_NS_DECL"/>
    <function name="XML_CHECK_NOT_UTF8" link="libxml2-xmlerror.html#XML_CHECK_NOT_UTF8"/>
    <function name="XML_CHECK_NO_DICT" link="libxml2-xmlerror.html#XML_CHECK_NO_DICT"/>
    <function name="XML_CHECK_NO_DOC" link="libxml2-xmlerror.html#XML_CHECK_NO_DOC"/>
    <function name="XML_CHECK_NO_ELEM" link="libxml2-xmlerror.html#XML_CHECK_NO_ELEM"/>
    <function name="XML_CHECK_NO_HREF" link="libxml2-xmlerror.html#XML_CHECK_NO_HREF"/>
    <function name="XML_CHECK_NO_NAME" link="libxml2-xmlerror.html#XML_CHECK_NO_NAME"/>
    <function name="XML_CHECK_NO_NEXT" link="libxml2-xmlerror.html#XML_CHECK_NO_NEXT"/>
    <function name="XML_CHECK_NO_PARENT" link="libxml2-xmlerror.html#XML_CHECK_NO_PARENT"/>
    <function name="XML_CHECK_NO_PREV" link="libxml2-xmlerror.html#XML_CHECK_NO_PREV"/>
    <function name="XML_CHECK_NS_ANCESTOR" link="libxml2-xmlerror.html#XML_CHECK_NS_ANCESTOR"/>
    <function name="XML_CHECK_NS_SCOPE" link="libxml2-xmlerror.html#XML_CHECK_NS_SCOPE"/>
    <function name="XML_CHECK_OUTSIDE_DICT" link="libxml2-xmlerror.html#XML_CHECK_OUTSIDE_DICT"/>
    <function name="XML_CHECK_UNKNOWN_NODE" link="libxml2-xmlerror.html#XML_CHECK_UNKNOWN_NODE"/>
    <function name="XML_CHECK_WRONG_DOC" link="libxml2-xmlerror.html#XML_CHECK_WRONG_DOC"/>
    <function name="XML_CHECK_WRONG_NAME" link="libxml2-xmlerror.html#XML_CHECK_WRONG_NAME"/>
    <function name="XML_CHECK_WRONG_NEXT" link="libxml2-xmlerror.html#XML_CHECK_WRONG_NEXT"/>
    <function name="XML_CHECK_WRONG_PARENT" link="libxml2-xmlerror.html#XML_CHECK_WRONG_PARENT"/>
    <function name="XML_CHECK_WRONG_PREV" link="libxml2-xmlerror.html#XML_CHECK_WRONG_PREV"/>
    <function name="XML_COMMENT_NODE" link="libxml2-tree.html#XML_COMMENT_NODE"/>
    <function name="XML_DOCB_DOCUMENT_NODE" link="libxml2-tree.html#XML_DOCB_DOCUMENT_NODE"/>
    <function name="XML_DOCUMENT_FRAG_NODE" link="libxml2-tree.html#XML_DOCUMENT_FRAG_NODE"/>
    <function name="XML_DOCUMENT_NODE" link="libxml2-tree.html#XML_DOCUMENT_NODE"/>
    <function name="XML_DOCUMENT_TYPE_NODE" link="libxml2-tree.html#XML_DOCUMENT_TYPE_NODE"/>
    <function name="XML_DOC_DTDVALID" link="libxml2-tree.html#XML_DOC_DTDVALID"/>
    <function name="XML_DOC_HTML" link="libxml2-tree.html#XML_DOC_HTML"/>
    <function name="XML_DOC_INTERNAL" link="libxml2-tree.html#XML_DOC_INTERNAL"/>
    <function name="XML_DOC_NSVALID" link="libxml2-tree.html#XML_DOC_NSVALID"/>
    <function name="XML_DOC_OLD10" link="libxml2-tree.html#XML_DOC_OLD10"/>
    <function name="XML_DOC_USERBUILT" link="libxml2-tree.html#XML_DOC_USERBUILT"/>
    <function name="XML_DOC_WELLFORMED" link="libxml2-tree.html#XML_DOC_WELLFORMED"/>
    <function name="XML_DOC_XINCLUDE" link="libxml2-tree.html#XML_DOC_XINCLUDE"/>
    <function name="XML_DTD_ATTRIBUTE_DEFAULT" link="libxml2-xmlerror.html#XML_DTD_ATTRIBUTE_DEFAULT"/>
    <function name="XML_DTD_ATTRIBUTE_REDEFINED" link="libxml2-xmlerror.html#XML_DTD_ATTRIBUTE_REDEFINED"/>
    <function name="XML_DTD_ATTRIBUTE_VALUE" link="libxml2-xmlerror.html#XML_DTD_ATTRIBUTE_VALUE"/>
    <function name="XML_DTD_CONTENT_ERROR" link="libxml2-xmlerror.html#XML_DTD_CONTENT_ERROR"/>
    <function name="XML_DTD_CONTENT_MODEL" link="libxml2-xmlerror.html#XML_DTD_CONTENT_MODEL"/>
    <function name="XML_DTD_CONTENT_NOT_DETERMINIST" link="libxml2-xmlerror.html#XML_DTD_CONTENT_NOT_DETERMINIST"/>
    <function name="XML_DTD_DIFFERENT_PREFIX" link="libxml2-xmlerror.html#XML_DTD_DIFFERENT_PREFIX"/>
    <function name="XML_DTD_DUP_TOKEN" link="libxml2-xmlerror.html#XML_DTD_DUP_TOKEN"/>
    <function name="XML_DTD_ELEM_DEFAULT_NAMESPACE" link="libxml2-xmlerror.html#XML_DTD_ELEM_DEFAULT_NAMESPACE"/>
    <function name="XML_DTD_ELEM_NAMESPACE" link="libxml2-xmlerror.html#XML_DTD_ELEM_NAMESPACE"/>
    <function name="XML_DTD_ELEM_REDEFINED" link="libxml2-xmlerror.html#XML_DTD_ELEM_REDEFINED"/>
    <function name="XML_DTD_EMPTY_NOTATION" link="libxml2-xmlerror.html#XML_DTD_EMPTY_NOTATION"/>
    <function name="XML_DTD_ENTITY_TYPE" link="libxml2-xmlerror.html#XML_DTD_ENTITY_TYPE"/>
    <function name="XML_DTD_ID_FIXED" link="libxml2-xmlerror.html#XML_DTD_ID_FIXED"/>
    <function name="XML_DTD_ID_REDEFINED" link="libxml2-xmlerror.html#XML_DTD_ID_REDEFINED"/>
    <function name="XML_DTD_ID_SUBSET" link="libxml2-xmlerror.html#XML_DTD_ID_SUBSET"/>
    <function name="XML_DTD_INVALID_CHILD" link="libxml2-xmlerror.html#XML_DTD_INVALID_CHILD"/>
    <function name="XML_DTD_INVALID_DEFAULT" link="libxml2-xmlerror.html#XML_DTD_INVALID_DEFAULT"/>
    <function name="XML_DTD_LOAD_ERROR" link="libxml2-xmlerror.html#XML_DTD_LOAD_ERROR"/>
    <function name="XML_DTD_MISSING_ATTRIBUTE" link="libxml2-xmlerror.html#XML_DTD_MISSING_ATTRIBUTE"/>
    <function name="XML_DTD_MIXED_CORRUPT" link="libxml2-xmlerror.html#XML_DTD_MIXED_CORRUPT"/>
    <function name="XML_DTD_MULTIPLE_ID" link="libxml2-xmlerror.html#XML_DTD_MULTIPLE_ID"/>
    <function name="XML_DTD_NODE" link="libxml2-tree.html#XML_DTD_NODE"/>
    <function name="XML_DTD_NOTATION_REDEFINED" link="libxml2-xmlerror.html#XML_DTD_NOTATION_REDEFINED"/>
    <function name="XML_DTD_NOTATION_VALUE" link="libxml2-xmlerror.html#XML_DTD_NOTATION_VALUE"/>
    <function name="XML_DTD_NOT_EMPTY" link="libxml2-xmlerror.html#XML_DTD_NOT_EMPTY"/>
    <function name="XML_DTD_NOT_PCDATA" link="libxml2-xmlerror.html#XML_DTD_NOT_PCDATA"/>
    <function name="XML_DTD_NOT_STANDALONE" link="libxml2-xmlerror.html#XML_DTD_NOT_STANDALONE"/>
    <function name="XML_DTD_NO_DOC" link="libxml2-xmlerror.html#XML_DTD_NO_DOC"/>
    <function name="XML_DTD_NO_DTD" link="libxml2-xmlerror.html#XML_DTD_NO_DTD"/>
    <function name="XML_DTD_NO_ELEM_NAME" link="libxml2-xmlerror.html#XML_DTD_NO_ELEM_NAME"/>
    <function name="XML_DTD_NO_PREFIX" link="libxml2-xmlerror.html#XML_DTD_NO_PREFIX"/>
    <function name="XML_DTD_NO_ROOT" link="libxml2-xmlerror.html#XML_DTD_NO_ROOT"/>
    <function name="XML_DTD_ROOT_NAME" link="libxml2-xmlerror.html#XML_DTD_ROOT_NAME"/>
    <function name="XML_DTD_STANDALONE_DEFAULTED" link="libxml2-xmlerror.html#XML_DTD_STANDALONE_DEFAULTED"/>
    <function name="XML_DTD_STANDALONE_WHITE_SPACE" link="libxml2-xmlerror.html#XML_DTD_STANDALONE_WHITE_SPACE"/>
    <function name="XML_DTD_UNKNOWN_ATTRIBUTE" link="libxml2-xmlerror.html#XML_DTD_UNKNOWN_ATTRIBUTE"/>
    <function name="XML_DTD_UNKNOWN_ELEM" link="libxml2-xmlerror.html#XML_DTD_UNKNOWN_ELEM"/>
    <function name="XML_DTD_UNKNOWN_ENTITY" link="libxml2-xmlerror.html#XML_DTD_UNKNOWN_ENTITY"/>
    <function name="XML_DTD_UNKNOWN_ID" link="libxml2-xmlerror.html#XML_DTD_UNKNOWN_ID"/>
    <function name="XML_DTD_UNKNOWN_NOTATION" link="libxml2-xmlerror.html#XML_DTD_UNKNOWN_NOTATION"/>
    <function name="XML_DTD_XMLID_TYPE" link="libxml2-xmlerror.html#XML_DTD_XMLID_TYPE"/>
    <function name="XML_DTD_XMLID_VALUE" link="libxml2-xmlerror.html#XML_DTD_XMLID_VALUE"/>
    <function name="XML_ELEMENT_CONTENT_ELEMENT" link="libxml2-tree.html#XML_ELEMENT_CONTENT_ELEMENT"/>
    <function name="XML_ELEMENT_CONTENT_MULT" link="libxml2-tree.html#XML_ELEMENT_CONTENT_MULT"/>
    <function name="XML_ELEMENT_CONTENT_ONCE" link="libxml2-tree.html#XML_ELEMENT_CONTENT_ONCE"/>
    <function name="XML_ELEMENT_CONTENT_OPT" link="libxml2-tree.html#XML_ELEMENT_CONTENT_OPT"/>
    <function name="XML_ELEMENT_CONTENT_OR" link="libxml2-tree.html#XML_ELEMENT_CONTENT_OR"/>
    <function name="XML_ELEMENT_CONTENT_PCDATA" link="libxml2-tree.html#XML_ELEMENT_CONTENT_PCDATA"/>
    <function name="XML_ELEMENT_CONTENT_PLUS" link="libxml2-tree.html#XML_ELEMENT_CONTENT_PLUS"/>
    <function name="XML_ELEMENT_CONTENT_SEQ" link="libxml2-tree.html#XML_ELEMENT_CONTENT_SEQ"/>
    <function name="XML_ELEMENT_DECL" link="libxml2-tree.html#XML_ELEMENT_DECL"/>
    <function name="XML_ELEMENT_NODE" link="libxml2-tree.html#XML_ELEMENT_NODE"/>
    <function name="XML_ELEMENT_TYPE_ANY" link="libxml2-tree.html#XML_ELEMENT_TYPE_ANY"/>
    <function name="XML_ELEMENT_TYPE_ELEMENT" link="libxml2-tree.html#XML_ELEMENT_TYPE_ELEMENT"/>
    <function name="XML_ELEMENT_TYPE_EMPTY" link="libxml2-tree.html#XML_ELEMENT_TYPE_EMPTY"/>
    <function name="XML_ELEMENT_TYPE_MIXED" link="libxml2-tree.html#XML_ELEMENT_TYPE_MIXED"/>
    <function name="XML_ELEMENT_TYPE_UNDEFINED" link="libxml2-tree.html#XML_ELEMENT_TYPE_UNDEFINED"/>
    <function name="XML_ENTITY_DECL" link="libxml2-tree.html#XML_ENTITY_DECL"/>
    <function name="XML_ENTITY_NODE" link="libxml2-tree.html#XML_ENTITY_NODE"/>
    <function name="XML_ENTITY_REF_NODE" link="libxml2-tree.html#XML_ENTITY_REF_NODE"/>
    <function name="XML_ERR_ATTLIST_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_ATTLIST_NOT_FINISHED"/>
    <function name="XML_ERR_ATTLIST_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_ATTLIST_NOT_STARTED"/>
    <function name="XML_ERR_ATTRIBUTE_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_ATTRIBUTE_NOT_FINISHED"/>
    <function name="XML_ERR_ATTRIBUTE_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_ATTRIBUTE_NOT_STARTED"/>
    <function name="XML_ERR_ATTRIBUTE_REDEFINED" link="libxml2-xmlerror.html#XML_ERR_ATTRIBUTE_REDEFINED"/>
    <function name="XML_ERR_ATTRIBUTE_WITHOUT_VALUE" link="libxml2-xmlerror.html#XML_ERR_ATTRIBUTE_WITHOUT_VALUE"/>
    <function name="XML_ERR_CDATA_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_CDATA_NOT_FINISHED"/>
    <function name="XML_ERR_CHARREF_AT_EOF" link="libxml2-xmlerror.html#XML_ERR_CHARREF_AT_EOF"/>
    <function name="XML_ERR_CHARREF_IN_DTD" link="libxml2-xmlerror.html#XML_ERR_CHARREF_IN_DTD"/>
    <function name="XML_ERR_CHARREF_IN_EPILOG" link="libxml2-xmlerror.html#XML_ERR_CHARREF_IN_EPILOG"/>
    <function name="XML_ERR_CHARREF_IN_PROLOG" link="libxml2-xmlerror.html#XML_ERR_CHARREF_IN_PROLOG"/>
    <function name="XML_ERR_COMMENT_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_COMMENT_NOT_FINISHED"/>
    <function name="XML_ERR_CONDSEC_INVALID" link="libxml2-xmlerror.html#XML_ERR_CONDSEC_INVALID"/>
    <function name="XML_ERR_CONDSEC_INVALID_KEYWORD" link="libxml2-xmlerror.html#XML_ERR_CONDSEC_INVALID_KEYWORD"/>
    <function name="XML_ERR_CONDSEC_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_CONDSEC_NOT_FINISHED"/>
    <function name="XML_ERR_CONDSEC_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_CONDSEC_NOT_STARTED"/>
    <function name="XML_ERR_DOCTYPE_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_DOCTYPE_NOT_FINISHED"/>
    <function name="XML_ERR_DOCUMENT_EMPTY" link="libxml2-xmlerror.html#XML_ERR_DOCUMENT_EMPTY"/>
    <function name="XML_ERR_DOCUMENT_END" link="libxml2-xmlerror.html#XML_ERR_DOCUMENT_END"/>
    <function name="XML_ERR_DOCUMENT_START" link="libxml2-xmlerror.html#XML_ERR_DOCUMENT_START"/>
    <function name="XML_ERR_ELEMCONTENT_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_ELEMCONTENT_NOT_FINISHED"/>
    <function name="XML_ERR_ELEMCONTENT_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_ELEMCONTENT_NOT_STARTED"/>
    <function name="XML_ERR_ENCODING_NAME" link="libxml2-xmlerror.html#XML_ERR_ENCODING_NAME"/>
    <function name="XML_ERR_ENTITYREF_AT_EOF" link="libxml2-xmlerror.html#XML_ERR_ENTITYREF_AT_EOF"/>
    <function name="XML_ERR_ENTITYREF_IN_DTD" link="libxml2-xmlerror.html#XML_ERR_ENTITYREF_IN_DTD"/>
    <function name="XML_ERR_ENTITYREF_IN_EPILOG" link="libxml2-xmlerror.html#XML_ERR_ENTITYREF_IN_EPILOG"/>
    <function name="XML_ERR_ENTITYREF_IN_PROLOG" link="libxml2-xmlerror.html#XML_ERR_ENTITYREF_IN_PROLOG"/>
    <function name="XML_ERR_ENTITYREF_NO_NAME" link="libxml2-xmlerror.html#XML_ERR_ENTITYREF_NO_NAME"/>
    <function name="XML_ERR_ENTITYREF_SEMICOL_MISSING" link="libxml2-xmlerror.html#XML_ERR_ENTITYREF_SEMICOL_MISSING"/>
    <function name="XML_ERR_ENTITY_BOUNDARY" link="libxml2-xmlerror.html#XML_ERR_ENTITY_BOUNDARY"/>
    <function name="XML_ERR_ENTITY_CHAR_ERROR" link="libxml2-xmlerror.html#XML_ERR_ENTITY_CHAR_ERROR"/>
    <function name="XML_ERR_ENTITY_IS_EXTERNAL" link="libxml2-xmlerror.html#XML_ERR_ENTITY_IS_EXTERNAL"/>
    <function name="XML_ERR_ENTITY_IS_PARAMETER" link="libxml2-xmlerror.html#XML_ERR_ENTITY_IS_PARAMETER"/>
    <function name="XML_ERR_ENTITY_LOOP" link="libxml2-xmlerror.html#XML_ERR_ENTITY_LOOP"/>
    <function name="XML_ERR_ENTITY_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_ENTITY_NOT_FINISHED"/>
    <function name="XML_ERR_ENTITY_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_ENTITY_NOT_STARTED"/>
    <function name="XML_ERR_ENTITY_PE_INTERNAL" link="libxml2-xmlerror.html#XML_ERR_ENTITY_PE_INTERNAL"/>
    <function name="XML_ERR_ENTITY_PROCESSING" link="libxml2-xmlerror.html#XML_ERR_ENTITY_PROCESSING"/>
    <function name="XML_ERR_EQUAL_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_EQUAL_REQUIRED"/>
    <function name="XML_ERR_ERROR" link="libxml2-xmlerror.html#XML_ERR_ERROR"/>
    <function name="XML_ERR_EXTRA_CONTENT" link="libxml2-xmlerror.html#XML_ERR_EXTRA_CONTENT"/>
    <function name="XML_ERR_EXT_ENTITY_STANDALONE" link="libxml2-xmlerror.html#XML_ERR_EXT_ENTITY_STANDALONE"/>
    <function name="XML_ERR_EXT_SUBSET_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_EXT_SUBSET_NOT_FINISHED"/>
    <function name="XML_ERR_FATAL" link="libxml2-xmlerror.html#XML_ERR_FATAL"/>
    <function name="XML_ERR_GT_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_GT_REQUIRED"/>
    <function name="XML_ERR_HYPHEN_IN_COMMENT" link="libxml2-xmlerror.html#XML_ERR_HYPHEN_IN_COMMENT"/>
    <function name="XML_ERR_INTERNAL_ERROR" link="libxml2-xmlerror.html#XML_ERR_INTERNAL_ERROR"/>
    <function name="XML_ERR_INVALID_CHAR" link="libxml2-xmlerror.html#XML_ERR_INVALID_CHAR"/>
    <function name="XML_ERR_INVALID_CHARREF" link="libxml2-xmlerror.html#XML_ERR_INVALID_CHARREF"/>
    <function name="XML_ERR_INVALID_DEC_CHARREF" link="libxml2-xmlerror.html#XML_ERR_INVALID_DEC_CHARREF"/>
    <function name="XML_ERR_INVALID_ENCODING" link="libxml2-xmlerror.html#XML_ERR_INVALID_ENCODING"/>
    <function name="XML_ERR_INVALID_HEX_CHARREF" link="libxml2-xmlerror.html#XML_ERR_INVALID_HEX_CHARREF"/>
    <function name="XML_ERR_INVALID_URI" link="libxml2-xmlerror.html#XML_ERR_INVALID_URI"/>
    <function name="XML_ERR_LITERAL_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_LITERAL_NOT_FINISHED"/>
    <function name="XML_ERR_LITERAL_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_LITERAL_NOT_STARTED"/>
    <function name="XML_ERR_LTSLASH_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_LTSLASH_REQUIRED"/>
    <function name="XML_ERR_LT_IN_ATTRIBUTE" link="libxml2-xmlerror.html#XML_ERR_LT_IN_ATTRIBUTE"/>
    <function name="XML_ERR_LT_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_LT_REQUIRED"/>
    <function name="XML_ERR_MISPLACED_CDATA_END" link="libxml2-xmlerror.html#XML_ERR_MISPLACED_CDATA_END"/>
    <function name="XML_ERR_MISSING_ENCODING" link="libxml2-xmlerror.html#XML_ERR_MISSING_ENCODING"/>
    <function name="XML_ERR_MIXED_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_MIXED_NOT_FINISHED"/>
    <function name="XML_ERR_MIXED_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_MIXED_NOT_STARTED"/>
    <function name="XML_ERR_NAME_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_NAME_REQUIRED"/>
    <function name="XML_ERR_NAME_TOO_LONG" link="libxml2-xmlerror.html#XML_ERR_NAME_TOO_LONG"/>
    <function name="XML_ERR_NMTOKEN_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_NMTOKEN_REQUIRED"/>
    <function name="XML_ERR_NONE" link="libxml2-xmlerror.html#XML_ERR_NONE"/>
    <function name="XML_ERR_NOTATION_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_NOTATION_NOT_FINISHED"/>
    <function name="XML_ERR_NOTATION_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_NOTATION_NOT_STARTED"/>
    <function name="XML_ERR_NOTATION_PROCESSING" link="libxml2-xmlerror.html#XML_ERR_NOTATION_PROCESSING"/>
    <function name="XML_ERR_NOT_STANDALONE" link="libxml2-xmlerror.html#XML_ERR_NOT_STANDALONE"/>
    <function name="XML_ERR_NOT_WELL_BALANCED" link="libxml2-xmlerror.html#XML_ERR_NOT_WELL_BALANCED"/>
    <function name="XML_ERR_NO_DTD" link="libxml2-xmlerror.html#XML_ERR_NO_DTD"/>
    <function name="XML_ERR_NO_MEMORY" link="libxml2-xmlerror.html#XML_ERR_NO_MEMORY"/>
    <function name="XML_ERR_NS_DECL_ERROR" link="libxml2-xmlerror.html#XML_ERR_NS_DECL_ERROR"/>
    <function name="XML_ERR_OK" link="libxml2-xmlerror.html#XML_ERR_OK"/>
    <function name="XML_ERR_PCDATA_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_PCDATA_REQUIRED"/>
    <function name="XML_ERR_PEREF_AT_EOF" link="libxml2-xmlerror.html#XML_ERR_PEREF_AT_EOF"/>
    <function name="XML_ERR_PEREF_IN_EPILOG" link="libxml2-xmlerror.html#XML_ERR_PEREF_IN_EPILOG"/>
    <function name="XML_ERR_PEREF_IN_INT_SUBSET" link="libxml2-xmlerror.html#XML_ERR_PEREF_IN_INT_SUBSET"/>
    <function name="XML_ERR_PEREF_IN_PROLOG" link="libxml2-xmlerror.html#XML_ERR_PEREF_IN_PROLOG"/>
    <function name="XML_ERR_PEREF_NO_NAME" link="libxml2-xmlerror.html#XML_ERR_PEREF_NO_NAME"/>
    <function name="XML_ERR_PEREF_SEMICOL_MISSING" link="libxml2-xmlerror.html#XML_ERR_PEREF_SEMICOL_MISSING"/>
    <function name="XML_ERR_PI_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_PI_NOT_FINISHED"/>
    <function name="XML_ERR_PI_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_PI_NOT_STARTED"/>
    <function name="XML_ERR_PUBID_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_PUBID_REQUIRED"/>
    <function name="XML_ERR_RESERVED_XML_NAME" link="libxml2-xmlerror.html#XML_ERR_RESERVED_XML_NAME"/>
    <function name="XML_ERR_SEPARATOR_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_SEPARATOR_REQUIRED"/>
    <function name="XML_ERR_SPACE_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_SPACE_REQUIRED"/>
    <function name="XML_ERR_STANDALONE_VALUE" link="libxml2-xmlerror.html#XML_ERR_STANDALONE_VALUE"/>
    <function name="XML_ERR_STRING_NOT_CLOSED" link="libxml2-xmlerror.html#XML_ERR_STRING_NOT_CLOSED"/>
    <function name="XML_ERR_STRING_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_STRING_NOT_STARTED"/>
    <function name="XML_ERR_TAG_NAME_MISMATCH" link="libxml2-xmlerror.html#XML_ERR_TAG_NAME_MISMATCH"/>
    <function name="XML_ERR_TAG_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_TAG_NOT_FINISHED"/>
    <function name="XML_ERR_UNDECLARED_ENTITY" link="libxml2-xmlerror.html#XML_ERR_UNDECLARED_ENTITY"/>
    <function name="XML_ERR_UNKNOWN_ENCODING" link="libxml2-xmlerror.html#XML_ERR_UNKNOWN_ENCODING"/>
    <function name="XML_ERR_UNKNOWN_VERSION" link="libxml2-xmlerror.html#XML_ERR_UNKNOWN_VERSION"/>
    <function name="XML_ERR_UNPARSED_ENTITY" link="libxml2-xmlerror.html#XML_ERR_UNPARSED_ENTITY"/>
    <function name="XML_ERR_UNSUPPORTED_ENCODING" link="libxml2-xmlerror.html#XML_ERR_UNSUPPORTED_ENCODING"/>
    <function name="XML_ERR_URI_FRAGMENT" link="libxml2-xmlerror.html#XML_ERR_URI_FRAGMENT"/>
    <function name="XML_ERR_URI_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_URI_REQUIRED"/>
    <function name="XML_ERR_USER_STOP" link="libxml2-xmlerror.html#XML_ERR_USER_STOP"/>
    <function name="XML_ERR_VALUE_REQUIRED" link="libxml2-xmlerror.html#XML_ERR_VALUE_REQUIRED"/>
    <function name="XML_ERR_VERSION_MISMATCH" link="libxml2-xmlerror.html#XML_ERR_VERSION_MISMATCH"/>
    <function name="XML_ERR_VERSION_MISSING" link="libxml2-xmlerror.html#XML_ERR_VERSION_MISSING"/>
    <function name="XML_ERR_WARNING" link="libxml2-xmlerror.html#XML_ERR_WARNING"/>
    <function name="XML_ERR_XMLDECL_NOT_FINISHED" link="libxml2-xmlerror.html#XML_ERR_XMLDECL_NOT_FINISHED"/>
    <function name="XML_ERR_XMLDECL_NOT_STARTED" link="libxml2-xmlerror.html#XML_ERR_XMLDECL_NOT_STARTED"/>
    <function name="XML_EXP_ATOM" link="libxml2-xmlregexp.html#XML_EXP_ATOM"/>
    <function name="XML_EXP_COUNT" link="libxml2-xmlregexp.html#XML_EXP_COUNT"/>
    <function name="XML_EXP_EMPTY" link="libxml2-xmlregexp.html#XML_EXP_EMPTY"/>
    <function name="XML_EXP_FORBID" link="libxml2-xmlregexp.html#XML_EXP_FORBID"/>
    <function name="XML_EXP_OR" link="libxml2-xmlregexp.html#XML_EXP_OR"/>
    <function name="XML_EXP_SEQ" link="libxml2-xmlregexp.html#XML_EXP_SEQ"/>
    <function name="XML_EXTERNAL_GENERAL_PARSED_ENTITY" link="libxml2-entities.html#XML_EXTERNAL_GENERAL_PARSED_ENTITY"/>
    <function name="XML_EXTERNAL_GENERAL_UNPARSED_ENTITY" link="libxml2-entities.html#XML_EXTERNAL_GENERAL_UNPARSED_ENTITY"/>
    <function name="XML_EXTERNAL_PARAMETER_ENTITY" link="libxml2-entities.html#XML_EXTERNAL_PARAMETER_ENTITY"/>
    <function name="XML_FROM_BUFFER" link="libxml2-xmlerror.html#XML_FROM_BUFFER"/>
    <function name="XML_FROM_C14N" link="libxml2-xmlerror.html#XML_FROM_C14N"/>
    <function name="XML_FROM_CATALOG" link="libxml2-xmlerror.html#XML_FROM_CATALOG"/>
    <function name="XML_FROM_CHECK" link="libxml2-xmlerror.html#XML_FROM_CHECK"/>
    <function name="XML_FROM_DATATYPE" link="libxml2-xmlerror.html#XML_FROM_DATATYPE"/>
    <function name="XML_FROM_DTD" link="libxml2-xmlerror.html#XML_FROM_DTD"/>
    <function name="XML_FROM_FTP" link="libxml2-xmlerror.html#XML_FROM_FTP"/>
    <function name="XML_FROM_HTML" link="libxml2-xmlerror.html#XML_FROM_HTML"/>
    <function name="XML_FROM_HTTP" link="libxml2-xmlerror.html#XML_FROM_HTTP"/>
    <function name="XML_FROM_I18N" link="libxml2-xmlerror.html#XML_FROM_I18N"/>
    <function name="XML_FROM_IO" link="libxml2-xmlerror.html#XML_FROM_IO"/>
    <function name="XML_FROM_MEMORY" link="libxml2-xmlerror.html#XML_FROM_MEMORY"/>
    <function name="XML_FROM_MODULE" link="libxml2-xmlerror.html#XML_FROM_MODULE"/>
    <function name="XML_FROM_NAMESPACE" link="libxml2-xmlerror.html#XML_FROM_NAMESPACE"/>
    <function name="XML_FROM_NONE" link="libxml2-xmlerror.html#XML_FROM_NONE"/>
    <function name="XML_FROM_OUTPUT" link="libxml2-xmlerror.html#XML_FROM_OUTPUT"/>
    <function name="XML_FROM_PARSER" link="libxml2-xmlerror.html#XML_FROM_PARSER"/>
    <function name="XML_FROM_REGEXP" link="libxml2-xmlerror.html#XML_FROM_REGEXP"/>
    <function name="XML_FROM_RELAXNGP" link="libxml2-xmlerror.html#XML_FROM_RELAXNGP"/>
    <function name="XML_FROM_RELAXNGV" link="libxml2-xmlerror.html#XML_FROM_RELAXNGV"/>
    <function name="XML_FROM_SCHEMASP" link="libxml2-xmlerror.html#XML_FROM_SCHEMASP"/>
    <function name="XML_FROM_SCHEMASV" link="libxml2-xmlerror.html#XML_FROM_SCHEMASV"/>
    <function name="XML_FROM_SCHEMATRONV" link="libxml2-xmlerror.html#XML_FROM_SCHEMATRONV"/>
    <function name="XML_FROM_TREE" link="libxml2-xmlerror.html#XML_FROM_TREE"/>
    <function name="XML_FROM_URI" link="libxml2-xmlerror.html#XML_FROM_URI"/>
    <function name="XML_FROM_VALID" link="libxml2-xmlerror.html#XML_FROM_VALID"/>
    <function name="XML_FROM_WRITER" link="libxml2-xmlerror.html#XML_FROM_WRITER"/>
    <function name="XML_FROM_XINCLUDE" link="libxml2-xmlerror.html#XML_FROM_XINCLUDE"/>
    <function name="XML_FROM_XPATH" link="libxml2-xmlerror.html#XML_FROM_XPATH"/>
    <function name="XML_FROM_XPOINTER" link="libxml2-xmlerror.html#XML_FROM_XPOINTER"/>
    <function name="XML_FROM_XSLT" link="libxml2-xmlerror.html#XML_FROM_XSLT"/>
    <function name="XML_FTP_ACCNT" link="libxml2-xmlerror.html#XML_FTP_ACCNT"/>
    <function name="XML_FTP_EPSV_ANSWER" link="libxml2-xmlerror.html#XML_FTP_EPSV_ANSWER"/>
    <function name="XML_FTP_PASV_ANSWER" link="libxml2-xmlerror.html#XML_FTP_PASV_ANSWER"/>
    <function name="XML_FTP_URL_SYNTAX" link="libxml2-xmlerror.html#XML_FTP_URL_SYNTAX"/>
    <function name="XML_HTML_DOCUMENT_NODE" link="libxml2-tree.html#XML_HTML_DOCUMENT_NODE"/>
    <function name="XML_HTML_STRUCURE_ERROR" link="libxml2-xmlerror.html#XML_HTML_STRUCURE_ERROR"/>
    <function name="XML_HTML_UNKNOWN_TAG" link="libxml2-xmlerror.html#XML_HTML_UNKNOWN_TAG"/>
    <function name="XML_HTTP_UNKNOWN_HOST" link="libxml2-xmlerror.html#XML_HTTP_UNKNOWN_HOST"/>
    <function name="XML_HTTP_URL_SYNTAX" link="libxml2-xmlerror.html#XML_HTTP_URL_SYNTAX"/>
    <function name="XML_HTTP_USE_IP" link="libxml2-xmlerror.html#XML_HTTP_USE_IP"/>
    <function name="XML_I18N_CONV_FAILED" link="libxml2-xmlerror.html#XML_I18N_CONV_FAILED"/>
    <function name="XML_I18N_EXCESS_HANDLER" link="libxml2-xmlerror.html#XML_I18N_EXCESS_HANDLER"/>
    <function name="XML_I18N_NO_HANDLER" link="libxml2-xmlerror.html#XML_I18N_NO_HANDLER"/>
    <function name="XML_I18N_NO_NAME" link="libxml2-xmlerror.html#XML_I18N_NO_NAME"/>
    <function name="XML_I18N_NO_OUTPUT" link="libxml2-xmlerror.html#XML_I18N_NO_OUTPUT"/>
    <function name="XML_INTERNAL_GENERAL_ENTITY" link="libxml2-entities.html#XML_INTERNAL_GENERAL_ENTITY"/>
    <function name="XML_INTERNAL_PARAMETER_ENTITY" link="libxml2-entities.html#XML_INTERNAL_PARAMETER_ENTITY"/>
    <function name="XML_INTERNAL_PREDEFINED_ENTITY" link="libxml2-entities.html#XML_INTERNAL_PREDEFINED_ENTITY"/>
    <function name="XML_IO_BUFFER_FULL" link="libxml2-xmlerror.html#XML_IO_BUFFER_FULL"/>
    <function name="XML_IO_EACCES" link="libxml2-xmlerror.html#XML_IO_EACCES"/>
    <function name="XML_IO_EADDRINUSE" link="libxml2-xmlerror.html#XML_IO_EADDRINUSE"/>
    <function name="XML_IO_EAFNOSUPPORT" link="libxml2-xmlerror.html#XML_IO_EAFNOSUPPORT"/>
    <function name="XML_IO_EAGAIN" link="libxml2-xmlerror.html#XML_IO_EAGAIN"/>
    <function name="XML_IO_EALREADY" link="libxml2-xmlerror.html#XML_IO_EALREADY"/>
    <function name="XML_IO_EBADF" link="libxml2-xmlerror.html#XML_IO_EBADF"/>
    <function name="XML_IO_EBADMSG" link="libxml2-xmlerror.html#XML_IO_EBADMSG"/>
    <function name="XML_IO_EBUSY" link="libxml2-xmlerror.html#XML_IO_EBUSY"/>
    <function name="XML_IO_ECANCELED" link="libxml2-xmlerror.html#XML_IO_ECANCELED"/>
    <function name="XML_IO_ECHILD" link="libxml2-xmlerror.html#XML_IO_ECHILD"/>
    <function name="XML_IO_ECONNREFUSED" link="libxml2-xmlerror.html#XML_IO_ECONNREFUSED"/>
    <function name="XML_IO_EDEADLK" link="libxml2-xmlerror.html#XML_IO_EDEADLK"/>
    <function name="XML_IO_EDOM" link="libxml2-xmlerror.html#XML_IO_EDOM"/>
    <function name="XML_IO_EEXIST" link="libxml2-xmlerror.html#XML_IO_EEXIST"/>
    <function name="XML_IO_EFAULT" link="libxml2-xmlerror.html#XML_IO_EFAULT"/>
    <function name="XML_IO_EFBIG" link="libxml2-xmlerror.html#XML_IO_EFBIG"/>
    <function name="XML_IO_EINPROGRESS" link="libxml2-xmlerror.html#XML_IO_EINPROGRESS"/>
    <function name="XML_IO_EINTR" link="libxml2-xmlerror.html#XML_IO_EINTR"/>
    <function name="XML_IO_EINVAL" link="libxml2-xmlerror.html#XML_IO_EINVAL"/>
    <function name="XML_IO_EIO" link="libxml2-xmlerror.html#XML_IO_EIO"/>
    <function name="XML_IO_EISCONN" link="libxml2-xmlerror.html#XML_IO_EISCONN"/>
    <function name="XML_IO_EISDIR" link="libxml2-xmlerror.html#XML_IO_EISDIR"/>
    <function name="XML_IO_EMFILE" link="libxml2-xmlerror.html#XML_IO_EMFILE"/>
    <function name="XML_IO_EMLINK" link="libxml2-xmlerror.html#XML_IO_EMLINK"/>
    <function name="XML_IO_EMSGSIZE" link="libxml2-xmlerror.html#XML_IO_EMSGSIZE"/>
    <function name="XML_IO_ENAMETOOLONG" link="libxml2-xmlerror.html#XML_IO_ENAMETOOLONG"/>
    <function name="XML_IO_ENCODER" link="libxml2-xmlerror.html#XML_IO_ENCODER"/>
    <function name="XML_IO_ENETUNREACH" link="libxml2-xmlerror.html#XML_IO_ENETUNREACH"/>
    <function name="XML_IO_ENFILE" link="libxml2-xmlerror.html#XML_IO_ENFILE"/>
    <function name="XML_IO_ENODEV" link="libxml2-xmlerror.html#XML_IO_ENODEV"/>
    <function name="XML_IO_ENOENT" link="libxml2-xmlerror.html#XML_IO_ENOENT"/>
    <function name="XML_IO_ENOEXEC" link="libxml2-xmlerror.html#XML_IO_ENOEXEC"/>
    <function name="XML_IO_ENOLCK" link="libxml2-xmlerror.html#XML_IO_ENOLCK"/>
    <function name="XML_IO_ENOMEM" link="libxml2-xmlerror.html#XML_IO_ENOMEM"/>
    <function name="XML_IO_ENOSPC" link="libxml2-xmlerror.html#XML_IO_ENOSPC"/>
    <function name="XML_IO_ENOSYS" link="libxml2-xmlerror.html#XML_IO_ENOSYS"/>
    <function name="XML_IO_ENOTDIR" link="libxml2-xmlerror.html#XML_IO_ENOTDIR"/>
    <function name="XML_IO_ENOTEMPTY" link="libxml2-xmlerror.html#XML_IO_ENOTEMPTY"/>
    <function name="XML_IO_ENOTSOCK" link="libxml2-xmlerror.html#XML_IO_ENOTSOCK"/>
    <function name="XML_IO_ENOTSUP" link="libxml2-xmlerror.html#XML_IO_ENOTSUP"/>
    <function name="XML_IO_ENOTTY" link="libxml2-xmlerror.html#XML_IO_ENOTTY"/>
    <function name="XML_IO_ENXIO" link="libxml2-xmlerror.html#XML_IO_ENXIO"/>
    <function name="XML_IO_EPERM" link="libxml2-xmlerror.html#XML_IO_EPERM"/>
    <function name="XML_IO_EPIPE" link="libxml2-xmlerror.html#XML_IO_EPIPE"/>
    <function name="XML_IO_ERANGE" link="libxml2-xmlerror.html#XML_IO_ERANGE"/>
    <function name="XML_IO_EROFS" link="libxml2-xmlerror.html#XML_IO_EROFS"/>
    <function name="XML_IO_ESPIPE" link="libxml2-xmlerror.html#XML_IO_ESPIPE"/>
    <function name="XML_IO_ESRCH" link="libxml2-xmlerror.html#XML_IO_ESRCH"/>
    <function name="XML_IO_ETIMEDOUT" link="libxml2-xmlerror.html#XML_IO_ETIMEDOUT"/>
    <function name="XML_IO_EXDEV" link="libxml2-xmlerror.html#XML_IO_EXDEV"/>
    <function name="XML_IO_FLUSH" link="libxml2-xmlerror.html#XML_IO_FLUSH"/>
    <function name="XML_IO_LOAD_ERROR" link="libxml2-xmlerror.html#XML_IO_LOAD_ERROR"/>
    <function name="XML_IO_NETWORK_ATTEMPT" link="libxml2-xmlerror.html#XML_IO_NETWORK_ATTEMPT"/>
    <function name="XML_IO_NO_INPUT" link="libxml2-xmlerror.html#XML_IO_NO_INPUT"/>
    <function name="XML_IO_UNKNOWN" link="libxml2-xmlerror.html#XML_IO_UNKNOWN"/>
    <function name="XML_IO_WRITE" link="libxml2-xmlerror.html#XML_IO_WRITE"/>
    <function name="XML_MODULE_CLOSE" link="libxml2-xmlerror.html#XML_MODULE_CLOSE"/>
    <function name="XML_MODULE_LAZY" link="libxml2-xmlmodule.html#XML_MODULE_LAZY"/>
    <function name="XML_MODULE_LOCAL" link="libxml2-xmlmodule.html#XML_MODULE_LOCAL"/>
    <function name="XML_MODULE_OPEN" link="libxml2-xmlerror.html#XML_MODULE_OPEN"/>
    <function name="XML_NAMESPACE_DECL" link="libxml2-tree.html#XML_NAMESPACE_DECL"/>
    <function name="XML_NOTATION_NODE" link="libxml2-tree.html#XML_NOTATION_NODE"/>
    <function name="XML_NS_ERR_ATTRIBUTE_REDEFINED" link="libxml2-xmlerror.html#XML_NS_ERR_ATTRIBUTE_REDEFINED"/>
    <function name="XML_NS_ERR_COLON" link="libxml2-xmlerror.html#XML_NS_ERR_COLON"/>
    <function name="XML_NS_ERR_EMPTY" link="libxml2-xmlerror.html#XML_NS_ERR_EMPTY"/>
    <function name="XML_NS_ERR_QNAME" link="libxml2-xmlerror.html#XML_NS_ERR_QNAME"/>
    <function name="XML_NS_ERR_UNDEFINED_NAMESPACE" link="libxml2-xmlerror.html#XML_NS_ERR_UNDEFINED_NAMESPACE"/>
    <function name="XML_NS_ERR_XML_NAMESPACE" link="libxml2-xmlerror.html#XML_NS_ERR_XML_NAMESPACE"/>
    <function name="XML_PARSER_ATTRIBUTE_VALUE" link="libxml2-parser.html#XML_PARSER_ATTRIBUTE_VALUE"/>
    <function name="XML_PARSER_CDATA_SECTION" link="libxml2-parser.html#XML_PARSER_CDATA_SECTION"/>
    <function name="XML_PARSER_COMMENT" link="libxml2-parser.html#XML_PARSER_COMMENT"/>
    <function name="XML_PARSER_CONTENT" link="libxml2-parser.html#XML_PARSER_CONTENT"/>
    <function name="XML_PARSER_DEFAULTATTRS" link="libxml2-xmlreader.html#XML_PARSER_DEFAULTATTRS"/>
    <function name="XML_PARSER_DTD" link="libxml2-parser.html#XML_PARSER_DTD"/>
    <function name="XML_PARSER_END_TAG" link="libxml2-parser.html#XML_PARSER_END_TAG"/>
    <function name="XML_PARSER_ENTITY_DECL" link="libxml2-parser.html#XML_PARSER_ENTITY_DECL"/>
    <function name="XML_PARSER_ENTITY_VALUE" link="libxml2-parser.html#XML_PARSER_ENTITY_VALUE"/>
    <function name="XML_PARSER_EOF" link="libxml2-parser.html#XML_PARSER_EOF"/>
    <function name="XML_PARSER_EPILOG" link="libxml2-parser.html#XML_PARSER_EPILOG"/>
    <function name="XML_PARSER_IGNORE" link="libxml2-parser.html#XML_PARSER_IGNORE"/>
    <function name="XML_PARSER_LOADDTD" link="libxml2-xmlreader.html#XML_PARSER_LOADDTD"/>
    <function name="XML_PARSER_MISC" link="libxml2-parser.html#XML_PARSER_MISC"/>
    <function name="XML_PARSER_PI" link="libxml2-parser.html#XML_PARSER_PI"/>
    <function name="XML_PARSER_PROLOG" link="libxml2-parser.html#XML_PARSER_PROLOG"/>
    <function name="XML_PARSER_PUBLIC_LITERAL" link="libxml2-parser.html#XML_PARSER_PUBLIC_LITERAL"/>
    <function name="XML_PARSER_SEVERITY_ERROR" link="libxml2-xmlreader.html#XML_PARSER_SEVERITY_ERROR"/>
    <function name="XML_PARSER_SEVERITY_VALIDITY_ERROR" link="libxml2-xmlreader.html#XML_PARSER_SEVERITY_VALIDITY_ERROR"/>
    <function name="XML_PARSER_SEVERITY_VALIDITY_WARNING" link="libxml2-xmlreader.html#XML_PARSER_SEVERITY_VALIDITY_WARNING"/>
    <function name="XML_PARSER_SEVERITY_WARNING" link="libxml2-xmlreader.html#XML_PARSER_SEVERITY_WARNING"/>
    <function name="XML_PARSER_START" link="libxml2-parser.html#XML_PARSER_START"/>
    <function name="XML_PARSER_START_TAG" link="libxml2-parser.html#XML_PARSER_START_TAG"/>
    <function name="XML_PARSER_SUBST_ENTITIES" link="libxml2-xmlreader.html#XML_PARSER_SUBST_ENTITIES"/>
    <function name="XML_PARSER_SYSTEM_LITERAL" link="libxml2-parser.html#XML_PARSER_SYSTEM_LITERAL"/>
    <function name="XML_PARSER_VALIDATE" link="libxml2-xmlreader.html#XML_PARSER_VALIDATE"/>
    <function name="XML_PARSE_BIG_LINES" link="libxml2-parser.html#XML_PARSE_BIG_LINES"/>
    <function name="XML_PARSE_COMPACT" link="libxml2-parser.html#XML_PARSE_COMPACT"/>
    <function name="XML_PARSE_DOM" link="libxml2-parser.html#XML_PARSE_DOM"/>
    <function name="XML_PARSE_DTDATTR" link="libxml2-parser.html#XML_PARSE_DTDATTR"/>
    <function name="XML_PARSE_DTDLOAD" link="libxml2-parser.html#XML_PARSE_DTDLOAD"/>
    <function name="XML_PARSE_DTDVALID" link="libxml2-parser.html#XML_PARSE_DTDVALID"/>
    <function name="XML_PARSE_HUGE" link="libxml2-parser.html#XML_PARSE_HUGE"/>
    <function name="XML_PARSE_IGNORE_ENC" link="libxml2-parser.html#XML_PARSE_IGNORE_ENC"/>
    <function name="XML_PARSE_NOBASEFIX" link="libxml2-parser.html#XML_PARSE_NOBASEFIX"/>
    <function name="XML_PARSE_NOBLANKS" link="libxml2-parser.html#XML_PARSE_NOBLANKS"/>
    <function name="XML_PARSE_NOCDATA" link="libxml2-parser.html#XML_PARSE_NOCDATA"/>
    <function name="XML_PARSE_NODICT" link="libxml2-parser.html#XML_PARSE_NODICT"/>
    <function name="XML_PARSE_NOENT" link="libxml2-parser.html#XML_PARSE_NOENT"/>
    <function name="XML_PARSE_NOERROR" link="libxml2-parser.html#XML_PARSE_NOERROR"/>
    <function name="XML_PARSE_NONET" link="libxml2-parser.html#XML_PARSE_NONET"/>
    <function name="XML_PARSE_NOWARNING" link="libxml2-parser.html#XML_PARSE_NOWARNING"/>
    <function name="XML_PARSE_NOXINCNODE" link="libxml2-parser.html#XML_PARSE_NOXINCNODE"/>
    <function name="XML_PARSE_NSCLEAN" link="libxml2-parser.html#XML_PARSE_NSCLEAN"/>
    <function name="XML_PARSE_OLD10" link="libxml2-parser.html#XML_PARSE_OLD10"/>
    <function name="XML_PARSE_OLDSAX" link="libxml2-parser.html#XML_PARSE_OLDSAX"/>
    <function name="XML_PARSE_PEDANTIC" link="libxml2-parser.html#XML_PARSE_PEDANTIC"/>
    <function name="XML_PARSE_PUSH_DOM" link="libxml2-parser.html#XML_PARSE_PUSH_DOM"/>
    <function name="XML_PARSE_PUSH_SAX" link="libxml2-parser.html#XML_PARSE_PUSH_SAX"/>
    <function name="XML_PARSE_READER" link="libxml2-parser.html#XML_PARSE_READER"/>
    <function name="XML_PARSE_RECOVER" link="libxml2-parser.html#XML_PARSE_RECOVER"/>
    <function name="XML_PARSE_SAX" link="libxml2-parser.html#XML_PARSE_SAX"/>
    <function name="XML_PARSE_SAX1" link="libxml2-parser.html#XML_PARSE_SAX1"/>
    <function name="XML_PARSE_UNKNOWN" link="libxml2-parser.html#XML_PARSE_UNKNOWN"/>
    <function name="XML_PARSE_XINCLUDE" link="libxml2-parser.html#XML_PARSE_XINCLUDE"/>
    <function name="XML_PATTERN_DEFAULT" link="libxml2-pattern.html#XML_PATTERN_DEFAULT"/>
    <function name="XML_PATTERN_XPATH" link="libxml2-pattern.html#XML_PATTERN_XPATH"/>
    <function name="XML_PATTERN_XSFIELD" link="libxml2-pattern.html#XML_PATTERN_XSFIELD"/>
    <function name="XML_PATTERN_XSSEL" link="libxml2-pattern.html#XML_PATTERN_XSSEL"/>
    <function name="XML_PI_NODE" link="libxml2-tree.html#XML_PI_NODE"/>
    <function name="XML_READER_TYPE_ATTRIBUTE" link="libxml2-xmlreader.html#XML_READER_TYPE_ATTRIBUTE"/>
    <function name="XML_READER_TYPE_CDATA" link="libxml2-xmlreader.html#XML_READER_TYPE_CDATA"/>
    <function name="XML_READER_TYPE_COMMENT" link="libxml2-xmlreader.html#XML_READER_TYPE_COMMENT"/>
    <function name="XML_READER_TYPE_DOCUMENT" link="libxml2-xmlreader.html#XML_READER_TYPE_DOCUMENT"/>
    <function name="XML_READER_TYPE_DOCUMENT_FRAGMENT" link="libxml2-xmlreader.html#XML_READER_TYPE_DOCUMENT_FRAGMENT"/>
    <function name="XML_READER_TYPE_DOCUMENT_TYPE" link="libxml2-xmlreader.html#XML_READER_TYPE_DOCUMENT_TYPE"/>
    <function name="XML_READER_TYPE_ELEMENT" link="libxml2-xmlreader.html#XML_READER_TYPE_ELEMENT"/>
    <function name="XML_READER_TYPE_END_ELEMENT" link="libxml2-xmlreader.html#XML_READER_TYPE_END_ELEMENT"/>
    <function name="XML_READER_TYPE_END_ENTITY" link="libxml2-xmlreader.html#XML_READER_TYPE_END_ENTITY"/>
    <function name="XML_READER_TYPE_ENTITY" link="libxml2-xmlreader.html#XML_READER_TYPE_ENTITY"/>
    <function name="XML_READER_TYPE_ENTITY_REFERENCE" link="libxml2-xmlreader.html#XML_READER_TYPE_ENTITY_REFERENCE"/>
    <function name="XML_READER_TYPE_NONE" link="libxml2-xmlreader.html#XML_READER_TYPE_NONE"/>
    <function name="XML_READER_TYPE_NOTATION" link="libxml2-xmlreader.html#XML_READER_TYPE_NOTATION"/>
    <function name="XML_READER_TYPE_PROCESSING_INSTRUCTION" link="libxml2-xmlreader.html#XML_READER_TYPE_PROCESSING_INSTRUCTION"/>
    <function name="XML_READER_TYPE_SIGNIFICANT_WHITESPACE" link="libxml2-xmlreader.html#XML_READER_TYPE_SIGNIFICANT_WHITESPACE"/>
    <function name="XML_READER_TYPE_TEXT" link="libxml2-xmlreader.html#XML_READER_TYPE_TEXT"/>
    <function name="XML_READER_TYPE_WHITESPACE" link="libxml2-xmlreader.html#XML_READER_TYPE_WHITESPACE"/>
    <function name="XML_READER_TYPE_XML_DECLARATION" link="libxml2-xmlreader.html#XML_READER_TYPE_XML_DECLARATION"/>
    <function name="XML_REGEXP_COMPILE_ERROR" link="libxml2-xmlerror.html#XML_REGEXP_COMPILE_ERROR"/>
    <function name="XML_RELAXNGP_CRNG" link="libxml2-relaxng.html#XML_RELAXNGP_CRNG"/>
    <function name="XML_RELAXNGP_FREE_DOC" link="libxml2-relaxng.html#XML_RELAXNGP_FREE_DOC"/>
    <function name="XML_RELAXNGP_NONE" link="libxml2-relaxng.html#XML_RELAXNGP_NONE"/>
    <function name="XML_RELAXNG_ERR_ATTREXTRANS" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ATTREXTRANS"/>
    <function name="XML_RELAXNG_ERR_ATTRNAME" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ATTRNAME"/>
    <function name="XML_RELAXNG_ERR_ATTRNONS" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ATTRNONS"/>
    <function name="XML_RELAXNG_ERR_ATTRVALID" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ATTRVALID"/>
    <function name="XML_RELAXNG_ERR_ATTRWRONGNS" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ATTRWRONGNS"/>
    <function name="XML_RELAXNG_ERR_CONTENTVALID" link="libxml2-relaxng.html#XML_RELAXNG_ERR_CONTENTVALID"/>
    <function name="XML_RELAXNG_ERR_DATAELEM" link="libxml2-relaxng.html#XML_RELAXNG_ERR_DATAELEM"/>
    <function name="XML_RELAXNG_ERR_DATATYPE" link="libxml2-relaxng.html#XML_RELAXNG_ERR_DATATYPE"/>
    <function name="XML_RELAXNG_ERR_DUPID" link="libxml2-relaxng.html#XML_RELAXNG_ERR_DUPID"/>
    <function name="XML_RELAXNG_ERR_ELEMEXTRANS" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ELEMEXTRANS"/>
    <function name="XML_RELAXNG_ERR_ELEMNAME" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ELEMNAME"/>
    <function name="XML_RELAXNG_ERR_ELEMNONS" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ELEMNONS"/>
    <function name="XML_RELAXNG_ERR_ELEMNOTEMPTY" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ELEMNOTEMPTY"/>
    <function name="XML_RELAXNG_ERR_ELEMWRONG" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ELEMWRONG"/>
    <function name="XML_RELAXNG_ERR_ELEMWRONGNS" link="libxml2-relaxng.html#XML_RELAXNG_ERR_ELEMWRONGNS"/>
    <function name="XML_RELAXNG_ERR_EXTRACONTENT" link="libxml2-relaxng.html#XML_RELAXNG_ERR_EXTRACONTENT"/>
    <function name="XML_RELAXNG_ERR_EXTRADATA" link="libxml2-relaxng.html#XML_RELAXNG_ERR_EXTRADATA"/>
    <function name="XML_RELAXNG_ERR_INTEREXTRA" link="libxml2-relaxng.html#XML_RELAXNG_ERR_INTEREXTRA"/>
    <function name="XML_RELAXNG_ERR_INTERNAL" link="libxml2-relaxng.html#XML_RELAXNG_ERR_INTERNAL"/>
    <function name="XML_RELAXNG_ERR_INTERNODATA" link="libxml2-relaxng.html#XML_RELAXNG_ERR_INTERNODATA"/>
    <function name="XML_RELAXNG_ERR_INTERSEQ" link="libxml2-relaxng.html#XML_RELAXNG_ERR_INTERSEQ"/>
    <function name="XML_RELAXNG_ERR_INVALIDATTR" link="libxml2-relaxng.html#XML_RELAXNG_ERR_INVALIDATTR"/>
    <function name="XML_RELAXNG_ERR_LACKDATA" link="libxml2-relaxng.html#XML_RELAXNG_ERR_LACKDATA"/>
    <function name="XML_RELAXNG_ERR_LIST" link="libxml2-relaxng.html#XML_RELAXNG_ERR_LIST"/>
    <function name="XML_RELAXNG_ERR_LISTELEM" link="libxml2-relaxng.html#XML_RELAXNG_ERR_LISTELEM"/>
    <function name="XML_RELAXNG_ERR_LISTEMPTY" link="libxml2-relaxng.html#XML_RELAXNG_ERR_LISTEMPTY"/>
    <function name="XML_RELAXNG_ERR_LISTEXTRA" link="libxml2-relaxng.html#XML_RELAXNG_ERR_LISTEXTRA"/>
    <function name="XML_RELAXNG_ERR_MEMORY" link="libxml2-relaxng.html#XML_RELAXNG_ERR_MEMORY"/>
    <function name="XML_RELAXNG_ERR_NODEFINE" link="libxml2-relaxng.html#XML_RELAXNG_ERR_NODEFINE"/>
    <function name="XML_RELAXNG_ERR_NOELEM" link="libxml2-relaxng.html#XML_RELAXNG_ERR_NOELEM"/>
    <function name="XML_RELAXNG_ERR_NOGRAMMAR" link="libxml2-relaxng.html#XML_RELAXNG_ERR_NOGRAMMAR"/>
    <function name="XML_RELAXNG_ERR_NOSTATE" link="libxml2-relaxng.html#XML_RELAXNG_ERR_NOSTATE"/>
    <function name="XML_RELAXNG_ERR_NOTELEM" link="libxml2-relaxng.html#XML_RELAXNG_ERR_NOTELEM"/>
    <function name="XML_RELAXNG_ERR_TEXTWRONG" link="libxml2-relaxng.html#XML_RELAXNG_ERR_TEXTWRONG"/>
    <function name="XML_RELAXNG_ERR_TYPE" link="libxml2-relaxng.html#XML_RELAXNG_ERR_TYPE"/>
    <function name="XML_RELAXNG_ERR_TYPECMP" link="libxml2-relaxng.html#XML_RELAXNG_ERR_TYPECMP"/>
    <function name="XML_RELAXNG_ERR_TYPEVAL" link="libxml2-relaxng.html#XML_RELAXNG_ERR_TYPEVAL"/>
    <function name="XML_RELAXNG_ERR_VALELEM" link="libxml2-relaxng.html#XML_RELAXNG_ERR_VALELEM"/>
    <function name="XML_RELAXNG_ERR_VALUE" link="libxml2-relaxng.html#XML_RELAXNG_ERR_VALUE"/>
    <function name="XML_RELAXNG_OK" link="libxml2-relaxng.html#XML_RELAXNG_OK"/>
    <function name="XML_RNGP_ANYNAME_ATTR_ANCESTOR" link="libxml2-xmlerror.html#XML_RNGP_ANYNAME_ATTR_ANCESTOR"/>
    <function name="XML_RNGP_ATTRIBUTE_CHILDREN" link="libxml2-xmlerror.html#XML_RNGP_ATTRIBUTE_CHILDREN"/>
    <function name="XML_RNGP_ATTRIBUTE_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_ATTRIBUTE_CONTENT"/>
    <function name="XML_RNGP_ATTRIBUTE_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_ATTRIBUTE_EMPTY"/>
    <function name="XML_RNGP_ATTRIBUTE_NOOP" link="libxml2-xmlerror.html#XML_RNGP_ATTRIBUTE_NOOP"/>
    <function name="XML_RNGP_ATTR_CONFLICT" link="libxml2-xmlerror.html#XML_RNGP_ATTR_CONFLICT"/>
    <function name="XML_RNGP_CHOICE_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_CHOICE_CONTENT"/>
    <function name="XML_RNGP_CHOICE_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_CHOICE_EMPTY"/>
    <function name="XML_RNGP_CREATE_FAILURE" link="libxml2-xmlerror.html#XML_RNGP_CREATE_FAILURE"/>
    <function name="XML_RNGP_DATA_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_DATA_CONTENT"/>
    <function name="XML_RNGP_DEFINE_CREATE_FAILED" link="libxml2-xmlerror.html#XML_RNGP_DEFINE_CREATE_FAILED"/>
    <function name="XML_RNGP_DEFINE_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_DEFINE_EMPTY"/>
    <function name="XML_RNGP_DEFINE_MISSING" link="libxml2-xmlerror.html#XML_RNGP_DEFINE_MISSING"/>
    <function name="XML_RNGP_DEFINE_NAME_MISSING" link="libxml2-xmlerror.html#XML_RNGP_DEFINE_NAME_MISSING"/>
    <function name="XML_RNGP_DEF_CHOICE_AND_INTERLEAVE" link="libxml2-xmlerror.html#XML_RNGP_DEF_CHOICE_AND_INTERLEAVE"/>
    <function name="XML_RNGP_ELEMENT_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_ELEMENT_CONTENT"/>
    <function name="XML_RNGP_ELEMENT_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_ELEMENT_EMPTY"/>
    <function name="XML_RNGP_ELEMENT_NAME" link="libxml2-xmlerror.html#XML_RNGP_ELEMENT_NAME"/>
    <function name="XML_RNGP_ELEMENT_NO_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_ELEMENT_NO_CONTENT"/>
    <function name="XML_RNGP_ELEM_CONTENT_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_ELEM_CONTENT_EMPTY"/>
    <function name="XML_RNGP_ELEM_CONTENT_ERROR" link="libxml2-xmlerror.html#XML_RNGP_ELEM_CONTENT_ERROR"/>
    <function name="XML_RNGP_ELEM_TEXT_CONFLICT" link="libxml2-xmlerror.html#XML_RNGP_ELEM_TEXT_CONFLICT"/>
    <function name="XML_RNGP_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_EMPTY"/>
    <function name="XML_RNGP_EMPTY_CONSTRUCT" link="libxml2-xmlerror.html#XML_RNGP_EMPTY_CONSTRUCT"/>
    <function name="XML_RNGP_EMPTY_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_EMPTY_CONTENT"/>
    <function name="XML_RNGP_EMPTY_NOT_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_EMPTY_NOT_EMPTY"/>
    <function name="XML_RNGP_ERROR_TYPE_LIB" link="libxml2-xmlerror.html#XML_RNGP_ERROR_TYPE_LIB"/>
    <function name="XML_RNGP_EXCEPT_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_EXCEPT_EMPTY"/>
    <function name="XML_RNGP_EXCEPT_MISSING" link="libxml2-xmlerror.html#XML_RNGP_EXCEPT_MISSING"/>
    <function name="XML_RNGP_EXCEPT_MULTIPLE" link="libxml2-xmlerror.html#XML_RNGP_EXCEPT_MULTIPLE"/>
    <function name="XML_RNGP_EXCEPT_NO_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_EXCEPT_NO_CONTENT"/>
    <function name="XML_RNGP_EXTERNALREF_EMTPY" link="libxml2-xmlerror.html#XML_RNGP_EXTERNALREF_EMTPY"/>
    <function name="XML_RNGP_EXTERNALREF_RECURSE" link="libxml2-xmlerror.html#XML_RNGP_EXTERNALREF_RECURSE"/>
    <function name="XML_RNGP_EXTERNAL_REF_FAILURE" link="libxml2-xmlerror.html#XML_RNGP_EXTERNAL_REF_FAILURE"/>
    <function name="XML_RNGP_FORBIDDEN_ATTRIBUTE" link="libxml2-xmlerror.html#XML_RNGP_FORBIDDEN_ATTRIBUTE"/>
    <function name="XML_RNGP_FOREIGN_ELEMENT" link="libxml2-xmlerror.html#XML_RNGP_FOREIGN_ELEMENT"/>
    <function name="XML_RNGP_GRAMMAR_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_GRAMMAR_CONTENT"/>
    <function name="XML_RNGP_GRAMMAR_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_GRAMMAR_EMPTY"/>
    <function name="XML_RNGP_GRAMMAR_MISSING" link="libxml2-xmlerror.html#XML_RNGP_GRAMMAR_MISSING"/>
    <function name="XML_RNGP_GRAMMAR_NO_START" link="libxml2-xmlerror.html#XML_RNGP_GRAMMAR_NO_START"/>
    <function name="XML_RNGP_GROUP_ATTR_CONFLICT" link="libxml2-xmlerror.html#XML_RNGP_GROUP_ATTR_CONFLICT"/>
    <function name="XML_RNGP_HREF_ERROR" link="libxml2-xmlerror.html#XML_RNGP_HREF_ERROR"/>
    <function name="XML_RNGP_INCLUDE_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_INCLUDE_EMPTY"/>
    <function name="XML_RNGP_INCLUDE_FAILURE" link="libxml2-xmlerror.html#XML_RNGP_INCLUDE_FAILURE"/>
    <function name="XML_RNGP_INCLUDE_RECURSE" link="libxml2-xmlerror.html#XML_RNGP_INCLUDE_RECURSE"/>
    <function name="XML_RNGP_INTERLEAVE_ADD" link="libxml2-xmlerror.html#XML_RNGP_INTERLEAVE_ADD"/>
    <function name="XML_RNGP_INTERLEAVE_CREATE_FAILED" link="libxml2-xmlerror.html#XML_RNGP_INTERLEAVE_CREATE_FAILED"/>
    <function name="XML_RNGP_INTERLEAVE_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_INTERLEAVE_EMPTY"/>
    <function name="XML_RNGP_INTERLEAVE_NO_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_INTERLEAVE_NO_CONTENT"/>
    <function name="XML_RNGP_INVALID_DEFINE_NAME" link="libxml2-xmlerror.html#XML_RNGP_INVALID_DEFINE_NAME"/>
    <function name="XML_RNGP_INVALID_URI" link="libxml2-xmlerror.html#XML_RNGP_INVALID_URI"/>
    <function name="XML_RNGP_INVALID_VALUE" link="libxml2-xmlerror.html#XML_RNGP_INVALID_VALUE"/>
    <function name="XML_RNGP_MISSING_HREF" link="libxml2-xmlerror.html#XML_RNGP_MISSING_HREF"/>
    <function name="XML_RNGP_NAME_MISSING" link="libxml2-xmlerror.html#XML_RNGP_NAME_MISSING"/>
    <function name="XML_RNGP_NEED_COMBINE" link="libxml2-xmlerror.html#XML_RNGP_NEED_COMBINE"/>
    <function name="XML_RNGP_NOTALLOWED_NOT_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_NOTALLOWED_NOT_EMPTY"/>
    <function name="XML_RNGP_NSNAME_ATTR_ANCESTOR" link="libxml2-xmlerror.html#XML_RNGP_NSNAME_ATTR_ANCESTOR"/>
    <function name="XML_RNGP_NSNAME_NO_NS" link="libxml2-xmlerror.html#XML_RNGP_NSNAME_NO_NS"/>
    <function name="XML_RNGP_PARAM_FORBIDDEN" link="libxml2-xmlerror.html#XML_RNGP_PARAM_FORBIDDEN"/>
    <function name="XML_RNGP_PARAM_NAME_MISSING" link="libxml2-xmlerror.html#XML_RNGP_PARAM_NAME_MISSING"/>
    <function name="XML_RNGP_PARENTREF_CREATE_FAILED" link="libxml2-xmlerror.html#XML_RNGP_PARENTREF_CREATE_FAILED"/>
    <function name="XML_RNGP_PARENTREF_NAME_INVALID" link="libxml2-xmlerror.html#XML_RNGP_PARENTREF_NAME_INVALID"/>
    <function name="XML_RNGP_PARENTREF_NOT_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_PARENTREF_NOT_EMPTY"/>
    <function name="XML_RNGP_PARENTREF_NO_NAME" link="libxml2-xmlerror.html#XML_RNGP_PARENTREF_NO_NAME"/>
    <function name="XML_RNGP_PARENTREF_NO_PARENT" link="libxml2-xmlerror.html#XML_RNGP_PARENTREF_NO_PARENT"/>
    <function name="XML_RNGP_PARSE_ERROR" link="libxml2-xmlerror.html#XML_RNGP_PARSE_ERROR"/>
    <function name="XML_RNGP_PAT_ANYNAME_EXCEPT_ANYNAME" link="libxml2-xmlerror.html#XML_RNGP_PAT_ANYNAME_EXCEPT_ANYNAME"/>
    <function name="XML_RNGP_PAT_ATTR_ATTR" link="libxml2-xmlerror.html#XML_RNGP_PAT_ATTR_ATTR"/>
    <function name="XML_RNGP_PAT_ATTR_ELEM" link="libxml2-xmlerror.html#XML_RNGP_PAT_ATTR_ELEM"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_ATTR" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_ATTR"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_ELEM" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_ELEM"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_EMPTY"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_GROUP" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_GROUP"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_INTERLEAVE" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_INTERLEAVE"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_LIST" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_LIST"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_ONEMORE" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_ONEMORE"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_REF" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_REF"/>
    <function name="XML_RNGP_PAT_DATA_EXCEPT_TEXT" link="libxml2-xmlerror.html#XML_RNGP_PAT_DATA_EXCEPT_TEXT"/>
    <function name="XML_RNGP_PAT_LIST_ATTR" link="libxml2-xmlerror.html#XML_RNGP_PAT_LIST_ATTR"/>
    <function name="XML_RNGP_PAT_LIST_ELEM" link="libxml2-xmlerror.html#XML_RNGP_PAT_LIST_ELEM"/>
    <function name="XML_RNGP_PAT_LIST_INTERLEAVE" link="libxml2-xmlerror.html#XML_RNGP_PAT_LIST_INTERLEAVE"/>
    <function name="XML_RNGP_PAT_LIST_LIST" link="libxml2-xmlerror.html#XML_RNGP_PAT_LIST_LIST"/>
    <function name="XML_RNGP_PAT_LIST_REF" link="libxml2-xmlerror.html#XML_RNGP_PAT_LIST_REF"/>
    <function name="XML_RNGP_PAT_LIST_TEXT" link="libxml2-xmlerror.html#XML_RNGP_PAT_LIST_TEXT"/>
    <function name="XML_RNGP_PAT_NSNAME_EXCEPT_ANYNAME" link="libxml2-xmlerror.html#XML_RNGP_PAT_NSNAME_EXCEPT_ANYNAME"/>
    <function name="XML_RNGP_PAT_NSNAME_EXCEPT_NSNAME" link="libxml2-xmlerror.html#XML_RNGP_PAT_NSNAME_EXCEPT_NSNAME"/>
    <function name="XML_RNGP_PAT_ONEMORE_GROUP_ATTR" link="libxml2-xmlerror.html#XML_RNGP_PAT_ONEMORE_GROUP_ATTR"/>
    <function name="XML_RNGP_PAT_ONEMORE_INTERLEAVE_ATTR" link="libxml2-xmlerror.html#XML_RNGP_PAT_ONEMORE_INTERLEAVE_ATTR"/>
    <function name="XML_RNGP_PAT_START_ATTR" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_ATTR"/>
    <function name="XML_RNGP_PAT_START_DATA" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_DATA"/>
    <function name="XML_RNGP_PAT_START_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_EMPTY"/>
    <function name="XML_RNGP_PAT_START_GROUP" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_GROUP"/>
    <function name="XML_RNGP_PAT_START_INTERLEAVE" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_INTERLEAVE"/>
    <function name="XML_RNGP_PAT_START_LIST" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_LIST"/>
    <function name="XML_RNGP_PAT_START_ONEMORE" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_ONEMORE"/>
    <function name="XML_RNGP_PAT_START_TEXT" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_TEXT"/>
    <function name="XML_RNGP_PAT_START_VALUE" link="libxml2-xmlerror.html#XML_RNGP_PAT_START_VALUE"/>
    <function name="XML_RNGP_PREFIX_UNDEFINED" link="libxml2-xmlerror.html#XML_RNGP_PREFIX_UNDEFINED"/>
    <function name="XML_RNGP_REF_CREATE_FAILED" link="libxml2-xmlerror.html#XML_RNGP_REF_CREATE_FAILED"/>
    <function name="XML_RNGP_REF_CYCLE" link="libxml2-xmlerror.html#XML_RNGP_REF_CYCLE"/>
    <function name="XML_RNGP_REF_NAME_INVALID" link="libxml2-xmlerror.html#XML_RNGP_REF_NAME_INVALID"/>
    <function name="XML_RNGP_REF_NOT_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_REF_NOT_EMPTY"/>
    <function name="XML_RNGP_REF_NO_DEF" link="libxml2-xmlerror.html#XML_RNGP_REF_NO_DEF"/>
    <function name="XML_RNGP_REF_NO_NAME" link="libxml2-xmlerror.html#XML_RNGP_REF_NO_NAME"/>
    <function name="XML_RNGP_START_CHOICE_AND_INTERLEAVE" link="libxml2-xmlerror.html#XML_RNGP_START_CHOICE_AND_INTERLEAVE"/>
    <function name="XML_RNGP_START_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_START_CONTENT"/>
    <function name="XML_RNGP_START_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_START_EMPTY"/>
    <function name="XML_RNGP_START_MISSING" link="libxml2-xmlerror.html#XML_RNGP_START_MISSING"/>
    <function name="XML_RNGP_TEXT_EXPECTED" link="libxml2-xmlerror.html#XML_RNGP_TEXT_EXPECTED"/>
    <function name="XML_RNGP_TEXT_HAS_CHILD" link="libxml2-xmlerror.html#XML_RNGP_TEXT_HAS_CHILD"/>
    <function name="XML_RNGP_TYPE_MISSING" link="libxml2-xmlerror.html#XML_RNGP_TYPE_MISSING"/>
    <function name="XML_RNGP_TYPE_NOT_FOUND" link="libxml2-xmlerror.html#XML_RNGP_TYPE_NOT_FOUND"/>
    <function name="XML_RNGP_TYPE_VALUE" link="libxml2-xmlerror.html#XML_RNGP_TYPE_VALUE"/>
    <function name="XML_RNGP_UNKNOWN_ATTRIBUTE" link="libxml2-xmlerror.html#XML_RNGP_UNKNOWN_ATTRIBUTE"/>
    <function name="XML_RNGP_UNKNOWN_COMBINE" link="libxml2-xmlerror.html#XML_RNGP_UNKNOWN_COMBINE"/>
    <function name="XML_RNGP_UNKNOWN_CONSTRUCT" link="libxml2-xmlerror.html#XML_RNGP_UNKNOWN_CONSTRUCT"/>
    <function name="XML_RNGP_UNKNOWN_TYPE_LIB" link="libxml2-xmlerror.html#XML_RNGP_UNKNOWN_TYPE_LIB"/>
    <function name="XML_RNGP_URI_FRAGMENT" link="libxml2-xmlerror.html#XML_RNGP_URI_FRAGMENT"/>
    <function name="XML_RNGP_URI_NOT_ABSOLUTE" link="libxml2-xmlerror.html#XML_RNGP_URI_NOT_ABSOLUTE"/>
    <function name="XML_RNGP_VALUE_EMPTY" link="libxml2-xmlerror.html#XML_RNGP_VALUE_EMPTY"/>
    <function name="XML_RNGP_VALUE_NO_CONTENT" link="libxml2-xmlerror.html#XML_RNGP_VALUE_NO_CONTENT"/>
    <function name="XML_RNGP_XMLNS_NAME" link="libxml2-xmlerror.html#XML_RNGP_XMLNS_NAME"/>
    <function name="XML_RNGP_XML_NS" link="libxml2-xmlerror.html#XML_RNGP_XML_NS"/>
    <function name="XML_SAVE_AS_HTML" link="libxml2-xmlsave.html#XML_SAVE_AS_HTML"/>
    <function name="XML_SAVE_AS_XML" link="libxml2-xmlsave.html#XML_SAVE_AS_XML"/>
    <function name="XML_SAVE_CHAR_INVALID" link="libxml2-xmlerror.html#XML_SAVE_CHAR_INVALID"/>
    <function name="XML_SAVE_FORMAT" link="libxml2-xmlsave.html#XML_SAVE_FORMAT"/>
    <function name="XML_SAVE_NOT_UTF8" link="libxml2-xmlerror.html#XML_SAVE_NOT_UTF8"/>
    <function name="XML_SAVE_NO_DECL" link="libxml2-xmlsave.html#XML_SAVE_NO_DECL"/>
    <function name="XML_SAVE_NO_DOCTYPE" link="libxml2-xmlerror.html#XML_SAVE_NO_DOCTYPE"/>
    <function name="XML_SAVE_NO_EMPTY" link="libxml2-xmlsave.html#XML_SAVE_NO_EMPTY"/>
    <function name="XML_SAVE_NO_XHTML" link="libxml2-xmlsave.html#XML_SAVE_NO_XHTML"/>
    <function name="XML_SAVE_UNKNOWN_ENCODING" link="libxml2-xmlerror.html#XML_SAVE_UNKNOWN_ENCODING"/>
    <function name="XML_SAVE_WSNONSIG" link="libxml2-xmlsave.html#XML_SAVE_WSNONSIG"/>
    <function name="XML_SAVE_XHTML" link="libxml2-xmlsave.html#XML_SAVE_XHTML"/>
    <function name="XML_SCHEMAP_AG_PROPS_CORRECT" link="libxml2-xmlerror.html#XML_SCHEMAP_AG_PROPS_CORRECT"/>
    <function name="XML_SCHEMAP_ATTRFORMDEFAULT_VALUE" link="libxml2-xmlerror.html#XML_SCHEMAP_ATTRFORMDEFAULT_VALUE"/>
    <function name="XML_SCHEMAP_ATTRGRP_NONAME_NOREF" link="libxml2-xmlerror.html#XML_SCHEMAP_ATTRGRP_NONAME_NOREF"/>
    <function name="XML_SCHEMAP_ATTR_NONAME_NOREF" link="libxml2-xmlerror.html#XML_SCHEMAP_ATTR_NONAME_NOREF"/>
    <function name="XML_SCHEMAP_AU_PROPS_CORRECT" link="libxml2-xmlerror.html#XML_SCHEMAP_AU_PROPS_CORRECT"/>
    <function name="XML_SCHEMAP_AU_PROPS_CORRECT_2" link="libxml2-xmlerror.html#XML_SCHEMAP_AU_PROPS_CORRECT_2"/>
    <function name="XML_SCHEMAP_A_PROPS_CORRECT_2" link="libxml2-xmlerror.html#XML_SCHEMAP_A_PROPS_CORRECT_2"/>
    <function name="XML_SCHEMAP_A_PROPS_CORRECT_3" link="libxml2-xmlerror.html#XML_SCHEMAP_A_PROPS_CORRECT_3"/>
    <function name="XML_SCHEMAP_COMPLEXTYPE_NONAME_NOREF" link="libxml2-xmlerror.html#XML_SCHEMAP_COMPLEXTYPE_NONAME_NOREF"/>
    <function name="XML_SCHEMAP_COS_ALL_LIMITED" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ALL_LIMITED"/>
    <function name="XML_SCHEMAP_COS_CT_EXTENDS_1_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_CT_EXTENDS_1_1"/>
    <function name="XML_SCHEMAP_COS_CT_EXTENDS_1_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_CT_EXTENDS_1_2"/>
    <function name="XML_SCHEMAP_COS_CT_EXTENDS_1_3" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_CT_EXTENDS_1_3"/>
    <function name="XML_SCHEMAP_COS_ST_DERIVED_OK_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_DERIVED_OK_2_1"/>
    <function name="XML_SCHEMAP_COS_ST_DERIVED_OK_2_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_DERIVED_OK_2_2"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_1_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_1_1"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_1_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_1_2"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_1_3_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_1_3_1"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_1_3_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_1_3_2"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_2_1"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_1_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_2_3_1_1"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_1_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_2_3_1_2"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_1"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_2"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_3" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_3"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_4" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_4"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_5" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_2_3_2_5"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_3_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_3_1"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_3_3_1"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_1_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_3_3_1_2"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_1"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_2"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_3" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_3"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_4" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_4"/>
    <function name="XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_5" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_ST_RESTRICTS_3_3_2_5"/>
    <function name="XML_SCHEMAP_COS_VALID_DEFAULT_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_VALID_DEFAULT_1"/>
    <function name="XML_SCHEMAP_COS_VALID_DEFAULT_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_VALID_DEFAULT_2_1"/>
    <function name="XML_SCHEMAP_COS_VALID_DEFAULT_2_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_VALID_DEFAULT_2_2_1"/>
    <function name="XML_SCHEMAP_COS_VALID_DEFAULT_2_2_2" link="libxml2-xmlerror.html#XML_SCHEMAP_COS_VALID_DEFAULT_2_2_2"/>
    <function name="XML_SCHEMAP_CT_PROPS_CORRECT_1" link="libxml2-xmlerror.html#XML_SCHEMAP_CT_PROPS_CORRECT_1"/>
    <function name="XML_SCHEMAP_CT_PROPS_CORRECT_2" link="libxml2-xmlerror.html#XML_SCHEMAP_CT_PROPS_CORRECT_2"/>
    <function name="XML_SCHEMAP_CT_PROPS_CORRECT_3" link="libxml2-xmlerror.html#XML_SCHEMAP_CT_PROPS_CORRECT_3"/>
    <function name="XML_SCHEMAP_CT_PROPS_CORRECT_4" link="libxml2-xmlerror.html#XML_SCHEMAP_CT_PROPS_CORRECT_4"/>
    <function name="XML_SCHEMAP_CT_PROPS_CORRECT_5" link="libxml2-xmlerror.html#XML_SCHEMAP_CT_PROPS_CORRECT_5"/>
    <function name="XML_SCHEMAP_CVC_SIMPLE_TYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_CVC_SIMPLE_TYPE"/>
    <function name="XML_SCHEMAP_C_PROPS_CORRECT" link="libxml2-xmlerror.html#XML_SCHEMAP_C_PROPS_CORRECT"/>
    <function name="XML_SCHEMAP_DEF_AND_PREFIX" link="libxml2-xmlerror.html#XML_SCHEMAP_DEF_AND_PREFIX"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_1" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_1"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_1" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_1"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_2" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_2"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_3" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_1_3"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_2" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_2_2"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_3" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_3"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_1" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_1"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_2" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_2"/>
    <function name="XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_3" link="libxml2-xmlerror.html#XML_SCHEMAP_DERIVATION_OK_RESTRICTION_4_3"/>
    <function name="XML_SCHEMAP_ELEMFORMDEFAULT_VALUE" link="libxml2-xmlerror.html#XML_SCHEMAP_ELEMFORMDEFAULT_VALUE"/>
    <function name="XML_SCHEMAP_ELEM_DEFAULT_FIXED" link="libxml2-xmlerror.html#XML_SCHEMAP_ELEM_DEFAULT_FIXED"/>
    <function name="XML_SCHEMAP_ELEM_NONAME_NOREF" link="libxml2-xmlerror.html#XML_SCHEMAP_ELEM_NONAME_NOREF"/>
    <function name="XML_SCHEMAP_EXTENSION_NO_BASE" link="libxml2-xmlerror.html#XML_SCHEMAP_EXTENSION_NO_BASE"/>
    <function name="XML_SCHEMAP_E_PROPS_CORRECT_2" link="libxml2-xmlerror.html#XML_SCHEMAP_E_PROPS_CORRECT_2"/>
    <function name="XML_SCHEMAP_E_PROPS_CORRECT_3" link="libxml2-xmlerror.html#XML_SCHEMAP_E_PROPS_CORRECT_3"/>
    <function name="XML_SCHEMAP_E_PROPS_CORRECT_4" link="libxml2-xmlerror.html#XML_SCHEMAP_E_PROPS_CORRECT_4"/>
    <function name="XML_SCHEMAP_E_PROPS_CORRECT_5" link="libxml2-xmlerror.html#XML_SCHEMAP_E_PROPS_CORRECT_5"/>
    <function name="XML_SCHEMAP_E_PROPS_CORRECT_6" link="libxml2-xmlerror.html#XML_SCHEMAP_E_PROPS_CORRECT_6"/>
    <function name="XML_SCHEMAP_FACET_NO_VALUE" link="libxml2-xmlerror.html#XML_SCHEMAP_FACET_NO_VALUE"/>
    <function name="XML_SCHEMAP_FAILED_BUILD_IMPORT" link="libxml2-xmlerror.html#XML_SCHEMAP_FAILED_BUILD_IMPORT"/>
    <function name="XML_SCHEMAP_FAILED_LOAD" link="libxml2-xmlerror.html#XML_SCHEMAP_FAILED_LOAD"/>
    <function name="XML_SCHEMAP_FAILED_PARSE" link="libxml2-xmlerror.html#XML_SCHEMAP_FAILED_PARSE"/>
    <function name="XML_SCHEMAP_GROUP_NONAME_NOREF" link="libxml2-xmlerror.html#XML_SCHEMAP_GROUP_NONAME_NOREF"/>
    <function name="XML_SCHEMAP_IMPORT_NAMESPACE_NOT_URI" link="libxml2-xmlerror.html#XML_SCHEMAP_IMPORT_NAMESPACE_NOT_URI"/>
    <function name="XML_SCHEMAP_IMPORT_REDEFINE_NSNAME" link="libxml2-xmlerror.html#XML_SCHEMAP_IMPORT_REDEFINE_NSNAME"/>
    <function name="XML_SCHEMAP_IMPORT_SCHEMA_NOT_URI" link="libxml2-xmlerror.html#XML_SCHEMAP_IMPORT_SCHEMA_NOT_URI"/>
    <function name="XML_SCHEMAP_INCLUDE_SCHEMA_NOT_URI" link="libxml2-xmlerror.html#XML_SCHEMAP_INCLUDE_SCHEMA_NOT_URI"/>
    <function name="XML_SCHEMAP_INCLUDE_SCHEMA_NO_URI" link="libxml2-xmlerror.html#XML_SCHEMAP_INCLUDE_SCHEMA_NO_URI"/>
    <function name="XML_SCHEMAP_INTERNAL" link="libxml2-xmlerror.html#XML_SCHEMAP_INTERNAL"/>
    <function name="XML_SCHEMAP_INTERSECTION_NOT_EXPRESSIBLE" link="libxml2-xmlerror.html#XML_SCHEMAP_INTERSECTION_NOT_EXPRESSIBLE"/>
    <function name="XML_SCHEMAP_INVALID_ATTR_COMBINATION" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_ATTR_COMBINATION"/>
    <function name="XML_SCHEMAP_INVALID_ATTR_INLINE_COMBINATION" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_ATTR_INLINE_COMBINATION"/>
    <function name="XML_SCHEMAP_INVALID_ATTR_NAME" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_ATTR_NAME"/>
    <function name="XML_SCHEMAP_INVALID_ATTR_USE" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_ATTR_USE"/>
    <function name="XML_SCHEMAP_INVALID_BOOLEAN" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_BOOLEAN"/>
    <function name="XML_SCHEMAP_INVALID_ENUM" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_ENUM"/>
    <function name="XML_SCHEMAP_INVALID_FACET" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_FACET"/>
    <function name="XML_SCHEMAP_INVALID_FACET_VALUE" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_FACET_VALUE"/>
    <function name="XML_SCHEMAP_INVALID_MAXOCCURS" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_MAXOCCURS"/>
    <function name="XML_SCHEMAP_INVALID_MINOCCURS" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_MINOCCURS"/>
    <function name="XML_SCHEMAP_INVALID_REF_AND_SUBTYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_REF_AND_SUBTYPE"/>
    <function name="XML_SCHEMAP_INVALID_WHITE_SPACE" link="libxml2-xmlerror.html#XML_SCHEMAP_INVALID_WHITE_SPACE"/>
    <function name="XML_SCHEMAP_MG_PROPS_CORRECT_1" link="libxml2-xmlerror.html#XML_SCHEMAP_MG_PROPS_CORRECT_1"/>
    <function name="XML_SCHEMAP_MG_PROPS_CORRECT_2" link="libxml2-xmlerror.html#XML_SCHEMAP_MG_PROPS_CORRECT_2"/>
    <function name="XML_SCHEMAP_MISSING_SIMPLETYPE_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_MISSING_SIMPLETYPE_CHILD"/>
    <function name="XML_SCHEMAP_NOATTR_NOREF" link="libxml2-xmlerror.html#XML_SCHEMAP_NOATTR_NOREF"/>
    <function name="XML_SCHEMAP_NOROOT" link="libxml2-xmlerror.html#XML_SCHEMAP_NOROOT"/>
    <function name="XML_SCHEMAP_NOTATION_NO_NAME" link="libxml2-xmlerror.html#XML_SCHEMAP_NOTATION_NO_NAME"/>
    <function name="XML_SCHEMAP_NOTHING_TO_PARSE" link="libxml2-xmlerror.html#XML_SCHEMAP_NOTHING_TO_PARSE"/>
    <function name="XML_SCHEMAP_NOTYPE_NOREF" link="libxml2-xmlerror.html#XML_SCHEMAP_NOTYPE_NOREF"/>
    <function name="XML_SCHEMAP_NOT_DETERMINISTIC" link="libxml2-xmlerror.html#XML_SCHEMAP_NOT_DETERMINISTIC"/>
    <function name="XML_SCHEMAP_NOT_SCHEMA" link="libxml2-xmlerror.html#XML_SCHEMAP_NOT_SCHEMA"/>
    <function name="XML_SCHEMAP_NO_XMLNS" link="libxml2-xmlerror.html#XML_SCHEMAP_NO_XMLNS"/>
    <function name="XML_SCHEMAP_NO_XSI" link="libxml2-xmlerror.html#XML_SCHEMAP_NO_XSI"/>
    <function name="XML_SCHEMAP_PREFIX_UNDEFINED" link="libxml2-xmlerror.html#XML_SCHEMAP_PREFIX_UNDEFINED"/>
    <function name="XML_SCHEMAP_P_PROPS_CORRECT_1" link="libxml2-xmlerror.html#XML_SCHEMAP_P_PROPS_CORRECT_1"/>
    <function name="XML_SCHEMAP_P_PROPS_CORRECT_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_P_PROPS_CORRECT_2_1"/>
    <function name="XML_SCHEMAP_P_PROPS_CORRECT_2_2" link="libxml2-xmlerror.html#XML_SCHEMAP_P_PROPS_CORRECT_2_2"/>
    <function name="XML_SCHEMAP_RECURSIVE" link="libxml2-xmlerror.html#XML_SCHEMAP_RECURSIVE"/>
    <function name="XML_SCHEMAP_REDEFINED_ATTR" link="libxml2-xmlerror.html#XML_SCHEMAP_REDEFINED_ATTR"/>
    <function name="XML_SCHEMAP_REDEFINED_ATTRGROUP" link="libxml2-xmlerror.html#XML_SCHEMAP_REDEFINED_ATTRGROUP"/>
    <function name="XML_SCHEMAP_REDEFINED_ELEMENT" link="libxml2-xmlerror.html#XML_SCHEMAP_REDEFINED_ELEMENT"/>
    <function name="XML_SCHEMAP_REDEFINED_GROUP" link="libxml2-xmlerror.html#XML_SCHEMAP_REDEFINED_GROUP"/>
    <function name="XML_SCHEMAP_REDEFINED_NOTATION" link="libxml2-xmlerror.html#XML_SCHEMAP_REDEFINED_NOTATION"/>
    <function name="XML_SCHEMAP_REDEFINED_TYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_REDEFINED_TYPE"/>
    <function name="XML_SCHEMAP_REF_AND_CONTENT" link="libxml2-xmlerror.html#XML_SCHEMAP_REF_AND_CONTENT"/>
    <function name="XML_SCHEMAP_REF_AND_SUBTYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_REF_AND_SUBTYPE"/>
    <function name="XML_SCHEMAP_REGEXP_INVALID" link="libxml2-xmlerror.html#XML_SCHEMAP_REGEXP_INVALID"/>
    <function name="XML_SCHEMAP_RESTRICTION_NONAME_NOREF" link="libxml2-xmlerror.html#XML_SCHEMAP_RESTRICTION_NONAME_NOREF"/>
    <function name="XML_SCHEMAP_S4S_ATTR_INVALID_VALUE" link="libxml2-xmlerror.html#XML_SCHEMAP_S4S_ATTR_INVALID_VALUE"/>
    <function name="XML_SCHEMAP_S4S_ATTR_MISSING" link="libxml2-xmlerror.html#XML_SCHEMAP_S4S_ATTR_MISSING"/>
    <function name="XML_SCHEMAP_S4S_ATTR_NOT_ALLOWED" link="libxml2-xmlerror.html#XML_SCHEMAP_S4S_ATTR_NOT_ALLOWED"/>
    <function name="XML_SCHEMAP_S4S_ELEM_MISSING" link="libxml2-xmlerror.html#XML_SCHEMAP_S4S_ELEM_MISSING"/>
    <function name="XML_SCHEMAP_S4S_ELEM_NOT_ALLOWED" link="libxml2-xmlerror.html#XML_SCHEMAP_S4S_ELEM_NOT_ALLOWED"/>
    <function name="XML_SCHEMAP_SIMPLETYPE_NONAME" link="libxml2-xmlerror.html#XML_SCHEMAP_SIMPLETYPE_NONAME"/>
    <function name="XML_SCHEMAP_SRC_ATTRIBUTE_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ATTRIBUTE_1"/>
    <function name="XML_SCHEMAP_SRC_ATTRIBUTE_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ATTRIBUTE_2"/>
    <function name="XML_SCHEMAP_SRC_ATTRIBUTE_3_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ATTRIBUTE_3_1"/>
    <function name="XML_SCHEMAP_SRC_ATTRIBUTE_3_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ATTRIBUTE_3_2"/>
    <function name="XML_SCHEMAP_SRC_ATTRIBUTE_4" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ATTRIBUTE_4"/>
    <function name="XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_1"/>
    <function name="XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_2"/>
    <function name="XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_3" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ATTRIBUTE_GROUP_3"/>
    <function name="XML_SCHEMAP_SRC_CT_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_CT_1"/>
    <function name="XML_SCHEMAP_SRC_ELEMENT_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ELEMENT_1"/>
    <function name="XML_SCHEMAP_SRC_ELEMENT_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ELEMENT_2_1"/>
    <function name="XML_SCHEMAP_SRC_ELEMENT_2_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ELEMENT_2_2"/>
    <function name="XML_SCHEMAP_SRC_ELEMENT_3" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_ELEMENT_3"/>
    <function name="XML_SCHEMAP_SRC_IMPORT" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_IMPORT"/>
    <function name="XML_SCHEMAP_SRC_IMPORT_1_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_IMPORT_1_1"/>
    <function name="XML_SCHEMAP_SRC_IMPORT_1_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_IMPORT_1_2"/>
    <function name="XML_SCHEMAP_SRC_IMPORT_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_IMPORT_2"/>
    <function name="XML_SCHEMAP_SRC_IMPORT_2_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_IMPORT_2_1"/>
    <function name="XML_SCHEMAP_SRC_IMPORT_2_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_IMPORT_2_2"/>
    <function name="XML_SCHEMAP_SRC_IMPORT_3_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_IMPORT_3_1"/>
    <function name="XML_SCHEMAP_SRC_IMPORT_3_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_IMPORT_3_2"/>
    <function name="XML_SCHEMAP_SRC_INCLUDE" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_INCLUDE"/>
    <function name="XML_SCHEMAP_SRC_LIST_ITEMTYPE_OR_SIMPLETYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_LIST_ITEMTYPE_OR_SIMPLETYPE"/>
    <function name="XML_SCHEMAP_SRC_REDEFINE" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_REDEFINE"/>
    <function name="XML_SCHEMAP_SRC_RESOLVE" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_RESOLVE"/>
    <function name="XML_SCHEMAP_SRC_RESTRICTION_BASE_OR_SIMPLETYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_RESTRICTION_BASE_OR_SIMPLETYPE"/>
    <function name="XML_SCHEMAP_SRC_SIMPLE_TYPE_1" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_SIMPLE_TYPE_1"/>
    <function name="XML_SCHEMAP_SRC_SIMPLE_TYPE_2" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_SIMPLE_TYPE_2"/>
    <function name="XML_SCHEMAP_SRC_SIMPLE_TYPE_3" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_SIMPLE_TYPE_3"/>
    <function name="XML_SCHEMAP_SRC_SIMPLE_TYPE_4" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_SIMPLE_TYPE_4"/>
    <function name="XML_SCHEMAP_SRC_UNION_MEMBERTYPES_OR_SIMPLETYPES" link="libxml2-xmlerror.html#XML_SCHEMAP_SRC_UNION_MEMBERTYPES_OR_SIMPLETYPES"/>
    <function name="XML_SCHEMAP_ST_PROPS_CORRECT_1" link="libxml2-xmlerror.html#XML_SCHEMAP_ST_PROPS_CORRECT_1"/>
    <function name="XML_SCHEMAP_ST_PROPS_CORRECT_2" link="libxml2-xmlerror.html#XML_SCHEMAP_ST_PROPS_CORRECT_2"/>
    <function name="XML_SCHEMAP_ST_PROPS_CORRECT_3" link="libxml2-xmlerror.html#XML_SCHEMAP_ST_PROPS_CORRECT_3"/>
    <function name="XML_SCHEMAP_SUPERNUMEROUS_LIST_ITEM_TYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_SUPERNUMEROUS_LIST_ITEM_TYPE"/>
    <function name="XML_SCHEMAP_TYPE_AND_SUBTYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_TYPE_AND_SUBTYPE"/>
    <function name="XML_SCHEMAP_UNION_NOT_EXPRESSIBLE" link="libxml2-xmlerror.html#XML_SCHEMAP_UNION_NOT_EXPRESSIBLE"/>
    <function name="XML_SCHEMAP_UNKNOWN_ALL_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_ALL_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_ANYATTRIBUTE_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_ANYATTRIBUTE_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_ATTRGRP_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_ATTRGRP_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_ATTRIBUTE_GROUP" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_ATTRIBUTE_GROUP"/>
    <function name="XML_SCHEMAP_UNKNOWN_ATTR_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_ATTR_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_BASE_TYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_BASE_TYPE"/>
    <function name="XML_SCHEMAP_UNKNOWN_CHOICE_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_CHOICE_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_COMPLEXCONTENT_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_COMPLEXCONTENT_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_COMPLEXTYPE_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_COMPLEXTYPE_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_ELEM_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_ELEM_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_EXTENSION_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_EXTENSION_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_FACET_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_FACET_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_FACET_TYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_FACET_TYPE"/>
    <function name="XML_SCHEMAP_UNKNOWN_GROUP_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_GROUP_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_IMPORT_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_IMPORT_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_INCLUDE_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_INCLUDE_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_LIST_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_LIST_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_MEMBER_TYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_MEMBER_TYPE"/>
    <function name="XML_SCHEMAP_UNKNOWN_NOTATION_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_NOTATION_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_PREFIX" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_PREFIX"/>
    <function name="XML_SCHEMAP_UNKNOWN_PROCESSCONTENT_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_PROCESSCONTENT_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_REF" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_REF"/>
    <function name="XML_SCHEMAP_UNKNOWN_RESTRICTION_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_RESTRICTION_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_SCHEMAS_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_SCHEMAS_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_SEQUENCE_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_SEQUENCE_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_SIMPLECONTENT_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_SIMPLECONTENT_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_SIMPLETYPE_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_SIMPLETYPE_CHILD"/>
    <function name="XML_SCHEMAP_UNKNOWN_TYPE" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_TYPE"/>
    <function name="XML_SCHEMAP_UNKNOWN_UNION_CHILD" link="libxml2-xmlerror.html#XML_SCHEMAP_UNKNOWN_UNION_CHILD"/>
    <function name="XML_SCHEMAP_WARN_ATTR_POINTLESS_PROH" link="libxml2-xmlerror.html#XML_SCHEMAP_WARN_ATTR_POINTLESS_PROH"/>
    <function name="XML_SCHEMAP_WARN_ATTR_REDECL_PROH" link="libxml2-xmlerror.html#XML_SCHEMAP_WARN_ATTR_REDECL_PROH"/>
    <function name="XML_SCHEMAP_WARN_SKIP_SCHEMA" link="libxml2-xmlerror.html#XML_SCHEMAP_WARN_SKIP_SCHEMA"/>
    <function name="XML_SCHEMAP_WARN_UNLOCATED_SCHEMA" link="libxml2-xmlerror.html#XML_SCHEMAP_WARN_UNLOCATED_SCHEMA"/>
    <function name="XML_SCHEMAP_WILDCARD_INVALID_NS_MEMBER" link="libxml2-xmlerror.html#XML_SCHEMAP_WILDCARD_INVALID_NS_MEMBER"/>
    <function name="XML_SCHEMAS_ANYSIMPLETYPE" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANYSIMPLETYPE"/>
    <function name="XML_SCHEMAS_ANYTYPE" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANYTYPE"/>
    <function name="XML_SCHEMAS_ANYURI" link="libxml2-schemasInternals.html#XML_SCHEMAS_ANYURI"/>
    <function name="XML_SCHEMAS_BASE64BINARY" link="libxml2-schemasInternals.html#XML_SCHEMAS_BASE64BINARY"/>
    <function name="XML_SCHEMAS_BOOLEAN" link="libxml2-schemasInternals.html#XML_SCHEMAS_BOOLEAN"/>
    <function name="XML_SCHEMAS_BYTE" link="libxml2-schemasInternals.html#XML_SCHEMAS_BYTE"/>
    <function name="XML_SCHEMAS_DATE" link="libxml2-schemasInternals.html#XML_SCHEMAS_DATE"/>
    <function name="XML_SCHEMAS_DATETIME" link="libxml2-schemasInternals.html#XML_SCHEMAS_DATETIME"/>
    <function name="XML_SCHEMAS_DECIMAL" link="libxml2-schemasInternals.html#XML_SCHEMAS_DECIMAL"/>
    <function name="XML_SCHEMAS_DOUBLE" link="libxml2-schemasInternals.html#XML_SCHEMAS_DOUBLE"/>
    <function name="XML_SCHEMAS_DURATION" link="libxml2-schemasInternals.html#XML_SCHEMAS_DURATION"/>
    <function name="XML_SCHEMAS_ENTITIES" link="libxml2-schemasInternals.html#XML_SCHEMAS_ENTITIES"/>
    <function name="XML_SCHEMAS_ENTITY" link="libxml2-schemasInternals.html#XML_SCHEMAS_ENTITY"/>
    <function name="XML_SCHEMAS_ERR_" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_"/>
    <function name="XML_SCHEMAS_ERR_ATTRINVALID" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_ATTRINVALID"/>
    <function name="XML_SCHEMAS_ERR_ATTRUNKNOWN" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_ATTRUNKNOWN"/>
    <function name="XML_SCHEMAS_ERR_CONSTRUCT" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_CONSTRUCT"/>
    <function name="XML_SCHEMAS_ERR_ELEMCONT" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_ELEMCONT"/>
    <function name="XML_SCHEMAS_ERR_EXTRACONTENT" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_EXTRACONTENT"/>
    <function name="XML_SCHEMAS_ERR_FACET" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_FACET"/>
    <function name="XML_SCHEMAS_ERR_HAVEDEFAULT" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_HAVEDEFAULT"/>
    <function name="XML_SCHEMAS_ERR_INTERNAL" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_INTERNAL"/>
    <function name="XML_SCHEMAS_ERR_INVALIDATTR" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_INVALIDATTR"/>
    <function name="XML_SCHEMAS_ERR_INVALIDELEM" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_INVALIDELEM"/>
    <function name="XML_SCHEMAS_ERR_ISABSTRACT" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_ISABSTRACT"/>
    <function name="XML_SCHEMAS_ERR_MISSING" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_MISSING"/>
    <function name="XML_SCHEMAS_ERR_NOROLLBACK" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_NOROLLBACK"/>
    <function name="XML_SCHEMAS_ERR_NOROOT" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_NOROOT"/>
    <function name="XML_SCHEMAS_ERR_NOTDETERMINIST" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_NOTDETERMINIST"/>
    <function name="XML_SCHEMAS_ERR_NOTEMPTY" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_NOTEMPTY"/>
    <function name="XML_SCHEMAS_ERR_NOTNILLABLE" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_NOTNILLABLE"/>
    <function name="XML_SCHEMAS_ERR_NOTSIMPLE" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_NOTSIMPLE"/>
    <function name="XML_SCHEMAS_ERR_NOTTOPLEVEL" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_NOTTOPLEVEL"/>
    <function name="XML_SCHEMAS_ERR_NOTYPE" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_NOTYPE"/>
    <function name="XML_SCHEMAS_ERR_OK" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_OK"/>
    <function name="XML_SCHEMAS_ERR_UNDECLAREDELEM" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_UNDECLAREDELEM"/>
    <function name="XML_SCHEMAS_ERR_VALUE" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_VALUE"/>
    <function name="XML_SCHEMAS_ERR_WRONGELEM" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_WRONGELEM"/>
    <function name="XML_SCHEMAS_ERR_XXX" link="libxml2-xmlschemas.html#XML_SCHEMAS_ERR_XXX"/>
    <function name="XML_SCHEMAS_FLOAT" link="libxml2-schemasInternals.html#XML_SCHEMAS_FLOAT"/>
    <function name="XML_SCHEMAS_GDAY" link="libxml2-schemasInternals.html#XML_SCHEMAS_GDAY"/>
    <function name="XML_SCHEMAS_GMONTH" link="libxml2-schemasInternals.html#XML_SCHEMAS_GMONTH"/>
    <function name="XML_SCHEMAS_GMONTHDAY" link="libxml2-schemasInternals.html#XML_SCHEMAS_GMONTHDAY"/>
    <function name="XML_SCHEMAS_GYEAR" link="libxml2-schemasInternals.html#XML_SCHEMAS_GYEAR"/>
    <function name="XML_SCHEMAS_GYEARMONTH" link="libxml2-schemasInternals.html#XML_SCHEMAS_GYEARMONTH"/>
    <function name="XML_SCHEMAS_HEXBINARY" link="libxml2-schemasInternals.html#XML_SCHEMAS_HEXBINARY"/>
    <function name="XML_SCHEMAS_ID" link="libxml2-schemasInternals.html#XML_SCHEMAS_ID"/>
    <function name="XML_SCHEMAS_IDREF" link="libxml2-schemasInternals.html#XML_SCHEMAS_IDREF"/>
    <function name="XML_SCHEMAS_IDREFS" link="libxml2-schemasInternals.html#XML_SCHEMAS_IDREFS"/>
    <function name="XML_SCHEMAS_INT" link="libxml2-schemasInternals.html#XML_SCHEMAS_INT"/>
    <function name="XML_SCHEMAS_INTEGER" link="libxml2-schemasInternals.html#XML_SCHEMAS_INTEGER"/>
    <function name="XML_SCHEMAS_LANGUAGE" link="libxml2-schemasInternals.html#XML_SCHEMAS_LANGUAGE"/>
    <function name="XML_SCHEMAS_LONG" link="libxml2-schemasInternals.html#XML_SCHEMAS_LONG"/>
    <function name="XML_SCHEMAS_NAME" link="libxml2-schemasInternals.html#XML_SCHEMAS_NAME"/>
    <function name="XML_SCHEMAS_NCNAME" link="libxml2-schemasInternals.html#XML_SCHEMAS_NCNAME"/>
    <function name="XML_SCHEMAS_NINTEGER" link="libxml2-schemasInternals.html#XML_SCHEMAS_NINTEGER"/>
    <function name="XML_SCHEMAS_NMTOKEN" link="libxml2-schemasInternals.html#XML_SCHEMAS_NMTOKEN"/>
    <function name="XML_SCHEMAS_NMTOKENS" link="libxml2-schemasInternals.html#XML_SCHEMAS_NMTOKENS"/>
    <function name="XML_SCHEMAS_NNINTEGER" link="libxml2-schemasInternals.html#XML_SCHEMAS_NNINTEGER"/>
    <function name="XML_SCHEMAS_NORMSTRING" link="libxml2-schemasInternals.html#XML_SCHEMAS_NORMSTRING"/>
    <function name="XML_SCHEMAS_NOTATION" link="libxml2-schemasInternals.html#XML_SCHEMAS_NOTATION"/>
    <function name="XML_SCHEMAS_NPINTEGER" link="libxml2-schemasInternals.html#XML_SCHEMAS_NPINTEGER"/>
    <function name="XML_SCHEMAS_PINTEGER" link="libxml2-schemasInternals.html#XML_SCHEMAS_PINTEGER"/>
    <function name="XML_SCHEMAS_QNAME" link="libxml2-schemasInternals.html#XML_SCHEMAS_QNAME"/>
    <function name="XML_SCHEMAS_SHORT" link="libxml2-schemasInternals.html#XML_SCHEMAS_SHORT"/>
    <function name="XML_SCHEMAS_STRING" link="libxml2-schemasInternals.html#XML_SCHEMAS_STRING"/>
    <function name="XML_SCHEMAS_TIME" link="libxml2-schemasInternals.html#XML_SCHEMAS_TIME"/>
    <function name="XML_SCHEMAS_TOKEN" link="libxml2-schemasInternals.html#XML_SCHEMAS_TOKEN"/>
    <function name="XML_SCHEMAS_UBYTE" link="libxml2-schemasInternals.html#XML_SCHEMAS_UBYTE"/>
    <function name="XML_SCHEMAS_UINT" link="libxml2-schemasInternals.html#XML_SCHEMAS_UINT"/>
    <function name="XML_SCHEMAS_ULONG" link="libxml2-schemasInternals.html#XML_SCHEMAS_ULONG"/>
    <function name="XML_SCHEMAS_UNKNOWN" link="libxml2-schemasInternals.html#XML_SCHEMAS_UNKNOWN"/>
    <function name="XML_SCHEMAS_USHORT" link="libxml2-schemasInternals.html#XML_SCHEMAS_USHORT"/>
    <function name="XML_SCHEMATRONV_ASSERT" link="libxml2-xmlerror.html#XML_SCHEMATRONV_ASSERT"/>
    <function name="XML_SCHEMATRONV_REPORT" link="libxml2-xmlerror.html#XML_SCHEMATRONV_REPORT"/>
    <function name="XML_SCHEMATRON_OUT_BUFFER" link="libxml2-schematron.html#XML_SCHEMATRON_OUT_BUFFER"/>
    <function name="XML_SCHEMATRON_OUT_ERROR" link="libxml2-schematron.html#XML_SCHEMATRON_OUT_ERROR"/>
    <function name="XML_SCHEMATRON_OUT_FILE" link="libxml2-schematron.html#XML_SCHEMATRON_OUT_FILE"/>
    <function name="XML_SCHEMATRON_OUT_IO" link="libxml2-schematron.html#XML_SCHEMATRON_OUT_IO"/>
    <function name="XML_SCHEMATRON_OUT_QUIET" link="libxml2-schematron.html#XML_SCHEMATRON_OUT_QUIET"/>
    <function name="XML_SCHEMATRON_OUT_TEXT" link="libxml2-schematron.html#XML_SCHEMATRON_OUT_TEXT"/>
    <function name="XML_SCHEMATRON_OUT_XML" link="libxml2-schematron.html#XML_SCHEMATRON_OUT_XML"/>
    <function name="XML_SCHEMAV_ATTRINVALID" link="libxml2-xmlerror.html#XML_SCHEMAV_ATTRINVALID"/>
    <function name="XML_SCHEMAV_ATTRUNKNOWN" link="libxml2-xmlerror.html#XML_SCHEMAV_ATTRUNKNOWN"/>
    <function name="XML_SCHEMAV_CONSTRUCT" link="libxml2-xmlerror.html#XML_SCHEMAV_CONSTRUCT"/>
    <function name="XML_SCHEMAV_CVC_ATTRIBUTE_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ATTRIBUTE_1"/>
    <function name="XML_SCHEMAV_CVC_ATTRIBUTE_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ATTRIBUTE_2"/>
    <function name="XML_SCHEMAV_CVC_ATTRIBUTE_3" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ATTRIBUTE_3"/>
    <function name="XML_SCHEMAV_CVC_ATTRIBUTE_4" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ATTRIBUTE_4"/>
    <function name="XML_SCHEMAV_CVC_AU" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_AU"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_1"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_2_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_2_1"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_2_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_2_2"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_2_3" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_2_3"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_2_4" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_2_4"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_3_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_3_1"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_3_2_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_3_2_1"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_3_2_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_3_2_2"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_4" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_4"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_5_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_5_1"/>
    <function name="XML_SCHEMAV_CVC_COMPLEX_TYPE_5_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_COMPLEX_TYPE_5_2"/>
    <function name="XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_1"/>
    <function name="XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_2"/>
    <function name="XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_3" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_DATATYPE_VALID_1_2_3"/>
    <function name="XML_SCHEMAV_CVC_ELT_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_1"/>
    <function name="XML_SCHEMAV_CVC_ELT_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_2"/>
    <function name="XML_SCHEMAV_CVC_ELT_3_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_3_1"/>
    <function name="XML_SCHEMAV_CVC_ELT_3_2_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_3_2_1"/>
    <function name="XML_SCHEMAV_CVC_ELT_3_2_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_3_2_2"/>
    <function name="XML_SCHEMAV_CVC_ELT_4_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_4_1"/>
    <function name="XML_SCHEMAV_CVC_ELT_4_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_4_2"/>
    <function name="XML_SCHEMAV_CVC_ELT_4_3" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_4_3"/>
    <function name="XML_SCHEMAV_CVC_ELT_5_1_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_5_1_1"/>
    <function name="XML_SCHEMAV_CVC_ELT_5_1_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_5_1_2"/>
    <function name="XML_SCHEMAV_CVC_ELT_5_2_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_5_2_1"/>
    <function name="XML_SCHEMAV_CVC_ELT_5_2_2_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_5_2_2_1"/>
    <function name="XML_SCHEMAV_CVC_ELT_5_2_2_2_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_5_2_2_2_1"/>
    <function name="XML_SCHEMAV_CVC_ELT_5_2_2_2_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_5_2_2_2_2"/>
    <function name="XML_SCHEMAV_CVC_ELT_6" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_6"/>
    <function name="XML_SCHEMAV_CVC_ELT_7" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ELT_7"/>
    <function name="XML_SCHEMAV_CVC_ENUMERATION_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_ENUMERATION_VALID"/>
    <function name="XML_SCHEMAV_CVC_FACET_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_FACET_VALID"/>
    <function name="XML_SCHEMAV_CVC_FRACTIONDIGITS_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_FRACTIONDIGITS_VALID"/>
    <function name="XML_SCHEMAV_CVC_IDC" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_IDC"/>
    <function name="XML_SCHEMAV_CVC_LENGTH_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_LENGTH_VALID"/>
    <function name="XML_SCHEMAV_CVC_MAXEXCLUSIVE_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_MAXEXCLUSIVE_VALID"/>
    <function name="XML_SCHEMAV_CVC_MAXINCLUSIVE_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_MAXINCLUSIVE_VALID"/>
    <function name="XML_SCHEMAV_CVC_MAXLENGTH_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_MAXLENGTH_VALID"/>
    <function name="XML_SCHEMAV_CVC_MINEXCLUSIVE_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_MINEXCLUSIVE_VALID"/>
    <function name="XML_SCHEMAV_CVC_MININCLUSIVE_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_MININCLUSIVE_VALID"/>
    <function name="XML_SCHEMAV_CVC_MINLENGTH_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_MINLENGTH_VALID"/>
    <function name="XML_SCHEMAV_CVC_PATTERN_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_PATTERN_VALID"/>
    <function name="XML_SCHEMAV_CVC_TOTALDIGITS_VALID" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_TOTALDIGITS_VALID"/>
    <function name="XML_SCHEMAV_CVC_TYPE_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_TYPE_1"/>
    <function name="XML_SCHEMAV_CVC_TYPE_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_TYPE_2"/>
    <function name="XML_SCHEMAV_CVC_TYPE_3_1_1" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_TYPE_3_1_1"/>
    <function name="XML_SCHEMAV_CVC_TYPE_3_1_2" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_TYPE_3_1_2"/>
    <function name="XML_SCHEMAV_CVC_WILDCARD" link="libxml2-xmlerror.html#XML_SCHEMAV_CVC_WILDCARD"/>
    <function name="XML_SCHEMAV_DOCUMENT_ELEMENT_MISSING" link="libxml2-xmlerror.html#XML_SCHEMAV_DOCUMENT_ELEMENT_MISSING"/>
    <function name="XML_SCHEMAV_ELEMCONT" link="libxml2-xmlerror.html#XML_SCHEMAV_ELEMCONT"/>
    <function name="XML_SCHEMAV_ELEMENT_CONTENT" link="libxml2-xmlerror.html#XML_SCHEMAV_ELEMENT_CONTENT"/>
    <function name="XML_SCHEMAV_EXTRACONTENT" link="libxml2-xmlerror.html#XML_SCHEMAV_EXTRACONTENT"/>
    <function name="XML_SCHEMAV_FACET" link="libxml2-xmlerror.html#XML_SCHEMAV_FACET"/>
    <function name="XML_SCHEMAV_HAVEDEFAULT" link="libxml2-xmlerror.html#XML_SCHEMAV_HAVEDEFAULT"/>
    <function name="XML_SCHEMAV_INTERNAL" link="libxml2-xmlerror.html#XML_SCHEMAV_INTERNAL"/>
    <function name="XML_SCHEMAV_INVALIDATTR" link="libxml2-xmlerror.html#XML_SCHEMAV_INVALIDATTR"/>
    <function name="XML_SCHEMAV_INVALIDELEM" link="libxml2-xmlerror.html#XML_SCHEMAV_INVALIDELEM"/>
    <function name="XML_SCHEMAV_ISABSTRACT" link="libxml2-xmlerror.html#XML_SCHEMAV_ISABSTRACT"/>
    <function name="XML_SCHEMAV_MISC" link="libxml2-xmlerror.html#XML_SCHEMAV_MISC"/>
    <function name="XML_SCHEMAV_MISSING" link="libxml2-xmlerror.html#XML_SCHEMAV_MISSING"/>
    <function name="XML_SCHEMAV_NOROLLBACK" link="libxml2-xmlerror.html#XML_SCHEMAV_NOROLLBACK"/>
    <function name="XML_SCHEMAV_NOROOT" link="libxml2-xmlerror.html#XML_SCHEMAV_NOROOT"/>
    <function name="XML_SCHEMAV_NOTDETERMINIST" link="libxml2-xmlerror.html#XML_SCHEMAV_NOTDETERMINIST"/>
    <function name="XML_SCHEMAV_NOTEMPTY" link="libxml2-xmlerror.html#XML_SCHEMAV_NOTEMPTY"/>
    <function name="XML_SCHEMAV_NOTNILLABLE" link="libxml2-xmlerror.html#XML_SCHEMAV_NOTNILLABLE"/>
    <function name="XML_SCHEMAV_NOTSIMPLE" link="libxml2-xmlerror.html#XML_SCHEMAV_NOTSIMPLE"/>
    <function name="XML_SCHEMAV_NOTTOPLEVEL" link="libxml2-xmlerror.html#XML_SCHEMAV_NOTTOPLEVEL"/>
    <function name="XML_SCHEMAV_NOTYPE" link="libxml2-xmlerror.html#XML_SCHEMAV_NOTYPE"/>
    <function name="XML_SCHEMAV_UNDECLAREDELEM" link="libxml2-xmlerror.html#XML_SCHEMAV_UNDECLAREDELEM"/>
    <function name="XML_SCHEMAV_VALUE" link="libxml2-xmlerror.html#XML_SCHEMAV_VALUE"/>
    <function name="XML_SCHEMAV_WRONGELEM" link="libxml2-xmlerror.html#XML_SCHEMAV_WRONGELEM"/>
    <function name="XML_SCHEMA_CONTENT_ANY" link="libxml2-schemasInternals.html#XML_SCHEMA_CONTENT_ANY"/>
    <function name="XML_SCHEMA_CONTENT_BASIC" link="libxml2-schemasInternals.html#XML_SCHEMA_CONTENT_BASIC"/>
    <function name="XML_SCHEMA_CONTENT_ELEMENTS" link="libxml2-schemasInternals.html#XML_SCHEMA_CONTENT_ELEMENTS"/>
    <function name="XML_SCHEMA_CONTENT_EMPTY" link="libxml2-schemasInternals.html#XML_SCHEMA_CONTENT_EMPTY"/>
    <function name="XML_SCHEMA_CONTENT_MIXED" link="libxml2-schemasInternals.html#XML_SCHEMA_CONTENT_MIXED"/>
    <function name="XML_SCHEMA_CONTENT_MIXED_OR_ELEMENTS" link="libxml2-schemasInternals.html#XML_SCHEMA_CONTENT_MIXED_OR_ELEMENTS"/>
    <function name="XML_SCHEMA_CONTENT_SIMPLE" link="libxml2-schemasInternals.html#XML_SCHEMA_CONTENT_SIMPLE"/>
    <function name="XML_SCHEMA_CONTENT_UNKNOWN" link="libxml2-schemasInternals.html#XML_SCHEMA_CONTENT_UNKNOWN"/>
    <function name="XML_SCHEMA_EXTRA_ATTR_USE_PROHIB" link="libxml2-schemasInternals.html#XML_SCHEMA_EXTRA_ATTR_USE_PROHIB"/>
    <function name="XML_SCHEMA_EXTRA_QNAMEREF" link="libxml2-schemasInternals.html#XML_SCHEMA_EXTRA_QNAMEREF"/>
    <function name="XML_SCHEMA_FACET_ENUMERATION" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_ENUMERATION"/>
    <function name="XML_SCHEMA_FACET_FRACTIONDIGITS" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_FRACTIONDIGITS"/>
    <function name="XML_SCHEMA_FACET_LENGTH" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_LENGTH"/>
    <function name="XML_SCHEMA_FACET_MAXEXCLUSIVE" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_MAXEXCLUSIVE"/>
    <function name="XML_SCHEMA_FACET_MAXINCLUSIVE" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_MAXINCLUSIVE"/>
    <function name="XML_SCHEMA_FACET_MAXLENGTH" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_MAXLENGTH"/>
    <function name="XML_SCHEMA_FACET_MINEXCLUSIVE" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_MINEXCLUSIVE"/>
    <function name="XML_SCHEMA_FACET_MININCLUSIVE" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_MININCLUSIVE"/>
    <function name="XML_SCHEMA_FACET_MINLENGTH" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_MINLENGTH"/>
    <function name="XML_SCHEMA_FACET_PATTERN" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_PATTERN"/>
    <function name="XML_SCHEMA_FACET_TOTALDIGITS" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_TOTALDIGITS"/>
    <function name="XML_SCHEMA_FACET_WHITESPACE" link="libxml2-schemasInternals.html#XML_SCHEMA_FACET_WHITESPACE"/>
    <function name="XML_SCHEMA_TYPE_ALL" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_ALL"/>
    <function name="XML_SCHEMA_TYPE_ANY" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_ANY"/>
    <function name="XML_SCHEMA_TYPE_ANY_ATTRIBUTE" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_ANY_ATTRIBUTE"/>
    <function name="XML_SCHEMA_TYPE_ATTRIBUTE" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_ATTRIBUTE"/>
    <function name="XML_SCHEMA_TYPE_ATTRIBUTEGROUP" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_ATTRIBUTEGROUP"/>
    <function name="XML_SCHEMA_TYPE_ATTRIBUTE_USE" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_ATTRIBUTE_USE"/>
    <function name="XML_SCHEMA_TYPE_BASIC" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_BASIC"/>
    <function name="XML_SCHEMA_TYPE_CHOICE" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_CHOICE"/>
    <function name="XML_SCHEMA_TYPE_COMPLEX" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_COMPLEX"/>
    <function name="XML_SCHEMA_TYPE_COMPLEX_CONTENT" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_COMPLEX_CONTENT"/>
    <function name="XML_SCHEMA_TYPE_ELEMENT" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_ELEMENT"/>
    <function name="XML_SCHEMA_TYPE_EXTENSION" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_EXTENSION"/>
    <function name="XML_SCHEMA_TYPE_FACET" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_FACET"/>
    <function name="XML_SCHEMA_TYPE_GROUP" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_GROUP"/>
    <function name="XML_SCHEMA_TYPE_IDC_KEY" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_IDC_KEY"/>
    <function name="XML_SCHEMA_TYPE_IDC_KEYREF" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_IDC_KEYREF"/>
    <function name="XML_SCHEMA_TYPE_IDC_UNIQUE" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_IDC_UNIQUE"/>
    <function name="XML_SCHEMA_TYPE_LIST" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_LIST"/>
    <function name="XML_SCHEMA_TYPE_NOTATION" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_NOTATION"/>
    <function name="XML_SCHEMA_TYPE_PARTICLE" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_PARTICLE"/>
    <function name="XML_SCHEMA_TYPE_RESTRICTION" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_RESTRICTION"/>
    <function name="XML_SCHEMA_TYPE_SEQUENCE" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_SEQUENCE"/>
    <function name="XML_SCHEMA_TYPE_SIMPLE" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_SIMPLE"/>
    <function name="XML_SCHEMA_TYPE_SIMPLE_CONTENT" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_SIMPLE_CONTENT"/>
    <function name="XML_SCHEMA_TYPE_UNION" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_UNION"/>
    <function name="XML_SCHEMA_TYPE_UR" link="libxml2-schemasInternals.html#XML_SCHEMA_TYPE_UR"/>
    <function name="XML_SCHEMA_VAL_VC_I_CREATE" link="libxml2-xmlschemas.html#XML_SCHEMA_VAL_VC_I_CREATE"/>
    <function name="XML_SCHEMA_WHITESPACE_COLLAPSE" link="libxml2-xmlschemastypes.html#XML_SCHEMA_WHITESPACE_COLLAPSE"/>
    <function name="XML_SCHEMA_WHITESPACE_PRESERVE" link="libxml2-xmlschemastypes.html#XML_SCHEMA_WHITESPACE_PRESERVE"/>
    <function name="XML_SCHEMA_WHITESPACE_REPLACE" link="libxml2-xmlschemastypes.html#XML_SCHEMA_WHITESPACE_REPLACE"/>
    <function name="XML_SCHEMA_WHITESPACE_UNKNOWN" link="libxml2-xmlschemastypes.html#XML_SCHEMA_WHITESPACE_UNKNOWN"/>
    <function name="XML_TEXTREADER_MODE_CLOSED" link="libxml2-xmlreader.html#XML_TEXTREADER_MODE_CLOSED"/>
    <function name="XML_TEXTREADER_MODE_EOF" link="libxml2-xmlreader.html#XML_TEXTREADER_MODE_EOF"/>
    <function name="XML_TEXTREADER_MODE_ERROR" link="libxml2-xmlreader.html#XML_TEXTREADER_MODE_ERROR"/>
    <function name="XML_TEXTREADER_MODE_INITIAL" link="libxml2-xmlreader.html#XML_TEXTREADER_MODE_INITIAL"/>
    <function name="XML_TEXTREADER_MODE_INTERACTIVE" link="libxml2-xmlreader.html#XML_TEXTREADER_MODE_INTERACTIVE"/>
    <function name="XML_TEXTREADER_MODE_READING" link="libxml2-xmlreader.html#XML_TEXTREADER_MODE_READING"/>
    <function name="XML_TEXT_NODE" link="libxml2-tree.html#XML_TEXT_NODE"/>
    <function name="XML_TREE_INVALID_DEC" link="libxml2-xmlerror.html#XML_TREE_INVALID_DEC"/>
    <function name="XML_TREE_INVALID_HEX" link="libxml2-xmlerror.html#XML_TREE_INVALID_HEX"/>
    <function name="XML_TREE_NOT_UTF8" link="libxml2-xmlerror.html#XML_TREE_NOT_UTF8"/>
    <function name="XML_TREE_UNTERMINATED_ENTITY" link="libxml2-xmlerror.html#XML_TREE_UNTERMINATED_ENTITY"/>
    <function name="XML_WAR_CATALOG_PI" link="libxml2-xmlerror.html#XML_WAR_CATALOG_PI"/>
    <function name="XML_WAR_ENTITY_REDEFINED" link="libxml2-xmlerror.html#XML_WAR_ENTITY_REDEFINED"/>
    <function name="XML_WAR_LANG_VALUE" link="libxml2-xmlerror.html#XML_WAR_LANG_VALUE"/>
    <function name="XML_WAR_NS_COLUMN" link="libxml2-xmlerror.html#XML_WAR_NS_COLUMN"/>
    <function name="XML_WAR_NS_URI" link="libxml2-xmlerror.html#XML_WAR_NS_URI"/>
    <function name="XML_WAR_NS_URI_RELATIVE" link="libxml2-xmlerror.html#XML_WAR_NS_URI_RELATIVE"/>
    <function name="XML_WAR_SPACE_VALUE" link="libxml2-xmlerror.html#XML_WAR_SPACE_VALUE"/>
    <function name="XML_WAR_UNDECLARED_ENTITY" link="libxml2-xmlerror.html#XML_WAR_UNDECLARED_ENTITY"/>
    <function name="XML_WAR_UNKNOWN_VERSION" link="libxml2-xmlerror.html#XML_WAR_UNKNOWN_VERSION"/>
    <function name="XML_WITH_AUTOMATA" link="libxml2-parser.html#XML_WITH_AUTOMATA"/>
    <function name="XML_WITH_C14N" link="libxml2-parser.html#XML_WITH_C14N"/>
    <function name="XML_WITH_CATALOG" link="libxml2-parser.html#XML_WITH_CATALOG"/>
    <function name="XML_WITH_DEBUG" link="libxml2-parser.html#XML_WITH_DEBUG"/>
    <function name="XML_WITH_DEBUG_MEM" link="libxml2-parser.html#XML_WITH_DEBUG_MEM"/>
    <function name="XML_WITH_DEBUG_RUN" link="libxml2-parser.html#XML_WITH_DEBUG_RUN"/>
    <function name="XML_WITH_EXPR" link="libxml2-parser.html#XML_WITH_EXPR"/>
    <function name="XML_WITH_FTP" link="libxml2-parser.html#XML_WITH_FTP"/>
    <function name="XML_WITH_HTML" link="libxml2-parser.html#XML_WITH_HTML"/>
    <function name="XML_WITH_HTTP" link="libxml2-parser.html#XML_WITH_HTTP"/>
    <function name="XML_WITH_ICONV" link="libxml2-parser.html#XML_WITH_ICONV"/>
    <function name="XML_WITH_ICU" link="libxml2-parser.html#XML_WITH_ICU"/>
    <function name="XML_WITH_ISO8859X" link="libxml2-parser.html#XML_WITH_ISO8859X"/>
    <function name="XML_WITH_LEGACY" link="libxml2-parser.html#XML_WITH_LEGACY"/>
    <function name="XML_WITH_LZMA" link="libxml2-parser.html#XML_WITH_LZMA"/>
    <function name="XML_WITH_MODULES" link="libxml2-parser.html#XML_WITH_MODULES"/>
    <function name="XML_WITH_NONE" link="libxml2-parser.html#XML_WITH_NONE"/>
    <function name="XML_WITH_OUTPUT" link="libxml2-parser.html#XML_WITH_OUTPUT"/>
    <function name="XML_WITH_PATTERN" link="libxml2-parser.html#XML_WITH_PATTERN"/>
    <function name="XML_WITH_PUSH" link="libxml2-parser.html#XML_WITH_PUSH"/>
    <function name="XML_WITH_READER" link="libxml2-parser.html#XML_WITH_READER"/>
    <function name="XML_WITH_REGEXP" link="libxml2-parser.html#XML_WITH_REGEXP"/>
    <function name="XML_WITH_SAX1" link="libxml2-parser.html#XML_WITH_SAX1"/>
    <function name="XML_WITH_SCHEMAS" link="libxml2-parser.html#XML_WITH_SCHEMAS"/>
    <function name="XML_WITH_SCHEMATRON" link="libxml2-parser.html#XML_WITH_SCHEMATRON"/>
    <function name="XML_WITH_THREAD" link="libxml2-parser.html#XML_WITH_THREAD"/>
    <function name="XML_WITH_TREE" link="libxml2-parser.html#XML_WITH_TREE"/>
    <function name="XML_WITH_UNICODE" link="libxml2-parser.html#XML_WITH_UNICODE"/>
    <function name="XML_WITH_VALID" link="libxml2-parser.html#XML_WITH_VALID"/>
    <function name="XML_WITH_WRITER" link="libxml2-parser.html#XML_WITH_WRITER"/>
    <function name="XML_WITH_XINCLUDE" link="libxml2-parser.html#XML_WITH_XINCLUDE"/>
    <function name="XML_WITH_XPATH" link="libxml2-parser.html#XML_WITH_XPATH"/>
    <function name="XML_WITH_XPTR" link="libxml2-parser.html#XML_WITH_XPTR"/>
    <function name="XML_WITH_ZLIB" link="libxml2-parser.html#XML_WITH_ZLIB"/>
    <function name="XML_XINCLUDE_BUILD_FAILED" link="libxml2-xmlerror.html#XML_XINCLUDE_BUILD_FAILED"/>
    <function name="XML_XINCLUDE_DEPRECATED_NS" link="libxml2-xmlerror.html#XML_XINCLUDE_DEPRECATED_NS"/>
    <function name="XML_XINCLUDE_END" link="libxml2-tree.html#XML_XINCLUDE_END"/>
    <function name="XML_XINCLUDE_ENTITY_DEF_MISMATCH" link="libxml2-xmlerror.html#XML_XINCLUDE_ENTITY_DEF_MISMATCH"/>
    <function name="XML_XINCLUDE_FALLBACKS_IN_INCLUDE" link="libxml2-xmlerror.html#XML_XINCLUDE_FALLBACKS_IN_INCLUDE"/>
    <function name="XML_XINCLUDE_FALLBACK_NOT_IN_INCLUDE" link="libxml2-xmlerror.html#XML_XINCLUDE_FALLBACK_NOT_IN_INCLUDE"/>
    <function name="XML_XINCLUDE_FRAGMENT_ID" link="libxml2-xmlerror.html#XML_XINCLUDE_FRAGMENT_ID"/>
    <function name="XML_XINCLUDE_HREF_URI" link="libxml2-xmlerror.html#XML_XINCLUDE_HREF_URI"/>
    <function name="XML_XINCLUDE_INCLUDE_IN_INCLUDE" link="libxml2-xmlerror.html#XML_XINCLUDE_INCLUDE_IN_INCLUDE"/>
    <function name="XML_XINCLUDE_INVALID_CHAR" link="libxml2-xmlerror.html#XML_XINCLUDE_INVALID_CHAR"/>
    <function name="XML_XINCLUDE_MULTIPLE_ROOT" link="libxml2-xmlerror.html#XML_XINCLUDE_MULTIPLE_ROOT"/>
    <function name="XML_XINCLUDE_NO_FALLBACK" link="libxml2-xmlerror.html#XML_XINCLUDE_NO_FALLBACK"/>
    <function name="XML_XINCLUDE_NO_HREF" link="libxml2-xmlerror.html#XML_XINCLUDE_NO_HREF"/>
    <function name="XML_XINCLUDE_PARSE_VALUE" link="libxml2-xmlerror.html#XML_XINCLUDE_PARSE_VALUE"/>
    <function name="XML_XINCLUDE_RECURSION" link="libxml2-xmlerror.html#XML_XINCLUDE_RECURSION"/>
    <function name="XML_XINCLUDE_START" link="libxml2-tree.html#XML_XINCLUDE_START"/>
    <function name="XML_XINCLUDE_TEXT_DOCUMENT" link="libxml2-xmlerror.html#XML_XINCLUDE_TEXT_DOCUMENT"/>
    <function name="XML_XINCLUDE_TEXT_FRAGMENT" link="libxml2-xmlerror.html#XML_XINCLUDE_TEXT_FRAGMENT"/>
    <function name="XML_XINCLUDE_UNKNOWN_ENCODING" link="libxml2-xmlerror.html#XML_XINCLUDE_UNKNOWN_ENCODING"/>
    <function name="XML_XINCLUDE_XPTR_FAILED" link="libxml2-xmlerror.html#XML_XINCLUDE_XPTR_FAILED"/>
    <function name="XML_XINCLUDE_XPTR_RESULT" link="libxml2-xmlerror.html#XML_XINCLUDE_XPTR_RESULT"/>
    <function name="XML_XPATH_ENCODING_ERROR" link="libxml2-xmlerror.html#XML_XPATH_ENCODING_ERROR"/>
    <function name="XML_XPATH_EXPRESSION_OK" link="libxml2-xmlerror.html#XML_XPATH_EXPRESSION_OK"/>
    <function name="XML_XPATH_EXPR_ERROR" link="libxml2-xmlerror.html#XML_XPATH_EXPR_ERROR"/>
    <function name="XML_XPATH_INVALID_ARITY" link="libxml2-xmlerror.html#XML_XPATH_INVALID_ARITY"/>
    <function name="XML_XPATH_INVALID_CHAR_ERROR" link="libxml2-xmlerror.html#XML_XPATH_INVALID_CHAR_ERROR"/>
    <function name="XML_XPATH_INVALID_CTXT_POSITION" link="libxml2-xmlerror.html#XML_XPATH_INVALID_CTXT_POSITION"/>
    <function name="XML_XPATH_INVALID_CTXT_SIZE" link="libxml2-xmlerror.html#XML_XPATH_INVALID_CTXT_SIZE"/>
    <function name="XML_XPATH_INVALID_OPERAND" link="libxml2-xmlerror.html#XML_XPATH_INVALID_OPERAND"/>
    <function name="XML_XPATH_INVALID_PREDICATE_ERROR" link="libxml2-xmlerror.html#XML_XPATH_INVALID_PREDICATE_ERROR"/>
    <function name="XML_XPATH_INVALID_TYPE" link="libxml2-xmlerror.html#XML_XPATH_INVALID_TYPE"/>
    <function name="XML_XPATH_MEMORY_ERROR" link="libxml2-xmlerror.html#XML_XPATH_MEMORY_ERROR"/>
    <function name="XML_XPATH_NUMBER_ERROR" link="libxml2-xmlerror.html#XML_XPATH_NUMBER_ERROR"/>
    <function name="XML_XPATH_START_LITERAL_ERROR" link="libxml2-xmlerror.html#XML_XPATH_START_LITERAL_ERROR"/>
    <function name="XML_XPATH_UNCLOSED_ERROR" link="libxml2-xmlerror.html#XML_XPATH_UNCLOSED_ERROR"/>
    <function name="XML_XPATH_UNDEF_PREFIX_ERROR" link="libxml2-xmlerror.html#XML_XPATH_UNDEF_PREFIX_ERROR"/>
    <function name="XML_XPATH_UNDEF_VARIABLE_ERROR" link="libxml2-xmlerror.html#XML_XPATH_UNDEF_VARIABLE_ERROR"/>
    <function name="XML_XPATH_UNFINISHED_LITERAL_ERROR" link="libxml2-xmlerror.html#XML_XPATH_UNFINISHED_LITERAL_ERROR"/>
    <function name="XML_XPATH_UNKNOWN_FUNC_ERROR" link="libxml2-xmlerror.html#XML_XPATH_UNKNOWN_FUNC_ERROR"/>
    <function name="XML_XPATH_VARIABLE_REF_ERROR" link="libxml2-xmlerror.html#XML_XPATH_VARIABLE_REF_ERROR"/>
    <function name="XML_XPTR_CHILDSEQ_START" link="libxml2-xmlerror.html#XML_XPTR_CHILDSEQ_START"/>
    <function name="XML_XPTR_EVAL_FAILED" link="libxml2-xmlerror.html#XML_XPTR_EVAL_FAILED"/>
    <function name="XML_XPTR_EXTRA_OBJECTS" link="libxml2-xmlerror.html#XML_XPTR_EXTRA_OBJECTS"/>
    <function name="XML_XPTR_RESOURCE_ERROR" link="libxml2-xmlerror.html#XML_XPTR_RESOURCE_ERROR"/>
    <function name="XML_XPTR_SUB_RESOURCE_ERROR" link="libxml2-xmlerror.html#XML_XPTR_SUB_RESOURCE_ERROR"/>
    <function name="XML_XPTR_SYNTAX_ERROR" link="libxml2-xmlerror.html#XML_XPTR_SYNTAX_ERROR"/>
    <function name="XML_XPTR_UNKNOWN_SCHEME" link="libxml2-xmlerror.html#XML_XPTR_UNKNOWN_SCHEME"/>
    <function name="XPATH_BOOLEAN" link="libxml2-xpath.html#XPATH_BOOLEAN"/>
    <function name="XPATH_ENCODING_ERROR" link="libxml2-xpath.html#XPATH_ENCODING_ERROR"/>
    <function name="XPATH_EXPRESSION_OK" link="libxml2-xpath.html#XPATH_EXPRESSION_OK"/>
    <function name="XPATH_EXPR_ERROR" link="libxml2-xpath.html#XPATH_EXPR_ERROR"/>
    <function name="XPATH_FORBID_VARIABLE_ERROR" link="libxml2-xpath.html#XPATH_FORBID_VARIABLE_ERROR"/>
    <function name="XPATH_INVALID_ARITY" link="libxml2-xpath.html#XPATH_INVALID_ARITY"/>
    <function name="XPATH_INVALID_CHAR_ERROR" link="libxml2-xpath.html#XPATH_INVALID_CHAR_ERROR"/>
    <function name="XPATH_INVALID_CTXT" link="libxml2-xpath.html#XPATH_INVALID_CTXT"/>
    <function name="XPATH_INVALID_CTXT_POSITION" link="libxml2-xpath.html#XPATH_INVALID_CTXT_POSITION"/>
    <function name="XPATH_INVALID_CTXT_SIZE" link="libxml2-xpath.html#XPATH_INVALID_CTXT_SIZE"/>
    <function name="XPATH_INVALID_OPERAND" link="libxml2-xpath.html#XPATH_INVALID_OPERAND"/>
    <function name="XPATH_INVALID_PREDICATE_ERROR" link="libxml2-xpath.html#XPATH_INVALID_PREDICATE_ERROR"/>
    <function name="XPATH_INVALID_TYPE" link="libxml2-xpath.html#XPATH_INVALID_TYPE"/>
    <function name="XPATH_LOCATIONSET" link="libxml2-xpath.html#XPATH_LOCATIONSET"/>
    <function name="XPATH_MEMORY_ERROR" link="libxml2-xpath.html#XPATH_MEMORY_ERROR"/>
    <function name="XPATH_NODESET" link="libxml2-xpath.html#XPATH_NODESET"/>
    <function name="XPATH_NUMBER" link="libxml2-xpath.html#XPATH_NUMBER"/>
    <function name="XPATH_NUMBER_ERROR" link="libxml2-xpath.html#XPATH_NUMBER_ERROR"/>
    <function name="XPATH_POINT" link="libxml2-xpath.html#XPATH_POINT"/>
    <function name="XPATH_RANGE" link="libxml2-xpath.html#XPATH_RANGE"/>
    <function name="XPATH_STACK_ERROR" link="libxml2-xpath.html#XPATH_STACK_ERROR"/>
    <function name="XPATH_START_LITERAL_ERROR" link="libxml2-xpath.html#XPATH_START_LITERAL_ERROR"/>
    <function name="XPATH_STRING" link="libxml2-xpath.html#XPATH_STRING"/>
    <function name="XPATH_UNCLOSED_ERROR" link="libxml2-xpath.html#XPATH_UNCLOSED_ERROR"/>
    <function name="XPATH_UNDEFINED" link="libxml2-xpath.html#XPATH_UNDEFINED"/>
    <function name="XPATH_UNDEF_PREFIX_ERROR" link="libxml2-xpath.html#XPATH_UNDEF_PREFIX_ERROR"/>
    <function name="XPATH_UNDEF_VARIABLE_ERROR" link="libxml2-xpath.html#XPATH_UNDEF_VARIABLE_ERROR"/>
    <function name="XPATH_UNFINISHED_LITERAL_ERROR" link="libxml2-xpath.html#XPATH_UNFINISHED_LITERAL_ERROR"/>
    <function name="XPATH_UNKNOWN_FUNC_ERROR" link="libxml2-xpath.html#XPATH_UNKNOWN_FUNC_ERROR"/>
    <function name="XPATH_USERS" link="libxml2-xpath.html#XPATH_USERS"/>
    <function name="XPATH_VARIABLE_REF_ERROR" link="libxml2-xpath.html#XPATH_VARIABLE_REF_ERROR"/>
    <function name="XPATH_XSLT_TREE" link="libxml2-xpath.html#XPATH_XSLT_TREE"/>
    <function name="XPTR_RESOURCE_ERROR" link="libxml2-xpath.html#XPTR_RESOURCE_ERROR"/>
    <function name="XPTR_SUB_RESOURCE_ERROR" link="libxml2-xpath.html#XPTR_SUB_RESOURCE_ERROR"/>
    <function name="XPTR_SYNTAX_ERROR" link="libxml2-xpath.html#XPTR_SYNTAX_ERROR"/>
    <function name="docbDocPtr" link="libxml2-DOCBparser.html#docbDocPtr"/>
    <function name="docbParserCtxt" link="libxml2-DOCBparser.html#docbParserCtxt"/>
    <function name="docbParserCtxtPtr" link="libxml2-DOCBparser.html#docbParserCtxtPtr"/>
    <function name="docbParserInput" link="libxml2-DOCBparser.html#docbParserInput"/>
    <function name="docbParserInputPtr" link="libxml2-DOCBparser.html#docbParserInputPtr"/>
    <function name="docbSAXHandler" link="libxml2-DOCBparser.html#docbSAXHandler"/>
    <function name="docbSAXHandlerPtr" link="libxml2-DOCBparser.html#docbSAXHandlerPtr"/>
    <function name="htmlDocPtr" link="libxml2-HTMLparser.html#htmlDocPtr"/>
    <function name="htmlElemDescPtr" link="libxml2-HTMLparser.html#htmlElemDescPtr"/>
    <function name="htmlEntityDescPtr" link="libxml2-HTMLparser.html#htmlEntityDescPtr"/>
    <function name="htmlNodePtr" link="libxml2-HTMLparser.html#htmlNodePtr"/>
    <function name="htmlParserCtxt" link="libxml2-HTMLparser.html#htmlParserCtxt"/>
    <function name="htmlParserCtxtPtr" link="libxml2-HTMLparser.html#htmlParserCtxtPtr"/>
    <function name="htmlParserInput" link="libxml2-HTMLparser.html#htmlParserInput"/>
    <function name="htmlParserInputPtr" link="libxml2-HTMLparser.html#htmlParserInputPtr"/>
    <function name="htmlParserNodeInfo" link="libxml2-HTMLparser.html#htmlParserNodeInfo"/>
    <function name="htmlParserOption" link="libxml2-HTMLparser.html#htmlParserOption"/>
    <function name="htmlSAXHandler" link="libxml2-HTMLparser.html#htmlSAXHandler"/>
    <function name="htmlSAXHandlerPtr" link="libxml2-HTMLparser.html#htmlSAXHandlerPtr"/>
    <function name="htmlStatus" link="libxml2-HTMLparser.html#htmlStatus"/>
    <function name="xlinkActuate" link="libxml2-xlink.html#xlinkActuate"/>
    <function name="xlinkHRef" link="libxml2-xlink.html#xlinkHRef"/>
    <function name="xlinkHandlerPtr" link="libxml2-xlink.html#xlinkHandlerPtr"/>
    <function name="xlinkRole" link="libxml2-xlink.html#xlinkRole"/>
    <function name="xlinkShow" link="libxml2-xlink.html#xlinkShow"/>
    <function name="xlinkTitle" link="libxml2-xlink.html#xlinkTitle"/>
    <function name="xlinkType" link="libxml2-xlink.html#xlinkType"/>
    <function name="xmlAttrPtr" link="libxml2-tree.html#xmlAttrPtr"/>
    <function name="xmlAttributeDefault" link="libxml2-tree.html#xmlAttributeDefault"/>
    <function name="xmlAttributePtr" link="libxml2-tree.html#xmlAttributePtr"/>
    <function name="xmlAttributeTablePtr" link="libxml2-valid.html#xmlAttributeTablePtr"/>
    <function name="xmlAttributeType" link="libxml2-tree.html#xmlAttributeType"/>
    <function name="xmlAutomataPtr" link="libxml2-xmlautomata.html#xmlAutomataPtr"/>
    <function name="xmlAutomataStatePtr" link="libxml2-xmlautomata.html#xmlAutomataStatePtr"/>
    <function name="xmlBufPtr" link="libxml2-tree.html#xmlBufPtr"/>
    <function name="xmlBufferAllocationScheme" link="libxml2-tree.html#xmlBufferAllocationScheme"/>
    <function name="xmlBufferPtr" link="libxml2-tree.html#xmlBufferPtr"/>
    <function name="xmlC14NMode" link="libxml2-c14n.html#xmlC14NMode"/>
    <function name="xmlCatalogAllow" link="libxml2-catalog.html#xmlCatalogAllow"/>
    <function name="xmlCatalogPrefer" link="libxml2-catalog.html#xmlCatalogPrefer"/>
    <function name="xmlCatalogPtr" link="libxml2-catalog.html#xmlCatalogPtr"/>
    <function name="xmlChLRangePtr" link="libxml2-chvalid.html#xmlChLRangePtr"/>
    <function name="xmlChRangeGroupPtr" link="libxml2-chvalid.html#xmlChRangeGroupPtr"/>
    <function name="xmlChSRangePtr" link="libxml2-chvalid.html#xmlChSRangePtr"/>
    <function name="xmlChar" link="libxml2-xmlstring.html#xmlChar"/>
    <function name="xmlCharEncoding" link="libxml2-encoding.html#xmlCharEncoding"/>
    <function name="xmlCharEncodingHandlerPtr" link="libxml2-encoding.html#xmlCharEncodingHandlerPtr"/>
    <function name="xmlDOMWrapCtxtPtr" link="libxml2-tree.html#xmlDOMWrapCtxtPtr"/>
    <function name="xmlDictPtr" link="libxml2-dict.html#xmlDictPtr"/>
    <function name="xmlDocProperties" link="libxml2-tree.html#xmlDocProperties"/>
    <function name="xmlDocPtr" link="libxml2-tree.html#xmlDocPtr"/>
    <function name="xmlDtdPtr" link="libxml2-tree.html#xmlDtdPtr"/>
    <function name="xmlElementContentOccur" link="libxml2-tree.html#xmlElementContentOccur"/>
    <function name="xmlElementContentPtr" link="libxml2-tree.html#xmlElementContentPtr"/>
    <function name="xmlElementContentType" link="libxml2-tree.html#xmlElementContentType"/>
    <function name="xmlElementPtr" link="libxml2-tree.html#xmlElementPtr"/>
    <function name="xmlElementTablePtr" link="libxml2-valid.html#xmlElementTablePtr"/>
    <function name="xmlElementType" link="libxml2-tree.html#xmlElementType"/>
    <function name="xmlElementTypeVal" link="libxml2-tree.html#xmlElementTypeVal"/>
    <function name="xmlEntitiesTablePtr" link="libxml2-entities.html#xmlEntitiesTablePtr"/>
    <function name="xmlEntityPtr" link="libxml2-tree.html#xmlEntityPtr"/>
    <function name="xmlEntityType" link="libxml2-entities.html#xmlEntityType"/>
    <function name="xmlEnumerationPtr" link="libxml2-tree.html#xmlEnumerationPtr"/>
    <function name="xmlErrorDomain" link="libxml2-xmlerror.html#xmlErrorDomain"/>
    <function name="xmlErrorLevel" link="libxml2-xmlerror.html#xmlErrorLevel"/>
    <function name="xmlErrorPtr" link="libxml2-xmlerror.html#xmlErrorPtr"/>
    <function name="xmlExpCtxtPtr" link="libxml2-xmlregexp.html#xmlExpCtxtPtr"/>
    <function name="xmlExpNodePtr" link="libxml2-xmlregexp.html#xmlExpNodePtr"/>
    <function name="xmlExpNodeType" link="libxml2-xmlregexp.html#xmlExpNodeType"/>
    <function name="xmlFeature" link="libxml2-parser.html#xmlFeature"/>
    <function name="xmlGlobalStatePtr" link="libxml2-globals.html#xmlGlobalStatePtr"/>
    <function name="xmlHashTablePtr" link="libxml2-hash.html#xmlHashTablePtr"/>
    <function name="xmlIDPtr" link="libxml2-tree.html#xmlIDPtr"/>
    <function name="xmlIDTablePtr" link="libxml2-valid.html#xmlIDTablePtr"/>
    <function name="xmlLinkPtr" link="libxml2-list.html#xmlLinkPtr"/>
    <function name="xmlListPtr" link="libxml2-list.html#xmlListPtr"/>
    <function name="xmlLocationSetPtr" link="libxml2-xpointer.html#xmlLocationSetPtr"/>
    <function name="xmlModuleOption" link="libxml2-xmlmodule.html#xmlModuleOption"/>
    <function name="xmlModulePtr" link="libxml2-xmlmodule.html#xmlModulePtr"/>
    <function name="xmlMutexPtr" link="libxml2-threads.html#xmlMutexPtr"/>
    <function name="xmlNodePtr" link="libxml2-tree.html#xmlNodePtr"/>
    <function name="xmlNodeSetPtr" link="libxml2-xpath.html#xmlNodeSetPtr"/>
    <function name="xmlNotationPtr" link="libxml2-tree.html#xmlNotationPtr"/>
    <function name="xmlNotationTablePtr" link="libxml2-valid.html#xmlNotationTablePtr"/>
    <function name="xmlNsPtr" link="libxml2-tree.html#xmlNsPtr"/>
    <function name="xmlNsType" link="libxml2-tree.html#xmlNsType"/>
    <function name="xmlOutputBufferPtr" link="libxml2-tree.html#xmlOutputBufferPtr"/>
    <function name="xmlParserCtxtPtr" link="libxml2-tree.html#xmlParserCtxtPtr"/>
    <function name="xmlParserErrors" link="libxml2-xmlerror.html#xmlParserErrors"/>
    <function name="xmlParserInputBufferPtr" link="libxml2-tree.html#xmlParserInputBufferPtr"/>
    <function name="xmlParserInputPtr" link="libxml2-tree.html#xmlParserInputPtr"/>
    <function name="xmlParserInputState" link="libxml2-parser.html#xmlParserInputState"/>
    <function name="xmlParserMode" link="libxml2-parser.html#xmlParserMode"/>
    <function name="xmlParserNodeInfoPtr" link="libxml2-parser.html#xmlParserNodeInfoPtr"/>
    <function name="xmlParserNodeInfoSeqPtr" link="libxml2-parser.html#xmlParserNodeInfoSeqPtr"/>
    <function name="xmlParserOption" link="libxml2-parser.html#xmlParserOption"/>
    <function name="xmlParserProperties" link="libxml2-xmlreader.html#xmlParserProperties"/>
    <function name="xmlParserSeverities" link="libxml2-xmlreader.html#xmlParserSeverities"/>
    <function name="xmlPatternFlags" link="libxml2-pattern.html#xmlPatternFlags"/>
    <function name="xmlPatternPtr" link="libxml2-pattern.html#xmlPatternPtr"/>
    <function name="xmlRMutexPtr" link="libxml2-threads.html#xmlRMutexPtr"/>
    <function name="xmlReaderTypes" link="libxml2-xmlreader.html#xmlReaderTypes"/>
    <function name="xmlRefPtr" link="libxml2-tree.html#xmlRefPtr"/>
    <function name="xmlRefTablePtr" link="libxml2-valid.html#xmlRefTablePtr"/>
    <function name="xmlRegExecCtxtPtr" link="libxml2-xmlregexp.html#xmlRegExecCtxtPtr"/>
    <function name="xmlRegexpPtr" link="libxml2-xmlregexp.html#xmlRegexpPtr"/>
    <function name="xmlRelaxNGParserCtxtPtr" link="libxml2-relaxng.html#xmlRelaxNGParserCtxtPtr"/>
    <function name="xmlRelaxNGParserFlag" link="libxml2-relaxng.html#xmlRelaxNGParserFlag"/>
    <function name="xmlRelaxNGPtr" link="libxml2-relaxng.html#xmlRelaxNGPtr"/>
    <function name="xmlRelaxNGValidCtxtPtr" link="libxml2-relaxng.html#xmlRelaxNGValidCtxtPtr"/>
    <function name="xmlRelaxNGValidErr" link="libxml2-relaxng.html#xmlRelaxNGValidErr"/>
    <function name="xmlSAXHandlerPtr" link="libxml2-tree.html#xmlSAXHandlerPtr"/>
    <function name="xmlSAXHandlerV1Ptr" link="libxml2-parser.html#xmlSAXHandlerV1Ptr"/>
    <function name="xmlSAXLocatorPtr" link="libxml2-tree.html#xmlSAXLocatorPtr"/>
    <function name="xmlSaveCtxtPtr" link="libxml2-xmlsave.html#xmlSaveCtxtPtr"/>
    <function name="xmlSaveOption" link="libxml2-xmlsave.html#xmlSaveOption"/>
    <function name="xmlSchemaAnnotPtr" link="libxml2-schemasInternals.html#xmlSchemaAnnotPtr"/>
    <function name="xmlSchemaAttributeGroupPtr" link="libxml2-schemasInternals.html#xmlSchemaAttributeGroupPtr"/>
    <function name="xmlSchemaAttributeLinkPtr" link="libxml2-schemasInternals.html#xmlSchemaAttributeLinkPtr"/>
    <function name="xmlSchemaAttributePtr" link="libxml2-schemasInternals.html#xmlSchemaAttributePtr"/>
    <function name="xmlSchemaContentType" link="libxml2-schemasInternals.html#xmlSchemaContentType"/>
    <function name="xmlSchemaElementPtr" link="libxml2-schemasInternals.html#xmlSchemaElementPtr"/>
    <function name="xmlSchemaFacetLinkPtr" link="libxml2-schemasInternals.html#xmlSchemaFacetLinkPtr"/>
    <function name="xmlSchemaFacetPtr" link="libxml2-schemasInternals.html#xmlSchemaFacetPtr"/>
    <function name="xmlSchemaNotationPtr" link="libxml2-schemasInternals.html#xmlSchemaNotationPtr"/>
    <function name="xmlSchemaParserCtxtPtr" link="libxml2-xmlschemas.html#xmlSchemaParserCtxtPtr"/>
    <function name="xmlSchemaPtr" link="libxml2-xmlschemas.html#xmlSchemaPtr"/>
    <function name="xmlSchemaSAXPlugPtr" link="libxml2-xmlschemas.html#xmlSchemaSAXPlugPtr"/>
    <function name="xmlSchemaTypeLinkPtr" link="libxml2-schemasInternals.html#xmlSchemaTypeLinkPtr"/>
    <function name="xmlSchemaTypePtr" link="libxml2-schemasInternals.html#xmlSchemaTypePtr"/>
    <function name="xmlSchemaTypeType" link="libxml2-schemasInternals.html#xmlSchemaTypeType"/>
    <function name="xmlSchemaValPtr" link="libxml2-schemasInternals.html#xmlSchemaValPtr"/>
    <function name="xmlSchemaValType" link="libxml2-schemasInternals.html#xmlSchemaValType"/>
    <function name="xmlSchemaValidCtxtPtr" link="libxml2-xmlschemas.html#xmlSchemaValidCtxtPtr"/>
    <function name="xmlSchemaValidError" link="libxml2-xmlschemas.html#xmlSchemaValidError"/>
    <function name="xmlSchemaValidOption" link="libxml2-xmlschemas.html#xmlSchemaValidOption"/>
    <function name="xmlSchemaWhitespaceValueType" link="libxml2-xmlschemastypes.html#xmlSchemaWhitespaceValueType"/>
    <function name="xmlSchemaWildcardNsPtr" link="libxml2-schemasInternals.html#xmlSchemaWildcardNsPtr"/>
    <function name="xmlSchemaWildcardPtr" link="libxml2-schemasInternals.html#xmlSchemaWildcardPtr"/>
    <function name="xmlSchematronParserCtxtPtr" link="libxml2-schematron.html#xmlSchematronParserCtxtPtr"/>
    <function name="xmlSchematronPtr" link="libxml2-schematron.html#xmlSchematronPtr"/>
    <function name="xmlSchematronValidCtxtPtr" link="libxml2-schematron.html#xmlSchematronValidCtxtPtr"/>
    <function name="xmlSchematronValidOptions" link="libxml2-schematron.html#xmlSchematronValidOptions"/>
    <function name="xmlShellCtxtPtr" link="libxml2-debugXML.html#xmlShellCtxtPtr"/>
    <function name="xmlStreamCtxtPtr" link="libxml2-pattern.html#xmlStreamCtxtPtr"/>
    <function name="xmlTextReaderLocatorPtr" link="libxml2-xmlreader.html#xmlTextReaderLocatorPtr"/>
    <function name="xmlTextReaderMode" link="libxml2-xmlreader.html#xmlTextReaderMode"/>
    <function name="xmlTextReaderPtr" link="libxml2-xmlreader.html#xmlTextReaderPtr"/>
    <function name="xmlTextWriterPtr" link="libxml2-xmlwriter.html#xmlTextWriterPtr"/>
    <function name="xmlURIPtr" link="libxml2-uri.html#xmlURIPtr"/>
    <function name="xmlValidCtxtPtr" link="libxml2-valid.html#xmlValidCtxtPtr"/>
    <function name="xmlValidStatePtr" link="libxml2-valid.html#xmlValidStatePtr"/>
    <function name="xmlXIncludeCtxtPtr" link="libxml2-xinclude.html#xmlXIncludeCtxtPtr"/>
    <function name="xmlXPathAxisPtr" link="libxml2-xpath.html#xmlXPathAxisPtr"/>
    <function name="xmlXPathCompExprPtr" link="libxml2-xpath.html#xmlXPathCompExprPtr"/>
    <function name="xmlXPathContextPtr" link="libxml2-xpath.html#xmlXPathContextPtr"/>
    <function name="xmlXPathError" link="libxml2-xpath.html#xmlXPathError"/>
    <function name="xmlXPathFuncPtr" link="libxml2-xpath.html#xmlXPathFuncPtr"/>
    <function name="xmlXPathObjectPtr" link="libxml2-xpath.html#xmlXPathObjectPtr"/>
    <function name="xmlXPathObjectType" link="libxml2-xpath.html#xmlXPathObjectType"/>
    <function name="xmlXPathParserContextPtr" link="libxml2-xpath.html#xmlXPathParserContextPtr"/>
    <function name="xmlXPathTypePtr" link="libxml2-xpath.html#xmlXPathTypePtr"/>
    <function name="xmlXPathVariablePtr" link="libxml2-xpath.html#xmlXPathVariablePtr"/>
    <function name="htmlElemDesc" link="libxml2-HTMLparser.html#htmlElemDesc"/>
    <function name="htmlEntityDesc" link="libxml2-HTMLparser.html#htmlEntityDesc"/>
    <function name="uconv_t" link="libxml2-encoding.html#uconv_t"/>
    <function name="xlinkHandler" link="libxml2-xlink.html#xlinkHandler"/>
    <function name="xmlAttr" link="libxml2-tree.html#xmlAttr"/>
    <function name="xmlAttribute" link="libxml2-tree.html#xmlAttribute"/>
    <function name="xmlAttributeTable" link="libxml2-valid.html#xmlAttributeTable"/>
    <function name="xmlAutomata" link="libxml2-xmlautomata.html#xmlAutomata"/>
    <function name="xmlAutomataState" link="libxml2-xmlautomata.html#xmlAutomataState"/>
    <function name="xmlBuf" link="libxml2-tree.html#xmlBuf"/>
    <function name="xmlBuffer" link="libxml2-tree.html#xmlBuffer"/>
    <function name="xmlCatalog" link="libxml2-catalog.html#xmlCatalog"/>
    <function name="xmlChLRange" link="libxml2-chvalid.html#xmlChLRange"/>
    <function name="xmlChRangeGroup" link="libxml2-chvalid.html#xmlChRangeGroup"/>
    <function name="xmlChSRange" link="libxml2-chvalid.html#xmlChSRange"/>
    <function name="xmlCharEncodingHandler" link="libxml2-encoding.html#xmlCharEncodingHandler"/>
    <function name="xmlDOMWrapCtxt" link="libxml2-tree.html#xmlDOMWrapCtxt"/>
    <function name="xmlDict" link="libxml2-dict.html#xmlDict"/>
    <function name="xmlDoc" link="libxml2-tree.html#xmlDoc"/>
    <function name="xmlDtd" link="libxml2-tree.html#xmlDtd"/>
    <function name="xmlElement" link="libxml2-tree.html#xmlElement"/>
    <function name="xmlElementContent" link="libxml2-tree.html#xmlElementContent"/>
    <function name="xmlElementTable" link="libxml2-valid.html#xmlElementTable"/>
    <function name="xmlEntitiesTable" link="libxml2-entities.html#xmlEntitiesTable"/>
    <function name="xmlEntity" link="libxml2-tree.html#xmlEntity"/>
    <function name="xmlEnumeration" link="libxml2-tree.html#xmlEnumeration"/>
    <function name="xmlError" link="libxml2-xmlerror.html#xmlError"/>
    <function name="xmlExpCtxt" link="libxml2-xmlregexp.html#xmlExpCtxt"/>
    <function name="xmlExpNode" link="libxml2-xmlregexp.html#xmlExpNode"/>
    <function name="xmlGlobalState" link="libxml2-globals.html#xmlGlobalState"/>
    <function name="xmlHashTable" link="libxml2-hash.html#xmlHashTable"/>
    <function name="xmlID" link="libxml2-tree.html#xmlID"/>
    <function name="xmlIDTable" link="libxml2-valid.html#xmlIDTable"/>
    <function name="xmlLink" link="libxml2-list.html#xmlLink"/>
    <function name="xmlList" link="libxml2-list.html#xmlList"/>
    <function name="xmlLocationSet" link="libxml2-xpointer.html#xmlLocationSet"/>
    <function name="xmlModule" link="libxml2-xmlmodule.html#xmlModule"/>
    <function name="xmlMutex" link="libxml2-threads.html#xmlMutex"/>
    <function name="xmlNode" link="libxml2-tree.html#xmlNode"/>
    <function name="xmlNodeSet" link="libxml2-xpath.html#xmlNodeSet"/>
    <function name="xmlNotation" link="libxml2-tree.html#xmlNotation"/>
    <function name="xmlNotationTable" link="libxml2-valid.html#xmlNotationTable"/>
    <function name="xmlNs" link="libxml2-tree.html#xmlNs"/>
    <function name="xmlOutputBuffer" link="libxml2-tree.html#xmlOutputBuffer"/>
    <function name="xmlParserCtxt" link="libxml2-tree.html#xmlParserCtxt"/>
    <function name="xmlParserInput" link="libxml2-tree.html#xmlParserInput"/>
    <function name="xmlParserInputBuffer" link="libxml2-tree.html#xmlParserInputBuffer"/>
    <function name="xmlParserNodeInfo" link="libxml2-parser.html#xmlParserNodeInfo"/>
    <function name="xmlParserNodeInfoSeq" link="libxml2-parser.html#xmlParserNodeInfoSeq"/>
    <function name="xmlPattern" link="libxml2-pattern.html#xmlPattern"/>
    <function name="xmlRMutex" link="libxml2-threads.html#xmlRMutex"/>
    <function name="xmlRef" link="libxml2-tree.html#xmlRef"/>
    <function name="xmlRefTable" link="libxml2-valid.html#xmlRefTable"/>
    <function name="xmlRegExecCtxt" link="libxml2-xmlregexp.html#xmlRegExecCtxt"/>
    <function name="xmlRegexp" link="libxml2-xmlregexp.html#xmlRegexp"/>
    <function name="xmlRelaxNG" link="libxml2-relaxng.html#xmlRelaxNG"/>
    <function name="xmlRelaxNGParserCtxt" link="libxml2-relaxng.html#xmlRelaxNGParserCtxt"/>
    <function name="xmlRelaxNGValidCtxt" link="libxml2-relaxng.html#xmlRelaxNGValidCtxt"/>
    <function name="xmlSAXHandler" link="libxml2-tree.html#xmlSAXHandler"/>
    <function name="xmlSAXHandlerV1" link="libxml2-parser.html#xmlSAXHandlerV1"/>
    <function name="xmlSAXLocator" link="libxml2-tree.html#xmlSAXLocator"/>
    <function name="xmlSaveCtxt" link="libxml2-xmlsave.html#xmlSaveCtxt"/>
    <function name="xmlSchema" link="libxml2-xmlschemas.html#xmlSchema"/>
    <function name="xmlSchemaAnnot" link="libxml2-schemasInternals.html#xmlSchemaAnnot"/>
    <function name="xmlSchemaAttribute" link="libxml2-schemasInternals.html#xmlSchemaAttribute"/>
    <function name="xmlSchemaAttributeGroup" link="libxml2-schemasInternals.html#xmlSchemaAttributeGroup"/>
    <function name="xmlSchemaAttributeLink" link="libxml2-schemasInternals.html#xmlSchemaAttributeLink"/>
    <function name="xmlSchemaElement" link="libxml2-schemasInternals.html#xmlSchemaElement"/>
    <function name="xmlSchemaFacet" link="libxml2-schemasInternals.html#xmlSchemaFacet"/>
    <function name="xmlSchemaFacetLink" link="libxml2-schemasInternals.html#xmlSchemaFacetLink"/>
    <function name="xmlSchemaNotation" link="libxml2-schemasInternals.html#xmlSchemaNotation"/>
    <function name="xmlSchemaParserCtxt" link="libxml2-xmlschemas.html#xmlSchemaParserCtxt"/>
    <function name="xmlSchemaSAXPlugStruct" link="libxml2-xmlschemas.html#xmlSchemaSAXPlugStruct"/>
    <function name="xmlSchemaType" link="libxml2-schemasInternals.html#xmlSchemaType"/>
    <function name="xmlSchemaTypeLink" link="libxml2-schemasInternals.html#xmlSchemaTypeLink"/>
    <function name="xmlSchemaVal" link="libxml2-schemasInternals.html#xmlSchemaVal"/>
    <function name="xmlSchemaValidCtxt" link="libxml2-xmlschemas.html#xmlSchemaValidCtxt"/>
    <function name="xmlSchemaWildcard" link="libxml2-schemasInternals.html#xmlSchemaWildcard"/>
    <function name="xmlSchemaWildcardNs" link="libxml2-schemasInternals.html#xmlSchemaWildcardNs"/>
    <function name="xmlSchematron" link="libxml2-schematron.html#xmlSchematron"/>
    <function name="xmlSchematronParserCtxt" link="libxml2-schematron.html#xmlSchematronParserCtxt"/>
    <function name="xmlSchematronValidCtxt" link="libxml2-schematron.html#xmlSchematronValidCtxt"/>
    <function name="xmlShellCtxt" link="libxml2-debugXML.html#xmlShellCtxt"/>
    <function name="xmlStreamCtxt" link="libxml2-pattern.html#xmlStreamCtxt"/>
    <function name="xmlTextReader" link="libxml2-xmlreader.html#xmlTextReader"/>
    <function name="xmlTextWriter" link="libxml2-xmlwriter.html#xmlTextWriter"/>
    <function name="xmlURI" link="libxml2-uri.html#xmlURI"/>
    <function name="xmlValidCtxt" link="libxml2-valid.html#xmlValidCtxt"/>
    <function name="xmlValidState" link="libxml2-valid.html#xmlValidState"/>
    <function name="xmlXIncludeCtxt" link="libxml2-xinclude.html#xmlXIncludeCtxt"/>
    <function name="xmlXPathAxis" link="libxml2-xpath.html#xmlXPathAxis"/>
    <function name="xmlXPathCompExpr" link="libxml2-xpath.html#xmlXPathCompExpr"/>
    <function name="xmlXPathContext" link="libxml2-xpath.html#xmlXPathContext"/>
    <function name="xmlXPathFunct" link="libxml2-xpath.html#xmlXPathFunct"/>
    <function name="xmlXPathObject" link="libxml2-xpath.html#xmlXPathObject"/>
    <function name="xmlXPathParserContext" link="libxml2-xpath.html#xmlXPathParserContext"/>
    <function name="xmlXPathType" link="libxml2-xpath.html#xmlXPathType"/>
    <function name="xmlXPathVariable" link="libxml2-xpath.html#xmlXPathVariable"/>
    <function name="attributeDeclSAXFunc" link="libxml2-parser.html#attributeDeclSAXFunc"/>
    <function name="attributeSAXFunc" link="libxml2-parser.html#attributeSAXFunc"/>
    <function name="cdataBlockSAXFunc" link="libxml2-parser.html#cdataBlockSAXFunc"/>
    <function name="charactersSAXFunc" link="libxml2-parser.html#charactersSAXFunc"/>
    <function name="commentSAXFunc" link="libxml2-parser.html#commentSAXFunc"/>
    <function name="elementDeclSAXFunc" link="libxml2-parser.html#elementDeclSAXFunc"/>
    <function name="endDocumentSAXFunc" link="libxml2-parser.html#endDocumentSAXFunc"/>
    <function name="endElementNsSAX2Func" link="libxml2-parser.html#endElementNsSAX2Func"/>
    <function name="endElementSAXFunc" link="libxml2-parser.html#endElementSAXFunc"/>
    <function name="entityDeclSAXFunc" link="libxml2-parser.html#entityDeclSAXFunc"/>
    <function name="errorSAXFunc" link="libxml2-parser.html#errorSAXFunc"/>
    <function name="externalSubsetSAXFunc" link="libxml2-parser.html#externalSubsetSAXFunc"/>
    <function name="fatalErrorSAXFunc" link="libxml2-parser.html#fatalErrorSAXFunc"/>
    <function name="ftpDataCallback" link="libxml2-nanoftp.html#ftpDataCallback"/>
    <function name="ftpListCallback" link="libxml2-nanoftp.html#ftpListCallback"/>
    <function name="getEntitySAXFunc" link="libxml2-parser.html#getEntitySAXFunc"/>
    <function name="getParameterEntitySAXFunc" link="libxml2-parser.html#getParameterEntitySAXFunc"/>
    <function name="hasExternalSubsetSAXFunc" link="libxml2-parser.html#hasExternalSubsetSAXFunc"/>
    <function name="hasInternalSubsetSAXFunc" link="libxml2-parser.html#hasInternalSubsetSAXFunc"/>
    <function name="ignorableWhitespaceSAXFunc" link="libxml2-parser.html#ignorableWhitespaceSAXFunc"/>
    <function name="internalSubsetSAXFunc" link="libxml2-parser.html#internalSubsetSAXFunc"/>
    <function name="isStandaloneSAXFunc" link="libxml2-parser.html#isStandaloneSAXFunc"/>
    <function name="notationDeclSAXFunc" link="libxml2-parser.html#notationDeclSAXFunc"/>
    <function name="processingInstructionSAXFunc" link="libxml2-parser.html#processingInstructionSAXFunc"/>
    <function name="referenceSAXFunc" link="libxml2-parser.html#referenceSAXFunc"/>
    <function name="resolveEntitySAXFunc" link="libxml2-parser.html#resolveEntitySAXFunc"/>
    <function name="setDocumentLocatorSAXFunc" link="libxml2-parser.html#setDocumentLocatorSAXFunc"/>
    <function name="startDocumentSAXFunc" link="libxml2-parser.html#startDocumentSAXFunc"/>
    <function name="startElementNsSAX2Func" link="libxml2-parser.html#startElementNsSAX2Func"/>
    <function name="startElementSAXFunc" link="libxml2-parser.html#startElementSAXFunc"/>
    <function name="unparsedEntityDeclSAXFunc" link="libxml2-parser.html#unparsedEntityDeclSAXFunc"/>
    <function name="warningSAXFunc" link="libxml2-parser.html#warningSAXFunc"/>
    <function name="xlinkExtendedLinkFunk" link="libxml2-xlink.html#xlinkExtendedLinkFunk"/>
    <function name="xlinkExtendedLinkSetFunk" link="libxml2-xlink.html#xlinkExtendedLinkSetFunk"/>
    <function name="xlinkNodeDetectFunc" link="libxml2-xlink.html#xlinkNodeDetectFunc"/>
    <function name="xlinkSimpleLinkFunk" link="libxml2-xlink.html#xlinkSimpleLinkFunk"/>
    <function name="xmlC14NIsVisibleCallback" link="libxml2-c14n.html#xmlC14NIsVisibleCallback"/>
    <function name="xmlCharEncodingInputFunc" link="libxml2-encoding.html#xmlCharEncodingInputFunc"/>
    <function name="xmlCharEncodingOutputFunc" link="libxml2-encoding.html#xmlCharEncodingOutputFunc"/>
    <function name="xmlDOMWrapAcquireNsFunction" link="libxml2-tree.html#xmlDOMWrapAcquireNsFunction"/>
    <function name="xmlDeregisterNodeFunc" link="libxml2-globals.html#xmlDeregisterNodeFunc"/>
    <function name="xmlEntityReferenceFunc" link="libxml2-parserInternals.html#xmlEntityReferenceFunc"/>
    <function name="xmlExternalEntityLoader" link="libxml2-parser.html#xmlExternalEntityLoader"/>
    <function name="xmlFreeFunc" link="libxml2-xmlmemory.html#xmlFreeFunc"/>
    <function name="xmlGenericErrorFunc" link="libxml2-xmlerror.html#xmlGenericErrorFunc"/>
    <function name="xmlHashCopier" link="libxml2-hash.html#xmlHashCopier"/>
    <function name="xmlHashDeallocator" link="libxml2-hash.html#xmlHashDeallocator"/>
    <function name="xmlHashScanner" link="libxml2-hash.html#xmlHashScanner"/>
    <function name="xmlHashScannerFull" link="libxml2-hash.html#xmlHashScannerFull"/>
    <function name="xmlInputCloseCallback" link="libxml2-xmlIO.html#xmlInputCloseCallback"/>
    <function name="xmlInputMatchCallback" link="libxml2-xmlIO.html#xmlInputMatchCallback"/>
    <function name="xmlInputOpenCallback" link="libxml2-xmlIO.html#xmlInputOpenCallback"/>
    <function name="xmlInputReadCallback" link="libxml2-xmlIO.html#xmlInputReadCallback"/>
    <function name="xmlListDataCompare" link="libxml2-list.html#xmlListDataCompare"/>
    <function name="xmlListDeallocator" link="libxml2-list.html#xmlListDeallocator"/>
    <function name="xmlListWalker" link="libxml2-list.html#xmlListWalker"/>
    <function name="xmlMallocFunc" link="libxml2-xmlmemory.html#xmlMallocFunc"/>
    <function name="xmlOutputBufferCreateFilenameFunc" link="libxml2-globals.html#xmlOutputBufferCreateFilenameFunc"/>
    <function name="xmlOutputCloseCallback" link="libxml2-xmlIO.html#xmlOutputCloseCallback"/>
    <function name="xmlOutputMatchCallback" link="libxml2-xmlIO.html#xmlOutputMatchCallback"/>
    <function name="xmlOutputOpenCallback" link="libxml2-xmlIO.html#xmlOutputOpenCallback"/>
    <function name="xmlOutputWriteCallback" link="libxml2-xmlIO.html#xmlOutputWriteCallback"/>
    <function name="xmlParserInputBufferCreateFilenameFunc" link="libxml2-globals.html#xmlParserInputBufferCreateFilenameFunc"/>
    <function name="xmlParserInputDeallocate" link="libxml2-parser.html#xmlParserInputDeallocate"/>
    <function name="xmlReallocFunc" link="libxml2-xmlmemory.html#xmlReallocFunc"/>
    <function name="xmlRegExecCallbacks" link="libxml2-xmlregexp.html#xmlRegExecCallbacks"/>
    <function name="xmlRegisterNodeFunc" link="libxml2-globals.html#xmlRegisterNodeFunc"/>
    <function name="xmlRelaxNGValidityErrorFunc" link="libxml2-relaxng.html#xmlRelaxNGValidityErrorFunc"/>
    <function name="xmlRelaxNGValidityWarningFunc" link="libxml2-relaxng.html#xmlRelaxNGValidityWarningFunc"/>
    <function name="xmlSchemaValidityErrorFunc" link="libxml2-xmlschemas.html#xmlSchemaValidityErrorFunc"/>
    <function name="xmlSchemaValidityLocatorFunc" link="libxml2-xmlschemas.html#xmlSchemaValidityLocatorFunc"/>
    <function name="xmlSchemaValidityWarningFunc" link="libxml2-xmlschemas.html#xmlSchemaValidityWarningFunc"/>
    <function name="xmlSchematronValidityErrorFunc" link="libxml2-schematron.html#xmlSchematronValidityErrorFunc"/>
    <function name="xmlSchematronValidityWarningFunc" link="libxml2-schematron.html#xmlSchematronValidityWarningFunc"/>
    <function name="xmlShellCmd" link="libxml2-debugXML.html#xmlShellCmd"/>
    <function name="xmlShellReadlineFunc" link="libxml2-debugXML.html#xmlShellReadlineFunc"/>
    <function name="xmlStrdupFunc" link="libxml2-xmlmemory.html#xmlStrdupFunc"/>
    <function name="xmlStructuredErrorFunc" link="libxml2-xmlerror.html#xmlStructuredErrorFunc"/>
    <function name="xmlTextReaderErrorFunc" link="libxml2-xmlreader.html#xmlTextReaderErrorFunc"/>
    <function name="xmlValidityErrorFunc" link="libxml2-valid.html#xmlValidityErrorFunc"/>
    <function name="xmlValidityWarningFunc" link="libxml2-valid.html#xmlValidityWarningFunc"/>
    <function name="xmlXPathAxisFunc" link="libxml2-xpath.html#xmlXPathAxisFunc"/>
    <function name="xmlXPathConvertFunc" link="libxml2-xpath.html#xmlXPathConvertFunc"/>
    <function name="xmlXPathEvalFunc" link="libxml2-xpath.html#xmlXPathEvalFunc"/>
    <function name="xmlXPathFuncLookupFunc" link="libxml2-xpath.html#xmlXPathFuncLookupFunc"/>
    <function name="xmlXPathFunction" link="libxml2-xpath.html#xmlXPathFunction"/>
    <function name="xmlXPathVariableLookupFunc" link="libxml2-xpath.html#xmlXPathVariableLookupFunc"/>
    <function name="docbDefaultSAXHandler" link="libxml2-globals.html#docbDefaultSAXHandler"/>
    <function name="emptyExp" link="libxml2-xmlregexp.html#emptyExp"/>
    <function name="forbiddenExp" link="libxml2-xmlregexp.html#forbiddenExp"/>
    <function name="htmlDefaultSAXHandler" link="libxml2-globals.html#htmlDefaultSAXHandler"/>
    <function name="oldXMLWDcompatibility" link="libxml2-globals.html#oldXMLWDcompatibility"/>
    <function name="xmlBufferAllocScheme" link="libxml2-globals.html#xmlBufferAllocScheme"/>
    <function name="xmlDefaultBufferSize" link="libxml2-globals.html#xmlDefaultBufferSize"/>
    <function name="xmlDefaultSAXHandler" link="libxml2-globals.html#xmlDefaultSAXHandler"/>
    <function name="xmlDefaultSAXLocator" link="libxml2-globals.html#xmlDefaultSAXLocator"/>
    <function name="xmlDeregisterNodeDefaultValue" link="libxml2-globals.html#xmlDeregisterNodeDefaultValue"/>
    <function name="xmlDoValidityCheckingDefaultValue" link="libxml2-globals.html#xmlDoValidityCheckingDefaultValue"/>
    <function name="xmlFree" link="libxml2-globals.html#xmlFree"/>
    <function name="xmlGenericError" link="libxml2-globals.html#xmlGenericError"/>
    <function name="xmlGenericErrorContext" link="libxml2-globals.html#xmlGenericErrorContext"/>
    <function name="xmlGetWarningsDefaultValue" link="libxml2-globals.html#xmlGetWarningsDefaultValue"/>
    <function name="xmlIndentTreeOutput" link="libxml2-globals.html#xmlIndentTreeOutput"/>
    <function name="xmlIsBaseCharGroup" link="libxml2-chvalid.html#xmlIsBaseCharGroup"/>
    <function name="xmlIsCharGroup" link="libxml2-chvalid.html#xmlIsCharGroup"/>
    <function name="xmlIsCombiningGroup" link="libxml2-chvalid.html#xmlIsCombiningGroup"/>
    <function name="xmlIsDigitGroup" link="libxml2-chvalid.html#xmlIsDigitGroup"/>
    <function name="xmlIsExtenderGroup" link="libxml2-chvalid.html#xmlIsExtenderGroup"/>
    <function name="xmlIsIdeographicGroup" link="libxml2-chvalid.html#xmlIsIdeographicGroup"/>
    <function name="xmlIsPubidChar_tab" link="libxml2-chvalid.html#xmlIsPubidChar_tab"/>
    <function name="xmlKeepBlanksDefaultValue" link="libxml2-globals.html#xmlKeepBlanksDefaultValue"/>
    <function name="xmlLastError" link="libxml2-globals.html#xmlLastError"/>
    <function name="xmlLineNumbersDefaultValue" link="libxml2-globals.html#xmlLineNumbersDefaultValue"/>
    <function name="xmlLoadExtDtdDefaultValue" link="libxml2-globals.html#xmlLoadExtDtdDefaultValue"/>
    <function name="xmlMalloc" link="libxml2-globals.html#xmlMalloc"/>
    <function name="xmlMallocAtomic" link="libxml2-globals.html#xmlMallocAtomic"/>
    <function name="xmlMemStrdup" link="libxml2-globals.html#xmlMemStrdup"/>
    <function name="xmlOutputBufferCreateFilenameValue" link="libxml2-globals.html#xmlOutputBufferCreateFilenameValue"/>
    <function name="xmlParserDebugEntities" link="libxml2-globals.html#xmlParserDebugEntities"/>
    <function name="xmlParserInputBufferCreateFilenameValue" link="libxml2-globals.html#xmlParserInputBufferCreateFilenameValue"/>
    <function name="xmlParserMaxDepth" link="libxml2-parserInternals.html#xmlParserMaxDepth"/>
    <function name="xmlParserVersion" link="libxml2-globals.html#xmlParserVersion"/>
    <function name="xmlPedanticParserDefaultValue" link="libxml2-globals.html#xmlPedanticParserDefaultValue"/>
    <function name="xmlRealloc" link="libxml2-globals.html#xmlRealloc"/>
    <function name="xmlRegisterNodeDefaultValue" link="libxml2-globals.html#xmlRegisterNodeDefaultValue"/>
    <function name="xmlSaveNoEmptyTags" link="libxml2-globals.html#xmlSaveNoEmptyTags"/>
    <function name="xmlStringComment" link="libxml2-parserInternals.html#xmlStringComment"/>
    <function name="xmlStringText" link="libxml2-parserInternals.html#xmlStringText"/>
    <function name="xmlStringTextNoenc" link="libxml2-parserInternals.html#xmlStringTextNoenc"/>
    <function name="xmlStructuredError" link="libxml2-globals.html#xmlStructuredError"/>
    <function name="xmlStructuredErrorContext" link="libxml2-globals.html#xmlStructuredErrorContext"/>
    <function name="xmlSubstituteEntitiesDefaultValue" link="libxml2-globals.html#xmlSubstituteEntitiesDefaultValue"/>
    <function name="xmlTreeIndentString" link="libxml2-globals.html#xmlTreeIndentString"/>
    <function name="xmlXPathNAN" link="libxml2-xpath.html#xmlXPathNAN"/>
    <function name="xmlXPathNINF" link="libxml2-xpath.html#xmlXPathNINF"/>
    <function name="xmlXPathPINF" link="libxml2-xpath.html#xmlXPathPINF"/>
    <function name="UTF8ToHtml ()" link="libxml2-HTMLparser.html#UTF8ToHtml"/>
    <function name="UTF8Toisolat1 ()" link="libxml2-encoding.html#UTF8Toisolat1"/>
    <function name="attribute ()" link="libxml2-SAX.html#attribute"/>
    <function name="attributeDecl ()" link="libxml2-SAX.html#attributeDecl"/>
    <function name="cdataBlock ()" link="libxml2-SAX.html#cdataBlock"/>
    <function name="characters ()" link="libxml2-SAX.html#characters"/>
    <function name="checkNamespace ()" link="libxml2-SAX.html#checkNamespace"/>
    <function name="comment ()" link="libxml2-SAX.html#comment"/>
    <function name="docbCreateFileParserCtxt ()" link="libxml2-DOCBparser.html#docbCreateFileParserCtxt"/>
    <function name="docbCreatePushParserCtxt ()" link="libxml2-DOCBparser.html#docbCreatePushParserCtxt"/>
    <function name="docbDefaultSAXHandlerInit ()" link="libxml2-SAX2.html#docbDefaultSAXHandlerInit"/>
    <function name="docbEncodeEntities ()" link="libxml2-DOCBparser.html#docbEncodeEntities"/>
    <function name="docbFreeParserCtxt ()" link="libxml2-DOCBparser.html#docbFreeParserCtxt"/>
    <function name="docbParseChunk ()" link="libxml2-DOCBparser.html#docbParseChunk"/>
    <function name="docbParseDoc ()" link="libxml2-DOCBparser.html#docbParseDoc"/>
    <function name="docbParseDocument ()" link="libxml2-DOCBparser.html#docbParseDocument"/>
    <function name="docbParseFile ()" link="libxml2-DOCBparser.html#docbParseFile"/>
    <function name="docbSAXParseDoc ()" link="libxml2-DOCBparser.html#docbSAXParseDoc"/>
    <function name="docbSAXParseFile ()" link="libxml2-DOCBparser.html#docbSAXParseFile"/>
    <function name="elementDecl ()" link="libxml2-SAX.html#elementDecl"/>
    <function name="endDocument ()" link="libxml2-SAX.html#endDocument"/>
    <function name="endElement ()" link="libxml2-SAX.html#endElement"/>
    <function name="entityDecl ()" link="libxml2-SAX.html#entityDecl"/>
    <function name="externalSubset ()" link="libxml2-SAX.html#externalSubset"/>
    <function name="getColumnNumber ()" link="libxml2-SAX.html#getColumnNumber"/>
    <function name="getEntity ()" link="libxml2-SAX.html#getEntity"/>
    <function name="getLineNumber ()" link="libxml2-SAX.html#getLineNumber"/>
    <function name="getNamespace ()" link="libxml2-SAX.html#getNamespace"/>
    <function name="getParameterEntity ()" link="libxml2-SAX.html#getParameterEntity"/>
    <function name="getPublicId ()" link="libxml2-SAX.html#getPublicId"/>
    <function name="getSystemId ()" link="libxml2-SAX.html#getSystemId"/>
    <function name="globalNamespace ()" link="libxml2-SAX.html#globalNamespace"/>
    <function name="hasExternalSubset ()" link="libxml2-SAX.html#hasExternalSubset"/>
    <function name="hasInternalSubset ()" link="libxml2-SAX.html#hasInternalSubset"/>
    <function name="htmlAttrAllowed ()" link="libxml2-HTMLparser.html#htmlAttrAllowed"/>
    <function name="htmlAutoCloseTag ()" link="libxml2-HTMLparser.html#htmlAutoCloseTag"/>
    <function name="htmlCreateFileParserCtxt ()" link="libxml2-parserInternals.html#htmlCreateFileParserCtxt"/>
    <function name="htmlCreateMemoryParserCtxt ()" link="libxml2-HTMLparser.html#htmlCreateMemoryParserCtxt"/>
    <function name="htmlCreatePushParserCtxt ()" link="libxml2-HTMLparser.html#htmlCreatePushParserCtxt"/>
    <function name="htmlCtxtReadDoc ()" link="libxml2-HTMLparser.html#htmlCtxtReadDoc"/>
    <function name="htmlCtxtReadFd ()" link="libxml2-HTMLparser.html#htmlCtxtReadFd"/>
    <function name="htmlCtxtReadFile ()" link="libxml2-HTMLparser.html#htmlCtxtReadFile"/>
    <function name="htmlCtxtReadIO ()" link="libxml2-HTMLparser.html#htmlCtxtReadIO"/>
    <function name="htmlCtxtReadMemory ()" link="libxml2-HTMLparser.html#htmlCtxtReadMemory"/>
    <function name="htmlCtxtReset ()" link="libxml2-HTMLparser.html#htmlCtxtReset"/>
    <function name="htmlCtxtUseOptions ()" link="libxml2-HTMLparser.html#htmlCtxtUseOptions"/>
    <function name="htmlDefaultSAXHandlerInit ()" link="libxml2-SAX2.html#htmlDefaultSAXHandlerInit"/>
    <function name="htmlDocContentDumpFormatOutput ()" link="libxml2-HTMLtree.html#htmlDocContentDumpFormatOutput"/>
    <function name="htmlDocContentDumpOutput ()" link="libxml2-HTMLtree.html#htmlDocContentDumpOutput"/>
    <function name="htmlDocDump ()" link="libxml2-HTMLtree.html#htmlDocDump"/>
    <function name="htmlDocDumpMemory ()" link="libxml2-HTMLtree.html#htmlDocDumpMemory"/>
    <function name="htmlDocDumpMemoryFormat ()" link="libxml2-HTMLtree.html#htmlDocDumpMemoryFormat"/>
    <function name="htmlElementAllowedHere ()" link="libxml2-HTMLparser.html#htmlElementAllowedHere"/>
    <function name="htmlElementStatusHere ()" link="libxml2-HTMLparser.html#htmlElementStatusHere"/>
    <function name="htmlEncodeEntities ()" link="libxml2-HTMLparser.html#htmlEncodeEntities"/>
    <function name="htmlEntityLookup ()" link="libxml2-HTMLparser.html#htmlEntityLookup"/>
    <function name="htmlEntityValueLookup ()" link="libxml2-HTMLparser.html#htmlEntityValueLookup"/>
    <function name="htmlFreeParserCtxt ()" link="libxml2-HTMLparser.html#htmlFreeParserCtxt"/>
    <function name="htmlGetMetaEncoding ()" link="libxml2-HTMLtree.html#htmlGetMetaEncoding"/>
    <function name="htmlHandleOmittedElem ()" link="libxml2-HTMLparser.html#htmlHandleOmittedElem"/>
    <function name="htmlInitAutoClose ()" link="libxml2-parserInternals.html#htmlInitAutoClose"/>
    <function name="htmlIsAutoClosed ()" link="libxml2-HTMLparser.html#htmlIsAutoClosed"/>
    <function name="htmlIsBooleanAttr ()" link="libxml2-HTMLtree.html#htmlIsBooleanAttr"/>
    <function name="htmlIsScriptAttribute ()" link="libxml2-HTMLparser.html#htmlIsScriptAttribute"/>
    <function name="htmlNewDoc ()" link="libxml2-HTMLtree.html#htmlNewDoc"/>
    <function name="htmlNewDocNoDtD ()" link="libxml2-HTMLtree.html#htmlNewDocNoDtD"/>
    <function name="htmlNewParserCtxt ()" link="libxml2-HTMLparser.html#htmlNewParserCtxt"/>
    <function name="htmlNodeDump ()" link="libxml2-HTMLtree.html#htmlNodeDump"/>
    <function name="htmlNodeDumpFile ()" link="libxml2-HTMLtree.html#htmlNodeDumpFile"/>
    <function name="htmlNodeDumpFileFormat ()" link="libxml2-HTMLtree.html#htmlNodeDumpFileFormat"/>
    <function name="htmlNodeDumpFormatOutput ()" link="libxml2-HTMLtree.html#htmlNodeDumpFormatOutput"/>
    <function name="htmlNodeDumpOutput ()" link="libxml2-HTMLtree.html#htmlNodeDumpOutput"/>
    <function name="htmlNodeStatus ()" link="libxml2-HTMLparser.html#htmlNodeStatus"/>
    <function name="htmlParseCharRef ()" link="libxml2-HTMLparser.html#htmlParseCharRef"/>
    <function name="htmlParseChunk ()" link="libxml2-HTMLparser.html#htmlParseChunk"/>
    <function name="htmlParseDoc ()" link="libxml2-HTMLparser.html#htmlParseDoc"/>
    <function name="htmlParseDocument ()" link="libxml2-HTMLparser.html#htmlParseDocument"/>
    <function name="htmlParseElement ()" link="libxml2-HTMLparser.html#htmlParseElement"/>
    <function name="htmlParseEntityRef ()" link="libxml2-HTMLparser.html#htmlParseEntityRef"/>
    <function name="htmlParseFile ()" link="libxml2-HTMLparser.html#htmlParseFile"/>
    <function name="htmlReadDoc ()" link="libxml2-HTMLparser.html#htmlReadDoc"/>
    <function name="htmlReadFd ()" link="libxml2-HTMLparser.html#htmlReadFd"/>
    <function name="htmlReadFile ()" link="libxml2-HTMLparser.html#htmlReadFile"/>
    <function name="htmlReadIO ()" link="libxml2-HTMLparser.html#htmlReadIO"/>
    <function name="htmlReadMemory ()" link="libxml2-HTMLparser.html#htmlReadMemory"/>
    <function name="htmlSAXParseDoc ()" link="libxml2-HTMLparser.html#htmlSAXParseDoc"/>
    <function name="htmlSAXParseFile ()" link="libxml2-HTMLparser.html#htmlSAXParseFile"/>
    <function name="htmlSaveFile ()" link="libxml2-HTMLtree.html#htmlSaveFile"/>
    <function name="htmlSaveFileEnc ()" link="libxml2-HTMLtree.html#htmlSaveFileEnc"/>
    <function name="htmlSaveFileFormat ()" link="libxml2-HTMLtree.html#htmlSaveFileFormat"/>
    <function name="htmlSetMetaEncoding ()" link="libxml2-HTMLtree.html#htmlSetMetaEncoding"/>
    <function name="htmlTagLookup ()" link="libxml2-HTMLparser.html#htmlTagLookup"/>
    <function name="ignorableWhitespace ()" link="libxml2-SAX.html#ignorableWhitespace"/>
    <function name="initGenericErrorDefaultFunc ()" link="libxml2-xmlerror.html#initGenericErrorDefaultFunc"/>
    <function name="initdocbDefaultSAXHandler ()" link="libxml2-SAX.html#initdocbDefaultSAXHandler"/>
    <function name="inithtmlDefaultSAXHandler ()" link="libxml2-SAX.html#inithtmlDefaultSAXHandler"/>
    <function name="initxmlDefaultSAXHandler ()" link="libxml2-SAX.html#initxmlDefaultSAXHandler"/>
    <function name="inputPop ()" link="libxml2-parserInternals.html#inputPop"/>
    <function name="inputPush ()" link="libxml2-parserInternals.html#inputPush"/>
    <function name="internalSubset ()" link="libxml2-SAX.html#internalSubset"/>
    <function name="isStandalone ()" link="libxml2-SAX.html#isStandalone"/>
    <function name="isolat1ToUTF8 ()" link="libxml2-encoding.html#isolat1ToUTF8"/>
    <function name="namePop ()" link="libxml2-parserInternals.html#namePop"/>
    <function name="namePush ()" link="libxml2-parserInternals.html#namePush"/>
    <function name="namespaceDecl ()" link="libxml2-SAX.html#namespaceDecl"/>
    <function name="nodePop ()" link="libxml2-parserInternals.html#nodePop"/>
    <function name="nodePush ()" link="libxml2-parserInternals.html#nodePush"/>
    <function name="notationDecl ()" link="libxml2-SAX.html#notationDecl"/>
    <function name="processingInstruction ()" link="libxml2-SAX.html#processingInstruction"/>
    <function name="reference ()" link="libxml2-SAX.html#reference"/>
    <function name="resolveEntity ()" link="libxml2-SAX.html#resolveEntity"/>
    <function name="setDocumentLocator ()" link="libxml2-SAX.html#setDocumentLocator"/>
    <function name="setNamespace ()" link="libxml2-SAX.html#setNamespace"/>
    <function name="startDocument ()" link="libxml2-SAX.html#startDocument"/>
    <function name="startElement ()" link="libxml2-SAX.html#startElement"/>
    <function name="unparsedEntityDecl ()" link="libxml2-SAX.html#unparsedEntityDecl"/>
    <function name="valuePop ()" link="libxml2-xpathInternals.html#valuePop"/>
    <function name="valuePush ()" link="libxml2-xpathInternals.html#valuePush"/>
    <function name="xlinkGetDefaultDetect ()" link="libxml2-xlink.html#xlinkGetDefaultDetect"/>
    <function name="xlinkGetDefaultHandler ()" link="libxml2-xlink.html#xlinkGetDefaultHandler"/>
    <function name="xlinkIsLink ()" link="libxml2-xlink.html#xlinkIsLink"/>
    <function name="xlinkSetDefaultDetect ()" link="libxml2-xlink.html#xlinkSetDefaultDetect"/>
    <function name="xlinkSetDefaultHandler ()" link="libxml2-xlink.html#xlinkSetDefaultHandler"/>
    <function name="xmlACatalogAdd ()" link="libxml2-catalog.html#xmlACatalogAdd"/>
    <function name="xmlACatalogDump ()" link="libxml2-catalog.html#xmlACatalogDump"/>
    <function name="xmlACatalogRemove ()" link="libxml2-catalog.html#xmlACatalogRemove"/>
    <function name="xmlACatalogResolve ()" link="libxml2-catalog.html#xmlACatalogResolve"/>
    <function name="xmlACatalogResolvePublic ()" link="libxml2-catalog.html#xmlACatalogResolvePublic"/>
    <function name="xmlACatalogResolveSystem ()" link="libxml2-catalog.html#xmlACatalogResolveSystem"/>
    <function name="xmlACatalogResolveURI ()" link="libxml2-catalog.html#xmlACatalogResolveURI"/>
    <function name="xmlAddAttributeDecl ()" link="libxml2-valid.html#xmlAddAttributeDecl"/>
    <function name="xmlAddChild ()" link="libxml2-tree.html#xmlAddChild"/>
    <function name="xmlAddChildList ()" link="libxml2-tree.html#xmlAddChildList"/>
    <function name="xmlAddDocEntity ()" link="libxml2-entities.html#xmlAddDocEntity"/>
    <function name="xmlAddDtdEntity ()" link="libxml2-entities.html#xmlAddDtdEntity"/>
    <function name="xmlAddElementDecl ()" link="libxml2-valid.html#xmlAddElementDecl"/>
    <function name="xmlAddEncodingAlias ()" link="libxml2-encoding.html#xmlAddEncodingAlias"/>
    <function name="xmlAddID ()" link="libxml2-valid.html#xmlAddID"/>
    <function name="xmlAddNextSibling ()" link="libxml2-tree.html#xmlAddNextSibling"/>
    <function name="xmlAddNotationDecl ()" link="libxml2-valid.html#xmlAddNotationDecl"/>
    <function name="xmlAddPrevSibling ()" link="libxml2-tree.html#xmlAddPrevSibling"/>
    <function name="xmlAddRef ()" link="libxml2-valid.html#xmlAddRef"/>
    <function name="xmlAddSibling ()" link="libxml2-tree.html#xmlAddSibling"/>
    <function name="xmlAllocOutputBuffer ()" link="libxml2-xmlIO.html#xmlAllocOutputBuffer"/>
    <function name="xmlAllocParserInputBuffer ()" link="libxml2-xmlIO.html#xmlAllocParserInputBuffer"/>
    <function name="xmlAttrSerializeTxtContent ()" link="libxml2-tree.html#xmlAttrSerializeTxtContent"/>
    <function name="xmlAutomataCompile ()" link="libxml2-xmlautomata.html#xmlAutomataCompile"/>
    <function name="xmlAutomataGetInitState ()" link="libxml2-xmlautomata.html#xmlAutomataGetInitState"/>
    <function name="xmlAutomataIsDeterminist ()" link="libxml2-xmlautomata.html#xmlAutomataIsDeterminist"/>
    <function name="xmlAutomataNewAllTrans ()" link="libxml2-xmlautomata.html#xmlAutomataNewAllTrans"/>
    <function name="xmlAutomataNewCountTrans ()" link="libxml2-xmlautomata.html#xmlAutomataNewCountTrans"/>
    <function name="xmlAutomataNewCountTrans2 ()" link="libxml2-xmlautomata.html#xmlAutomataNewCountTrans2"/>
    <function name="xmlAutomataNewCountedTrans ()" link="libxml2-xmlautomata.html#xmlAutomataNewCountedTrans"/>
    <function name="xmlAutomataNewCounter ()" link="libxml2-xmlautomata.html#xmlAutomataNewCounter"/>
    <function name="xmlAutomataNewCounterTrans ()" link="libxml2-xmlautomata.html#xmlAutomataNewCounterTrans"/>
    <function name="xmlAutomataNewEpsilon ()" link="libxml2-xmlautomata.html#xmlAutomataNewEpsilon"/>
    <function name="xmlAutomataNewNegTrans ()" link="libxml2-xmlautomata.html#xmlAutomataNewNegTrans"/>
    <function name="xmlAutomataNewOnceTrans ()" link="libxml2-xmlautomata.html#xmlAutomataNewOnceTrans"/>
    <function name="xmlAutomataNewOnceTrans2 ()" link="libxml2-xmlautomata.html#xmlAutomataNewOnceTrans2"/>
    <function name="xmlAutomataNewState ()" link="libxml2-xmlautomata.html#xmlAutomataNewState"/>
    <function name="xmlAutomataNewTransition ()" link="libxml2-xmlautomata.html#xmlAutomataNewTransition"/>
    <function name="xmlAutomataNewTransition2 ()" link="libxml2-xmlautomata.html#xmlAutomataNewTransition2"/>
    <function name="xmlAutomataSetFinalState ()" link="libxml2-xmlautomata.html#xmlAutomataSetFinalState"/>
    <function name="xmlBoolToText ()" link="libxml2-debugXML.html#xmlBoolToText"/>
    <function name="xmlBufContent ()" link="libxml2-tree.html#xmlBufContent"/>
    <function name="xmlBufEnd ()" link="libxml2-tree.html#xmlBufEnd"/>
    <function name="xmlBufGetNodeContent ()" link="libxml2-tree.html#xmlBufGetNodeContent"/>
    <function name="xmlBufNodeDump ()" link="libxml2-tree.html#xmlBufNodeDump"/>
    <function name="xmlBufShrink ()" link="libxml2-tree.html#xmlBufShrink"/>
    <function name="xmlBufUse ()" link="libxml2-tree.html#xmlBufUse"/>
    <function name="xmlBufferAdd ()" link="libxml2-tree.html#xmlBufferAdd"/>
    <function name="xmlBufferAddHead ()" link="libxml2-tree.html#xmlBufferAddHead"/>
    <function name="xmlBufferCCat ()" link="libxml2-tree.html#xmlBufferCCat"/>
    <function name="xmlBufferCat ()" link="libxml2-tree.html#xmlBufferCat"/>
    <function name="xmlBufferContent ()" link="libxml2-tree.html#xmlBufferContent"/>
    <function name="xmlBufferCreate ()" link="libxml2-tree.html#xmlBufferCreate"/>
    <function name="xmlBufferCreateSize ()" link="libxml2-tree.html#xmlBufferCreateSize"/>
    <function name="xmlBufferCreateStatic ()" link="libxml2-tree.html#xmlBufferCreateStatic"/>
    <function name="xmlBufferDetach ()" link="libxml2-tree.html#xmlBufferDetach"/>
    <function name="xmlBufferDump ()" link="libxml2-tree.html#xmlBufferDump"/>
    <function name="xmlBufferEmpty ()" link="libxml2-tree.html#xmlBufferEmpty"/>
    <function name="xmlBufferFree ()" link="libxml2-tree.html#xmlBufferFree"/>
    <function name="xmlBufferGrow ()" link="libxml2-tree.html#xmlBufferGrow"/>
    <function name="xmlBufferLength ()" link="libxml2-tree.html#xmlBufferLength"/>
    <function name="xmlBufferResize ()" link="libxml2-tree.html#xmlBufferResize"/>
    <function name="xmlBufferSetAllocationScheme ()" link="libxml2-tree.html#xmlBufferSetAllocationScheme"/>
    <function name="xmlBufferShrink ()" link="libxml2-tree.html#xmlBufferShrink"/>
    <function name="xmlBufferWriteCHAR ()" link="libxml2-tree.html#xmlBufferWriteCHAR"/>
    <function name="xmlBufferWriteChar ()" link="libxml2-tree.html#xmlBufferWriteChar"/>
    <function name="xmlBufferWriteQuotedString ()" link="libxml2-tree.html#xmlBufferWriteQuotedString"/>
    <function name="xmlBuildQName ()" link="libxml2-tree.html#xmlBuildQName"/>
    <function name="xmlBuildRelativeURI ()" link="libxml2-uri.html#xmlBuildRelativeURI"/>
    <function name="xmlBuildURI ()" link="libxml2-uri.html#xmlBuildURI"/>
    <function name="xmlByteConsumed ()" link="libxml2-parser.html#xmlByteConsumed"/>
    <function name="xmlC14NDocDumpMemory ()" link="libxml2-c14n.html#xmlC14NDocDumpMemory"/>
    <function name="xmlC14NDocSave ()" link="libxml2-c14n.html#xmlC14NDocSave"/>
    <function name="xmlC14NDocSaveTo ()" link="libxml2-c14n.html#xmlC14NDocSaveTo"/>
    <function name="xmlC14NExecute ()" link="libxml2-c14n.html#xmlC14NExecute"/>
    <function name="xmlCanonicPath ()" link="libxml2-uri.html#xmlCanonicPath"/>
    <function name="xmlCatalogAdd ()" link="libxml2-catalog.html#xmlCatalogAdd"/>
    <function name="xmlCatalogAddLocal ()" link="libxml2-catalog.html#xmlCatalogAddLocal"/>
    <function name="xmlCatalogCleanup ()" link="libxml2-catalog.html#xmlCatalogCleanup"/>
    <function name="xmlCatalogConvert ()" link="libxml2-catalog.html#xmlCatalogConvert"/>
    <function name="xmlCatalogDump ()" link="libxml2-catalog.html#xmlCatalogDump"/>
    <function name="xmlCatalogFreeLocal ()" link="libxml2-catalog.html#xmlCatalogFreeLocal"/>
    <function name="xmlCatalogGetDefaults ()" link="libxml2-catalog.html#xmlCatalogGetDefaults"/>
    <function name="xmlCatalogGetPublic ()" link="libxml2-catalog.html#xmlCatalogGetPublic"/>
    <function name="xmlCatalogGetSystem ()" link="libxml2-catalog.html#xmlCatalogGetSystem"/>
    <function name="xmlCatalogIsEmpty ()" link="libxml2-catalog.html#xmlCatalogIsEmpty"/>
    <function name="xmlCatalogLocalResolve ()" link="libxml2-catalog.html#xmlCatalogLocalResolve"/>
    <function name="xmlCatalogLocalResolveURI ()" link="libxml2-catalog.html#xmlCatalogLocalResolveURI"/>
    <function name="xmlCatalogRemove ()" link="libxml2-catalog.html#xmlCatalogRemove"/>
    <function name="xmlCatalogResolve ()" link="libxml2-catalog.html#xmlCatalogResolve"/>
    <function name="xmlCatalogResolvePublic ()" link="libxml2-catalog.html#xmlCatalogResolvePublic"/>
    <function name="xmlCatalogResolveSystem ()" link="libxml2-catalog.html#xmlCatalogResolveSystem"/>
    <function name="xmlCatalogResolveURI ()" link="libxml2-catalog.html#xmlCatalogResolveURI"/>
    <function name="xmlCatalogSetDebug ()" link="libxml2-catalog.html#xmlCatalogSetDebug"/>
    <function name="xmlCatalogSetDefaultPrefer ()" link="libxml2-catalog.html#xmlCatalogSetDefaultPrefer"/>
    <function name="xmlCatalogSetDefaults ()" link="libxml2-catalog.html#xmlCatalogSetDefaults"/>
    <function name="xmlCharEncCloseFunc ()" link="libxml2-encoding.html#xmlCharEncCloseFunc"/>
    <function name="xmlCharEncFirstLine ()" link="libxml2-encoding.html#xmlCharEncFirstLine"/>
    <function name="xmlCharEncInFunc ()" link="libxml2-encoding.html#xmlCharEncInFunc"/>
    <function name="xmlCharEncOutFunc ()" link="libxml2-encoding.html#xmlCharEncOutFunc"/>
    <function name="xmlCharInRange ()" link="libxml2-chvalid.html#xmlCharInRange"/>
    <function name="xmlCharStrdup ()" link="libxml2-xmlstring.html#xmlCharStrdup"/>
    <function name="xmlCharStrndup ()" link="libxml2-xmlstring.html#xmlCharStrndup"/>
    <function name="xmlCheckFilename ()" link="libxml2-xmlIO.html#xmlCheckFilename"/>
    <function name="xmlCheckHTTPInput ()" link="libxml2-xmlIO.html#xmlCheckHTTPInput"/>
    <function name="xmlCheckLanguageID ()" link="libxml2-parserInternals.html#xmlCheckLanguageID"/>
    <function name="xmlCheckUTF8 ()" link="libxml2-xmlstring.html#xmlCheckUTF8"/>
    <function name="xmlCheckVersion ()" link="libxml2-xmlversion.html#xmlCheckVersion"/>
    <function name="xmlChildElementCount ()" link="libxml2-tree.html#xmlChildElementCount"/>
    <function name="xmlCleanupCharEncodingHandlers ()" link="libxml2-encoding.html#xmlCleanupCharEncodingHandlers"/>
    <function name="xmlCleanupEncodingAliases ()" link="libxml2-encoding.html#xmlCleanupEncodingAliases"/>
    <function name="xmlCleanupGlobals ()" link="libxml2-globals.html#xmlCleanupGlobals"/>
    <function name="xmlCleanupInputCallbacks ()" link="libxml2-xmlIO.html#xmlCleanupInputCallbacks"/>
    <function name="xmlCleanupMemory ()" link="libxml2-xmlmemory.html#xmlCleanupMemory"/>
    <function name="xmlCleanupOutputCallbacks ()" link="libxml2-xmlIO.html#xmlCleanupOutputCallbacks"/>
    <function name="xmlCleanupParser ()" link="libxml2-parser.html#xmlCleanupParser"/>
    <function name="xmlCleanupPredefinedEntities ()" link="libxml2-entities.html#xmlCleanupPredefinedEntities"/>
    <function name="xmlCleanupThreads ()" link="libxml2-threads.html#xmlCleanupThreads"/>
    <function name="xmlClearNodeInfoSeq ()" link="libxml2-parser.html#xmlClearNodeInfoSeq"/>
    <function name="xmlClearParserCtxt ()" link="libxml2-parser.html#xmlClearParserCtxt"/>
    <function name="xmlConvertSGMLCatalog ()" link="libxml2-catalog.html#xmlConvertSGMLCatalog"/>
    <function name="xmlCopyAttributeTable ()" link="libxml2-valid.html#xmlCopyAttributeTable"/>
    <function name="xmlCopyChar ()" link="libxml2-parserInternals.html#xmlCopyChar"/>
    <function name="xmlCopyCharMultiByte ()" link="libxml2-parserInternals.html#xmlCopyCharMultiByte"/>
    <function name="xmlCopyDoc ()" link="libxml2-tree.html#xmlCopyDoc"/>
    <function name="xmlCopyDocElementContent ()" link="libxml2-valid.html#xmlCopyDocElementContent"/>
    <function name="xmlCopyDtd ()" link="libxml2-tree.html#xmlCopyDtd"/>
    <function name="xmlCopyElementContent ()" link="libxml2-valid.html#xmlCopyElementContent"/>
    <function name="xmlCopyElementTable ()" link="libxml2-valid.html#xmlCopyElementTable"/>
    <function name="xmlCopyEntitiesTable ()" link="libxml2-entities.html#xmlCopyEntitiesTable"/>
    <function name="xmlCopyEnumeration ()" link="libxml2-valid.html#xmlCopyEnumeration"/>
    <function name="xmlCopyError ()" link="libxml2-xmlerror.html#xmlCopyError"/>
    <function name="xmlCopyNamespace ()" link="libxml2-tree.html#xmlCopyNamespace"/>
    <function name="xmlCopyNamespaceList ()" link="libxml2-tree.html#xmlCopyNamespaceList"/>
    <function name="xmlCopyNode ()" link="libxml2-tree.html#xmlCopyNode"/>
    <function name="xmlCopyNodeList ()" link="libxml2-tree.html#xmlCopyNodeList"/>
    <function name="xmlCopyNotationTable ()" link="libxml2-valid.html#xmlCopyNotationTable"/>
    <function name="xmlCopyProp ()" link="libxml2-tree.html#xmlCopyProp"/>
    <function name="xmlCopyPropList ()" link="libxml2-tree.html#xmlCopyPropList"/>
    <function name="xmlCreateDocParserCtxt ()" link="libxml2-parser.html#xmlCreateDocParserCtxt"/>
    <function name="xmlCreateEntitiesTable ()" link="libxml2-entities.html#xmlCreateEntitiesTable"/>
    <function name="xmlCreateEntityParserCtxt ()" link="libxml2-parserInternals.html#xmlCreateEntityParserCtxt"/>
    <function name="xmlCreateEnumeration ()" link="libxml2-valid.html#xmlCreateEnumeration"/>
    <function name="xmlCreateFileParserCtxt ()" link="libxml2-parserInternals.html#xmlCreateFileParserCtxt"/>
    <function name="xmlCreateIOParserCtxt ()" link="libxml2-parser.html#xmlCreateIOParserCtxt"/>
    <function name="xmlCreateIntSubset ()" link="libxml2-tree.html#xmlCreateIntSubset"/>
    <function name="xmlCreateMemoryParserCtxt ()" link="libxml2-parserInternals.html#xmlCreateMemoryParserCtxt"/>
    <function name="xmlCreatePushParserCtxt ()" link="libxml2-parser.html#xmlCreatePushParserCtxt"/>
    <function name="xmlCreateURI ()" link="libxml2-uri.html#xmlCreateURI"/>
    <function name="xmlCreateURLParserCtxt ()" link="libxml2-parserInternals.html#xmlCreateURLParserCtxt"/>
    <function name="xmlCtxtGetLastError ()" link="libxml2-xmlerror.html#xmlCtxtGetLastError"/>
    <function name="xmlCtxtReadDoc ()" link="libxml2-parser.html#xmlCtxtReadDoc"/>
    <function name="xmlCtxtReadFd ()" link="libxml2-parser.html#xmlCtxtReadFd"/>
    <function name="xmlCtxtReadFile ()" link="libxml2-parser.html#xmlCtxtReadFile"/>
    <function name="xmlCtxtReadIO ()" link="libxml2-parser.html#xmlCtxtReadIO"/>
    <function name="xmlCtxtReadMemory ()" link="libxml2-parser.html#xmlCtxtReadMemory"/>
    <function name="xmlCtxtReset ()" link="libxml2-parser.html#xmlCtxtReset"/>
    <function name="xmlCtxtResetLastError ()" link="libxml2-xmlerror.html#xmlCtxtResetLastError"/>
    <function name="xmlCtxtResetPush ()" link="libxml2-parser.html#xmlCtxtResetPush"/>
    <function name="xmlCtxtUseOptions ()" link="libxml2-parser.html#xmlCtxtUseOptions"/>
    <function name="xmlCurrentChar ()" link="libxml2-parserInternals.html#xmlCurrentChar"/>
    <function name="xmlDOMWrapAdoptNode ()" link="libxml2-tree.html#xmlDOMWrapAdoptNode"/>
    <function name="xmlDOMWrapCloneNode ()" link="libxml2-tree.html#xmlDOMWrapCloneNode"/>
    <function name="xmlDOMWrapFreeCtxt ()" link="libxml2-tree.html#xmlDOMWrapFreeCtxt"/>
    <function name="xmlDOMWrapNewCtxt ()" link="libxml2-tree.html#xmlDOMWrapNewCtxt"/>
    <function name="xmlDOMWrapReconcileNamespaces ()" link="libxml2-tree.html#xmlDOMWrapReconcileNamespaces"/>
    <function name="xmlDOMWrapRemoveNode ()" link="libxml2-tree.html#xmlDOMWrapRemoveNode"/>
    <function name="xmlDebugCheckDocument ()" link="libxml2-debugXML.html#xmlDebugCheckDocument"/>
    <function name="xmlDebugDumpAttr ()" link="libxml2-debugXML.html#xmlDebugDumpAttr"/>
    <function name="xmlDebugDumpAttrList ()" link="libxml2-debugXML.html#xmlDebugDumpAttrList"/>
    <function name="xmlDebugDumpDTD ()" link="libxml2-debugXML.html#xmlDebugDumpDTD"/>
    <function name="xmlDebugDumpDocument ()" link="libxml2-debugXML.html#xmlDebugDumpDocument"/>
    <function name="xmlDebugDumpDocumentHead ()" link="libxml2-debugXML.html#xmlDebugDumpDocumentHead"/>
    <function name="xmlDebugDumpEntities ()" link="libxml2-debugXML.html#xmlDebugDumpEntities"/>
    <function name="xmlDebugDumpNode ()" link="libxml2-debugXML.html#xmlDebugDumpNode"/>
    <function name="xmlDebugDumpNodeList ()" link="libxml2-debugXML.html#xmlDebugDumpNodeList"/>
    <function name="xmlDebugDumpOneNode ()" link="libxml2-debugXML.html#xmlDebugDumpOneNode"/>
    <function name="xmlDebugDumpString ()" link="libxml2-debugXML.html#xmlDebugDumpString"/>
    <function name="xmlDecodeEntities ()" link="libxml2-parserInternals.html#xmlDecodeEntities"/>
    <function name="xmlDefaultSAXHandlerInit ()" link="libxml2-SAX2.html#xmlDefaultSAXHandlerInit"/>
    <function name="xmlDelEncodingAlias ()" link="libxml2-encoding.html#xmlDelEncodingAlias"/>
    <function name="xmlDeregisterNodeDefault ()" link="libxml2-globals.html#xmlDeregisterNodeDefault"/>
    <function name="xmlDetectCharEncoding ()" link="libxml2-encoding.html#xmlDetectCharEncoding"/>
    <function name="xmlDictCleanup ()" link="libxml2-dict.html#xmlDictCleanup"/>
    <function name="xmlDictCreate ()" link="libxml2-dict.html#xmlDictCreate"/>
    <function name="xmlDictCreateSub ()" link="libxml2-dict.html#xmlDictCreateSub"/>
    <function name="xmlDictExists ()" link="libxml2-dict.html#xmlDictExists"/>
    <function name="xmlDictFree ()" link="libxml2-dict.html#xmlDictFree"/>
    <function name="xmlDictGetUsage ()" link="libxml2-dict.html#xmlDictGetUsage"/>
    <function name="xmlDictLookup ()" link="libxml2-dict.html#xmlDictLookup"/>
    <function name="xmlDictOwns ()" link="libxml2-dict.html#xmlDictOwns"/>
    <function name="xmlDictQLookup ()" link="libxml2-dict.html#xmlDictQLookup"/>
    <function name="xmlDictReference ()" link="libxml2-dict.html#xmlDictReference"/>
    <function name="xmlDictSetLimit ()" link="libxml2-dict.html#xmlDictSetLimit"/>
    <function name="xmlDictSize ()" link="libxml2-dict.html#xmlDictSize"/>
    <function name="xmlDllMain ()" link="libxml2-threads.html#xmlDllMain"/>
    <function name="xmlDocCopyNode ()" link="libxml2-tree.html#xmlDocCopyNode"/>
    <function name="xmlDocCopyNodeList ()" link="libxml2-tree.html#xmlDocCopyNodeList"/>
    <function name="xmlDocDump ()" link="libxml2-tree.html#xmlDocDump"/>
    <function name="xmlDocDumpFormatMemory ()" link="libxml2-tree.html#xmlDocDumpFormatMemory"/>
    <function name="xmlDocDumpFormatMemoryEnc ()" link="libxml2-tree.html#xmlDocDumpFormatMemoryEnc"/>
    <function name="xmlDocDumpMemory ()" link="libxml2-tree.html#xmlDocDumpMemory"/>
    <function name="xmlDocDumpMemoryEnc ()" link="libxml2-tree.html#xmlDocDumpMemoryEnc"/>
    <function name="xmlDocFormatDump ()" link="libxml2-tree.html#xmlDocFormatDump"/>
    <function name="xmlDocGetRootElement ()" link="libxml2-tree.html#xmlDocGetRootElement"/>
    <function name="xmlDocSetRootElement ()" link="libxml2-tree.html#xmlDocSetRootElement"/>
    <function name="xmlDumpAttributeDecl ()" link="libxml2-valid.html#xmlDumpAttributeDecl"/>
    <function name="xmlDumpAttributeTable ()" link="libxml2-valid.html#xmlDumpAttributeTable"/>
    <function name="xmlDumpElementDecl ()" link="libxml2-valid.html#xmlDumpElementDecl"/>
    <function name="xmlDumpElementTable ()" link="libxml2-valid.html#xmlDumpElementTable"/>
    <function name="xmlDumpEntitiesTable ()" link="libxml2-entities.html#xmlDumpEntitiesTable"/>
    <function name="xmlDumpEntityDecl ()" link="libxml2-entities.html#xmlDumpEntityDecl"/>
    <function name="xmlDumpNotationDecl ()" link="libxml2-valid.html#xmlDumpNotationDecl"/>
    <function name="xmlDumpNotationTable ()" link="libxml2-valid.html#xmlDumpNotationTable"/>
    <function name="xmlElemDump ()" link="libxml2-tree.html#xmlElemDump"/>
    <function name="xmlEncodeEntities ()" link="libxml2-entities.html#xmlEncodeEntities"/>
    <function name="xmlEncodeEntitiesReentrant ()" link="libxml2-entities.html#xmlEncodeEntitiesReentrant"/>
    <function name="xmlEncodeSpecialChars ()" link="libxml2-entities.html#xmlEncodeSpecialChars"/>
    <function name="xmlErrMemory ()" link="libxml2-parserInternals.html#xmlErrMemory"/>
    <function name="xmlExpCtxtNbCons ()" link="libxml2-xmlregexp.html#xmlExpCtxtNbCons"/>
    <function name="xmlExpCtxtNbNodes ()" link="libxml2-xmlregexp.html#xmlExpCtxtNbNodes"/>
    <function name="xmlExpDump ()" link="libxml2-xmlregexp.html#xmlExpDump"/>
    <function name="xmlExpExpDerive ()" link="libxml2-xmlregexp.html#xmlExpExpDerive"/>
    <function name="xmlExpFree ()" link="libxml2-xmlregexp.html#xmlExpFree"/>
    <function name="xmlExpFreeCtxt ()" link="libxml2-xmlregexp.html#xmlExpFreeCtxt"/>
    <function name="xmlExpGetLanguage ()" link="libxml2-xmlregexp.html#xmlExpGetLanguage"/>
    <function name="xmlExpGetStart ()" link="libxml2-xmlregexp.html#xmlExpGetStart"/>
    <function name="xmlExpIsNillable ()" link="libxml2-xmlregexp.html#xmlExpIsNillable"/>
    <function name="xmlExpMaxToken ()" link="libxml2-xmlregexp.html#xmlExpMaxToken"/>
    <function name="xmlExpNewAtom ()" link="libxml2-xmlregexp.html#xmlExpNewAtom"/>
    <function name="xmlExpNewCtxt ()" link="libxml2-xmlregexp.html#xmlExpNewCtxt"/>
    <function name="xmlExpNewOr ()" link="libxml2-xmlregexp.html#xmlExpNewOr"/>
    <function name="xmlExpNewRange ()" link="libxml2-xmlregexp.html#xmlExpNewRange"/>
    <function name="xmlExpNewSeq ()" link="libxml2-xmlregexp.html#xmlExpNewSeq"/>
    <function name="xmlExpParse ()" link="libxml2-xmlregexp.html#xmlExpParse"/>
    <function name="xmlExpRef ()" link="libxml2-xmlregexp.html#xmlExpRef"/>
    <function name="xmlExpStringDerive ()" link="libxml2-xmlregexp.html#xmlExpStringDerive"/>
    <function name="xmlExpSubsume ()" link="libxml2-xmlregexp.html#xmlExpSubsume"/>
    <function name="xmlFileClose ()" link="libxml2-xmlIO.html#xmlFileClose"/>
    <function name="xmlFileMatch ()" link="libxml2-xmlIO.html#xmlFileMatch"/>
    <function name="xmlFileOpen ()" link="libxml2-xmlIO.html#xmlFileOpen"/>
    <function name="xmlFileRead ()" link="libxml2-xmlIO.html#xmlFileRead"/>
    <function name="xmlFindCharEncodingHandler ()" link="libxml2-encoding.html#xmlFindCharEncodingHandler"/>
    <function name="xmlFirstElementChild ()" link="libxml2-tree.html#xmlFirstElementChild"/>
    <function name="xmlFreeAttributeTable ()" link="libxml2-valid.html#xmlFreeAttributeTable"/>
    <function name="xmlFreeAutomata ()" link="libxml2-xmlautomata.html#xmlFreeAutomata"/>
    <function name="xmlFreeCatalog ()" link="libxml2-catalog.html#xmlFreeCatalog"/>
    <function name="xmlFreeDoc ()" link="libxml2-tree.html#xmlFreeDoc"/>
    <function name="xmlFreeDocElementContent ()" link="libxml2-valid.html#xmlFreeDocElementContent"/>
    <function name="xmlFreeDtd ()" link="libxml2-tree.html#xmlFreeDtd"/>
    <function name="xmlFreeElementContent ()" link="libxml2-valid.html#xmlFreeElementContent"/>
    <function name="xmlFreeElementTable ()" link="libxml2-valid.html#xmlFreeElementTable"/>
    <function name="xmlFreeEntitiesTable ()" link="libxml2-entities.html#xmlFreeEntitiesTable"/>
    <function name="xmlFreeEnumeration ()" link="libxml2-valid.html#xmlFreeEnumeration"/>
    <function name="xmlFreeIDTable ()" link="libxml2-valid.html#xmlFreeIDTable"/>
    <function name="xmlFreeInputStream ()" link="libxml2-parserInternals.html#xmlFreeInputStream"/>
    <function name="xmlFreeMutex ()" link="libxml2-threads.html#xmlFreeMutex"/>
    <function name="xmlFreeNode ()" link="libxml2-tree.html#xmlFreeNode"/>
    <function name="xmlFreeNodeList ()" link="libxml2-tree.html#xmlFreeNodeList"/>
    <function name="xmlFreeNotationTable ()" link="libxml2-valid.html#xmlFreeNotationTable"/>
    <function name="xmlFreeNs ()" link="libxml2-tree.html#xmlFreeNs"/>
    <function name="xmlFreeNsList ()" link="libxml2-tree.html#xmlFreeNsList"/>
    <function name="xmlFreeParserCtxt ()" link="libxml2-parser.html#xmlFreeParserCtxt"/>
    <function name="xmlFreeParserInputBuffer ()" link="libxml2-xmlIO.html#xmlFreeParserInputBuffer"/>
    <function name="xmlFreePattern ()" link="libxml2-pattern.html#xmlFreePattern"/>
    <function name="xmlFreePatternList ()" link="libxml2-pattern.html#xmlFreePatternList"/>
    <function name="xmlFreeProp ()" link="libxml2-tree.html#xmlFreeProp"/>
    <function name="xmlFreePropList ()" link="libxml2-tree.html#xmlFreePropList"/>
    <function name="xmlFreeRMutex ()" link="libxml2-threads.html#xmlFreeRMutex"/>
    <function name="xmlFreeRefTable ()" link="libxml2-valid.html#xmlFreeRefTable"/>
    <function name="xmlFreeStreamCtxt ()" link="libxml2-pattern.html#xmlFreeStreamCtxt"/>
    <function name="xmlFreeTextReader ()" link="libxml2-xmlreader.html#xmlFreeTextReader"/>
    <function name="xmlFreeTextWriter ()" link="libxml2-xmlwriter.html#xmlFreeTextWriter"/>
    <function name="xmlFreeURI ()" link="libxml2-uri.html#xmlFreeURI"/>
    <function name="xmlFreeValidCtxt ()" link="libxml2-valid.html#xmlFreeValidCtxt"/>
    <function name="xmlGcMemGet ()" link="libxml2-xmlmemory.html#xmlGcMemGet"/>
    <function name="xmlGcMemSetup ()" link="libxml2-xmlmemory.html#xmlGcMemSetup"/>
    <function name="xmlGetBufferAllocationScheme ()" link="libxml2-tree.html#xmlGetBufferAllocationScheme"/>
    <function name="xmlGetCharEncodingHandler ()" link="libxml2-encoding.html#xmlGetCharEncodingHandler"/>
    <function name="xmlGetCharEncodingName ()" link="libxml2-encoding.html#xmlGetCharEncodingName"/>
    <function name="xmlGetCompressMode ()" link="libxml2-tree.html#xmlGetCompressMode"/>
    <function name="xmlGetDocCompressMode ()" link="libxml2-tree.html#xmlGetDocCompressMode"/>
    <function name="xmlGetDocEntity ()" link="libxml2-entities.html#xmlGetDocEntity"/>
    <function name="xmlGetDtdAttrDesc ()" link="libxml2-valid.html#xmlGetDtdAttrDesc"/>
    <function name="xmlGetDtdElementDesc ()" link="libxml2-valid.html#xmlGetDtdElementDesc"/>
    <function name="xmlGetDtdEntity ()" link="libxml2-entities.html#xmlGetDtdEntity"/>
    <function name="xmlGetDtdNotationDesc ()" link="libxml2-valid.html#xmlGetDtdNotationDesc"/>
    <function name="xmlGetDtdQAttrDesc ()" link="libxml2-valid.html#xmlGetDtdQAttrDesc"/>
    <function name="xmlGetDtdQElementDesc ()" link="libxml2-valid.html#xmlGetDtdQElementDesc"/>
    <function name="xmlGetEncodingAlias ()" link="libxml2-encoding.html#xmlGetEncodingAlias"/>
    <function name="xmlGetExternalEntityLoader ()" link="libxml2-parser.html#xmlGetExternalEntityLoader"/>
    <function name="xmlGetFeature ()" link="libxml2-parser.html#xmlGetFeature"/>
    <function name="xmlGetFeaturesList ()" link="libxml2-parser.html#xmlGetFeaturesList"/>
    <function name="xmlGetGlobalState ()" link="libxml2-threads.html#xmlGetGlobalState"/>
    <function name="xmlGetID ()" link="libxml2-valid.html#xmlGetID"/>
    <function name="xmlGetIntSubset ()" link="libxml2-tree.html#xmlGetIntSubset"/>
    <function name="xmlGetLastChild ()" link="libxml2-tree.html#xmlGetLastChild"/>
    <function name="xmlGetLastError ()" link="libxml2-xmlerror.html#xmlGetLastError"/>
    <function name="xmlGetLineNo ()" link="libxml2-tree.html#xmlGetLineNo"/>
    <function name="xmlGetNoNsProp ()" link="libxml2-tree.html#xmlGetNoNsProp"/>
    <function name="xmlGetNodePath ()" link="libxml2-tree.html#xmlGetNodePath"/>
    <function name="xmlGetNsList ()" link="libxml2-tree.html#xmlGetNsList"/>
    <function name="xmlGetNsProp ()" link="libxml2-tree.html#xmlGetNsProp"/>
    <function name="xmlGetParameterEntity ()" link="libxml2-entities.html#xmlGetParameterEntity"/>
    <function name="xmlGetPredefinedEntity ()" link="libxml2-entities.html#xmlGetPredefinedEntity"/>
    <function name="xmlGetProp ()" link="libxml2-tree.html#xmlGetProp"/>
    <function name="xmlGetRefs ()" link="libxml2-valid.html#xmlGetRefs"/>
    <function name="xmlGetThreadId ()" link="libxml2-threads.html#xmlGetThreadId"/>
    <function name="xmlGetUTF8Char ()" link="libxml2-xmlstring.html#xmlGetUTF8Char"/>
    <function name="xmlHandleEntity ()" link="libxml2-parserInternals.html#xmlHandleEntity"/>
    <function name="xmlHasFeature ()" link="libxml2-parser.html#xmlHasFeature"/>
    <function name="xmlHasNsProp ()" link="libxml2-tree.html#xmlHasNsProp"/>
    <function name="xmlHasProp ()" link="libxml2-tree.html#xmlHasProp"/>
    <function name="xmlHashAddEntry ()" link="libxml2-hash.html#xmlHashAddEntry"/>
    <function name="xmlHashAddEntry2 ()" link="libxml2-hash.html#xmlHashAddEntry2"/>
    <function name="xmlHashAddEntry3 ()" link="libxml2-hash.html#xmlHashAddEntry3"/>
    <function name="xmlHashCopy ()" link="libxml2-hash.html#xmlHashCopy"/>
    <function name="xmlHashCreate ()" link="libxml2-hash.html#xmlHashCreate"/>
    <function name="xmlHashCreateDict ()" link="libxml2-hash.html#xmlHashCreateDict"/>
    <function name="xmlHashFree ()" link="libxml2-hash.html#xmlHashFree"/>
    <function name="xmlHashLookup ()" link="libxml2-hash.html#xmlHashLookup"/>
    <function name="xmlHashLookup2 ()" link="libxml2-hash.html#xmlHashLookup2"/>
    <function name="xmlHashLookup3 ()" link="libxml2-hash.html#xmlHashLookup3"/>
    <function name="xmlHashQLookup ()" link="libxml2-hash.html#xmlHashQLookup"/>
    <function name="xmlHashQLookup2 ()" link="libxml2-hash.html#xmlHashQLookup2"/>
    <function name="xmlHashQLookup3 ()" link="libxml2-hash.html#xmlHashQLookup3"/>
    <function name="xmlHashRemoveEntry ()" link="libxml2-hash.html#xmlHashRemoveEntry"/>
    <function name="xmlHashRemoveEntry2 ()" link="libxml2-hash.html#xmlHashRemoveEntry2"/>
    <function name="xmlHashRemoveEntry3 ()" link="libxml2-hash.html#xmlHashRemoveEntry3"/>
    <function name="xmlHashScan ()" link="libxml2-hash.html#xmlHashScan"/>
    <function name="xmlHashScan3 ()" link="libxml2-hash.html#xmlHashScan3"/>
    <function name="xmlHashScanFull ()" link="libxml2-hash.html#xmlHashScanFull"/>
    <function name="xmlHashScanFull3 ()" link="libxml2-hash.html#xmlHashScanFull3"/>
    <function name="xmlHashSize ()" link="libxml2-hash.html#xmlHashSize"/>
    <function name="xmlHashUpdateEntry ()" link="libxml2-hash.html#xmlHashUpdateEntry"/>
    <function name="xmlHashUpdateEntry2 ()" link="libxml2-hash.html#xmlHashUpdateEntry2"/>
    <function name="xmlHashUpdateEntry3 ()" link="libxml2-hash.html#xmlHashUpdateEntry3"/>
    <function name="xmlIOFTPClose ()" link="libxml2-xmlIO.html#xmlIOFTPClose"/>
    <function name="xmlIOFTPMatch ()" link="libxml2-xmlIO.html#xmlIOFTPMatch"/>
    <function name="xmlIOFTPOpen ()" link="libxml2-xmlIO.html#xmlIOFTPOpen"/>
    <function name="xmlIOFTPRead ()" link="libxml2-xmlIO.html#xmlIOFTPRead"/>
    <function name="xmlIOHTTPClose ()" link="libxml2-xmlIO.html#xmlIOHTTPClose"/>
    <function name="xmlIOHTTPMatch ()" link="libxml2-xmlIO.html#xmlIOHTTPMatch"/>
    <function name="xmlIOHTTPOpen ()" link="libxml2-xmlIO.html#xmlIOHTTPOpen"/>
    <function name="xmlIOHTTPOpenW ()" link="libxml2-xmlIO.html#xmlIOHTTPOpenW"/>
    <function name="xmlIOHTTPRead ()" link="libxml2-xmlIO.html#xmlIOHTTPRead"/>
    <function name="xmlIOParseDTD ()" link="libxml2-parser.html#xmlIOParseDTD"/>
    <function name="xmlInitCharEncodingHandlers ()" link="libxml2-encoding.html#xmlInitCharEncodingHandlers"/>
    <function name="xmlInitGlobals ()" link="libxml2-globals.html#xmlInitGlobals"/>
    <function name="xmlInitMemory ()" link="libxml2-xmlmemory.html#xmlInitMemory"/>
    <function name="xmlInitNodeInfoSeq ()" link="libxml2-parser.html#xmlInitNodeInfoSeq"/>
    <function name="xmlInitParser ()" link="libxml2-parser.html#xmlInitParser"/>
    <function name="xmlInitParserCtxt ()" link="libxml2-parser.html#xmlInitParserCtxt"/>
    <function name="xmlInitThreads ()" link="libxml2-threads.html#xmlInitThreads"/>
    <function name="xmlInitializeCatalog ()" link="libxml2-catalog.html#xmlInitializeCatalog"/>
    <function name="xmlInitializeDict ()" link="libxml2-dict.html#xmlInitializeDict"/>
    <function name="xmlInitializeGlobalState ()" link="libxml2-globals.html#xmlInitializeGlobalState"/>
    <function name="xmlInitializePredefinedEntities ()" link="libxml2-entities.html#xmlInitializePredefinedEntities"/>
    <function name="xmlIsBaseChar ()" link="libxml2-chvalid.html#xmlIsBaseChar"/>
    <function name="xmlIsBlank ()" link="libxml2-chvalid.html#xmlIsBlank"/>
    <function name="xmlIsBlankNode ()" link="libxml2-tree.html#xmlIsBlankNode"/>
    <function name="xmlIsChar ()" link="libxml2-chvalid.html#xmlIsChar"/>
    <function name="xmlIsCombining ()" link="libxml2-chvalid.html#xmlIsCombining"/>
    <function name="xmlIsDigit ()" link="libxml2-chvalid.html#xmlIsDigit"/>
    <function name="xmlIsExtender ()" link="libxml2-chvalid.html#xmlIsExtender"/>
    <function name="xmlIsID ()" link="libxml2-valid.html#xmlIsID"/>
    <function name="xmlIsIdeographic ()" link="libxml2-chvalid.html#xmlIsIdeographic"/>
    <function name="xmlIsLetter ()" link="libxml2-parserInternals.html#xmlIsLetter"/>
    <function name="xmlIsMainThread ()" link="libxml2-threads.html#xmlIsMainThread"/>
    <function name="xmlIsMixedElement ()" link="libxml2-valid.html#xmlIsMixedElement"/>
    <function name="xmlIsPubidChar ()" link="libxml2-chvalid.html#xmlIsPubidChar"/>
    <function name="xmlIsRef ()" link="libxml2-valid.html#xmlIsRef"/>
    <function name="xmlIsXHTML ()" link="libxml2-tree.html#xmlIsXHTML"/>
    <function name="xmlKeepBlanksDefault ()" link="libxml2-parser.html#xmlKeepBlanksDefault"/>
    <function name="xmlLastElementChild ()" link="libxml2-tree.html#xmlLastElementChild"/>
    <function name="xmlLineNumbersDefault ()" link="libxml2-parser.html#xmlLineNumbersDefault"/>
    <function name="xmlLinkGetData ()" link="libxml2-list.html#xmlLinkGetData"/>
    <function name="xmlListAppend ()" link="libxml2-list.html#xmlListAppend"/>
    <function name="xmlListClear ()" link="libxml2-list.html#xmlListClear"/>
    <function name="xmlListCopy ()" link="libxml2-list.html#xmlListCopy"/>
    <function name="xmlListCreate ()" link="libxml2-list.html#xmlListCreate"/>
    <function name="xmlListDelete ()" link="libxml2-list.html#xmlListDelete"/>
    <function name="xmlListDup ()" link="libxml2-list.html#xmlListDup"/>
    <function name="xmlListEmpty ()" link="libxml2-list.html#xmlListEmpty"/>
    <function name="xmlListEnd ()" link="libxml2-list.html#xmlListEnd"/>
    <function name="xmlListFront ()" link="libxml2-list.html#xmlListFront"/>
    <function name="xmlListInsert ()" link="libxml2-list.html#xmlListInsert"/>
    <function name="xmlListMerge ()" link="libxml2-list.html#xmlListMerge"/>
    <function name="xmlListPopBack ()" link="libxml2-list.html#xmlListPopBack"/>
    <function name="xmlListPopFront ()" link="libxml2-list.html#xmlListPopFront"/>
    <function name="xmlListPushBack ()" link="libxml2-list.html#xmlListPushBack"/>
    <function name="xmlListPushFront ()" link="libxml2-list.html#xmlListPushFront"/>
    <function name="xmlListRemoveAll ()" link="libxml2-list.html#xmlListRemoveAll"/>
    <function name="xmlListRemoveFirst ()" link="libxml2-list.html#xmlListRemoveFirst"/>
    <function name="xmlListRemoveLast ()" link="libxml2-list.html#xmlListRemoveLast"/>
    <function name="xmlListReverse ()" link="libxml2-list.html#xmlListReverse"/>
    <function name="xmlListReverseSearch ()" link="libxml2-list.html#xmlListReverseSearch"/>
    <function name="xmlListReverseWalk ()" link="libxml2-list.html#xmlListReverseWalk"/>
    <function name="xmlListSearch ()" link="libxml2-list.html#xmlListSearch"/>
    <function name="xmlListSize ()" link="libxml2-list.html#xmlListSize"/>
    <function name="xmlListSort ()" link="libxml2-list.html#xmlListSort"/>
    <function name="xmlListWalk ()" link="libxml2-list.html#xmlListWalk"/>
    <function name="xmlLoadACatalog ()" link="libxml2-catalog.html#xmlLoadACatalog"/>
    <function name="xmlLoadCatalog ()" link="libxml2-catalog.html#xmlLoadCatalog"/>
    <function name="xmlLoadCatalogs ()" link="libxml2-catalog.html#xmlLoadCatalogs"/>
    <function name="xmlLoadExternalEntity ()" link="libxml2-parser.html#xmlLoadExternalEntity"/>
    <function name="xmlLoadSGMLSuperCatalog ()" link="libxml2-catalog.html#xmlLoadSGMLSuperCatalog"/>
    <function name="xmlLockLibrary ()" link="libxml2-threads.html#xmlLockLibrary"/>
    <function name="xmlLsCountNode ()" link="libxml2-debugXML.html#xmlLsCountNode"/>
    <function name="xmlLsOneNode ()" link="libxml2-debugXML.html#xmlLsOneNode"/>
    <function name="xmlMallocAtomicLoc ()" link="libxml2-xmlmemory.html#xmlMallocAtomicLoc"/>
    <function name="xmlMallocLoc ()" link="libxml2-xmlmemory.html#xmlMallocLoc"/>
    <function name="xmlMemBlocks ()" link="libxml2-xmlmemory.html#xmlMemBlocks"/>
    <function name="xmlMemDisplay ()" link="libxml2-xmlmemory.html#xmlMemDisplay"/>
    <function name="xmlMemDisplayLast ()" link="libxml2-xmlmemory.html#xmlMemDisplayLast"/>
    <function name="xmlMemFree ()" link="libxml2-xmlmemory.html#xmlMemFree"/>
    <function name="xmlMemGet ()" link="libxml2-xmlmemory.html#xmlMemGet"/>
    <function name="xmlMemMalloc ()" link="libxml2-xmlmemory.html#xmlMemMalloc"/>
    <function name="xmlMemRealloc ()" link="libxml2-xmlmemory.html#xmlMemRealloc"/>
    <function name="xmlMemSetup ()" link="libxml2-xmlmemory.html#xmlMemSetup"/>
    <function name="xmlMemShow ()" link="libxml2-xmlmemory.html#xmlMemShow"/>
    <function name="xmlMemStrdupLoc ()" link="libxml2-xmlmemory.html#xmlMemStrdupLoc"/>
    <function name="xmlMemUsed ()" link="libxml2-xmlmemory.html#xmlMemUsed"/>
    <function name="xmlMemoryDump ()" link="libxml2-xmlmemory.html#xmlMemoryDump"/>
    <function name="xmlMemoryStrdup ()" link="libxml2-xmlmemory.html#xmlMemoryStrdup"/>
    <function name="xmlModuleClose ()" link="libxml2-xmlmodule.html#xmlModuleClose"/>
    <function name="xmlModuleFree ()" link="libxml2-xmlmodule.html#xmlModuleFree"/>
    <function name="xmlModuleOpen ()" link="libxml2-xmlmodule.html#xmlModuleOpen"/>
    <function name="xmlModuleSymbol ()" link="libxml2-xmlmodule.html#xmlModuleSymbol"/>
    <function name="xmlMutexLock ()" link="libxml2-threads.html#xmlMutexLock"/>
    <function name="xmlMutexUnlock ()" link="libxml2-threads.html#xmlMutexUnlock"/>
    <function name="xmlNamespaceParseNCName ()" link="libxml2-parserInternals.html#xmlNamespaceParseNCName"/>
    <function name="xmlNamespaceParseNSDef ()" link="libxml2-parserInternals.html#xmlNamespaceParseNSDef"/>
    <function name="xmlNamespaceParseQName ()" link="libxml2-parserInternals.html#xmlNamespaceParseQName"/>
    <function name="xmlNanoFTPCheckResponse ()" link="libxml2-nanoftp.html#xmlNanoFTPCheckResponse"/>
    <function name="xmlNanoFTPCleanup ()" link="libxml2-nanoftp.html#xmlNanoFTPCleanup"/>
    <function name="xmlNanoFTPClose ()" link="libxml2-nanoftp.html#xmlNanoFTPClose"/>
    <function name="xmlNanoFTPCloseConnection ()" link="libxml2-nanoftp.html#xmlNanoFTPCloseConnection"/>
    <function name="xmlNanoFTPConnect ()" link="libxml2-nanoftp.html#xmlNanoFTPConnect"/>
    <function name="xmlNanoFTPConnectTo ()" link="libxml2-nanoftp.html#xmlNanoFTPConnectTo"/>
    <function name="xmlNanoFTPCwd ()" link="libxml2-nanoftp.html#xmlNanoFTPCwd"/>
    <function name="xmlNanoFTPDele ()" link="libxml2-nanoftp.html#xmlNanoFTPDele"/>
    <function name="xmlNanoFTPFreeCtxt ()" link="libxml2-nanoftp.html#xmlNanoFTPFreeCtxt"/>
    <function name="xmlNanoFTPGet ()" link="libxml2-nanoftp.html#xmlNanoFTPGet"/>
    <function name="xmlNanoFTPGetConnection ()" link="libxml2-nanoftp.html#xmlNanoFTPGetConnection"/>
    <function name="xmlNanoFTPGetResponse ()" link="libxml2-nanoftp.html#xmlNanoFTPGetResponse"/>
    <function name="xmlNanoFTPGetSocket ()" link="libxml2-nanoftp.html#xmlNanoFTPGetSocket"/>
    <function name="xmlNanoFTPInit ()" link="libxml2-nanoftp.html#xmlNanoFTPInit"/>
    <function name="xmlNanoFTPList ()" link="libxml2-nanoftp.html#xmlNanoFTPList"/>
    <function name="xmlNanoFTPNewCtxt ()" link="libxml2-nanoftp.html#xmlNanoFTPNewCtxt"/>
    <function name="xmlNanoFTPOpen ()" link="libxml2-nanoftp.html#xmlNanoFTPOpen"/>
    <function name="xmlNanoFTPProxy ()" link="libxml2-nanoftp.html#xmlNanoFTPProxy"/>
    <function name="xmlNanoFTPQuit ()" link="libxml2-nanoftp.html#xmlNanoFTPQuit"/>
    <function name="xmlNanoFTPRead ()" link="libxml2-nanoftp.html#xmlNanoFTPRead"/>
    <function name="xmlNanoFTPScanProxy ()" link="libxml2-nanoftp.html#xmlNanoFTPScanProxy"/>
    <function name="xmlNanoFTPUpdateURL ()" link="libxml2-nanoftp.html#xmlNanoFTPUpdateURL"/>
    <function name="xmlNanoHTTPAuthHeader ()" link="libxml2-nanohttp.html#xmlNanoHTTPAuthHeader"/>
    <function name="xmlNanoHTTPCleanup ()" link="libxml2-nanohttp.html#xmlNanoHTTPCleanup"/>
    <function name="xmlNanoHTTPClose ()" link="libxml2-nanohttp.html#xmlNanoHTTPClose"/>
    <function name="xmlNanoHTTPContentLength ()" link="libxml2-nanohttp.html#xmlNanoHTTPContentLength"/>
    <function name="xmlNanoHTTPEncoding ()" link="libxml2-nanohttp.html#xmlNanoHTTPEncoding"/>
    <function name="xmlNanoHTTPFetch ()" link="libxml2-nanohttp.html#xmlNanoHTTPFetch"/>
    <function name="xmlNanoHTTPInit ()" link="libxml2-nanohttp.html#xmlNanoHTTPInit"/>
    <function name="xmlNanoHTTPMethod ()" link="libxml2-nanohttp.html#xmlNanoHTTPMethod"/>
    <function name="xmlNanoHTTPMethodRedir ()" link="libxml2-nanohttp.html#xmlNanoHTTPMethodRedir"/>
    <function name="xmlNanoHTTPMimeType ()" link="libxml2-nanohttp.html#xmlNanoHTTPMimeType"/>
    <function name="xmlNanoHTTPOpen ()" link="libxml2-nanohttp.html#xmlNanoHTTPOpen"/>
    <function name="xmlNanoHTTPOpenRedir ()" link="libxml2-nanohttp.html#xmlNanoHTTPOpenRedir"/>
    <function name="xmlNanoHTTPRead ()" link="libxml2-nanohttp.html#xmlNanoHTTPRead"/>
    <function name="xmlNanoHTTPRedir ()" link="libxml2-nanohttp.html#xmlNanoHTTPRedir"/>
    <function name="xmlNanoHTTPReturnCode ()" link="libxml2-nanohttp.html#xmlNanoHTTPReturnCode"/>
    <function name="xmlNanoHTTPSave ()" link="libxml2-nanohttp.html#xmlNanoHTTPSave"/>
    <function name="xmlNanoHTTPScanProxy ()" link="libxml2-nanohttp.html#xmlNanoHTTPScanProxy"/>
    <function name="xmlNewAutomata ()" link="libxml2-xmlautomata.html#xmlNewAutomata"/>
    <function name="xmlNewCDataBlock ()" link="libxml2-tree.html#xmlNewCDataBlock"/>
    <function name="xmlNewCatalog ()" link="libxml2-catalog.html#xmlNewCatalog"/>
    <function name="xmlNewCharEncodingHandler ()" link="libxml2-encoding.html#xmlNewCharEncodingHandler"/>
    <function name="xmlNewCharRef ()" link="libxml2-tree.html#xmlNewCharRef"/>
    <function name="xmlNewChild ()" link="libxml2-tree.html#xmlNewChild"/>
    <function name="xmlNewComment ()" link="libxml2-tree.html#xmlNewComment"/>
    <function name="xmlNewDoc ()" link="libxml2-tree.html#xmlNewDoc"/>
    <function name="xmlNewDocComment ()" link="libxml2-tree.html#xmlNewDocComment"/>
    <function name="xmlNewDocElementContent ()" link="libxml2-valid.html#xmlNewDocElementContent"/>
    <function name="xmlNewDocFragment ()" link="libxml2-tree.html#xmlNewDocFragment"/>
    <function name="xmlNewDocNode ()" link="libxml2-tree.html#xmlNewDocNode"/>
    <function name="xmlNewDocNodeEatName ()" link="libxml2-tree.html#xmlNewDocNodeEatName"/>
    <function name="xmlNewDocPI ()" link="libxml2-tree.html#xmlNewDocPI"/>
    <function name="xmlNewDocProp ()" link="libxml2-tree.html#xmlNewDocProp"/>
    <function name="xmlNewDocRawNode ()" link="libxml2-tree.html#xmlNewDocRawNode"/>
    <function name="xmlNewDocText ()" link="libxml2-tree.html#xmlNewDocText"/>
    <function name="xmlNewDocTextLen ()" link="libxml2-tree.html#xmlNewDocTextLen"/>
    <function name="xmlNewDtd ()" link="libxml2-tree.html#xmlNewDtd"/>
    <function name="xmlNewElementContent ()" link="libxml2-valid.html#xmlNewElementContent"/>
    <function name="xmlNewEntity ()" link="libxml2-entities.html#xmlNewEntity"/>
    <function name="xmlNewEntityInputStream ()" link="libxml2-parserInternals.html#xmlNewEntityInputStream"/>
    <function name="xmlNewGlobalNs ()" link="libxml2-tree.html#xmlNewGlobalNs"/>
    <function name="xmlNewIOInputStream ()" link="libxml2-parser.html#xmlNewIOInputStream"/>
    <function name="xmlNewInputFromFile ()" link="libxml2-parserInternals.html#xmlNewInputFromFile"/>
    <function name="xmlNewInputStream ()" link="libxml2-parserInternals.html#xmlNewInputStream"/>
    <function name="xmlNewMutex ()" link="libxml2-threads.html#xmlNewMutex"/>
    <function name="xmlNewNode ()" link="libxml2-tree.html#xmlNewNode"/>
    <function name="xmlNewNodeEatName ()" link="libxml2-tree.html#xmlNewNodeEatName"/>
    <function name="xmlNewNs ()" link="libxml2-tree.html#xmlNewNs"/>
    <function name="xmlNewNsProp ()" link="libxml2-tree.html#xmlNewNsProp"/>
    <function name="xmlNewNsPropEatName ()" link="libxml2-tree.html#xmlNewNsPropEatName"/>
    <function name="xmlNewPI ()" link="libxml2-tree.html#xmlNewPI"/>
    <function name="xmlNewParserCtxt ()" link="libxml2-parser.html#xmlNewParserCtxt"/>
    <function name="xmlNewProp ()" link="libxml2-tree.html#xmlNewProp"/>
    <function name="xmlNewRMutex ()" link="libxml2-threads.html#xmlNewRMutex"/>
    <function name="xmlNewReference ()" link="libxml2-tree.html#xmlNewReference"/>
    <function name="xmlNewStringInputStream ()" link="libxml2-parserInternals.html#xmlNewStringInputStream"/>
    <function name="xmlNewText ()" link="libxml2-tree.html#xmlNewText"/>
    <function name="xmlNewTextChild ()" link="libxml2-tree.html#xmlNewTextChild"/>
    <function name="xmlNewTextLen ()" link="libxml2-tree.html#xmlNewTextLen"/>
    <function name="xmlNewTextReader ()" link="libxml2-xmlreader.html#xmlNewTextReader"/>
    <function name="xmlNewTextReaderFilename ()" link="libxml2-xmlreader.html#xmlNewTextReaderFilename"/>
    <function name="xmlNewTextWriter ()" link="libxml2-xmlwriter.html#xmlNewTextWriter"/>
    <function name="xmlNewTextWriterDoc ()" link="libxml2-xmlwriter.html#xmlNewTextWriterDoc"/>
    <function name="xmlNewTextWriterFilename ()" link="libxml2-xmlwriter.html#xmlNewTextWriterFilename"/>
    <function name="xmlNewTextWriterMemory ()" link="libxml2-xmlwriter.html#xmlNewTextWriterMemory"/>
    <function name="xmlNewTextWriterPushParser ()" link="libxml2-xmlwriter.html#xmlNewTextWriterPushParser"/>
    <function name="xmlNewTextWriterTree ()" link="libxml2-xmlwriter.html#xmlNewTextWriterTree"/>
    <function name="xmlNewValidCtxt ()" link="libxml2-valid.html#xmlNewValidCtxt"/>
    <function name="xmlNextChar ()" link="libxml2-parserInternals.html#xmlNextChar"/>
    <function name="xmlNextElementSibling ()" link="libxml2-tree.html#xmlNextElementSibling"/>
    <function name="xmlNoNetExternalEntityLoader ()" link="libxml2-xmlIO.html#xmlNoNetExternalEntityLoader"/>
    <function name="xmlNodeAddContent ()" link="libxml2-tree.html#xmlNodeAddContent"/>
    <function name="xmlNodeAddContentLen ()" link="libxml2-tree.html#xmlNodeAddContentLen"/>
    <function name="xmlNodeBufGetContent ()" link="libxml2-tree.html#xmlNodeBufGetContent"/>
    <function name="xmlNodeDump ()" link="libxml2-tree.html#xmlNodeDump"/>
    <function name="xmlNodeDumpOutput ()" link="libxml2-tree.html#xmlNodeDumpOutput"/>
    <function name="xmlNodeGetBase ()" link="libxml2-tree.html#xmlNodeGetBase"/>
    <function name="xmlNodeGetContent ()" link="libxml2-tree.html#xmlNodeGetContent"/>
    <function name="xmlNodeGetLang ()" link="libxml2-tree.html#xmlNodeGetLang"/>
    <function name="xmlNodeGetSpacePreserve ()" link="libxml2-tree.html#xmlNodeGetSpacePreserve"/>
    <function name="xmlNodeIsText ()" link="libxml2-tree.html#xmlNodeIsText"/>
    <function name="xmlNodeListGetRawString ()" link="libxml2-tree.html#xmlNodeListGetRawString"/>
    <function name="xmlNodeListGetString ()" link="libxml2-tree.html#xmlNodeListGetString"/>
    <function name="xmlNodeSetBase ()" link="libxml2-tree.html#xmlNodeSetBase"/>
    <function name="xmlNodeSetContent ()" link="libxml2-tree.html#xmlNodeSetContent"/>
    <function name="xmlNodeSetContentLen ()" link="libxml2-tree.html#xmlNodeSetContentLen"/>
    <function name="xmlNodeSetLang ()" link="libxml2-tree.html#xmlNodeSetLang"/>
    <function name="xmlNodeSetName ()" link="libxml2-tree.html#xmlNodeSetName"/>
    <function name="xmlNodeSetSpacePreserve ()" link="libxml2-tree.html#xmlNodeSetSpacePreserve"/>
    <function name="xmlNormalizeURIPath ()" link="libxml2-uri.html#xmlNormalizeURIPath"/>
    <function name="xmlNormalizeWindowsPath ()" link="libxml2-xmlIO.html#xmlNormalizeWindowsPath"/>
    <function name="xmlOutputBufferClose ()" link="libxml2-xmlIO.html#xmlOutputBufferClose"/>
    <function name="xmlOutputBufferCreateBuffer ()" link="libxml2-xmlIO.html#xmlOutputBufferCreateBuffer"/>
    <function name="xmlOutputBufferCreateFd ()" link="libxml2-xmlIO.html#xmlOutputBufferCreateFd"/>
    <function name="xmlOutputBufferCreateFile ()" link="libxml2-xmlIO.html#xmlOutputBufferCreateFile"/>
    <function name="xmlOutputBufferCreateFilename ()" link="libxml2-xmlIO.html#xmlOutputBufferCreateFilename"/>
    <function name="xmlOutputBufferCreateFilenameDefault ()" link="libxml2-globals.html#xmlOutputBufferCreateFilenameDefault"/>
    <function name="xmlOutputBufferCreateIO ()" link="libxml2-xmlIO.html#xmlOutputBufferCreateIO"/>
    <function name="xmlOutputBufferFlush ()" link="libxml2-xmlIO.html#xmlOutputBufferFlush"/>
    <function name="xmlOutputBufferGetContent ()" link="libxml2-xmlIO.html#xmlOutputBufferGetContent"/>
    <function name="xmlOutputBufferGetSize ()" link="libxml2-xmlIO.html#xmlOutputBufferGetSize"/>
    <function name="xmlOutputBufferWrite ()" link="libxml2-xmlIO.html#xmlOutputBufferWrite"/>
    <function name="xmlOutputBufferWriteEscape ()" link="libxml2-xmlIO.html#xmlOutputBufferWriteEscape"/>
    <function name="xmlOutputBufferWriteString ()" link="libxml2-xmlIO.html#xmlOutputBufferWriteString"/>
    <function name="xmlParseAttValue ()" link="libxml2-parserInternals.html#xmlParseAttValue"/>
    <function name="xmlParseAttribute ()" link="libxml2-parserInternals.html#xmlParseAttribute"/>
    <function name="xmlParseAttributeListDecl ()" link="libxml2-parserInternals.html#xmlParseAttributeListDecl"/>
    <function name="xmlParseAttributeType ()" link="libxml2-parserInternals.html#xmlParseAttributeType"/>
    <function name="xmlParseBalancedChunkMemory ()" link="libxml2-parser.html#xmlParseBalancedChunkMemory"/>
    <function name="xmlParseBalancedChunkMemoryRecover ()" link="libxml2-parser.html#xmlParseBalancedChunkMemoryRecover"/>
    <function name="xmlParseCDSect ()" link="libxml2-parserInternals.html#xmlParseCDSect"/>
    <function name="xmlParseCatalogFile ()" link="libxml2-catalog.html#xmlParseCatalogFile"/>
    <function name="xmlParseCharData ()" link="libxml2-parserInternals.html#xmlParseCharData"/>
    <function name="xmlParseCharEncoding ()" link="libxml2-encoding.html#xmlParseCharEncoding"/>
    <function name="xmlParseCharRef ()" link="libxml2-parserInternals.html#xmlParseCharRef"/>
    <function name="xmlParseChunk ()" link="libxml2-parser.html#xmlParseChunk"/>
    <function name="xmlParseComment ()" link="libxml2-parserInternals.html#xmlParseComment"/>
    <function name="xmlParseContent ()" link="libxml2-parserInternals.html#xmlParseContent"/>
    <function name="xmlParseCtxtExternalEntity ()" link="libxml2-parser.html#xmlParseCtxtExternalEntity"/>
    <function name="xmlParseDTD ()" link="libxml2-parser.html#xmlParseDTD"/>
    <function name="xmlParseDefaultDecl ()" link="libxml2-parserInternals.html#xmlParseDefaultDecl"/>
    <function name="xmlParseDoc ()" link="libxml2-parser.html#xmlParseDoc"/>
    <function name="xmlParseDocTypeDecl ()" link="libxml2-parserInternals.html#xmlParseDocTypeDecl"/>
    <function name="xmlParseDocument ()" link="libxml2-parser.html#xmlParseDocument"/>
    <function name="xmlParseElement ()" link="libxml2-parserInternals.html#xmlParseElement"/>
    <function name="xmlParseElementChildrenContentDecl ()" link="libxml2-parserInternals.html#xmlParseElementChildrenContentDecl"/>
    <function name="xmlParseElementContentDecl ()" link="libxml2-parserInternals.html#xmlParseElementContentDecl"/>
    <function name="xmlParseElementDecl ()" link="libxml2-parserInternals.html#xmlParseElementDecl"/>
    <function name="xmlParseElementMixedContentDecl ()" link="libxml2-parserInternals.html#xmlParseElementMixedContentDecl"/>
    <function name="xmlParseEncName ()" link="libxml2-parserInternals.html#xmlParseEncName"/>
    <function name="xmlParseEncodingDecl ()" link="libxml2-parserInternals.html#xmlParseEncodingDecl"/>
    <function name="xmlParseEndTag ()" link="libxml2-parserInternals.html#xmlParseEndTag"/>
    <function name="xmlParseEntity ()" link="libxml2-parser.html#xmlParseEntity"/>
    <function name="xmlParseEntityDecl ()" link="libxml2-parserInternals.html#xmlParseEntityDecl"/>
    <function name="xmlParseEntityRef ()" link="libxml2-parserInternals.html#xmlParseEntityRef"/>
    <function name="xmlParseEntityValue ()" link="libxml2-parserInternals.html#xmlParseEntityValue"/>
    <function name="xmlParseEnumeratedType ()" link="libxml2-parserInternals.html#xmlParseEnumeratedType"/>
    <function name="xmlParseEnumerationType ()" link="libxml2-parserInternals.html#xmlParseEnumerationType"/>
    <function name="xmlParseExtParsedEnt ()" link="libxml2-parser.html#xmlParseExtParsedEnt"/>
    <function name="xmlParseExternalEntity ()" link="libxml2-parser.html#xmlParseExternalEntity"/>
    <function name="xmlParseExternalID ()" link="libxml2-parserInternals.html#xmlParseExternalID"/>
    <function name="xmlParseExternalSubset ()" link="libxml2-parserInternals.html#xmlParseExternalSubset"/>
    <function name="xmlParseFile ()" link="libxml2-parser.html#xmlParseFile"/>
    <function name="xmlParseInNodeContext ()" link="libxml2-parser.html#xmlParseInNodeContext"/>
    <function name="xmlParseMarkupDecl ()" link="libxml2-parserInternals.html#xmlParseMarkupDecl"/>
    <function name="xmlParseMemory ()" link="libxml2-parser.html#xmlParseMemory"/>
    <function name="xmlParseMisc ()" link="libxml2-parserInternals.html#xmlParseMisc"/>
    <function name="xmlParseName ()" link="libxml2-parserInternals.html#xmlParseName"/>
    <function name="xmlParseNamespace ()" link="libxml2-parserInternals.html#xmlParseNamespace"/>
    <function name="xmlParseNmtoken ()" link="libxml2-parserInternals.html#xmlParseNmtoken"/>
    <function name="xmlParseNotationDecl ()" link="libxml2-parserInternals.html#xmlParseNotationDecl"/>
    <function name="xmlParseNotationType ()" link="libxml2-parserInternals.html#xmlParseNotationType"/>
    <function name="xmlParsePEReference ()" link="libxml2-parserInternals.html#xmlParsePEReference"/>
    <function name="xmlParsePI ()" link="libxml2-parserInternals.html#xmlParsePI"/>
    <function name="xmlParsePITarget ()" link="libxml2-parserInternals.html#xmlParsePITarget"/>
    <function name="xmlParsePubidLiteral ()" link="libxml2-parserInternals.html#xmlParsePubidLiteral"/>
    <function name="xmlParseQuotedString ()" link="libxml2-parserInternals.html#xmlParseQuotedString"/>
    <function name="xmlParseReference ()" link="libxml2-parserInternals.html#xmlParseReference"/>
    <function name="xmlParseSDDecl ()" link="libxml2-parserInternals.html#xmlParseSDDecl"/>
    <function name="xmlParseStartTag ()" link="libxml2-parserInternals.html#xmlParseStartTag"/>
    <function name="xmlParseSystemLiteral ()" link="libxml2-parserInternals.html#xmlParseSystemLiteral"/>
    <function name="xmlParseTextDecl ()" link="libxml2-parserInternals.html#xmlParseTextDecl"/>
    <function name="xmlParseURI ()" link="libxml2-uri.html#xmlParseURI"/>
    <function name="xmlParseURIRaw ()" link="libxml2-uri.html#xmlParseURIRaw"/>
    <function name="xmlParseURIReference ()" link="libxml2-uri.html#xmlParseURIReference"/>
    <function name="xmlParseVersionInfo ()" link="libxml2-parserInternals.html#xmlParseVersionInfo"/>
    <function name="xmlParseVersionNum ()" link="libxml2-parserInternals.html#xmlParseVersionNum"/>
    <function name="xmlParseXMLDecl ()" link="libxml2-parserInternals.html#xmlParseXMLDecl"/>
    <function name="xmlParserAddNodeInfo ()" link="libxml2-parser.html#xmlParserAddNodeInfo"/>
    <function name="xmlParserError ()" link="libxml2-xmlerror.html#xmlParserError"/>
    <function name="xmlParserFindNodeInfo ()" link="libxml2-parser.html#xmlParserFindNodeInfo"/>
    <function name="xmlParserFindNodeInfoIndex ()" link="libxml2-parser.html#xmlParserFindNodeInfoIndex"/>
    <function name="xmlParserGetDirectory ()" link="libxml2-xmlIO.html#xmlParserGetDirectory"/>
    <function name="xmlParserHandlePEReference ()" link="libxml2-parserInternals.html#xmlParserHandlePEReference"/>
    <function name="xmlParserHandleReference ()" link="libxml2-parserInternals.html#xmlParserHandleReference"/>
    <function name="xmlParserInputBufferCreateFd ()" link="libxml2-xmlIO.html#xmlParserInputBufferCreateFd"/>
    <function name="xmlParserInputBufferCreateFile ()" link="libxml2-xmlIO.html#xmlParserInputBufferCreateFile"/>
    <function name="xmlParserInputBufferCreateFilename ()" link="libxml2-xmlIO.html#xmlParserInputBufferCreateFilename"/>
    <function name="xmlParserInputBufferCreateFilenameDefault ()" link="libxml2-globals.html#xmlParserInputBufferCreateFilenameDefault"/>
    <function name="xmlParserInputBufferCreateIO ()" link="libxml2-xmlIO.html#xmlParserInputBufferCreateIO"/>
    <function name="xmlParserInputBufferCreateMem ()" link="libxml2-xmlIO.html#xmlParserInputBufferCreateMem"/>
    <function name="xmlParserInputBufferCreateStatic ()" link="libxml2-xmlIO.html#xmlParserInputBufferCreateStatic"/>
    <function name="xmlParserInputBufferGrow ()" link="libxml2-xmlIO.html#xmlParserInputBufferGrow"/>
    <function name="xmlParserInputBufferPush ()" link="libxml2-xmlIO.html#xmlParserInputBufferPush"/>
    <function name="xmlParserInputBufferRead ()" link="libxml2-xmlIO.html#xmlParserInputBufferRead"/>
    <function name="xmlParserInputGrow ()" link="libxml2-parser.html#xmlParserInputGrow"/>
    <function name="xmlParserInputRead ()" link="libxml2-parser.html#xmlParserInputRead"/>
    <function name="xmlParserInputShrink ()" link="libxml2-parserInternals.html#xmlParserInputShrink"/>
    <function name="xmlParserPrintFileContext ()" link="libxml2-xmlerror.html#xmlParserPrintFileContext"/>
    <function name="xmlParserPrintFileInfo ()" link="libxml2-xmlerror.html#xmlParserPrintFileInfo"/>
    <function name="xmlParserValidityError ()" link="libxml2-xmlerror.html#xmlParserValidityError"/>
    <function name="xmlParserValidityWarning ()" link="libxml2-xmlerror.html#xmlParserValidityWarning"/>
    <function name="xmlParserWarning ()" link="libxml2-xmlerror.html#xmlParserWarning"/>
    <function name="xmlPathToURI ()" link="libxml2-uri.html#xmlPathToURI"/>
    <function name="xmlPatternFromRoot ()" link="libxml2-pattern.html#xmlPatternFromRoot"/>
    <function name="xmlPatternGetStreamCtxt ()" link="libxml2-pattern.html#xmlPatternGetStreamCtxt"/>
    <function name="xmlPatternMatch ()" link="libxml2-pattern.html#xmlPatternMatch"/>
    <function name="xmlPatternMaxDepth ()" link="libxml2-pattern.html#xmlPatternMaxDepth"/>
    <function name="xmlPatternMinDepth ()" link="libxml2-pattern.html#xmlPatternMinDepth"/>
    <function name="xmlPatternStreamable ()" link="libxml2-pattern.html#xmlPatternStreamable"/>
    <function name="xmlPatterncompile ()" link="libxml2-pattern.html#xmlPatterncompile"/>
    <function name="xmlPedanticParserDefault ()" link="libxml2-parser.html#xmlPedanticParserDefault"/>
    <function name="xmlPopInput ()" link="libxml2-parserInternals.html#xmlPopInput"/>
    <function name="xmlPopInputCallbacks ()" link="libxml2-xmlIO.html#xmlPopInputCallbacks"/>
    <function name="xmlPreviousElementSibling ()" link="libxml2-tree.html#xmlPreviousElementSibling"/>
    <function name="xmlPrintURI ()" link="libxml2-uri.html#xmlPrintURI"/>
    <function name="xmlPushInput ()" link="libxml2-parserInternals.html#xmlPushInput"/>
    <function name="xmlRMutexLock ()" link="libxml2-threads.html#xmlRMutexLock"/>
    <function name="xmlRMutexUnlock ()" link="libxml2-threads.html#xmlRMutexUnlock"/>
    <function name="xmlReadDoc ()" link="libxml2-parser.html#xmlReadDoc"/>
    <function name="xmlReadFd ()" link="libxml2-parser.html#xmlReadFd"/>
    <function name="xmlReadFile ()" link="libxml2-parser.html#xmlReadFile"/>
    <function name="xmlReadIO ()" link="libxml2-parser.html#xmlReadIO"/>
    <function name="xmlReadMemory ()" link="libxml2-parser.html#xmlReadMemory"/>
    <function name="xmlReaderForDoc ()" link="libxml2-xmlreader.html#xmlReaderForDoc"/>
    <function name="xmlReaderForFd ()" link="libxml2-xmlreader.html#xmlReaderForFd"/>
    <function name="xmlReaderForFile ()" link="libxml2-xmlreader.html#xmlReaderForFile"/>
    <function name="xmlReaderForIO ()" link="libxml2-xmlreader.html#xmlReaderForIO"/>
    <function name="xmlReaderForMemory ()" link="libxml2-xmlreader.html#xmlReaderForMemory"/>
    <function name="xmlReaderNewDoc ()" link="libxml2-xmlreader.html#xmlReaderNewDoc"/>
    <function name="xmlReaderNewFd ()" link="libxml2-xmlreader.html#xmlReaderNewFd"/>
    <function name="xmlReaderNewFile ()" link="libxml2-xmlreader.html#xmlReaderNewFile"/>
    <function name="xmlReaderNewIO ()" link="libxml2-xmlreader.html#xmlReaderNewIO"/>
    <function name="xmlReaderNewMemory ()" link="libxml2-xmlreader.html#xmlReaderNewMemory"/>
    <function name="xmlReaderNewWalker ()" link="libxml2-xmlreader.html#xmlReaderNewWalker"/>
    <function name="xmlReaderWalker ()" link="libxml2-xmlreader.html#xmlReaderWalker"/>
    <function name="xmlReallocLoc ()" link="libxml2-xmlmemory.html#xmlReallocLoc"/>
    <function name="xmlReconciliateNs ()" link="libxml2-tree.html#xmlReconciliateNs"/>
    <function name="xmlRecoverDoc ()" link="libxml2-parser.html#xmlRecoverDoc"/>
    <function name="xmlRecoverFile ()" link="libxml2-parser.html#xmlRecoverFile"/>
    <function name="xmlRecoverMemory ()" link="libxml2-parser.html#xmlRecoverMemory"/>
    <function name="xmlRegExecErrInfo ()" link="libxml2-xmlregexp.html#xmlRegExecErrInfo"/>
    <function name="xmlRegExecNextValues ()" link="libxml2-xmlregexp.html#xmlRegExecNextValues"/>
    <function name="xmlRegExecPushString ()" link="libxml2-xmlregexp.html#xmlRegExecPushString"/>
    <function name="xmlRegExecPushString2 ()" link="libxml2-xmlregexp.html#xmlRegExecPushString2"/>
    <function name="xmlRegFreeExecCtxt ()" link="libxml2-xmlregexp.html#xmlRegFreeExecCtxt"/>
    <function name="xmlRegFreeRegexp ()" link="libxml2-xmlregexp.html#xmlRegFreeRegexp"/>
    <function name="xmlRegNewExecCtxt ()" link="libxml2-xmlregexp.html#xmlRegNewExecCtxt"/>
    <function name="xmlRegexpCompile ()" link="libxml2-xmlregexp.html#xmlRegexpCompile"/>
    <function name="xmlRegexpExec ()" link="libxml2-xmlregexp.html#xmlRegexpExec"/>
    <function name="xmlRegexpIsDeterminist ()" link="libxml2-xmlregexp.html#xmlRegexpIsDeterminist"/>
    <function name="xmlRegexpPrint ()" link="libxml2-xmlregexp.html#xmlRegexpPrint"/>
    <function name="xmlRegisterCharEncodingHandler ()" link="libxml2-encoding.html#xmlRegisterCharEncodingHandler"/>
    <function name="xmlRegisterDefaultInputCallbacks ()" link="libxml2-xmlIO.html#xmlRegisterDefaultInputCallbacks"/>
    <function name="xmlRegisterDefaultOutputCallbacks ()" link="libxml2-xmlIO.html#xmlRegisterDefaultOutputCallbacks"/>
    <function name="xmlRegisterHTTPPostCallbacks ()" link="libxml2-xmlIO.html#xmlRegisterHTTPPostCallbacks"/>
    <function name="xmlRegisterInputCallbacks ()" link="libxml2-xmlIO.html#xmlRegisterInputCallbacks"/>
    <function name="xmlRegisterNodeDefault ()" link="libxml2-globals.html#xmlRegisterNodeDefault"/>
    <function name="xmlRegisterOutputCallbacks ()" link="libxml2-xmlIO.html#xmlRegisterOutputCallbacks"/>
    <function name="xmlRelaxNGCleanupTypes ()" link="libxml2-relaxng.html#xmlRelaxNGCleanupTypes"/>
    <function name="xmlRelaxNGDump ()" link="libxml2-relaxng.html#xmlRelaxNGDump"/>
    <function name="xmlRelaxNGDumpTree ()" link="libxml2-relaxng.html#xmlRelaxNGDumpTree"/>
    <function name="xmlRelaxNGFree ()" link="libxml2-relaxng.html#xmlRelaxNGFree"/>
    <function name="xmlRelaxNGFreeParserCtxt ()" link="libxml2-relaxng.html#xmlRelaxNGFreeParserCtxt"/>
    <function name="xmlRelaxNGFreeValidCtxt ()" link="libxml2-relaxng.html#xmlRelaxNGFreeValidCtxt"/>
    <function name="xmlRelaxNGGetParserErrors ()" link="libxml2-relaxng.html#xmlRelaxNGGetParserErrors"/>
    <function name="xmlRelaxNGGetValidErrors ()" link="libxml2-relaxng.html#xmlRelaxNGGetValidErrors"/>
    <function name="xmlRelaxNGInitTypes ()" link="libxml2-relaxng.html#xmlRelaxNGInitTypes"/>
    <function name="xmlRelaxNGNewDocParserCtxt ()" link="libxml2-relaxng.html#xmlRelaxNGNewDocParserCtxt"/>
    <function name="xmlRelaxNGNewMemParserCtxt ()" link="libxml2-relaxng.html#xmlRelaxNGNewMemParserCtxt"/>
    <function name="xmlRelaxNGNewParserCtxt ()" link="libxml2-relaxng.html#xmlRelaxNGNewParserCtxt"/>
    <function name="xmlRelaxNGNewValidCtxt ()" link="libxml2-relaxng.html#xmlRelaxNGNewValidCtxt"/>
    <function name="xmlRelaxNGParse ()" link="libxml2-relaxng.html#xmlRelaxNGParse"/>
    <function name="xmlRelaxNGSetParserErrors ()" link="libxml2-relaxng.html#xmlRelaxNGSetParserErrors"/>
    <function name="xmlRelaxNGSetParserStructuredErrors ()" link="libxml2-relaxng.html#xmlRelaxNGSetParserStructuredErrors"/>
    <function name="xmlRelaxNGSetValidErrors ()" link="libxml2-relaxng.html#xmlRelaxNGSetValidErrors"/>
    <function name="xmlRelaxNGSetValidStructuredErrors ()" link="libxml2-relaxng.html#xmlRelaxNGSetValidStructuredErrors"/>
    <function name="xmlRelaxNGValidateDoc ()" link="libxml2-relaxng.html#xmlRelaxNGValidateDoc"/>
    <function name="xmlRelaxNGValidateFullElement ()" link="libxml2-relaxng.html#xmlRelaxNGValidateFullElement"/>
    <function name="xmlRelaxNGValidatePopElement ()" link="libxml2-relaxng.html#xmlRelaxNGValidatePopElement"/>
    <function name="xmlRelaxNGValidatePushCData ()" link="libxml2-relaxng.html#xmlRelaxNGValidatePushCData"/>
    <function name="xmlRelaxNGValidatePushElement ()" link="libxml2-relaxng.html#xmlRelaxNGValidatePushElement"/>
    <function name="xmlRelaxParserSetFlag ()" link="libxml2-relaxng.html#xmlRelaxParserSetFlag"/>
    <function name="xmlRemoveID ()" link="libxml2-valid.html#xmlRemoveID"/>
    <function name="xmlRemoveProp ()" link="libxml2-tree.html#xmlRemoveProp"/>
    <function name="xmlRemoveRef ()" link="libxml2-valid.html#xmlRemoveRef"/>
    <function name="xmlReplaceNode ()" link="libxml2-tree.html#xmlReplaceNode"/>
    <function name="xmlResetError ()" link="libxml2-xmlerror.html#xmlResetError"/>
    <function name="xmlResetLastError ()" link="libxml2-xmlerror.html#xmlResetLastError"/>
    <function name="xmlSAX2AttributeDecl ()" link="libxml2-SAX2.html#xmlSAX2AttributeDecl"/>
    <function name="xmlSAX2CDataBlock ()" link="libxml2-SAX2.html#xmlSAX2CDataBlock"/>
    <function name="xmlSAX2Characters ()" link="libxml2-SAX2.html#xmlSAX2Characters"/>
    <function name="xmlSAX2Comment ()" link="libxml2-SAX2.html#xmlSAX2Comment"/>
    <function name="xmlSAX2ElementDecl ()" link="libxml2-SAX2.html#xmlSAX2ElementDecl"/>
    <function name="xmlSAX2EndDocument ()" link="libxml2-SAX2.html#xmlSAX2EndDocument"/>
    <function name="xmlSAX2EndElement ()" link="libxml2-SAX2.html#xmlSAX2EndElement"/>
    <function name="xmlSAX2EndElementNs ()" link="libxml2-SAX2.html#xmlSAX2EndElementNs"/>
    <function name="xmlSAX2EntityDecl ()" link="libxml2-SAX2.html#xmlSAX2EntityDecl"/>
    <function name="xmlSAX2ExternalSubset ()" link="libxml2-SAX2.html#xmlSAX2ExternalSubset"/>
    <function name="xmlSAX2GetColumnNumber ()" link="libxml2-SAX2.html#xmlSAX2GetColumnNumber"/>
    <function name="xmlSAX2GetEntity ()" link="libxml2-SAX2.html#xmlSAX2GetEntity"/>
    <function name="xmlSAX2GetLineNumber ()" link="libxml2-SAX2.html#xmlSAX2GetLineNumber"/>
    <function name="xmlSAX2GetParameterEntity ()" link="libxml2-SAX2.html#xmlSAX2GetParameterEntity"/>
    <function name="xmlSAX2GetPublicId ()" link="libxml2-SAX2.html#xmlSAX2GetPublicId"/>
    <function name="xmlSAX2GetSystemId ()" link="libxml2-SAX2.html#xmlSAX2GetSystemId"/>
    <function name="xmlSAX2HasExternalSubset ()" link="libxml2-SAX2.html#xmlSAX2HasExternalSubset"/>
    <function name="xmlSAX2HasInternalSubset ()" link="libxml2-SAX2.html#xmlSAX2HasInternalSubset"/>
    <function name="xmlSAX2IgnorableWhitespace ()" link="libxml2-SAX2.html#xmlSAX2IgnorableWhitespace"/>
    <function name="xmlSAX2InitDefaultSAXHandler ()" link="libxml2-SAX2.html#xmlSAX2InitDefaultSAXHandler"/>
    <function name="xmlSAX2InitDocbDefaultSAXHandler ()" link="libxml2-SAX2.html#xmlSAX2InitDocbDefaultSAXHandler"/>
    <function name="xmlSAX2InitHtmlDefaultSAXHandler ()" link="libxml2-SAX2.html#xmlSAX2InitHtmlDefaultSAXHandler"/>
    <function name="xmlSAX2InternalSubset ()" link="libxml2-SAX2.html#xmlSAX2InternalSubset"/>
    <function name="xmlSAX2IsStandalone ()" link="libxml2-SAX2.html#xmlSAX2IsStandalone"/>
    <function name="xmlSAX2NotationDecl ()" link="libxml2-SAX2.html#xmlSAX2NotationDecl"/>
    <function name="xmlSAX2ProcessingInstruction ()" link="libxml2-SAX2.html#xmlSAX2ProcessingInstruction"/>
    <function name="xmlSAX2Reference ()" link="libxml2-SAX2.html#xmlSAX2Reference"/>
    <function name="xmlSAX2ResolveEntity ()" link="libxml2-SAX2.html#xmlSAX2ResolveEntity"/>
    <function name="xmlSAX2SetDocumentLocator ()" link="libxml2-SAX2.html#xmlSAX2SetDocumentLocator"/>
    <function name="xmlSAX2StartDocument ()" link="libxml2-SAX2.html#xmlSAX2StartDocument"/>
    <function name="xmlSAX2StartElement ()" link="libxml2-SAX2.html#xmlSAX2StartElement"/>
    <function name="xmlSAX2StartElementNs ()" link="libxml2-SAX2.html#xmlSAX2StartElementNs"/>
    <function name="xmlSAX2UnparsedEntityDecl ()" link="libxml2-SAX2.html#xmlSAX2UnparsedEntityDecl"/>
    <function name="xmlSAXDefaultVersion ()" link="libxml2-SAX2.html#xmlSAXDefaultVersion"/>
    <function name="xmlSAXParseDTD ()" link="libxml2-parser.html#xmlSAXParseDTD"/>
    <function name="xmlSAXParseDoc ()" link="libxml2-parser.html#xmlSAXParseDoc"/>
    <function name="xmlSAXParseEntity ()" link="libxml2-parser.html#xmlSAXParseEntity"/>
    <function name="xmlSAXParseFile ()" link="libxml2-parser.html#xmlSAXParseFile"/>
    <function name="xmlSAXParseFileWithData ()" link="libxml2-parser.html#xmlSAXParseFileWithData"/>
    <function name="xmlSAXParseMemory ()" link="libxml2-parser.html#xmlSAXParseMemory"/>
    <function name="xmlSAXParseMemoryWithData ()" link="libxml2-parser.html#xmlSAXParseMemoryWithData"/>
    <function name="xmlSAXUserParseFile ()" link="libxml2-parser.html#xmlSAXUserParseFile"/>
    <function name="xmlSAXUserParseMemory ()" link="libxml2-parser.html#xmlSAXUserParseMemory"/>
    <function name="xmlSAXVersion ()" link="libxml2-SAX2.html#xmlSAXVersion"/>
    <function name="xmlSaveClose ()" link="libxml2-xmlsave.html#xmlSaveClose"/>
    <function name="xmlSaveDoc ()" link="libxml2-xmlsave.html#xmlSaveDoc"/>
    <function name="xmlSaveFile ()" link="libxml2-tree.html#xmlSaveFile"/>
    <function name="xmlSaveFileEnc ()" link="libxml2-tree.html#xmlSaveFileEnc"/>
    <function name="xmlSaveFileTo ()" link="libxml2-tree.html#xmlSaveFileTo"/>
    <function name="xmlSaveFlush ()" link="libxml2-xmlsave.html#xmlSaveFlush"/>
    <function name="xmlSaveFormatFile ()" link="libxml2-tree.html#xmlSaveFormatFile"/>
    <function name="xmlSaveFormatFileEnc ()" link="libxml2-tree.html#xmlSaveFormatFileEnc"/>
    <function name="xmlSaveFormatFileTo ()" link="libxml2-tree.html#xmlSaveFormatFileTo"/>
    <function name="xmlSaveSetAttrEscape ()" link="libxml2-xmlsave.html#xmlSaveSetAttrEscape"/>
    <function name="xmlSaveSetEscape ()" link="libxml2-xmlsave.html#xmlSaveSetEscape"/>
    <function name="xmlSaveToBuffer ()" link="libxml2-xmlsave.html#xmlSaveToBuffer"/>
    <function name="xmlSaveToFd ()" link="libxml2-xmlsave.html#xmlSaveToFd"/>
    <function name="xmlSaveToFilename ()" link="libxml2-xmlsave.html#xmlSaveToFilename"/>
    <function name="xmlSaveToIO ()" link="libxml2-xmlsave.html#xmlSaveToIO"/>
    <function name="xmlSaveTree ()" link="libxml2-xmlsave.html#xmlSaveTree"/>
    <function name="xmlSaveUri ()" link="libxml2-uri.html#xmlSaveUri"/>
    <function name="xmlScanName ()" link="libxml2-parserInternals.html#xmlScanName"/>
    <function name="xmlSchemaCheckFacet ()" link="libxml2-xmlschemastypes.html#xmlSchemaCheckFacet"/>
    <function name="xmlSchemaCleanupTypes ()" link="libxml2-xmlschemastypes.html#xmlSchemaCleanupTypes"/>
    <function name="xmlSchemaCollapseString ()" link="libxml2-xmlschemastypes.html#xmlSchemaCollapseString"/>
    <function name="xmlSchemaCompareValues ()" link="libxml2-xmlschemastypes.html#xmlSchemaCompareValues"/>
    <function name="xmlSchemaCompareValuesWhtsp ()" link="libxml2-xmlschemastypes.html#xmlSchemaCompareValuesWhtsp"/>
    <function name="xmlSchemaCopyValue ()" link="libxml2-xmlschemastypes.html#xmlSchemaCopyValue"/>
    <function name="xmlSchemaDump ()" link="libxml2-xmlschemas.html#xmlSchemaDump"/>
    <function name="xmlSchemaFree ()" link="libxml2-xmlschemas.html#xmlSchemaFree"/>
    <function name="xmlSchemaFreeFacet ()" link="libxml2-xmlschemastypes.html#xmlSchemaFreeFacet"/>
    <function name="xmlSchemaFreeParserCtxt ()" link="libxml2-xmlschemas.html#xmlSchemaFreeParserCtxt"/>
    <function name="xmlSchemaFreeType ()" link="libxml2-schemasInternals.html#xmlSchemaFreeType"/>
    <function name="xmlSchemaFreeValidCtxt ()" link="libxml2-xmlschemas.html#xmlSchemaFreeValidCtxt"/>
    <function name="xmlSchemaFreeValue ()" link="libxml2-xmlschemastypes.html#xmlSchemaFreeValue"/>
    <function name="xmlSchemaFreeWildcard ()" link="libxml2-schemasInternals.html#xmlSchemaFreeWildcard"/>
    <function name="xmlSchemaGetBuiltInListSimpleTypeItemType ()" link="libxml2-xmlschemastypes.html#xmlSchemaGetBuiltInListSimpleTypeItemType"/>
    <function name="xmlSchemaGetBuiltInType ()" link="libxml2-xmlschemastypes.html#xmlSchemaGetBuiltInType"/>
    <function name="xmlSchemaGetCanonValue ()" link="libxml2-xmlschemastypes.html#xmlSchemaGetCanonValue"/>
    <function name="xmlSchemaGetCanonValueWhtsp ()" link="libxml2-xmlschemastypes.html#xmlSchemaGetCanonValueWhtsp"/>
    <function name="xmlSchemaGetFacetValueAsULong ()" link="libxml2-xmlschemastypes.html#xmlSchemaGetFacetValueAsULong"/>
    <function name="xmlSchemaGetParserErrors ()" link="libxml2-xmlschemas.html#xmlSchemaGetParserErrors"/>
    <function name="xmlSchemaGetPredefinedType ()" link="libxml2-xmlschemastypes.html#xmlSchemaGetPredefinedType"/>
    <function name="xmlSchemaGetValType ()" link="libxml2-xmlschemastypes.html#xmlSchemaGetValType"/>
    <function name="xmlSchemaGetValidErrors ()" link="libxml2-xmlschemas.html#xmlSchemaGetValidErrors"/>
    <function name="xmlSchemaInitTypes ()" link="libxml2-xmlschemastypes.html#xmlSchemaInitTypes"/>
    <function name="xmlSchemaIsBuiltInTypeFacet ()" link="libxml2-xmlschemastypes.html#xmlSchemaIsBuiltInTypeFacet"/>
    <function name="xmlSchemaIsValid ()" link="libxml2-xmlschemas.html#xmlSchemaIsValid"/>
    <function name="xmlSchemaNewDocParserCtxt ()" link="libxml2-xmlschemas.html#xmlSchemaNewDocParserCtxt"/>
    <function name="xmlSchemaNewFacet ()" link="libxml2-xmlschemastypes.html#xmlSchemaNewFacet"/>
    <function name="xmlSchemaNewMemParserCtxt ()" link="libxml2-xmlschemas.html#xmlSchemaNewMemParserCtxt"/>
    <function name="xmlSchemaNewNOTATIONValue ()" link="libxml2-xmlschemastypes.html#xmlSchemaNewNOTATIONValue"/>
    <function name="xmlSchemaNewParserCtxt ()" link="libxml2-xmlschemas.html#xmlSchemaNewParserCtxt"/>
    <function name="xmlSchemaNewQNameValue ()" link="libxml2-xmlschemastypes.html#xmlSchemaNewQNameValue"/>
    <function name="xmlSchemaNewStringValue ()" link="libxml2-xmlschemastypes.html#xmlSchemaNewStringValue"/>
    <function name="xmlSchemaNewValidCtxt ()" link="libxml2-xmlschemas.html#xmlSchemaNewValidCtxt"/>
    <function name="xmlSchemaParse ()" link="libxml2-xmlschemas.html#xmlSchemaParse"/>
    <function name="xmlSchemaSAXPlug ()" link="libxml2-xmlschemas.html#xmlSchemaSAXPlug"/>
    <function name="xmlSchemaSAXUnplug ()" link="libxml2-xmlschemas.html#xmlSchemaSAXUnplug"/>
    <function name="xmlSchemaSetParserErrors ()" link="libxml2-xmlschemas.html#xmlSchemaSetParserErrors"/>
    <function name="xmlSchemaSetParserStructuredErrors ()" link="libxml2-xmlschemas.html#xmlSchemaSetParserStructuredErrors"/>
    <function name="xmlSchemaSetValidErrors ()" link="libxml2-xmlschemas.html#xmlSchemaSetValidErrors"/>
    <function name="xmlSchemaSetValidOptions ()" link="libxml2-xmlschemas.html#xmlSchemaSetValidOptions"/>
    <function name="xmlSchemaSetValidStructuredErrors ()" link="libxml2-xmlschemas.html#xmlSchemaSetValidStructuredErrors"/>
    <function name="xmlSchemaValPredefTypeNode ()" link="libxml2-xmlschemastypes.html#xmlSchemaValPredefTypeNode"/>
    <function name="xmlSchemaValPredefTypeNodeNoNorm ()" link="libxml2-xmlschemastypes.html#xmlSchemaValPredefTypeNodeNoNorm"/>
    <function name="xmlSchemaValidCtxtGetOptions ()" link="libxml2-xmlschemas.html#xmlSchemaValidCtxtGetOptions"/>
    <function name="xmlSchemaValidCtxtGetParserCtxt ()" link="libxml2-xmlschemas.html#xmlSchemaValidCtxtGetParserCtxt"/>
    <function name="xmlSchemaValidateDoc ()" link="libxml2-xmlschemas.html#xmlSchemaValidateDoc"/>
    <function name="xmlSchemaValidateFacet ()" link="libxml2-xmlschemastypes.html#xmlSchemaValidateFacet"/>
    <function name="xmlSchemaValidateFacetWhtsp ()" link="libxml2-xmlschemastypes.html#xmlSchemaValidateFacetWhtsp"/>
    <function name="xmlSchemaValidateFile ()" link="libxml2-xmlschemas.html#xmlSchemaValidateFile"/>
    <function name="xmlSchemaValidateLengthFacet ()" link="libxml2-xmlschemastypes.html#xmlSchemaValidateLengthFacet"/>
    <function name="xmlSchemaValidateLengthFacetWhtsp ()" link="libxml2-xmlschemastypes.html#xmlSchemaValidateLengthFacetWhtsp"/>
    <function name="xmlSchemaValidateListSimpleTypeFacet ()" link="libxml2-xmlschemastypes.html#xmlSchemaValidateListSimpleTypeFacet"/>
    <function name="xmlSchemaValidateOneElement ()" link="libxml2-xmlschemas.html#xmlSchemaValidateOneElement"/>
    <function name="xmlSchemaValidatePredefinedType ()" link="libxml2-xmlschemastypes.html#xmlSchemaValidatePredefinedType"/>
    <function name="xmlSchemaValidateSetFilename ()" link="libxml2-xmlschemas.html#xmlSchemaValidateSetFilename"/>
    <function name="xmlSchemaValidateSetLocator ()" link="libxml2-xmlschemas.html#xmlSchemaValidateSetLocator"/>
    <function name="xmlSchemaValidateStream ()" link="libxml2-xmlschemas.html#xmlSchemaValidateStream"/>
    <function name="xmlSchemaValueAppend ()" link="libxml2-xmlschemastypes.html#xmlSchemaValueAppend"/>
    <function name="xmlSchemaValueGetAsBoolean ()" link="libxml2-xmlschemastypes.html#xmlSchemaValueGetAsBoolean"/>
    <function name="xmlSchemaValueGetAsString ()" link="libxml2-xmlschemastypes.html#xmlSchemaValueGetAsString"/>
    <function name="xmlSchemaValueGetNext ()" link="libxml2-xmlschemastypes.html#xmlSchemaValueGetNext"/>
    <function name="xmlSchemaWhiteSpaceReplace ()" link="libxml2-xmlschemastypes.html#xmlSchemaWhiteSpaceReplace"/>
    <function name="xmlSchematronFree ()" link="libxml2-schematron.html#xmlSchematronFree"/>
    <function name="xmlSchematronFreeParserCtxt ()" link="libxml2-schematron.html#xmlSchematronFreeParserCtxt"/>
    <function name="xmlSchematronFreeValidCtxt ()" link="libxml2-schematron.html#xmlSchematronFreeValidCtxt"/>
    <function name="xmlSchematronNewDocParserCtxt ()" link="libxml2-schematron.html#xmlSchematronNewDocParserCtxt"/>
    <function name="xmlSchematronNewMemParserCtxt ()" link="libxml2-schematron.html#xmlSchematronNewMemParserCtxt"/>
    <function name="xmlSchematronNewParserCtxt ()" link="libxml2-schematron.html#xmlSchematronNewParserCtxt"/>
    <function name="xmlSchematronNewValidCtxt ()" link="libxml2-schematron.html#xmlSchematronNewValidCtxt"/>
    <function name="xmlSchematronParse ()" link="libxml2-schematron.html#xmlSchematronParse"/>
    <function name="xmlSchematronSetValidStructuredErrors ()" link="libxml2-schematron.html#xmlSchematronSetValidStructuredErrors"/>
    <function name="xmlSchematronValidateDoc ()" link="libxml2-schematron.html#xmlSchematronValidateDoc"/>
    <function name="xmlSearchNs ()" link="libxml2-tree.html#xmlSearchNs"/>
    <function name="xmlSearchNsByHref ()" link="libxml2-tree.html#xmlSearchNsByHref"/>
    <function name="xmlSetBufferAllocationScheme ()" link="libxml2-tree.html#xmlSetBufferAllocationScheme"/>
    <function name="xmlSetCompressMode ()" link="libxml2-tree.html#xmlSetCompressMode"/>
    <function name="xmlSetDocCompressMode ()" link="libxml2-tree.html#xmlSetDocCompressMode"/>
    <function name="xmlSetEntityReferenceFunc ()" link="libxml2-parserInternals.html#xmlSetEntityReferenceFunc"/>
    <function name="xmlSetExternalEntityLoader ()" link="libxml2-parser.html#xmlSetExternalEntityLoader"/>
    <function name="xmlSetFeature ()" link="libxml2-parser.html#xmlSetFeature"/>
    <function name="xmlSetGenericErrorFunc ()" link="libxml2-xmlerror.html#xmlSetGenericErrorFunc"/>
    <function name="xmlSetListDoc ()" link="libxml2-tree.html#xmlSetListDoc"/>
    <function name="xmlSetNs ()" link="libxml2-tree.html#xmlSetNs"/>
    <function name="xmlSetNsProp ()" link="libxml2-tree.html#xmlSetNsProp"/>
    <function name="xmlSetProp ()" link="libxml2-tree.html#xmlSetProp"/>
    <function name="xmlSetStructuredErrorFunc ()" link="libxml2-xmlerror.html#xmlSetStructuredErrorFunc"/>
    <function name="xmlSetTreeDoc ()" link="libxml2-tree.html#xmlSetTreeDoc"/>
    <function name="xmlSetupParserForBuffer ()" link="libxml2-parser.html#xmlSetupParserForBuffer"/>
    <function name="xmlShell ()" link="libxml2-debugXML.html#xmlShell"/>
    <function name="xmlShellBase ()" link="libxml2-debugXML.html#xmlShellBase"/>
    <function name="xmlShellCat ()" link="libxml2-debugXML.html#xmlShellCat"/>
    <function name="xmlShellDir ()" link="libxml2-debugXML.html#xmlShellDir"/>
    <function name="xmlShellDu ()" link="libxml2-debugXML.html#xmlShellDu"/>
    <function name="xmlShellList ()" link="libxml2-debugXML.html#xmlShellList"/>
    <function name="xmlShellLoad ()" link="libxml2-debugXML.html#xmlShellLoad"/>
    <function name="xmlShellPrintNode ()" link="libxml2-debugXML.html#xmlShellPrintNode"/>
    <function name="xmlShellPrintXPathError ()" link="libxml2-debugXML.html#xmlShellPrintXPathError"/>
    <function name="xmlShellPrintXPathResult ()" link="libxml2-debugXML.html#xmlShellPrintXPathResult"/>
    <function name="xmlShellPwd ()" link="libxml2-debugXML.html#xmlShellPwd"/>
    <function name="xmlShellSave ()" link="libxml2-debugXML.html#xmlShellSave"/>
    <function name="xmlShellValidate ()" link="libxml2-debugXML.html#xmlShellValidate"/>
    <function name="xmlShellWrite ()" link="libxml2-debugXML.html#xmlShellWrite"/>
    <function name="xmlSkipBlankChars ()" link="libxml2-parserInternals.html#xmlSkipBlankChars"/>
    <function name="xmlSnprintfElementContent ()" link="libxml2-valid.html#xmlSnprintfElementContent"/>
    <function name="xmlSplitQName ()" link="libxml2-parserInternals.html#xmlSplitQName"/>
    <function name="xmlSplitQName2 ()" link="libxml2-tree.html#xmlSplitQName2"/>
    <function name="xmlSplitQName3 ()" link="libxml2-tree.html#xmlSplitQName3"/>
    <function name="xmlSprintfElementContent ()" link="libxml2-valid.html#xmlSprintfElementContent"/>
    <function name="xmlStopParser ()" link="libxml2-parser.html#xmlStopParser"/>
    <function name="xmlStrEqual ()" link="libxml2-xmlstring.html#xmlStrEqual"/>
    <function name="xmlStrPrintf ()" link="libxml2-xmlstring.html#xmlStrPrintf"/>
    <function name="xmlStrQEqual ()" link="libxml2-xmlstring.html#xmlStrQEqual"/>
    <function name="xmlStrVPrintf ()" link="libxml2-xmlstring.html#xmlStrVPrintf"/>
    <function name="xmlStrcasecmp ()" link="libxml2-xmlstring.html#xmlStrcasecmp"/>
    <function name="xmlStrcasestr ()" link="libxml2-xmlstring.html#xmlStrcasestr"/>
    <function name="xmlStrcat ()" link="libxml2-xmlstring.html#xmlStrcat"/>
    <function name="xmlStrchr ()" link="libxml2-xmlstring.html#xmlStrchr"/>
    <function name="xmlStrcmp ()" link="libxml2-xmlstring.html#xmlStrcmp"/>
    <function name="xmlStrdup ()" link="libxml2-xmlstring.html#xmlStrdup"/>
    <function name="xmlStreamPop ()" link="libxml2-pattern.html#xmlStreamPop"/>
    <function name="xmlStreamPush ()" link="libxml2-pattern.html#xmlStreamPush"/>
    <function name="xmlStreamPushAttr ()" link="libxml2-pattern.html#xmlStreamPushAttr"/>
    <function name="xmlStreamPushNode ()" link="libxml2-pattern.html#xmlStreamPushNode"/>
    <function name="xmlStreamWantsAnyNode ()" link="libxml2-pattern.html#xmlStreamWantsAnyNode"/>
    <function name="xmlStringCurrentChar ()" link="libxml2-parserInternals.html#xmlStringCurrentChar"/>
    <function name="xmlStringDecodeEntities ()" link="libxml2-parserInternals.html#xmlStringDecodeEntities"/>
    <function name="xmlStringGetNodeList ()" link="libxml2-tree.html#xmlStringGetNodeList"/>
    <function name="xmlStringLenDecodeEntities ()" link="libxml2-parserInternals.html#xmlStringLenDecodeEntities"/>
    <function name="xmlStringLenGetNodeList ()" link="libxml2-tree.html#xmlStringLenGetNodeList"/>
    <function name="xmlStrlen ()" link="libxml2-xmlstring.html#xmlStrlen"/>
    <function name="xmlStrncasecmp ()" link="libxml2-xmlstring.html#xmlStrncasecmp"/>
    <function name="xmlStrncat ()" link="libxml2-xmlstring.html#xmlStrncat"/>
    <function name="xmlStrncatNew ()" link="libxml2-xmlstring.html#xmlStrncatNew"/>
    <function name="xmlStrncmp ()" link="libxml2-xmlstring.html#xmlStrncmp"/>
    <function name="xmlStrndup ()" link="libxml2-xmlstring.html#xmlStrndup"/>
    <function name="xmlStrstr ()" link="libxml2-xmlstring.html#xmlStrstr"/>
    <function name="xmlStrsub ()" link="libxml2-xmlstring.html#xmlStrsub"/>
    <function name="xmlSubstituteEntitiesDefault ()" link="libxml2-parser.html#xmlSubstituteEntitiesDefault"/>
    <function name="xmlSwitchEncoding ()" link="libxml2-parserInternals.html#xmlSwitchEncoding"/>
    <function name="xmlSwitchInputEncoding ()" link="libxml2-parserInternals.html#xmlSwitchInputEncoding"/>
    <function name="xmlSwitchToEncoding ()" link="libxml2-parserInternals.html#xmlSwitchToEncoding"/>
    <function name="xmlTextConcat ()" link="libxml2-tree.html#xmlTextConcat"/>
    <function name="xmlTextMerge ()" link="libxml2-tree.html#xmlTextMerge"/>
    <function name="xmlTextReaderAttributeCount ()" link="libxml2-xmlreader.html#xmlTextReaderAttributeCount"/>
    <function name="xmlTextReaderBaseUri ()" link="libxml2-xmlreader.html#xmlTextReaderBaseUri"/>
    <function name="xmlTextReaderByteConsumed ()" link="libxml2-xmlreader.html#xmlTextReaderByteConsumed"/>
    <function name="xmlTextReaderClose ()" link="libxml2-xmlreader.html#xmlTextReaderClose"/>
    <function name="xmlTextReaderConstBaseUri ()" link="libxml2-xmlreader.html#xmlTextReaderConstBaseUri"/>
    <function name="xmlTextReaderConstEncoding ()" link="libxml2-xmlreader.html#xmlTextReaderConstEncoding"/>
    <function name="xmlTextReaderConstLocalName ()" link="libxml2-xmlreader.html#xmlTextReaderConstLocalName"/>
    <function name="xmlTextReaderConstName ()" link="libxml2-xmlreader.html#xmlTextReaderConstName"/>
    <function name="xmlTextReaderConstNamespaceUri ()" link="libxml2-xmlreader.html#xmlTextReaderConstNamespaceUri"/>
    <function name="xmlTextReaderConstPrefix ()" link="libxml2-xmlreader.html#xmlTextReaderConstPrefix"/>
    <function name="xmlTextReaderConstString ()" link="libxml2-xmlreader.html#xmlTextReaderConstString"/>
    <function name="xmlTextReaderConstValue ()" link="libxml2-xmlreader.html#xmlTextReaderConstValue"/>
    <function name="xmlTextReaderConstXmlLang ()" link="libxml2-xmlreader.html#xmlTextReaderConstXmlLang"/>
    <function name="xmlTextReaderConstXmlVersion ()" link="libxml2-xmlreader.html#xmlTextReaderConstXmlVersion"/>
    <function name="xmlTextReaderCurrentDoc ()" link="libxml2-xmlreader.html#xmlTextReaderCurrentDoc"/>
    <function name="xmlTextReaderCurrentNode ()" link="libxml2-xmlreader.html#xmlTextReaderCurrentNode"/>
    <function name="xmlTextReaderDepth ()" link="libxml2-xmlreader.html#xmlTextReaderDepth"/>
    <function name="xmlTextReaderExpand ()" link="libxml2-xmlreader.html#xmlTextReaderExpand"/>
    <function name="xmlTextReaderGetAttribute ()" link="libxml2-xmlreader.html#xmlTextReaderGetAttribute"/>
    <function name="xmlTextReaderGetAttributeNo ()" link="libxml2-xmlreader.html#xmlTextReaderGetAttributeNo"/>
    <function name="xmlTextReaderGetAttributeNs ()" link="libxml2-xmlreader.html#xmlTextReaderGetAttributeNs"/>
    <function name="xmlTextReaderGetErrorHandler ()" link="libxml2-xmlreader.html#xmlTextReaderGetErrorHandler"/>
    <function name="xmlTextReaderGetParserColumnNumber ()" link="libxml2-xmlreader.html#xmlTextReaderGetParserColumnNumber"/>
    <function name="xmlTextReaderGetParserLineNumber ()" link="libxml2-xmlreader.html#xmlTextReaderGetParserLineNumber"/>
    <function name="xmlTextReaderGetParserProp ()" link="libxml2-xmlreader.html#xmlTextReaderGetParserProp"/>
    <function name="xmlTextReaderGetRemainder ()" link="libxml2-xmlreader.html#xmlTextReaderGetRemainder"/>
    <function name="xmlTextReaderHasAttributes ()" link="libxml2-xmlreader.html#xmlTextReaderHasAttributes"/>
    <function name="xmlTextReaderHasValue ()" link="libxml2-xmlreader.html#xmlTextReaderHasValue"/>
    <function name="xmlTextReaderIsDefault ()" link="libxml2-xmlreader.html#xmlTextReaderIsDefault"/>
    <function name="xmlTextReaderIsEmptyElement ()" link="libxml2-xmlreader.html#xmlTextReaderIsEmptyElement"/>
    <function name="xmlTextReaderIsNamespaceDecl ()" link="libxml2-xmlreader.html#xmlTextReaderIsNamespaceDecl"/>
    <function name="xmlTextReaderIsValid ()" link="libxml2-xmlreader.html#xmlTextReaderIsValid"/>
    <function name="xmlTextReaderLocalName ()" link="libxml2-xmlreader.html#xmlTextReaderLocalName"/>
    <function name="xmlTextReaderLocatorBaseURI ()" link="libxml2-xmlreader.html#xmlTextReaderLocatorBaseURI"/>
    <function name="xmlTextReaderLocatorLineNumber ()" link="libxml2-xmlreader.html#xmlTextReaderLocatorLineNumber"/>
    <function name="xmlTextReaderLookupNamespace ()" link="libxml2-xmlreader.html#xmlTextReaderLookupNamespace"/>
    <function name="xmlTextReaderMoveToAttribute ()" link="libxml2-xmlreader.html#xmlTextReaderMoveToAttribute"/>
    <function name="xmlTextReaderMoveToAttributeNo ()" link="libxml2-xmlreader.html#xmlTextReaderMoveToAttributeNo"/>
    <function name="xmlTextReaderMoveToAttributeNs ()" link="libxml2-xmlreader.html#xmlTextReaderMoveToAttributeNs"/>
    <function name="xmlTextReaderMoveToElement ()" link="libxml2-xmlreader.html#xmlTextReaderMoveToElement"/>
    <function name="xmlTextReaderMoveToFirstAttribute ()" link="libxml2-xmlreader.html#xmlTextReaderMoveToFirstAttribute"/>
    <function name="xmlTextReaderMoveToNextAttribute ()" link="libxml2-xmlreader.html#xmlTextReaderMoveToNextAttribute"/>
    <function name="xmlTextReaderName ()" link="libxml2-xmlreader.html#xmlTextReaderName"/>
    <function name="xmlTextReaderNamespaceUri ()" link="libxml2-xmlreader.html#xmlTextReaderNamespaceUri"/>
    <function name="xmlTextReaderNext ()" link="libxml2-xmlreader.html#xmlTextReaderNext"/>
    <function name="xmlTextReaderNextSibling ()" link="libxml2-xmlreader.html#xmlTextReaderNextSibling"/>
    <function name="xmlTextReaderNodeType ()" link="libxml2-xmlreader.html#xmlTextReaderNodeType"/>
    <function name="xmlTextReaderNormalization ()" link="libxml2-xmlreader.html#xmlTextReaderNormalization"/>
    <function name="xmlTextReaderPrefix ()" link="libxml2-xmlreader.html#xmlTextReaderPrefix"/>
    <function name="xmlTextReaderPreserve ()" link="libxml2-xmlreader.html#xmlTextReaderPreserve"/>
    <function name="xmlTextReaderPreservePattern ()" link="libxml2-xmlreader.html#xmlTextReaderPreservePattern"/>
    <function name="xmlTextReaderQuoteChar ()" link="libxml2-xmlreader.html#xmlTextReaderQuoteChar"/>
    <function name="xmlTextReaderRead ()" link="libxml2-xmlreader.html#xmlTextReaderRead"/>
    <function name="xmlTextReaderReadAttributeValue ()" link="libxml2-xmlreader.html#xmlTextReaderReadAttributeValue"/>
    <function name="xmlTextReaderReadInnerXml ()" link="libxml2-xmlreader.html#xmlTextReaderReadInnerXml"/>
    <function name="xmlTextReaderReadOuterXml ()" link="libxml2-xmlreader.html#xmlTextReaderReadOuterXml"/>
    <function name="xmlTextReaderReadState ()" link="libxml2-xmlreader.html#xmlTextReaderReadState"/>
    <function name="xmlTextReaderReadString ()" link="libxml2-xmlreader.html#xmlTextReaderReadString"/>
    <function name="xmlTextReaderRelaxNGSetSchema ()" link="libxml2-xmlreader.html#xmlTextReaderRelaxNGSetSchema"/>
    <function name="xmlTextReaderRelaxNGValidate ()" link="libxml2-xmlreader.html#xmlTextReaderRelaxNGValidate"/>
    <function name="xmlTextReaderRelaxNGValidateCtxt ()" link="libxml2-xmlreader.html#xmlTextReaderRelaxNGValidateCtxt"/>
    <function name="xmlTextReaderSchemaValidate ()" link="libxml2-xmlreader.html#xmlTextReaderSchemaValidate"/>
    <function name="xmlTextReaderSchemaValidateCtxt ()" link="libxml2-xmlreader.html#xmlTextReaderSchemaValidateCtxt"/>
    <function name="xmlTextReaderSetErrorHandler ()" link="libxml2-xmlreader.html#xmlTextReaderSetErrorHandler"/>
    <function name="xmlTextReaderSetParserProp ()" link="libxml2-xmlreader.html#xmlTextReaderSetParserProp"/>
    <function name="xmlTextReaderSetSchema ()" link="libxml2-xmlreader.html#xmlTextReaderSetSchema"/>
    <function name="xmlTextReaderSetStructuredErrorHandler ()" link="libxml2-xmlreader.html#xmlTextReaderSetStructuredErrorHandler"/>
    <function name="xmlTextReaderSetup ()" link="libxml2-xmlreader.html#xmlTextReaderSetup"/>
    <function name="xmlTextReaderStandalone ()" link="libxml2-xmlreader.html#xmlTextReaderStandalone"/>
    <function name="xmlTextReaderValue ()" link="libxml2-xmlreader.html#xmlTextReaderValue"/>
    <function name="xmlTextReaderXmlLang ()" link="libxml2-xmlreader.html#xmlTextReaderXmlLang"/>
    <function name="xmlTextWriterEndAttribute ()" link="libxml2-xmlwriter.html#xmlTextWriterEndAttribute"/>
    <function name="xmlTextWriterEndCDATA ()" link="libxml2-xmlwriter.html#xmlTextWriterEndCDATA"/>
    <function name="xmlTextWriterEndComment ()" link="libxml2-xmlwriter.html#xmlTextWriterEndComment"/>
    <function name="xmlTextWriterEndDTD ()" link="libxml2-xmlwriter.html#xmlTextWriterEndDTD"/>
    <function name="xmlTextWriterEndDTDAttlist ()" link="libxml2-xmlwriter.html#xmlTextWriterEndDTDAttlist"/>
    <function name="xmlTextWriterEndDTDElement ()" link="libxml2-xmlwriter.html#xmlTextWriterEndDTDElement"/>
    <function name="xmlTextWriterEndDTDEntity ()" link="libxml2-xmlwriter.html#xmlTextWriterEndDTDEntity"/>
    <function name="xmlTextWriterEndDocument ()" link="libxml2-xmlwriter.html#xmlTextWriterEndDocument"/>
    <function name="xmlTextWriterEndElement ()" link="libxml2-xmlwriter.html#xmlTextWriterEndElement"/>
    <function name="xmlTextWriterEndPI ()" link="libxml2-xmlwriter.html#xmlTextWriterEndPI"/>
    <function name="xmlTextWriterFlush ()" link="libxml2-xmlwriter.html#xmlTextWriterFlush"/>
    <function name="xmlTextWriterFullEndElement ()" link="libxml2-xmlwriter.html#xmlTextWriterFullEndElement"/>
    <function name="xmlTextWriterSetIndent ()" link="libxml2-xmlwriter.html#xmlTextWriterSetIndent"/>
    <function name="xmlTextWriterSetIndentString ()" link="libxml2-xmlwriter.html#xmlTextWriterSetIndentString"/>
    <function name="xmlTextWriterSetQuoteChar ()" link="libxml2-xmlwriter.html#xmlTextWriterSetQuoteChar"/>
    <function name="xmlTextWriterStartAttribute ()" link="libxml2-xmlwriter.html#xmlTextWriterStartAttribute"/>
    <function name="xmlTextWriterStartAttributeNS ()" link="libxml2-xmlwriter.html#xmlTextWriterStartAttributeNS"/>
    <function name="xmlTextWriterStartCDATA ()" link="libxml2-xmlwriter.html#xmlTextWriterStartCDATA"/>
    <function name="xmlTextWriterStartComment ()" link="libxml2-xmlwriter.html#xmlTextWriterStartComment"/>
    <function name="xmlTextWriterStartDTD ()" link="libxml2-xmlwriter.html#xmlTextWriterStartDTD"/>
    <function name="xmlTextWriterStartDTDAttlist ()" link="libxml2-xmlwriter.html#xmlTextWriterStartDTDAttlist"/>
    <function name="xmlTextWriterStartDTDElement ()" link="libxml2-xmlwriter.html#xmlTextWriterStartDTDElement"/>
    <function name="xmlTextWriterStartDTDEntity ()" link="libxml2-xmlwriter.html#xmlTextWriterStartDTDEntity"/>
    <function name="xmlTextWriterStartDocument ()" link="libxml2-xmlwriter.html#xmlTextWriterStartDocument"/>
    <function name="xmlTextWriterStartElement ()" link="libxml2-xmlwriter.html#xmlTextWriterStartElement"/>
    <function name="xmlTextWriterStartElementNS ()" link="libxml2-xmlwriter.html#xmlTextWriterStartElementNS"/>
    <function name="xmlTextWriterStartPI ()" link="libxml2-xmlwriter.html#xmlTextWriterStartPI"/>
    <function name="xmlTextWriterWriteAttribute ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteAttribute"/>
    <function name="xmlTextWriterWriteAttributeNS ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteAttributeNS"/>
    <function name="xmlTextWriterWriteBase64 ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteBase64"/>
    <function name="xmlTextWriterWriteBinHex ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteBinHex"/>
    <function name="xmlTextWriterWriteCDATA ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteCDATA"/>
    <function name="xmlTextWriterWriteComment ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteComment"/>
    <function name="xmlTextWriterWriteDTD ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteDTD"/>
    <function name="xmlTextWriterWriteDTDAttlist ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteDTDAttlist"/>
    <function name="xmlTextWriterWriteDTDElement ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteDTDElement"/>
    <function name="xmlTextWriterWriteDTDEntity ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteDTDEntity"/>
    <function name="xmlTextWriterWriteDTDExternalEntity ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteDTDExternalEntity"/>
    <function name="xmlTextWriterWriteDTDExternalEntityContents ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteDTDExternalEntityContents"/>
    <function name="xmlTextWriterWriteDTDInternalEntity ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteDTDInternalEntity"/>
    <function name="xmlTextWriterWriteDTDNotation ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteDTDNotation"/>
    <function name="xmlTextWriterWriteElement ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteElement"/>
    <function name="xmlTextWriterWriteElementNS ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteElementNS"/>
    <function name="xmlTextWriterWriteFormatAttribute ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatAttribute"/>
    <function name="xmlTextWriterWriteFormatAttributeNS ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatAttributeNS"/>
    <function name="xmlTextWriterWriteFormatCDATA ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatCDATA"/>
    <function name="xmlTextWriterWriteFormatComment ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatComment"/>
    <function name="xmlTextWriterWriteFormatDTD ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatDTD"/>
    <function name="xmlTextWriterWriteFormatDTDAttlist ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatDTDAttlist"/>
    <function name="xmlTextWriterWriteFormatDTDElement ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatDTDElement"/>
    <function name="xmlTextWriterWriteFormatDTDInternalEntity ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatDTDInternalEntity"/>
    <function name="xmlTextWriterWriteFormatElement ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatElement"/>
    <function name="xmlTextWriterWriteFormatElementNS ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatElementNS"/>
    <function name="xmlTextWriterWriteFormatPI ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatPI"/>
    <function name="xmlTextWriterWriteFormatRaw ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatRaw"/>
    <function name="xmlTextWriterWriteFormatString ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteFormatString"/>
    <function name="xmlTextWriterWritePI ()" link="libxml2-xmlwriter.html#xmlTextWriterWritePI"/>
    <function name="xmlTextWriterWriteRaw ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteRaw"/>
    <function name="xmlTextWriterWriteRawLen ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteRawLen"/>
    <function name="xmlTextWriterWriteString ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteString"/>
    <function name="xmlTextWriterWriteVFormatAttribute ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatAttribute"/>
    <function name="xmlTextWriterWriteVFormatAttributeNS ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatAttributeNS"/>
    <function name="xmlTextWriterWriteVFormatCDATA ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatCDATA"/>
    <function name="xmlTextWriterWriteVFormatComment ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatComment"/>
    <function name="xmlTextWriterWriteVFormatDTD ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatDTD"/>
    <function name="xmlTextWriterWriteVFormatDTDAttlist ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatDTDAttlist"/>
    <function name="xmlTextWriterWriteVFormatDTDElement ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatDTDElement"/>
    <function name="xmlTextWriterWriteVFormatDTDInternalEntity ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatDTDInternalEntity"/>
    <function name="xmlTextWriterWriteVFormatElement ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatElement"/>
    <function name="xmlTextWriterWriteVFormatElementNS ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatElementNS"/>
    <function name="xmlTextWriterWriteVFormatPI ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatPI"/>
    <function name="xmlTextWriterWriteVFormatRaw ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatRaw"/>
    <function name="xmlTextWriterWriteVFormatString ()" link="libxml2-xmlwriter.html#xmlTextWriterWriteVFormatString"/>
    <function name="xmlThrDefBufferAllocScheme ()" link="libxml2-globals.html#xmlThrDefBufferAllocScheme"/>
    <function name="xmlThrDefDefaultBufferSize ()" link="libxml2-globals.html#xmlThrDefDefaultBufferSize"/>
    <function name="xmlThrDefDeregisterNodeDefault ()" link="libxml2-globals.html#xmlThrDefDeregisterNodeDefault"/>
    <function name="xmlThrDefDoValidityCheckingDefaultValue ()" link="libxml2-globals.html#xmlThrDefDoValidityCheckingDefaultValue"/>
    <function name="xmlThrDefGetWarningsDefaultValue ()" link="libxml2-globals.html#xmlThrDefGetWarningsDefaultValue"/>
    <function name="xmlThrDefIndentTreeOutput ()" link="libxml2-globals.html#xmlThrDefIndentTreeOutput"/>
    <function name="xmlThrDefKeepBlanksDefaultValue ()" link="libxml2-globals.html#xmlThrDefKeepBlanksDefaultValue"/>
    <function name="xmlThrDefLineNumbersDefaultValue ()" link="libxml2-globals.html#xmlThrDefLineNumbersDefaultValue"/>
    <function name="xmlThrDefLoadExtDtdDefaultValue ()" link="libxml2-globals.html#xmlThrDefLoadExtDtdDefaultValue"/>
    <function name="xmlThrDefOutputBufferCreateFilenameDefault ()" link="libxml2-globals.html#xmlThrDefOutputBufferCreateFilenameDefault"/>
    <function name="xmlThrDefParserDebugEntities ()" link="libxml2-globals.html#xmlThrDefParserDebugEntities"/>
    <function name="xmlThrDefParserInputBufferCreateFilenameDefault ()" link="libxml2-globals.html#xmlThrDefParserInputBufferCreateFilenameDefault"/>
    <function name="xmlThrDefPedanticParserDefaultValue ()" link="libxml2-globals.html#xmlThrDefPedanticParserDefaultValue"/>
    <function name="xmlThrDefRegisterNodeDefault ()" link="libxml2-globals.html#xmlThrDefRegisterNodeDefault"/>
    <function name="xmlThrDefSaveNoEmptyTags ()" link="libxml2-globals.html#xmlThrDefSaveNoEmptyTags"/>
    <function name="xmlThrDefSetGenericErrorFunc ()" link="libxml2-globals.html#xmlThrDefSetGenericErrorFunc"/>
    <function name="xmlThrDefSetStructuredErrorFunc ()" link="libxml2-globals.html#xmlThrDefSetStructuredErrorFunc"/>
    <function name="xmlThrDefSubstituteEntitiesDefaultValue ()" link="libxml2-globals.html#xmlThrDefSubstituteEntitiesDefaultValue"/>
    <function name="xmlThrDefTreeIndentString ()" link="libxml2-globals.html#xmlThrDefTreeIndentString"/>
    <function name="xmlUCSIsAegeanNumbers ()" link="libxml2-xmlunicode.html#xmlUCSIsAegeanNumbers"/>
    <function name="xmlUCSIsAlphabeticPresentationForms ()" link="libxml2-xmlunicode.html#xmlUCSIsAlphabeticPresentationForms"/>
    <function name="xmlUCSIsArabic ()" link="libxml2-xmlunicode.html#xmlUCSIsArabic"/>
    <function name="xmlUCSIsArabicPresentationFormsA ()" link="libxml2-xmlunicode.html#xmlUCSIsArabicPresentationFormsA"/>
    <function name="xmlUCSIsArabicPresentationFormsB ()" link="libxml2-xmlunicode.html#xmlUCSIsArabicPresentationFormsB"/>
    <function name="xmlUCSIsArmenian ()" link="libxml2-xmlunicode.html#xmlUCSIsArmenian"/>
    <function name="xmlUCSIsArrows ()" link="libxml2-xmlunicode.html#xmlUCSIsArrows"/>
    <function name="xmlUCSIsBasicLatin ()" link="libxml2-xmlunicode.html#xmlUCSIsBasicLatin"/>
    <function name="xmlUCSIsBengali ()" link="libxml2-xmlunicode.html#xmlUCSIsBengali"/>
    <function name="xmlUCSIsBlock ()" link="libxml2-xmlunicode.html#xmlUCSIsBlock"/>
    <function name="xmlUCSIsBlockElements ()" link="libxml2-xmlunicode.html#xmlUCSIsBlockElements"/>
    <function name="xmlUCSIsBopomofo ()" link="libxml2-xmlunicode.html#xmlUCSIsBopomofo"/>
    <function name="xmlUCSIsBopomofoExtended ()" link="libxml2-xmlunicode.html#xmlUCSIsBopomofoExtended"/>
    <function name="xmlUCSIsBoxDrawing ()" link="libxml2-xmlunicode.html#xmlUCSIsBoxDrawing"/>
    <function name="xmlUCSIsBraillePatterns ()" link="libxml2-xmlunicode.html#xmlUCSIsBraillePatterns"/>
    <function name="xmlUCSIsBuhid ()" link="libxml2-xmlunicode.html#xmlUCSIsBuhid"/>
    <function name="xmlUCSIsByzantineMusicalSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsByzantineMusicalSymbols"/>
    <function name="xmlUCSIsCJKCompatibility ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKCompatibility"/>
    <function name="xmlUCSIsCJKCompatibilityForms ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKCompatibilityForms"/>
    <function name="xmlUCSIsCJKCompatibilityIdeographs ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKCompatibilityIdeographs"/>
    <function name="xmlUCSIsCJKCompatibilityIdeographsSupplement ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKCompatibilityIdeographsSupplement"/>
    <function name="xmlUCSIsCJKRadicalsSupplement ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKRadicalsSupplement"/>
    <function name="xmlUCSIsCJKSymbolsandPunctuation ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKSymbolsandPunctuation"/>
    <function name="xmlUCSIsCJKUnifiedIdeographs ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKUnifiedIdeographs"/>
    <function name="xmlUCSIsCJKUnifiedIdeographsExtensionA ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKUnifiedIdeographsExtensionA"/>
    <function name="xmlUCSIsCJKUnifiedIdeographsExtensionB ()" link="libxml2-xmlunicode.html#xmlUCSIsCJKUnifiedIdeographsExtensionB"/>
    <function name="xmlUCSIsCat ()" link="libxml2-xmlunicode.html#xmlUCSIsCat"/>
    <function name="xmlUCSIsCatC ()" link="libxml2-xmlunicode.html#xmlUCSIsCatC"/>
    <function name="xmlUCSIsCatCc ()" link="libxml2-xmlunicode.html#xmlUCSIsCatCc"/>
    <function name="xmlUCSIsCatCf ()" link="libxml2-xmlunicode.html#xmlUCSIsCatCf"/>
    <function name="xmlUCSIsCatCo ()" link="libxml2-xmlunicode.html#xmlUCSIsCatCo"/>
    <function name="xmlUCSIsCatCs ()" link="libxml2-xmlunicode.html#xmlUCSIsCatCs"/>
    <function name="xmlUCSIsCatL ()" link="libxml2-xmlunicode.html#xmlUCSIsCatL"/>
    <function name="xmlUCSIsCatLl ()" link="libxml2-xmlunicode.html#xmlUCSIsCatLl"/>
    <function name="xmlUCSIsCatLm ()" link="libxml2-xmlunicode.html#xmlUCSIsCatLm"/>
    <function name="xmlUCSIsCatLo ()" link="libxml2-xmlunicode.html#xmlUCSIsCatLo"/>
    <function name="xmlUCSIsCatLt ()" link="libxml2-xmlunicode.html#xmlUCSIsCatLt"/>
    <function name="xmlUCSIsCatLu ()" link="libxml2-xmlunicode.html#xmlUCSIsCatLu"/>
    <function name="xmlUCSIsCatM ()" link="libxml2-xmlunicode.html#xmlUCSIsCatM"/>
    <function name="xmlUCSIsCatMc ()" link="libxml2-xmlunicode.html#xmlUCSIsCatMc"/>
    <function name="xmlUCSIsCatMe ()" link="libxml2-xmlunicode.html#xmlUCSIsCatMe"/>
    <function name="xmlUCSIsCatMn ()" link="libxml2-xmlunicode.html#xmlUCSIsCatMn"/>
    <function name="xmlUCSIsCatN ()" link="libxml2-xmlunicode.html#xmlUCSIsCatN"/>
    <function name="xmlUCSIsCatNd ()" link="libxml2-xmlunicode.html#xmlUCSIsCatNd"/>
    <function name="xmlUCSIsCatNl ()" link="libxml2-xmlunicode.html#xmlUCSIsCatNl"/>
    <function name="xmlUCSIsCatNo ()" link="libxml2-xmlunicode.html#xmlUCSIsCatNo"/>
    <function name="xmlUCSIsCatP ()" link="libxml2-xmlunicode.html#xmlUCSIsCatP"/>
    <function name="xmlUCSIsCatPc ()" link="libxml2-xmlunicode.html#xmlUCSIsCatPc"/>
    <function name="xmlUCSIsCatPd ()" link="libxml2-xmlunicode.html#xmlUCSIsCatPd"/>
    <function name="xmlUCSIsCatPe ()" link="libxml2-xmlunicode.html#xmlUCSIsCatPe"/>
    <function name="xmlUCSIsCatPf ()" link="libxml2-xmlunicode.html#xmlUCSIsCatPf"/>
    <function name="xmlUCSIsCatPi ()" link="libxml2-xmlunicode.html#xmlUCSIsCatPi"/>
    <function name="xmlUCSIsCatPo ()" link="libxml2-xmlunicode.html#xmlUCSIsCatPo"/>
    <function name="xmlUCSIsCatPs ()" link="libxml2-xmlunicode.html#xmlUCSIsCatPs"/>
    <function name="xmlUCSIsCatS ()" link="libxml2-xmlunicode.html#xmlUCSIsCatS"/>
    <function name="xmlUCSIsCatSc ()" link="libxml2-xmlunicode.html#xmlUCSIsCatSc"/>
    <function name="xmlUCSIsCatSk ()" link="libxml2-xmlunicode.html#xmlUCSIsCatSk"/>
    <function name="xmlUCSIsCatSm ()" link="libxml2-xmlunicode.html#xmlUCSIsCatSm"/>
    <function name="xmlUCSIsCatSo ()" link="libxml2-xmlunicode.html#xmlUCSIsCatSo"/>
    <function name="xmlUCSIsCatZ ()" link="libxml2-xmlunicode.html#xmlUCSIsCatZ"/>
    <function name="xmlUCSIsCatZl ()" link="libxml2-xmlunicode.html#xmlUCSIsCatZl"/>
    <function name="xmlUCSIsCatZp ()" link="libxml2-xmlunicode.html#xmlUCSIsCatZp"/>
    <function name="xmlUCSIsCatZs ()" link="libxml2-xmlunicode.html#xmlUCSIsCatZs"/>
    <function name="xmlUCSIsCherokee ()" link="libxml2-xmlunicode.html#xmlUCSIsCherokee"/>
    <function name="xmlUCSIsCombiningDiacriticalMarks ()" link="libxml2-xmlunicode.html#xmlUCSIsCombiningDiacriticalMarks"/>
    <function name="xmlUCSIsCombiningDiacriticalMarksforSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsCombiningDiacriticalMarksforSymbols"/>
    <function name="xmlUCSIsCombiningHalfMarks ()" link="libxml2-xmlunicode.html#xmlUCSIsCombiningHalfMarks"/>
    <function name="xmlUCSIsCombiningMarksforSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsCombiningMarksforSymbols"/>
    <function name="xmlUCSIsControlPictures ()" link="libxml2-xmlunicode.html#xmlUCSIsControlPictures"/>
    <function name="xmlUCSIsCurrencySymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsCurrencySymbols"/>
    <function name="xmlUCSIsCypriotSyllabary ()" link="libxml2-xmlunicode.html#xmlUCSIsCypriotSyllabary"/>
    <function name="xmlUCSIsCyrillic ()" link="libxml2-xmlunicode.html#xmlUCSIsCyrillic"/>
    <function name="xmlUCSIsCyrillicSupplement ()" link="libxml2-xmlunicode.html#xmlUCSIsCyrillicSupplement"/>
    <function name="xmlUCSIsDeseret ()" link="libxml2-xmlunicode.html#xmlUCSIsDeseret"/>
    <function name="xmlUCSIsDevanagari ()" link="libxml2-xmlunicode.html#xmlUCSIsDevanagari"/>
    <function name="xmlUCSIsDingbats ()" link="libxml2-xmlunicode.html#xmlUCSIsDingbats"/>
    <function name="xmlUCSIsEnclosedAlphanumerics ()" link="libxml2-xmlunicode.html#xmlUCSIsEnclosedAlphanumerics"/>
    <function name="xmlUCSIsEnclosedCJKLettersandMonths ()" link="libxml2-xmlunicode.html#xmlUCSIsEnclosedCJKLettersandMonths"/>
    <function name="xmlUCSIsEthiopic ()" link="libxml2-xmlunicode.html#xmlUCSIsEthiopic"/>
    <function name="xmlUCSIsGeneralPunctuation ()" link="libxml2-xmlunicode.html#xmlUCSIsGeneralPunctuation"/>
    <function name="xmlUCSIsGeometricShapes ()" link="libxml2-xmlunicode.html#xmlUCSIsGeometricShapes"/>
    <function name="xmlUCSIsGeorgian ()" link="libxml2-xmlunicode.html#xmlUCSIsGeorgian"/>
    <function name="xmlUCSIsGothic ()" link="libxml2-xmlunicode.html#xmlUCSIsGothic"/>
    <function name="xmlUCSIsGreek ()" link="libxml2-xmlunicode.html#xmlUCSIsGreek"/>
    <function name="xmlUCSIsGreekExtended ()" link="libxml2-xmlunicode.html#xmlUCSIsGreekExtended"/>
    <function name="xmlUCSIsGreekandCoptic ()" link="libxml2-xmlunicode.html#xmlUCSIsGreekandCoptic"/>
    <function name="xmlUCSIsGujarati ()" link="libxml2-xmlunicode.html#xmlUCSIsGujarati"/>
    <function name="xmlUCSIsGurmukhi ()" link="libxml2-xmlunicode.html#xmlUCSIsGurmukhi"/>
    <function name="xmlUCSIsHalfwidthandFullwidthForms ()" link="libxml2-xmlunicode.html#xmlUCSIsHalfwidthandFullwidthForms"/>
    <function name="xmlUCSIsHangulCompatibilityJamo ()" link="libxml2-xmlunicode.html#xmlUCSIsHangulCompatibilityJamo"/>
    <function name="xmlUCSIsHangulJamo ()" link="libxml2-xmlunicode.html#xmlUCSIsHangulJamo"/>
    <function name="xmlUCSIsHangulSyllables ()" link="libxml2-xmlunicode.html#xmlUCSIsHangulSyllables"/>
    <function name="xmlUCSIsHanunoo ()" link="libxml2-xmlunicode.html#xmlUCSIsHanunoo"/>
    <function name="xmlUCSIsHebrew ()" link="libxml2-xmlunicode.html#xmlUCSIsHebrew"/>
    <function name="xmlUCSIsHighPrivateUseSurrogates ()" link="libxml2-xmlunicode.html#xmlUCSIsHighPrivateUseSurrogates"/>
    <function name="xmlUCSIsHighSurrogates ()" link="libxml2-xmlunicode.html#xmlUCSIsHighSurrogates"/>
    <function name="xmlUCSIsHiragana ()" link="libxml2-xmlunicode.html#xmlUCSIsHiragana"/>
    <function name="xmlUCSIsIPAExtensions ()" link="libxml2-xmlunicode.html#xmlUCSIsIPAExtensions"/>
    <function name="xmlUCSIsIdeographicDescriptionCharacters ()" link="libxml2-xmlunicode.html#xmlUCSIsIdeographicDescriptionCharacters"/>
    <function name="xmlUCSIsKanbun ()" link="libxml2-xmlunicode.html#xmlUCSIsKanbun"/>
    <function name="xmlUCSIsKangxiRadicals ()" link="libxml2-xmlunicode.html#xmlUCSIsKangxiRadicals"/>
    <function name="xmlUCSIsKannada ()" link="libxml2-xmlunicode.html#xmlUCSIsKannada"/>
    <function name="xmlUCSIsKatakana ()" link="libxml2-xmlunicode.html#xmlUCSIsKatakana"/>
    <function name="xmlUCSIsKatakanaPhoneticExtensions ()" link="libxml2-xmlunicode.html#xmlUCSIsKatakanaPhoneticExtensions"/>
    <function name="xmlUCSIsKhmer ()" link="libxml2-xmlunicode.html#xmlUCSIsKhmer"/>
    <function name="xmlUCSIsKhmerSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsKhmerSymbols"/>
    <function name="xmlUCSIsLao ()" link="libxml2-xmlunicode.html#xmlUCSIsLao"/>
    <function name="xmlUCSIsLatin1Supplement ()" link="libxml2-xmlunicode.html#xmlUCSIsLatin1Supplement"/>
    <function name="xmlUCSIsLatinExtendedA ()" link="libxml2-xmlunicode.html#xmlUCSIsLatinExtendedA"/>
    <function name="xmlUCSIsLatinExtendedAdditional ()" link="libxml2-xmlunicode.html#xmlUCSIsLatinExtendedAdditional"/>
    <function name="xmlUCSIsLatinExtendedB ()" link="libxml2-xmlunicode.html#xmlUCSIsLatinExtendedB"/>
    <function name="xmlUCSIsLetterlikeSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsLetterlikeSymbols"/>
    <function name="xmlUCSIsLimbu ()" link="libxml2-xmlunicode.html#xmlUCSIsLimbu"/>
    <function name="xmlUCSIsLinearBIdeograms ()" link="libxml2-xmlunicode.html#xmlUCSIsLinearBIdeograms"/>
    <function name="xmlUCSIsLinearBSyllabary ()" link="libxml2-xmlunicode.html#xmlUCSIsLinearBSyllabary"/>
    <function name="xmlUCSIsLowSurrogates ()" link="libxml2-xmlunicode.html#xmlUCSIsLowSurrogates"/>
    <function name="xmlUCSIsMalayalam ()" link="libxml2-xmlunicode.html#xmlUCSIsMalayalam"/>
    <function name="xmlUCSIsMathematicalAlphanumericSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsMathematicalAlphanumericSymbols"/>
    <function name="xmlUCSIsMathematicalOperators ()" link="libxml2-xmlunicode.html#xmlUCSIsMathematicalOperators"/>
    <function name="xmlUCSIsMiscellaneousMathematicalSymbolsA ()" link="libxml2-xmlunicode.html#xmlUCSIsMiscellaneousMathematicalSymbolsA"/>
    <function name="xmlUCSIsMiscellaneousMathematicalSymbolsB ()" link="libxml2-xmlunicode.html#xmlUCSIsMiscellaneousMathematicalSymbolsB"/>
    <function name="xmlUCSIsMiscellaneousSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsMiscellaneousSymbols"/>
    <function name="xmlUCSIsMiscellaneousSymbolsandArrows ()" link="libxml2-xmlunicode.html#xmlUCSIsMiscellaneousSymbolsandArrows"/>
    <function name="xmlUCSIsMiscellaneousTechnical ()" link="libxml2-xmlunicode.html#xmlUCSIsMiscellaneousTechnical"/>
    <function name="xmlUCSIsMongolian ()" link="libxml2-xmlunicode.html#xmlUCSIsMongolian"/>
    <function name="xmlUCSIsMusicalSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsMusicalSymbols"/>
    <function name="xmlUCSIsMyanmar ()" link="libxml2-xmlunicode.html#xmlUCSIsMyanmar"/>
    <function name="xmlUCSIsNumberForms ()" link="libxml2-xmlunicode.html#xmlUCSIsNumberForms"/>
    <function name="xmlUCSIsOgham ()" link="libxml2-xmlunicode.html#xmlUCSIsOgham"/>
    <function name="xmlUCSIsOldItalic ()" link="libxml2-xmlunicode.html#xmlUCSIsOldItalic"/>
    <function name="xmlUCSIsOpticalCharacterRecognition ()" link="libxml2-xmlunicode.html#xmlUCSIsOpticalCharacterRecognition"/>
    <function name="xmlUCSIsOriya ()" link="libxml2-xmlunicode.html#xmlUCSIsOriya"/>
    <function name="xmlUCSIsOsmanya ()" link="libxml2-xmlunicode.html#xmlUCSIsOsmanya"/>
    <function name="xmlUCSIsPhoneticExtensions ()" link="libxml2-xmlunicode.html#xmlUCSIsPhoneticExtensions"/>
    <function name="xmlUCSIsPrivateUse ()" link="libxml2-xmlunicode.html#xmlUCSIsPrivateUse"/>
    <function name="xmlUCSIsPrivateUseArea ()" link="libxml2-xmlunicode.html#xmlUCSIsPrivateUseArea"/>
    <function name="xmlUCSIsRunic ()" link="libxml2-xmlunicode.html#xmlUCSIsRunic"/>
    <function name="xmlUCSIsShavian ()" link="libxml2-xmlunicode.html#xmlUCSIsShavian"/>
    <function name="xmlUCSIsSinhala ()" link="libxml2-xmlunicode.html#xmlUCSIsSinhala"/>
    <function name="xmlUCSIsSmallFormVariants ()" link="libxml2-xmlunicode.html#xmlUCSIsSmallFormVariants"/>
    <function name="xmlUCSIsSpacingModifierLetters ()" link="libxml2-xmlunicode.html#xmlUCSIsSpacingModifierLetters"/>
    <function name="xmlUCSIsSpecials ()" link="libxml2-xmlunicode.html#xmlUCSIsSpecials"/>
    <function name="xmlUCSIsSuperscriptsandSubscripts ()" link="libxml2-xmlunicode.html#xmlUCSIsSuperscriptsandSubscripts"/>
    <function name="xmlUCSIsSupplementalArrowsA ()" link="libxml2-xmlunicode.html#xmlUCSIsSupplementalArrowsA"/>
    <function name="xmlUCSIsSupplementalArrowsB ()" link="libxml2-xmlunicode.html#xmlUCSIsSupplementalArrowsB"/>
    <function name="xmlUCSIsSupplementalMathematicalOperators ()" link="libxml2-xmlunicode.html#xmlUCSIsSupplementalMathematicalOperators"/>
    <function name="xmlUCSIsSupplementaryPrivateUseAreaA ()" link="libxml2-xmlunicode.html#xmlUCSIsSupplementaryPrivateUseAreaA"/>
    <function name="xmlUCSIsSupplementaryPrivateUseAreaB ()" link="libxml2-xmlunicode.html#xmlUCSIsSupplementaryPrivateUseAreaB"/>
    <function name="xmlUCSIsSyriac ()" link="libxml2-xmlunicode.html#xmlUCSIsSyriac"/>
    <function name="xmlUCSIsTagalog ()" link="libxml2-xmlunicode.html#xmlUCSIsTagalog"/>
    <function name="xmlUCSIsTagbanwa ()" link="libxml2-xmlunicode.html#xmlUCSIsTagbanwa"/>
    <function name="xmlUCSIsTags ()" link="libxml2-xmlunicode.html#xmlUCSIsTags"/>
    <function name="xmlUCSIsTaiLe ()" link="libxml2-xmlunicode.html#xmlUCSIsTaiLe"/>
    <function name="xmlUCSIsTaiXuanJingSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsTaiXuanJingSymbols"/>
    <function name="xmlUCSIsTamil ()" link="libxml2-xmlunicode.html#xmlUCSIsTamil"/>
    <function name="xmlUCSIsTelugu ()" link="libxml2-xmlunicode.html#xmlUCSIsTelugu"/>
    <function name="xmlUCSIsThaana ()" link="libxml2-xmlunicode.html#xmlUCSIsThaana"/>
    <function name="xmlUCSIsThai ()" link="libxml2-xmlunicode.html#xmlUCSIsThai"/>
    <function name="xmlUCSIsTibetan ()" link="libxml2-xmlunicode.html#xmlUCSIsTibetan"/>
    <function name="xmlUCSIsUgaritic ()" link="libxml2-xmlunicode.html#xmlUCSIsUgaritic"/>
    <function name="xmlUCSIsUnifiedCanadianAboriginalSyllabics ()" link="libxml2-xmlunicode.html#xmlUCSIsUnifiedCanadianAboriginalSyllabics"/>
    <function name="xmlUCSIsVariationSelectors ()" link="libxml2-xmlunicode.html#xmlUCSIsVariationSelectors"/>
    <function name="xmlUCSIsVariationSelectorsSupplement ()" link="libxml2-xmlunicode.html#xmlUCSIsVariationSelectorsSupplement"/>
    <function name="xmlUCSIsYiRadicals ()" link="libxml2-xmlunicode.html#xmlUCSIsYiRadicals"/>
    <function name="xmlUCSIsYiSyllables ()" link="libxml2-xmlunicode.html#xmlUCSIsYiSyllables"/>
    <function name="xmlUCSIsYijingHexagramSymbols ()" link="libxml2-xmlunicode.html#xmlUCSIsYijingHexagramSymbols"/>
    <function name="xmlURIEscape ()" link="libxml2-uri.html#xmlURIEscape"/>
    <function name="xmlURIEscapeStr ()" link="libxml2-uri.html#xmlURIEscapeStr"/>
    <function name="xmlURIUnescapeString ()" link="libxml2-uri.html#xmlURIUnescapeString"/>
    <function name="xmlUTF8Charcmp ()" link="libxml2-xmlstring.html#xmlUTF8Charcmp"/>
    <function name="xmlUTF8Size ()" link="libxml2-xmlstring.html#xmlUTF8Size"/>
    <function name="xmlUTF8Strlen ()" link="libxml2-xmlstring.html#xmlUTF8Strlen"/>
    <function name="xmlUTF8Strloc ()" link="libxml2-xmlstring.html#xmlUTF8Strloc"/>
    <function name="xmlUTF8Strndup ()" link="libxml2-xmlstring.html#xmlUTF8Strndup"/>
    <function name="xmlUTF8Strpos ()" link="libxml2-xmlstring.html#xmlUTF8Strpos"/>
    <function name="xmlUTF8Strsize ()" link="libxml2-xmlstring.html#xmlUTF8Strsize"/>
    <function name="xmlUTF8Strsub ()" link="libxml2-xmlstring.html#xmlUTF8Strsub"/>
    <function name="xmlUnlinkNode ()" link="libxml2-tree.html#xmlUnlinkNode"/>
    <function name="xmlUnlockLibrary ()" link="libxml2-threads.html#xmlUnlockLibrary"/>
    <function name="xmlUnsetNsProp ()" link="libxml2-tree.html#xmlUnsetNsProp"/>
    <function name="xmlUnsetProp ()" link="libxml2-tree.html#xmlUnsetProp"/>
    <function name="xmlValidBuildContentModel ()" link="libxml2-valid.html#xmlValidBuildContentModel"/>
    <function name="xmlValidCtxtNormalizeAttributeValue ()" link="libxml2-valid.html#xmlValidCtxtNormalizeAttributeValue"/>
    <function name="xmlValidGetPotentialChildren ()" link="libxml2-valid.html#xmlValidGetPotentialChildren"/>
    <function name="xmlValidGetValidElements ()" link="libxml2-valid.html#xmlValidGetValidElements"/>
    <function name="xmlValidNormalizeAttributeValue ()" link="libxml2-valid.html#xmlValidNormalizeAttributeValue"/>
    <function name="xmlValidateAttributeDecl ()" link="libxml2-valid.html#xmlValidateAttributeDecl"/>
    <function name="xmlValidateAttributeValue ()" link="libxml2-valid.html#xmlValidateAttributeValue"/>
    <function name="xmlValidateDocument ()" link="libxml2-valid.html#xmlValidateDocument"/>
    <function name="xmlValidateDocumentFinal ()" link="libxml2-valid.html#xmlValidateDocumentFinal"/>
    <function name="xmlValidateDtd ()" link="libxml2-valid.html#xmlValidateDtd"/>
    <function name="xmlValidateDtdFinal ()" link="libxml2-valid.html#xmlValidateDtdFinal"/>
    <function name="xmlValidateElement ()" link="libxml2-valid.html#xmlValidateElement"/>
    <function name="xmlValidateElementDecl ()" link="libxml2-valid.html#xmlValidateElementDecl"/>
    <function name="xmlValidateNCName ()" link="libxml2-tree.html#xmlValidateNCName"/>
    <function name="xmlValidateNMToken ()" link="libxml2-tree.html#xmlValidateNMToken"/>
    <function name="xmlValidateName ()" link="libxml2-tree.html#xmlValidateName"/>
    <function name="xmlValidateNameValue ()" link="libxml2-valid.html#xmlValidateNameValue"/>
    <function name="xmlValidateNamesValue ()" link="libxml2-valid.html#xmlValidateNamesValue"/>
    <function name="xmlValidateNmtokenValue ()" link="libxml2-valid.html#xmlValidateNmtokenValue"/>
    <function name="xmlValidateNmtokensValue ()" link="libxml2-valid.html#xmlValidateNmtokensValue"/>
    <function name="xmlValidateNotationDecl ()" link="libxml2-valid.html#xmlValidateNotationDecl"/>
    <function name="xmlValidateNotationUse ()" link="libxml2-valid.html#xmlValidateNotationUse"/>
    <function name="xmlValidateOneAttribute ()" link="libxml2-valid.html#xmlValidateOneAttribute"/>
    <function name="xmlValidateOneElement ()" link="libxml2-valid.html#xmlValidateOneElement"/>
    <function name="xmlValidateOneNamespace ()" link="libxml2-valid.html#xmlValidateOneNamespace"/>
    <function name="xmlValidatePopElement ()" link="libxml2-valid.html#xmlValidatePopElement"/>
    <function name="xmlValidatePushCData ()" link="libxml2-valid.html#xmlValidatePushCData"/>
    <function name="xmlValidatePushElement ()" link="libxml2-valid.html#xmlValidatePushElement"/>
    <function name="xmlValidateQName ()" link="libxml2-tree.html#xmlValidateQName"/>
    <function name="xmlValidateRoot ()" link="libxml2-valid.html#xmlValidateRoot"/>
    <function name="xmlXIncludeFreeContext ()" link="libxml2-xinclude.html#xmlXIncludeFreeContext"/>
    <function name="xmlXIncludeNewContext ()" link="libxml2-xinclude.html#xmlXIncludeNewContext"/>
    <function name="xmlXIncludeProcess ()" link="libxml2-xinclude.html#xmlXIncludeProcess"/>
    <function name="xmlXIncludeProcessFlags ()" link="libxml2-xinclude.html#xmlXIncludeProcessFlags"/>
    <function name="xmlXIncludeProcessFlagsData ()" link="libxml2-xinclude.html#xmlXIncludeProcessFlagsData"/>
    <function name="xmlXIncludeProcessNode ()" link="libxml2-xinclude.html#xmlXIncludeProcessNode"/>
    <function name="xmlXIncludeProcessTree ()" link="libxml2-xinclude.html#xmlXIncludeProcessTree"/>
    <function name="xmlXIncludeProcessTreeFlags ()" link="libxml2-xinclude.html#xmlXIncludeProcessTreeFlags"/>
    <function name="xmlXIncludeProcessTreeFlagsData ()" link="libxml2-xinclude.html#xmlXIncludeProcessTreeFlagsData"/>
    <function name="xmlXIncludeSetFlags ()" link="libxml2-xinclude.html#xmlXIncludeSetFlags"/>
    <function name="xmlXPathAddValues ()" link="libxml2-xpathInternals.html#xmlXPathAddValues"/>
    <function name="xmlXPathBooleanFunction ()" link="libxml2-xpathInternals.html#xmlXPathBooleanFunction"/>
    <function name="xmlXPathCastBooleanToNumber ()" link="libxml2-xpath.html#xmlXPathCastBooleanToNumber"/>
    <function name="xmlXPathCastBooleanToString ()" link="libxml2-xpath.html#xmlXPathCastBooleanToString"/>
    <function name="xmlXPathCastNodeSetToBoolean ()" link="libxml2-xpath.html#xmlXPathCastNodeSetToBoolean"/>
    <function name="xmlXPathCastNodeSetToNumber ()" link="libxml2-xpath.html#xmlXPathCastNodeSetToNumber"/>
    <function name="xmlXPathCastNodeSetToString ()" link="libxml2-xpath.html#xmlXPathCastNodeSetToString"/>
    <function name="xmlXPathCastNodeToNumber ()" link="libxml2-xpath.html#xmlXPathCastNodeToNumber"/>
    <function name="xmlXPathCastNodeToString ()" link="libxml2-xpath.html#xmlXPathCastNodeToString"/>
    <function name="xmlXPathCastNumberToBoolean ()" link="libxml2-xpath.html#xmlXPathCastNumberToBoolean"/>
    <function name="xmlXPathCastNumberToString ()" link="libxml2-xpath.html#xmlXPathCastNumberToString"/>
    <function name="xmlXPathCastStringToBoolean ()" link="libxml2-xpath.html#xmlXPathCastStringToBoolean"/>
    <function name="xmlXPathCastStringToNumber ()" link="libxml2-xpath.html#xmlXPathCastStringToNumber"/>
    <function name="xmlXPathCastToBoolean ()" link="libxml2-xpath.html#xmlXPathCastToBoolean"/>
    <function name="xmlXPathCastToNumber ()" link="libxml2-xpath.html#xmlXPathCastToNumber"/>
    <function name="xmlXPathCastToString ()" link="libxml2-xpath.html#xmlXPathCastToString"/>
    <function name="xmlXPathCeilingFunction ()" link="libxml2-xpathInternals.html#xmlXPathCeilingFunction"/>
    <function name="xmlXPathCmpNodes ()" link="libxml2-xpath.html#xmlXPathCmpNodes"/>
    <function name="xmlXPathCompareValues ()" link="libxml2-xpathInternals.html#xmlXPathCompareValues"/>
    <function name="xmlXPathCompile ()" link="libxml2-xpath.html#xmlXPathCompile"/>
    <function name="xmlXPathCompiledEval ()" link="libxml2-xpath.html#xmlXPathCompiledEval"/>
    <function name="xmlXPathCompiledEvalToBoolean ()" link="libxml2-xpath.html#xmlXPathCompiledEvalToBoolean"/>
    <function name="xmlXPathConcatFunction ()" link="libxml2-xpathInternals.html#xmlXPathConcatFunction"/>
    <function name="xmlXPathContainsFunction ()" link="libxml2-xpathInternals.html#xmlXPathContainsFunction"/>
    <function name="xmlXPathContextSetCache ()" link="libxml2-xpath.html#xmlXPathContextSetCache"/>
    <function name="xmlXPathConvertBoolean ()" link="libxml2-xpath.html#xmlXPathConvertBoolean"/>
    <function name="xmlXPathConvertNumber ()" link="libxml2-xpath.html#xmlXPathConvertNumber"/>
    <function name="xmlXPathConvertString ()" link="libxml2-xpath.html#xmlXPathConvertString"/>
    <function name="xmlXPathCountFunction ()" link="libxml2-xpathInternals.html#xmlXPathCountFunction"/>
    <function name="xmlXPathCtxtCompile ()" link="libxml2-xpath.html#xmlXPathCtxtCompile"/>
    <function name="xmlXPathDebugDumpCompExpr ()" link="libxml2-xpathInternals.html#xmlXPathDebugDumpCompExpr"/>
    <function name="xmlXPathDebugDumpObject ()" link="libxml2-xpathInternals.html#xmlXPathDebugDumpObject"/>
    <function name="xmlXPathDifference ()" link="libxml2-xpathInternals.html#xmlXPathDifference"/>
    <function name="xmlXPathDistinct ()" link="libxml2-xpathInternals.html#xmlXPathDistinct"/>
    <function name="xmlXPathDistinctSorted ()" link="libxml2-xpathInternals.html#xmlXPathDistinctSorted"/>
    <function name="xmlXPathDivValues ()" link="libxml2-xpathInternals.html#xmlXPathDivValues"/>
    <function name="xmlXPathEqualValues ()" link="libxml2-xpathInternals.html#xmlXPathEqualValues"/>
    <function name="xmlXPathErr ()" link="libxml2-xpathInternals.html#xmlXPathErr"/>
    <function name="xmlXPathEval ()" link="libxml2-xpath.html#xmlXPathEval"/>
    <function name="xmlXPathEvalExpr ()" link="libxml2-xpathInternals.html#xmlXPathEvalExpr"/>
    <function name="xmlXPathEvalExpression ()" link="libxml2-xpath.html#xmlXPathEvalExpression"/>
    <function name="xmlXPathEvalPredicate ()" link="libxml2-xpath.html#xmlXPathEvalPredicate"/>
    <function name="xmlXPathEvaluatePredicateResult ()" link="libxml2-xpathInternals.html#xmlXPathEvaluatePredicateResult"/>
    <function name="xmlXPathFalseFunction ()" link="libxml2-xpathInternals.html#xmlXPathFalseFunction"/>
    <function name="xmlXPathFloorFunction ()" link="libxml2-xpathInternals.html#xmlXPathFloorFunction"/>
    <function name="xmlXPathFreeCompExpr ()" link="libxml2-xpath.html#xmlXPathFreeCompExpr"/>
    <function name="xmlXPathFreeContext ()" link="libxml2-xpath.html#xmlXPathFreeContext"/>
    <function name="xmlXPathFreeNodeSet ()" link="libxml2-xpath.html#xmlXPathFreeNodeSet"/>
    <function name="xmlXPathFreeNodeSetList ()" link="libxml2-xpath.html#xmlXPathFreeNodeSetList"/>
    <function name="xmlXPathFreeObject ()" link="libxml2-xpath.html#xmlXPathFreeObject"/>
    <function name="xmlXPathFreeParserContext ()" link="libxml2-xpathInternals.html#xmlXPathFreeParserContext"/>
    <function name="xmlXPathFunctionLookup ()" link="libxml2-xpathInternals.html#xmlXPathFunctionLookup"/>
    <function name="xmlXPathFunctionLookupNS ()" link="libxml2-xpathInternals.html#xmlXPathFunctionLookupNS"/>
    <function name="xmlXPathHasSameNodes ()" link="libxml2-xpathInternals.html#xmlXPathHasSameNodes"/>
    <function name="xmlXPathIdFunction ()" link="libxml2-xpathInternals.html#xmlXPathIdFunction"/>
    <function name="xmlXPathInit ()" link="libxml2-xpath.html#xmlXPathInit"/>
    <function name="xmlXPathIntersection ()" link="libxml2-xpathInternals.html#xmlXPathIntersection"/>
    <function name="xmlXPathIsInf ()" link="libxml2-xpath.html#xmlXPathIsInf"/>
    <function name="xmlXPathIsNaN ()" link="libxml2-xpath.html#xmlXPathIsNaN"/>
    <function name="xmlXPathIsNodeType ()" link="libxml2-xpathInternals.html#xmlXPathIsNodeType"/>
    <function name="xmlXPathLangFunction ()" link="libxml2-xpathInternals.html#xmlXPathLangFunction"/>
    <function name="xmlXPathLastFunction ()" link="libxml2-xpathInternals.html#xmlXPathLastFunction"/>
    <function name="xmlXPathLeading ()" link="libxml2-xpathInternals.html#xmlXPathLeading"/>
    <function name="xmlXPathLeadingSorted ()" link="libxml2-xpathInternals.html#xmlXPathLeadingSorted"/>
    <function name="xmlXPathLocalNameFunction ()" link="libxml2-xpathInternals.html#xmlXPathLocalNameFunction"/>
    <function name="xmlXPathModValues ()" link="libxml2-xpathInternals.html#xmlXPathModValues"/>
    <function name="xmlXPathMultValues ()" link="libxml2-xpathInternals.html#xmlXPathMultValues"/>
    <function name="xmlXPathNamespaceURIFunction ()" link="libxml2-xpathInternals.html#xmlXPathNamespaceURIFunction"/>
    <function name="xmlXPathNewBoolean ()" link="libxml2-xpathInternals.html#xmlXPathNewBoolean"/>
    <function name="xmlXPathNewCString ()" link="libxml2-xpathInternals.html#xmlXPathNewCString"/>
    <function name="xmlXPathNewContext ()" link="libxml2-xpath.html#xmlXPathNewContext"/>
    <function name="xmlXPathNewFloat ()" link="libxml2-xpathInternals.html#xmlXPathNewFloat"/>
    <function name="xmlXPathNewNodeSet ()" link="libxml2-xpathInternals.html#xmlXPathNewNodeSet"/>
    <function name="xmlXPathNewNodeSetList ()" link="libxml2-xpathInternals.html#xmlXPathNewNodeSetList"/>
    <function name="xmlXPathNewParserContext ()" link="libxml2-xpathInternals.html#xmlXPathNewParserContext"/>
    <function name="xmlXPathNewString ()" link="libxml2-xpathInternals.html#xmlXPathNewString"/>
    <function name="xmlXPathNewValueTree ()" link="libxml2-xpathInternals.html#xmlXPathNewValueTree"/>
    <function name="xmlXPathNextAncestor ()" link="libxml2-xpathInternals.html#xmlXPathNextAncestor"/>
    <function name="xmlXPathNextAncestorOrSelf ()" link="libxml2-xpathInternals.html#xmlXPathNextAncestorOrSelf"/>
    <function name="xmlXPathNextAttribute ()" link="libxml2-xpathInternals.html#xmlXPathNextAttribute"/>
    <function name="xmlXPathNextChild ()" link="libxml2-xpathInternals.html#xmlXPathNextChild"/>
    <function name="xmlXPathNextDescendant ()" link="libxml2-xpathInternals.html#xmlXPathNextDescendant"/>
    <function name="xmlXPathNextDescendantOrSelf ()" link="libxml2-xpathInternals.html#xmlXPathNextDescendantOrSelf"/>
    <function name="xmlXPathNextFollowing ()" link="libxml2-xpathInternals.html#xmlXPathNextFollowing"/>
    <function name="xmlXPathNextFollowingSibling ()" link="libxml2-xpathInternals.html#xmlXPathNextFollowingSibling"/>
    <function name="xmlXPathNextNamespace ()" link="libxml2-xpathInternals.html#xmlXPathNextNamespace"/>
    <function name="xmlXPathNextParent ()" link="libxml2-xpathInternals.html#xmlXPathNextParent"/>
    <function name="xmlXPathNextPreceding ()" link="libxml2-xpathInternals.html#xmlXPathNextPreceding"/>
    <function name="xmlXPathNextPrecedingSibling ()" link="libxml2-xpathInternals.html#xmlXPathNextPrecedingSibling"/>
    <function name="xmlXPathNextSelf ()" link="libxml2-xpathInternals.html#xmlXPathNextSelf"/>
    <function name="xmlXPathNodeEval ()" link="libxml2-xpath.html#xmlXPathNodeEval"/>
    <function name="xmlXPathNodeLeading ()" link="libxml2-xpathInternals.html#xmlXPathNodeLeading"/>
    <function name="xmlXPathNodeLeadingSorted ()" link="libxml2-xpathInternals.html#xmlXPathNodeLeadingSorted"/>
    <function name="xmlXPathNodeSetAdd ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetAdd"/>
    <function name="xmlXPathNodeSetAddNs ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetAddNs"/>
    <function name="xmlXPathNodeSetAddUnique ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetAddUnique"/>
    <function name="xmlXPathNodeSetContains ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetContains"/>
    <function name="xmlXPathNodeSetCreate ()" link="libxml2-xpath.html#xmlXPathNodeSetCreate"/>
    <function name="xmlXPathNodeSetDel ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetDel"/>
    <function name="xmlXPathNodeSetFreeNs ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetFreeNs"/>
    <function name="xmlXPathNodeSetMerge ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetMerge"/>
    <function name="xmlXPathNodeSetRemove ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetRemove"/>
    <function name="xmlXPathNodeSetSort ()" link="libxml2-xpathInternals.html#xmlXPathNodeSetSort"/>
    <function name="xmlXPathNodeTrailing ()" link="libxml2-xpathInternals.html#xmlXPathNodeTrailing"/>
    <function name="xmlXPathNodeTrailingSorted ()" link="libxml2-xpathInternals.html#xmlXPathNodeTrailingSorted"/>
    <function name="xmlXPathNormalizeFunction ()" link="libxml2-xpathInternals.html#xmlXPathNormalizeFunction"/>
    <function name="xmlXPathNotEqualValues ()" link="libxml2-xpathInternals.html#xmlXPathNotEqualValues"/>
    <function name="xmlXPathNotFunction ()" link="libxml2-xpathInternals.html#xmlXPathNotFunction"/>
    <function name="xmlXPathNsLookup ()" link="libxml2-xpathInternals.html#xmlXPathNsLookup"/>
    <function name="xmlXPathNumberFunction ()" link="libxml2-xpathInternals.html#xmlXPathNumberFunction"/>
    <function name="xmlXPathObjectCopy ()" link="libxml2-xpath.html#xmlXPathObjectCopy"/>
    <function name="xmlXPathOrderDocElems ()" link="libxml2-xpath.html#xmlXPathOrderDocElems"/>
    <function name="xmlXPathParseNCName ()" link="libxml2-xpathInternals.html#xmlXPathParseNCName"/>
    <function name="xmlXPathParseName ()" link="libxml2-xpathInternals.html#xmlXPathParseName"/>
    <function name="xmlXPathPopBoolean ()" link="libxml2-xpathInternals.html#xmlXPathPopBoolean"/>
    <function name="xmlXPathPopExternal ()" link="libxml2-xpathInternals.html#xmlXPathPopExternal"/>
    <function name="xmlXPathPopNodeSet ()" link="libxml2-xpathInternals.html#xmlXPathPopNodeSet"/>
    <function name="xmlXPathPopNumber ()" link="libxml2-xpathInternals.html#xmlXPathPopNumber"/>
    <function name="xmlXPathPopString ()" link="libxml2-xpathInternals.html#xmlXPathPopString"/>
    <function name="xmlXPathPositionFunction ()" link="libxml2-xpathInternals.html#xmlXPathPositionFunction"/>
    <function name="xmlXPathRegisterAllFunctions ()" link="libxml2-xpathInternals.html#xmlXPathRegisterAllFunctions"/>
    <function name="xmlXPathRegisterFunc ()" link="libxml2-xpathInternals.html#xmlXPathRegisterFunc"/>
    <function name="xmlXPathRegisterFuncLookup ()" link="libxml2-xpathInternals.html#xmlXPathRegisterFuncLookup"/>
    <function name="xmlXPathRegisterFuncNS ()" link="libxml2-xpathInternals.html#xmlXPathRegisterFuncNS"/>
    <function name="xmlXPathRegisterNs ()" link="libxml2-xpathInternals.html#xmlXPathRegisterNs"/>
    <function name="xmlXPathRegisterVariable ()" link="libxml2-xpathInternals.html#xmlXPathRegisterVariable"/>
    <function name="xmlXPathRegisterVariableLookup ()" link="libxml2-xpathInternals.html#xmlXPathRegisterVariableLookup"/>
    <function name="xmlXPathRegisterVariableNS ()" link="libxml2-xpathInternals.html#xmlXPathRegisterVariableNS"/>
    <function name="xmlXPathRegisteredFuncsCleanup ()" link="libxml2-xpathInternals.html#xmlXPathRegisteredFuncsCleanup"/>
    <function name="xmlXPathRegisteredNsCleanup ()" link="libxml2-xpathInternals.html#xmlXPathRegisteredNsCleanup"/>
    <function name="xmlXPathRegisteredVariablesCleanup ()" link="libxml2-xpathInternals.html#xmlXPathRegisteredVariablesCleanup"/>
    <function name="xmlXPathRoot ()" link="libxml2-xpathInternals.html#xmlXPathRoot"/>
    <function name="xmlXPathRoundFunction ()" link="libxml2-xpathInternals.html#xmlXPathRoundFunction"/>
    <function name="xmlXPathSetContextNode ()" link="libxml2-xpath.html#xmlXPathSetContextNode"/>
    <function name="xmlXPathStartsWithFunction ()" link="libxml2-xpathInternals.html#xmlXPathStartsWithFunction"/>
    <function name="xmlXPathStringEvalNumber ()" link="libxml2-xpathInternals.html#xmlXPathStringEvalNumber"/>
    <function name="xmlXPathStringFunction ()" link="libxml2-xpathInternals.html#xmlXPathStringFunction"/>
    <function name="xmlXPathStringLengthFunction ()" link="libxml2-xpathInternals.html#xmlXPathStringLengthFunction"/>
    <function name="xmlXPathSubValues ()" link="libxml2-xpathInternals.html#xmlXPathSubValues"/>
    <function name="xmlXPathSubstringAfterFunction ()" link="libxml2-xpathInternals.html#xmlXPathSubstringAfterFunction"/>
    <function name="xmlXPathSubstringBeforeFunction ()" link="libxml2-xpathInternals.html#xmlXPathSubstringBeforeFunction"/>
    <function name="xmlXPathSubstringFunction ()" link="libxml2-xpathInternals.html#xmlXPathSubstringFunction"/>
    <function name="xmlXPathSumFunction ()" link="libxml2-xpathInternals.html#xmlXPathSumFunction"/>
    <function name="xmlXPathTrailing ()" link="libxml2-xpathInternals.html#xmlXPathTrailing"/>
    <function name="xmlXPathTrailingSorted ()" link="libxml2-xpathInternals.html#xmlXPathTrailingSorted"/>
    <function name="xmlXPathTranslateFunction ()" link="libxml2-xpathInternals.html#xmlXPathTranslateFunction"/>
    <function name="xmlXPathTrueFunction ()" link="libxml2-xpathInternals.html#xmlXPathTrueFunction"/>
    <function name="xmlXPathValueFlipSign ()" link="libxml2-xpathInternals.html#xmlXPathValueFlipSign"/>
    <function name="xmlXPathVariableLookup ()" link="libxml2-xpathInternals.html#xmlXPathVariableLookup"/>
    <function name="xmlXPathVariableLookupNS ()" link="libxml2-xpathInternals.html#xmlXPathVariableLookupNS"/>
    <function name="xmlXPathWrapCString ()" link="libxml2-xpathInternals.html#xmlXPathWrapCString"/>
    <function name="xmlXPathWrapExternal ()" link="libxml2-xpathInternals.html#xmlXPathWrapExternal"/>
    <function name="xmlXPathWrapNodeSet ()" link="libxml2-xpathInternals.html#xmlXPathWrapNodeSet"/>
    <function name="xmlXPathWrapString ()" link="libxml2-xpathInternals.html#xmlXPathWrapString"/>
    <function name="xmlXPatherror ()" link="libxml2-xpathInternals.html#xmlXPatherror"/>
    <function name="xmlXPtrBuildNodeList ()" link="libxml2-xpointer.html#xmlXPtrBuildNodeList"/>
    <function name="xmlXPtrEval ()" link="libxml2-xpointer.html#xmlXPtrEval"/>
    <function name="xmlXPtrEvalRangePredicate ()" link="libxml2-xpointer.html#xmlXPtrEvalRangePredicate"/>
    <function name="xmlXPtrFreeLocationSet ()" link="libxml2-xpointer.html#xmlXPtrFreeLocationSet"/>
    <function name="xmlXPtrLocationSetAdd ()" link="libxml2-xpointer.html#xmlXPtrLocationSetAdd"/>
    <function name="xmlXPtrLocationSetCreate ()" link="libxml2-xpointer.html#xmlXPtrLocationSetCreate"/>
    <function name="xmlXPtrLocationSetDel ()" link="libxml2-xpointer.html#xmlXPtrLocationSetDel"/>
    <function name="xmlXPtrLocationSetMerge ()" link="libxml2-xpointer.html#xmlXPtrLocationSetMerge"/>
    <function name="xmlXPtrLocationSetRemove ()" link="libxml2-xpointer.html#xmlXPtrLocationSetRemove"/>
    <function name="xmlXPtrNewCollapsedRange ()" link="libxml2-xpointer.html#xmlXPtrNewCollapsedRange"/>
    <function name="xmlXPtrNewContext ()" link="libxml2-xpointer.html#xmlXPtrNewContext"/>
    <function name="xmlXPtrNewLocationSetNodeSet ()" link="libxml2-xpointer.html#xmlXPtrNewLocationSetNodeSet"/>
    <function name="xmlXPtrNewLocationSetNodes ()" link="libxml2-xpointer.html#xmlXPtrNewLocationSetNodes"/>
    <function name="xmlXPtrNewRange ()" link="libxml2-xpointer.html#xmlXPtrNewRange"/>
    <function name="xmlXPtrNewRangeNodeObject ()" link="libxml2-xpointer.html#xmlXPtrNewRangeNodeObject"/>
    <function name="xmlXPtrNewRangeNodePoint ()" link="libxml2-xpointer.html#xmlXPtrNewRangeNodePoint"/>
    <function name="xmlXPtrNewRangeNodes ()" link="libxml2-xpointer.html#xmlXPtrNewRangeNodes"/>
    <function name="xmlXPtrNewRangePointNode ()" link="libxml2-xpointer.html#xmlXPtrNewRangePointNode"/>
    <function name="xmlXPtrNewRangePoints ()" link="libxml2-xpointer.html#xmlXPtrNewRangePoints"/>
    <function name="xmlXPtrRangeToFunction ()" link="libxml2-xpointer.html#xmlXPtrRangeToFunction"/>
    <function name="xmlXPtrWrapLocationSet ()" link="libxml2-xpointer.html#xmlXPtrWrapLocationSet"/>
  </functions>
</book>
