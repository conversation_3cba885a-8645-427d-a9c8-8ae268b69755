<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>libxml2 Reference Manual</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="general.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">libxml2 Reference Manual</span>
    </h2>
    <p>Libxml2 is the XML C parser and toolkit developed for the Gnome project
(but usable outside of the Gnome platform), it is free software available
under the <a href="http://www.opensource.org/licenses/mit-license.html">MIT
License</a>. XML itself is a metalanguage to design markup languages, i.e.
text language where semantic and structure are added to the content using
extra "markup" information enclosed between angle brackets. HTML is the most
well-known markup language. Though the library is written in C <a href="http://xmlsoft.org/python.html">a variety of language bindings</a> make it available in
other environments.</p>
    <p>Libxml2 implements a number of existing standards related to markup
languages:</p>
    <ul>
      <li>the XML standard: <a href="http://www.w3.org/TR/REC-xml">http://www.w3.org/TR/REC-xml</a></li>
      <li>Namespaces in XML: <a href="http://www.w3.org/TR/REC-xml-names/">http://www.w3.org/TR/REC-xml-names/</a></li>
      <li>XML Base: <a href="http://www.w3.org/TR/xmlbase/">http://www.w3.org/TR/xmlbase/</a></li>
      <li><a href="http://www.cis.ohio-state.edu/rfc/rfc2396.txt">RFC 2396</a> :
Uniform Resource Identifiers <a href="http://www.ietf.org/rfc/rfc2396.txt">http://www.ietf.org/rfc/rfc2396.txt</a></li>
      <li>XML Path Language (XPath) 1.0: <a href="http://www.w3.org/TR/xpath">http://www.w3.org/TR/xpath</a></li>
      <li>HTML4 parser: <a href="http://www.w3.org/TR/html401/">http://www.w3.org/TR/html401/</a></li>
      <li>XML Pointer Language (XPointer) Version 1.0: <a href="http://www.w3.org/TR/xptr">http://www.w3.org/TR/xptr</a></li>
      <li>XML Inclusions (XInclude) Version 1.0: <a href="http://www.w3.org/TR/xinclude/">http://www.w3.org/TR/xinclude/</a></li>
      <li>ISO-8859-x encodings, as well as <a href="http://www.cis.ohio-state.edu/rfc/rfc2044.txt">rfc2044</a> [UTF-8]
and <a href="http://www.cis.ohio-state.edu/rfc/rfc2781.txt">rfc2781</a>
[UTF-16] Unicode encodings, and more if using iconv support</li>
      <li>part of SGML Open Technical Resolution TR9401:1997</li>
      <li>XML Catalogs Working Draft 06 August 2001: <a href="http://www.oasis-open.org/committees/entity/spec-2001-08-06.html">http://www.oasis-open.org/committees/entity/spec-2001-08-06.html</a></li>
      <li>Canonical XML Version 1.0: <a href="http://www.w3.org/TR/xml-c14n">http://www.w3.org/TR/xml-c14n</a>
and the Exclusive XML Canonicalization CR draft <a href="http://www.w3.org/TR/xml-exc-c14n">http://www.w3.org/TR/xml-exc-c14n</a></li>
      <li>Relax NG, ISO/IEC 19757-2:2003, <a href="http://www.oasis-open.org/committees/relax-ng/spec-20011203.html">http://www.oasis-open.org/committees/relax-ng/spec-20011203.html</a></li>
      <li>W3C XML Schemas Part 2: Datatypes <a href="http://www.w3.org/TR/2001/REC-xmlschema-2-20010502/">REC 02 May
2001</a></li>
      <li>W3C <a href="http://www.w3.org/TR/xml-id/">xml:id</a> Working Draft 7
April 2004</li>
    </ul>
    <p> As a result the <a href="general.html">libxml2 API</a> is very
	      large. If you get lost searching for some specific API use
	      <a href="http://xmlsoft.org/search.php">the online search
	      engine</a> hosted on <a href="http://xmlsoft.org/">xmlsoft.org</a>
	      the libxml2 and libxslt project page. </p>
  </body>
</html>
