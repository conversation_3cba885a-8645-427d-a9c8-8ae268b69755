<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xmlIO: interface for the I/O interfaces used by the parser</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xlink.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-xmlautomata.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xmlIO</span>
    </h2>
    <p>xmlIO - interface for the I/O interfaces used by the parser</p>
    <p>interface for the I/O interfaces used by the parser </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">int	<a href="#xmlIOHTTPRead">xmlIOHTTPRead</a>			(void * context, <br/>					 char * buffer, <br/>					 int len);
typedef int <a href="#xmlInputMatchCallback">xmlInputMatchCallback</a>		(char const * filename);
void	<a href="#xmlRegisterDefaultOutputCallbacks">xmlRegisterDefaultOutputCallbacks</a>	(void);
int	<a href="#xmlFileClose">xmlFileClose</a>			(void * context);
typedef int <a href="#xmlOutputMatchCallback">xmlOutputMatchCallback</a>		(char const * filename);
int	<a href="#xmlParserInputBufferPush">xmlParserInputBufferPush</a>	(<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in, <br/>					 int len, <br/>					 const char * buf);
int	<a href="#xmlIOFTPRead">xmlIOFTPRead</a>			(void * context, <br/>					 char * buffer, <br/>					 int len);
void	<a href="#xmlRegisterHTTPPostCallbacks">xmlRegisterHTTPPostCallbacks</a>	(void);
void *	<a href="#xmlIOFTPOpen">xmlIOFTPOpen</a>			(const char * filename);
int	<a href="#xmlIOFTPClose">xmlIOFTPClose</a>			(void * context);
void *	<a href="#xmlFileOpen">xmlFileOpen</a>			(const char * filename);
<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	<a href="#xmlOutputBufferCreateFile">xmlOutputBufferCreateFile</a>	(FILE * file, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder);
int	<a href="#xmlCheckFilename">xmlCheckFilename</a>		(const char * path);
typedef void * <a href="#xmlOutputOpenCallback">xmlOutputOpenCallback</a>		(char const * filename);
<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlParserInputBufferCreateFilename">xmlParserInputBufferCreateFilename</a>	(const char * URI, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
int	<a href="#xmlOutputBufferClose">xmlOutputBufferClose</a>		(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out);
<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlAllocParserInputBuffer">xmlAllocParserInputBuffer</a>	(<a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	<a href="#xmlOutputBufferCreateIO">xmlOutputBufferCreateIO</a>	(<a href="libxml2-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a> iowrite, <br/>						 <a href="libxml2-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a> ioclose, <br/>						 void * ioctx, <br/>						 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder);
typedef int <a href="#xmlOutputWriteCallback">xmlOutputWriteCallback</a>		(void * context, <br/>					 const char * buffer, <br/>					 int len);
int	<a href="#xmlOutputBufferFlush">xmlOutputBufferFlush</a>		(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out);
<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlCheckHTTPInput">xmlCheckHTTPInput</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> ret);
int	<a href="#xmlRegisterOutputCallbacks">xmlRegisterOutputCallbacks</a>	(<a href="libxml2-xmlIO.html#xmlOutputMatchCallback">xmlOutputMatchCallback</a> matchFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlOutputOpenCallback">xmlOutputOpenCallback</a> openFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a> writeFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a> closeFunc);
<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlParserInputBufferCreateMem">xmlParserInputBufferCreateMem</a>	(const char * mem, <br/>							 int size, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
int	<a href="#xmlIOFTPMatch">xmlIOFTPMatch</a>			(const char * filename);
int	<a href="#xmlRegisterInputCallbacks">xmlRegisterInputCallbacks</a>	(<a href="libxml2-xmlIO.html#xmlInputMatchCallback">xmlInputMatchCallback</a> matchFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlInputOpenCallback">xmlInputOpenCallback</a> openFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> readFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> closeFunc);
void	<a href="#xmlFreeParserInputBuffer">xmlFreeParserInputBuffer</a>	(<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in);
void	<a href="#xmlRegisterDefaultInputCallbacks">xmlRegisterDefaultInputCallbacks</a>	(void);
int	<a href="#xmlParserInputBufferGrow">xmlParserInputBufferGrow</a>	(<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in, <br/>					 int len);
typedef int <a href="#xmlOutputCloseCallback">xmlOutputCloseCallback</a>		(void * context);
<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	<a href="#xmlAllocOutputBuffer">xmlAllocOutputBuffer</a>	(<a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder);
<a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	<a href="#xmlNoNetExternalEntityLoader">xmlNoNetExternalEntityLoader</a>	(const char * URL, <br/>							 const char * ID, <br/>							 <a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt);
<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	<a href="#xmlOutputBufferCreateBuffer">xmlOutputBufferCreateBuffer</a>	(<a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> buffer, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder);
int	<a href="#xmlIOHTTPMatch">xmlIOHTTPMatch</a>			(const char * filename);
void *	<a href="#xmlIOHTTPOpen">xmlIOHTTPOpen</a>			(const char * filename);
<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlParserInputBufferCreateIO">xmlParserInputBufferCreateIO</a>	(<a href="libxml2-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br/>							 <a href="libxml2-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br/>							 void * ioctx, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	<a href="#xmlOutputBufferCreateFd">xmlOutputBufferCreateFd</a>	(int fd, <br/>						 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder);
<a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlNormalizeWindowsPath">xmlNormalizeWindowsPath</a>	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * path);
typedef int <a href="#xmlInputReadCallback">xmlInputReadCallback</a>		(void * context, <br/>					 char * buffer, <br/>					 int len);
<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlParserInputBufferCreateStatic">xmlParserInputBufferCreateStatic</a>	(const char * mem, <br/>							 int size, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	<a href="#xmlOutputBufferGetContent">xmlOutputBufferGetContent</a>	(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out);
int	<a href="#xmlIOHTTPClose">xmlIOHTTPClose</a>			(void * context);
int	<a href="#xmlOutputBufferWriteEscape">xmlOutputBufferWriteEscape</a>	(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escaping);
<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	<a href="#xmlOutputBufferCreateFilename">xmlOutputBufferCreateFilename</a>	(const char * URI, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder, <br/>							 int compression);
size_t	<a href="#xmlOutputBufferGetSize">xmlOutputBufferGetSize</a>		(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out);
void	<a href="#xmlCleanupOutputCallbacks">xmlCleanupOutputCallbacks</a>	(void);
typedef void * <a href="#xmlInputOpenCallback">xmlInputOpenCallback</a>		(char const * filename);
int	<a href="#xmlParserInputBufferRead">xmlParserInputBufferRead</a>	(<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in, <br/>					 int len);
int	<a href="#xmlOutputBufferWriteString">xmlOutputBufferWriteString</a>	(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out, <br/>					 const char * str);
int	<a href="#xmlFileMatch">xmlFileMatch</a>			(const char * filename);
int	<a href="#xmlPopInputCallbacks">xmlPopInputCallbacks</a>		(void);
int	<a href="#xmlFileRead">xmlFileRead</a>			(void * context, <br/>					 char * buffer, <br/>					 int len);
<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlParserInputBufferCreateFile">xmlParserInputBufferCreateFile</a>	(FILE * file, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
char *	<a href="#xmlParserGetDirectory">xmlParserGetDirectory</a>		(const char * filename);
int	<a href="#xmlOutputBufferWrite">xmlOutputBufferWrite</a>		(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out, <br/>					 int len, <br/>					 const char * buf);
void	<a href="#xmlCleanupInputCallbacks">xmlCleanupInputCallbacks</a>	(void);
typedef int <a href="#xmlInputCloseCallback">xmlInputCloseCallback</a>		(void * context);
void *	<a href="#xmlIOHTTPOpenW">xmlIOHTTPOpenW</a>			(const char * post_uri, <br/>					 int compression);
<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	<a href="#xmlParserInputBufferCreateFd">xmlParserInputBufferCreateFd</a>	(int fd, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlInputCloseCallback"/>Function type xmlInputCloseCallback</h3><pre class="programlisting">int	xmlInputCloseCallback		(void * context)<br/>
</pre><p>Callback used in the I/O Input API to close the resource</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>an Input context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlInputMatchCallback"/>Function type xmlInputMatchCallback</h3><pre class="programlisting">int	xmlInputMatchCallback		(char const * filename)<br/>
</pre><p>Callback used in the I/O Input API to detect if the current handler can provide input fonctionnalities for this resource.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename or URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if yes and 0 if another Input module should be used</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlInputOpenCallback"/>Function type xmlInputOpenCallback</h3><pre class="programlisting">void *	xmlInputOpenCallback		(char const * filename)<br/>
</pre><p>Callback used in the I/O Input API to open the resource</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename or URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an Input context or NULL in case or error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlInputReadCallback"/>Function type xmlInputReadCallback</h3><pre class="programlisting">int	xmlInputReadCallback		(void * context, <br/>					 char * buffer, <br/>					 int len)<br/>
</pre><p>Callback used in the I/O Input API to read the resource</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>an Input context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>the buffer to store data read</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of the buffer in bytes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes read or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputCloseCallback"/>Function type xmlOutputCloseCallback</h3><pre class="programlisting">int	xmlOutputCloseCallback		(void * context)<br/>
</pre><p>Callback used in the I/O Output API to close the resource</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>an Output context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputMatchCallback"/>Function type xmlOutputMatchCallback</h3><pre class="programlisting">int	xmlOutputMatchCallback		(char const * filename)<br/>
</pre><p>Callback used in the I/O Output API to detect if the current handler can provide output fonctionnalities for this resource.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename or URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if yes and 0 if another Output module should be used</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputOpenCallback"/>Function type xmlOutputOpenCallback</h3><pre class="programlisting">void *	xmlOutputOpenCallback		(char const * filename)<br/>
</pre><p>Callback used in the I/O Output API to open the resource</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename or URI</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an Output context or NULL in case or error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputWriteCallback"/>Function type xmlOutputWriteCallback</h3><pre class="programlisting">int	xmlOutputWriteCallback		(void * context, <br/>					 const char * buffer, <br/>					 int len)<br/>
</pre><p>Callback used in the I/O Output API to write to the resource</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>an Output context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>the buffer of data to write</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the length of the buffer in bytes</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlAllocOutputBuffer"/>xmlAllocOutputBuffer ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	xmlAllocOutputBuffer	(<a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder)<br/>
</pre><p>Create a buffered parser output</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>encoder</tt></i>:</span></td><td>the encoding converter or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser output or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlAllocParserInputBuffer"/>xmlAllocParserInputBuffer ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlAllocParserInputBuffer	(<a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Create a buffered parser input for progressive parsing</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser input or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCheckFilename"/>xmlCheckFilename ()</h3><pre class="programlisting">int	xmlCheckFilename		(const char * path)<br/>
</pre><p>function checks to see if @path is a valid source (file, socket...) for XML. if stat is not available on the target machine,</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>path</tt></i>:</span></td><td>the path to check</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1. if stat fails, returns 0 (if calling stat on the filename fails, it can't be right). if stat succeeds and the file is a directory, returns 2. otherwise returns 1.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCheckHTTPInput"/>xmlCheckHTTPInput ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlCheckHTTPInput	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>						 <a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a> ret)<br/>
</pre><p>Check an input in case it was created from an HTTP stream, in that case it will handle encoding and update of the base URL in case of redirection. It also checks for HTTP errors in which case the input is cleanly freed up and an appropriate error is raised in context</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>an XML parser context</td></tr><tr><td><span class="term"><i><tt>ret</tt></i>:</span></td><td>an XML parser input</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the input or NULL in case of HTTP error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCleanupInputCallbacks"/>xmlCleanupInputCallbacks ()</h3><pre class="programlisting">void	xmlCleanupInputCallbacks	(void)<br/>
</pre><p>clears the entire input callback table. this includes the compiled-in I/O.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCleanupOutputCallbacks"/>xmlCleanupOutputCallbacks ()</h3><pre class="programlisting">void	xmlCleanupOutputCallbacks	(void)<br/>
</pre><p>clears the entire output callback table. this includes the compiled-in I/O callbacks.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFileClose"/>xmlFileClose ()</h3><pre class="programlisting">int	xmlFileClose			(void * context)<br/>
</pre><p>Close an I/O channel</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>the I/O context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFileMatch"/>xmlFileMatch ()</h3><pre class="programlisting">int	xmlFileMatch			(const char * filename)<br/>
</pre><p>input from FILE *</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the URI for matching</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if matches, 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFileOpen"/>xmlFileOpen ()</h3><pre class="programlisting">void *	xmlFileOpen			(const char * filename)<br/>
</pre><p>Wrapper around xmlFileOpen_real that try it with an unescaped version of @filename, if this fails fallback to @filename</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the URI for matching</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a handler or NULL in case or failure</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFileRead"/>xmlFileRead ()</h3><pre class="programlisting">int	xmlFileRead			(void * context, <br/>					 char * buffer, <br/>					 int len)<br/>
</pre><p>Read @len bytes to @buffer from the I/O channel.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>the I/O context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>where to drop data</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>number of bytes to write</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written or &lt; 0 in case of failure</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFreeParserInputBuffer"/>xmlFreeParserInputBuffer ()</h3><pre class="programlisting">void	xmlFreeParserInputBuffer	(<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in)<br/>
</pre><p>Free up the memory used by a buffered parser input</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a buffered parser input</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOFTPClose"/>xmlIOFTPClose ()</h3><pre class="programlisting">int	xmlIOFTPClose			(void * context)<br/>
</pre><p>Close an FTP I/O channel</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>the I/O context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOFTPMatch"/>xmlIOFTPMatch ()</h3><pre class="programlisting">int	xmlIOFTPMatch			(const char * filename)<br/>
</pre><p>check if the URI matches an FTP one</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the URI for matching</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if matches, 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOFTPOpen"/>xmlIOFTPOpen ()</h3><pre class="programlisting">void *	xmlIOFTPOpen			(const char * filename)<br/>
</pre><p>open an FTP I/O channel</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the URI for matching</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an I/O context or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOFTPRead"/>xmlIOFTPRead ()</h3><pre class="programlisting">int	xmlIOFTPRead			(void * context, <br/>					 char * buffer, <br/>					 int len)<br/>
</pre><p>Read @len bytes to @buffer from the I/O channel.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>the I/O context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>where to drop data</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>number of bytes to write</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOHTTPClose"/>xmlIOHTTPClose ()</h3><pre class="programlisting">int	xmlIOHTTPClose			(void * context)<br/>
</pre><p>Close an HTTP I/O channel</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>the I/O context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOHTTPMatch"/>xmlIOHTTPMatch ()</h3><pre class="programlisting">int	xmlIOHTTPMatch			(const char * filename)<br/>
</pre><p>check if the URI matches an HTTP one</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the URI for matching</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>1 if matches, 0 otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOHTTPOpen"/>xmlIOHTTPOpen ()</h3><pre class="programlisting">void *	xmlIOHTTPOpen			(const char * filename)<br/>
</pre><p>open an HTTP I/O channel</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the URI for matching</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an I/O context or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOHTTPOpenW"/>xmlIOHTTPOpenW ()</h3><pre class="programlisting">void *	xmlIOHTTPOpenW			(const char * post_uri, <br/>					 int compression)<br/>
</pre><p>Open a temporary buffer to collect the document for a subsequent HTTP POST request. Non-static as is called from the output buffer creation routine.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>post_uri</tt></i>:</span></td><td>The destination URI for the document</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>The compression desired for the document.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>an I/O context or NULL in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlIOHTTPRead"/>xmlIOHTTPRead ()</h3><pre class="programlisting">int	xmlIOHTTPRead			(void * context, <br/>					 char * buffer, <br/>					 int len)<br/>
</pre><p>Read @len bytes to @buffer from the I/O channel.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>context</tt></i>:</span></td><td>the I/O context</td></tr><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>where to drop data</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>number of bytes to write</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNoNetExternalEntityLoader"/>xmlNoNetExternalEntityLoader ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputPtr">xmlParserInputPtr</a>	xmlNoNetExternalEntityLoader	(const char * URL, <br/>							 const char * ID, <br/>							 <a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt)<br/>
</pre><p>A specific entity loader disabling network accesses, though still allowing local catalog accesses for resolution.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>the URL for the entity to load</td></tr><tr><td><span class="term"><i><tt>ID</tt></i>:</span></td><td>the System ID for the entity to load</td></tr><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the context in which the entity is called or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new allocated xmlParserInputPtr, or NULL.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNormalizeWindowsPath"/>xmlNormalizeWindowsPath ()</h3><pre class="programlisting"><a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlNormalizeWindowsPath	(const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * path)<br/>
</pre><p>This function is obsolete. Please see xmlURIFromPath in uri.c for a better solution.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>path</tt></i>:</span></td><td>the input file path</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a canonicalized version of the path</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferClose"/>xmlOutputBufferClose ()</h3><pre class="programlisting">int	xmlOutputBufferClose		(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out)<br/>
</pre><p>flushes and close the output I/O channel and free up all the associated resources</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a buffered output</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferCreateBuffer"/>xmlOutputBufferCreateBuffer ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	xmlOutputBufferCreateBuffer	(<a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> buffer, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder)<br/>
</pre><p>Create a buffered output for the progressive saving to a <a href="libxml2-tree.html#xmlBuffer">xmlBuffer</a></p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>buffer</tt></i>:</span></td><td>a <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a></td></tr><tr><td><span class="term"><i><tt>encoder</tt></i>:</span></td><td>the encoding converter or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser output or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferCreateFd"/>xmlOutputBufferCreateFd ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	xmlOutputBufferCreateFd	(int fd, <br/>						 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder)<br/>
</pre><p>Create a buffered output for the progressive saving to a file descriptor</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>fd</tt></i>:</span></td><td>a file descriptor number</td></tr><tr><td><span class="term"><i><tt>encoder</tt></i>:</span></td><td>the encoding converter or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser output or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferCreateFile"/>xmlOutputBufferCreateFile ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	xmlOutputBufferCreateFile	(FILE * file, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder)<br/>
</pre><p>Create a buffered output for the progressive saving to a FILE * buffered C I/O</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>a FILE*</td></tr><tr><td><span class="term"><i><tt>encoder</tt></i>:</span></td><td>the encoding converter or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser output or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferCreateFilename"/>xmlOutputBufferCreateFilename ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	xmlOutputBufferCreateFilename	(const char * URI, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder, <br/>							 int compression)<br/>
</pre><p>Create a buffered output for the progressive saving of a file If filename is "-' then we use stdout as the output. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time. TODO: currently if compression is set, the library only support writing to a local file.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>a C string containing the URI or filename</td></tr><tr><td><span class="term"><i><tt>encoder</tt></i>:</span></td><td>the encoding converter or NULL</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>the compression ration (0 none, 9 max).</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new output or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferCreateIO"/>xmlOutputBufferCreateIO ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a>	xmlOutputBufferCreateIO	(<a href="libxml2-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a> iowrite, <br/>						 <a href="libxml2-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a> ioclose, <br/>						 void * ioctx, <br/>						 <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> encoder)<br/>
</pre><p>Create a buffered output for the progressive saving to an I/O handler</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>iowrite</tt></i>:</span></td><td>an I/O write function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>encoder</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser output or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferFlush"/>xmlOutputBufferFlush ()</h3><pre class="programlisting">int	xmlOutputBufferFlush		(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out)<br/>
</pre><p>flushes the output I/O channel</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a buffered output</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferGetContent"/>xmlOutputBufferGetContent ()</h3><pre class="programlisting">const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> *	xmlOutputBufferGetContent	(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out)<br/>
</pre><p>Gives a pointer to the data currently held in the output buffer</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a pointer to the data or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferGetSize"/>xmlOutputBufferGetSize ()</h3><pre class="programlisting">size_t	xmlOutputBufferGetSize		(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out)<br/>
</pre><p>Gives the length of the data currently held in the output buffer</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case or error or no data is held, the size otherwise</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferWrite"/>xmlOutputBufferWrite ()</h3><pre class="programlisting">int	xmlOutputBufferWrite		(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out, <br/>					 int len, <br/>					 const char * buf)<br/>
</pre><p>Write the content of the array in the output I/O buffer This routine handle the I18N transcoding from internal UTF-8 The buffer is lossless, i.e. will store in case of partial or delayed writes.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a buffered parser output</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the size in bytes of the array.</td></tr><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>an char array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of chars immediately written, or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferWriteEscape"/>xmlOutputBufferWriteEscape ()</h3><pre class="programlisting">int	xmlOutputBufferWriteEscape	(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str, <br/>					 <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> escaping)<br/>
</pre><p>Write the content of the string in the output I/O buffer This routine escapes the caracters and then handle the I18N transcoding from internal UTF-8 The buffer is lossless, i.e. will store in case of partial or delayed writes.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a buffered parser output</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>a zero terminated UTF-8 string</td></tr><tr><td><span class="term"><i><tt>escaping</tt></i>:</span></td><td>an optional escaping function (or NULL)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of chars immediately written, or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlOutputBufferWriteString"/>xmlOutputBufferWriteString ()</h3><pre class="programlisting">int	xmlOutputBufferWriteString	(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out, <br/>					 const char * str)<br/>
</pre><p>Write the content of the string in the output I/O buffer This routine handle the I18N transcoding from internal UTF-8 The buffer is lossless, i.e. will store in case of partial or delayed writes.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a buffered parser output</td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>a zero terminated C string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of chars immediately written, or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserGetDirectory"/>xmlParserGetDirectory ()</h3><pre class="programlisting">char *	xmlParserGetDirectory		(const char * filename)<br/>
</pre><p>lookup the directory for that file</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the path to a file</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>a new allocated string containing the directory, or NULL.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateFd"/>xmlParserInputBufferCreateFd ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlParserInputBufferCreateFd	(int fd, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Create a buffered parser input for the progressive parsing for the input from a file descriptor</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>fd</tt></i>:</span></td><td>a file descriptor number</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser input or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateFile"/>xmlParserInputBufferCreateFile ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlParserInputBufferCreateFile	(FILE * file, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Create a buffered parser input for the progressive parsing of a FILE * buffered C I/O</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>file</tt></i>:</span></td><td>a FILE*</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser input or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateFilename"/>xmlParserInputBufferCreateFilename ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlParserInputBufferCreateFilename	(const char * URI, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Create a buffered parser input for the progressive parsing of a file If filename is "-' then we use stdin as the input. Automatic support for ZLIB/Compress compressed document is provided by default if found at compile-time. Do an encoding check if enc == <a href="libxml2-encoding.html#XML_CHAR_ENCODING_NONE">XML_CHAR_ENCODING_NONE</a></p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URI</tt></i>:</span></td><td>a C string containing the URI or filename</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser input or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateIO"/>xmlParserInputBufferCreateIO ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlParserInputBufferCreateIO	(<a href="libxml2-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> ioread, <br/>							 <a href="libxml2-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> ioclose, <br/>							 void * ioctx, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Create a buffered parser input for the progressive parsing for the input from an I/O handler</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ioread</tt></i>:</span></td><td>an I/O read function</td></tr><tr><td><span class="term"><i><tt>ioclose</tt></i>:</span></td><td>an I/O close function</td></tr><tr><td><span class="term"><i><tt>ioctx</tt></i>:</span></td><td>an I/O handler</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser input or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateMem"/>xmlParserInputBufferCreateMem ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlParserInputBufferCreateMem	(const char * mem, <br/>							 int size, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Create a buffered parser input for the progressive parsing for the input from a memory area.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>the memory input</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the length of the memory block</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser input or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferCreateStatic"/>xmlParserInputBufferCreateStatic ()</h3><pre class="programlisting"><a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a>	xmlParserInputBufferCreateStatic	(const char * mem, <br/>							 int size, <br/>							 <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Create a buffered parser input for the progressive parsing for the input from an immutable memory area. This will not copy the memory area to the buffer, but the memory is expected to be available until the end of the parsing, this is useful for example when using mmap'ed file.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>mem</tt></i>:</span></td><td>the memory input</td></tr><tr><td><span class="term"><i><tt>size</tt></i>:</span></td><td>the length of the memory block</td></tr><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the charset encoding if known</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new parser input or NULL</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferGrow"/>xmlParserInputBufferGrow ()</h3><pre class="programlisting">int	xmlParserInputBufferGrow	(<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in, <br/>					 int len)<br/>
</pre><p>Grow up the content of the input buffer, the old data are preserved This routine handle the I18N transcoding to internal UTF-8 This routine is used when operating the parser in normal (pull) mode TODO: one should be able to remove one extra copy by copying directly onto in-&gt;buffer or in-&gt;raw</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a buffered parser input</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>indicative value of the amount of chars to read</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of chars read and stored in the buffer, or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferPush"/>xmlParserInputBufferPush ()</h3><pre class="programlisting">int	xmlParserInputBufferPush	(<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in, <br/>					 int len, <br/>					 const char * buf)<br/>
</pre><p>Push the content of the arry in the input buffer This routine handle the I18N transcoding to internal UTF-8 This is used when operating the parser in progressive (push) mode.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a buffered parser input</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the size in bytes of the array.</td></tr><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td>an char array</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of chars read and stored in the buffer, or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParserInputBufferRead"/>xmlParserInputBufferRead ()</h3><pre class="programlisting">int	xmlParserInputBufferRead	(<a href="libxml2-tree.html#xmlParserInputBufferPtr">xmlParserInputBufferPtr</a> in, <br/>					 int len)<br/>
</pre><p>Refresh the content of the input buffer, the old data are considered consumed This routine handle the I18N transcoding to internal UTF-8</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a buffered parser input</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>indicative value of the amount of chars to read</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of chars read and stored in the buffer, or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlPopInputCallbacks"/>xmlPopInputCallbacks ()</h3><pre class="programlisting">int	xmlPopInputCallbacks		(void)<br/>
</pre><p>Clear the top input callback from the input stack. this includes the compiled-in I/O.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of input callback registered or -1 in case of error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterDefaultInputCallbacks"/>xmlRegisterDefaultInputCallbacks ()</h3><pre class="programlisting">void	xmlRegisterDefaultInputCallbacks	(void)<br/>
</pre><p>Registers the default compiled-in I/O handlers.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterDefaultOutputCallbacks"/>xmlRegisterDefaultOutputCallbacks ()</h3><pre class="programlisting">void	xmlRegisterDefaultOutputCallbacks	(void)<br/>
</pre><p>Registers the default compiled-in I/O handlers.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterHTTPPostCallbacks"/>xmlRegisterHTTPPostCallbacks ()</h3><pre class="programlisting">void	xmlRegisterHTTPPostCallbacks	(void)<br/>
</pre><p>By default, libxml submits HTTP output requests using the "PUT" method. Calling this method changes the HTTP output method to use the "POST" method instead.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterInputCallbacks"/>xmlRegisterInputCallbacks ()</h3><pre class="programlisting">int	xmlRegisterInputCallbacks	(<a href="libxml2-xmlIO.html#xmlInputMatchCallback">xmlInputMatchCallback</a> matchFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlInputOpenCallback">xmlInputOpenCallback</a> openFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a> readFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a> closeFunc)<br/>
</pre><p>Register a new set of I/O callback for handling parser input.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>matchFunc</tt></i>:</span></td><td>the <a href="libxml2-xmlIO.html#xmlInputMatchCallback">xmlInputMatchCallback</a></td></tr><tr><td><span class="term"><i><tt>openFunc</tt></i>:</span></td><td>the <a href="libxml2-xmlIO.html#xmlInputOpenCallback">xmlInputOpenCallback</a></td></tr><tr><td><span class="term"><i><tt>readFunc</tt></i>:</span></td><td>the <a href="libxml2-xmlIO.html#xmlInputReadCallback">xmlInputReadCallback</a></td></tr><tr><td><span class="term"><i><tt>closeFunc</tt></i>:</span></td><td>the <a href="libxml2-xmlIO.html#xmlInputCloseCallback">xmlInputCloseCallback</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the registered handler number or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterOutputCallbacks"/>xmlRegisterOutputCallbacks ()</h3><pre class="programlisting">int	xmlRegisterOutputCallbacks	(<a href="libxml2-xmlIO.html#xmlOutputMatchCallback">xmlOutputMatchCallback</a> matchFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlOutputOpenCallback">xmlOutputOpenCallback</a> openFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a> writeFunc, <br/>					 <a href="libxml2-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a> closeFunc)<br/>
</pre><p>Register a new set of I/O callback for handling output.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>matchFunc</tt></i>:</span></td><td>the <a href="libxml2-xmlIO.html#xmlOutputMatchCallback">xmlOutputMatchCallback</a></td></tr><tr><td><span class="term"><i><tt>openFunc</tt></i>:</span></td><td>the <a href="libxml2-xmlIO.html#xmlOutputOpenCallback">xmlOutputOpenCallback</a></td></tr><tr><td><span class="term"><i><tt>writeFunc</tt></i>:</span></td><td>the <a href="libxml2-xmlIO.html#xmlOutputWriteCallback">xmlOutputWriteCallback</a></td></tr><tr><td><span class="term"><i><tt>closeFunc</tt></i>:</span></td><td>the <a href="libxml2-xmlIO.html#xmlOutputCloseCallback">xmlOutputCloseCallback</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the registered handler number or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
