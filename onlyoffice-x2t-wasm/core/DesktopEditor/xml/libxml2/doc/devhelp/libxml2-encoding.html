<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>encoding: interface for the encoding conversion functions</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-dict.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-entities.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">encoding</span>
    </h2>
    <p>encoding - interface for the encoding conversion functions</p>
    <p>interface for the encoding conversion functions needed for XML basic encoding and iconv() support.  Related specs are rfc2044        (UTF-8 and UTF-16) F. Yergeau Alis Technologies [ISO-10646]    UTF-8 and UTF-16 in Annexes [ISO-8859-1]   ISO Latin-1 characters codes. [UNICODE]      The Unicode Consortium, "The Unicode Standard -- Worldwide Character Encoding -- Version 1.0", Addison- Wesley, Volume 1, 1991, Volume 2, 1992.  UTF-8 is described in Unicode Technical Report #4. [US-ASCII]     Coded Character Set--7-bit American Standard Code for Information Interchange, ANSI X3.4-1986. </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">typedef struct _uconv_t <a href="#uconv_t">uconv_t</a>;
typedef enum <a href="#xmlCharEncoding">xmlCharEncoding</a>;
typedef struct _xmlCharEncodingHandler <a href="#xmlCharEncodingHandler">xmlCharEncodingHandler</a>;
typedef <a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * <a href="#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>;
int	<a href="#xmlDelEncodingAlias">xmlDelEncodingAlias</a>		(const char * alias);
const char *	<a href="#xmlGetEncodingAlias">xmlGetEncodingAlias</a>	(const char * alias);
void	<a href="#xmlRegisterCharEncodingHandler">xmlRegisterCharEncodingHandler</a>	(<a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler);
int	<a href="#UTF8Toisolat1">UTF8Toisolat1</a>			(unsigned char * out, <br/>					 int * outlen, <br/>					 const unsigned char * in, <br/>					 int * inlen);
void	<a href="#xmlInitCharEncodingHandlers">xmlInitCharEncodingHandlers</a>	(void);
int	<a href="#xmlAddEncodingAlias">xmlAddEncodingAlias</a>		(const char * name, <br/>					 const char * alias);
void	<a href="#xmlCleanupEncodingAliases">xmlCleanupEncodingAliases</a>	(void);
int	<a href="#xmlCharEncOutFunc">xmlCharEncOutFunc</a>		(<a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> in);
<a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a>	<a href="#xmlParseCharEncoding">xmlParseCharEncoding</a>	(const char * name);
typedef int <a href="#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a>	(unsigned char * out, <br/>					 int * outlen, <br/>					 const unsigned char * in, <br/>					 int * inlen);
void	<a href="#xmlCleanupCharEncodingHandlers">xmlCleanupCharEncodingHandlers</a>	(void);
<a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	<a href="#xmlNewCharEncodingHandler">xmlNewCharEncodingHandler</a>	(const char * name, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a> input, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> output);
typedef int <a href="#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a>	(unsigned char * out, <br/>					 int * outlen, <br/>					 const unsigned char * in, <br/>					 int * inlen);
int	<a href="#isolat1ToUTF8">isolat1ToUTF8</a>			(unsigned char * out, <br/>					 int * outlen, <br/>					 const unsigned char * in, <br/>					 int * inlen);
<a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	<a href="#xmlFindCharEncodingHandler">xmlFindCharEncodingHandler</a>	(const char * name);
int	<a href="#xmlCharEncInFunc">xmlCharEncInFunc</a>		(<a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> in);
<a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	<a href="#xmlGetCharEncodingHandler">xmlGetCharEncodingHandler</a>	(<a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
int	<a href="#xmlCharEncFirstLine">xmlCharEncFirstLine</a>		(<a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> in);
<a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a>	<a href="#xmlDetectCharEncoding">xmlDetectCharEncoding</a>	(const unsigned char * in, <br/>					 int len);
int	<a href="#xmlCharEncCloseFunc">xmlCharEncCloseFunc</a>		(<a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler);
const char *	<a href="#xmlGetCharEncodingName">xmlGetCharEncodingName</a>	(<a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="uconv_t">Structure </a>uconv_t</h3><pre class="programlisting">struct _uconv_t {
    UConverter *	uconv	: for conversion between an encoding and UTF-16
    UConverter *	utf8	: for conversion between UTF-8 and UTF-16
} uconv_t;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncoding">Enum </a>xmlCharEncoding</h3><pre class="programlisting">enum <a href="#xmlCharEncoding">xmlCharEncoding</a> {
    <a name="XML_CHAR_ENCODING_ERROR">XML_CHAR_ENCODING_ERROR</a> = -1 /* No char encoding detected */
    <a name="XML_CHAR_ENCODING_NONE">XML_CHAR_ENCODING_NONE</a> = 0 /* No char encoding detected */
    <a name="XML_CHAR_ENCODING_UTF8">XML_CHAR_ENCODING_UTF8</a> = 1 /* UTF-8 */
    <a name="XML_CHAR_ENCODING_UTF16LE">XML_CHAR_ENCODING_UTF16LE</a> = 2 /* UTF-16 little endian */
    <a name="XML_CHAR_ENCODING_UTF16BE">XML_CHAR_ENCODING_UTF16BE</a> = 3 /* UTF-16 big endian */
    <a name="XML_CHAR_ENCODING_UCS4LE">XML_CHAR_ENCODING_UCS4LE</a> = 4 /* UCS-4 little endian */
    <a name="XML_CHAR_ENCODING_UCS4BE">XML_CHAR_ENCODING_UCS4BE</a> = 5 /* UCS-4 big endian */
    <a name="XML_CHAR_ENCODING_EBCDIC">XML_CHAR_ENCODING_EBCDIC</a> = 6 /* EBCDIC uh! */
    <a name="XML_CHAR_ENCODING_UCS4_2143">XML_CHAR_ENCODING_UCS4_2143</a> = 7 /* UCS-4 unusual ordering */
    <a name="XML_CHAR_ENCODING_UCS4_3412">XML_CHAR_ENCODING_UCS4_3412</a> = 8 /* UCS-4 unusual ordering */
    <a name="XML_CHAR_ENCODING_UCS2">XML_CHAR_ENCODING_UCS2</a> = 9 /* UCS-2 */
    <a name="XML_CHAR_ENCODING_8859_1">XML_CHAR_ENCODING_8859_1</a> = 10 /* ISO-8859-1 ISO Latin 1 */
    <a name="XML_CHAR_ENCODING_8859_2">XML_CHAR_ENCODING_8859_2</a> = 11 /* ISO-8859-2 ISO Latin 2 */
    <a name="XML_CHAR_ENCODING_8859_3">XML_CHAR_ENCODING_8859_3</a> = 12 /* ISO-8859-3 */
    <a name="XML_CHAR_ENCODING_8859_4">XML_CHAR_ENCODING_8859_4</a> = 13 /* ISO-8859-4 */
    <a name="XML_CHAR_ENCODING_8859_5">XML_CHAR_ENCODING_8859_5</a> = 14 /* ISO-8859-5 */
    <a name="XML_CHAR_ENCODING_8859_6">XML_CHAR_ENCODING_8859_6</a> = 15 /* ISO-8859-6 */
    <a name="XML_CHAR_ENCODING_8859_7">XML_CHAR_ENCODING_8859_7</a> = 16 /* ISO-8859-7 */
    <a name="XML_CHAR_ENCODING_8859_8">XML_CHAR_ENCODING_8859_8</a> = 17 /* ISO-8859-8 */
    <a name="XML_CHAR_ENCODING_8859_9">XML_CHAR_ENCODING_8859_9</a> = 18 /* ISO-8859-9 */
    <a name="XML_CHAR_ENCODING_2022_JP">XML_CHAR_ENCODING_2022_JP</a> = 19 /* ISO-2022-JP */
    <a name="XML_CHAR_ENCODING_SHIFT_JIS">XML_CHAR_ENCODING_SHIFT_JIS</a> = 20 /* Shift_JIS */
    <a name="XML_CHAR_ENCODING_EUC_JP">XML_CHAR_ENCODING_EUC_JP</a> = 21 /* EUC-JP */
    <a name="XML_CHAR_ENCODING_ASCII">XML_CHAR_ENCODING_ASCII</a> = 22 /*  pure ASCII */
};
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncodingHandler">Structure </a>xmlCharEncodingHandler</h3><pre class="programlisting">struct _xmlCharEncodingHandler {
    char *	name
    <a href="libxml2-encoding.html#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a>	input
    <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a>	output
    iconv_t	iconv_in
    iconv_t	iconv_out
    <a href="libxml2-encoding.html#uconv_t">uconv_t</a> *	uconv_in
    <a href="libxml2-encoding.html#uconv_t">uconv_t</a> *	uconv_out
} xmlCharEncodingHandler;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncodingHandlerPtr">Typedef </a>xmlCharEncodingHandlerPtr</h3><pre class="programlisting"><a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * xmlCharEncodingHandlerPtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncodingInputFunc"/>Function type xmlCharEncodingInputFunc</h3><pre class="programlisting">int	xmlCharEncodingInputFunc	(unsigned char * out, <br/>					 int * outlen, <br/>					 const unsigned char * in, <br/>					 int * inlen)<br/>
</pre><p>Take a block of chars in the original encoding and try to convert it to an UTF-8 block of chars out.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the UTF-8 result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of chars in the original encoding</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written, -1 if lack of space, or -2 if the transcoding failed. The value of @inlen after return is the number of octets consumed if the return value is positive, else unpredictiable. The value of @outlen after return is the number of octets consumed.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncodingOutputFunc"/>Function type xmlCharEncodingOutputFunc</h3><pre class="programlisting">int	xmlCharEncodingOutputFunc	(unsigned char * out, <br/>					 int * outlen, <br/>					 const unsigned char * in, <br/>					 int * inlen)<br/>
</pre><p>Take a block of UTF-8 chars in and try to convert it to another encoding. Note: a first call designed to produce heading info is called with in = NULL. If stateful this should also initialize the encoder state.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of UTF-8 chars</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written, -1 if lack of space, or -2 if the transcoding failed. The value of @inlen after return is the number of octets consumed if the return value is positive, else unpredictiable. The value of @outlen after return is the number of octets produced.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="UTF8Toisolat1"/>UTF8Toisolat1 ()</h3><pre class="programlisting">int	UTF8Toisolat1			(unsigned char * out, <br/>					 int * outlen, <br/>					 const unsigned char * in, <br/>					 int * inlen)<br/>
</pre><p>Take a block of UTF-8 chars in and try to convert it to an ISO Latin 1 block of chars out.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of UTF-8 chars</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written if success, -2 if the transcoding fails, or -1 otherwise The value of @inlen after return is the number of octets consumed if the return value is positive, else unpredictable. The value of @outlen after return is the number of octets consumed.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="isolat1ToUTF8"/>isolat1ToUTF8 ()</h3><pre class="programlisting">int	isolat1ToUTF8			(unsigned char * out, <br/>					 int * outlen, <br/>					 const unsigned char * in, <br/>					 int * inlen)<br/>
</pre><p>Take a block of ISO Latin 1 chars in and try to convert it to an UTF-8 block of chars out.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>a pointer to an array of bytes to store the result</td></tr><tr><td><span class="term"><i><tt>outlen</tt></i>:</span></td><td>the length of @out</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to an array of ISO Latin 1 chars</td></tr><tr><td><span class="term"><i><tt>inlen</tt></i>:</span></td><td>the length of @in</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of bytes written if success, or -1 otherwise The value of @inlen after return is the number of octets consumed if the return value is positive, else unpredictable. The value of @outlen after return is the number of octets consumed.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlAddEncodingAlias"/>xmlAddEncodingAlias ()</h3><pre class="programlisting">int	xmlAddEncodingAlias		(const char * name, <br/>					 const char * alias)<br/>
</pre><p>Registers an alias @alias for an encoding named @name. Existing alias will be overwritten.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the encoding name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>alias</tt></i>:</span></td><td>the alias name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncCloseFunc"/>xmlCharEncCloseFunc ()</h3><pre class="programlisting">int	xmlCharEncCloseFunc		(<a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler)<br/>
</pre><p>Generic front-end for encoding handler close function</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>char enconding transformation data structure</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 if success, or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncFirstLine"/>xmlCharEncFirstLine ()</h3><pre class="programlisting">int	xmlCharEncFirstLine		(<a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)<br/>
</pre><p>Front-end for the encoding handler input function, but handle only the very first line, i.e. limit itself to 45 chars.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>char enconding transformation data structure</td></tr><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlBuffer">xmlBuffer</a> for the output.</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlBuffer">xmlBuffer</a> for the input</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written if success, or -1 general error -2 if the transcoding fails (for *in is not valid utf8 string or the result of transformation can't fit into the encoding we want), or</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncInFunc"/>xmlCharEncInFunc ()</h3><pre class="programlisting">int	xmlCharEncInFunc		(<a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)<br/>
</pre><p>Generic front-end for the encoding handler input function</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>char encoding transformation data structure</td></tr><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlBuffer">xmlBuffer</a> for the output.</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlBuffer">xmlBuffer</a> for the input</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written if success, or -1 general error -2 if the transcoding fails (for *in is not valid utf8 string or the result of transformation can't fit into the encoding we want), or</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCharEncOutFunc"/>xmlCharEncOutFunc ()</h3><pre class="programlisting">int	xmlCharEncOutFunc		(<a href="libxml2-encoding.html#xmlCharEncodingHandler">xmlCharEncodingHandler</a> * handler, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> out, <br/>					 <a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> in)<br/>
</pre><p>Generic front-end for the encoding handler output function a first call with @in == NULL has to be made firs to initiate the output in case of non-stateless encoding needing to initiate their state or the output (like the BOM in UTF16). In case of UTF8 sequence conversion errors for the given encoder, the content will be automatically remapped to a CharRef sequence.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>char enconding transformation data structure</td></tr><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlBuffer">xmlBuffer</a> for the output.</td></tr><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlBuffer">xmlBuffer</a> for the input</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte written if success, or -1 general error -2 if the transcoding fails (for *in is not valid utf8 string or the result of transformation can't fit into the encoding we want), or</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCleanupCharEncodingHandlers"/>xmlCleanupCharEncodingHandlers ()</h3><pre class="programlisting">void	xmlCleanupCharEncodingHandlers	(void)<br/>
</pre><p>Cleanup the memory allocated for the char encoding support, it unregisters all the encoding handlers and the aliases.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlCleanupEncodingAliases"/>xmlCleanupEncodingAliases ()</h3><pre class="programlisting">void	xmlCleanupEncodingAliases	(void)<br/>
</pre><p>Unregisters all aliases</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDelEncodingAlias"/>xmlDelEncodingAlias ()</h3><pre class="programlisting">int	xmlDelEncodingAlias		(const char * alias)<br/>
</pre><p>Unregisters an encoding alias @alias</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>alias</tt></i>:</span></td><td>the alias name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>0 in case of success, -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlDetectCharEncoding"/>xmlDetectCharEncoding ()</h3><pre class="programlisting"><a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a>	xmlDetectCharEncoding	(const unsigned char * in, <br/>					 int len)<br/>
</pre><p>Guess the encoding of the entity using the first bytes of the entity content according to the non-normative appendix F of the XML-1.0 recommendation.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>in</tt></i>:</span></td><td>a pointer to the first bytes of the XML entity, must be at least 2 bytes long (at least 4 if encoding is UTF4 variant).</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>pointer to the length of the buffer</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>one of the XML_CHAR_ENCODING_... values.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFindCharEncodingHandler"/>xmlFindCharEncodingHandler ()</h3><pre class="programlisting"><a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	xmlFindCharEncodingHandler	(const char * name)<br/>
</pre><p>Search in the registered set the handler able to read/write that encoding.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>a string describing the char encoding.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the handler or NULL if not found</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGetCharEncodingHandler"/>xmlGetCharEncodingHandler ()</h3><pre class="programlisting"><a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	xmlGetCharEncodingHandler	(<a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>Search in the registered set the handler able to read/write that encoding.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>an <a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> value.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the handler or NULL if not found</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGetCharEncodingName"/>xmlGetCharEncodingName ()</h3><pre class="programlisting">const char *	xmlGetCharEncodingName	(<a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a> enc)<br/>
</pre><p>The "canonical" name for XML encoding. C.f. http://www.w3.org/TR/REC-xml#charencoding Section 4.3.3 Character Encoding in Entities</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>enc</tt></i>:</span></td><td>the encoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the canonical name for the given encoding</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlGetEncodingAlias"/>xmlGetEncodingAlias ()</h3><pre class="programlisting">const char *	xmlGetEncodingAlias	(const char * alias)<br/>
</pre><p>Lookup an encoding name for the given alias.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>alias</tt></i>:</span></td><td>the alias name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL if not found, otherwise the original name</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlInitCharEncodingHandlers"/>xmlInitCharEncodingHandlers ()</h3><pre class="programlisting">void	xmlInitCharEncodingHandlers	(void)<br/>
</pre><p>Initialize the char encoding support, it registers the default encoding supported. NOTE: while public, this function usually doesn't need to be called in normal processing.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewCharEncodingHandler"/>xmlNewCharEncodingHandler ()</h3><pre class="programlisting"><a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a>	xmlNewCharEncodingHandler	(const char * name, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a> input, <br/>							 <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> output)<br/>
</pre><p>Create and registers an xmlCharEncodingHandler.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the encoding name, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the <a href="libxml2-encoding.html#xmlCharEncodingInputFunc">xmlCharEncodingInputFunc</a> to read that encoding</td></tr><tr><td><span class="term"><i><tt>output</tt></i>:</span></td><td>the <a href="libxml2-encoding.html#xmlCharEncodingOutputFunc">xmlCharEncodingOutputFunc</a> to write that encoding</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> created (or NULL in case of error).</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlParseCharEncoding"/>xmlParseCharEncoding ()</h3><pre class="programlisting"><a href="libxml2-encoding.html#xmlCharEncoding">xmlCharEncoding</a>	xmlParseCharEncoding	(const char * name)<br/>
</pre><p>Compare the string to the encoding schemes already known. Note that the comparison is case insensitive accordingly to the section [XML] 4.3.3 Character Encoding in Entities.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the encoding name as parsed, in UTF-8 format (ASCII actually)</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>one of the XML_CHAR_ENCODING_... values or <a href="libxml2-encoding.html#XML_CHAR_ENCODING_NONE">XML_CHAR_ENCODING_NONE</a> if not recognized.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlRegisterCharEncodingHandler"/>xmlRegisterCharEncodingHandler ()</h3><pre class="programlisting">void	xmlRegisterCharEncodingHandler	(<a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler)<br/>
</pre><p>Register the char encoding handler, surprising, isn't it ?</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>handler</tt></i>:</span></td><td>the <a href="libxml2-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> handler block</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
