<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xmlexports: macros for marking symbols as exportable/importable.</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xmlerror.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-xmlmemory.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xmlexports</span>
    </h2>
    <p>xmlexports - macros for marking symbols as exportable/importable.</p>
    <p>macros for marking symbols as exportable/importable. </p>
    <p>Author(s): Igor Zlatovic &lt;<EMAIL>&gt; </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">#define <a href="#_REENTRANT">_REENTRANT</a>;
#define <a href="#XMLCDECL">XMLCDECL</a>;
#define <a href="#XMLPUBVAR">XMLPUBVAR</a>;
#define <a href="#LIBXML_DLL_IMPORT">LIBXML_DLL_IMPORT</a>;
#define <a href="#XMLCALL">XMLCALL</a>;
#define <a href="#XMLPUBFUN">XMLPUBFUN</a>;
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="LIBXML_DLL_IMPORT">Macro </a>LIBXML_DLL_IMPORT</h3><pre class="programlisting">#define <a href="#LIBXML_DLL_IMPORT">LIBXML_DLL_IMPORT</a>;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XMLCALL">Macro </a>XMLCALL</h3><pre class="programlisting">#define <a href="#XMLCALL">XMLCALL</a>;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XMLCDECL">Macro </a>XMLCDECL</h3><pre class="programlisting">#define <a href="#XMLCDECL">XMLCDECL</a>;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XMLPUBFUN">Macro </a>XMLPUBFUN</h3><pre class="programlisting">#define <a href="#XMLPUBFUN">XMLPUBFUN</a>;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="XMLPUBVAR">Macro </a>XMLPUBVAR</h3><pre class="programlisting">#define <a href="#XMLPUBVAR">XMLPUBVAR</a>;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="_REENTRANT">Macro </a>_REENTRANT</h3><pre class="programlisting">#define <a href="#_REENTRANT">_REENTRANT</a>;
</pre><p/>
</div>
        <hr/>
      </div>
    </div>
  </body>
</html>
