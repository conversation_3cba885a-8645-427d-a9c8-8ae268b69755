<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>nanohttp: minimal HTTP implementation</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-nanoftp.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-parser.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">nanohttp</span>
    </h2>
    <p>nanohttp - minimal HTTP implementation</p>
    <p>minimal HTTP implementation allowing to fetch resources like external subset. </p>
    <p>Author(s): Daniel Veillard </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">int	<a href="#xmlNanoHTTPRead">xmlNanoHTTPRead</a>			(void * ctx, <br/>					 void * dest, <br/>					 int len);
int	<a href="#xmlNanoHTTPSave">xmlNanoHTTPSave</a>			(void * ctxt, <br/>					 const char * filename);
const char *	<a href="#xmlNanoHTTPRedir">xmlNanoHTTPRedir</a>	(void * ctx);
const char *	<a href="#xmlNanoHTTPAuthHeader">xmlNanoHTTPAuthHeader</a>	(void * ctx);
int	<a href="#xmlNanoHTTPFetch">xmlNanoHTTPFetch</a>		(const char * URL, <br/>					 const char * filename, <br/>					 char ** contentType);
int	<a href="#xmlNanoHTTPContentLength">xmlNanoHTTPContentLength</a>	(void * ctx);
const char *	<a href="#xmlNanoHTTPMimeType">xmlNanoHTTPMimeType</a>	(void * ctx);
void	<a href="#xmlNanoHTTPClose">xmlNanoHTTPClose</a>		(void * ctx);
void	<a href="#xmlNanoHTTPCleanup">xmlNanoHTTPCleanup</a>		(void);
void *	<a href="#xmlNanoHTTPMethod">xmlNanoHTTPMethod</a>		(const char * URL, <br/>					 const char * method, <br/>					 const char * input, <br/>					 char ** contentType, <br/>					 const char * headers, <br/>					 int ilen);
void	<a href="#xmlNanoHTTPInit">xmlNanoHTTPInit</a>			(void);
void *	<a href="#xmlNanoHTTPOpen">xmlNanoHTTPOpen</a>			(const char * URL, <br/>					 char ** contentType);
void *	<a href="#xmlNanoHTTPOpenRedir">xmlNanoHTTPOpenRedir</a>		(const char * URL, <br/>					 char ** contentType, <br/>					 char ** redir);
void *	<a href="#xmlNanoHTTPMethodRedir">xmlNanoHTTPMethodRedir</a>		(const char * URL, <br/>					 const char * method, <br/>					 const char * input, <br/>					 char ** contentType, <br/>					 char ** redir, <br/>					 const char * headers, <br/>					 int ilen);
void	<a href="#xmlNanoHTTPScanProxy">xmlNanoHTTPScanProxy</a>		(const char * URL);
const char *	<a href="#xmlNanoHTTPEncoding">xmlNanoHTTPEncoding</a>	(void * ctx);
int	<a href="#xmlNanoHTTPReturnCode">xmlNanoHTTPReturnCode</a>		(void * ctx);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPAuthHeader"/>xmlNanoHTTPAuthHeader ()</h3><pre class="programlisting">const char *	xmlNanoHTTPAuthHeader	(void * ctx)<br/>
</pre><p>Get the authentication header of an HTTP context</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the stashed value of the WWW-Authenticate or Proxy-Authenticate header.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPCleanup"/>xmlNanoHTTPCleanup ()</h3><pre class="programlisting">void	xmlNanoHTTPCleanup		(void)<br/>
</pre><p>Cleanup the HTTP protocol layer.</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPClose"/>xmlNanoHTTPClose ()</h3><pre class="programlisting">void	xmlNanoHTTPClose		(void * ctx)<br/>
</pre><p>This function closes an HTTP context, it ends up the connection and free all data related to it.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPContentLength"/>xmlNanoHTTPContentLength ()</h3><pre class="programlisting">int	xmlNanoHTTPContentLength	(void * ctx)<br/>
</pre><p>Provides the specified content length from the HTTP header.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the specified content length from the HTTP header. Note that a value of -1 indicates that the content length element was not included in the response header.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPEncoding"/>xmlNanoHTTPEncoding ()</h3><pre class="programlisting">const char *	xmlNanoHTTPEncoding	(void * ctx)<br/>
</pre><p>Provides the specified encoding if specified in the HTTP headers.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the specified encoding or NULL if not available</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPFetch"/>xmlNanoHTTPFetch ()</h3><pre class="programlisting">int	xmlNanoHTTPFetch		(const char * URL, <br/>					 const char * filename, <br/>					 char ** contentType)<br/>
</pre><p>This function try to fetch the indicated resource via HTTP GET and save it's content in the file.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename where the content should be saved</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>if available the Content-Type information will be returned at that location</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of failure, 0 incase of success. The contentType, if provided must be freed by the caller</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPInit"/>xmlNanoHTTPInit ()</h3><pre class="programlisting">void	xmlNanoHTTPInit			(void)<br/>
</pre><p>Initialize the HTTP protocol layer. Currently it just checks for proxy informations</p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPMethod"/>xmlNanoHTTPMethod ()</h3><pre class="programlisting">void *	xmlNanoHTTPMethod		(const char * URL, <br/>					 const char * method, <br/>					 const char * input, <br/>					 char ** contentType, <br/>					 const char * headers, <br/>					 int ilen)<br/>
</pre><p>This function try to open a connection to the indicated resource via HTTP using the given @method, adding the given extra headers and the input buffer for the request content.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>method</tt></i>:</span></td><td>the HTTP method to use</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the input string if any</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>the Content-Type information IN and OUT</td></tr><tr><td><span class="term"><i><tt>headers</tt></i>:</span></td><td>the extra headers</td></tr><tr><td><span class="term"><i><tt>ilen</tt></i>:</span></td><td>input length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL in case of failure, otherwise a request handler. The contentType, if provided must be freed by the caller</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPMethodRedir"/>xmlNanoHTTPMethodRedir ()</h3><pre class="programlisting">void *	xmlNanoHTTPMethodRedir		(const char * URL, <br/>					 const char * method, <br/>					 const char * input, <br/>					 char ** contentType, <br/>					 char ** redir, <br/>					 const char * headers, <br/>					 int ilen)<br/>
</pre><p>This function try to open a connection to the indicated resource via HTTP using the given @method, adding the given extra headers and the input buffer for the request content.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>method</tt></i>:</span></td><td>the HTTP method to use</td></tr><tr><td><span class="term"><i><tt>input</tt></i>:</span></td><td>the input string if any</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>the Content-Type information IN and OUT</td></tr><tr><td><span class="term"><i><tt>redir</tt></i>:</span></td><td>the redirected URL OUT</td></tr><tr><td><span class="term"><i><tt>headers</tt></i>:</span></td><td>the extra headers</td></tr><tr><td><span class="term"><i><tt>ilen</tt></i>:</span></td><td>input length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL in case of failure, otherwise a request handler. The contentType, or redir, if provided must be freed by the caller</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPMimeType"/>xmlNanoHTTPMimeType ()</h3><pre class="programlisting">const char *	xmlNanoHTTPMimeType	(void * ctx)<br/>
</pre><p>Provides the specified Mime-Type if specified in the HTTP headers.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the specified Mime-Type or NULL if not available</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPOpen"/>xmlNanoHTTPOpen ()</h3><pre class="programlisting">void *	xmlNanoHTTPOpen			(const char * URL, <br/>					 char ** contentType)<br/>
</pre><p>This function try to open a connection to the indicated resource via HTTP GET.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>if available the Content-Type information will be returned at that location</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL in case of failure, otherwise a request handler. The contentType, if provided must be freed by the caller</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPOpenRedir"/>xmlNanoHTTPOpenRedir ()</h3><pre class="programlisting">void *	xmlNanoHTTPOpenRedir		(const char * URL, <br/>					 char ** contentType, <br/>					 char ** redir)<br/>
</pre><p>This function try to open a connection to the indicated resource via HTTP GET.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The URL to load</td></tr><tr><td><span class="term"><i><tt>contentType</tt></i>:</span></td><td>if available the Content-Type information will be returned at that location</td></tr><tr><td><span class="term"><i><tt>redir</tt></i>:</span></td><td>if available the redirected URL will be returned</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>NULL in case of failure, otherwise a request handler. The contentType, if provided must be freed by the caller</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPRead"/>xmlNanoHTTPRead ()</h3><pre class="programlisting">int	xmlNanoHTTPRead			(void * ctx, <br/>					 void * dest, <br/>					 int len)<br/>
</pre><p>This function tries to read @len bytes from the existing HTTP connection and saves them in @dest. This is a blocking call.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>dest</tt></i>:</span></td><td>a buffer</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the buffer length</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the number of byte read. 0 is an indication of an end of connection. -1 indicates a parameter error.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPRedir"/>xmlNanoHTTPRedir ()</h3><pre class="programlisting">const char *	xmlNanoHTTPRedir	(void * ctx)<br/>
</pre><p>Provides the specified redirection URL if available from the HTTP header.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the specified redirection URL or NULL if not redirected.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPReturnCode"/>xmlNanoHTTPReturnCode ()</h3><pre class="programlisting">int	xmlNanoHTTPReturnCode		(void * ctx)<br/>
</pre><p>Get the latest HTTP return code received</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctx</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the HTTP return code for the request.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPSave"/>xmlNanoHTTPSave ()</h3><pre class="programlisting">int	xmlNanoHTTPSave			(void * ctxt, <br/>					 const char * filename)<br/>
</pre><p>This function saves the output of the HTTP transaction to a file It closes and free the context at the end</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td>the HTTP context</td></tr><tr><td><span class="term"><i><tt>filename</tt></i>:</span></td><td>the filename where the content should be saved</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 in case of failure, 0 incase of success.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNanoHTTPScanProxy"/>xmlNanoHTTPScanProxy ()</h3><pre class="programlisting">void	xmlNanoHTTPScanProxy		(const char * URL)<br/>
</pre><p>(Re)Initialize the HTTP Proxy context by parsing the URL and finding the protocol host port it indicates. Should be like http://myproxy/ or http://myproxy:3128/ A NULL URL cleans up proxy informations.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>URL</tt></i>:</span></td><td>The proxy URL used to initialize the proxy context</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
