<?xml version="1.0" encoding="UTF-8"?>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>xmlwriter: text writing API for XML</title>
    <meta name="generator" content="Libxml2 devhelp stylesheet"/>
    <link rel="start" href="index.html" title="libxml2 Reference Manual"/>
    <link rel="up" href="general.html" title="API"/>
    <link rel="stylesheet" href="style.css" type="text/css"/>
    <link rel="chapter" href="general.html" title="API"/>
  </head>
  <body bgcolor="white" text="black" link="#0000FF" vlink="#840084" alink="#0000FF">
    <table class="navigation" width="100%" summary="Navigation header" cellpadding="2" cellspacing="2">
      <tr valign="middle">
        <td>
          <a accesskey="p" href="libxml2-xmlversion.html">
            <img src="left.png" width="24" height="24" border="0" alt="Prev"/>
          </a>
        </td>
        <td>
          <a accesskey="u" href="general.html">
            <img src="up.png" width="24" height="24" border="0" alt="Up"/>
          </a>
        </td>
        <td>
          <a accesskey="h" href="index.html">
            <img src="home.png" width="24" height="24" border="0" alt="Home"/>
          </a>
        </td>
        <td>
          <a accesskey="n" href="libxml2-xpath.html">
            <img src="right.png" width="24" height="24" border="0" alt="Next"/>
          </a>
        </td>
        <th width="100%" align="center">libxml2 Reference Manual</th>
      </tr>
    </table>
    <h2>
      <span class="refentrytitle">xmlwriter</span>
    </h2>
    <p>xmlwriter - text writing API for XML</p>
    <p>text writing API for XML </p>
    <p>Author(s): Alfred Mickautsch &lt;<EMAIL>&gt; </p>
    <div class="refsynopsisdiv">
      <h2>Synopsis</h2>
      <pre class="synopsis">#define <a href="#xmlTextWriterWriteProcessingInstruction">xmlTextWriterWriteProcessingInstruction</a>;
#define <a href="#xmlTextWriterWriteDocType">xmlTextWriterWriteDocType</a>;
typedef struct _xmlTextWriter <a href="#xmlTextWriter">xmlTextWriter</a>;
typedef <a href="libxml2-xmlwriter.html#xmlTextWriter">xmlTextWriter</a> * <a href="#xmlTextWriterPtr">xmlTextWriterPtr</a>;
int	<a href="#xmlTextWriterStartDocument">xmlTextWriterStartDocument</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * version, <br/>					 const char * encoding, <br/>					 const char * standalone);
int	<a href="#xmlTextWriterEndPI">xmlTextWriterEndPI</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterWriteBase64">xmlTextWriterWriteBase64</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * data, <br/>					 int start, <br/>					 int len);
int	<a href="#xmlTextWriterSetIndentString">xmlTextWriterSetIndentString</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str);
int	<a href="#xmlTextWriterStartAttribute">xmlTextWriterStartAttribute</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
int	<a href="#xmlTextWriterEndComment">xmlTextWriterEndComment</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterWriteRawLen">xmlTextWriterWriteRawLen</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content, <br/>					 int len);
int	<a href="#xmlTextWriterWriteDTDExternalEntityContents">xmlTextWriterWriteDTDExternalEntityContents</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ndataid);
int	<a href="#xmlTextWriterWriteVFormatCDATA">xmlTextWriterWriteVFormatCDATA</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 va_list argptr);
int	<a href="#xmlTextWriterStartAttributeNS">xmlTextWriterStartAttributeNS</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI);
<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterPushParser">xmlNewTextWriterPushParser</a>	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>							 int compression);
int	<a href="#xmlTextWriterWriteFormatAttributeNS">xmlTextWriterWriteFormatAttributeNS</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>						 const char * format, <br/>						 ... ...);
int	<a href="#xmlTextWriterWriteDTDEntity">xmlTextWriterWriteDTDEntity</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 int pe, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ndataid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWriteVFormatPI">xmlTextWriterWriteVFormatPI</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target, <br/>					 const char * format, <br/>					 va_list argptr);
int	<a href="#xmlTextWriterWriteBinHex">xmlTextWriterWriteBinHex</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * data, <br/>					 int start, <br/>					 int len);
int	<a href="#xmlTextWriterEndAttribute">xmlTextWriterEndAttribute</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterSetIndent">xmlTextWriterSetIndent</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 int indent);
int	<a href="#xmlTextWriterWriteFormatPI">xmlTextWriterWriteFormatPI</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target, <br/>					 const char * format, <br/>					 ... ...);
int	<a href="#xmlTextWriterEndDocument">xmlTextWriterEndDocument</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterWriteDTDAttlist">xmlTextWriterWriteDTDAttlist</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterStartComment">xmlTextWriterStartComment</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterWriteVFormatDTD">xmlTextWriterWriteVFormatDTD</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>					 const char * format, <br/>					 va_list argptr);
int	<a href="#xmlTextWriterEndCDATA">xmlTextWriterEndCDATA</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterStartElementNS">xmlTextWriterStartElementNS</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI);
int	<a href="#xmlTextWriterEndDTDEntity">xmlTextWriterEndDTDEntity</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriter">xmlNewTextWriter</a>	(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out);
void	<a href="#xmlFreeTextWriter">xmlFreeTextWriter</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterWriteVFormatDTDAttlist">xmlTextWriterWriteVFormatDTDAttlist</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 va_list argptr);
int	<a href="#xmlTextWriterStartPI">xmlTextWriterStartPI</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target);
int	<a href="#xmlTextWriterStartElement">xmlTextWriterStartElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
int	<a href="#xmlTextWriterWriteDTDExternalEntity">xmlTextWriterWriteDTDExternalEntity</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 int pe, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ndataid);
int	<a href="#xmlTextWriterWriteFormatRaw">xmlTextWriterWriteFormatRaw</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 ... ...);
int	<a href="#xmlTextWriterWriteCDATA">xmlTextWriterWriteCDATA</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWriteVFormatDTDInternalEntity">xmlTextWriterWriteVFormatDTDInternalEntity</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>							 int pe, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>							 const char * format, <br/>							 va_list argptr);
int	<a href="#xmlTextWriterWriteVFormatAttribute">xmlTextWriterWriteVFormatAttribute</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 va_list argptr);
int	<a href="#xmlTextWriterEndDTDElement">xmlTextWriterEndDTDElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterEndDTD">xmlTextWriterEndDTD</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterWriteElement">xmlTextWriterWriteElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterEndElement">xmlTextWriterEndElement</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterWriteVFormatComment">xmlTextWriterWriteVFormatComment</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const char * format, <br/>						 va_list argptr);
int	<a href="#xmlTextWriterStartCDATA">xmlTextWriterStartCDATA</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterFilename">xmlNewTextWriterFilename</a>	(const char * uri, <br/>							 int compression);
int	<a href="#xmlTextWriterWriteVFormatElement">xmlTextWriterWriteVFormatElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 va_list argptr);
int	<a href="#xmlTextWriterWriteFormatComment">xmlTextWriterWriteFormatComment</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 ... ...);
int	<a href="#xmlTextWriterWriteAttributeNS">xmlTextWriterWriteAttributeNS</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWritePI">xmlTextWriterWritePI</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWriteFormatDTDInternalEntity">xmlTextWriterWriteFormatDTDInternalEntity</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>							 int pe, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>							 const char * format, <br/>							 ... ...);
int	<a href="#xmlTextWriterWriteVFormatString">xmlTextWriterWriteVFormatString</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 va_list argptr);
int	<a href="#xmlTextWriterWriteDTDInternalEntity">xmlTextWriterWriteDTDInternalEntity</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 int pe, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWriteVFormatElementNS">xmlTextWriterWriteVFormatElementNS</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>						 const char * format, <br/>						 va_list argptr);
int	<a href="#xmlTextWriterWriteDTDNotation">xmlTextWriterWriteDTDNotation</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid);
int	<a href="#xmlTextWriterWriteFormatElement">xmlTextWriterWriteFormatElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const char * format, <br/>					 ... ...);
int	<a href="#xmlTextWriterSetQuoteChar">xmlTextWriterSetQuoteChar</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> quotechar);
int	<a href="#xmlTextWriterWriteString">xmlTextWriterWriteString</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWriteElementNS">xmlTextWriterWriteElementNS</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterFullEndElement">xmlTextWriterFullEndElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterWriteVFormatAttributeNS">xmlTextWriterWriteVFormatAttributeNS</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>						 const char * format, <br/>						 va_list argptr);
int	<a href="#xmlTextWriterFlush">xmlTextWriterFlush</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
int	<a href="#xmlTextWriterStartDTD">xmlTextWriterStartDTD</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid);
int	<a href="#xmlTextWriterWriteAttribute">xmlTextWriterWriteAttribute</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterDoc">xmlNewTextWriterDoc</a>	(<a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> * doc, <br/>						 int compression);
int	<a href="#xmlTextWriterWriteFormatDTDElement">xmlTextWriterWriteFormatDTDElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 ... ...);
int	<a href="#xmlTextWriterEndDTDAttlist">xmlTextWriterEndDTDAttlist</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer);
<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterTree">xmlNewTextWriterTree</a>	(<a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>						 int compression);
<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	<a href="#xmlNewTextWriterMemory">xmlNewTextWriterMemory</a>	(<a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br/>						 int compression);
int	<a href="#xmlTextWriterWriteFormatCDATA">xmlTextWriterWriteFormatCDATA</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 ... ...);
int	<a href="#xmlTextWriterStartDTDAttlist">xmlTextWriterStartDTDAttlist</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
int	<a href="#xmlTextWriterWriteFormatString">xmlTextWriterWriteFormatString</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 ... ...);
int	<a href="#xmlTextWriterWriteComment">xmlTextWriterWriteComment</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWriteVFormatRaw">xmlTextWriterWriteVFormatRaw</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 va_list argptr);
int	<a href="#xmlTextWriterWriteFormatDTD">xmlTextWriterWriteFormatDTD</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>					 const char * format, <br/>					 ... ...);
int	<a href="#xmlTextWriterWriteFormatDTDAttlist">xmlTextWriterWriteFormatDTDAttlist</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 ... ...);
int	<a href="#xmlTextWriterWriteRaw">xmlTextWriterWriteRaw</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWriteDTDElement">xmlTextWriterWriteDTDElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content);
int	<a href="#xmlTextWriterWriteDTD">xmlTextWriterWriteDTD</a>		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * subset);
int	<a href="#xmlTextWriterWriteFormatAttribute">xmlTextWriterWriteFormatAttribute</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 ... ...);
int	<a href="#xmlTextWriterStartDTDEntity">xmlTextWriterStartDTDEntity</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 int pe, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
int	<a href="#xmlTextWriterWriteVFormatDTDElement">xmlTextWriterWriteVFormatDTDElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 va_list argptr);
int	<a href="#xmlTextWriterStartDTDElement">xmlTextWriterStartDTDElement</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name);
int	<a href="#xmlTextWriterWriteFormatElementNS">xmlTextWriterWriteFormatElementNS</a>	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>						 const char * format, <br/>						 ... ...);
</pre>
    </div>
    <div class="refsect1" lang="en">
      <h2>Description</h2>
    </div>
    <div class="refsect1" lang="en">
      <h2>Details</h2>
      <div class="refsect2" lang="en">
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDocType">Macro </a>xmlTextWriterWriteDocType</h3><pre class="programlisting">#define <a href="#xmlTextWriterWriteDocType">xmlTextWriterWriteDocType</a>;
</pre><p>this macro maps to <a href="libxml2-xmlwriter.html#xmlTextWriterWriteDTD">xmlTextWriterWriteDTD</a></p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteProcessingInstruction">Macro </a>xmlTextWriterWriteProcessingInstruction</h3><pre class="programlisting">#define <a href="#xmlTextWriterWriteProcessingInstruction">xmlTextWriterWriteProcessingInstruction</a>;
</pre><p>This macro maps to <a href="libxml2-xmlwriter.html#xmlTextWriterWritePI">xmlTextWriterWritePI</a></p>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriter">Structure </a>xmlTextWriter</h3><pre class="programlisting">struct _xmlTextWriter {
The content of this structure is not made public by the API.
} xmlTextWriter;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterPtr">Typedef </a>xmlTextWriterPtr</h3><pre class="programlisting"><a href="libxml2-xmlwriter.html#xmlTextWriter">xmlTextWriter</a> * xmlTextWriterPtr;
</pre><p/>
</div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlFreeTextWriter"/>xmlFreeTextWriter ()</h3><pre class="programlisting">void	xmlFreeTextWriter		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>Deallocate all the resources associated to the writer</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewTextWriter"/>xmlNewTextWriter ()</h3><pre class="programlisting"><a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriter	(<a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> out)<br/>
</pre><p>Create a new <a href="libxml2-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure using an <a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a> NOTE: the @out parameter will be deallocated when the writer is closed (if the call succeed.)</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>out</tt></i>:</span></td><td>an <a href="libxml2-tree.html#xmlOutputBufferPtr">xmlOutputBufferPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewTextWriterDoc"/>xmlNewTextWriterDoc ()</h3><pre class="programlisting"><a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterDoc	(<a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> * doc, <br/>						 int compression)<br/>
</pre><p>Create a new <a href="libxml2-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @*doc as output</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td>address of a <a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> to hold the new XML document tree</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewTextWriterFilename"/>xmlNewTextWriterFilename ()</h3><pre class="programlisting"><a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterFilename	(const char * uri, <br/>							 int compression)<br/>
</pre><p>Create a new <a href="libxml2-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @uri as output</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>uri</tt></i>:</span></td><td>the URI of the resource for the output</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewTextWriterMemory"/>xmlNewTextWriterMemory ()</h3><pre class="programlisting"><a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterMemory	(<a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a> buf, <br/>						 int compression)<br/>
</pre><p>Create a new <a href="libxml2-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @buf as output TODO: handle compression</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>buf</tt></i>:</span></td><td><a href="libxml2-tree.html#xmlBufferPtr">xmlBufferPtr</a></td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewTextWriterPushParser"/>xmlNewTextWriterPushParser ()</h3><pre class="programlisting"><a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterPushParser	(<a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> ctxt, <br/>							 int compression)<br/>
</pre><p>Create a new <a href="libxml2-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @ctxt as output NOTE: the @ctxt context will be freed with the resulting writer (if the call succeeds). TODO: handle compression</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>ctxt</tt></i>:</span></td><td><a href="libxml2-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> to hold the new XML document tree</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlNewTextWriterTree"/>xmlNewTextWriterTree ()</h3><pre class="programlisting"><a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a>	xmlNewTextWriterTree	(<a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a> doc, <br/>						 <a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> node, <br/>						 int compression)<br/>
</pre><p>Create a new <a href="libxml2-xmlwriter.html#xmlNewTextWriter">xmlNewTextWriter</a> structure with @doc as output starting at @node</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>doc</tt></i>:</span></td><td><a href="libxml2-tree.html#xmlDocPtr">xmlDocPtr</a></td></tr><tr><td><span class="term"><i><tt>node</tt></i>:</span></td><td><a href="libxml2-tree.html#xmlNodePtr">xmlNodePtr</a> or NULL for doc-&gt;children</td></tr><tr><td><span class="term"><i><tt>compression</tt></i>:</span></td><td>compress the output?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the new <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> or NULL in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndAttribute"/>xmlTextWriterEndAttribute ()</h3><pre class="programlisting">int	xmlTextWriterEndAttribute	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End the current xml element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndCDATA"/>xmlTextWriterEndCDATA ()</h3><pre class="programlisting">int	xmlTextWriterEndCDATA		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End an xml CDATA section.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndComment"/>xmlTextWriterEndComment ()</h3><pre class="programlisting">int	xmlTextWriterEndComment		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End the current xml coment.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndDTD"/>xmlTextWriterEndDTD ()</h3><pre class="programlisting">int	xmlTextWriterEndDTD		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End an xml DTD.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndDTDAttlist"/>xmlTextWriterEndDTDAttlist ()</h3><pre class="programlisting">int	xmlTextWriterEndDTDAttlist	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End an xml DTD <a href="libxml2-SAX.html#attribute">attribute</a> list.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndDTDElement"/>xmlTextWriterEndDTDElement ()</h3><pre class="programlisting">int	xmlTextWriterEndDTDElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End an xml DTD element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndDTDEntity"/>xmlTextWriterEndDTDEntity ()</h3><pre class="programlisting">int	xmlTextWriterEndDTDEntity	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End an xml DTD entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndDocument"/>xmlTextWriterEndDocument ()</h3><pre class="programlisting">int	xmlTextWriterEndDocument	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End an xml document. All open elements are closed, and the content is flushed to the output.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndElement"/>xmlTextWriterEndElement ()</h3><pre class="programlisting">int	xmlTextWriterEndElement		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End the current xml element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterEndPI"/>xmlTextWriterEndPI ()</h3><pre class="programlisting">int	xmlTextWriterEndPI		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End the current xml PI.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterFlush"/>xmlTextWriterFlush ()</h3><pre class="programlisting">int	xmlTextWriterFlush		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>Flush the output buffer.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterFullEndElement"/>xmlTextWriterFullEndElement ()</h3><pre class="programlisting">int	xmlTextWriterFullEndElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>End the current xml element. Writes an end tag even if the element is empty</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterSetIndent"/>xmlTextWriterSetIndent ()</h3><pre class="programlisting">int	xmlTextWriterSetIndent		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 int indent)<br/>
</pre><p>Set indentation output. indent = 0 do not indentation. indent &gt; 0 do indentation.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>indent</tt></i>:</span></td><td>do indentation?</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 on error or 0 otherwise.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterSetIndentString"/>xmlTextWriterSetIndentString ()</h3><pre class="programlisting">int	xmlTextWriterSetIndentString	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * str)<br/>
</pre><p>Set string indentation.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>str</tt></i>:</span></td><td>the <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 on error or 0 otherwise.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterSetQuoteChar"/>xmlTextWriterSetQuoteChar ()</h3><pre class="programlisting">int	xmlTextWriterSetQuoteChar	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> quotechar)<br/>
</pre><p>Set the character used for quoting attributes.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>quotechar</tt></i>:</span></td><td>the quote character</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>-1 on error or 0 otherwise.</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartAttribute"/>xmlTextWriterStartAttribute ()</h3><pre class="programlisting">int	xmlTextWriterStartAttribute	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Start an xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartAttributeNS"/>xmlTextWriterStartAttributeNS ()</h3><pre class="programlisting">int	xmlTextWriterStartAttributeNS	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)<br/>
</pre><p>Start an xml <a href="libxml2-SAX.html#attribute">attribute</a> with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix or NULL</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartCDATA"/>xmlTextWriterStartCDATA ()</h3><pre class="programlisting">int	xmlTextWriterStartCDATA		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>Start an xml CDATA section.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartComment"/>xmlTextWriterStartComment ()</h3><pre class="programlisting">int	xmlTextWriterStartComment	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer)<br/>
</pre><p>Start an xml comment.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartDTD"/>xmlTextWriterStartDTD ()</h3><pre class="programlisting">int	xmlTextWriterStartDTD		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid)<br/>
</pre><p>Start an xml DTD.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartDTDAttlist"/>xmlTextWriterStartDTDAttlist ()</h3><pre class="programlisting">int	xmlTextWriterStartDTDAttlist	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Start an xml DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartDTDElement"/>xmlTextWriterStartDTDElement ()</h3><pre class="programlisting">int	xmlTextWriterStartDTDElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Start an xml DTD element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD element</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartDTDEntity"/>xmlTextWriterStartDTDEntity ()</h3><pre class="programlisting">int	xmlTextWriterStartDTDEntity	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 int pe, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Start an xml DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartDocument"/>xmlTextWriterStartDocument ()</h3><pre class="programlisting">int	xmlTextWriterStartDocument	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * version, <br/>					 const char * encoding, <br/>					 const char * standalone)<br/>
</pre><p>Start a new xml document</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>version</tt></i>:</span></td><td>the xml version ("1.0") or NULL for default ("1.0")</td></tr><tr><td><span class="term"><i><tt>encoding</tt></i>:</span></td><td>the encoding or NULL for default</td></tr><tr><td><span class="term"><i><tt>standalone</tt></i>:</span></td><td>"yes" or "no" or NULL for default</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartElement"/>xmlTextWriterStartElement ()</h3><pre class="programlisting">int	xmlTextWriterStartElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name)<br/>
</pre><p>Start an xml element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartElementNS"/>xmlTextWriterStartElementNS ()</h3><pre class="programlisting">int	xmlTextWriterStartElementNS	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI)<br/>
</pre><p>Start an xml element with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix or NULL</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI or NULL</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterStartPI"/>xmlTextWriterStartPI ()</h3><pre class="programlisting">int	xmlTextWriterStartPI		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target)<br/>
</pre><p>Start an xml PI.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>PI target</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteAttribute"/>xmlTextWriterWriteAttribute ()</h3><pre class="programlisting">int	xmlTextWriterWriteAttribute	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write an xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml2-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td><a href="libxml2-SAX.html#attribute">attribute</a> content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteAttributeNS"/>xmlTextWriterWriteAttributeNS ()</h3><pre class="programlisting">int	xmlTextWriterWriteAttributeNS	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write an xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml2-SAX.html#attribute">attribute</a> local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td><a href="libxml2-SAX.html#attribute">attribute</a> content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteBase64"/>xmlTextWriterWriteBase64 ()</h3><pre class="programlisting">int	xmlTextWriterWriteBase64	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * data, <br/>					 int start, <br/>					 int len)<br/>
</pre><p>Write an base64 encoded xml text.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>binary data</td></tr><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the position within the data of the first byte to encode</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of bytes to encode</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteBinHex"/>xmlTextWriterWriteBinHex ()</h3><pre class="programlisting">int	xmlTextWriterWriteBinHex	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * data, <br/>					 int start, <br/>					 int len)<br/>
</pre><p>Write a BinHex encoded xml text.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>data</tt></i>:</span></td><td>binary data</td></tr><tr><td><span class="term"><i><tt>start</tt></i>:</span></td><td>the position within the data of the first byte to encode</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>the number of bytes to encode</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteCDATA"/>xmlTextWriterWriteCDATA ()</h3><pre class="programlisting">int	xmlTextWriterWriteCDATA		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write an xml CDATA.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>CDATA content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteComment"/>xmlTextWriterWriteComment ()</h3><pre class="programlisting">int	xmlTextWriterWriteComment	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write an xml comment.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td><a href="libxml2-SAX.html#comment">comment</a> string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDTD"/>xmlTextWriterWriteDTD ()</h3><pre class="programlisting">int	xmlTextWriterWriteDTD		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * subset)<br/>
</pre><p>Write a DTD.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>subset</tt></i>:</span></td><td>string content of the DTD</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDTDAttlist"/>xmlTextWriterWriteDTDAttlist ()</h3><pre class="programlisting">int	xmlTextWriterWriteDTDAttlist	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write a DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>content of the ATTLIST</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDTDElement"/>xmlTextWriterWriteDTDElement ()</h3><pre class="programlisting">int	xmlTextWriterWriteDTDElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write a DTD element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD element</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>content of the element</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDTDEntity"/>xmlTextWriterWriteDTDEntity ()</h3><pre class="programlisting">int	xmlTextWriterWriteDTDEntity	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 int pe, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ndataid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write a DTD entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>ndataid</tt></i>:</span></td><td>the xml notation name.</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>content of the entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDTDExternalEntity"/>xmlTextWriterWriteDTDExternalEntity ()</h3><pre class="programlisting">int	xmlTextWriterWriteDTDExternalEntity	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 int pe, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ndataid)<br/>
</pre><p>Write a DTD external entity. The entity must have been started with <a href="libxml2-xmlwriter.html#xmlTextWriterStartDTDEntity">xmlTextWriterStartDTDEntity</a></p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>ndataid</tt></i>:</span></td><td>the xml notation name.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDTDExternalEntityContents"/>xmlTextWriterWriteDTDExternalEntityContents ()</h3><pre class="programlisting">int	xmlTextWriterWriteDTDExternalEntityContents	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * ndataid)<br/>
</pre><p>Write the contents of a DTD external entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>ndataid</tt></i>:</span></td><td>the xml notation name.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDTDInternalEntity"/>xmlTextWriterWriteDTDInternalEntity ()</h3><pre class="programlisting">int	xmlTextWriterWriteDTDInternalEntity	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 int pe, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write a DTD internal entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>content of the entity</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteDTDNotation"/>xmlTextWriterWriteDTDNotation ()</h3><pre class="programlisting">int	xmlTextWriterWriteDTDNotation	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid)<br/>
</pre><p>Write a DTD entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the xml notation</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteElement"/>xmlTextWriterWriteElement ()</h3><pre class="programlisting">int	xmlTextWriterWriteElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write an xml element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>element content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteElementNS"/>xmlTextWriterWriteElementNS ()</h3><pre class="programlisting">int	xmlTextWriterWriteElementNS	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write an xml element with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>element content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatAttribute"/>xmlTextWriterWriteFormatAttribute ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatAttribute	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 ... ...)<br/>
</pre><p>Write a formatted xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml2-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatAttributeNS"/>xmlTextWriterWriteFormatAttributeNS ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatAttributeNS	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>						 const char * format, <br/>						 ... ...)<br/>
</pre><p>Write a formatted xml attribute.with namespace support</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml2-SAX.html#attribute">attribute</a> local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatCDATA"/>xmlTextWriterWriteFormatCDATA ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatCDATA	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 ... ...)<br/>
</pre><p>Write a formatted xml CDATA.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatComment"/>xmlTextWriterWriteFormatComment ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatComment	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 ... ...)<br/>
</pre><p>Write an xml comment.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatDTD"/>xmlTextWriterWriteFormatDTD ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatDTD	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>					 const char * format, <br/>					 ... ...)<br/>
</pre><p>Write a DTD with a formatted markup declarations part.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatDTDAttlist"/>xmlTextWriterWriteFormatDTDAttlist ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatDTDAttlist	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 ... ...)<br/>
</pre><p>Write a formatted DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatDTDElement"/>xmlTextWriterWriteFormatDTDElement ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatDTDElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 ... ...)<br/>
</pre><p>Write a formatted DTD element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD element</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatDTDInternalEntity"/>xmlTextWriterWriteFormatDTDInternalEntity ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatDTDInternalEntity	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>							 int pe, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>							 const char * format, <br/>							 ... ...)<br/>
</pre><p>Write a formatted DTD internal entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatElement"/>xmlTextWriterWriteFormatElement ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const char * format, <br/>					 ... ...)<br/>
</pre><p>Write a formatted xml element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatElementNS"/>xmlTextWriterWriteFormatElementNS ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatElementNS	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>						 const char * format, <br/>						 ... ...)<br/>
</pre><p>Write a formatted xml element with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatPI"/>xmlTextWriterWriteFormatPI ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatPI	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target, <br/>					 const char * format, <br/>					 ... ...)<br/>
</pre><p>Write a formatted PI.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>PI target</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatRaw"/>xmlTextWriterWriteFormatRaw ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatRaw	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 ... ...)<br/>
</pre><p>Write a formatted raw xml text.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteFormatString"/>xmlTextWriterWriteFormatString ()</h3><pre class="programlisting">int	xmlTextWriterWriteFormatString	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 ... ...)<br/>
</pre><p>Write a formatted xml text.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>...</tt></i>:</span></td><td>extra parameters for the format</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWritePI"/>xmlTextWriterWritePI ()</h3><pre class="programlisting">int	xmlTextWriterWritePI		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write an xml PI.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>PI target</td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>PI content</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteRaw"/>xmlTextWriterWriteRaw ()</h3><pre class="programlisting">int	xmlTextWriterWriteRaw		(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write a raw xml text.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>text string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteRawLen"/>xmlTextWriterWriteRawLen ()</h3><pre class="programlisting">int	xmlTextWriterWriteRawLen	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content, <br/>					 int len)<br/>
</pre><p>Write an xml text. TODO: what about entities and special chars??</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>text string</td></tr><tr><td><span class="term"><i><tt>len</tt></i>:</span></td><td>length of the text string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteString"/>xmlTextWriterWriteString ()</h3><pre class="programlisting">int	xmlTextWriterWriteString	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * content)<br/>
</pre><p>Write an xml text.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>content</tt></i>:</span></td><td>text string</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatAttribute"/>xmlTextWriterWriteVFormatAttribute ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatAttribute	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 va_list argptr)<br/>
</pre><p>Write a formatted xml attribute.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml2-SAX.html#attribute">attribute</a> name</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatAttributeNS"/>xmlTextWriterWriteVFormatAttributeNS ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatAttributeNS	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>						 const char * format, <br/>						 va_list argptr)<br/>
</pre><p>Write a formatted xml attribute.with namespace support</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td><a href="libxml2-SAX.html#attribute">attribute</a> local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatCDATA"/>xmlTextWriterWriteVFormatCDATA ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatCDATA	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 va_list argptr)<br/>
</pre><p>Write a formatted xml CDATA.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatComment"/>xmlTextWriterWriteVFormatComment ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatComment	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const char * format, <br/>						 va_list argptr)<br/>
</pre><p>Write an xml comment.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatDTD"/>xmlTextWriterWriteVFormatDTD ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatDTD	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * pubid, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * sysid, <br/>					 const char * format, <br/>					 va_list argptr)<br/>
</pre><p>Write a DTD with a formatted markup declarations part.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD</td></tr><tr><td><span class="term"><i><tt>pubid</tt></i>:</span></td><td>the public identifier, which is an alternative to the system identifier</td></tr><tr><td><span class="term"><i><tt>sysid</tt></i>:</span></td><td>the system identifier, which is the URI of the DTD</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatDTDAttlist"/>xmlTextWriterWriteVFormatDTDAttlist ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatDTDAttlist	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 va_list argptr)<br/>
</pre><p>Write a formatted DTD ATTLIST.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD ATTLIST</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatDTDElement"/>xmlTextWriterWriteVFormatDTDElement ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatDTDElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 va_list argptr)<br/>
</pre><p>Write a formatted DTD element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD element</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatDTDInternalEntity"/>xmlTextWriterWriteVFormatDTDInternalEntity ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatDTDInternalEntity	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>							 int pe, <br/>							 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>							 const char * format, <br/>							 va_list argptr)<br/>
</pre><p>Write a formatted DTD internal entity.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>pe</tt></i>:</span></td><td>TRUE if this is a parameter entity, FALSE if not</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>the name of the DTD entity</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatElement"/>xmlTextWriterWriteVFormatElement ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatElement	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const char * format, <br/>						 va_list argptr)<br/>
</pre><p>Write a formatted xml element.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element name</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatElementNS"/>xmlTextWriterWriteVFormatElementNS ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatElementNS	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * prefix, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * name, <br/>						 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * namespaceURI, <br/>						 const char * format, <br/>						 va_list argptr)<br/>
</pre><p>Write a formatted xml element with namespace support.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>prefix</tt></i>:</span></td><td>namespace prefix</td></tr><tr><td><span class="term"><i><tt>name</tt></i>:</span></td><td>element local name</td></tr><tr><td><span class="term"><i><tt>namespaceURI</tt></i>:</span></td><td>namespace URI</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatPI"/>xmlTextWriterWriteVFormatPI ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatPI	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const <a href="libxml2-xmlstring.html#xmlChar">xmlChar</a> * target, <br/>					 const char * format, <br/>					 va_list argptr)<br/>
</pre><p>Write a formatted xml PI.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>target</tt></i>:</span></td><td>PI target</td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatRaw"/>xmlTextWriterWriteVFormatRaw ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatRaw	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 va_list argptr)<br/>
</pre><p>Write a formatted raw xml text.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
        <div class="refsect2" lang="en"><h3><a name="xmlTextWriterWriteVFormatString"/>xmlTextWriterWriteVFormatString ()</h3><pre class="programlisting">int	xmlTextWriterWriteVFormatString	(<a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> writer, <br/>					 const char * format, <br/>					 va_list argptr)<br/>
</pre><p>Write a formatted xml text.</p>
<div class="variablelist"><table border="0"><col align="left"/><tbody><tr><td><span class="term"><i><tt>writer</tt></i>:</span></td><td>the <a href="libxml2-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a></td></tr><tr><td><span class="term"><i><tt>format</tt></i>:</span></td><td>format string (see printf)</td></tr><tr><td><span class="term"><i><tt>argptr</tt></i>:</span></td><td>pointer to the first member of the variable argument list.</td></tr><tr><td><span class="term"><i><tt>Returns</tt></i>:</span></td><td>the bytes written (may be 0 because of buffering) or -1 in case of error</td></tr></tbody></table></div></div>
        <hr/>
      </div>
    </div>
  </body>
</html>
