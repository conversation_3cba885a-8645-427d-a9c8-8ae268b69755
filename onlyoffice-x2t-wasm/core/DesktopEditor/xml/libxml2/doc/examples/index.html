<?xml version="1.0" encoding="ISO-8859-1"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head><meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" /><link rel="SHORTCUT ICON" href="/favicon.ico" /><style type="text/css">
TD {font-family: Verdana,Arial,Helvetica}
BODY {font-family: Verdana,Arial,Helvetica; margin-top: 2em; margin-left: 0em; margin-right: 0em}
H1 {font-family: Verdana,Arial,Helvetica}
H2 {font-family: Verdana,Arial,Helvetica}
H3 {font-family: Verdana,Arial,Helvetica}
A:link, A:visited, A:active { text-decoration: underline }
</style><title>Libxml2 set of examples</title></head><body bgcolor="#8b7765" text="#000000" link="#000000" vlink="#000000"><table border="0" width="100%" cellpadding="5" cellspacing="0" align="center"><tr><td width="120"><a href="http://swpat.ffii.org/"><img src="../epatents.png" alt="Action against software patents" /></a></td><td width="180"><a href="http://www.gnome.org/"><img src="../gnome2.png" alt="Gnome2 Logo" /></a><a href="http://www.w3.org/Status"><img src="../w3c.png" alt="W3C Logo" /></a><a href="http://www.redhat.com/"><img src="../redhat.gif" alt="Red Hat Logo" /></a><div align="left"><a href="http://xmlsoft.org/"><img src="../Libxml2-Logo-180x168.gif" alt="Made with Libxml2 Logo" /></a></div></td><td><table border="0" width="90%" cellpadding="2" cellspacing="0" align="center" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3" bgcolor="#fffacd"><tr><td align="center"><h1></h1><h2>Libxml2 set of examples</h2></td></tr></table></td></tr></table></td></tr></table><table border="0" cellpadding="4" cellspacing="0" width="100%" align="center"><tr><td bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="2" width="100%"><tr><td valign="top" width="200" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Examples Menu</b></center></td></tr><tr><td bgcolor="#fffacd"><form action="../search.php" enctype="application/x-www-form-urlencoded" method="get"><input name="query" type="text" size="20" value="" /><input name="submit" type="submit" value="Search ..." /></form><ul><li><a href="../index.html">Home</a></li><li><a style="font-weight:bold" href="../docs.html">Developer Menu</a></li><li><a style="font-weight:bold" href="../html/index.html">API Menu</a></li><li><a href="#InputOutput">InputOutput Examples</a></li><li><a href="#Parsing">Parsing Examples</a></li><li><a href="#Tree">Tree Examples</a></li><li><a href="#XPath">XPath Examples</a></li><li><a href="#xmlReader">xmlReader Examples</a></li><li><a href="#xmlWriter">xmlWriter Examples</a></li><li><a href="../guidelines.html">XML Guidelines</a></li></ul></td></tr></table><table width="100%" border="0" cellspacing="1" cellpadding="3"><tr><td colspan="1" bgcolor="#eecfa1" align="center"><center><b>Related links</b></center></td></tr><tr><td bgcolor="#fffacd"><ul><li><a href="http://mail.gnome.org/archives/xml/">Mail archive</a></li><li><a href="http://xmlsoft.org/XSLT/">XSLT libxslt</a></li><li><a href="http://phd.cs.unibo.it/gdome2/">DOM gdome2</a></li><li><a href="http://www.aleksey.com/xmlsec/">XML-DSig xmlsec</a></li><li><a href="ftp://xmlsoft.org/">FTP</a></li><li><a href="http://www.zlatkovic.com/projects/libxml/">Windows binaries</a></li><li><a href="http://opencsw.org/packages/libxml2">Solaris binaries</a></li><li><a href="http://www.explain.com.au/oss/libxml2xslt.html">MacOsX binaries</a></li><li><a href="http://lxml.de/">lxml Python bindings</a></li><li><a href="http://cpan.uwinnipeg.ca/dist/XML-LibXML">Perl bindings</a></li><li><a href="http://libxmlplusplus.sourceforge.net/">C++ bindings</a></li><li><a href="http://www.zend.com/php5/articles/php5-xmlphp.php#Heading4">PHP bindings</a></li><li><a href="http://sourceforge.net/projects/libxml2-pas/">Pascal bindings</a></li><li><a href="http://libxml.rubyforge.org/">Ruby bindings</a></li><li><a href="http://tclxml.sourceforge.net/">Tcl bindings</a></li><li><a href="http://bugzilla.gnome.org/buglist.cgi?product=libxml2">Bug Tracker</a></li></ul></td></tr></table></td></tr></table></td><td valign="top" bgcolor="#8b7765"><table border="0" cellspacing="0" cellpadding="1" width="100%"><tr><td><table border="0" cellspacing="0" cellpadding="1" width="100%" bgcolor="#000000"><tr><td><table border="0" cellpadding="3" cellspacing="1" width="100%"><tr><td bgcolor="#fffacd"><p> The examples are stored per section depending on the main focus
    of the example:</p><ul><li><p><a href="#xmlWriter">xmlWriter</a> :</p><ul><li><a href="#testWriter.c">testWriter.c</a>: use various APIs for the xmlWriter</li></ul></li><li><p><a href="#InputOutput">InputOutput</a> :</p><ul><li><a href="#io1.c">io1.c</a>: Example of custom Input/Output</li><li><a href="#io2.c">io2.c</a>: Output to char buffer</li></ul></li><li><p><a href="#Tree">Tree</a> :</p><ul><li><a href="#tree2.c">tree2.c</a>: Creates a tree</li><li><a href="#tree1.c">tree1.c</a>: Navigates a tree to print element names</li></ul></li><li><p><a href="#XPath">XPath</a> :</p><ul><li><a href="#xpath1.c">xpath1.c</a>: Evaluate XPath expression and prints result node set.</li><li><a href="#xpath2.c">xpath2.c</a>: Load a document, locate subelements with XPath, modify said elements and save the resulting document.</li></ul></li><li><p><a href="#Parsing">Parsing</a> :</p><ul><li><a href="#parse3.c">parse3.c</a>: Parse an XML document in memory to a tree and free it</li><li><a href="#parse4.c">parse4.c</a>: Parse an XML document chunk by chunk to a tree and free it</li><li><a href="#parse2.c">parse2.c</a>: Parse and validate an XML file to a tree and free the result</li><li><a href="#parse1.c">parse1.c</a>: Parse an XML file to a tree and free it</li></ul></li><li><p><a href="#xmlReader">xmlReader</a> :</p><ul><li><a href="#reader2.c">reader2.c</a>: Parse and validate an XML file with an xmlReader</li><li><a href="#reader1.c">reader1.c</a>: Parse an XML file with an xmlReader</li><li><a href="#reader3.c">reader3.c</a>: Show how to extract subdocuments with xmlReader</li><li><a href="#reader4.c">reader4.c</a>: Parse multiple XML files reusing an xmlReader</li></ul></li></ul><p> Getting the compilation options and libraries dependancies needed
to generate binaries from the examples is best done on Linux/Unix by using
the xml2-config script which should have been installed as part of <i>make
install</i> step or when installing the libxml2 development package:</p><pre>gcc -o example `xml2-config --cflags` example.c `xml2-config --libs`</pre><h2><a name="InputOutput" id="InputOutput"></a>InputOutput Examples</h2><h3><a name="io1.c" href="io1.c" id="io1.c">io1.c</a>: Example of custom Input/Output</h3><p>Demonstrate the use of xmlRegisterInputCallbacks to build a custom I/O layer, this is used in an XInclude method context to show how dynamic document can be built in a clean way.</p><p>Includes:</p><ul><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li><li><a href="../html/libxml-xmlIO.html">&lt;libxml/xmlIO.h&gt;</a></li><li><a href="../html/libxml-xinclude.html">&lt;libxml/xinclude.h&gt;</a></li><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 105: Type <a href="../html/libxml-tree.html#xmlDocPtr">xmlDocPtr</a> from tree.h</li><li> line 117: Macro <a href="../html/libxml-xmlversion.html#LIBXML_TEST_VERSION">LIBXML_TEST_VERSION</a> from xmlversion.h</li><li> line 117: Function <a href="../html/libxml-xmlIO.html#xmlRegisterInputCallbacks">xmlRegisterInputCallbacks</a> from xmlIO.h</li><li> line 124: Function <a href="../html/libxml-parser.html#xmlReadMemory">xmlReadMemory</a> from parser.h</li><li> line 134: Function <a href="../html/libxml-xinclude.html#xmlXIncludeProcess">xmlXIncludeProcess</a> from xinclude.h</li><li> line 143: Function <a href="../html/libxml-tree.html#xmlDocDump">xmlDocDump</a> from tree.h</li><li> line 149: Function <a href="../html/libxml-tree.html#xmlFreeDoc">xmlFreeDoc</a> from tree.h</li><li> line 154: Function <a href="../html/libxml-parser.html#xmlCleanupParser">xmlCleanupParser</a> from parser.h</li><li> line 158: Function <a href="../html/libxml-xmlmemory.html#xmlMemoryDump">xmlMemoryDump</a> from xmlmemory.h</li></ul><p>Usage:</p><p>io1</p><p>Author: Daniel Veillard</p><h3><a name="io2.c" href="io2.c" id="io2.c">io2.c</a>: Output to char buffer</h3><p>Demonstrate the use of xmlDocDumpMemory to output document to a character buffer</p><p>Includes:</p><ul><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 19: Type <a href="../html/libxml-tree.html#xmlNodePtr">xmlNodePtr</a> from tree.h</li><li> line 20: Type <a href="../html/libxml-tree.html#xmlDocPtr">xmlDocPtr</a> from tree.h</li><li> line 27: Function <a href="../html/libxml-tree.html#xmlNewDoc">xmlNewDoc</a> from tree.h</li><li> line 28: Function <a href="../html/libxml-tree.html#xmlNewNode">xmlNewNode</a> from tree.h</li><li> line 29: Function <a href="../html/libxml-tree.html#xmlNodeSetContent">xmlNodeSetContent</a> from tree.h</li><li> line 30: Function <a href="../html/libxml-tree.html#xmlDocSetRootElement">xmlDocSetRootElement</a> from tree.h</li><li> line 36: Function <a href="../html/libxml-tree.html#xmlDocDumpFormatMemory">xmlDocDumpFormatMemory</a> from tree.h</li></ul><p>Usage:</p><p>io2</p><p>Author: John Fleck</p><h2><a name="Parsing" id="Parsing"></a>Parsing Examples</h2><h3><a name="parse3.c" href="parse3.c" id="parse3.c">parse3.c</a>: Parse an XML document in memory to a tree and free it</h3><p>Demonstrate the use of xmlReadMemory() to read an XML file into a tree and and xmlFreeDoc() to free the resulting tree</p><p>Includes:</p><ul><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 27: Type <a href="../html/libxml-tree.html#xmlDocPtr">xmlDocPtr</a> from tree.h</li><li> line 33: Function <a href="../html/libxml-parser.html#xmlReadMemory">xmlReadMemory</a> from parser.h</li><li> line 38: Function <a href="../html/libxml-tree.html#xmlFreeDoc">xmlFreeDoc</a> from tree.h</li><li> line 49: Macro <a href="../html/libxml-xmlversion.html#LIBXML_TEST_VERSION">LIBXML_TEST_VERSION</a> from xmlversion.h</li><li> line 54: Function <a href="../html/libxml-parser.html#xmlCleanupParser">xmlCleanupParser</a> from parser.h</li><li> line 58: Function <a href="../html/libxml-xmlmemory.html#xmlMemoryDump">xmlMemoryDump</a> from xmlmemory.h</li></ul><p>Usage:</p><p>parse3</p><p>Author: Daniel Veillard</p><h3><a name="parse4.c" href="parse4.c" id="parse4.c">parse4.c</a>: Parse an XML document chunk by chunk to a tree and free it</h3><p>Demonstrate the use of xmlCreatePushParserCtxt() and xmlParseChunk() to read an XML file progressively into a tree and and xmlFreeDoc() to free the resulting tree</p><p>Includes:</p><ul><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 45: Type <a href="../html/libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> from tree.h</li><li> line 47: Type <a href="../html/libxml-tree.html#xmlDocPtr">xmlDocPtr</a> from tree.h</li><li> line 67: Function <a href="../html/libxml-parser.html#xmlCreatePushParserCtxt">xmlCreatePushParserCtxt</a> from parser.h</li><li> line 86: Function <a href="../html/libxml-parser.html#xmlParseChunk">xmlParseChunk</a> from parser.h</li><li> line 94: Function <a href="../html/libxml-parser.html#xmlFreeParserCtxt">xmlFreeParserCtxt</a> from parser.h</li><li> line 103: Function <a href="../html/libxml-tree.html#xmlFreeDoc">xmlFreeDoc</a> from tree.h</li><li> line 120: Macro <a href="../html/libxml-xmlversion.html#LIBXML_TEST_VERSION">LIBXML_TEST_VERSION</a> from xmlversion.h</li><li> line 131: Function <a href="../html/libxml-parser.html#xmlCleanupParser">xmlCleanupParser</a> from parser.h</li><li> line 135: Function <a href="../html/libxml-xmlmemory.html#xmlMemoryDump">xmlMemoryDump</a> from xmlmemory.h</li></ul><p>Usage:</p><p>parse4 test3.xml</p><p>Author: Daniel Veillard</p><h3><a name="parse2.c" href="parse2.c" id="parse2.c">parse2.c</a>: Parse and validate an XML file to a tree and free the result</h3><p>Create a parser context for an XML file, then parse and validate the file, creating a tree, check the validation result and xmlFreeDoc() to free the resulting tree.</p><p>Includes:</p><ul><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 25: Type <a href="../html/libxml-tree.html#xmlParserCtxtPtr">xmlParserCtxtPtr</a> from tree.h</li><li> line 29: Function <a href="../html/libxml-parser.html#xmlNewParserCtxt">xmlNewParserCtxt</a> from parser.h</li><li> line 35: Function <a href="../html/libxml-parser.html#xmlCtxtReadFile">xmlCtxtReadFile</a> from parser.h</li><li> line 47: Function <a href="../html/libxml-parser.html#xmlFreeParserCtxt">xmlFreeParserCtxt</a> from parser.h</li></ul><p>Usage:</p><p>parse2 test2.xml</p><p>Author: Daniel Veillard</p><h3><a name="parse1.c" href="parse1.c" id="parse1.c">parse1.c</a>: Parse an XML file to a tree and free it</h3><p>Demonstrate the use of xmlReadFile() to read an XML file into a tree and and xmlFreeDoc() to free the resulting tree</p><p>Includes:</p><ul><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 26: Function <a href="../html/libxml-parser.html#xmlReadFile">xmlReadFile</a> from parser.h</li></ul><p>Usage:</p><p>parse1 test1.xml</p><p>Author: Daniel Veillard</p><h2><a name="Tree" id="Tree"></a>Tree Examples</h2><h3><a name="tree2.c" href="tree2.c" id="tree2.c">tree2.c</a>: Creates a tree</h3><p>Shows how to create document, nodes and dump it to stdout or file.</p><p>Includes:</p><ul><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 34: Macro <a href="../html/libxml-xmlversion.html#LIBXML_TEST_VERSION">LIBXML_TEST_VERSION</a> from xmlversion.h</li><li> line 39: Function <a href="../html/libxml-tree.html#xmlNewDoc">xmlNewDoc</a> from tree.h</li><li> line 41: Function <a href="../html/libxml-tree.html#xmlDocSetRootElement">xmlDocSetRootElement</a> from tree.h</li><li> line 46: Function <a href="../html/libxml-tree.html#xmlCreateIntSubset">xmlCreateIntSubset</a> from tree.h</li><li> line 74: Function <a href="../html/libxml-tree.html#xmlNewNode">xmlNewNode</a> from tree.h</li><li> line 75: Function <a href="../html/libxml-tree.html#xmlNewText">xmlNewText</a> from tree.h</li><li> line 78: Function <a href="../html/libxml-tree.html#xmlAddChild">xmlAddChild</a> from tree.h</li><li> line 88: Function <a href="../html/libxml-tree.html#xmlNewChild">xmlNewChild</a> from tree.h</li><li> line 89: Function <a href="../html/libxml-tree.html#xmlNewProp">xmlNewProp</a> from tree.h</li><li> line 96: Function <a href="../html/libxml-tree.html#xmlSaveFormatFileEnc">xmlSaveFormatFileEnc</a> from tree.h</li><li> line 99: Function <a href="../html/libxml-tree.html#xmlFreeDoc">xmlFreeDoc</a> from tree.h</li><li> line 105: Function <a href="../html/libxml-parser.html#xmlCleanupParser">xmlCleanupParser</a> from parser.h</li><li> line 110: Function <a href="../html/libxml-xmlmemory.html#xmlMemoryDump">xmlMemoryDump</a> from xmlmemory.h</li></ul><p>Usage:</p><p>tree2 &lt;filename&gt;  -Default output: stdout</p><p>Author: Lucas Brasilino &lt;<EMAIL>&gt;</p><h3><a name="tree1.c" href="tree1.c" id="tree1.c">tree1.c</a>: Navigates a tree to print element names</h3><p>Parse a file to a tree, use xmlDocGetRootElement() to get the root element, then walk the document and print all the element name in document order.</p><p>Includes:</p><ul><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 67: Function <a href="../html/libxml-parser.html#xmlReadFile">xmlReadFile</a> from parser.h</li><li> line 74: Function <a href="../html/libxml-tree.html#xmlDocGetRootElement">xmlDocGetRootElement</a> from tree.h</li></ul><p>Usage:</p><p>tree1 filename_or_URL</p><p>Author: Dodji Seketeli</p><h2><a name="XPath" id="XPath"></a>XPath Examples</h2><h3><a name="xpath1.c" href="xpath1.c" id="xpath1.c">xpath1.c</a>: Evaluate XPath expression and prints result node set.</h3><p>Shows how to evaluate XPath expression and register known namespaces in XPath context.</p><p>Includes:</p><ul><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li><li><a href="../html/libxml-xpath.html">&lt;libxml/xpath.h&gt;</a></li><li><a href="../html/libxml-xpathInternals.html">&lt;libxml/xpathInternals.h&gt;</a></li><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 39: Function <a href="../html/libxml-parser.html#xmlInitParser">xmlInitParser</a> from parser.h</li><li> line 43: Macro <a href="../html/libxml-xmlversion.html#LIBXML_TEST_VERSION">LIBXML_TEST_VERSION</a> from xmlversion.h</li><li> line 49: Function <a href="../html/libxml-parser.html#xmlCleanupParser">xmlCleanupParser</a> from parser.h</li><li> line 54: Function <a href="../html/libxml-xmlmemory.html#xmlMemoryDump">xmlMemoryDump</a> from xmlmemory.h</li><li> line 86: Type <a href="../html/libxml-tree.html#xmlDocPtr">xmlDocPtr</a> from tree.h</li><li> line 87: Type <a href="../html/libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> from xpath.h</li><li> line 88: Type <a href="../html/libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> from xpath.h</li><li> line 94: Function <a href="../html/libxml-parser.html#xmlParseFile">xmlParseFile</a> from parser.h</li><li> line 101: Function <a href="../html/libxml-xpath.html#xmlXPathNewContext">xmlXPathNewContext</a> from xpath.h</li><li> line 117: Function <a href="../html/libxml-xpath.html#xmlXPathEvalExpression">xmlXPathEvalExpression</a> from xpath.h</li><li> line 129: Function <a href="../html/libxml-xpath.html#xmlXPathFreeObject">xmlXPathFreeObject</a> from xpath.h</li><li> line 130: Function <a href="../html/libxml-xpath.html#xmlXPathFreeContext">xmlXPathFreeContext</a> from xpath.h</li><li> line 131: Function <a href="../html/libxml-tree.html#xmlFreeDoc">xmlFreeDoc</a> from tree.h</li><li> line 156: Function <a href="../html/libxml-xmlstring.html#xmlStrdup">xmlStrdup</a> from xmlstring.h</li><li> line 180: Function <a href="../html/libxml-xmlstring.html#xmlStrchr">xmlStrchr</a> from xmlstring.h</li><li> line 186: Function <a href="../html/libxml-xpathInternals.html#xmlXPathRegisterNs">xmlXPathRegisterNs</a> from xpathInternals.h</li><li> line 206: Type <a href="../html/libxml-tree.html#xmlNodePtr">xmlNodePtr</a> from tree.h</li><li> line 218: Type <a href="../html/libxml-tree.html#xmlNsPtr">xmlNsPtr</a> from tree.h</li></ul><p>Usage:</p><p>xpath1 &lt;xml-file&gt; &lt;xpath-expr&gt; [&lt;known-ns-list&gt;]</p><p>Author: Aleksey Sanin</p><h3><a name="xpath2.c" href="xpath2.c" id="xpath2.c">xpath2.c</a>: Load a document, locate subelements with XPath, modify said elements and save the resulting document.</h3><p>Shows how to make a full round-trip from a load/edit/save</p><p>Includes:</p><ul><li><a href="../html/libxml-parser.html">&lt;libxml/parser.h&gt;</a></li><li><a href="../html/libxml-xpath.html">&lt;libxml/xpath.h&gt;</a></li><li><a href="../html/libxml-xpathInternals.html">&lt;libxml/xpathInternals.h&gt;</a></li><li><a href="../html/libxml-tree.html">&lt;libxml/tree.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 41: Function <a href="../html/libxml-parser.html#xmlInitParser">xmlInitParser</a> from parser.h</li><li> line 86: Type <a href="../html/libxml-tree.html#xmlDocPtr">xmlDocPtr</a> from tree.h</li><li> line 87: Type <a href="../html/libxml-xpath.html#xmlXPathContextPtr">xmlXPathContextPtr</a> from xpath.h</li><li> line 88: Type <a href="../html/libxml-xpath.html#xmlXPathObjectPtr">xmlXPathObjectPtr</a> from xpath.h</li><li> line 95: Function <a href="../html/libxml-parser.html#xmlParseFile">xmlParseFile</a> from parser.h</li><li> line 102: Function <a href="../html/libxml-xpath.html#xmlXPathNewContext">xmlXPathNewContext</a> from xpath.h</li><li> line 110: Function <a href="../html/libxml-xpath.html#xmlXPathEvalExpression">xmlXPathEvalExpression</a> from xpath.h</li><li> line 123: Function <a href="../html/libxml-xpath.html#xmlXPathFreeObject">xmlXPathFreeObject</a> from xpath.h</li><li> line 124: Function <a href="../html/libxml-xpath.html#xmlXPathFreeContext">xmlXPathFreeContext</a> from xpath.h</li><li> line 127: Function <a href="../html/libxml-tree.html#xmlDocDump">xmlDocDump</a> from tree.h</li><li> line 131: Function <a href="../html/libxml-tree.html#xmlFreeDoc">xmlFreeDoc</a> from tree.h</li><li> line 162: Function <a href="../html/libxml-tree.html#xmlNodeSetContent">xmlNodeSetContent</a> from tree.h</li></ul><p>Usage:</p><p>xpath2 &lt;xml-file&gt; &lt;xpath-expr&gt; &lt;new-value&gt;</p><p>Author: Aleksey Sanin and Daniel Veillard</p><h2><a name="xmlReader" id="xmlReader"></a>xmlReader Examples</h2><h3><a name="reader2.c" href="reader2.c" id="reader2.c">reader2.c</a>: Parse and validate an XML file with an xmlReader</h3><p>Demonstrate the use of xmlReaderForFile() to parse an XML file validating the content in the process and activating options like entities substitution, and DTD attributes defaulting. (Note that the XMLReader functions require libxml2 version later than 2.6.)</p><p>Includes:</p><ul><li><a href="../html/libxml-xmlreader.html">&lt;libxml/xmlreader.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 30: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderConstName">xmlTextReaderConstName</a> from xmlreader.h</li><li> line 34: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderConstValue">xmlTextReaderConstValue</a> from xmlreader.h</li><li> line 37: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderDepth">xmlTextReaderDepth</a> from xmlreader.h</li><li> line 38: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderNodeType">xmlTextReaderNodeType</a> from xmlreader.h</li><li> line 40: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderIsEmptyElement">xmlTextReaderIsEmptyElement</a> from xmlreader.h</li><li> line 41: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderHasValue">xmlTextReaderHasValue</a> from xmlreader.h</li><li> line 45: Function <a href="../html/libxml-xmlstring.html#xmlStrlen">xmlStrlen</a> from xmlstring.h</li><li> line 60: Type <a href="../html/libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> from xmlreader.h</li><li> line 68: Function <a href="../html/libxml-xmlreader.html#xmlReaderForFile">xmlReaderForFile</a> from xmlreader.h</li><li> line 76: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderRead">xmlTextReaderRead</a> from xmlreader.h</li><li> line 81: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderIsValid">xmlTextReaderIsValid</a> from xmlreader.h</li><li> line 84: Function <a href="../html/libxml-xmlreader.html#xmlFreeTextReader">xmlFreeTextReader</a> from xmlreader.h</li><li> line 104: Macro <a href="../html/libxml-xmlversion.html#LIBXML_TEST_VERSION">LIBXML_TEST_VERSION</a> from xmlversion.h</li><li> line 109: Function <a href="../html/libxml-parser.html#xmlCleanupParser">xmlCleanupParser</a> from parser.h</li><li> line 113: Function <a href="../html/libxml-xmlmemory.html#xmlMemoryDump">xmlMemoryDump</a> from xmlmemory.h</li></ul><p>Usage:</p><p>reader2 &lt;valid_xml_filename&gt;</p><p>Author: Daniel Veillard</p><h3><a name="reader1.c" href="reader1.c" id="reader1.c">reader1.c</a>: Parse an XML file with an xmlReader</h3><p>Demonstrate the use of xmlReaderForFile() to parse an XML file and dump the informations about the nodes found in the process. (Note that the XMLReader functions require libxml2 version later than 2.6.)</p><p>Includes:</p><ul><li><a href="../html/libxml-xmlreader.html">&lt;libxml/xmlreader.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 29: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderConstName">xmlTextReaderConstName</a> from xmlreader.h</li><li> line 33: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderConstValue">xmlTextReaderConstValue</a> from xmlreader.h</li><li> line 36: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderDepth">xmlTextReaderDepth</a> from xmlreader.h</li><li> line 37: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderNodeType">xmlTextReaderNodeType</a> from xmlreader.h</li><li> line 39: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderIsEmptyElement">xmlTextReaderIsEmptyElement</a> from xmlreader.h</li><li> line 40: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderHasValue">xmlTextReaderHasValue</a> from xmlreader.h</li><li> line 44: Function <a href="../html/libxml-xmlstring.html#xmlStrlen">xmlStrlen</a> from xmlstring.h</li><li> line 59: Type <a href="../html/libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> from xmlreader.h</li><li> line 62: Function <a href="../html/libxml-xmlreader.html#xmlReaderForFile">xmlReaderForFile</a> from xmlreader.h</li><li> line 67: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderRead">xmlTextReaderRead</a> from xmlreader.h</li><li> line 69: Function <a href="../html/libxml-xmlreader.html#xmlFreeTextReader">xmlFreeTextReader</a> from xmlreader.h</li></ul><p>Usage:</p><p>reader1 &lt;filename&gt;</p><p>Author: Daniel Veillard</p><h3><a name="reader3.c" href="reader3.c" id="reader3.c">reader3.c</a>: Show how to extract subdocuments with xmlReader</h3><p>Demonstrate the use of xmlTextReaderPreservePattern() to parse an XML file with the xmlReader while collecting only some subparts of the document. (Note that the XMLReader functions require libxml2 version later than 2.6.)</p><p>Includes:</p><ul><li><a href="../html/libxml-xmlreader.html">&lt;libxml/xmlreader.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 32: Type <a href="../html/libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> from xmlreader.h</li><li> line 38: Function <a href="../html/libxml-xmlreader.html#xmlReaderForFile">xmlReaderForFile</a> from xmlreader.h</li><li> line 43: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderPreservePattern">xmlTextReaderPreservePattern</a> from xmlreader.h</li><li> line 52: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderRead">xmlTextReaderRead</a> from xmlreader.h</li><li> line 62: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderCurrentDoc">xmlTextReaderCurrentDoc</a> from xmlreader.h</li><li> line 66: Function <a href="../html/libxml-xmlreader.html#xmlFreeTextReader">xmlFreeTextReader</a> from xmlreader.h</li><li> line 96: Function <a href="../html/libxml-tree.html#xmlDocDump">xmlDocDump</a> from tree.h</li></ul><p>Usage:</p><p>reader3</p><p>Author: Daniel Veillard</p><h3><a name="reader4.c" href="reader4.c" id="reader4.c">reader4.c</a>: Parse multiple XML files reusing an xmlReader</h3><p>Demonstrate the use of xmlReaderForFile() and xmlReaderNewFile to parse XML files while reusing the reader object and parser context.  (Note that the XMLReader functions require libxml2 version later than 2.6.)</p><p>Includes:</p><ul><li><a href="../html/libxml-xmlreader.html">&lt;libxml/xmlreader.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 26: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderRead">xmlTextReaderRead</a> from xmlreader.h</li><li> line 54: Type <a href="../html/libxml-xmlreader.html#xmlTextReaderPtr">xmlTextReaderPtr</a> from xmlreader.h</li><li> line 72: Function <a href="../html/libxml-xmlreader.html#xmlReaderForFile">xmlReaderForFile</a> from xmlreader.h</li><li> line 83: Function <a href="../html/libxml-xmlreader.html#xmlReaderNewFile">xmlReaderNewFile</a> from xmlreader.h</li><li> line 97: Function <a href="../html/libxml-xmlreader.html#xmlTextReaderCurrentDoc">xmlTextReaderCurrentDoc</a> from xmlreader.h</li><li> line 104: Function <a href="../html/libxml-xmlreader.html#xmlFreeTextReader">xmlFreeTextReader</a> from xmlreader.h</li></ul><p>Usage:</p><p>reader4 &lt;filename&gt; [ filename ... ]</p><p>Author: Graham Bennett</p><h2><a name="xmlWriter" id="xmlWriter"></a>xmlWriter Examples</h2><h3><a name="testWriter.c" href="testWriter.c" id="testWriter.c">testWriter.c</a>: use various APIs for the xmlWriter</h3><p>tests a number of APIs for the xmlWriter, especially the various methods to write to a filename, to a memory buffer, to a new document, or to a subtree. It shows how to do encoding string conversions too. The resulting documents are then serialized.</p><p>Includes:</p><ul><li><a href="../html/libxml-encoding.html">&lt;libxml/encoding.h&gt;</a></li><li><a href="../html/libxml-xmlwriter.html">&lt;libxml/xmlwriter.h&gt;</a></li></ul><p>Uses:</p><ul><li> line 76: Function <a href="../html/libxml-xmlwriter.html#xmlNewTextWriterFilename">xmlNewTextWriterFilename</a> from xmlwriter.h</li><li> line 341: Type <a href="../html/libxml-tree.html#xmlBufferPtr">xmlBufferPtr</a> from tree.h</li><li> line 347: Function <a href="../html/libxml-tree.html#xmlBufferCreate">xmlBufferCreate</a> from tree.h</li><li> line 355: Function <a href="../html/libxml-xmlwriter.html#xmlNewTextWriterMemory">xmlNewTextWriterMemory</a> from xmlwriter.h</li><li> line 613: Function <a href="../html/libxml-tree.html#xmlBufferFree">xmlBufferFree</a> from tree.h</li><li> line 632: Function <a href="../html/libxml-xmlwriter.html#xmlNewTextWriterDoc">xmlNewTextWriterDoc</a> from xmlwriter.h</li><li> line 878: Type <a href="../html/libxml-xmlwriter.html#xmlTextWriterPtr">xmlTextWriterPtr</a> from xmlwriter.h</li><li> line 880: Type <a href="../html/libxml-tree.html#xmlNodePtr">xmlNodePtr</a> from tree.h</li><li> line 885: Macro <a href="../html/libxml-parser.html#XML_DEFAULT_VERSION">XML_DEFAULT_VERSION</a> from parser.h</li><li> line 885: Function <a href="../html/libxml-tree.html#xmlNewDoc">xmlNewDoc</a> from tree.h</li><li> line 894: Function <a href="../html/libxml-tree.html#xmlNewDocNode">xmlNewDocNode</a> from tree.h</li><li> line 901: Function <a href="../html/libxml-tree.html#xmlDocSetRootElement">xmlDocSetRootElement</a> from tree.h</li><li> line 904: Function <a href="../html/libxml-xmlwriter.html#xmlNewTextWriterTree">xmlNewTextWriterTree</a> from xmlwriter.h</li><li> line 913: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterStartDocument">xmlTextWriterStartDocument</a> from xmlwriter.h</li><li> line 925: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterWriteComment">xmlTextWriterWriteComment</a> from xmlwriter.h</li><li> line 949: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterWriteAttribute">xmlTextWriterWriteAttribute</a> from xmlwriter.h</li><li> line 959: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterWriteFormatComment">xmlTextWriterWriteFormatComment</a> from xmlwriter.h</li><li> line 1073: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterWriteFormatElement">xmlTextWriterWriteFormatElement</a> from xmlwriter.h</li><li> line 1096: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterStartElement">xmlTextWriterStartElement</a> from xmlwriter.h</li><li> line 1103: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterWriteElement">xmlTextWriterWriteElement</a> from xmlwriter.h</li><li> line 1111: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterEndElement">xmlTextWriterEndElement</a> from xmlwriter.h</li><li> line 1121: Function <a href="../html/libxml-xmlwriter.html#xmlTextWriterEndDocument">xmlTextWriterEndDocument</a> from xmlwriter.h</li><li> line 1127: Function <a href="../html/libxml-xmlwriter.html#xmlFreeTextWriter">xmlFreeTextWriter</a> from xmlwriter.h</li><li> line 1129: Function <a href="../html/libxml-tree.html#xmlSaveFileEnc">xmlSaveFileEnc</a> from tree.h</li><li> line 1151: Type <a href="../html/libxml-encoding.html#xmlCharEncodingHandlerPtr">xmlCharEncodingHandlerPtr</a> from encoding.h</li><li> line 1156: Function <a href="../html/libxml-encoding.html#xmlFindCharEncodingHandler">xmlFindCharEncodingHandler</a> from encoding.h</li></ul><p>Usage:</p><p>testWriter</p><p>Author: Alfred Mickautsch</p><p><a href="../bugs.html">Daniel Veillard</a></p></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></body></html>
