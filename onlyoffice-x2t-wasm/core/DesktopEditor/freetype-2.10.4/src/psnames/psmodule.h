/****************************************************************************
 *
 * psmodule.h
 *
 *   High-level psnames module interface (specification).
 *
 * Copyright (C) 1996-2020 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#ifndef PSMODULE_H_
#define PSMODULE_H_


#include <freetype/ftmodapi.h>


FT_BEGIN_HEADER


  FT_DECLARE_MODULE( psnames_module_class )


FT_END_HEADER

#endif /* PSMODULE_H_ */


/* END */
