/****************************************************************************
 *
 * ttdriver.h
 *
 *   High-level TrueType driver interface (specification).
 *
 * Copyright (C) 1996-2020 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#ifndef TTDRIVER_H_
#define TTDRIVER_H_


#include <freetype/internal/ftdrv.h>


FT_BEGIN_HEADER

  FT_DECLARE_DRIVER( tt_driver_class )

FT_END_HEADER

#endif /* TTDRIVER_H_ */


/* END */
