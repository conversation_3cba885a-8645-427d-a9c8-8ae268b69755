/****************************************************************************
 *
 * cidriver.h
 *
 *   High-level CID driver interface (specification).
 *
 * Copyright (C) 1996-2020 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#ifndef CIDRIVER_H_
#define CIDRIVER_H_


#include <freetype/internal/ftdrv.h>


FT_BEGIN_HEADER

  FT_CALLBACK_TABLE
  const FT_Driver_ClassRec  t1cid_driver_class;

FT_END_HEADER

#endif /* CIDRIVER_H_ */


/* END */
