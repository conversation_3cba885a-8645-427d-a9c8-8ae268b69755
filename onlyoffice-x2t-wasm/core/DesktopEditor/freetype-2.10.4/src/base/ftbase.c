/****************************************************************************
 *
 * ftbase.c
 *
 *   Single object library component (body only).
 *
 * Copyright (C) 1996-2020 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#define  FT_MAKE_OPTION_SINGLE_OBJECT

#include "ftadvanc.c"
#include "ftcalc.c"
#include "ftcolor.c"
#include "ftdbgmem.c"
#include "fterrors.c"
#include "ftfntfmt.c"
#include "ftgloadr.c"
#include "fthash.c"
#include "ftlcdfil.c"
#include "ftmac.c"
#include "ftobjs.c"
#include "ftoutln.c"
#include "ftpsprop.c"
#include "ftrfork.c"
#include "ftsnames.c"
#include "ftstream.c"
#include "fttrigon.c"
#include "ftutil.c"


/* END */
