/****************************************************************************
 *
 * otvmod.h
 *
 *   FreeType's OpenType validation module implementation
 *   (specification).
 *
 * Copyright (C) 2004-2020 by
 * <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#ifndef OTVMOD_H_
#define OTVMOD_H_


#include <freetype/ftmodapi.h>


FT_BEGIN_HEADER


  FT_EXPORT_VAR( const FT_Module_Class )  otv_module_class;


FT_END_HEADER

#endif /* OTVMOD_H_ */


/* END */
