/****************************************************************************
 *
 * gxvalid.c
 *
 *   FreeType validator for TrueTypeGX/AAT tables (body only).
 *
 * Copyright (C) 2005-2020 by
 * <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>,
 <PERSON> <PERSON>, <PERSON>, and <PERSON>.
 *
 * This file is part of the FreeType project, and may only be used,
 * modified, and distributed under the terms of the FreeType project
 * license, LICENSE.TXT.  By continuing to use, modify, or distribute
 * this file you indicate that you have read the license and
 * understand and accept it fully.
 *
 */


#define FT_MAKE_OPTION_SINGLE_OBJECT

#include "gxvbsln.c"
#include "gxvcommn.c"
#include "gxvfeat.c"
#include "gxvjust.c"
#include "gxvkern.c"
#include "gxvlcar.c"
#include "gxvmod.c"
#include "gxvmort.c"
#include "gxvmort0.c"
#include "gxvmort1.c"
#include "gxvmort2.c"
#include "gxvmort4.c"
#include "gxvmort5.c"
#include "gxvmorx.c"
#include "gxvmorx0.c"
#include "gxvmorx1.c"
#include "gxvmorx2.c"
#include "gxvmorx4.c"
#include "gxvmorx5.c"
#include "gxvopbd.c"
#include "gxvprop.c"
#include "gxvtrak.c"


/* END */
