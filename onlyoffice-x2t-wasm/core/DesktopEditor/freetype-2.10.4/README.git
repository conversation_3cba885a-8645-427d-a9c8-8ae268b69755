The git  archive doesn't  contain pre-built configuration  scripts for
UNIXish platforms.  To generate them say

  sh autogen.sh

which in turn depends on the following packages:

  automake (1.10.1)
  libtool (2.2.4)
  autoconf (2.62)

The versions given  in parentheses are known to  work.  Newer versions
should work too, of course.   Note that autogen.sh also sets up proper
file permissions for the `configure' and auxiliary scripts.

The autogen.sh script  now checks the version of  above three packages
whether they match the numbers  above.  Otherwise it will complain and
suggest either upgrading or using  an environment variable to point to
a more recent version of the required tool(s).

Note that  `aclocal' is provided  by the `automake' package  on Linux,
and that `libtoolize' is called `glibtoolize' on Darwin (OS X).


For static builds which  don't use platform specific optimizations, no
configure script is necessary at all; saying

  make setup ansi
  make

should work on all platforms which have GNU make (or makepp).


Similarly, a  build with  `cmake' can  be done  directly from  the git
repository.


----------------------------------------------------------------------

Copyright (C) 2005-2020 by
<PERSON>, <PERSON>, and <PERSON>.

This  file is  part of  the FreeType  project, and  may only  be used,
modified,  and distributed  under the  terms of  the  FreeType project
license,  LICENSE.TXT.  By  continuing to  use, modify,  or distribute
this file you  indicate that you have read  the license and understand
and accept it fully.


--- end of README.git ---
