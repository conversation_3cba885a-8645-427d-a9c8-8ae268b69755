



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>Base Interface - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#base-interface" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                Base Interface
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4" checked>
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        Base Interface
      </label>
    
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link md-nav__link--active">
      Base Interface
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_library" class="md-nav__link">
    FT_Library
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_face" class="md-nav__link">
    FT_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size" class="md-nav__link">
    FT_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_glyphslot" class="md-nav__link">
    FT_GlyphSlot
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_charmap" class="md-nav__link">
    FT_CharMap
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_encoding" class="md-nav__link">
    FT_Encoding
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_enc_tag" class="md-nav__link">
    FT_ENC_TAG
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_facerec" class="md-nav__link">
    FT_FaceRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_horizontal" class="md-nav__link">
    FT_HAS_HORIZONTAL
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_vertical" class="md-nav__link">
    FT_HAS_VERTICAL
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_kerning" class="md-nav__link">
    FT_HAS_KERNING
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_fixed_sizes" class="md-nav__link">
    FT_HAS_FIXED_SIZES
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_glyph_names" class="md-nav__link">
    FT_HAS_GLYPH_NAMES
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_color" class="md-nav__link">
    FT_HAS_COLOR
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_multiple_masters" class="md-nav__link">
    FT_HAS_MULTIPLE_MASTERS
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_sfnt" class="md-nav__link">
    FT_IS_SFNT
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_scalable" class="md-nav__link">
    FT_IS_SCALABLE
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_fixed_width" class="md-nav__link">
    FT_IS_FIXED_WIDTH
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_cid_keyed" class="md-nav__link">
    FT_IS_CID_KEYED
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_tricky" class="md-nav__link">
    FT_IS_TRICKY
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_named_instance" class="md-nav__link">
    FT_IS_NAMED_INSTANCE
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_variation" class="md-nav__link">
    FT_IS_VARIATION
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_sizerec" class="md-nav__link">
    FT_SizeRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_metrics" class="md-nav__link">
    FT_Size_Metrics
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_glyphslotrec" class="md-nav__link">
    FT_GlyphSlotRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_glyph_metrics" class="md-nav__link">
    FT_Glyph_Metrics
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_subglyph" class="md-nav__link">
    FT_SubGlyph
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bitmap_size" class="md-nav__link">
    FT_Bitmap_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_init_freetype" class="md-nav__link">
    FT_Init_FreeType
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_done_freetype" class="md-nav__link">
    FT_Done_FreeType
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_new_face" class="md-nav__link">
    FT_New_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_done_face" class="md-nav__link">
    FT_Done_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_reference_face" class="md-nav__link">
    FT_Reference_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_new_memory_face" class="md-nav__link">
    FT_New_Memory_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_face_properties" class="md-nav__link">
    FT_Face_Properties
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_open_face" class="md-nav__link">
    FT_Open_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_open_args" class="md-nav__link">
    FT_Open_Args
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_parameter" class="md-nav__link">
    FT_Parameter
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_attach_file" class="md-nav__link">
    FT_Attach_File
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_attach_stream" class="md-nav__link">
    FT_Attach_Stream
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_set_char_size" class="md-nav__link">
    FT_Set_Char_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_set_pixel_sizes" class="md-nav__link">
    FT_Set_Pixel_Sizes
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_request_size" class="md-nav__link">
    FT_Request_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_select_size" class="md-nav__link">
    FT_Select_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_request_type" class="md-nav__link">
    FT_Size_Request_Type
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_requestrec" class="md-nav__link">
    FT_Size_RequestRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_request" class="md-nav__link">
    FT_Size_Request
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_set_transform" class="md-nav__link">
    FT_Set_Transform
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_glyph" class="md-nav__link">
    FT_Load_Glyph
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_char_index" class="md-nav__link">
    FT_Get_Char_Index
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_first_char" class="md-nav__link">
    FT_Get_First_Char
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_next_char" class="md-nav__link">
    FT_Get_Next_Char
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_name_index" class="md-nav__link">
    FT_Get_Name_Index
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_char" class="md-nav__link">
    FT_Load_Char
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_target_mode" class="md-nav__link">
    FT_LOAD_TARGET_MODE
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_render_glyph" class="md-nav__link">
    FT_Render_Glyph
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_render_mode" class="md-nav__link">
    FT_Render_Mode
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_kerning" class="md-nav__link">
    FT_Get_Kerning
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_kerning_mode" class="md-nav__link">
    FT_Kerning_Mode
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_track_kerning" class="md-nav__link">
    FT_Get_Track_Kerning
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_glyph_name" class="md-nav__link">
    FT_Get_Glyph_Name
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_postscript_name" class="md-nav__link">
    FT_Get_Postscript_Name
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_charmaprec" class="md-nav__link">
    FT_CharMapRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_select_charmap" class="md-nav__link">
    FT_Select_Charmap
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_set_charmap" class="md-nav__link">
    FT_Set_Charmap
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_charmap_index" class="md-nav__link">
    FT_Get_Charmap_Index
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_fstype_flags" class="md-nav__link">
    FT_Get_FSType_Flags
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_subglyph_info" class="md-nav__link">
    FT_Get_SubGlyph_Info
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_face_internal" class="md-nav__link">
    FT_Face_Internal
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_internal" class="md-nav__link">
    FT_Size_Internal
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_slot_internal" class="md-nav__link">
    FT_Slot_Internal
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_face_flag_xxx" class="md-nav__link">
    FT_FACE_FLAG_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_style_flag_xxx" class="md-nav__link">
    FT_STYLE_FLAG_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_open_xxx" class="md-nav__link">
    FT_OPEN_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_xxx" class="md-nav__link">
    FT_LOAD_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_target_xxx" class="md-nav__link">
    FT_LOAD_TARGET_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_subglyph_flag_xxx" class="md-nav__link">
    FT_SUBGLYPH_FLAG_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_fstype_xxx" class="md-nav__link">
    FT_FSTYPE_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_fast_glyphs" class="md-nav__link">
    FT_HAS_FAST_GLYPHS
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link">
      Glyph Color Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link">
      Header File Macros
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5">
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link">
      Window FNT Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6">
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link">
      Subpixel Rendering
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8">
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link">
      Outline Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link">
      Scanline Converter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link">
      System Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10">
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link">
      TrueTypeGX/AAT Validation
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_library" class="md-nav__link">
    FT_Library
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_face" class="md-nav__link">
    FT_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size" class="md-nav__link">
    FT_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_glyphslot" class="md-nav__link">
    FT_GlyphSlot
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_charmap" class="md-nav__link">
    FT_CharMap
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_encoding" class="md-nav__link">
    FT_Encoding
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_enc_tag" class="md-nav__link">
    FT_ENC_TAG
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_facerec" class="md-nav__link">
    FT_FaceRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_horizontal" class="md-nav__link">
    FT_HAS_HORIZONTAL
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_vertical" class="md-nav__link">
    FT_HAS_VERTICAL
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_kerning" class="md-nav__link">
    FT_HAS_KERNING
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_fixed_sizes" class="md-nav__link">
    FT_HAS_FIXED_SIZES
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_glyph_names" class="md-nav__link">
    FT_HAS_GLYPH_NAMES
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_color" class="md-nav__link">
    FT_HAS_COLOR
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_multiple_masters" class="md-nav__link">
    FT_HAS_MULTIPLE_MASTERS
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_sfnt" class="md-nav__link">
    FT_IS_SFNT
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_scalable" class="md-nav__link">
    FT_IS_SCALABLE
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_fixed_width" class="md-nav__link">
    FT_IS_FIXED_WIDTH
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_cid_keyed" class="md-nav__link">
    FT_IS_CID_KEYED
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_tricky" class="md-nav__link">
    FT_IS_TRICKY
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_named_instance" class="md-nav__link">
    FT_IS_NAMED_INSTANCE
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_is_variation" class="md-nav__link">
    FT_IS_VARIATION
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_sizerec" class="md-nav__link">
    FT_SizeRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_metrics" class="md-nav__link">
    FT_Size_Metrics
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_glyphslotrec" class="md-nav__link">
    FT_GlyphSlotRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_glyph_metrics" class="md-nav__link">
    FT_Glyph_Metrics
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_subglyph" class="md-nav__link">
    FT_SubGlyph
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bitmap_size" class="md-nav__link">
    FT_Bitmap_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_init_freetype" class="md-nav__link">
    FT_Init_FreeType
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_done_freetype" class="md-nav__link">
    FT_Done_FreeType
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_new_face" class="md-nav__link">
    FT_New_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_done_face" class="md-nav__link">
    FT_Done_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_reference_face" class="md-nav__link">
    FT_Reference_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_new_memory_face" class="md-nav__link">
    FT_New_Memory_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_face_properties" class="md-nav__link">
    FT_Face_Properties
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_open_face" class="md-nav__link">
    FT_Open_Face
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_open_args" class="md-nav__link">
    FT_Open_Args
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_parameter" class="md-nav__link">
    FT_Parameter
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_attach_file" class="md-nav__link">
    FT_Attach_File
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_attach_stream" class="md-nav__link">
    FT_Attach_Stream
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_set_char_size" class="md-nav__link">
    FT_Set_Char_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_set_pixel_sizes" class="md-nav__link">
    FT_Set_Pixel_Sizes
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_request_size" class="md-nav__link">
    FT_Request_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_select_size" class="md-nav__link">
    FT_Select_Size
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_request_type" class="md-nav__link">
    FT_Size_Request_Type
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_requestrec" class="md-nav__link">
    FT_Size_RequestRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_request" class="md-nav__link">
    FT_Size_Request
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_set_transform" class="md-nav__link">
    FT_Set_Transform
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_glyph" class="md-nav__link">
    FT_Load_Glyph
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_char_index" class="md-nav__link">
    FT_Get_Char_Index
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_first_char" class="md-nav__link">
    FT_Get_First_Char
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_next_char" class="md-nav__link">
    FT_Get_Next_Char
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_name_index" class="md-nav__link">
    FT_Get_Name_Index
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_char" class="md-nav__link">
    FT_Load_Char
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_target_mode" class="md-nav__link">
    FT_LOAD_TARGET_MODE
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_render_glyph" class="md-nav__link">
    FT_Render_Glyph
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_render_mode" class="md-nav__link">
    FT_Render_Mode
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_kerning" class="md-nav__link">
    FT_Get_Kerning
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_kerning_mode" class="md-nav__link">
    FT_Kerning_Mode
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_track_kerning" class="md-nav__link">
    FT_Get_Track_Kerning
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_glyph_name" class="md-nav__link">
    FT_Get_Glyph_Name
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_postscript_name" class="md-nav__link">
    FT_Get_Postscript_Name
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_charmaprec" class="md-nav__link">
    FT_CharMapRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_select_charmap" class="md-nav__link">
    FT_Select_Charmap
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_set_charmap" class="md-nav__link">
    FT_Set_Charmap
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_charmap_index" class="md-nav__link">
    FT_Get_Charmap_Index
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_fstype_flags" class="md-nav__link">
    FT_Get_FSType_Flags
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_subglyph_info" class="md-nav__link">
    FT_Get_SubGlyph_Info
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_face_internal" class="md-nav__link">
    FT_Face_Internal
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_size_internal" class="md-nav__link">
    FT_Size_Internal
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_slot_internal" class="md-nav__link">
    FT_Slot_Internal
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_face_flag_xxx" class="md-nav__link">
    FT_FACE_FLAG_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_style_flag_xxx" class="md-nav__link">
    FT_STYLE_FLAG_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_open_xxx" class="md-nav__link">
    FT_OPEN_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_xxx" class="md-nav__link">
    FT_LOAD_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_load_target_xxx" class="md-nav__link">
    FT_LOAD_TARGET_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_subglyph_flag_xxx" class="md-nav__link">
    FT_SUBGLYPH_FLAG_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_fstype_xxx" class="md-nav__link">
    FT_FSTYPE_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_has_fast_glyphs" class="md-nav__link">
    FT_HAS_FAST_GLYPHS
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#core-api">Core API</a> &raquo; Base Interface</p>
<hr />
<h1 id="base-interface">Base Interface<a class="headerlink" href="#base-interface" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>This section describes the most important public high-level API functions of FreeType&nbsp;2.</p>
<h2 id="ft_library">FT_Library<a class="headerlink" href="#ft_library" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_LibraryRec_  *<b>FT_Library</b>;
</code></pre></div>

<p>A handle to a FreeType library instance. Each &lsquo;library&rsquo; is completely independent from the others; it is the &lsquo;root&rsquo; of a set of objects like fonts, faces, sizes, etc.</p>
<p>It also embeds a memory manager (see <code><a href="ft2-system_interface.html#ft_memory">FT_Memory</a></code>), as well as a scan-line converter object (see <code><a href="ft2-raster.html#ft_raster">FT_Raster</a></code>).</p>
<p>[Since 2.5.6] In multi-threaded applications it is easiest to use one <code>FT_Library</code> object per thread. In case this is too cumbersome, a single <code>FT_Library</code> object across threads is possible also, as long as a mutex lock is used around <code><a href="ft2-base_interface.html#ft_new_face">FT_New_Face</a></code> and <code><a href="ft2-base_interface.html#ft_done_face">FT_Done_Face</a></code>.</p>
<h4>note</h4>

<p>Library objects are normally created by <code><a href="ft2-base_interface.html#ft_init_freetype">FT_Init_FreeType</a></code>, and destroyed with <code><a href="ft2-base_interface.html#ft_done_freetype">FT_Done_FreeType</a></code>. If you need reference-counting (cf. <code><a href="ft2-module_management.html#ft_reference_library">FT_Reference_Library</a></code>), use <code><a href="ft2-module_management.html#ft_new_library">FT_New_Library</a></code> and <code><a href="ft2-module_management.html#ft_done_library">FT_Done_Library</a></code>.</p>
<hr>

<h2 id="ft_face">FT_Face<a class="headerlink" href="#ft_face" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_FaceRec_*  <b>FT_Face</b>;
</code></pre></div>

<p>A handle to a typographic face object. A face object models a given typeface, in a given style.</p>
<h4>note</h4>

<p>A face object also owns a single <code><a href="ft2-base_interface.html#ft_glyphslot">FT_GlyphSlot</a></code> object, as well as one or more <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code> objects.</p>
<p>Use <code><a href="ft2-base_interface.html#ft_new_face">FT_New_Face</a></code> or <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> to create a new face object from a given filepath or a custom input stream.</p>
<p>Use <code><a href="ft2-base_interface.html#ft_done_face">FT_Done_Face</a></code> to destroy it (along with its slot and sizes).</p>
<p>An <code>FT_Face</code> object can only be safely used from one thread at a time. Similarly, creation and destruction of <code>FT_Face</code> with the same <code><a href="ft2-base_interface.html#ft_library">FT_Library</a></code> object can only be done from one thread at a time. On the other hand, functions like <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> and its siblings are thread-safe and do not need the lock to be held as long as the same <code>FT_Face</code> object is not used from multiple threads at the same time.</p>
<h4>also</h4>

<p>See <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> for the publicly accessible fields of a given face object.</p>
<hr>

<h2 id="ft_size">FT_Size<a class="headerlink" href="#ft_size" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_SizeRec_*  <b>FT_Size</b>;
</code></pre></div>

<p>A handle to an object that models a face scaled to a given character size.</p>
<h4>note</h4>

<p>An <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code> has one <em>active</em> <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code> object that is used by functions like <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> to determine the scaling transformation that in turn is used to load and hint glyphs and metrics.</p>
<p>You can use <code><a href="ft2-base_interface.html#ft_set_char_size">FT_Set_Char_Size</a></code>, <code><a href="ft2-base_interface.html#ft_set_pixel_sizes">FT_Set_Pixel_Sizes</a></code>, <code><a href="ft2-base_interface.html#ft_request_size">FT_Request_Size</a></code> or even <code><a href="ft2-base_interface.html#ft_select_size">FT_Select_Size</a></code> to change the content (i.e., the scaling values) of the active <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code>.</p>
<p>You can use <code><a href="ft2-sizes_management.html#ft_new_size">FT_New_Size</a></code> to create additional size objects for a given <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code>, but they won't be used by other functions until you activate it through <code><a href="ft2-sizes_management.html#ft_activate_size">FT_Activate_Size</a></code>. Only one size can be activated at any given time per face.</p>
<h4>also</h4>

<p>See <code><a href="ft2-base_interface.html#ft_sizerec">FT_SizeRec</a></code> for the publicly accessible fields of a given size object.</p>
<hr>

<h2 id="ft_glyphslot">FT_GlyphSlot<a class="headerlink" href="#ft_glyphslot" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_GlyphSlotRec_*  <b>FT_GlyphSlot</b>;
</code></pre></div>

<p>A handle to a given &lsquo;glyph slot&rsquo;. A slot is a container that can hold any of the glyphs contained in its parent face.</p>
<p>In other words, each time you call <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> or <code><a href="ft2-base_interface.html#ft_load_char">FT_Load_Char</a></code>, the slot's content is erased by the new glyph data, i.e., the glyph's metrics, its image (bitmap or outline), and other control information.</p>
<h4>also</h4>

<p>See <code><a href="ft2-base_interface.html#ft_glyphslotrec">FT_GlyphSlotRec</a></code> for the publicly accessible glyph fields.</p>
<hr>

<h2 id="ft_charmap">FT_CharMap<a class="headerlink" href="#ft_charmap" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_CharMapRec_*  <b>FT_CharMap</b>;
</code></pre></div>

<p>A handle to a character map (usually abbreviated to &lsquo;charmap&rsquo;). A charmap is used to translate character codes in a given encoding into glyph indexes for its parent's face. Some font formats may provide several charmaps per font.</p>
<p>Each face object owns zero or more charmaps, but only one of them can be &lsquo;active&rsquo;, providing the data used by <code><a href="ft2-base_interface.html#ft_get_char_index">FT_Get_Char_Index</a></code> or <code><a href="ft2-base_interface.html#ft_load_char">FT_Load_Char</a></code>.</p>
<p>The list of available charmaps in a face is available through the <code>face-&gt;num_charmaps</code> and <code>face-&gt;charmaps</code> fields of <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code>.</p>
<p>The currently active charmap is available as <code>face-&gt;charmap</code>. You should call <code><a href="ft2-base_interface.html#ft_set_charmap">FT_Set_Charmap</a></code> to change it.</p>
<h4>note</h4>

<p>When a new face is created (either through <code><a href="ft2-base_interface.html#ft_new_face">FT_New_Face</a></code> or <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code>), the library looks for a Unicode charmap within the list and automatically activates it. If there is no Unicode charmap, FreeType doesn't set an &lsquo;active&rsquo; charmap.</p>
<h4>also</h4>

<p>See <code><a href="ft2-base_interface.html#ft_charmaprec">FT_CharMapRec</a></code> for the publicly accessible fields of a given character map.</p>
<hr>

<h2 id="ft_encoding">FT_Encoding<a class="headerlink" href="#ft_encoding" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Encoding_
  {
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_none">FT_ENCODING_NONE</a>, 0, 0, 0, 0 ),

    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_ms_symbol">FT_ENCODING_MS_SYMBOL</a>, 's', 'y', 'm', 'b' ),
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_unicode">FT_ENCODING_UNICODE</a>,   'u', 'n', 'i', 'c' ),

    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_sjis">FT_ENCODING_SJIS</a>,    's', 'j', 'i', 's' ),
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_prc">FT_ENCODING_PRC</a>,     'g', 'b', ' ', ' ' ),
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_big5">FT_ENCODING_BIG5</a>,    'b', 'i', 'g', '5' ),
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_wansung">FT_ENCODING_WANSUNG</a>, 'w', 'a', 'n', 's' ),
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_johab">FT_ENCODING_JOHAB</a>,   'j', 'o', 'h', 'a' ),

    /* for backward compatibility */
    FT_ENCODING_GB2312     = <a href="ft2-base_interface.html#ft_encoding_prc">FT_ENCODING_PRC</a>,
    <a href="ft2-base_interface.html#ft_encoding_ms_sjis">FT_ENCODING_MS_SJIS</a>    = <a href="ft2-base_interface.html#ft_encoding_sjis">FT_ENCODING_SJIS</a>,
    <a href="ft2-base_interface.html#ft_encoding_ms_gb2312">FT_ENCODING_MS_GB2312</a>  = <a href="ft2-base_interface.html#ft_encoding_prc">FT_ENCODING_PRC</a>,
    <a href="ft2-base_interface.html#ft_encoding_ms_big5">FT_ENCODING_MS_BIG5</a>    = <a href="ft2-base_interface.html#ft_encoding_big5">FT_ENCODING_BIG5</a>,
    <a href="ft2-base_interface.html#ft_encoding_ms_wansung">FT_ENCODING_MS_WANSUNG</a> = <a href="ft2-base_interface.html#ft_encoding_wansung">FT_ENCODING_WANSUNG</a>,
    <a href="ft2-base_interface.html#ft_encoding_ms_johab">FT_ENCODING_MS_JOHAB</a>   = <a href="ft2-base_interface.html#ft_encoding_johab">FT_ENCODING_JOHAB</a>,

    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_adobe_standard">FT_ENCODING_ADOBE_STANDARD</a>, 'A', 'D', 'O', 'B' ),
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_adobe_expert">FT_ENCODING_ADOBE_EXPERT</a>,   'A', 'D', 'B', 'E' ),
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_adobe_custom">FT_ENCODING_ADOBE_CUSTOM</a>,   'A', 'D', 'B', 'C' ),
    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_adobe_latin_1">FT_ENCODING_ADOBE_LATIN_1</a>,  'l', 'a', 't', '1' ),

    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_old_latin_2">FT_ENCODING_OLD_LATIN_2</a>, 'l', 'a', 't', '2' ),

    <a href="ft2-base_interface.html#ft_enc_tag">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#ft_encoding_apple_roman">FT_ENCODING_APPLE_ROMAN</a>, 'a', 'r', 'm', 'n' )

  } <b>FT_Encoding</b>;


  /* these constants are deprecated; use the corresponding `<b>FT_Encoding</b>` */
  /* values instead                                                      */
#<span class="keyword">define</span> ft_encoding_none            <a href="ft2-base_interface.html#ft_encoding_none">FT_ENCODING_NONE</a>
#<span class="keyword">define</span> ft_encoding_unicode         <a href="ft2-base_interface.html#ft_encoding_unicode">FT_ENCODING_UNICODE</a>
#<span class="keyword">define</span> ft_encoding_symbol          <a href="ft2-base_interface.html#ft_encoding_ms_symbol">FT_ENCODING_MS_SYMBOL</a>
#<span class="keyword">define</span> ft_encoding_latin_1         <a href="ft2-base_interface.html#ft_encoding_adobe_latin_1">FT_ENCODING_ADOBE_LATIN_1</a>
#<span class="keyword">define</span> ft_encoding_latin_2         <a href="ft2-base_interface.html#ft_encoding_old_latin_2">FT_ENCODING_OLD_LATIN_2</a>
#<span class="keyword">define</span> ft_encoding_sjis            <a href="ft2-base_interface.html#ft_encoding_sjis">FT_ENCODING_SJIS</a>
#<span class="keyword">define</span> ft_encoding_gb2312          <a href="ft2-base_interface.html#ft_encoding_prc">FT_ENCODING_PRC</a>
#<span class="keyword">define</span> ft_encoding_big5            <a href="ft2-base_interface.html#ft_encoding_big5">FT_ENCODING_BIG5</a>
#<span class="keyword">define</span> ft_encoding_wansung         <a href="ft2-base_interface.html#ft_encoding_wansung">FT_ENCODING_WANSUNG</a>
#<span class="keyword">define</span> ft_encoding_johab           <a href="ft2-base_interface.html#ft_encoding_johab">FT_ENCODING_JOHAB</a>

#<span class="keyword">define</span> ft_encoding_adobe_standard  <a href="ft2-base_interface.html#ft_encoding_adobe_standard">FT_ENCODING_ADOBE_STANDARD</a>
#<span class="keyword">define</span> ft_encoding_adobe_expert    <a href="ft2-base_interface.html#ft_encoding_adobe_expert">FT_ENCODING_ADOBE_EXPERT</a>
#<span class="keyword">define</span> ft_encoding_adobe_custom    <a href="ft2-base_interface.html#ft_encoding_adobe_custom">FT_ENCODING_ADOBE_CUSTOM</a>
#<span class="keyword">define</span> ft_encoding_apple_roman     <a href="ft2-base_interface.html#ft_encoding_apple_roman">FT_ENCODING_APPLE_ROMAN</a>
</code></pre></div>

<p>An enumeration to specify character sets supported by charmaps. Used in the <code><a href="ft2-base_interface.html#ft_select_charmap">FT_Select_Charmap</a></code> API function.</p>
<h4>note</h4>

<p>Despite the name, this enumeration lists specific character repertories (i.e., charsets), and not text encoding methods (e.g., UTF-8, UTF-16, etc.).</p>
<p>Other encodings might be defined in the future.</p>
<h4>values</h4>

<table class="fields long">
<tr><td class="val" id="ft_encoding_none">FT_ENCODING_NONE</td><td class="desc">
<p>The encoding value&nbsp;0 is reserved for all formats except BDF, PCF, and Windows FNT; see below for more information.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_unicode">FT_ENCODING_UNICODE</td><td class="desc">
<p>The Unicode character set. This value covers all versions of the Unicode repertoire, including ASCII and Latin-1. Most fonts include a Unicode charmap, but not all of them.</p>
<p>For example, if you want to access Unicode value U+1F028 (and the font contains it), use value 0x1F028 as the input value for <code><a href="ft2-base_interface.html#ft_get_char_index">FT_Get_Char_Index</a></code>.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_ms_symbol">FT_ENCODING_MS_SYMBOL</td><td class="desc">
<p>Microsoft Symbol encoding, used to encode mathematical symbols and wingdings. For more information, see &lsquo;<a href="https://www.microsoft.com/typography/otspec/recom.htm#non-standard-symbol-fonts">https://www.microsoft.com/typography/otspec/recom.htm#non-standard-symbol-fonts</a>&rsquo;, &lsquo;<a href="http://www.kostis.net/charsets/symbol.htm">http://www.kostis.net/charsets/symbol.htm</a>&rsquo;, and &lsquo;<a href="http://www.kostis.net/charsets/wingding.htm">http://www.kostis.net/charsets/wingding.htm</a>&rsquo;.</p>
<p>This encoding uses character codes from the PUA (Private Unicode Area) in the range U+F020-U+F0FF.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_sjis">FT_ENCODING_SJIS</td><td class="desc">
<p>Shift JIS encoding for Japanese. More info at &lsquo;<a href="https://en.wikipedia.org/wiki/Shift_JIS">https://en.wikipedia.org/wiki/Shift_JIS</a>&rsquo;. See note on multi-byte encodings below.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_prc">FT_ENCODING_PRC</td><td class="desc">
<p>Corresponds to encoding systems mainly for Simplified Chinese as used in People's Republic of China (PRC). The encoding layout is based on GB&nbsp;2312 and its supersets GBK and GB&nbsp;18030.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_big5">FT_ENCODING_BIG5</td><td class="desc">
<p>Corresponds to an encoding system for Traditional Chinese as used in Taiwan and Hong Kong.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_wansung">FT_ENCODING_WANSUNG</td><td class="desc">
<p>Corresponds to the Korean encoding system known as Extended Wansung (MS Windows code page 949). For more information see &lsquo;<a href="https://www.unicode.org/Public/MAPPINGS/VENDORS/MICSFT/WindowsBestFit/bestfit949.txt">https://www.unicode.org/Public/MAPPINGS/VENDORS/MICSFT/WindowsBestFit/bestfit949.txt</a>&rsquo;.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_johab">FT_ENCODING_JOHAB</td><td class="desc">
<p>The Korean standard character set (KS&nbsp;C 5601-1992), which corresponds to MS Windows code page 1361. This character set includes all possible Hangul character combinations.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_adobe_latin_1">FT_ENCODING_ADOBE_LATIN_1</td><td class="desc">
<p>Corresponds to a Latin-1 encoding as defined in a Type&nbsp;1 PostScript font. It is limited to 256 character codes.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_adobe_standard">FT_ENCODING_ADOBE_STANDARD</td><td class="desc">
<p>Adobe Standard encoding, as found in Type&nbsp;1, CFF, and OpenType/CFF fonts. It is limited to 256 character codes.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_adobe_expert">FT_ENCODING_ADOBE_EXPERT</td><td class="desc">
<p>Adobe Expert encoding, as found in Type&nbsp;1, CFF, and OpenType/CFF fonts. It is limited to 256 character codes.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_adobe_custom">FT_ENCODING_ADOBE_CUSTOM</td><td class="desc">
<p>Corresponds to a custom encoding, as found in Type&nbsp;1, CFF, and OpenType/CFF fonts. It is limited to 256 character codes.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_apple_roman">FT_ENCODING_APPLE_ROMAN</td><td class="desc">
<p>Apple roman encoding. Many TrueType and OpenType fonts contain a charmap for this 8-bit encoding, since older versions of Mac OS are able to use it.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_old_latin_2">FT_ENCODING_OLD_LATIN_2</td><td class="desc">
<p>This value is deprecated and was neither used nor reported by FreeType. Don't use or test for it.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_ms_sjis">FT_ENCODING_MS_SJIS</td><td class="desc">
<p>Same as FT_ENCODING_SJIS. Deprecated.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_ms_gb2312">FT_ENCODING_MS_GB2312</td><td class="desc">
<p>Same as FT_ENCODING_PRC. Deprecated.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_ms_big5">FT_ENCODING_MS_BIG5</td><td class="desc">
<p>Same as FT_ENCODING_BIG5. Deprecated.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_ms_wansung">FT_ENCODING_MS_WANSUNG</td><td class="desc">
<p>Same as FT_ENCODING_WANSUNG. Deprecated.</p>
</td></tr>
<tr><td class="val" id="ft_encoding_ms_johab">FT_ENCODING_MS_JOHAB</td><td class="desc">
<p>Same as FT_ENCODING_JOHAB. Deprecated.</p>
</td></tr>
</table>

<h4>note</h4>

<p>By default, FreeType enables a Unicode charmap and tags it with <code>FT_ENCODING_UNICODE</code> when it is either provided or can be generated from PostScript glyph name dictionaries in the font file. All other encodings are considered legacy and tagged only if explicitly defined in the font file. Otherwise, <code>FT_ENCODING_NONE</code> is used.</p>
<p><code>FT_ENCODING_NONE</code> is set by the BDF and PCF drivers if the charmap is neither Unicode nor ISO-8859-1 (otherwise it is set to <code>FT_ENCODING_UNICODE</code>). Use <code><a href="ft2-bdf_fonts.html#ft_get_bdf_charset_id">FT_Get_BDF_Charset_ID</a></code> to find out which encoding is really present. If, for example, the <code>cs_registry</code> field is &lsquo;KOI8&rsquo; and the <code>cs_encoding</code> field is &lsquo;R&rsquo;, the font is encoded in KOI8-R.</p>
<p><code>FT_ENCODING_NONE</code> is always set (with a single exception) by the winfonts driver. Use <code><a href="ft2-winfnt_fonts.html#ft_get_winfnt_header">FT_Get_WinFNT_Header</a></code> and examine the <code>charset</code> field of the <code><a href="ft2-winfnt_fonts.html#ft_winfnt_headerrec">FT_WinFNT_HeaderRec</a></code> structure to find out which encoding is really present. For example, <code><a href="ft2-winfnt_fonts.html#ft_winfnt_id_xxx">FT_WinFNT_ID_CP1251</a></code> (204) means Windows code page 1251 (for Russian).</p>
<p><code>FT_ENCODING_NONE</code> is set if <code>platform_id</code> is <code><a href="ft2-truetype_tables.html#tt_platform_xxx">TT_PLATFORM_MACINTOSH</a></code> and <code>encoding_id</code> is not <code>TT_MAC_ID_ROMAN</code> (otherwise it is set to <code>FT_ENCODING_APPLE_ROMAN</code>).</p>
<p>If <code>platform_id</code> is <code><a href="ft2-truetype_tables.html#tt_platform_xxx">TT_PLATFORM_MACINTOSH</a></code>, use the function <code><a href="ft2-truetype_tables.html#ft_get_cmap_language_id">FT_Get_CMap_Language_ID</a></code> to query the Mac language ID that may be needed to be able to distinguish Apple encoding variants. See</p>
<p><a href="https://www.unicode.org/Public/MAPPINGS/VENDORS/APPLE/Readme.txt">https://www.unicode.org/Public/MAPPINGS/VENDORS/APPLE/Readme.txt</a></p>
<p>to get an idea how to do that. Basically, if the language ID is&nbsp;0, don't use it, otherwise subtract 1 from the language ID. Then examine <code>encoding_id</code>. If, for example, <code>encoding_id</code> is <code>TT_MAC_ID_ROMAN</code> and the language ID (minus&nbsp;1) is <code>TT_MAC_LANGID_GREEK</code>, it is the Greek encoding, not Roman. <code>TT_MAC_ID_ARABIC</code> with <code>TT_MAC_LANGID_FARSI</code> means the Farsi variant the Arabic encoding.</p>
<hr>

<h2 id="ft_enc_tag">FT_ENC_TAG<a class="headerlink" href="#ft_enc_tag" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">ifndef</span> <b>FT_ENC_TAG</b>
#<span class="keyword">define</span> <b>FT_ENC_TAG</b>( value, a, b, c, d )         \
          value = ( ( (<a href="ft2-basic_types.html#ft_uint32">FT_UInt32</a>)(a) &lt;&lt; 24 ) |  \
                    ( (<a href="ft2-basic_types.html#ft_uint32">FT_UInt32</a>)(b) &lt;&lt; 16 ) |  \
                    ( (<a href="ft2-basic_types.html#ft_uint32">FT_UInt32</a>)(c) &lt;&lt;  8 ) |  \
                      (<a href="ft2-basic_types.html#ft_uint32">FT_UInt32</a>)(d)         )

#<span class="keyword">endif</span> /* <b>FT_ENC_TAG</b> */
</code></pre></div>

<p>This macro converts four-letter tags into an unsigned long. It is used to define &lsquo;encoding&rsquo; identifiers (see <code><a href="ft2-base_interface.html#ft_encoding">FT_Encoding</a></code>).</p>
<h4>note</h4>

<p>Since many 16-bit compilers don't like 32-bit enumerations, you should redefine this macro in case of problems to something like this:
<div class="highlight"><pre><span></span><code>  #define FT_ENC_TAG( value, a, b, c, d )  value
</code></pre></div></p>
<p>to get a simple enumeration without assigning special numbers.</p>
<hr>

<h2 id="ft_facerec">FT_FaceRec<a class="headerlink" href="#ft_facerec" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_FaceRec_
  {
    <a href="ft2-basic_types.html#ft_long">FT_Long</a>           num_faces;
    <a href="ft2-basic_types.html#ft_long">FT_Long</a>           face_index;

    <a href="ft2-basic_types.html#ft_long">FT_Long</a>           face_flags;
    <a href="ft2-basic_types.html#ft_long">FT_Long</a>           style_flags;

    <a href="ft2-basic_types.html#ft_long">FT_Long</a>           num_glyphs;

    <a href="ft2-basic_types.html#ft_string">FT_String</a>*        family_name;
    <a href="ft2-basic_types.html#ft_string">FT_String</a>*        style_name;

    <a href="ft2-basic_types.html#ft_int">FT_Int</a>            num_fixed_sizes;
    <a href="ft2-base_interface.html#ft_bitmap_size">FT_Bitmap_Size</a>*   available_sizes;

    <a href="ft2-basic_types.html#ft_int">FT_Int</a>            num_charmaps;
    <a href="ft2-base_interface.html#ft_charmap">FT_CharMap</a>*       charmaps;

    <a href="ft2-basic_types.html#ft_generic">FT_Generic</a>        generic;

    /*# The following member variables (down to `underline_thickness`) */
    /*# are only relevant to scalable outlines; cf. @<a href="ft2-base_interface.html#ft_bitmap_size">FT_Bitmap_Size</a>    */
    /*# for bitmap fonts.                                              */
    <a href="ft2-basic_types.html#ft_bbox">FT_BBox</a>           bbox;

    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>         units_per_EM;
    <a href="ft2-basic_types.html#ft_short">FT_Short</a>          ascender;
    <a href="ft2-basic_types.html#ft_short">FT_Short</a>          descender;
    <a href="ft2-basic_types.html#ft_short">FT_Short</a>          height;

    <a href="ft2-basic_types.html#ft_short">FT_Short</a>          max_advance_width;
    <a href="ft2-basic_types.html#ft_short">FT_Short</a>          max_advance_height;

    <a href="ft2-basic_types.html#ft_short">FT_Short</a>          underline_position;
    <a href="ft2-basic_types.html#ft_short">FT_Short</a>          underline_thickness;

    <a href="ft2-base_interface.html#ft_glyphslot">FT_GlyphSlot</a>      glyph;
    <a href="ft2-base_interface.html#ft_size">FT_Size</a>           size;
    <a href="ft2-base_interface.html#ft_charmap">FT_CharMap</a>        charmap;

    /*@private begin */

    <a href="ft2-module_management.html#ft_driver">FT_Driver</a>         driver;
    <a href="ft2-system_interface.html#ft_memory">FT_Memory</a>         memory;
    <a href="ft2-system_interface.html#ft_stream">FT_Stream</a>         stream;

    <a href="ft2-list_processing.html#ft_listrec">FT_ListRec</a>        sizes_list;

    <a href="ft2-basic_types.html#ft_generic">FT_Generic</a>        autohint;   /* face-specific auto-hinter data */
    <span class="keyword">void</span>*             extensions; /* unused                         */

    <a href="ft2-base_interface.html#ft_face_internal">FT_Face_Internal</a>  internal;

    /*@private end */

  } <b>FT_FaceRec</b>;
</code></pre></div>

<p>FreeType root face class structure. A face object models a typeface in a font file.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="num_faces">num_faces</td><td class="desc">
<p>The number of faces in the font file. Some font formats can have multiple faces in a single font file.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>This field holds two different values. Bits 0-15 are the index of the face in the font file (starting with value&nbsp;0). They are set to&nbsp;0 if there is only one face in the font file.</p>
<p>[Since 2.6.1] Bits 16-30 are relevant to GX and OpenType variation fonts only, holding the named instance index for the current face index (starting with value&nbsp;1; value&nbsp;0 indicates font access without a named instance). For non-variation fonts, bits 16-30 are ignored. If we have the third named instance of face&nbsp;4, say, <code>face_index</code> is set to 0x00030004.</p>
<p>Bit 31 is always zero (this is, <code>face_index</code> is always a positive value).</p>
<p>[Since 2.9] Changing the design coordinates with <code><a href="ft2-multiple_masters.html#ft_set_var_design_coordinates">FT_Set_Var_Design_Coordinates</a></code> or <code><a href="ft2-multiple_masters.html#ft_set_var_blend_coordinates">FT_Set_Var_Blend_Coordinates</a></code> does not influence the named instance index value (only <code><a href="ft2-multiple_masters.html#ft_set_named_instance">FT_Set_Named_Instance</a></code> does that).</p>
</td></tr>
<tr><td class="val" id="face_flags">face_flags</td><td class="desc">
<p>A set of bit flags that give important information about the face; see <code><a href="ft2-base_interface.html#ft_face_flag_xxx">FT_FACE_FLAG_XXX</a></code> for the details.</p>
</td></tr>
<tr><td class="val" id="style_flags">style_flags</td><td class="desc">
<p>The lower 16&nbsp;bits contain a set of bit flags indicating the style of the face; see <code><a href="ft2-base_interface.html#ft_style_flag_xxx">FT_STYLE_FLAG_XXX</a></code> for the details.</p>
<p>[Since 2.6.1] Bits 16-30 hold the number of named instances available for the current face if we have a GX or OpenType variation (sub)font. Bit 31 is always zero (this is, <code>style_flags</code> is always a positive value). Note that a variation font has always at least one named instance, namely the default instance.</p>
</td></tr>
<tr><td class="val" id="num_glyphs">num_glyphs</td><td class="desc">
<p>The number of glyphs in the face. If the face is scalable and has sbits (see <code>num_fixed_sizes</code>), it is set to the number of outline glyphs.</p>
<p>For CID-keyed fonts (not in an SFNT wrapper) this value gives the highest CID used in the font.</p>
</td></tr>
<tr><td class="val" id="family_name">family_name</td><td class="desc">
<p>The face's family name. This is an ASCII string, usually in English, that describes the typeface's family (like &lsquo;Times New Roman&rsquo;, &lsquo;Bodoni&rsquo;, &lsquo;Garamond&rsquo;, etc). This is a least common denominator used to list fonts. Some formats (TrueType &amp; OpenType) provide localized and Unicode versions of this string. Applications should use the format-specific interface to access them. Can be <code>NULL</code> (e.g., in fonts embedded in a PDF file).</p>
<p>In case the font doesn't provide a specific family name entry, FreeType tries to synthesize one, deriving it from other name entries.</p>
</td></tr>
<tr><td class="val" id="style_name">style_name</td><td class="desc">
<p>The face's style name. This is an ASCII string, usually in English, that describes the typeface's style (like &lsquo;Italic&rsquo;, &lsquo;Bold&rsquo;, &lsquo;Condensed&rsquo;, etc). Not all font formats provide a style name, so this field is optional, and can be set to <code>NULL</code>. As for <code>family_name</code>, some formats provide localized and Unicode versions of this string. Applications should use the format-specific interface to access them.</p>
</td></tr>
<tr><td class="val" id="num_fixed_sizes">num_fixed_sizes</td><td class="desc">
<p>The number of bitmap strikes in the face. Even if the face is scalable, there might still be bitmap strikes, which are called &lsquo;sbits&rsquo; in that case.</p>
</td></tr>
<tr><td class="val" id="available_sizes">available_sizes</td><td class="desc">
<p>An array of <code><a href="ft2-base_interface.html#ft_bitmap_size">FT_Bitmap_Size</a></code> for all bitmap strikes in the face. It is set to <code>NULL</code> if there is no bitmap strike.</p>
<p>Note that FreeType tries to sanitize the strike data since they are sometimes sloppy or incorrect, but this can easily fail.</p>
</td></tr>
<tr><td class="val" id="num_charmaps">num_charmaps</td><td class="desc">
<p>The number of charmaps in the face.</p>
</td></tr>
<tr><td class="val" id="charmaps">charmaps</td><td class="desc">
<p>An array of the charmaps of the face.</p>
</td></tr>
<tr><td class="val" id="generic">generic</td><td class="desc">
<p>A field reserved for client uses. See the <code><a href="ft2-basic_types.html#ft_generic">FT_Generic</a></code> type description.</p>
</td></tr>
<tr><td class="val" id="bbox">bbox</td><td class="desc">
<p>The font bounding box. Coordinates are expressed in font units (see <code>units_per_EM</code>). The box is large enough to contain any glyph from the font. Thus, <code>bbox.yMax</code> can be seen as the &lsquo;maximum ascender&rsquo;, and <code>bbox.yMin</code> as the &lsquo;minimum descender&rsquo;. Only relevant for scalable formats.</p>
<p>Note that the bounding box might be off by (at least) one pixel for hinted fonts. See <code><a href="ft2-base_interface.html#ft_size_metrics">FT_Size_Metrics</a></code> for further discussion.</p>
<p>Note that the bounding box does not vary in OpenType variable fonts and should only be used in relation to the default instance.</p>
</td></tr>
<tr><td class="val" id="units_per_em">units_per_EM</td><td class="desc">
<p>The number of font units per EM square for this face. This is typically 2048 for TrueType fonts, and 1000 for Type&nbsp;1 fonts. Only relevant for scalable formats.</p>
</td></tr>
<tr><td class="val" id="ascender">ascender</td><td class="desc">
<p>The typographic ascender of the face, expressed in font units. For font formats not having this information, it is set to <code>bbox.yMax</code>. Only relevant for scalable formats.</p>
</td></tr>
<tr><td class="val" id="descender">descender</td><td class="desc">
<p>The typographic descender of the face, expressed in font units. For font formats not having this information, it is set to <code>bbox.yMin</code>. Note that this field is negative for values below the baseline. Only relevant for scalable formats.</p>
</td></tr>
<tr><td class="val" id="height">height</td><td class="desc">
<p>This value is the vertical distance between two consecutive baselines, expressed in font units. It is always positive. Only relevant for scalable formats.</p>
<p>If you want the global glyph height, use <code>ascender - descender</code>.</p>
</td></tr>
<tr><td class="val" id="max_advance_width">max_advance_width</td><td class="desc">
<p>The maximum advance width, in font units, for all glyphs in this face. This can be used to make word wrapping computations faster. Only relevant for scalable formats.</p>
</td></tr>
<tr><td class="val" id="max_advance_height">max_advance_height</td><td class="desc">
<p>The maximum advance height, in font units, for all glyphs in this face. This is only relevant for vertical layouts, and is set to <code>height</code> for fonts that do not provide vertical metrics. Only relevant for scalable formats.</p>
</td></tr>
<tr><td class="val" id="underline_position">underline_position</td><td class="desc">
<p>The position, in font units, of the underline line for this face. It is the center of the underlining stem. Only relevant for scalable formats.</p>
</td></tr>
<tr><td class="val" id="underline_thickness">underline_thickness</td><td class="desc">
<p>The thickness, in font units, of the underline for this face. Only relevant for scalable formats.</p>
</td></tr>
<tr><td class="val" id="glyph">glyph</td><td class="desc">
<p>The face's associated glyph slot(s).</p>
</td></tr>
<tr><td class="val" id="size">size</td><td class="desc">
<p>The current active size for this face.</p>
</td></tr>
<tr><td class="val" id="charmap">charmap</td><td class="desc">
<p>The current active charmap for this face.</p>
</td></tr>
</table>

<h4>note</h4>

<p>Fields may be changed after a call to <code><a href="ft2-base_interface.html#ft_attach_file">FT_Attach_File</a></code> or <code><a href="ft2-base_interface.html#ft_attach_stream">FT_Attach_Stream</a></code>.</p>
<p>For an OpenType variation font, the values of the following fields can change after a call to <code><a href="ft2-multiple_masters.html#ft_set_var_design_coordinates">FT_Set_Var_Design_Coordinates</a></code> (and friends) if the font contains an &lsquo;MVAR&rsquo; table: <code>ascender</code>, <code>descender</code>, <code>height</code>, <code>underline_position</code>, and <code>underline_thickness</code>.</p>
<p>Especially for TrueType fonts see also the documentation for <code><a href="ft2-base_interface.html#ft_size_metrics">FT_Size_Metrics</a></code>.</p>
<hr>

<h2 id="ft_has_horizontal">FT_HAS_HORIZONTAL<a class="headerlink" href="#ft_has_horizontal" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_HAS_HORIZONTAL</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_horizontal">FT_FACE_FLAG_HORIZONTAL</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains horizontal metrics (this is true for all font formats though).</p>
<h4>also</h4>

<p><code><a href="ft2-base_interface.html#ft_has_vertical">FT_HAS_VERTICAL</a></code> can be used to check for vertical metrics.</p>
<hr>

<h2 id="ft_has_vertical">FT_HAS_VERTICAL<a class="headerlink" href="#ft_has_vertical" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_HAS_VERTICAL</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_vertical">FT_FACE_FLAG_VERTICAL</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains real vertical metrics (and not only synthesized ones).</p>
<hr>

<h2 id="ft_has_kerning">FT_HAS_KERNING<a class="headerlink" href="#ft_has_kerning" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_HAS_KERNING</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_kerning">FT_FACE_FLAG_KERNING</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains kerning data that can be accessed with <code><a href="ft2-base_interface.html#ft_get_kerning">FT_Get_Kerning</a></code>.</p>
<hr>

<h2 id="ft_has_fixed_sizes">FT_HAS_FIXED_SIZES<a class="headerlink" href="#ft_has_fixed_sizes" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_HAS_FIXED_SIZES</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_fixed_sizes">FT_FACE_FLAG_FIXED_SIZES</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains some embedded bitmaps. See the <code>available_sizes</code> field of the <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> structure.</p>
<hr>

<h2 id="ft_has_glyph_names">FT_HAS_GLYPH_NAMES<a class="headerlink" href="#ft_has_glyph_names" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_HAS_GLYPH_NAMES</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_glyph_names">FT_FACE_FLAG_GLYPH_NAMES</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains some glyph names that can be accessed through <code><a href="ft2-base_interface.html#ft_get_glyph_name">FT_Get_Glyph_Name</a></code>.</p>
<hr>

<h2 id="ft_has_color">FT_HAS_COLOR<a class="headerlink" href="#ft_has_color" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_HAS_COLOR</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_color">FT_FACE_FLAG_COLOR</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains tables for color glyphs.</p>
<h4>since</h4>

<p>2.5.1</p>
<hr>

<h2 id="ft_has_multiple_masters">FT_HAS_MULTIPLE_MASTERS<a class="headerlink" href="#ft_has_multiple_masters" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_HAS_MULTIPLE_MASTERS</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_multiple_masters">FT_FACE_FLAG_MULTIPLE_MASTERS</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains some multiple masters. The functions provided by <code><a href="ft2-header_file_macros.html#ft_multiple_masters_h">FT_MULTIPLE_MASTERS_H</a></code> are then available to choose the exact design you want.</p>
<hr>

<h2 id="ft_is_sfnt">FT_IS_SFNT<a class="headerlink" href="#ft_is_sfnt" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_IS_SFNT</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_sfnt">FT_FACE_FLAG_SFNT</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains a font whose format is based on the SFNT storage scheme. This usually means: TrueType fonts, OpenType fonts, as well as SFNT-based embedded bitmap fonts.</p>
<p>If this macro is true, all functions defined in <code><a href="ft2-header_file_macros.html#ft_sfnt_names_h">FT_SFNT_NAMES_H</a></code> and <code><a href="ft2-header_file_macros.html#ft_truetype_tables_h">FT_TRUETYPE_TABLES_H</a></code> are available.</p>
<hr>

<h2 id="ft_is_scalable">FT_IS_SCALABLE<a class="headerlink" href="#ft_is_scalable" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_IS_SCALABLE</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_scalable">FT_FACE_FLAG_SCALABLE</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains a scalable font face (true for TrueType, Type&nbsp;1, Type&nbsp;42, CID, OpenType/CFF, and PFR font formats).</p>
<hr>

<h2 id="ft_is_fixed_width">FT_IS_FIXED_WIDTH<a class="headerlink" href="#ft_is_fixed_width" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_IS_FIXED_WIDTH</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_fixed_width">FT_FACE_FLAG_FIXED_WIDTH</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains a font face that contains fixed-width (or &lsquo;monospace&rsquo;, &lsquo;fixed-pitch&rsquo;, etc.) glyphs.</p>
<hr>

<h2 id="ft_is_cid_keyed">FT_IS_CID_KEYED<a class="headerlink" href="#ft_is_cid_keyed" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_IS_CID_KEYED</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_cid_keyed">FT_FACE_FLAG_CID_KEYED</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object contains a CID-keyed font. See the discussion of <code><a href="ft2-base_interface.html#ft_face_flag_xxx">FT_FACE_FLAG_CID_KEYED</a></code> for more details.</p>
<p>If this macro is true, all functions defined in <code><a href="ft2-header_file_macros.html#ft_cid_h">FT_CID_H</a></code> are available.</p>
<hr>

<h2 id="ft_is_tricky">FT_IS_TRICKY<a class="headerlink" href="#ft_is_tricky" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_IS_TRICKY</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_tricky">FT_FACE_FLAG_TRICKY</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face represents a &lsquo;tricky&rsquo; font. See the discussion of <code><a href="ft2-base_interface.html#ft_face_flag_xxx">FT_FACE_FLAG_TRICKY</a></code> for more details.</p>
<hr>

<h2 id="ft_is_named_instance">FT_IS_NAMED_INSTANCE<a class="headerlink" href="#ft_is_named_instance" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_IS_NAMED_INSTANCE</b>( face ) \
          ( !!( (face)-&gt;face_index &amp; 0x7FFF0000L ) )
</code></pre></div>

<p>A macro that returns true whenever a face object is a named instance of a GX or OpenType variation font.</p>
<p>[Since 2.9] Changing the design coordinates with <code><a href="ft2-multiple_masters.html#ft_set_var_design_coordinates">FT_Set_Var_Design_Coordinates</a></code> or <code><a href="ft2-multiple_masters.html#ft_set_var_blend_coordinates">FT_Set_Var_Blend_Coordinates</a></code> does not influence the return value of this macro (only <code><a href="ft2-multiple_masters.html#ft_set_named_instance">FT_Set_Named_Instance</a></code> does that).</p>
<h4>since</h4>

<p>2.7</p>
<hr>

<h2 id="ft_is_variation">FT_IS_VARIATION<a class="headerlink" href="#ft_is_variation" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_IS_VARIATION</b>( face ) \
          ( !!( (face)-&gt;face_flags &amp; <a href="ft2-base_interface.html#ft_face_flag_variation">FT_FACE_FLAG_VARIATION</a> ) )
</code></pre></div>

<p>A macro that returns true whenever a face object has been altered by <code><a href="ft2-multiple_masters.html#ft_set_mm_design_coordinates">FT_Set_MM_Design_Coordinates</a></code>, <code><a href="ft2-multiple_masters.html#ft_set_var_design_coordinates">FT_Set_Var_Design_Coordinates</a></code>, or <code><a href="ft2-multiple_masters.html#ft_set_var_blend_coordinates">FT_Set_Var_Blend_Coordinates</a></code>.</p>
<h4>since</h4>

<p>2.9</p>
<hr>

<h2 id="ft_sizerec">FT_SizeRec<a class="headerlink" href="#ft_sizerec" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_SizeRec_
  {
    <a href="ft2-base_interface.html#ft_face">FT_Face</a>           face;      /* parent face object              */
    <a href="ft2-basic_types.html#ft_generic">FT_Generic</a>        generic;   /* generic pointer for client uses */
    <a href="ft2-base_interface.html#ft_size_metrics">FT_Size_Metrics</a>   metrics;   /* size metrics                    */
    <a href="ft2-base_interface.html#ft_size_internal">FT_Size_Internal</a>  internal;

  } <b>FT_SizeRec</b>;
</code></pre></div>

<p>FreeType root size class structure. A size object models a face object at a given size.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>Handle to the parent face object.</p>
</td></tr>
<tr><td class="val" id="generic">generic</td><td class="desc">
<p>A typeless pointer, unused by the FreeType library or any of its drivers. It can be used by client applications to link their own data to each size object.</p>
</td></tr>
<tr><td class="val" id="metrics">metrics</td><td class="desc">
<p>Metrics for this size object. This field is read-only.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_size_metrics">FT_Size_Metrics<a class="headerlink" href="#ft_size_metrics" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Size_Metrics_
  {
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  x_ppem;      /* horizontal pixels per EM               */
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  y_ppem;      /* vertical pixels per EM                 */

    <a href="ft2-basic_types.html#ft_fixed">FT_Fixed</a>   x_scale;     /* scaling values used to convert font    */
    <a href="ft2-basic_types.html#ft_fixed">FT_Fixed</a>   y_scale;     /* units to 26.6 fractional pixels        */

    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>     ascender;    /* ascender in 26.6 frac. pixels          */
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>     descender;   /* descender in 26.6 frac. pixels         */
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>     height;      /* text height in 26.6 frac. pixels       */
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>     max_advance; /* max horizontal advance, in 26.6 pixels */

  } <b>FT_Size_Metrics</b>;
</code></pre></div>

<p>The size metrics structure gives the metrics of a size object.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="x_ppem">x_ppem</td><td class="desc">
<p>The width of the scaled EM square in pixels, hence the term &lsquo;ppem&rsquo; (pixels per EM). It is also referred to as &lsquo;nominal width&rsquo;.</p>
</td></tr>
<tr><td class="val" id="y_ppem">y_ppem</td><td class="desc">
<p>The height of the scaled EM square in pixels, hence the term &lsquo;ppem&rsquo; (pixels per EM). It is also referred to as &lsquo;nominal height&rsquo;.</p>
</td></tr>
<tr><td class="val" id="x_scale">x_scale</td><td class="desc">
<p>A 16.16 fractional scaling value to convert horizontal metrics from font units to 26.6 fractional pixels. Only relevant for scalable font formats.</p>
</td></tr>
<tr><td class="val" id="y_scale">y_scale</td><td class="desc">
<p>A 16.16 fractional scaling value to convert vertical metrics from font units to 26.6 fractional pixels. Only relevant for scalable font formats.</p>
</td></tr>
<tr><td class="val" id="ascender">ascender</td><td class="desc">
<p>The ascender in 26.6 fractional pixels, rounded up to an integer value. See <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> for the details.</p>
</td></tr>
<tr><td class="val" id="descender">descender</td><td class="desc">
<p>The descender in 26.6 fractional pixels, rounded down to an integer value. See <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> for the details.</p>
</td></tr>
<tr><td class="val" id="height">height</td><td class="desc">
<p>The height in 26.6 fractional pixels, rounded to an integer value. See <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> for the details.</p>
</td></tr>
<tr><td class="val" id="max_advance">max_advance</td><td class="desc">
<p>The maximum advance width in 26.6 fractional pixels, rounded to an integer value. See <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> for the details.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The scaling values, if relevant, are determined first during a size changing operation. The remaining fields are then set by the driver. For scalable formats, they are usually set to scaled values of the corresponding fields in <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code>. Some values like ascender or descender are rounded for historical reasons; more precise values (for outline fonts) can be derived by scaling the corresponding <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> values manually, with code similar to the following.
<div class="highlight"><pre><span></span><code>  scaled_ascender = FT_MulFix( face-&gt;ascender,
                               size_metrics-&gt;y_scale );
</code></pre></div></p>
<p>Note that due to glyph hinting and the selected rendering mode these values are usually not exact; consequently, they must be treated as unreliable with an error margin of at least one pixel!</p>
<p>Indeed, the only way to get the exact metrics is to render <em>all</em> glyphs. As this would be a definite performance hit, it is up to client applications to perform such computations.</p>
<p>The <code>FT_Size_Metrics</code> structure is valid for bitmap fonts also.</p>
<p><strong>TrueType fonts with native bytecode hinting</strong></p>
<p>All applications that handle TrueType fonts with native hinting must be aware that TTFs expect different rounding of vertical font dimensions. The application has to cater for this, especially if it wants to rely on a TTF's vertical data (for example, to properly align box characters vertically).</p>
<p>Only the application knows <em>in advance</em> that it is going to use native hinting for TTFs! FreeType, on the other hand, selects the hinting mode not at the time of creating an <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code> object but much later, namely while calling <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code>.</p>
<p>Here is some pseudo code that illustrates a possible solution.
<div class="highlight"><pre><span></span><code>  font_format = FT_Get_Font_Format( face );

  if ( !strcmp( font_format, &quot;TrueType&quot; ) &amp;&amp;
       do_native_bytecode_hinting         )
  {
    ascender  = ROUND( FT_MulFix( face-&gt;ascender,
                                  size_metrics-&gt;y_scale ) );
    descender = ROUND( FT_MulFix( face-&gt;descender,
                                  size_metrics-&gt;y_scale ) );
  }
  else
  {
    ascender  = size_metrics-&gt;ascender;
    descender = size_metrics-&gt;descender;
  }

  height      = size_metrics-&gt;height;
  max_advance = size_metrics-&gt;max_advance;
</code></pre></div></p>
<hr>

<h2 id="ft_glyphslotrec">FT_GlyphSlotRec<a class="headerlink" href="#ft_glyphslotrec" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_GlyphSlotRec_
  {
    <a href="ft2-base_interface.html#ft_library">FT_Library</a>        library;
    <a href="ft2-base_interface.html#ft_face">FT_Face</a>           face;
    <a href="ft2-base_interface.html#ft_glyphslot">FT_GlyphSlot</a>      next;
    <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>           glyph_index; /* new in 2.10; was reserved previously */
    <a href="ft2-basic_types.html#ft_generic">FT_Generic</a>        generic;

    <a href="ft2-base_interface.html#ft_glyph_metrics">FT_Glyph_Metrics</a>  metrics;
    <a href="ft2-basic_types.html#ft_fixed">FT_Fixed</a>          linearHoriAdvance;
    <a href="ft2-basic_types.html#ft_fixed">FT_Fixed</a>          linearVertAdvance;
    <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>         advance;

    <a href="ft2-basic_types.html#ft_glyph_format">FT_Glyph_Format</a>   format;

    <a href="ft2-basic_types.html#ft_bitmap">FT_Bitmap</a>         bitmap;
    <a href="ft2-basic_types.html#ft_int">FT_Int</a>            bitmap_left;
    <a href="ft2-basic_types.html#ft_int">FT_Int</a>            bitmap_top;

    <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>        outline;

    <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>           num_subglyphs;
    <a href="ft2-base_interface.html#ft_subglyph">FT_SubGlyph</a>       subglyphs;

    <span class="keyword">void</span>*             control_data;
    <span class="keyword">long</span>              control_len;

    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>            lsb_delta;
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>            rsb_delta;

    <span class="keyword">void</span>*             other;

    <a href="ft2-base_interface.html#ft_slot_internal">FT_Slot_Internal</a>  internal;

  } <b>FT_GlyphSlotRec</b>;
</code></pre></div>

<p>FreeType root glyph slot class structure. A glyph slot is a container where individual glyphs can be loaded, be they in outline or bitmap format.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the FreeType library instance this slot belongs to.</p>
</td></tr>
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the parent face object.</p>
</td></tr>
<tr><td class="val" id="next">next</td><td class="desc">
<p>In some cases (like some font tools), several glyph slots per face object can be a good thing. As this is rare, the glyph slots are listed through a direct, single-linked list using its <code>next</code> field.</p>
</td></tr>
<tr><td class="val" id="glyph_index">glyph_index</td><td class="desc">
<p>[Since 2.10] The glyph index passed as an argument to <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> while initializing the glyph slot.</p>
</td></tr>
<tr><td class="val" id="generic">generic</td><td class="desc">
<p>A typeless pointer unused by the FreeType library or any of its drivers. It can be used by client applications to link their own data to each glyph slot object.</p>
</td></tr>
<tr><td class="val" id="metrics">metrics</td><td class="desc">
<p>The metrics of the last loaded glyph in the slot. The returned values depend on the last load flags (see the <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> API function) and can be expressed either in 26.6 fractional pixels or font units.</p>
<p>Note that even when the glyph image is transformed, the metrics are not.</p>
</td></tr>
<tr><td class="val" id="linearhoriadvance">linearHoriAdvance</td><td class="desc">
<p>The advance width of the unhinted glyph. Its value is expressed in 16.16 fractional pixels, unless <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_LINEAR_DESIGN</a></code> is set when loading the glyph. This field can be important to perform correct WYSIWYG layout. Only relevant for outline glyphs.</p>
</td></tr>
<tr><td class="val" id="linearvertadvance">linearVertAdvance</td><td class="desc">
<p>The advance height of the unhinted glyph. Its value is expressed in 16.16 fractional pixels, unless <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_LINEAR_DESIGN</a></code> is set when loading the glyph. This field can be important to perform correct WYSIWYG layout. Only relevant for outline glyphs.</p>
</td></tr>
<tr><td class="val" id="advance">advance</td><td class="desc">
<p>This shorthand is, depending on <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_IGNORE_TRANSFORM</a></code>, the transformed (hinted) advance width for the glyph, in 26.6 fractional pixel format. As specified with <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_VERTICAL_LAYOUT</a></code>, it uses either the <code>horiAdvance</code> or the <code>vertAdvance</code> value of <code>metrics</code> field.</p>
</td></tr>
<tr><td class="val" id="format">format</td><td class="desc">
<p>This field indicates the format of the image contained in the glyph slot. Typically <code><a href="ft2-basic_types.html#ft_glyph_format">FT_GLYPH_FORMAT_BITMAP</a></code>, <code><a href="ft2-basic_types.html#ft_glyph_format">FT_GLYPH_FORMAT_OUTLINE</a></code>, or <code><a href="ft2-basic_types.html#ft_glyph_format">FT_GLYPH_FORMAT_COMPOSITE</a></code>, but other values are possible.</p>
</td></tr>
<tr><td class="val" id="bitmap">bitmap</td><td class="desc">
<p>This field is used as a bitmap descriptor. Note that the address and content of the bitmap buffer can change between calls of <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> and a few other functions.</p>
</td></tr>
<tr><td class="val" id="bitmap_left">bitmap_left</td><td class="desc">
<p>The bitmap's left bearing expressed in integer pixels.</p>
</td></tr>
<tr><td class="val" id="bitmap_top">bitmap_top</td><td class="desc">
<p>The bitmap's top bearing expressed in integer pixels. This is the distance from the baseline to the top-most glyph scanline, upwards y&nbsp;coordinates being <strong>positive</strong>.</p>
</td></tr>
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>The outline descriptor for the current glyph image if its format is <code><a href="ft2-basic_types.html#ft_glyph_format">FT_GLYPH_FORMAT_OUTLINE</a></code>. Once a glyph is loaded, <code>outline</code> can be transformed, distorted, emboldened, etc. However, it must not be freed.</p>
<p>[Since 2.10.1] If <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_SCALE</a></code> is set, outline coordinates of OpenType variation fonts for a selected instance are internally handled as 26.6 fractional font units but returned as (rounded) integers, as expected. To get unrounded font units, don't use <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_SCALE</a></code> but load the glyph with <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_HINTING</a></code> and scale it, using the font's <code>units_per_EM</code> value as the ppem.</p>
</td></tr>
<tr><td class="val" id="num_subglyphs">num_subglyphs</td><td class="desc">
<p>The number of subglyphs in a composite glyph. This field is only valid for the composite glyph format that should normally only be loaded with the <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_RECURSE</a></code> flag.</p>
</td></tr>
<tr><td class="val" id="subglyphs">subglyphs</td><td class="desc">
<p>An array of subglyph descriptors for composite glyphs. There are <code>num_subglyphs</code> elements in there. Currently internal to FreeType.</p>
</td></tr>
<tr><td class="val" id="control_data">control_data</td><td class="desc">
<p>Certain font drivers can also return the control data for a given glyph image (e.g. TrueType bytecode, Type&nbsp;1 charstrings, etc.). This field is a pointer to such data; it is currently internal to FreeType.</p>
</td></tr>
<tr><td class="val" id="control_len">control_len</td><td class="desc">
<p>This is the length in bytes of the control data. Currently internal to FreeType.</p>
</td></tr>
<tr><td class="val" id="other">other</td><td class="desc">
<p>Reserved.</p>
</td></tr>
<tr><td class="val" id="lsb_delta">lsb_delta</td><td class="desc">
<p>The difference between hinted and unhinted left side bearing while auto-hinting is active. Zero otherwise.</p>
</td></tr>
<tr><td class="val" id="rsb_delta">rsb_delta</td><td class="desc">
<p>The difference between hinted and unhinted right side bearing while auto-hinting is active. Zero otherwise.</p>
</td></tr>
</table>

<h4>note</h4>

<p>If <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> is called with default flags (see <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_DEFAULT</a></code>) the glyph image is loaded in the glyph slot in its native format (e.g., an outline glyph for TrueType and Type&nbsp;1 formats). [Since 2.9] The prospective bitmap metrics are calculated according to <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_XXX</a></code> and other flags even for the outline glyph, even if <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_RENDER</a></code> is not set.</p>
<p>This image can later be converted into a bitmap by calling <code><a href="ft2-base_interface.html#ft_render_glyph">FT_Render_Glyph</a></code>. This function searches the current renderer for the native image's format, then invokes it.</p>
<p>The renderer is in charge of transforming the native image through the slot's face transformation fields, then converting it into a bitmap that is returned in <code>slot-&gt;bitmap</code>.</p>
<p>Note that <code>slot-&gt;bitmap_left</code> and <code>slot-&gt;bitmap_top</code> are also used to specify the position of the bitmap relative to the current pen position (e.g., coordinates (0,0) on the baseline). Of course, <code>slot-&gt;format</code> is also changed to <code><a href="ft2-basic_types.html#ft_glyph_format">FT_GLYPH_FORMAT_BITMAP</a></code>.</p>
<p>Here is a small pseudo code fragment that shows how to use <code>lsb_delta</code> and <code>rsb_delta</code> to do fractional positioning of glyphs:
<div class="highlight"><pre><span></span><code>  FT_GlyphSlot  slot     = face-&gt;glyph;
  FT_Pos        origin_x = 0;


  for all glyphs do
    &lt;load glyph with `FT_Load_Glyph&#39;&gt;

    FT_Outline_Translate( slot-&gt;outline, origin_x &amp; 63, 0 );

    &lt;save glyph image, or render glyph, or ...&gt;

    &lt;compute kern between current and next glyph
     and add it to `origin_x&#39;&gt;

    origin_x += slot-&gt;advance.x;
    origin_x += slot-&gt;lsb_delta - slot-&gt;rsb_delta;
  endfor
</code></pre></div></p>
<p>Here is another small pseudo code fragment that shows how to use <code>lsb_delta</code> and <code>rsb_delta</code> to improve integer positioning of glyphs:
<div class="highlight"><pre><span></span><code>  FT_GlyphSlot  slot           = face-&gt;glyph;
  FT_Pos        origin_x       = 0;
  FT_Pos        prev_rsb_delta = 0;


  for all glyphs do
    &lt;compute kern between current and previous glyph
     and add it to `origin_x&#39;&gt;

    &lt;load glyph with `FT_Load_Glyph&#39;&gt;

    if ( prev_rsb_delta - slot-&gt;lsb_delta &gt;  32 )
      origin_x -= 64;
    else if ( prev_rsb_delta - slot-&gt;lsb_delta &lt; -31 )
      origin_x += 64;

    prev_rsb_delta = slot-&gt;rsb_delta;

    &lt;save glyph image, or render glyph, or ...&gt;

    origin_x += slot-&gt;advance.x;
  endfor
</code></pre></div></p>
<p>If you use strong auto-hinting, you <strong>must</strong> apply these delta values! Otherwise you will experience far too large inter-glyph spacing at small rendering sizes in most cases. Note that it doesn't harm to use the above code for other hinting modes also, since the delta values are zero then.</p>
<hr>

<h2 id="ft_glyph_metrics">FT_Glyph_Metrics<a class="headerlink" href="#ft_glyph_metrics" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Glyph_Metrics_
  {
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>  width;
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>  height;

    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>  horiBearingX;
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>  horiBearingY;
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>  horiAdvance;

    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>  vertBearingX;
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>  vertBearingY;
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>  vertAdvance;

  } <b>FT_Glyph_Metrics</b>;
</code></pre></div>

<p>A structure to model the metrics of a single glyph. The values are expressed in 26.6 fractional pixel format; if the flag <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_SCALE</a></code> has been used while loading the glyph, values are expressed in font units instead.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="width">width</td><td class="desc">
<p>The glyph's width.</p>
</td></tr>
<tr><td class="val" id="height">height</td><td class="desc">
<p>The glyph's height.</p>
</td></tr>
<tr><td class="val" id="horibearingx">horiBearingX</td><td class="desc">
<p>Left side bearing for horizontal layout.</p>
</td></tr>
<tr><td class="val" id="horibearingy">horiBearingY</td><td class="desc">
<p>Top side bearing for horizontal layout.</p>
</td></tr>
<tr><td class="val" id="horiadvance">horiAdvance</td><td class="desc">
<p>Advance width for horizontal layout.</p>
</td></tr>
<tr><td class="val" id="vertbearingx">vertBearingX</td><td class="desc">
<p>Left side bearing for vertical layout.</p>
</td></tr>
<tr><td class="val" id="vertbearingy">vertBearingY</td><td class="desc">
<p>Top side bearing for vertical layout. Larger positive values mean further below the vertical glyph origin.</p>
</td></tr>
<tr><td class="val" id="vertadvance">vertAdvance</td><td class="desc">
<p>Advance height for vertical layout. Positive values mean the glyph has a positive advance downward.</p>
</td></tr>
</table>

<h4>note</h4>

<p>If not disabled with <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_HINTING</a></code>, the values represent dimensions of the hinted glyph (in case hinting is applicable).</p>
<p>Stroking a glyph with an outside border does not increase <code>horiAdvance</code> or <code>vertAdvance</code>; you have to manually adjust these values to account for the added width and height.</p>
<p>FreeType doesn't use the &lsquo;VORG&rsquo; table data for CFF fonts because it doesn't have an interface to quickly retrieve the glyph height. The y&nbsp;coordinate of the vertical origin can be simply computed as <code>vertBearingY + height</code> after loading a glyph.</p>
<hr>

<h2 id="ft_subglyph">FT_SubGlyph<a class="headerlink" href="#ft_subglyph" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_SubGlyphRec_*  <b>FT_SubGlyph</b>;
</code></pre></div>

<p>The subglyph structure is an internal object used to describe subglyphs (for example, in the case of composites).</p>
<h4>note</h4>

<p>The subglyph implementation is not part of the high-level API, hence the forward structure declaration.</p>
<p>You can however retrieve subglyph information with <code><a href="ft2-base_interface.html#ft_get_subglyph_info">FT_Get_SubGlyph_Info</a></code>.</p>
<hr>

<h2 id="ft_bitmap_size">FT_Bitmap_Size<a class="headerlink" href="#ft_bitmap_size" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Bitmap_Size_
  {
    <a href="ft2-basic_types.html#ft_short">FT_Short</a>  height;
    <a href="ft2-basic_types.html#ft_short">FT_Short</a>  width;

    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>    size;

    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>    x_ppem;
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>    y_ppem;

  } <b>FT_Bitmap_Size</b>;
</code></pre></div>

<p>This structure models the metrics of a bitmap strike (i.e., a set of glyphs for a given point size and resolution) in a bitmap font. It is used for the <code>available_sizes</code> field of <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code>.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="height">height</td><td class="desc">
<p>The vertical distance, in pixels, between two consecutive baselines. It is always positive.</p>
</td></tr>
<tr><td class="val" id="width">width</td><td class="desc">
<p>The average width, in pixels, of all glyphs in the strike.</p>
</td></tr>
<tr><td class="val" id="size">size</td><td class="desc">
<p>The nominal size of the strike in 26.6 fractional points. This field is not very useful.</p>
</td></tr>
<tr><td class="val" id="x_ppem">x_ppem</td><td class="desc">
<p>The horizontal ppem (nominal width) in 26.6 fractional pixels.</p>
</td></tr>
<tr><td class="val" id="y_ppem">y_ppem</td><td class="desc">
<p>The vertical ppem (nominal height) in 26.6 fractional pixels.</p>
</td></tr>
</table>

<h4>note</h4>

<p>Windows FNT: The nominal size given in a FNT font is not reliable. If the driver finds it incorrect, it sets <code>size</code> to some calculated values, and <code>x_ppem</code> and <code>y_ppem</code> to the pixel width and height given in the font, respectively.</p>
<p>TrueType embedded bitmaps: <code>size</code>, <code>width</code>, and <code>height</code> values are not contained in the bitmap strike itself. They are computed from the global font parameters.</p>
<hr>

<h2 id="ft_init_freetype">FT_Init_FreeType<a class="headerlink" href="#ft_init_freetype" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Init_FreeType</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>  *alibrary );
</code></pre></div>

<p>Initialize a new FreeType library object. The set of modules that are registered by this function is determined at build time.</p>
<h4>output</h4>

<table class="fields">
<tr><td class="val" id="alibrary">alibrary</td><td class="desc">
<p>A handle to a new library object.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>In case you want to provide your own memory allocating routines, use <code><a href="ft2-module_management.html#ft_new_library">FT_New_Library</a></code> instead, followed by a call to <code><a href="ft2-module_management.html#ft_add_default_modules">FT_Add_Default_Modules</a></code> (or a series of calls to <code><a href="ft2-module_management.html#ft_add_module">FT_Add_Module</a></code>) and <code><a href="ft2-module_management.html#ft_set_default_properties">FT_Set_Default_Properties</a></code>.</p>
<p>See the documentation of <code><a href="ft2-base_interface.html#ft_library">FT_Library</a></code> and <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code> for multi-threading issues.</p>
<p>If you need reference-counting (cf. <code><a href="ft2-module_management.html#ft_reference_library">FT_Reference_Library</a></code>), use <code><a href="ft2-module_management.html#ft_new_library">FT_New_Library</a></code> and <code><a href="ft2-module_management.html#ft_done_library">FT_Done_Library</a></code>.</p>
<p>If compilation option <code>FT_CONFIG_OPTION_ENVIRONMENT_PROPERTIES</code> is set, this function reads the <code>FREETYPE_PROPERTIES</code> environment variable to control driver properties. See section &lsquo;<a href="ft2-properties.html#properties">Driver properties</a>&rsquo; for more.</p>
<hr>

<h2 id="ft_done_freetype">FT_Done_FreeType<a class="headerlink" href="#ft_done_freetype" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Done_FreeType</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>  library );
</code></pre></div>

<p>Destroy a given FreeType library object and all of its children, including resources, drivers, faces, sizes, etc.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the target library object.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<hr>

<h2 id="ft_new_face">FT_New_Face<a class="headerlink" href="#ft_new_face" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_New_Face</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>   library,
               <span class="keyword">const</span> <span class="keyword">char</span>*  filepathname,
               <a href="ft2-basic_types.html#ft_long">FT_Long</a>      face_index,
               <a href="ft2-base_interface.html#ft_face">FT_Face</a>     *aface );
</code></pre></div>

<p>Call <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> to open a font by its pathname.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the library resource.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="pathname">pathname</td><td class="desc">
<p>A path to the font file.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>See <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> for a detailed description of this parameter.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="aface">aface</td><td class="desc">
<p>A handle to a new face object. If <code>face_index</code> is greater than or equal to zero, it must be non-<code>NULL</code>.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>Use <code><a href="ft2-base_interface.html#ft_done_face">FT_Done_Face</a></code> to destroy the created <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code> object (along with its slot and sizes).</p>
<hr>

<h2 id="ft_done_face">FT_Done_Face<a class="headerlink" href="#ft_done_face" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Done_Face</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>  face );
</code></pre></div>

<p>Discard a given face object, as well as all of its child slots and sizes.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a target face object.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>See the discussion of reference counters in the description of <code><a href="ft2-base_interface.html#ft_reference_face">FT_Reference_Face</a></code>.</p>
<hr>

<h2 id="ft_reference_face">FT_Reference_Face<a class="headerlink" href="#ft_reference_face" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Reference_Face</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>  face );
</code></pre></div>

<p>A counter gets initialized to&nbsp;1 at the time an <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code> structure is created. This function increments the counter. <code><a href="ft2-base_interface.html#ft_done_face">FT_Done_Face</a></code> then only destroys a face if the counter is&nbsp;1, otherwise it simply decrements the counter.</p>
<p>This function helps in managing life-cycles of structures that reference <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code> objects.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a target face object.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>since</h4>

<p>2.4.2</p>
<hr>

<h2 id="ft_new_memory_face">FT_New_Memory_Face<a class="headerlink" href="#ft_new_memory_face" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_New_Memory_Face</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>      library,
                      <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>*  file_base,
                      <a href="ft2-basic_types.html#ft_long">FT_Long</a>         file_size,
                      <a href="ft2-basic_types.html#ft_long">FT_Long</a>         face_index,
                      <a href="ft2-base_interface.html#ft_face">FT_Face</a>        *aface );
</code></pre></div>

<p>Call <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> to open a font that has been loaded into memory.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the library resource.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="file_base">file_base</td><td class="desc">
<p>A pointer to the beginning of the font data.</p>
</td></tr>
<tr><td class="val" id="file_size">file_size</td><td class="desc">
<p>The size of the memory chunk used by the font data.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>See <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> for a detailed description of this parameter.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="aface">aface</td><td class="desc">
<p>A handle to a new face object. If <code>face_index</code> is greater than or equal to zero, it must be non-<code>NULL</code>.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>You must not deallocate the memory before calling <code><a href="ft2-base_interface.html#ft_done_face">FT_Done_Face</a></code>.</p>
<hr>

<h2 id="ft_face_properties">FT_Face_Properties<a class="headerlink" href="#ft_face_properties" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Face_Properties</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>        face,
                      <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>        num_properties,
                      <a href="ft2-base_interface.html#ft_parameter">FT_Parameter</a>*  properties );
</code></pre></div>

<p>Set or override certain (library or module-wide) properties on a face-by-face basis. Useful for finer-grained control and avoiding locks on shared structures (threads can modify their own faces as they see fit).</p>
<p>Contrary to <code><a href="ft2-module_management.html#ft_property_set">FT_Property_Set</a></code>, this function uses <code><a href="ft2-base_interface.html#ft_parameter">FT_Parameter</a></code> so that you can pass multiple properties to the target face in one call. Note that only a subset of the available properties can be controlled.</p>
<ul>
<li>
<p><code><a href="ft2-parameter_tags.html#ft_param_tag_stem_darkening">FT_PARAM_TAG_STEM_DARKENING</a></code> (stem darkening, corresponding to the property <code>no-stem-darkening</code> provided by the &lsquo;autofit&rsquo;, &lsquo;cff&rsquo;, &lsquo;type1&rsquo;, and &lsquo;t1cid&rsquo; modules; see <code><a href="ft2-properties.html#no-stem-darkening">no-stem-darkening</a></code>).</p>
</li>
<li>
<p><code><a href="ft2-parameter_tags.html#ft_param_tag_lcd_filter_weights">FT_PARAM_TAG_LCD_FILTER_WEIGHTS</a></code> (LCD filter weights, corresponding to function <code><a href="ft2-lcd_rendering.html#ft_library_setlcdfilterweights">FT_Library_SetLcdFilterWeights</a></code>).</p>
</li>
<li>
<p><code><a href="ft2-parameter_tags.html#ft_param_tag_random_seed">FT_PARAM_TAG_RANDOM_SEED</a></code> (seed value for the CFF, Type&nbsp;1, and CID &lsquo;random&rsquo; operator, corresponding to the <code>random-seed</code> property provided by the &lsquo;cff&rsquo;, &lsquo;type1&rsquo;, and &lsquo;t1cid&rsquo; modules; see <code><a href="ft2-properties.html#random-seed">random-seed</a></code>).</p>
</li>
</ul>
<p>Pass <code>NULL</code> as <code>data</code> in <code><a href="ft2-base_interface.html#ft_parameter">FT_Parameter</a></code> for a given tag to reset the option and use the library or module default again.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
<tr><td class="val" id="num_properties">num_properties</td><td class="desc">
<p>The number of properties that follow.</p>
</td></tr>
<tr><td class="val" id="properties">properties</td><td class="desc">
<p>A handle to an <code><a href="ft2-base_interface.html#ft_parameter">FT_Parameter</a></code> array with <code>num_properties</code> elements.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>example</h4>

<p>Here is an example that sets three properties. You must define <code>FT_CONFIG_OPTION_SUBPIXEL_RENDERING</code> to make the LCD filter examples work.
<div class="highlight"><pre><span></span><code>  FT_Parameter         property1;
  FT_Bool              darken_stems = 1;

  FT_Parameter         property2;
  FT_LcdFiveTapFilter  custom_weight =
                         { 0x11, 0x44, 0x56, 0x44, 0x11 };

  FT_Parameter         property3;
  FT_Int32             random_seed = 314159265;

  FT_Parameter         properties[3] = { property1,
                                         property2,
                                         property3 };


  property1.tag  = FT_PARAM_TAG_STEM_DARKENING;
  property1.data = &amp;darken_stems;

  property2.tag  = FT_PARAM_TAG_LCD_FILTER_WEIGHTS;
  property2.data = custom_weight;

  property3.tag  = FT_PARAM_TAG_RANDOM_SEED;
  property3.data = &amp;random_seed;

  FT_Face_Properties( face, 3, properties );
</code></pre></div></p>
<p>The next example resets a single property to its default value.
<div class="highlight"><pre><span></span><code>  FT_Parameter  property;


  property.tag  = FT_PARAM_TAG_LCD_FILTER_WEIGHTS;
  property.data = NULL;

  FT_Face_Properties( face, 1, &amp;property );
</code></pre></div></p>
<h4>since</h4>

<p>2.8</p>
<hr>

<h2 id="ft_open_face">FT_Open_Face<a class="headerlink" href="#ft_open_face" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Open_Face</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>           library,
                <span class="keyword">const</span> <a href="ft2-base_interface.html#ft_open_args">FT_Open_Args</a>*  args,
                <a href="ft2-basic_types.html#ft_long">FT_Long</a>              face_index,
                <a href="ft2-base_interface.html#ft_face">FT_Face</a>             *aface );
</code></pre></div>

<p>Create a face object from a given resource described by <code><a href="ft2-base_interface.html#ft_open_args">FT_Open_Args</a></code>.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the library resource.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="args">args</td><td class="desc">
<p>A pointer to an <code>FT_Open_Args</code> structure that must be filled by the caller.</p>
</td></tr>
<tr><td class="val" id="face_index">face_index</td><td class="desc">
<p>This field holds two different values. Bits 0-15 are the index of the face in the font file (starting with value&nbsp;0). Set it to&nbsp;0 if there is only one face in the font file.</p>
<p>[Since 2.6.1] Bits 16-30 are relevant to GX and OpenType variation fonts only, specifying the named instance index for the current face index (starting with value&nbsp;1; value&nbsp;0 makes FreeType ignore named instances). For non-variation fonts, bits 16-30 are ignored. Assuming that you want to access the third named instance in face&nbsp;4, <code>face_index</code> should be set to 0x00030004. If you want to access face&nbsp;4 without variation handling, simply set <code>face_index</code> to value&nbsp;4.</p>
<p><code>FT_Open_Face</code> and its siblings can be used to quickly check whether the font format of a given font resource is supported by FreeType. In general, if the <code>face_index</code> argument is negative, the function's return value is&nbsp;0 if the font format is recognized, or non-zero otherwise. The function allocates a more or less empty face handle in <code>*aface</code> (if <code>aface</code> isn't <code>NULL</code>); the only two useful fields in this special case are <code>face-&gt;num_faces</code> and <code>face-&gt;style_flags</code>. For any negative value of <code>face_index</code>, <code>face-&gt;num_faces</code> gives the number of faces within the font file. For the negative value &lsquo;-(N+1)&rsquo; (with &lsquo;N&rsquo; a non-negative 16-bit value), bits 16-30 in <code>face-&gt;style_flags</code> give the number of named instances in face &lsquo;N&rsquo; if we have a variation font (or zero otherwise). After examination, the returned <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code> structure should be deallocated with a call to <code><a href="ft2-base_interface.html#ft_done_face">FT_Done_Face</a></code>.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="aface">aface</td><td class="desc">
<p>A handle to a new face object. If <code>face_index</code> is greater than or equal to zero, it must be non-<code>NULL</code>.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>Unlike FreeType 1.x, this function automatically creates a glyph slot for the face object that can be accessed directly through <code>face-&gt;glyph</code>.</p>
<p>Each new face object created with this function also owns a default <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code> object, accessible as <code>face-&gt;size</code>.</p>
<p>One <code><a href="ft2-base_interface.html#ft_library">FT_Library</a></code> instance can have multiple face objects, this is, <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> and its siblings can be called multiple times using the same <code>library</code> argument.</p>
<p>See the discussion of reference counters in the description of <code><a href="ft2-base_interface.html#ft_reference_face">FT_Reference_Face</a></code>.</p>
<h4>example</h4>

<p>To loop over all faces, use code similar to the following snippet (omitting the error handling).
<div class="highlight"><pre><span></span><code>  ...
  FT_Face  face;
  FT_Long  i, num_faces;


  error = FT_Open_Face( library, args, -1, &amp;face );
  if ( error ) { ... }

  num_faces = face-&gt;num_faces;
  FT_Done_Face( face );

  for ( i = 0; i &lt; num_faces; i++ )
  {
    ...
    error = FT_Open_Face( library, args, i, &amp;face );
    ...
    FT_Done_Face( face );
    ...
  }
</code></pre></div></p>
<p>To loop over all valid values for <code>face_index</code>, use something similar to the following snippet, again without error handling. The code accesses all faces immediately (thus only a single call of <code>FT_Open_Face</code> within the do-loop), with and without named instances.
<div class="highlight"><pre><span></span><code>  ...
  FT_Face  face;

  FT_Long  num_faces     = 0;
  FT_Long  num_instances = 0;

  FT_Long  face_idx     = 0;
  FT_Long  instance_idx = 0;


  do
  {
    FT_Long  id = ( instance_idx &lt;&lt; 16 ) + face_idx;


    error = FT_Open_Face( library, args, id, &amp;face );
    if ( error ) { ... }

    num_faces     = face-&gt;num_faces;
    num_instances = face-&gt;style_flags &gt;&gt; 16;

    ...

    FT_Done_Face( face );

    if ( instance_idx &lt; num_instances )
      instance_idx++;
    else
    {
      face_idx++;
      instance_idx = 0;
    }

  } while ( face_idx &lt; num_faces )
</code></pre></div></p>
<hr>

<h2 id="ft_open_args">FT_Open_Args<a class="headerlink" href="#ft_open_args" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Open_Args_
  {
    <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>         flags;
    <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>*  memory_base;
    <a href="ft2-basic_types.html#ft_long">FT_Long</a>         memory_size;
    <a href="ft2-basic_types.html#ft_string">FT_String</a>*      pathname;
    <a href="ft2-system_interface.html#ft_stream">FT_Stream</a>       stream;
    <a href="ft2-module_management.html#ft_module">FT_Module</a>       driver;
    <a href="ft2-basic_types.html#ft_int">FT_Int</a>          num_params;
    <a href="ft2-base_interface.html#ft_parameter">FT_Parameter</a>*   params;

  } <b>FT_Open_Args</b>;
</code></pre></div>

<p>A structure to indicate how to open a new font file or stream. A pointer to such a structure can be used as a parameter for the functions <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> and <code><a href="ft2-base_interface.html#ft_attach_stream">FT_Attach_Stream</a></code>.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="flags">flags</td><td class="desc">
<p>A set of bit flags indicating how to use the structure.</p>
</td></tr>
<tr><td class="val" id="memory_base">memory_base</td><td class="desc">
<p>The first byte of the file in memory.</p>
</td></tr>
<tr><td class="val" id="memory_size">memory_size</td><td class="desc">
<p>The size in bytes of the file in memory.</p>
</td></tr>
<tr><td class="val" id="pathname">pathname</td><td class="desc">
<p>A pointer to an 8-bit file pathname. The pointer is not owned by FreeType.</p>
</td></tr>
<tr><td class="val" id="stream">stream</td><td class="desc">
<p>A handle to a source stream object.</p>
</td></tr>
<tr><td class="val" id="driver">driver</td><td class="desc">
<p>This field is exclusively used by <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code>; it simply specifies the font driver to use for opening the face. If set to <code>NULL</code>, FreeType tries to load the face with each one of the drivers in its list.</p>
</td></tr>
<tr><td class="val" id="num_params">num_params</td><td class="desc">
<p>The number of extra parameters.</p>
</td></tr>
<tr><td class="val" id="params">params</td><td class="desc">
<p>Extra parameters passed to the font driver when opening a new face.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The stream type is determined by the contents of <code>flags</code> that are tested in the following order by <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code>:</p>
<p>If the <code><a href="ft2-base_interface.html#ft_open_xxx">FT_OPEN_MEMORY</a></code> bit is set, assume that this is a memory file of <code>memory_size</code> bytes, located at <code>memory_address</code>. The data are not copied, and the client is responsible for releasing and destroying them <em>after</em> the corresponding call to <code><a href="ft2-base_interface.html#ft_done_face">FT_Done_Face</a></code>.</p>
<p>Otherwise, if the <code><a href="ft2-base_interface.html#ft_open_xxx">FT_OPEN_STREAM</a></code> bit is set, assume that a custom input stream <code>stream</code> is used.</p>
<p>Otherwise, if the <code><a href="ft2-base_interface.html#ft_open_xxx">FT_OPEN_PATHNAME</a></code> bit is set, assume that this is a normal file and use <code>pathname</code> to open it.</p>
<p>If the <code><a href="ft2-base_interface.html#ft_open_xxx">FT_OPEN_DRIVER</a></code> bit is set, <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> only tries to open the file with the driver whose handler is in <code>driver</code>.</p>
<p>If the <code><a href="ft2-base_interface.html#ft_open_xxx">FT_OPEN_PARAMS</a></code> bit is set, the parameters given by <code>num_params</code> and <code>params</code> is used. They are ignored otherwise.</p>
<p>Ideally, both the <code>pathname</code> and <code>params</code> fields should be tagged as &lsquo;const&rsquo;; this is missing for API backward compatibility. In other words, applications should treat them as read-only.</p>
<hr>

<h2 id="ft_parameter">FT_Parameter<a class="headerlink" href="#ft_parameter" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Parameter_
  {
    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>    tag;
    <a href="ft2-basic_types.html#ft_pointer">FT_Pointer</a>  data;

  } <b>FT_Parameter</b>;
</code></pre></div>

<p>A simple structure to pass more or less generic parameters to <code><a href="ft2-base_interface.html#ft_open_face">FT_Open_Face</a></code> and <code><a href="ft2-base_interface.html#ft_face_properties">FT_Face_Properties</a></code>.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="tag">tag</td><td class="desc">
<p>A four-byte identification tag.</p>
</td></tr>
<tr><td class="val" id="data">data</td><td class="desc">
<p>A pointer to the parameter data.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The ID and function of parameters are driver-specific. See section &lsquo;<a href="ft2-parameter_tags.html#parameter_tags">Parameter Tags</a>&rsquo; for more information.</p>
<hr>

<h2 id="ft_attach_file">FT_Attach_File<a class="headerlink" href="#ft_attach_file" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Attach_File</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>      face,
                  <span class="keyword">const</span> <span class="keyword">char</span>*  filepathname );
</code></pre></div>

<p>Call <code><a href="ft2-base_interface.html#ft_attach_stream">FT_Attach_Stream</a></code> to attach a file.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>The target face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="filepathname">filepathname</td><td class="desc">
<p>The pathname.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<hr>

<h2 id="ft_attach_stream">FT_Attach_Stream<a class="headerlink" href="#ft_attach_stream" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Attach_Stream</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>        face,
                    <a href="ft2-base_interface.html#ft_open_args">FT_Open_Args</a>*  parameters );
</code></pre></div>

<p>&lsquo;Attach&rsquo; data to a face object. Normally, this is used to read additional information for the face object. For example, you can attach an AFM file that comes with a Type&nbsp;1 font to get the kerning values and other metrics.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>The target face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="parameters">parameters</td><td class="desc">
<p>A pointer to <code><a href="ft2-base_interface.html#ft_open_args">FT_Open_Args</a></code> that must be filled by the caller.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>The meaning of the &lsquo;attach&rsquo; (i.e., what really happens when the new file is read) is not fixed by FreeType itself. It really depends on the font format (and thus the font driver).</p>
<p>Client applications are expected to know what they are doing when invoking this function. Most drivers simply do not implement file or stream attachments.</p>
<hr>

<h2 id="ft_set_char_size">FT_Set_Char_Size<a class="headerlink" href="#ft_set_char_size" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Set_Char_Size</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>     face,
                    <a href="ft2-basic_types.html#ft_f26dot6">FT_F26Dot6</a>  char_width,
                    <a href="ft2-basic_types.html#ft_f26dot6">FT_F26Dot6</a>  char_height,
                    <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>     horz_resolution,
                    <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>     vert_resolution );
</code></pre></div>

<p>Call <code><a href="ft2-base_interface.html#ft_request_size">FT_Request_Size</a></code> to request the nominal size (in points).</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a target face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="char_width">char_width</td><td class="desc">
<p>The nominal width, in 26.6 fractional points.</p>
</td></tr>
<tr><td class="val" id="char_height">char_height</td><td class="desc">
<p>The nominal height, in 26.6 fractional points.</p>
</td></tr>
<tr><td class="val" id="horz_resolution">horz_resolution</td><td class="desc">
<p>The horizontal resolution in dpi.</p>
</td></tr>
<tr><td class="val" id="vert_resolution">vert_resolution</td><td class="desc">
<p>The vertical resolution in dpi.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>While this function allows fractional points as input values, the resulting ppem value for the given resolution is always rounded to the nearest integer.</p>
<p>If either the character width or height is zero, it is set equal to the other value.</p>
<p>If either the horizontal or vertical resolution is zero, it is set equal to the other value.</p>
<p>A character width or height smaller than 1pt is set to 1pt; if both resolution values are zero, they are set to 72dpi.</p>
<p>Don't use this function if you are using the FreeType cache API.</p>
<hr>

<h2 id="ft_set_pixel_sizes">FT_Set_Pixel_Sizes<a class="headerlink" href="#ft_set_pixel_sizes" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Set_Pixel_Sizes</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>  face,
                      <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>  pixel_width,
                      <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>  pixel_height );
</code></pre></div>

<p>Call <code><a href="ft2-base_interface.html#ft_request_size">FT_Request_Size</a></code> to request the nominal size (in pixels).</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the target face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="pixel_width">pixel_width</td><td class="desc">
<p>The nominal width, in pixels.</p>
</td></tr>
<tr><td class="val" id="pixel_height">pixel_height</td><td class="desc">
<p>The nominal height, in pixels.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>You should not rely on the resulting glyphs matching or being constrained to this pixel size. Refer to <code><a href="ft2-base_interface.html#ft_request_size">FT_Request_Size</a></code> to understand how requested sizes relate to actual sizes.</p>
<p>Don't use this function if you are using the FreeType cache API.</p>
<hr>

<h2 id="ft_request_size">FT_Request_Size<a class="headerlink" href="#ft_request_size" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Request_Size</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>          face,
                   <a href="ft2-base_interface.html#ft_size_request">FT_Size_Request</a>  req );
</code></pre></div>

<p>Resize the scale of the active <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code> object in a face.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a target face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="req">req</td><td class="desc">
<p>A pointer to a <code><a href="ft2-base_interface.html#ft_size_requestrec">FT_Size_RequestRec</a></code>.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>Although drivers may select the bitmap strike matching the request, you should not rely on this if you intend to select a particular bitmap strike. Use <code><a href="ft2-base_interface.html#ft_select_size">FT_Select_Size</a></code> instead in that case.</p>
<p>The relation between the requested size and the resulting glyph size is dependent entirely on how the size is defined in the source face. The font designer chooses the final size of each glyph relative to this size. For more information refer to &lsquo;<a href="https://www.freetype.org/freetype2/docs/glyphs/glyphs-2.html">https://www.freetype.org/freetype2/docs/glyphs/glyphs-2.html</a>&rsquo;.</p>
<p>Contrary to <code><a href="ft2-base_interface.html#ft_set_char_size">FT_Set_Char_Size</a></code>, this function doesn't have special code to normalize zero-valued widths, heights, or resolutions (which lead to errors in most cases).</p>
<p>Don't use this function if you are using the FreeType cache API.</p>
<hr>

<h2 id="ft_select_size">FT_Select_Size<a class="headerlink" href="#ft_select_size" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Select_Size</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>  face,
                  <a href="ft2-basic_types.html#ft_int">FT_Int</a>   strike_index );
</code></pre></div>

<p>Select a bitmap strike. To be more precise, this function sets the scaling factors of the active <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code> object in a face so that bitmaps from this particular strike are taken by <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> and friends.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a target face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="strike_index">strike_index</td><td class="desc">
<p>The index of the bitmap strike in the <code>available_sizes</code> field of <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> structure.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>For bitmaps embedded in outline fonts it is common that only a subset of the available glyphs at a given ppem value is available. FreeType silently uses outlines if there is no bitmap for a given glyph index.</p>
<p>For GX and OpenType variation fonts, a bitmap strike makes sense only if the default instance is active (this is, no glyph variation takes place); otherwise, FreeType simply ignores bitmap strikes. The same is true for all named instances that are different from the default instance.</p>
<p>Don't use this function if you are using the FreeType cache API.</p>
<hr>

<h2 id="ft_size_request_type">FT_Size_Request_Type<a class="headerlink" href="#ft_size_request_type" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Size_Request_Type_
  {
    <a href="ft2-base_interface.html#ft_size_request_type_nominal">FT_SIZE_REQUEST_TYPE_NOMINAL</a>,
    <a href="ft2-base_interface.html#ft_size_request_type_real_dim">FT_SIZE_REQUEST_TYPE_REAL_DIM</a>,
    <a href="ft2-base_interface.html#ft_size_request_type_bbox">FT_SIZE_REQUEST_TYPE_BBOX</a>,
    <a href="ft2-base_interface.html#ft_size_request_type_cell">FT_SIZE_REQUEST_TYPE_CELL</a>,
    <a href="ft2-base_interface.html#ft_size_request_type_scales">FT_SIZE_REQUEST_TYPE_SCALES</a>,

    FT_SIZE_REQUEST_TYPE_MAX

  } <b>FT_Size_Request_Type</b>;
</code></pre></div>

<p>An enumeration type that lists the supported size request types, i.e., what input size (in font units) maps to the requested output size (in pixels, as computed from the arguments of <code><a href="ft2-base_interface.html#ft_size_request">FT_Size_Request</a></code>).</p>
<h4>values</h4>

<table class="fields long">
<tr><td class="val" id="ft_size_request_type_nominal">FT_SIZE_REQUEST_TYPE_NOMINAL</td><td class="desc">
<p>The nominal size. The <code>units_per_EM</code> field of <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> is used to determine both scaling values.</p>
<p>This is the standard scaling found in most applications. In particular, use this size request type for TrueType fonts if they provide optical scaling or something similar. Note, however, that <code>units_per_EM</code> is a rather abstract value which bears no relation to the actual size of the glyphs in a font.</p>
</td></tr>
<tr><td class="val" id="ft_size_request_type_real_dim">FT_SIZE_REQUEST_TYPE_REAL_DIM</td><td class="desc">
<p>The real dimension. The sum of the <code>ascender</code> and (minus of) the <code>descender</code> fields of <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> is used to determine both scaling values.</p>
</td></tr>
<tr><td class="val" id="ft_size_request_type_bbox">FT_SIZE_REQUEST_TYPE_BBOX</td><td class="desc">
<p>The font bounding box. The width and height of the <code>bbox</code> field of <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> are used to determine the horizontal and vertical scaling value, respectively.</p>
</td></tr>
<tr><td class="val" id="ft_size_request_type_cell">FT_SIZE_REQUEST_TYPE_CELL</td><td class="desc">
<p>The <code>max_advance_width</code> field of <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> is used to determine the horizontal scaling value; the vertical scaling value is determined the same way as <code><a href="ft2-base_interface.html#ft_size_request_type">FT_SIZE_REQUEST_TYPE_REAL_DIM</a></code> does. Finally, both scaling values are set to the smaller one. This type is useful if you want to specify the font size for, say, a window of a given dimension and 80x24 cells.</p>
</td></tr>
<tr><td class="val" id="ft_size_request_type_scales">FT_SIZE_REQUEST_TYPE_SCALES</td><td class="desc">
<p>Specify the scaling values directly.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The above descriptions only apply to scalable formats. For bitmap formats, the behaviour is up to the driver.</p>
<p>See the note section of <code><a href="ft2-base_interface.html#ft_size_metrics">FT_Size_Metrics</a></code> if you wonder how size requesting relates to scaling values.</p>
<hr>

<h2 id="ft_size_requestrec">FT_Size_RequestRec<a class="headerlink" href="#ft_size_requestrec" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Size_RequestRec_
  {
    <a href="ft2-base_interface.html#ft_size_request_type">FT_Size_Request_Type</a>  type;
    <a href="ft2-basic_types.html#ft_long">FT_Long</a>               width;
    <a href="ft2-basic_types.html#ft_long">FT_Long</a>               height;
    <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>               horiResolution;
    <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>               vertResolution;

  } <b>FT_Size_RequestRec</b>;
</code></pre></div>

<p>A structure to model a size request.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="type">type</td><td class="desc">
<p>See <code><a href="ft2-base_interface.html#ft_size_request_type">FT_Size_Request_Type</a></code>.</p>
</td></tr>
<tr><td class="val" id="width">width</td><td class="desc">
<p>The desired width, given as a 26.6 fractional point value (with 72pt = 1in).</p>
</td></tr>
<tr><td class="val" id="height">height</td><td class="desc">
<p>The desired height, given as a 26.6 fractional point value (with 72pt = 1in).</p>
</td></tr>
<tr><td class="val" id="horiresolution">horiResolution</td><td class="desc">
<p>The horizontal resolution (dpi, i.e., pixels per inch). If set to zero, <code>width</code> is treated as a 26.6 fractional <strong>pixel</strong> value, which gets internally rounded to an integer.</p>
</td></tr>
<tr><td class="val" id="vertresolution">vertResolution</td><td class="desc">
<p>The vertical resolution (dpi, i.e., pixels per inch). If set to zero, <code>height</code> is treated as a 26.6 fractional <strong>pixel</strong> value, which gets internally rounded to an integer.</p>
</td></tr>
</table>

<h4>note</h4>

<p>If <code>width</code> is zero, the horizontal scaling value is set equal to the vertical scaling value, and vice versa.</p>
<p>If <code>type</code> is <code>FT_SIZE_REQUEST_TYPE_SCALES</code>, <code>width</code> and <code>height</code> are interpreted directly as 16.16 fractional scaling values, without any further modification, and both <code>horiResolution</code> and <code>vertResolution</code> are ignored.</p>
<hr>

<h2 id="ft_size_request">FT_Size_Request<a class="headerlink" href="#ft_size_request" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Size_RequestRec_  *<b>FT_Size_Request</b>;
</code></pre></div>

<p>A handle to a size request structure.</p>
<hr>

<h2 id="ft_set_transform">FT_Set_Transform<a class="headerlink" href="#ft_set_transform" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Set_Transform</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>     face,
                    <a href="ft2-basic_types.html#ft_matrix">FT_Matrix</a>*  matrix,
                    <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  delta );
</code></pre></div>

<p>Set the transformation that is applied to glyph images when they are loaded into a glyph slot through <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code>.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="matrix">matrix</td><td class="desc">
<p>A pointer to the transformation's 2x2 matrix. Use <code>NULL</code> for the identity matrix.</p>
</td></tr>
<tr><td class="val" id="delta">delta</td><td class="desc">
<p>A pointer to the translation vector. Use <code>NULL</code> for the null vector.</p>
</td></tr>
</table>

<h4>note</h4>

<p>This function is provided as a convenience, but keep in mind that <code><a href="ft2-basic_types.html#ft_matrix">FT_Matrix</a></code> coefficients are only 16.16 fixed point values, which can limit the accuracy of the results. Using floating-point computations to perform the transform directly in client code instead will always yield better numbers.</p>
<p>The transformation is only applied to scalable image formats after the glyph has been loaded. It means that hinting is unaltered by the transformation and is performed on the character size given in the last call to <code><a href="ft2-base_interface.html#ft_set_char_size">FT_Set_Char_Size</a></code> or <code><a href="ft2-base_interface.html#ft_set_pixel_sizes">FT_Set_Pixel_Sizes</a></code>.</p>
<p>Note that this also transforms the <code>face.glyph.advance</code> field, but <strong>not</strong> the values in <code>face.glyph.metrics</code>.</p>
<hr>

<h2 id="ft_load_glyph">FT_Load_Glyph<a class="headerlink" href="#ft_load_glyph" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Load_Glyph</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>   face,
                 <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>   glyph_index,
                 <a href="ft2-basic_types.html#ft_int32">FT_Int32</a>  load_flags );
</code></pre></div>

<p>Load a glyph into the glyph slot of a face object.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the target face object where the glyph is loaded.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="glyph_index">glyph_index</td><td class="desc">
<p>The index of the glyph in the font file. For CID-keyed fonts (either in PS or in CFF format) this argument specifies the CID value.</p>
</td></tr>
<tr><td class="val" id="load_flags">load_flags</td><td class="desc">
<p>A flag indicating what to load for this glyph. The <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_XXX</a></code> constants can be used to control the glyph loading process (e.g., whether the outline should be scaled, whether to load bitmaps or not, whether to hint the outline, etc).</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>The loaded glyph may be transformed. See <code><a href="ft2-base_interface.html#ft_set_transform">FT_Set_Transform</a></code> for the details.</p>
<p>For subsetted CID-keyed fonts, <code>FT_Err_Invalid_Argument</code> is returned for invalid CID values (this is, for CID values that don't have a corresponding glyph in the font). See the discussion of the <code><a href="ft2-base_interface.html#ft_face_flag_xxx">FT_FACE_FLAG_CID_KEYED</a></code> flag for more details.</p>
<p>If you receive <code>FT_Err_Glyph_Too_Big</code>, try getting the glyph outline at EM size, then scale it manually and fill it as a graphics operation.</p>
<hr>

<h2 id="ft_get_char_index">FT_Get_Char_Index<a class="headerlink" href="#ft_get_char_index" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_uint">FT_UInt</a> )
  <b>FT_Get_Char_Index</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>   face,
                     <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>  charcode );
</code></pre></div>

<p>Return the glyph index of a given character code. This function uses the currently selected charmap to do the mapping.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
<tr><td class="val" id="charcode">charcode</td><td class="desc">
<p>The character code.</p>
</td></tr>
</table>

<h4>return</h4>

<p>The glyph index. 0&nbsp;means &lsquo;undefined character code&rsquo;.</p>
<h4>note</h4>

<p>If you use FreeType to manipulate the contents of font files directly, be aware that the glyph index returned by this function doesn't always correspond to the internal indices used within the file. This is done to ensure that value&nbsp;0 always corresponds to the &lsquo;missing glyph&rsquo;. If the first glyph is not named &lsquo;.notdef&rsquo;, then for Type&nbsp;1 and Type&nbsp;42 fonts, &lsquo;.notdef&rsquo; will be moved into the glyph ID&nbsp;0 position, and whatever was there will be moved to the position &lsquo;.notdef&rsquo; had. For Type&nbsp;1 fonts, if there is no &lsquo;.notdef&rsquo; glyph at all, then one will be created at index&nbsp;0 and whatever was there will be moved to the last index -- Type&nbsp;42 fonts are considered invalid under this condition.</p>
<hr>

<h2 id="ft_get_first_char">FT_Get_First_Char<a class="headerlink" href="#ft_get_first_char" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a> )
  <b>FT_Get_First_Char</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>   face,
                     <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>  *agindex );
</code></pre></div>

<p>Return the first character code in the current charmap of a given face, together with its corresponding glyph index.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="agindex">agindex</td><td class="desc">
<p>Glyph index of first character code. 0&nbsp;if charmap is empty.</p>
</td></tr>
</table>

<h4>return</h4>

<p>The charmap's first character code.</p>
<h4>note</h4>

<p>You should use this function together with <code><a href="ft2-base_interface.html#ft_get_next_char">FT_Get_Next_Char</a></code> to parse all character codes available in a given charmap. The code should look like this:
<div class="highlight"><pre><span></span><code>  FT_ULong  charcode;
  FT_UInt   gindex;


  charcode = FT_Get_First_Char( face, &amp;gindex );
  while ( gindex != 0 )
  {
    ... do something with (charcode,gindex) pair ...

    charcode = FT_Get_Next_Char( face, charcode, &amp;gindex );
  }
</code></pre></div></p>
<p>Be aware that character codes can have values up to 0xFFFFFFFF; this might happen for non-Unicode or malformed cmaps. However, even with regular Unicode encoding, so-called &lsquo;last resort fonts&rsquo; (using SFNT cmap format 13, see function <code><a href="ft2-truetype_tables.html#ft_get_cmap_format">FT_Get_CMap_Format</a></code>) normally have entries for all Unicode characters up to 0x1FFFFF, which can cause <em>a lot</em> of iterations.</p>
<p>Note that <code>*agindex</code> is set to&nbsp;0 if the charmap is empty. The result itself can be&nbsp;0 in two cases: if the charmap is empty or if the value&nbsp;0 is the first valid character code.</p>
<hr>

<h2 id="ft_get_next_char">FT_Get_Next_Char<a class="headerlink" href="#ft_get_next_char" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a> )
  <b>FT_Get_Next_Char</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>    face,
                    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>   char_code,
                    <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>   *agindex );
</code></pre></div>

<p>Return the next character code in the current charmap of a given face following the value <code>char_code</code>, as well as the corresponding glyph index.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
<tr><td class="val" id="char_code">char_code</td><td class="desc">
<p>The starting character code.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="agindex">agindex</td><td class="desc">
<p>Glyph index of next character code. 0&nbsp;if charmap is empty.</p>
</td></tr>
</table>

<h4>return</h4>

<p>The charmap's next character code.</p>
<h4>note</h4>

<p>You should use this function with <code><a href="ft2-base_interface.html#ft_get_first_char">FT_Get_First_Char</a></code> to walk over all character codes available in a given charmap. See the note for that function for a simple code example.</p>
<p>Note that <code>*agindex</code> is set to&nbsp;0 when there are no more codes in the charmap.</p>
<hr>

<h2 id="ft_get_name_index">FT_Get_Name_Index<a class="headerlink" href="#ft_get_name_index" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_uint">FT_UInt</a> )
  <b>FT_Get_Name_Index</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>           face,
                     <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_string">FT_String</a>*  glyph_name );
</code></pre></div>

<p>Return the glyph index of a given glyph name.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
<tr><td class="val" id="glyph_name">glyph_name</td><td class="desc">
<p>The glyph name.</p>
</td></tr>
</table>

<h4>return</h4>

<p>The glyph index. 0&nbsp;means &lsquo;undefined character code&rsquo;.</p>
<hr>

<h2 id="ft_load_char">FT_Load_Char<a class="headerlink" href="#ft_load_char" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Load_Char</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>   face,
                <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>  char_code,
                <a href="ft2-basic_types.html#ft_int32">FT_Int32</a>  load_flags );
</code></pre></div>

<p>Load a glyph into the glyph slot of a face object, accessed by its character code.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a target face object where the glyph is loaded.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="char_code">char_code</td><td class="desc">
<p>The glyph's character code, according to the current charmap used in the face.</p>
</td></tr>
<tr><td class="val" id="load_flags">load_flags</td><td class="desc">
<p>A flag indicating what to load for this glyph. The <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_XXX</a></code> constants can be used to control the glyph loading process (e.g., whether the outline should be scaled, whether to load bitmaps or not, whether to hint the outline, etc).</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>This function simply calls <code><a href="ft2-base_interface.html#ft_get_char_index">FT_Get_Char_Index</a></code> and <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code>.</p>
<p>Many fonts contain glyphs that can't be loaded by this function since its glyph indices are not listed in any of the font's charmaps.</p>
<p>If no active cmap is set up (i.e., <code>face-&gt;charmap</code> is zero), the call to <code><a href="ft2-base_interface.html#ft_get_char_index">FT_Get_Char_Index</a></code> is omitted, and the function behaves identically to <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code>.</p>
<hr>

<h2 id="ft_load_target_mode">FT_LOAD_TARGET_MODE<a class="headerlink" href="#ft_load_target_mode" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_LOAD_TARGET_MODE</b>( x )  ( (<a href="ft2-base_interface.html#ft_render_mode">FT_Render_Mode</a>)( ( (x) &gt;&gt; 16 ) &amp; 15 ) )
</code></pre></div>

<p>Return the <code><a href="ft2-base_interface.html#ft_render_mode">FT_Render_Mode</a></code> corresponding to a given <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_XXX</a></code> value.</p>
<hr>

<h2 id="ft_render_glyph">FT_Render_Glyph<a class="headerlink" href="#ft_render_glyph" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Render_Glyph</b>( <a href="ft2-base_interface.html#ft_glyphslot">FT_GlyphSlot</a>    slot,
                   <a href="ft2-base_interface.html#ft_render_mode">FT_Render_Mode</a>  render_mode );
</code></pre></div>

<p>Convert a given glyph image to a bitmap. It does so by inspecting the glyph image format, finding the relevant renderer, and invoking it.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="slot">slot</td><td class="desc">
<p>A handle to the glyph slot containing the image to convert.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="render_mode">render_mode</td><td class="desc">
<p>The render mode used to render the glyph image into a bitmap. See <code><a href="ft2-base_interface.html#ft_render_mode">FT_Render_Mode</a></code> for a list of possible values.</p>
<p>If <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_NORMAL</a></code> is used, a previous call of <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> with flag <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_COLOR</a></code> makes FT_Render_Glyph provide a default blending of colored glyph layers associated with the current glyph slot (provided the font contains such layers) instead of rendering the glyph slot's outline. This is an experimental feature; see <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_COLOR</a></code> for more information.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>To get meaningful results, font scaling values must be set with functions like <code><a href="ft2-base_interface.html#ft_set_char_size">FT_Set_Char_Size</a></code> before calling <code>FT_Render_Glyph</code>.</p>
<p>When FreeType outputs a bitmap of a glyph, it really outputs an alpha coverage map. If a pixel is completely covered by a filled-in outline, the bitmap contains 0xFF at that pixel, meaning that 0xFF/0xFF fraction of that pixel is covered, meaning the pixel is 100% black (or 0% bright). If a pixel is only 50% covered (value 0x80), the pixel is made 50% black (50% bright or a middle shade of grey). 0% covered means 0% black (100% bright or white).</p>
<p>On high-DPI screens like on smartphones and tablets, the pixels are so small that their chance of being completely covered and therefore completely black are fairly good. On the low-DPI screens, however, the situation is different. The pixels are too large for most of the details of a glyph and shades of gray are the norm rather than the exception.</p>
<p>This is relevant because all our screens have a second problem: they are not linear. 1&nbsp;+&nbsp;1 is not&nbsp;2. Twice the value does not result in twice the brightness. When a pixel is only 50% covered, the coverage map says 50% black, and this translates to a pixel value of 128 when you use 8&nbsp;bits per channel (0-255). However, this does not translate to 50% brightness for that pixel on our sRGB and gamma&nbsp;2.2 screens. Due to their non-linearity, they dwell longer in the darks and only a pixel value of about 186 results in 50% brightness -- 128 ends up too dark on both bright and dark backgrounds. The net result is that dark text looks burnt-out, pixely and blotchy on bright background, bright text too frail on dark backgrounds, and colored text on colored background (for example, red on green) seems to have dark halos or &lsquo;dirt&rsquo; around it. The situation is especially ugly for diagonal stems like in &lsquo;w&rsquo; glyph shapes where the quality of FreeType's anti-aliasing depends on the correct display of grays. On high-DPI screens where smaller, fully black pixels reign supreme, this doesn't matter, but on our low-DPI screens with all the gray shades, it does. 0% and 100% brightness are the same things in linear and non-linear space, just all the shades in-between aren't.</p>
<p>The blending function for placing text over a background is
<div class="highlight"><pre><span></span><code>  dst = alpha * src + (1 - alpha) * dst    ,
</code></pre></div></p>
<p>which is known as the OVER operator.</p>
<p>To correctly composite an antialiased pixel of a glyph onto a surface,</p>
<ol>
<li>
<p>take the foreground and background colors (e.g., in sRGB space) and apply gamma to get them in a linear space,</p>
</li>
<li>
<p>use OVER to blend the two linear colors using the glyph pixel as the alpha value (remember, the glyph bitmap is an alpha coverage bitmap), and</p>
</li>
<li>
<p>apply inverse gamma to the blended pixel and write it back to the image.</p>
</li>
</ol>
<p>Internal testing at Adobe found that a target inverse gamma of&nbsp;1.8 for step&nbsp;3 gives good results across a wide range of displays with an sRGB gamma curve or a similar one.</p>
<p>This process can cost performance. There is an approximation that does not need to know about the background color; see <a href="https://bel.fi/alankila/lcd/">https://bel.fi/alankila/lcd/</a> and <a href="https://bel.fi/alankila/lcd/alpcor.html">https://bel.fi/alankila/lcd/alpcor.html</a> for details.</p>
<p><strong>ATTENTION</strong>: Linear blending is even more important when dealing with subpixel-rendered glyphs to prevent color-fringing! A subpixel-rendered glyph must first be filtered with a filter that gives equal weight to the three color primaries and does not exceed a sum of 0x100, see section &lsquo;<a href="ft2-lcd_rendering.html#lcd_rendering">Subpixel Rendering</a>&rsquo;. Then the only difference to gray linear blending is that subpixel-rendered linear blending is done 3&nbsp;times per pixel: red foreground subpixel to red background subpixel and so on for green and blue.</p>
<hr>

<h2 id="ft_render_mode">FT_Render_Mode<a class="headerlink" href="#ft_render_mode" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Render_Mode_
  {
    <a href="ft2-base_interface.html#ft_render_mode_normal">FT_RENDER_MODE_NORMAL</a> = 0,
    <a href="ft2-base_interface.html#ft_render_mode_light">FT_RENDER_MODE_LIGHT</a>,
    <a href="ft2-base_interface.html#ft_render_mode_mono">FT_RENDER_MODE_MONO</a>,
    <a href="ft2-base_interface.html#ft_render_mode_lcd">FT_RENDER_MODE_LCD</a>,
    <a href="ft2-base_interface.html#ft_render_mode_lcd_v">FT_RENDER_MODE_LCD_V</a>,

    FT_RENDER_MODE_MAX

  } <b>FT_Render_Mode</b>;


  /* these constants are deprecated; use the corresponding */
  /* `<b>FT_Render_Mode</b>` values instead                       */
#<span class="keyword">define</span> ft_render_mode_normal  <a href="ft2-base_interface.html#ft_render_mode_normal">FT_RENDER_MODE_NORMAL</a>
#<span class="keyword">define</span> ft_render_mode_mono    <a href="ft2-base_interface.html#ft_render_mode_mono">FT_RENDER_MODE_MONO</a>
</code></pre></div>

<p>Render modes supported by FreeType&nbsp;2. Each mode corresponds to a specific type of scanline conversion performed on the outline.</p>
<p>For bitmap fonts and embedded bitmaps the <code>bitmap-&gt;pixel_mode</code> field in the <code><a href="ft2-base_interface.html#ft_glyphslotrec">FT_GlyphSlotRec</a></code> structure gives the format of the returned bitmap.</p>
<p>All modes except <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_MONO</a></code> use 256 levels of opacity, indicating pixel coverage. Use linear alpha blending and gamma correction to correctly render non-monochrome glyph bitmaps onto a surface; see <code><a href="ft2-base_interface.html#ft_render_glyph">FT_Render_Glyph</a></code>.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_render_mode_normal">FT_RENDER_MODE_NORMAL</td><td class="desc">
<p>Default render mode; it corresponds to 8-bit anti-aliased bitmaps.</p>
</td></tr>
<tr><td class="val" id="ft_render_mode_light">FT_RENDER_MODE_LIGHT</td><td class="desc">
<p>This is equivalent to <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_NORMAL</a></code>. It is only defined as a separate value because render modes are also used indirectly to define hinting algorithm selectors. See <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_XXX</a></code> for details.</p>
</td></tr>
<tr><td class="val" id="ft_render_mode_mono">FT_RENDER_MODE_MONO</td><td class="desc">
<p>This mode corresponds to 1-bit bitmaps (with 2&nbsp;levels of opacity).</p>
</td></tr>
<tr><td class="val" id="ft_render_mode_lcd">FT_RENDER_MODE_LCD</td><td class="desc">
<p>This mode corresponds to horizontal RGB and BGR subpixel displays like LCD screens. It produces 8-bit bitmaps that are 3&nbsp;times the width of the original glyph outline in pixels, and which use the <code><a href="ft2-basic_types.html#ft_pixel_mode">FT_PIXEL_MODE_LCD</a></code> mode.</p>
</td></tr>
<tr><td class="val" id="ft_render_mode_lcd_v">FT_RENDER_MODE_LCD_V</td><td class="desc">
<p>This mode corresponds to vertical RGB and BGR subpixel displays (like PDA screens, rotated LCD displays, etc.). It produces 8-bit bitmaps that are 3&nbsp;times the height of the original glyph outline in pixels and use the <code><a href="ft2-basic_types.html#ft_pixel_mode">FT_PIXEL_MODE_LCD_V</a></code> mode.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The selected render mode only affects vector glyphs of a font. Embedded bitmaps often have a different pixel mode like <code><a href="ft2-basic_types.html#ft_pixel_mode">FT_PIXEL_MODE_MONO</a></code>. You can use <code><a href="ft2-bitmap_handling.html#ft_bitmap_convert">FT_Bitmap_Convert</a></code> to transform them into 8-bit pixmaps.</p>
<hr>

<h2 id="ft_get_kerning">FT_Get_Kerning<a class="headerlink" href="#ft_get_kerning" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Get_Kerning</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>     face,
                  <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>     left_glyph,
                  <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>     right_glyph,
                  <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>     kern_mode,
                  <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>  *akerning );
</code></pre></div>

<p>Return the kerning vector between two glyphs of the same face.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a source face object.</p>
</td></tr>
<tr><td class="val" id="left_glyph">left_glyph</td><td class="desc">
<p>The index of the left glyph in the kern pair.</p>
</td></tr>
<tr><td class="val" id="right_glyph">right_glyph</td><td class="desc">
<p>The index of the right glyph in the kern pair.</p>
</td></tr>
<tr><td class="val" id="kern_mode">kern_mode</td><td class="desc">
<p>See <code><a href="ft2-base_interface.html#ft_kerning_mode">FT_Kerning_Mode</a></code> for more information. Determines the scale and dimension of the returned kerning vector.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="akerning">akerning</td><td class="desc">
<p>The kerning vector. This is either in font units, fractional pixels (26.6 format), or pixels for scalable formats, and in pixels for fixed-sizes formats.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>Only horizontal layouts (left-to-right &amp; right-to-left) are supported by this method. Other layouts, or more sophisticated kernings, are out of the scope of this API function -- they can be implemented through format-specific interfaces.</p>
<p>Kerning for OpenType fonts implemented in a &lsquo;GPOS&rsquo; table is not supported; use <code><a href="ft2-base_interface.html#ft_has_kerning">FT_HAS_KERNING</a></code> to find out whether a font has data that can be extracted with <code>FT_Get_Kerning</code>.</p>
<hr>

<h2 id="ft_kerning_mode">FT_Kerning_Mode<a class="headerlink" href="#ft_kerning_mode" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Kerning_Mode_
  {
    <a href="ft2-base_interface.html#ft_kerning_default">FT_KERNING_DEFAULT</a> = 0,
    <a href="ft2-base_interface.html#ft_kerning_unfitted">FT_KERNING_UNFITTED</a>,
    <a href="ft2-base_interface.html#ft_kerning_unscaled">FT_KERNING_UNSCALED</a>

  } <b>FT_Kerning_Mode</b>;


  /* these constants are deprecated; use the corresponding */
  /* `<b>FT_Kerning_Mode</b>` values instead                      */
#<span class="keyword">define</span> ft_kerning_default   <a href="ft2-base_interface.html#ft_kerning_default">FT_KERNING_DEFAULT</a>
#<span class="keyword">define</span> ft_kerning_unfitted  <a href="ft2-base_interface.html#ft_kerning_unfitted">FT_KERNING_UNFITTED</a>
#<span class="keyword">define</span> ft_kerning_unscaled  <a href="ft2-base_interface.html#ft_kerning_unscaled">FT_KERNING_UNSCALED</a>
</code></pre></div>

<p>An enumeration to specify the format of kerning values returned by <code><a href="ft2-base_interface.html#ft_get_kerning">FT_Get_Kerning</a></code>.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_kerning_default">FT_KERNING_DEFAULT</td><td class="desc">
<p>Return grid-fitted kerning distances in 26.6 fractional pixels.</p>
</td></tr>
<tr><td class="val" id="ft_kerning_unfitted">FT_KERNING_UNFITTED</td><td class="desc">
<p>Return un-grid-fitted kerning distances in 26.6 fractional pixels.</p>
</td></tr>
<tr><td class="val" id="ft_kerning_unscaled">FT_KERNING_UNSCALED</td><td class="desc">
<p>Return the kerning vector in original font units.</p>
</td></tr>
</table>

<h4>note</h4>

<p><code>FT_KERNING_DEFAULT</code> returns full pixel values; it also makes FreeType heuristically scale down kerning distances at small ppem values so that they don't become too big.</p>
<p>Both <code>FT_KERNING_DEFAULT</code> and <code>FT_KERNING_UNFITTED</code> use the current horizontal scaling factor (as set e.g. with <code><a href="ft2-base_interface.html#ft_set_char_size">FT_Set_Char_Size</a></code>) to convert font units to pixels.</p>
<hr>

<h2 id="ft_get_track_kerning">FT_Get_Track_Kerning<a class="headerlink" href="#ft_get_track_kerning" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Get_Track_Kerning</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>    face,
                        <a href="ft2-basic_types.html#ft_fixed">FT_Fixed</a>   point_size,
                        <a href="ft2-basic_types.html#ft_int">FT_Int</a>     degree,
                        <a href="ft2-basic_types.html#ft_fixed">FT_Fixed</a>*  akerning );
</code></pre></div>

<p>Return the track kerning for a given face object at a given size.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a source face object.</p>
</td></tr>
<tr><td class="val" id="point_size">point_size</td><td class="desc">
<p>The point size in 16.16 fractional points.</p>
</td></tr>
<tr><td class="val" id="degree">degree</td><td class="desc">
<p>The degree of tightness. Increasingly negative values represent tighter track kerning, while increasingly positive values represent looser track kerning. Value zero means no track kerning.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="akerning">akerning</td><td class="desc">
<p>The kerning in 16.16 fractional points, to be uniformly applied between all glyphs.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>Currently, only the Type&nbsp;1 font driver supports track kerning, using data from AFM files (if attached with <code><a href="ft2-base_interface.html#ft_attach_file">FT_Attach_File</a></code> or <code><a href="ft2-base_interface.html#ft_attach_stream">FT_Attach_Stream</a></code>).</p>
<p>Only very few AFM files come with track kerning data; please refer to Adobe's AFM specification for more details.</p>
<hr>

<h2 id="ft_get_glyph_name">FT_Get_Glyph_Name<a class="headerlink" href="#ft_get_glyph_name" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Get_Glyph_Name</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>     face,
                     <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>     glyph_index,
                     <a href="ft2-basic_types.html#ft_pointer">FT_Pointer</a>  buffer,
                     <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>     buffer_max );
</code></pre></div>

<p>Retrieve the ASCII name of a given glyph in a face. This only works for those faces where <code><a href="ft2-base_interface.html#ft_has_glyph_names">FT_HAS_GLYPH_NAMES</a></code>(face) returns&nbsp;1.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to a source face object.</p>
</td></tr>
<tr><td class="val" id="glyph_index">glyph_index</td><td class="desc">
<p>The glyph index.</p>
</td></tr>
<tr><td class="val" id="buffer_max">buffer_max</td><td class="desc">
<p>The maximum number of bytes available in the buffer.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="buffer">buffer</td><td class="desc">
<p>A pointer to a target buffer where the name is copied to.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>An error is returned if the face doesn't provide glyph names or if the glyph index is invalid. In all cases of failure, the first byte of <code>buffer</code> is set to&nbsp;0 to indicate an empty name.</p>
<p>The glyph name is truncated to fit within the buffer if it is too long. The returned string is always zero-terminated.</p>
<p>Be aware that FreeType reorders glyph indices internally so that glyph index&nbsp;0 always corresponds to the &lsquo;missing glyph&rsquo; (called &lsquo;.notdef&rsquo;).</p>
<p>This function always returns an error if the config macro <code>FT_CONFIG_OPTION_NO_GLYPH_NAMES</code> is not defined in <code>ftoption.h</code>.</p>
<hr>

<h2 id="ft_get_postscript_name">FT_Get_Postscript_Name<a class="headerlink" href="#ft_get_postscript_name" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <span class="keyword">const</span> <span class="keyword">char</span>* )
  <b>FT_Get_Postscript_Name</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>  face );
</code></pre></div>

<p>Retrieve the ASCII PostScript name of a given face, if available. This only works with PostScript, TrueType, and OpenType fonts.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
</table>

<h4>return</h4>

<p>A pointer to the face's PostScript name. <code>NULL</code> if unavailable.</p>
<h4>note</h4>

<p>The returned pointer is owned by the face and is destroyed with it.</p>
<p>For variation fonts, this string changes if you select a different instance, and you have to call <code>FT_Get_PostScript_Name</code> again to retrieve it. FreeType follows Adobe TechNote #5902, &lsquo;Generating PostScript Names for Fonts Using OpenType Font Variations&rsquo;.</p>
<p><a href="https://download.macromedia.com/pub/developer/opentype/tech-notes/5902.AdobePSNameGeneration.html">https://download.macromedia.com/pub/developer/opentype/tech-notes/5902.AdobePSNameGeneration.html</a></p>
<p>[Since 2.9] Special PostScript names for named instances are only returned if the named instance is set with <code><a href="ft2-multiple_masters.html#ft_set_named_instance">FT_Set_Named_Instance</a></code> (and the font has corresponding entries in its &lsquo;fvar&rsquo; table). If <code><a href="ft2-base_interface.html#ft_is_variation">FT_IS_VARIATION</a></code> returns true, the algorithmically derived PostScript name is provided, not looking up special entries for named instances.</p>
<hr>

<h2 id="ft_charmaprec">FT_CharMapRec<a class="headerlink" href="#ft_charmaprec" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_CharMapRec_
  {
    <a href="ft2-base_interface.html#ft_face">FT_Face</a>      face;
    <a href="ft2-base_interface.html#ft_encoding">FT_Encoding</a>  encoding;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>    platform_id;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>    encoding_id;

  } <b>FT_CharMapRec</b>;
</code></pre></div>

<p>The base charmap structure.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the parent face object.</p>
</td></tr>
<tr><td class="val" id="encoding">encoding</td><td class="desc">
<p>An <code><a href="ft2-base_interface.html#ft_encoding">FT_Encoding</a></code> tag identifying the charmap. Use this with <code><a href="ft2-base_interface.html#ft_select_charmap">FT_Select_Charmap</a></code>.</p>
</td></tr>
<tr><td class="val" id="platform_id">platform_id</td><td class="desc">
<p>An ID number describing the platform for the following encoding ID. This comes directly from the TrueType specification and gets emulated for other formats.</p>
</td></tr>
<tr><td class="val" id="encoding_id">encoding_id</td><td class="desc">
<p>A platform-specific encoding number. This also comes from the TrueType specification and gets emulated similarly.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_select_charmap">FT_Select_Charmap<a class="headerlink" href="#ft_select_charmap" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Select_Charmap</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>      face,
                     <a href="ft2-base_interface.html#ft_encoding">FT_Encoding</a>  encoding );
</code></pre></div>

<p>Select a given charmap by its encoding tag (as listed in <code>freetype.h</code>).</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="encoding">encoding</td><td class="desc">
<p>A handle to the selected encoding.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>This function returns an error if no charmap in the face corresponds to the encoding queried here.</p>
<p>Because many fonts contain more than a single cmap for Unicode encoding, this function has some special code to select the one that covers Unicode best (&lsquo;best&rsquo; in the sense that a UCS-4 cmap is preferred to a UCS-2 cmap). It is thus preferable to <code><a href="ft2-base_interface.html#ft_set_charmap">FT_Set_Charmap</a></code> in this case.</p>
<hr>

<h2 id="ft_set_charmap">FT_Set_Charmap<a class="headerlink" href="#ft_set_charmap" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Set_Charmap</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>     face,
                  <a href="ft2-base_interface.html#ft_charmap">FT_CharMap</a>  charmap );
</code></pre></div>

<p>Select a given charmap for character code to glyph index mapping.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="charmap">charmap</td><td class="desc">
<p>A handle to the selected charmap.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>This function returns an error if the charmap is not part of the face (i.e., if it is not listed in the <code>face-&gt;charmaps</code> table).</p>
<p>It also fails if an OpenType type&nbsp;14 charmap is selected (which doesn't map character codes to glyph indices at all).</p>
<hr>

<h2 id="ft_get_charmap_index">FT_Get_Charmap_Index<a class="headerlink" href="#ft_get_charmap_index" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_int">FT_Int</a> )
  <b>FT_Get_Charmap_Index</b>( <a href="ft2-base_interface.html#ft_charmap">FT_CharMap</a>  charmap );
</code></pre></div>

<p>Retrieve index of a given charmap.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="charmap">charmap</td><td class="desc">
<p>A handle to a charmap.</p>
</td></tr>
</table>

<h4>return</h4>

<p>The index into the array of character maps within the face to which <code>charmap</code> belongs. If an error occurs, -1 is returned.</p>
<hr>

<h2 id="ft_get_fstype_flags">FT_Get_FSType_Flags<a class="headerlink" href="#ft_get_fstype_flags" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a> )
  <b>FT_Get_FSType_Flags</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>  face );
</code></pre></div>

<p>Return the <code>fsType</code> flags for a font.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the source face object.</p>
</td></tr>
</table>

<h4>return</h4>

<p>The <code>fsType</code> flags, see <code><a href="ft2-base_interface.html#ft_fstype_xxx">FT_FSTYPE_XXX</a></code>.</p>
<h4>note</h4>

<p>Use this function rather than directly reading the <code>fs_type</code> field in the <code><a href="ft2-type1_tables.html#ps_fontinforec">PS_FontInfoRec</a></code> structure, which is only guaranteed to return the correct results for Type&nbsp;1 fonts.</p>
<h4>since</h4>

<p>2.3.8</p>
<hr>

<h2 id="ft_get_subglyph_info">FT_Get_SubGlyph_Info<a class="headerlink" href="#ft_get_subglyph_info" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Get_SubGlyph_Info</b>( <a href="ft2-base_interface.html#ft_glyphslot">FT_GlyphSlot</a>  glyph,
                        <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>       sub_index,
                        <a href="ft2-basic_types.html#ft_int">FT_Int</a>       *p_index,
                        <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>      *p_flags,
                        <a href="ft2-basic_types.html#ft_int">FT_Int</a>       *p_arg1,
                        <a href="ft2-basic_types.html#ft_int">FT_Int</a>       *p_arg2,
                        <a href="ft2-basic_types.html#ft_matrix">FT_Matrix</a>    *p_transform );
</code></pre></div>

<p>Retrieve a description of a given subglyph. Only use it if <code>glyph-&gt;format</code> is <code><a href="ft2-basic_types.html#ft_glyph_format">FT_GLYPH_FORMAT_COMPOSITE</a></code>; an error is returned otherwise.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="glyph">glyph</td><td class="desc">
<p>The source glyph slot.</p>
</td></tr>
<tr><td class="val" id="sub_index">sub_index</td><td class="desc">
<p>The index of the subglyph. Must be less than <code>glyph-&gt;num_subglyphs</code>.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="p_index">p_index</td><td class="desc">
<p>The glyph index of the subglyph.</p>
</td></tr>
<tr><td class="val" id="p_flags">p_flags</td><td class="desc">
<p>The subglyph flags, see <code><a href="ft2-base_interface.html#ft_subglyph_flag_xxx">FT_SUBGLYPH_FLAG_XXX</a></code>.</p>
</td></tr>
<tr><td class="val" id="p_arg1">p_arg1</td><td class="desc">
<p>The subglyph's first argument (if any).</p>
</td></tr>
<tr><td class="val" id="p_arg2">p_arg2</td><td class="desc">
<p>The subglyph's second argument (if any).</p>
</td></tr>
<tr><td class="val" id="p_transform">p_transform</td><td class="desc">
<p>The subglyph transformation (if any).</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>The values of <code>*p_arg1</code>, <code>*p_arg2</code>, and <code>*p_transform</code> must be interpreted depending on the flags returned in <code>*p_flags</code>. See the OpenType specification for details.</p>
<p><a href="https://docs.microsoft.com/en-us/typography/opentype/spec/glyf#composite-glyph-description">https://docs.microsoft.com/en-us/typography/opentype/spec/glyf#composite-glyph-description</a></p>
<hr>

<h2 id="ft_face_internal">FT_Face_Internal<a class="headerlink" href="#ft_face_internal" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Face_InternalRec_*  <b>FT_Face_Internal</b>;
</code></pre></div>

<p>An opaque handle to an <code>FT_Face_InternalRec</code> structure that models the private data of a given <code><a href="ft2-base_interface.html#ft_face">FT_Face</a></code> object.</p>
<p>This structure might change between releases of FreeType&nbsp;2 and is not generally available to client applications.</p>
<hr>

<h2 id="ft_size_internal">FT_Size_Internal<a class="headerlink" href="#ft_size_internal" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Size_InternalRec_*  <b>FT_Size_Internal</b>;
</code></pre></div>

<p>An opaque handle to an <code>FT_Size_InternalRec</code> structure, used to model private data of a given <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code> object.</p>
<hr>

<h2 id="ft_slot_internal">FT_Slot_Internal<a class="headerlink" href="#ft_slot_internal" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Slot_InternalRec_*  <b>FT_Slot_Internal</b>;
</code></pre></div>

<p>An opaque handle to an <code>FT_Slot_InternalRec</code> structure, used to model private data of a given <code><a href="ft2-base_interface.html#ft_glyphslot">FT_GlyphSlot</a></code> object.</p>
<hr>

<h2 id="ft_face_flag_xxx">FT_FACE_FLAG_XXX<a class="headerlink" href="#ft_face_flag_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_scalable">FT_FACE_FLAG_SCALABLE</a>          ( 1L &lt;&lt;  0 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_fixed_sizes">FT_FACE_FLAG_FIXED_SIZES</a>       ( 1L &lt;&lt;  1 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_fixed_width">FT_FACE_FLAG_FIXED_WIDTH</a>       ( 1L &lt;&lt;  2 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_sfnt">FT_FACE_FLAG_SFNT</a>              ( 1L &lt;&lt;  3 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_horizontal">FT_FACE_FLAG_HORIZONTAL</a>        ( 1L &lt;&lt;  4 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_vertical">FT_FACE_FLAG_VERTICAL</a>          ( 1L &lt;&lt;  5 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_kerning">FT_FACE_FLAG_KERNING</a>           ( 1L &lt;&lt;  6 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_fast_glyphs">FT_FACE_FLAG_FAST_GLYPHS</a>       ( 1L &lt;&lt;  7 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_multiple_masters">FT_FACE_FLAG_MULTIPLE_MASTERS</a>  ( 1L &lt;&lt;  8 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_glyph_names">FT_FACE_FLAG_GLYPH_NAMES</a>       ( 1L &lt;&lt;  9 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_external_stream">FT_FACE_FLAG_EXTERNAL_STREAM</a>   ( 1L &lt;&lt; 10 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_hinter">FT_FACE_FLAG_HINTER</a>            ( 1L &lt;&lt; 11 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_cid_keyed">FT_FACE_FLAG_CID_KEYED</a>         ( 1L &lt;&lt; 12 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_tricky">FT_FACE_FLAG_TRICKY</a>            ( 1L &lt;&lt; 13 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_color">FT_FACE_FLAG_COLOR</a>             ( 1L &lt;&lt; 14 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_face_flag_variation">FT_FACE_FLAG_VARIATION</a>         ( 1L &lt;&lt; 15 )
</code></pre></div>

<p>A list of bit flags used in the <code>face_flags</code> field of the <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code> structure. They inform client applications of properties of the corresponding face.</p>
<h4>values</h4>

<table class="fields long">
<tr><td class="val" id="ft_face_flag_scalable">FT_FACE_FLAG_SCALABLE</td><td class="desc">
<p>The face contains outline glyphs. Note that a face can contain bitmap strikes also, i.e., a face can have both this flag and <code><a href="ft2-base_interface.html#ft_face_flag_xxx">FT_FACE_FLAG_FIXED_SIZES</a></code> set.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_fixed_sizes">FT_FACE_FLAG_FIXED_SIZES</td><td class="desc">
<p>The face contains bitmap strikes. See also the <code>num_fixed_sizes</code> and <code>available_sizes</code> fields of <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code>.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_fixed_width">FT_FACE_FLAG_FIXED_WIDTH</td><td class="desc">
<p>The face contains fixed-width characters (like Courier, Lucida, MonoType, etc.).</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_sfnt">FT_FACE_FLAG_SFNT</td><td class="desc">
<p>The face uses the SFNT storage scheme. For now, this means TrueType and OpenType.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_horizontal">FT_FACE_FLAG_HORIZONTAL</td><td class="desc">
<p>The face contains horizontal glyph metrics. This should be set for all common formats.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_vertical">FT_FACE_FLAG_VERTICAL</td><td class="desc">
<p>The face contains vertical glyph metrics. This is only available in some formats, not all of them.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_kerning">FT_FACE_FLAG_KERNING</td><td class="desc">
<p>The face contains kerning information. If set, the kerning distance can be retrieved using the function <code><a href="ft2-base_interface.html#ft_get_kerning">FT_Get_Kerning</a></code>. Otherwise the function always return the vector (0,0). Note that FreeType doesn't handle kerning data from the SFNT &lsquo;GPOS&rsquo; table (as present in many OpenType fonts).</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_fast_glyphs">FT_FACE_FLAG_FAST_GLYPHS</td><td class="desc">
<p>THIS FLAG IS DEPRECATED. DO NOT USE OR TEST IT.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_multiple_masters">FT_FACE_FLAG_MULTIPLE_MASTERS</td><td class="desc">
<p>The face contains multiple masters and is capable of interpolating between them. Supported formats are Adobe MM, TrueType GX, and OpenType variation fonts.</p>
<p>See section &lsquo;<a href="ft2-multiple_masters.html#multiple_masters">Multiple Masters</a>&rsquo; for API details.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_glyph_names">FT_FACE_FLAG_GLYPH_NAMES</td><td class="desc">
<p>The face contains glyph names, which can be retrieved using <code><a href="ft2-base_interface.html#ft_get_glyph_name">FT_Get_Glyph_Name</a></code>. Note that some TrueType fonts contain broken glyph name tables. Use the function <code><a href="ft2-type1_tables.html#ft_has_ps_glyph_names">FT_Has_PS_Glyph_Names</a></code> when needed.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_external_stream">FT_FACE_FLAG_EXTERNAL_STREAM</td><td class="desc">
<p>Used internally by FreeType to indicate that a face's stream was provided by the client application and should not be destroyed when <code><a href="ft2-base_interface.html#ft_done_face">FT_Done_Face</a></code> is called. Don't read or test this flag.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_hinter">FT_FACE_FLAG_HINTER</td><td class="desc">
<p>The font driver has a hinting machine of its own. For example, with TrueType fonts, it makes sense to use data from the SFNT &lsquo;gasp&rsquo; table only if the native TrueType hinting engine (with the bytecode interpreter) is available and active.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_cid_keyed">FT_FACE_FLAG_CID_KEYED</td><td class="desc">
<p>The face is CID-keyed. In that case, the face is not accessed by glyph indices but by CID values. For subsetted CID-keyed fonts this has the consequence that not all index values are a valid argument to <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code>. Only the CID values for which corresponding glyphs in the subsetted font exist make <code>FT_Load_Glyph</code> return successfully; in all other cases you get an <code>FT_Err_Invalid_Argument</code> error.</p>
<p>Note that CID-keyed fonts that are in an SFNT wrapper (this is, all OpenType/CFF fonts) don't have this flag set since the glyphs are accessed in the normal way (using contiguous indices); the &lsquo;CID-ness&rsquo; isn't visible to the application.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_tricky">FT_FACE_FLAG_TRICKY</td><td class="desc">
<p>The face is &lsquo;tricky&rsquo;, this is, it always needs the font format's native hinting engine to get a reasonable result. A typical example is the old Chinese font <code>mingli.ttf</code> (but not <code>mingliu.ttc</code>) that uses TrueType bytecode instructions to move and scale all of its subglyphs.</p>
<p>It is not possible to auto-hint such fonts using <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_FORCE_AUTOHINT</a></code>; it will also ignore <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_HINTING</a></code>. You have to set both <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_HINTING</a></code> and <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_AUTOHINT</a></code> to really disable hinting; however, you probably never want this except for demonstration purposes.</p>
<p>Currently, there are about a dozen TrueType fonts in the list of tricky fonts; they are hard-coded in file <code>ttobjs.c</code>.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_color">FT_FACE_FLAG_COLOR</td><td class="desc">
<p>[Since 2.5.1] The face has color glyph tables. See <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_COLOR</a></code> for more information.</p>
</td></tr>
<tr><td class="val" id="ft_face_flag_variation">FT_FACE_FLAG_VARIATION</td><td class="desc">
<p>[Since 2.9] Set if the current face (or named instance) has been altered with <code><a href="ft2-multiple_masters.html#ft_set_mm_design_coordinates">FT_Set_MM_Design_Coordinates</a></code>, <code><a href="ft2-multiple_masters.html#ft_set_var_design_coordinates">FT_Set_Var_Design_Coordinates</a></code>, or <code><a href="ft2-multiple_masters.html#ft_set_var_blend_coordinates">FT_Set_Var_Blend_Coordinates</a></code>. This flag is unset by a call to <code><a href="ft2-multiple_masters.html#ft_set_named_instance">FT_Set_Named_Instance</a></code>.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_style_flag_xxx">FT_STYLE_FLAG_XXX<a class="headerlink" href="#ft_style_flag_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_style_flag_italic">FT_STYLE_FLAG_ITALIC</a>  ( 1 &lt;&lt; 0 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_style_flag_bold">FT_STYLE_FLAG_BOLD</a>    ( 1 &lt;&lt; 1 )
</code></pre></div>

<p>A list of bit flags to indicate the style of a given face. These are used in the <code>style_flags</code> field of <code><a href="ft2-base_interface.html#ft_facerec">FT_FaceRec</a></code>.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_style_flag_italic">FT_STYLE_FLAG_ITALIC</td><td class="desc">
<p>The face style is italic or oblique.</p>
</td></tr>
<tr><td class="val" id="ft_style_flag_bold">FT_STYLE_FLAG_BOLD</td><td class="desc">
<p>The face is bold.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The style information as provided by FreeType is very basic. More details are beyond the scope and should be done on a higher level (for example, by analyzing various fields of the &lsquo;OS/2&rsquo; table in SFNT based fonts).</p>
<hr>

<h2 id="ft_open_xxx">FT_OPEN_XXX<a class="headerlink" href="#ft_open_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_open_memory">FT_OPEN_MEMORY</a>    0x1
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_open_stream">FT_OPEN_STREAM</a>    0x2
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_open_pathname">FT_OPEN_PATHNAME</a>  0x4
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_open_driver">FT_OPEN_DRIVER</a>    0x8
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_open_params">FT_OPEN_PARAMS</a>    0x10

  /* these constants are deprecated; use the corresponding `<b>FT_OPEN_XXX</b>` */
  /* values instead                                                      */
#<span class="keyword">define</span> ft_open_memory    <a href="ft2-base_interface.html#ft_open_memory">FT_OPEN_MEMORY</a>
#<span class="keyword">define</span> ft_open_stream    <a href="ft2-base_interface.html#ft_open_stream">FT_OPEN_STREAM</a>
#<span class="keyword">define</span> ft_open_pathname  <a href="ft2-base_interface.html#ft_open_pathname">FT_OPEN_PATHNAME</a>
#<span class="keyword">define</span> ft_open_driver    <a href="ft2-base_interface.html#ft_open_driver">FT_OPEN_DRIVER</a>
#<span class="keyword">define</span> ft_open_params    <a href="ft2-base_interface.html#ft_open_params">FT_OPEN_PARAMS</a>
</code></pre></div>

<p>A list of bit field constants used within the <code>flags</code> field of the <code><a href="ft2-base_interface.html#ft_open_args">FT_Open_Args</a></code> structure.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_open_memory">FT_OPEN_MEMORY</td><td class="desc">
<p>This is a memory-based stream.</p>
</td></tr>
<tr><td class="val" id="ft_open_stream">FT_OPEN_STREAM</td><td class="desc">
<p>Copy the stream from the <code>stream</code> field.</p>
</td></tr>
<tr><td class="val" id="ft_open_pathname">FT_OPEN_PATHNAME</td><td class="desc">
<p>Create a new input stream from a C&nbsp;path name.</p>
</td></tr>
<tr><td class="val" id="ft_open_driver">FT_OPEN_DRIVER</td><td class="desc">
<p>Use the <code>driver</code> field.</p>
</td></tr>
<tr><td class="val" id="ft_open_params">FT_OPEN_PARAMS</td><td class="desc">
<p>Use the <code>num_params</code> and <code>params</code> fields.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The <code>FT_OPEN_MEMORY</code>, <code>FT_OPEN_STREAM</code>, and <code>FT_OPEN_PATHNAME</code> flags are mutually exclusive.</p>
<hr>

<h2 id="ft_load_xxx">FT_LOAD_XXX<a class="headerlink" href="#ft_load_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_default">FT_LOAD_DEFAULT</a>                      0x0
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_no_scale">FT_LOAD_NO_SCALE</a>                     ( 1L &lt;&lt; 0 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_no_hinting">FT_LOAD_NO_HINTING</a>                   ( 1L &lt;&lt; 1 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_render">FT_LOAD_RENDER</a>                       ( 1L &lt;&lt; 2 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_no_bitmap">FT_LOAD_NO_BITMAP</a>                    ( 1L &lt;&lt; 3 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_vertical_layout">FT_LOAD_VERTICAL_LAYOUT</a>              ( 1L &lt;&lt; 4 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_force_autohint">FT_LOAD_FORCE_AUTOHINT</a>               ( 1L &lt;&lt; 5 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_crop_bitmap">FT_LOAD_CROP_BITMAP</a>                  ( 1L &lt;&lt; 6 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_pedantic">FT_LOAD_PEDANTIC</a>                     ( 1L &lt;&lt; 7 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_ignore_global_advance_width">FT_LOAD_IGNORE_GLOBAL_ADVANCE_WIDTH</a>  ( 1L &lt;&lt; 9 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_no_recurse">FT_LOAD_NO_RECURSE</a>                   ( 1L &lt;&lt; 10 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_ignore_transform">FT_LOAD_IGNORE_TRANSFORM</a>             ( 1L &lt;&lt; 11 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_monochrome">FT_LOAD_MONOCHROME</a>                   ( 1L &lt;&lt; 12 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_linear_design">FT_LOAD_LINEAR_DESIGN</a>                ( 1L &lt;&lt; 13 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_no_autohint">FT_LOAD_NO_AUTOHINT</a>                  ( 1L &lt;&lt; 15 )
  /* Bits 16-19 are used by `FT_LOAD_TARGET_` */
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_color">FT_LOAD_COLOR</a>                        ( 1L &lt;&lt; 20 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_compute_metrics">FT_LOAD_COMPUTE_METRICS</a>              ( 1L &lt;&lt; 21 )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_bitmap_metrics_only">FT_LOAD_BITMAP_METRICS_ONLY</a>          ( 1L &lt;&lt; 22 )
</code></pre></div>

<p>A list of bit field constants for <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code> to indicate what kind of operations to perform during glyph loading.</p>
<h4>values</h4>

<table class="fields long">
<tr><td class="val" id="ft_load_default">FT_LOAD_DEFAULT</td><td class="desc">
<p>Corresponding to&nbsp;0, this value is used as the default glyph load operation. In this case, the following happens:</p>
<ol>
<li><p>FreeType looks for a bitmap for the glyph corresponding to the face's current size. If one is found, the function returns. The bitmap data can be accessed from the glyph slot (see note below).</p>
</li>
<li><p>If no embedded bitmap is searched for or found, FreeType looks for a scalable outline. If one is found, it is loaded from the font file, scaled to device pixels, then &lsquo;hinted&rsquo; to the pixel grid in order to optimize it. The outline data can be accessed from the glyph slot (see note below).</p>
</li>
</ol>
<p>Note that by default the glyph loader doesn't render outlines into bitmaps. The following flags are used to modify this default behaviour to more specific and useful cases.</p>
</td></tr>
<tr><td class="val" id="ft_load_no_scale">FT_LOAD_NO_SCALE</td><td class="desc">
<p>Don't scale the loaded outline glyph but keep it in font units.</p>
<p>This flag implies <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_HINTING</a></code> and <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_BITMAP</a></code>, and unsets <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_RENDER</a></code>.</p>
<p>If the font is &lsquo;tricky&rsquo; (see <code><a href="ft2-base_interface.html#ft_face_flag_xxx">FT_FACE_FLAG_TRICKY</a></code> for more), using <code>FT_LOAD_NO_SCALE</code> usually yields meaningless outlines because the subglyphs must be scaled and positioned with hinting instructions. This can be solved by loading the font without <code>FT_LOAD_NO_SCALE</code> and setting the character size to <code>font-&gt;units_per_EM</code>.</p>
</td></tr>
<tr><td class="val" id="ft_load_no_hinting">FT_LOAD_NO_HINTING</td><td class="desc">
<p>Disable hinting. This generally generates &lsquo;blurrier&rsquo; bitmap glyphs when the glyph are rendered in any of the anti-aliased modes. See also the note below.</p>
<p>This flag is implied by <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_SCALE</a></code>.</p>
</td></tr>
<tr><td class="val" id="ft_load_render">FT_LOAD_RENDER</td><td class="desc">
<p>Call <code><a href="ft2-base_interface.html#ft_render_glyph">FT_Render_Glyph</a></code> after the glyph is loaded. By default, the glyph is rendered in <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_NORMAL</a></code> mode. This can be overridden by <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_XXX</a></code> or <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_MONOCHROME</a></code>.</p>
<p>This flag is unset by <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_SCALE</a></code>.</p>
</td></tr>
<tr><td class="val" id="ft_load_no_bitmap">FT_LOAD_NO_BITMAP</td><td class="desc">
<p>Ignore bitmap strikes when loading. Bitmap-only fonts ignore this flag.</p>
<p><code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_SCALE</a></code> always sets this flag.</p>
</td></tr>
<tr><td class="val" id="ft_load_vertical_layout">FT_LOAD_VERTICAL_LAYOUT</td><td class="desc">
<p>Load the glyph for vertical text layout. In particular, the <code>advance</code> value in the <code><a href="ft2-base_interface.html#ft_glyphslotrec">FT_GlyphSlotRec</a></code> structure is set to the <code>vertAdvance</code> value of the <code>metrics</code> field.</p>
<p>In case <code><a href="ft2-base_interface.html#ft_has_vertical">FT_HAS_VERTICAL</a></code> doesn't return true, you shouldn't use this flag currently. Reason is that in this case vertical metrics get synthesized, and those values are not always consistent across various font formats.</p>
</td></tr>
<tr><td class="val" id="ft_load_force_autohint">FT_LOAD_FORCE_AUTOHINT</td><td class="desc">
<p>Prefer the auto-hinter over the font's native hinter. See also the note below.</p>
</td></tr>
<tr><td class="val" id="ft_load_pedantic">FT_LOAD_PEDANTIC</td><td class="desc">
<p>Make the font driver perform pedantic verifications during glyph loading and hinting. This is mostly used to detect broken glyphs in fonts. By default, FreeType tries to handle broken fonts also.</p>
<p>In particular, errors from the TrueType bytecode engine are not passed to the application if this flag is not set; this might result in partially hinted or distorted glyphs in case a glyph's bytecode is buggy.</p>
</td></tr>
<tr><td class="val" id="ft_load_no_recurse">FT_LOAD_NO_RECURSE</td><td class="desc">
<p>Don't load composite glyphs recursively. Instead, the font driver fills the <code>num_subglyph</code> and <code>subglyphs</code> values of the glyph slot; it also sets <code>glyph-&gt;format</code> to <code><a href="ft2-basic_types.html#ft_glyph_format">FT_GLYPH_FORMAT_COMPOSITE</a></code>. The description of subglyphs can then be accessed with <code><a href="ft2-base_interface.html#ft_get_subglyph_info">FT_Get_SubGlyph_Info</a></code>.</p>
<p>Don't use this flag for retrieving metrics information since some font drivers only return rudimentary data.</p>
<p>This flag implies <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_SCALE</a></code> and <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_IGNORE_TRANSFORM</a></code>.</p>
</td></tr>
<tr><td class="val" id="ft_load_ignore_transform">FT_LOAD_IGNORE_TRANSFORM</td><td class="desc">
<p>Ignore the transform matrix set by <code><a href="ft2-base_interface.html#ft_set_transform">FT_Set_Transform</a></code>.</p>
</td></tr>
<tr><td class="val" id="ft_load_monochrome">FT_LOAD_MONOCHROME</td><td class="desc">
<p>This flag is used with <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_RENDER</a></code> to indicate that you want to render an outline glyph to a 1-bit monochrome bitmap glyph, with 8&nbsp;pixels packed into each byte of the bitmap data.</p>
<p>Note that this has no effect on the hinting algorithm used. You should rather use <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_MONO</a></code> so that the monochrome-optimized hinting algorithm is used.</p>
</td></tr>
<tr><td class="val" id="ft_load_linear_design">FT_LOAD_LINEAR_DESIGN</td><td class="desc">
<p>Keep <code>linearHoriAdvance</code> and <code>linearVertAdvance</code> fields of <code><a href="ft2-base_interface.html#ft_glyphslotrec">FT_GlyphSlotRec</a></code> in font units. See <code><a href="ft2-base_interface.html#ft_glyphslotrec">FT_GlyphSlotRec</a></code> for details.</p>
</td></tr>
<tr><td class="val" id="ft_load_no_autohint">FT_LOAD_NO_AUTOHINT</td><td class="desc">
<p>Disable the auto-hinter. See also the note below.</p>
</td></tr>
<tr><td class="val" id="ft_load_color">FT_LOAD_COLOR</td><td class="desc">
<p>Load colored glyphs. There are slight differences depending on the font format.</p>
<p>[Since 2.5] Load embedded color bitmap images. The resulting color bitmaps, if available, will have the <code><a href="ft2-basic_types.html#ft_pixel_mode">FT_PIXEL_MODE_BGRA</a></code> format, with pre-multiplied color channels. If the flag is not set and color bitmaps are found, they are converted to 256-level gray bitmaps, using the <code><a href="ft2-basic_types.html#ft_pixel_mode">FT_PIXEL_MODE_GRAY</a></code> format.</p>
<p>[Since 2.10, experimental] If the glyph index contains an entry in the face's &lsquo;COLR&rsquo; table with a &lsquo;CPAL&rsquo; palette table (as defined in the OpenType specification), make <code><a href="ft2-base_interface.html#ft_render_glyph">FT_Render_Glyph</a></code> provide a default blending of the color glyph layers associated with the glyph index, using the same bitmap format as embedded color bitmap images. This is mainly for convenience; for full control of color layers use <code><a href="ft2-layer_management.html#ft_get_color_glyph_layer">FT_Get_Color_Glyph_Layer</a></code> and FreeType's color functions like <code><a href="ft2-color_management.html#ft_palette_select">FT_Palette_Select</a></code> instead of setting <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_COLOR</a></code> for rendering so that the client application can handle blending by itself.</p>
</td></tr>
<tr><td class="val" id="ft_load_compute_metrics">FT_LOAD_COMPUTE_METRICS</td><td class="desc">
<p>[Since 2.6.1] Compute glyph metrics from the glyph data, without the use of bundled metrics tables (for example, the &lsquo;hdmx&rsquo; table in TrueType fonts). This flag is mainly used by font validating or font editing applications, which need to ignore, verify, or edit those tables.</p>
<p>Currently, this flag is only implemented for TrueType fonts.</p>
</td></tr>
<tr><td class="val" id="ft_load_bitmap_metrics_only">FT_LOAD_BITMAP_METRICS_ONLY</td><td class="desc">
<p>[Since 2.7.1] Request loading of the metrics and bitmap image information of a (possibly embedded) bitmap glyph without allocating or copying the bitmap image data itself. No effect if the target glyph is not a bitmap image.</p>
<p>This flag unsets <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_RENDER</a></code>.</p>
</td></tr>
<tr><td class="val" id="ft_load_crop_bitmap">FT_LOAD_CROP_BITMAP</td><td class="desc">
<p>Ignored. Deprecated.</p>
</td></tr>
<tr><td class="val" id="ft_load_ignore_global_advance_width">FT_LOAD_IGNORE_GLOBAL_ADVANCE_WIDTH</td><td class="desc">
<p>Ignored. Deprecated.</p>
</td></tr>
</table>

<h4>note</h4>

<p>By default, hinting is enabled and the font's native hinter (see <code><a href="ft2-base_interface.html#ft_face_flag_xxx">FT_FACE_FLAG_HINTER</a></code>) is preferred over the auto-hinter. You can disable hinting by setting <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_HINTING</a></code> or change the precedence by setting <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_FORCE_AUTOHINT</a></code>. You can also set <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_AUTOHINT</a></code> in case you don't want the auto-hinter to be used at all.</p>
<p>See the description of <code><a href="ft2-base_interface.html#ft_face_flag_xxx">FT_FACE_FLAG_TRICKY</a></code> for a special exception (affecting only a handful of Asian fonts).</p>
<p>Besides deciding which hinter to use, you can also decide which hinting algorithm to use. See <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_XXX</a></code> for details.</p>
<p>Note that the auto-hinter needs a valid Unicode cmap (either a native one or synthesized by FreeType) for producing correct results. If a font provides an incorrect mapping (for example, assigning the character code U+005A, LATIN CAPITAL LETTER&nbsp;Z, to a glyph depicting a mathematical integral sign), the auto-hinter might produce useless results.</p>
<hr>

<h2 id="ft_load_target_xxx">FT_LOAD_TARGET_XXX<a class="headerlink" href="#ft_load_target_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> FT_LOAD_TARGET_( x )   ( (<a href="ft2-basic_types.html#ft_int32">FT_Int32</a>)( (x) &amp; 15 ) &lt;&lt; 16 )

#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_target_normal">FT_LOAD_TARGET_NORMAL</a>  FT_LOAD_TARGET_( <a href="ft2-base_interface.html#ft_render_mode_normal">FT_RENDER_MODE_NORMAL</a> )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_target_light">FT_LOAD_TARGET_LIGHT</a>   FT_LOAD_TARGET_( <a href="ft2-base_interface.html#ft_render_mode_light">FT_RENDER_MODE_LIGHT</a>  )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_target_mono">FT_LOAD_TARGET_MONO</a>    FT_LOAD_TARGET_( <a href="ft2-base_interface.html#ft_render_mode_mono">FT_RENDER_MODE_MONO</a>   )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_target_lcd">FT_LOAD_TARGET_LCD</a>     FT_LOAD_TARGET_( <a href="ft2-base_interface.html#ft_render_mode_lcd">FT_RENDER_MODE_LCD</a>    )
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_load_target_lcd_v">FT_LOAD_TARGET_LCD_V</a>   FT_LOAD_TARGET_( <a href="ft2-base_interface.html#ft_render_mode_lcd_v">FT_RENDER_MODE_LCD_V</a>  )
</code></pre></div>

<p>A list of values to select a specific hinting algorithm for the hinter. You should OR one of these values to your <code>load_flags</code> when calling <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code>.</p>
<p>Note that a font's native hinters may ignore the hinting algorithm you have specified (e.g., the TrueType bytecode interpreter). You can set <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_FORCE_AUTOHINT</a></code> to ensure that the auto-hinter is used.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_load_target_normal">FT_LOAD_TARGET_NORMAL</td><td class="desc">
<p>The default hinting algorithm, optimized for standard gray-level rendering. For monochrome output, use <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_MONO</a></code> instead.</p>
</td></tr>
<tr><td class="val" id="ft_load_target_light">FT_LOAD_TARGET_LIGHT</td><td class="desc">
<p>A lighter hinting algorithm for gray-level modes. Many generated glyphs are fuzzier but better resemble their original shape. This is achieved by snapping glyphs to the pixel grid only vertically (Y-axis), as is done by FreeType's new CFF engine or Microsoft's ClearType font renderer. This preserves inter-glyph spacing in horizontal text. The snapping is done either by the native font driver, if the driver itself and the font support it, or by the auto-hinter.</p>
<p>Advance widths are rounded to integer values; however, using the <code>lsb_delta</code> and <code>rsb_delta</code> fields of <code><a href="ft2-base_interface.html#ft_glyphslotrec">FT_GlyphSlotRec</a></code>, it is possible to get fractional advance widths for subpixel positioning (which is recommended to use).</p>
<p>If configuration option <code>AF_CONFIG_OPTION_TT_SIZE_METRICS</code> is active, TrueType-like metrics are used to make this mode behave similarly as in unpatched FreeType versions between 2.4.6 and 2.7.1 (inclusive).</p>
</td></tr>
<tr><td class="val" id="ft_load_target_mono">FT_LOAD_TARGET_MONO</td><td class="desc">
<p>Strong hinting algorithm that should only be used for monochrome output. The result is probably unpleasant if the glyph is rendered in non-monochrome modes.</p>
<p>Note that for outline fonts only the TrueType font driver has proper monochrome hinting support, provided the TTFs contain hints for B/W rendering (which most fonts no longer provide). If these conditions are not met it is very likely that you get ugly results at smaller sizes.</p>
</td></tr>
<tr><td class="val" id="ft_load_target_lcd">FT_LOAD_TARGET_LCD</td><td class="desc">
<p>A variant of <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_LIGHT</a></code> optimized for horizontally decimated LCD displays.</p>
</td></tr>
<tr><td class="val" id="ft_load_target_lcd_v">FT_LOAD_TARGET_LCD_V</td><td class="desc">
<p>A variant of <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_NORMAL</a></code> optimized for vertically decimated LCD displays.</p>
</td></tr>
</table>

<h4>note</h4>

<p>You should use only <em>one</em> of the <code>FT_LOAD_TARGET_XXX</code> values in your <code>load_flags</code>. They can't be ORed.</p>
<p>If <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_RENDER</a></code> is also set, the glyph is rendered in the corresponding mode (i.e., the mode that matches the used algorithm best). An exception is <code>FT_LOAD_TARGET_MONO</code> since it implies <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_MONOCHROME</a></code>.</p>
<p>You can use a hinting algorithm that doesn't correspond to the same rendering mode. As an example, it is possible to use the &lsquo;light&rsquo; hinting algorithm and have the results rendered in horizontal LCD pixel mode, with code like
<div class="highlight"><pre><span></span><code>  FT_Load_Glyph( face, glyph_index,
                 load_flags | FT_LOAD_TARGET_LIGHT );

  FT_Render_Glyph( face-&gt;glyph, FT_RENDER_MODE_LCD );
</code></pre></div></p>
<p>In general, you should stick with one rendering mode. For example, switching between <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_NORMAL</a></code> and <code><a href="ft2-base_interface.html#ft_load_target_xxx">FT_LOAD_TARGET_MONO</a></code> enforces a lot of recomputation for TrueType fonts, which is slow. Another reason is caching: Selecting a different mode usually causes changes in both the outlines and the rasterized bitmaps; it is thus necessary to empty the cache after a mode switch to avoid false hits.</p>
<hr>

<h2 id="ft_subglyph_flag_xxx">FT_SUBGLYPH_FLAG_XXX<a class="headerlink" href="#ft_subglyph_flag_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_subglyph_flag_args_are_words">FT_SUBGLYPH_FLAG_ARGS_ARE_WORDS</a>          1
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_subglyph_flag_args_are_xy_values">FT_SUBGLYPH_FLAG_ARGS_ARE_XY_VALUES</a>      2
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_subglyph_flag_round_xy_to_grid">FT_SUBGLYPH_FLAG_ROUND_XY_TO_GRID</a>        4
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_subglyph_flag_scale">FT_SUBGLYPH_FLAG_SCALE</a>                   8
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_subglyph_flag_xy_scale">FT_SUBGLYPH_FLAG_XY_SCALE</a>             0x40
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_subglyph_flag_2x2">FT_SUBGLYPH_FLAG_2X2</a>                  0x80
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_subglyph_flag_use_my_metrics">FT_SUBGLYPH_FLAG_USE_MY_METRICS</a>      0x200
</code></pre></div>

<p>A list of constants describing subglyphs. Please refer to the &lsquo;glyf&rsquo; table description in the OpenType specification for the meaning of the various flags (which get synthesized for non-OpenType subglyphs).</p>
<p><a href="https://docs.microsoft.com/en-us/typography/opentype/spec/glyf#composite-glyph-description">https://docs.microsoft.com/en-us/typography/opentype/spec/glyf#composite-glyph-description</a></p>
<h4>values</h4>

<table class="fields long">
<tr><td class="val" id="ft_subglyph_flag_args_are_words">FT_SUBGLYPH_FLAG_ARGS_ARE_WORDS</td><td class="desc">

</td></tr>
<tr><td class="val" id="ft_subglyph_flag_args_are_xy_values">FT_SUBGLYPH_FLAG_ARGS_ARE_XY_VALUES</td><td class="desc">

</td></tr>
<tr><td class="val" id="ft_subglyph_flag_round_xy_to_grid">FT_SUBGLYPH_FLAG_ROUND_XY_TO_GRID</td><td class="desc">

</td></tr>
<tr><td class="val" id="ft_subglyph_flag_scale">FT_SUBGLYPH_FLAG_SCALE</td><td class="desc">

</td></tr>
<tr><td class="val" id="ft_subglyph_flag_xy_scale">FT_SUBGLYPH_FLAG_XY_SCALE</td><td class="desc">

</td></tr>
<tr><td class="val" id="ft_subglyph_flag_2x2">FT_SUBGLYPH_FLAG_2X2</td><td class="desc">

</td></tr>
<tr><td class="val" id="ft_subglyph_flag_use_my_metrics">FT_SUBGLYPH_FLAG_USE_MY_METRICS</td><td class="desc">

</td></tr>
</table>

<hr>

<h2 id="ft_fstype_xxx">FT_FSTYPE_XXX<a class="headerlink" href="#ft_fstype_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_fstype_installable_embedding">FT_FSTYPE_INSTALLABLE_EMBEDDING</a>         0x0000
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_fstype_restricted_license_embedding">FT_FSTYPE_RESTRICTED_LICENSE_EMBEDDING</a>  0x0002
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_fstype_preview_and_print_embedding">FT_FSTYPE_PREVIEW_AND_PRINT_EMBEDDING</a>   0x0004
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_fstype_editable_embedding">FT_FSTYPE_EDITABLE_EMBEDDING</a>            0x0008
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_fstype_no_subsetting">FT_FSTYPE_NO_SUBSETTING</a>                 0x0100
#<span class="keyword">define</span> <a href="ft2-base_interface.html#ft_fstype_bitmap_embedding_only">FT_FSTYPE_BITMAP_EMBEDDING_ONLY</a>         0x0200
</code></pre></div>

<p>A list of bit flags used in the <code>fsType</code> field of the OS/2 table in a TrueType or OpenType font and the <code>FSType</code> entry in a PostScript font. These bit flags are returned by <code><a href="ft2-base_interface.html#ft_get_fstype_flags">FT_Get_FSType_Flags</a></code>; they inform client applications of embedding and subsetting restrictions associated with a font.</p>
<p>See <a href="https://www.adobe.com/content/dam/Adobe/en/devnet/acrobat/pdfs/FontPolicies.pdf">https://www.adobe.com/content/dam/Adobe/en/devnet/acrobat/pdfs/FontPolicies.pdf</a> for more details.</p>
<h4>values</h4>

<table class="fields long">
<tr><td class="val" id="ft_fstype_installable_embedding">FT_FSTYPE_INSTALLABLE_EMBEDDING</td><td class="desc">
<p>Fonts with no fsType bit set may be embedded and permanently installed on the remote system by an application.</p>
</td></tr>
<tr><td class="val" id="ft_fstype_restricted_license_embedding">FT_FSTYPE_RESTRICTED_LICENSE_EMBEDDING</td><td class="desc">
<p>Fonts that have only this bit set must not be modified, embedded or exchanged in any manner without first obtaining permission of the font software copyright owner.</p>
</td></tr>
<tr><td class="val" id="ft_fstype_preview_and_print_embedding">FT_FSTYPE_PREVIEW_AND_PRINT_EMBEDDING</td><td class="desc">
<p>The font may be embedded and temporarily loaded on the remote system. Documents containing Preview &amp; Print fonts must be opened &lsquo;read-only&rsquo;; no edits can be applied to the document.</p>
</td></tr>
<tr><td class="val" id="ft_fstype_editable_embedding">FT_FSTYPE_EDITABLE_EMBEDDING</td><td class="desc">
<p>The font may be embedded but must only be installed temporarily on other systems. In contrast to Preview &amp; Print fonts, documents containing editable fonts may be opened for reading, editing is permitted, and changes may be saved.</p>
</td></tr>
<tr><td class="val" id="ft_fstype_no_subsetting">FT_FSTYPE_NO_SUBSETTING</td><td class="desc">
<p>The font may not be subsetted prior to embedding.</p>
</td></tr>
<tr><td class="val" id="ft_fstype_bitmap_embedding_only">FT_FSTYPE_BITMAP_EMBEDDING_ONLY</td><td class="desc">
<p>Only bitmaps contained in the font may be embedded; no outline data may be embedded. If there are no bitmaps available in the font, then the font is unembeddable.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The flags are ORed together, thus more than a single value can be returned.</p>
<p>While the <code>fsType</code> flags can indicate that a font may be embedded, a license with the font vendor may be separately required to use the font in this way.</p>
<hr>

<h2 id="ft_has_fast_glyphs">FT_HAS_FAST_GLYPHS<a class="headerlink" href="#ft_has_fast_glyphs" title="Permanent link">&para;</a></h2>
<p>Defined in FT_FREETYPE_H (freetype/freetype.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_HAS_FAST_GLYPHS</b>( face )  0
</code></pre></div>

<p>Deprecated.</p>
<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-basic_types.html" title="Basic Data Types" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                Basic Data Types
              </span>
            </div>
          </a>
        
        
          <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Unicode Variation Sequences
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>