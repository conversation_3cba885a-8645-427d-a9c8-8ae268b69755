



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>Scanline Converter - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#scanline-converter" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                Scanline Converter
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4">
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link">
      Base Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link">
      Glyph Color Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link">
      Header File Macros
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5">
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link">
      Window FNT Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6">
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link">
      Subpixel Rendering
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8" checked>
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link">
      Outline Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        Scanline Converter
      </label>
    
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link md-nav__link--active">
      Scanline Converter
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster" class="md-nav__link">
    FT_Raster
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_span" class="md-nav__link">
    FT_Span
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_spanfunc" class="md-nav__link">
    FT_SpanFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_params" class="md-nav__link">
    FT_Raster_Params
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_flag_xxx" class="md-nav__link">
    FT_RASTER_FLAG_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_newfunc" class="md-nav__link">
    FT_Raster_NewFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_donefunc" class="md-nav__link">
    FT_Raster_DoneFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_resetfunc" class="md-nav__link">
    FT_Raster_ResetFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_setmodefunc" class="md-nav__link">
    FT_Raster_SetModeFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_renderfunc" class="md-nav__link">
    FT_Raster_RenderFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_funcs" class="md-nav__link">
    FT_Raster_Funcs
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_bittest_func" class="md-nav__link">
    FT_Raster_BitTest_Func
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_bitset_func" class="md-nav__link">
    FT_Raster_BitSet_Func
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link">
      System Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10">
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link">
      TrueTypeGX/AAT Validation
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster" class="md-nav__link">
    FT_Raster
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_span" class="md-nav__link">
    FT_Span
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_spanfunc" class="md-nav__link">
    FT_SpanFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_params" class="md-nav__link">
    FT_Raster_Params
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_flag_xxx" class="md-nav__link">
    FT_RASTER_FLAG_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_newfunc" class="md-nav__link">
    FT_Raster_NewFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_donefunc" class="md-nav__link">
    FT_Raster_DoneFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_resetfunc" class="md-nav__link">
    FT_Raster_ResetFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_setmodefunc" class="md-nav__link">
    FT_Raster_SetModeFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_renderfunc" class="md-nav__link">
    FT_Raster_RenderFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_funcs" class="md-nav__link">
    FT_Raster_Funcs
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_bittest_func" class="md-nav__link">
    FT_Raster_BitTest_Func
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_raster_bitset_func" class="md-nav__link">
    FT_Raster_BitSet_Func
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#support-api">Support API</a> &raquo; Scanline Converter</p>
<hr />
<h1 id="scanline-converter">Scanline Converter<a class="headerlink" href="#scanline-converter" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>This section contains technical definitions.</p>
<h2 id="ft_raster">FT_Raster<a class="headerlink" href="#ft_raster" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_RasterRec_*  <b>FT_Raster</b>;
</code></pre></div>

<p>An opaque handle (pointer) to a raster object. Each object can be used independently to convert an outline into a bitmap or pixmap.</p>
<hr>

<h2 id="ft_span">FT_Span<a class="headerlink" href="#ft_span" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Span_
  {
    <span class="keyword">short</span>           x;
    <span class="keyword">unsigned</span> <span class="keyword">short</span>  len;
    <span class="keyword">unsigned</span> <span class="keyword">char</span>   coverage;

  } <b>FT_Span</b>;
</code></pre></div>

<p>A structure used to model a single span of gray pixels when rendering an anti-aliased bitmap.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="x">x</td><td class="desc">
<p>The span's horizontal start position.</p>
</td></tr>
<tr><td class="val" id="len">len</td><td class="desc">
<p>The span's length in pixels.</p>
</td></tr>
<tr><td class="val" id="coverage">coverage</td><td class="desc">
<p>The span color/coverage, ranging from 0 (background) to 255 (foreground).</p>
</td></tr>
</table>

<h4>note</h4>

<p>This structure is used by the span drawing callback type named <code><a href="ft2-raster.html#ft_spanfunc">FT_SpanFunc</a></code> that takes the y&nbsp;coordinate of the span as a parameter.</p>
<p>The coverage value is always between 0 and 255. If you want less gray values, the callback function has to reduce them.</p>
<hr>

<h2 id="ft_spanfunc">FT_SpanFunc<a class="headerlink" href="#ft_spanfunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_SpanFunc</b>)( <span class="keyword">int</span>             y,
                  <span class="keyword">int</span>             count,
                  <span class="keyword">const</span> <a href="ft2-raster.html#ft_span">FT_Span</a>*  spans,
                  <span class="keyword">void</span>*           user );

#<span class="keyword">define</span> FT_Raster_Span_Func  <b>FT_SpanFunc</b>
</code></pre></div>

<p>A function used as a call-back by the anti-aliased renderer in order to let client applications draw themselves the gray pixel spans on each scan line.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="y">y</td><td class="desc">
<p>The scanline's upward y&nbsp;coordinate.</p>
</td></tr>
<tr><td class="val" id="count">count</td><td class="desc">
<p>The number of spans to draw on this scanline.</p>
</td></tr>
<tr><td class="val" id="spans">spans</td><td class="desc">
<p>A table of <code>count</code> spans to draw on the scanline.</p>
</td></tr>
<tr><td class="val" id="user">user</td><td class="desc">
<p>User-supplied data that is passed to the callback.</p>
</td></tr>
</table>

<h4>note</h4>

<p>This callback allows client applications to directly render the gray spans of the anti-aliased bitmap to any kind of surfaces.</p>
<p>This can be used to write anti-aliased outlines directly to a given background bitmap, and even perform translucency.</p>
<hr>

<h2 id="ft_raster_params">FT_Raster_Params<a class="headerlink" href="#ft_raster_params" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Raster_Params_
  {
    <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_bitmap">FT_Bitmap</a>*        target;
    <span class="keyword">const</span> <span class="keyword">void</span>*             source;
    <span class="keyword">int</span>                     flags;
    <a href="ft2-raster.html#ft_spanfunc">FT_SpanFunc</a>             gray_spans;
    <a href="ft2-raster.html#ft_spanfunc">FT_SpanFunc</a>             black_spans;  /* unused */
    <a href="ft2-raster.html#ft_raster_bittest_func">FT_Raster_BitTest_Func</a>  bit_test;     /* unused */
    <a href="ft2-raster.html#ft_raster_bitset_func">FT_Raster_BitSet_Func</a>   bit_set;      /* unused */
    <span class="keyword">void</span>*                   user;
    <a href="ft2-basic_types.html#ft_bbox">FT_BBox</a>                 clip_box;

  } <b>FT_Raster_Params</b>;
</code></pre></div>

<p>A structure to hold the parameters used by a raster's render function, passed as an argument to <code><a href="ft2-outline_processing.html#ft_outline_render">FT_Outline_Render</a></code>.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="target">target</td><td class="desc">
<p>The target bitmap.</p>
</td></tr>
<tr><td class="val" id="source">source</td><td class="desc">
<p>A pointer to the source glyph image (e.g., an <code><a href="ft2-outline_processing.html#ft_outline">FT_Outline</a></code>).</p>
</td></tr>
<tr><td class="val" id="flags">flags</td><td class="desc">
<p>The rendering flags.</p>
</td></tr>
<tr><td class="val" id="gray_spans">gray_spans</td><td class="desc">
<p>The gray span drawing callback.</p>
</td></tr>
<tr><td class="val" id="black_spans">black_spans</td><td class="desc">
<p>Unused.</p>
</td></tr>
<tr><td class="val" id="bit_test">bit_test</td><td class="desc">
<p>Unused.</p>
</td></tr>
<tr><td class="val" id="bit_set">bit_set</td><td class="desc">
<p>Unused.</p>
</td></tr>
<tr><td class="val" id="user">user</td><td class="desc">
<p>User-supplied data that is passed to each drawing callback.</p>
</td></tr>
<tr><td class="val" id="clip_box">clip_box</td><td class="desc">
<p>An optional span clipping box expressed in <em>integer</em> pixels (not in 26.6 fixed-point units).</p>
</td></tr>
</table>

<h4>note</h4>

<p>The <code><a href="ft2-raster.html#ft_raster_flag_xxx">FT_RASTER_FLAG_AA</a></code> bit flag must be set in the <code>flags</code> to generate an anti-aliased glyph bitmap, otherwise a monochrome bitmap is generated. The <code>target</code> should have appropriate pixel mode and its dimensions define the clipping region.</p>
<p>If both <code><a href="ft2-raster.html#ft_raster_flag_xxx">FT_RASTER_FLAG_AA</a></code> and <code><a href="ft2-raster.html#ft_raster_flag_xxx">FT_RASTER_FLAG_DIRECT</a></code> bit flags are set in <code>flags</code>, the raster calls an <code><a href="ft2-raster.html#ft_spanfunc">FT_SpanFunc</a></code> callback <code>gray_spans</code> with <code>user</code> data as an argument ignoring <code>target</code>. This allows direct composition over a pre-existing user surface to perform the span drawing and composition. To optionally clip the spans, set the <code><a href="ft2-raster.html#ft_raster_flag_xxx">FT_RASTER_FLAG_CLIP</a></code> flag and <code>clip_box</code>. The monochrome raster does not support the direct mode.</p>
<p>The gray-level rasterizer always uses 256 gray levels. If you want fewer gray levels, you have to use <code><a href="ft2-raster.html#ft_raster_flag_xxx">FT_RASTER_FLAG_DIRECT</a></code> and reduce the levels in the callback function.</p>
<hr>

<h2 id="ft_raster_flag_xxx">FT_RASTER_FLAG_XXX<a class="headerlink" href="#ft_raster_flag_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-raster.html#ft_raster_flag_default">FT_RASTER_FLAG_DEFAULT</a>  0x0
#<span class="keyword">define</span> <a href="ft2-raster.html#ft_raster_flag_aa">FT_RASTER_FLAG_AA</a>       0x1
#<span class="keyword">define</span> <a href="ft2-raster.html#ft_raster_flag_direct">FT_RASTER_FLAG_DIRECT</a>   0x2
#<span class="keyword">define</span> <a href="ft2-raster.html#ft_raster_flag_clip">FT_RASTER_FLAG_CLIP</a>     0x4

  /* these constants are deprecated; use the corresponding */
  /* `<b>FT_RASTER_FLAG_XXX</b>` values instead                   */
#<span class="keyword">define</span> ft_raster_flag_default  <a href="ft2-raster.html#ft_raster_flag_default">FT_RASTER_FLAG_DEFAULT</a>
#<span class="keyword">define</span> ft_raster_flag_aa       <a href="ft2-raster.html#ft_raster_flag_aa">FT_RASTER_FLAG_AA</a>
#<span class="keyword">define</span> ft_raster_flag_direct   <a href="ft2-raster.html#ft_raster_flag_direct">FT_RASTER_FLAG_DIRECT</a>
#<span class="keyword">define</span> ft_raster_flag_clip     <a href="ft2-raster.html#ft_raster_flag_clip">FT_RASTER_FLAG_CLIP</a>
</code></pre></div>

<p>A list of bit flag constants as used in the <code>flags</code> field of a <code><a href="ft2-raster.html#ft_raster_params">FT_Raster_Params</a></code> structure.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_raster_flag_default">FT_RASTER_FLAG_DEFAULT</td><td class="desc">
<p>This value is 0.</p>
</td></tr>
<tr><td class="val" id="ft_raster_flag_aa">FT_RASTER_FLAG_AA</td><td class="desc">
<p>This flag is set to indicate that an anti-aliased glyph image should be generated. Otherwise, it will be monochrome (1-bit).</p>
</td></tr>
<tr><td class="val" id="ft_raster_flag_direct">FT_RASTER_FLAG_DIRECT</td><td class="desc">
<p>This flag is set to indicate direct rendering. In this mode, client applications must provide their own span callback. This lets them directly draw or compose over an existing bitmap. If this bit is <em>not</em> set, the target pixmap's buffer <em>must</em> be zeroed before rendering and the output will be clipped to its size.</p>
<p>Direct rendering is only possible with anti-aliased glyphs.</p>
</td></tr>
<tr><td class="val" id="ft_raster_flag_clip">FT_RASTER_FLAG_CLIP</td><td class="desc">
<p>This flag is only used in direct rendering mode. If set, the output will be clipped to a box specified in the <code>clip_box</code> field of the <code><a href="ft2-raster.html#ft_raster_params">FT_Raster_Params</a></code> structure. Otherwise, the <code>clip_box</code> is effectively set to the bounding box and all spans are generated.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_raster_newfunc">FT_Raster_NewFunc<a class="headerlink" href="#ft_raster_newfunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Raster_NewFunc</b>)( <span class="keyword">void</span>*       memory,
                        <a href="ft2-raster.html#ft_raster">FT_Raster</a>*  raster );

#<span class="keyword">define</span> FT_Raster_New_Func  <b>FT_Raster_NewFunc</b>
</code></pre></div>

<p>A function used to create a new raster object.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>A handle to the memory allocator.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the new raster object.</p>
</td></tr>
</table>

<h4>return</h4>

<p>Error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>The <code>memory</code> parameter is a typeless pointer in order to avoid un-wanted dependencies on the rest of the FreeType code. In practice, it is an <code><a href="ft2-system_interface.html#ft_memory">FT_Memory</a></code> object, i.e., a handle to the standard FreeType memory allocator. However, this field can be completely ignored by a given raster implementation.</p>
<hr>

<h2 id="ft_raster_donefunc">FT_Raster_DoneFunc<a class="headerlink" href="#ft_raster_donefunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Raster_DoneFunc</b>)( <a href="ft2-raster.html#ft_raster">FT_Raster</a>  raster );

#<span class="keyword">define</span> FT_Raster_Done_Func  <b>FT_Raster_DoneFunc</b>
</code></pre></div>

<p>A function used to destroy a given raster object.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the raster object.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_raster_resetfunc">FT_Raster_ResetFunc<a class="headerlink" href="#ft_raster_resetfunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Raster_ResetFunc</b>)( <a href="ft2-raster.html#ft_raster">FT_Raster</a>       raster,
                          <span class="keyword">unsigned</span> <span class="keyword">char</span>*  pool_base,
                          <span class="keyword">unsigned</span> <span class="keyword">long</span>   pool_size );

#<span class="keyword">define</span> FT_Raster_Reset_Func  <b>FT_Raster_ResetFunc</b>
</code></pre></div>

<p>FreeType used to provide an area of memory called the &lsquo;render pool&rsquo; available to all registered rasterizers. This was not thread safe, however, and now FreeType never allocates this pool.</p>
<p>This function is called after a new raster object is created.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the new raster object.</p>
</td></tr>
<tr><td class="val" id="pool_base">pool_base</td><td class="desc">
<p>Previously, the address in memory of the render pool. Set this to <code>NULL</code>.</p>
</td></tr>
<tr><td class="val" id="pool_size">pool_size</td><td class="desc">
<p>Previously, the size in bytes of the render pool. Set this to 0.</p>
</td></tr>
</table>

<h4>note</h4>

<p>Rasterizers should rely on dynamic or stack allocation if they want to (a handle to the memory allocator is passed to the rasterizer constructor).</p>
<hr>

<h2 id="ft_raster_setmodefunc">FT_Raster_SetModeFunc<a class="headerlink" href="#ft_raster_setmodefunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Raster_SetModeFunc</b>)( <a href="ft2-raster.html#ft_raster">FT_Raster</a>      raster,
                            <span class="keyword">unsigned</span> <span class="keyword">long</span>  mode,
                            <span class="keyword">void</span>*          args );

#<span class="keyword">define</span> FT_Raster_Set_Mode_Func  <b>FT_Raster_SetModeFunc</b>
</code></pre></div>

<p>This function is a generic facility to change modes or attributes in a given raster. This can be used for debugging purposes, or simply to allow implementation-specific &lsquo;features&rsquo; in a given raster module.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the new raster object.</p>
</td></tr>
<tr><td class="val" id="mode">mode</td><td class="desc">
<p>A 4-byte tag used to name the mode or property.</p>
</td></tr>
<tr><td class="val" id="args">args</td><td class="desc">
<p>A pointer to the new mode/property to use.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_raster_renderfunc">FT_Raster_RenderFunc<a class="headerlink" href="#ft_raster_renderfunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Raster_RenderFunc</b>)( <a href="ft2-raster.html#ft_raster">FT_Raster</a>                raster,
                           <span class="keyword">const</span> <a href="ft2-raster.html#ft_raster_params">FT_Raster_Params</a>*  params );

#<span class="keyword">define</span> FT_Raster_Render_Func  <b>FT_Raster_RenderFunc</b>
</code></pre></div>

<p>Invoke a given raster to scan-convert a given glyph image into a target bitmap.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="raster">raster</td><td class="desc">
<p>A handle to the raster object.</p>
</td></tr>
<tr><td class="val" id="params">params</td><td class="desc">
<p>A pointer to an <code><a href="ft2-raster.html#ft_raster_params">FT_Raster_Params</a></code> structure used to store the rendering parameters.</p>
</td></tr>
</table>

<h4>return</h4>

<p>Error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>The exact format of the source image depends on the raster's glyph format defined in its <code><a href="ft2-raster.html#ft_raster_funcs">FT_Raster_Funcs</a></code> structure. It can be an <code><a href="ft2-outline_processing.html#ft_outline">FT_Outline</a></code> or anything else in order to support a large array of glyph formats.</p>
<p>Note also that the render function can fail and return a <code>FT_Err_Unimplemented_Feature</code> error code if the raster used does not support direct composition.</p>
<hr>

<h2 id="ft_raster_funcs">FT_Raster_Funcs<a class="headerlink" href="#ft_raster_funcs" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Raster_Funcs_
  {
    <a href="ft2-basic_types.html#ft_glyph_format">FT_Glyph_Format</a>        glyph_format;

    <a href="ft2-raster.html#ft_raster_newfunc">FT_Raster_NewFunc</a>      raster_new;
    <a href="ft2-raster.html#ft_raster_resetfunc">FT_Raster_ResetFunc</a>    raster_reset;
    <a href="ft2-raster.html#ft_raster_setmodefunc">FT_Raster_SetModeFunc</a>  raster_set_mode;
    <a href="ft2-raster.html#ft_raster_renderfunc">FT_Raster_RenderFunc</a>   raster_render;
    <a href="ft2-raster.html#ft_raster_donefunc">FT_Raster_DoneFunc</a>     raster_done;

  } <b>FT_Raster_Funcs</b>;
</code></pre></div>

<p>A structure used to describe a given raster class to the library.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="glyph_format">glyph_format</td><td class="desc">
<p>The supported glyph format for this raster.</p>
</td></tr>
<tr><td class="val" id="raster_new">raster_new</td><td class="desc">
<p>The raster constructor.</p>
</td></tr>
<tr><td class="val" id="raster_reset">raster_reset</td><td class="desc">
<p>Used to reset the render pool within the raster.</p>
</td></tr>
<tr><td class="val" id="raster_render">raster_render</td><td class="desc">
<p>A function to render a glyph into a given bitmap.</p>
</td></tr>
<tr><td class="val" id="raster_done">raster_done</td><td class="desc">
<p>The raster destructor.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_raster_bittest_func">FT_Raster_BitTest_Func<a class="headerlink" href="#ft_raster_bittest_func" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Raster_BitTest_Func</b>)( <span class="keyword">int</span>    y,
                             <span class="keyword">int</span>    x,
                             <span class="keyword">void</span>*  user );
</code></pre></div>

<p>Deprecated, unimplemented.</p>
<hr>

<h2 id="ft_raster_bitset_func">FT_Raster_BitSet_Func<a class="headerlink" href="#ft_raster_bitset_func" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Raster_BitSet_Func</b>)( <span class="keyword">int</span>    y,
                            <span class="keyword">int</span>    x,
                            <span class="keyword">void</span>*  user );
</code></pre></div>

<p>Deprecated, unimplemented.</p>
<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                Bitmap Handling
              </span>
            </div>
          </a>
        
        
          <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Glyph Stroker
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>