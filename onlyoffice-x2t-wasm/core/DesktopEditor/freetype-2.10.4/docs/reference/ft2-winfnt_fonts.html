



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>Window FNT Files - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#window-fnt-files" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                Window FNT Files
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4">
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link">
      Base Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link">
      Glyph Color Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link">
      Header File Macros
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5" checked>
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        Window FNT Files
      </label>
    
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link md-nav__link--active">
      Window FNT Files
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_winfnt_id_xxx" class="md-nav__link">
    FT_WinFNT_ID_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_winfnt_headerrec" class="md-nav__link">
    FT_WinFNT_HeaderRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_winfnt_header" class="md-nav__link">
    FT_WinFNT_Header
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_winfnt_header" class="md-nav__link">
    FT_Get_WinFNT_Header
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6">
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link">
      Subpixel Rendering
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8">
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link">
      Outline Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link">
      Scanline Converter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link">
      System Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10">
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link">
      TrueTypeGX/AAT Validation
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_winfnt_id_xxx" class="md-nav__link">
    FT_WinFNT_ID_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_winfnt_headerrec" class="md-nav__link">
    FT_WinFNT_HeaderRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_winfnt_header" class="md-nav__link">
    FT_WinFNT_Header
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_get_winfnt_header" class="md-nav__link">
    FT_Get_WinFNT_Header
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#format-specific-api">Format-Specific API</a> &raquo; Window FNT Files</p>
<hr />
<h1 id="window-fnt-files">Window FNT Files<a class="headerlink" href="#window-fnt-files" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>This section contains the declaration of Windows FNT-specific functions.</p>
<h2 id="ft_winfnt_id_xxx">FT_WinFNT_ID_XXX<a class="headerlink" href="#ft_winfnt_id_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_WINFONTS_H (freetype/ftwinfnt.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1252">FT_WinFNT_ID_CP1252</a>    0
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_default">FT_WinFNT_ID_DEFAULT</a>   1
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_symbol">FT_WinFNT_ID_SYMBOL</a>    2
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_mac">FT_WinFNT_ID_MAC</a>      77
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp932">FT_WinFNT_ID_CP932</a>   128
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp949">FT_WinFNT_ID_CP949</a>   129
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1361">FT_WinFNT_ID_CP1361</a>  130
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp936">FT_WinFNT_ID_CP936</a>   134
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp950">FT_WinFNT_ID_CP950</a>   136
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1253">FT_WinFNT_ID_CP1253</a>  161
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1254">FT_WinFNT_ID_CP1254</a>  162
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1258">FT_WinFNT_ID_CP1258</a>  163
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1255">FT_WinFNT_ID_CP1255</a>  177
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1256">FT_WinFNT_ID_CP1256</a>  178
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1257">FT_WinFNT_ID_CP1257</a>  186
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1251">FT_WinFNT_ID_CP1251</a>  204
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp874">FT_WinFNT_ID_CP874</a>   222
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_cp1250">FT_WinFNT_ID_CP1250</a>  238
#<span class="keyword">define</span> <a href="ft2-winfnt_fonts.html#ft_winfnt_id_oem">FT_WinFNT_ID_OEM</a>     255
</code></pre></div>

<p>A list of valid values for the <code>charset</code> byte in <code><a href="ft2-winfnt_fonts.html#ft_winfnt_headerrec">FT_WinFNT_HeaderRec</a></code>. Exact mapping tables for the various &lsquo;cpXXXX&rsquo; encodings (except for &lsquo;cp1361&rsquo;) can be found at &lsquo;<a href="ftp://ftp.unicode.org/Public/">ftp://ftp.unicode.org/Public/</a>&rsquo; in the <code>MAPPINGS/VENDORS/MICSFT/WINDOWS</code> subdirectory. &lsquo;cp1361&rsquo; is roughly a superset of <code>MAPPINGS/OBSOLETE/EASTASIA/KSC/JOHAB.TXT</code>.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_winfnt_id_default">FT_WinFNT_ID_DEFAULT</td><td class="desc">
<p>This is used for font enumeration and font creation as a &lsquo;don't care&rsquo; value. Valid font files don't contain this value. When querying for information about the character set of the font that is currently selected into a specified device context, this return value (of the related Windows API) simply denotes failure.</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_symbol">FT_WinFNT_ID_SYMBOL</td><td class="desc">
<p>There is no known mapping table available.</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_mac">FT_WinFNT_ID_MAC</td><td class="desc">
<p>Mac Roman encoding.</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_oem">FT_WinFNT_ID_OEM</td><td class="desc">
<p>From Michael Poettgen &lt;<EMAIL>&gt;:</p>
<p>The &lsquo;Windows Font Mapping&rsquo; article says that <code>FT_WinFNT_ID_OEM</code> is used for the charset of vector fonts, like <code>modern.fon</code>, <code>roman.fon</code>, and <code>script.fon</code> on Windows.</p>
<p>The &lsquo;CreateFont&rsquo; documentation says: The <code>FT_WinFNT_ID_OEM</code> value specifies a character set that is operating-system dependent.</p>
<p>The &lsquo;IFIMETRICS&rsquo; documentation from the &lsquo;Windows Driver Development Kit&rsquo; says: This font supports an OEM-specific character set. The OEM character set is system dependent.</p>
<p>In general OEM, as opposed to ANSI (i.e., &lsquo;cp1252&rsquo;), denotes the second default codepage that most international versions of Windows have. It is one of the OEM codepages from</p>
<p><a href="https://docs.microsoft.com/en-us/windows/desktop/intl/code-page-identifiers">https://docs.microsoft.com/en-us/windows/desktop/intl/code-page-identifiers</a> ,</p>
<p>and is used for the &lsquo;DOS boxes&rsquo;, to support legacy applications. A German Windows version for example usually uses ANSI codepage 1252 and OEM codepage 850.</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp874">FT_WinFNT_ID_CP874</td><td class="desc">
<p>A superset of Thai TIS 620 and ISO 8859-11.</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp932">FT_WinFNT_ID_CP932</td><td class="desc">
<p>A superset of Japanese Shift-JIS (with minor deviations).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp936">FT_WinFNT_ID_CP936</td><td class="desc">
<p>A superset of simplified Chinese GB 2312-1980 (with different ordering and minor deviations).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp949">FT_WinFNT_ID_CP949</td><td class="desc">
<p>A superset of Korean Hangul KS&nbsp;C 5601-1987 (with different ordering and minor deviations).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp950">FT_WinFNT_ID_CP950</td><td class="desc">
<p>A superset of traditional Chinese Big&nbsp;5 ETen (with different ordering and minor deviations).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1250">FT_WinFNT_ID_CP1250</td><td class="desc">
<p>A superset of East European ISO 8859-2 (with slightly different ordering).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1251">FT_WinFNT_ID_CP1251</td><td class="desc">
<p>A superset of Russian ISO 8859-5 (with different ordering).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1252">FT_WinFNT_ID_CP1252</td><td class="desc">
<p>ANSI encoding. A superset of ISO 8859-1.</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1253">FT_WinFNT_ID_CP1253</td><td class="desc">
<p>A superset of Greek ISO 8859-7 (with minor modifications).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1254">FT_WinFNT_ID_CP1254</td><td class="desc">
<p>A superset of Turkish ISO 8859-9.</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1255">FT_WinFNT_ID_CP1255</td><td class="desc">
<p>A superset of Hebrew ISO 8859-8 (with some modifications).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1256">FT_WinFNT_ID_CP1256</td><td class="desc">
<p>A superset of Arabic ISO 8859-6 (with different ordering).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1257">FT_WinFNT_ID_CP1257</td><td class="desc">
<p>A superset of Baltic ISO 8859-13 (with some deviations).</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1258">FT_WinFNT_ID_CP1258</td><td class="desc">
<p>For Vietnamese. This encoding doesn't cover all necessary characters.</p>
</td></tr>
<tr><td class="val" id="ft_winfnt_id_cp1361">FT_WinFNT_ID_CP1361</td><td class="desc">
<p>Korean (Johab).</p>
</td></tr>
</table>

<hr>

<h2 id="ft_winfnt_headerrec">FT_WinFNT_HeaderRec<a class="headerlink" href="#ft_winfnt_headerrec" title="Permanent link">&para;</a></h2>
<p>Defined in FT_WINFONTS_H (freetype/ftwinfnt.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_WinFNT_HeaderRec_
  {
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  version;
    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>   file_size;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    copyright[60];
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  file_type;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  nominal_point_size;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  vertical_resolution;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  horizontal_resolution;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  ascent;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  internal_leading;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  external_leading;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    italic;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    underline;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    strike_out;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  weight;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    charset;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  pixel_width;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  pixel_height;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    pitch_and_family;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  avg_width;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  max_width;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    first_char;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    last_char;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    default_char;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    break_char;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  bytes_per_row;
    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>   device_offset;
    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>   face_name_offset;
    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>   bits_pointer;
    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>   bits_offset;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>    reserved;
    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>   flags;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  A_space;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  B_space;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  C_space;
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>  color_table_offset;
    <a href="ft2-basic_types.html#ft_ulong">FT_ULong</a>   reserved1[4];

  } <b>FT_WinFNT_HeaderRec</b>;
</code></pre></div>

<p>Windows FNT Header info.</p>
<hr>

<h2 id="ft_winfnt_header">FT_WinFNT_Header<a class="headerlink" href="#ft_winfnt_header" title="Permanent link">&para;</a></h2>
<p>Defined in FT_WINFONTS_H (freetype/ftwinfnt.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_WinFNT_HeaderRec_*  <b>FT_WinFNT_Header</b>;
</code></pre></div>

<p>A handle to an <code><a href="ft2-winfnt_fonts.html#ft_winfnt_headerrec">FT_WinFNT_HeaderRec</a></code> structure.</p>
<hr>

<h2 id="ft_get_winfnt_header">FT_Get_WinFNT_Header<a class="headerlink" href="#ft_get_winfnt_header" title="Permanent link">&para;</a></h2>
<p>Defined in FT_WINFONTS_H (freetype/ftwinfnt.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Get_WinFNT_Header</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>               face,
                        <a href="ft2-winfnt_fonts.html#ft_winfnt_headerrec">FT_WinFNT_HeaderRec</a>  *aheader );
</code></pre></div>

<p>Retrieve a Windows FNT font info header.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the input face.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="aheader">aheader</td><td class="desc">
<p>The WinFNT header.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>This function only works with Windows FNT faces, returning an error otherwise.</p>
<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                PFR Fonts
              </span>
            </div>
          </a>
        
        
          <a href="ft2-font_formats.html" title="Font Formats" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Font Formats
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>