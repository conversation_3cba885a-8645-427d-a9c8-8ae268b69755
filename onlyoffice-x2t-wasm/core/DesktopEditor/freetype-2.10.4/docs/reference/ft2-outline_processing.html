



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>Outline Processing - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#outline-processing" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                Outline Processing
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4">
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link">
      Base Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link">
      Glyph Color Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link">
      Header File Macros
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5">
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link">
      Window FNT Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6">
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link">
      Subpixel Rendering
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8" checked>
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        Outline Processing
      </label>
    
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link md-nav__link--active">
      Outline Processing
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline" class="md-nav__link">
    FT_Outline
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_new" class="md-nav__link">
    FT_Outline_New
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_done" class="md-nav__link">
    FT_Outline_Done
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_copy" class="md-nav__link">
    FT_Outline_Copy
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_translate" class="md-nav__link">
    FT_Outline_Translate
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_transform" class="md-nav__link">
    FT_Outline_Transform
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_embolden" class="md-nav__link">
    FT_Outline_Embolden
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_emboldenxy" class="md-nav__link">
    FT_Outline_EmboldenXY
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_reverse" class="md-nav__link">
    FT_Outline_Reverse
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_check" class="md-nav__link">
    FT_Outline_Check
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_get_cbox" class="md-nav__link">
    FT_Outline_Get_CBox
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_get_bbox" class="md-nav__link">
    FT_Outline_Get_BBox
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_get_bitmap" class="md-nav__link">
    FT_Outline_Get_Bitmap
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_render" class="md-nav__link">
    FT_Outline_Render
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_decompose" class="md-nav__link">
    FT_Outline_Decompose
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_funcs" class="md-nav__link">
    FT_Outline_Funcs
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_movetofunc" class="md-nav__link">
    FT_Outline_MoveToFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_linetofunc" class="md-nav__link">
    FT_Outline_LineToFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_conictofunc" class="md-nav__link">
    FT_Outline_ConicToFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_cubictofunc" class="md-nav__link">
    FT_Outline_CubicToFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_orientation" class="md-nav__link">
    FT_Orientation
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_get_orientation" class="md-nav__link">
    FT_Outline_Get_Orientation
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_xxx" class="md-nav__link">
    FT_OUTLINE_XXX
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link">
      Scanline Converter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link">
      System Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10">
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link">
      TrueTypeGX/AAT Validation
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline" class="md-nav__link">
    FT_Outline
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_new" class="md-nav__link">
    FT_Outline_New
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_done" class="md-nav__link">
    FT_Outline_Done
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_copy" class="md-nav__link">
    FT_Outline_Copy
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_translate" class="md-nav__link">
    FT_Outline_Translate
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_transform" class="md-nav__link">
    FT_Outline_Transform
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_embolden" class="md-nav__link">
    FT_Outline_Embolden
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_emboldenxy" class="md-nav__link">
    FT_Outline_EmboldenXY
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_reverse" class="md-nav__link">
    FT_Outline_Reverse
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_check" class="md-nav__link">
    FT_Outline_Check
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_get_cbox" class="md-nav__link">
    FT_Outline_Get_CBox
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_get_bbox" class="md-nav__link">
    FT_Outline_Get_BBox
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_get_bitmap" class="md-nav__link">
    FT_Outline_Get_Bitmap
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_render" class="md-nav__link">
    FT_Outline_Render
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_decompose" class="md-nav__link">
    FT_Outline_Decompose
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_funcs" class="md-nav__link">
    FT_Outline_Funcs
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_movetofunc" class="md-nav__link">
    FT_Outline_MoveToFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_linetofunc" class="md-nav__link">
    FT_Outline_LineToFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_conictofunc" class="md-nav__link">
    FT_Outline_ConicToFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_cubictofunc" class="md-nav__link">
    FT_Outline_CubicToFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_orientation" class="md-nav__link">
    FT_Orientation
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_get_orientation" class="md-nav__link">
    FT_Outline_Get_Orientation
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_xxx" class="md-nav__link">
    FT_OUTLINE_XXX
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#support-api">Support API</a> &raquo; Outline Processing</p>
<hr />
<h1 id="outline-processing">Outline Processing<a class="headerlink" href="#outline-processing" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>This section contains routines used to create and destroy scalable glyph images known as &lsquo;outlines&rsquo;. These can also be measured, transformed, and converted into bitmaps and pixmaps.</p>
<h2 id="ft_outline">FT_Outline<a class="headerlink" href="#ft_outline" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Outline_
  {
    <span class="keyword">short</span>       n_contours;      /* number of contours in glyph        */
    <span class="keyword">short</span>       n_points;        /* number of points in the glyph      */

    <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  points;          /* the outline's points               */
    <span class="keyword">char</span>*       tags;            /* the points flags                   */
    <span class="keyword">short</span>*      contours;        /* the contour end points             */

    <span class="keyword">int</span>         flags;           /* outline masks                      */

  } <b>FT_Outline</b>;
</code></pre></div>

<p>This structure is used to describe an outline to the scan-line converter.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="n_contours">n_contours</td><td class="desc">
<p>The number of contours in the outline.</p>
</td></tr>
<tr><td class="val" id="n_points">n_points</td><td class="desc">
<p>The number of points in the outline.</p>
</td></tr>
<tr><td class="val" id="points">points</td><td class="desc">
<p>A pointer to an array of <code>n_points</code> <code><a href="ft2-basic_types.html#ft_vector">FT_Vector</a></code> elements, giving the outline's point coordinates.</p>
</td></tr>
<tr><td class="val" id="tags">tags</td><td class="desc">
<p>A pointer to an array of <code>n_points</code> chars, giving each outline point's type.</p>
<p>If bit&nbsp;0 is unset, the point is &lsquo;off&rsquo; the curve, i.e., a Bezier control point, while it is &lsquo;on&rsquo; if set.</p>
<p>Bit&nbsp;1 is meaningful for &lsquo;off&rsquo; points only. If set, it indicates a third-order Bezier arc control point; and a second-order control point if unset.</p>
<p>If bit&nbsp;2 is set, bits 5-7 contain the drop-out mode (as defined in the OpenType specification; the value is the same as the argument to the &lsquo;SCANMODE&rsquo; instruction).</p>
<p>Bits 3 and&nbsp;4 are reserved for internal purposes.</p>
</td></tr>
<tr><td class="val" id="contours">contours</td><td class="desc">
<p>An array of <code>n_contours</code> shorts, giving the end point of each contour within the outline. For example, the first contour is defined by the points &lsquo;0&rsquo; to <code>contours[0]</code>, the second one is defined by the points <code>contours[0]+1</code> to <code>contours[1]</code>, etc.</p>
</td></tr>
<tr><td class="val" id="flags">flags</td><td class="desc">
<p>A set of bit flags used to characterize the outline and give hints to the scan-converter and hinter on how to convert/grid-fit it. See <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_XXX</a></code>.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The B/W rasterizer only checks bit&nbsp;2 in the <code>tags</code> array for the first point of each contour. The drop-out mode as given with <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_IGNORE_DROPOUTS</a></code>, <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_SMART_DROPOUTS</a></code>, and <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_INCLUDE_STUBS</a></code> in <code>flags</code> is then overridden.</p>
<hr>

<h2 id="ft_outline_new">FT_Outline_New<a class="headerlink" href="#ft_outline_new" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_New</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>   library,
                  <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>      numPoints,
                  <a href="ft2-basic_types.html#ft_int">FT_Int</a>       numContours,
                  <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>  *anoutline );
</code></pre></div>

<p>Create a new outline of a given size.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the library object from where the outline is allocated. Note however that the new outline will <strong>not</strong> necessarily be <strong>freed</strong>, when destroying the library, by <code><a href="ft2-base_interface.html#ft_done_freetype">FT_Done_FreeType</a></code>.</p>
</td></tr>
<tr><td class="val" id="numpoints">numPoints</td><td class="desc">
<p>The maximum number of points within the outline. Must be smaller than or equal to 0xFFFF (65535).</p>
</td></tr>
<tr><td class="val" id="numcontours">numContours</td><td class="desc">
<p>The maximum number of contours within the outline. This value must be in the range 0 to <code>numPoints</code>.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="anoutline">anoutline</td><td class="desc">
<p>A handle to the new outline.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>The reason why this function takes a <code>library</code> parameter is simply to use the library's memory allocator.</p>
<hr>

<h2 id="ft_outline_done">FT_Outline_Done<a class="headerlink" href="#ft_outline_done" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_Done</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>   library,
                   <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline );
</code></pre></div>

<p>Destroy an outline created with <code><a href="ft2-outline_processing.html#ft_outline_new">FT_Outline_New</a></code>.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle of the library object used to allocate the outline.</p>
</td></tr>
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the outline object to be discarded.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>If the outline's &lsquo;owner&rsquo; field is not set, only the outline descriptor will be released.</p>
<hr>

<h2 id="ft_outline_copy">FT_Outline_Copy<a class="headerlink" href="#ft_outline_copy" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_Copy</b>( <span class="keyword">const</span> <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  source,
                   <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>        *target );
</code></pre></div>

<p>Copy an outline into another one. Both objects must have the same sizes (number of points &amp; number of contours) when this function is called.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="source">source</td><td class="desc">
<p>A handle to the source outline.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="target">target</td><td class="desc">
<p>A handle to the target outline.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<hr>

<h2 id="ft_outline_translate">FT_Outline_Translate<a class="headerlink" href="#ft_outline_translate" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Outline_Translate</b>( <span class="keyword">const</span> <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline,
                        <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>             xOffset,
                        <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>             yOffset );
</code></pre></div>

<p>Apply a simple translation to the points of an outline.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the target outline descriptor.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="xoffset">xOffset</td><td class="desc">
<p>The horizontal offset.</p>
</td></tr>
<tr><td class="val" id="yoffset">yOffset</td><td class="desc">
<p>The vertical offset.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_outline_transform">FT_Outline_Transform<a class="headerlink" href="#ft_outline_transform" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Outline_Transform</b>( <span class="keyword">const</span> <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline,
                        <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_matrix">FT_Matrix</a>*   matrix );
</code></pre></div>

<p>Apply a simple 2x2 matrix to all of an outline's points. Useful for applying rotations, slanting, flipping, etc.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the target outline descriptor.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="matrix">matrix</td><td class="desc">
<p>A pointer to the transformation matrix.</p>
</td></tr>
</table>

<h4>note</h4>

<p>You can use <code><a href="ft2-outline_processing.html#ft_outline_translate">FT_Outline_Translate</a></code> if you need to translate the outline's points.</p>
<hr>

<h2 id="ft_outline_embolden">FT_Outline_Embolden<a class="headerlink" href="#ft_outline_embolden" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_Embolden</b>( <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline,
                       <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>       strength );
</code></pre></div>

<p>Embolden an outline. The new outline will be at most 4&nbsp;times <code>strength</code> pixels wider and higher. You may think of the left and bottom borders as unchanged.</p>
<p>Negative <code>strength</code> values to reduce the outline thickness are possible also.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A handle to the target outline.</p>
</td></tr>
</table>

<h4>input</h4>

<table class="fields">
<tr><td class="val" id="strength">strength</td><td class="desc">
<p>How strong the glyph is emboldened. Expressed in 26.6 pixel format.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>The used algorithm to increase or decrease the thickness of the glyph doesn't change the number of points; this means that certain situations like acute angles or intersections are sometimes handled incorrectly.</p>
<p>If you need &lsquo;better&rsquo; metrics values you should call <code><a href="ft2-outline_processing.html#ft_outline_get_cbox">FT_Outline_Get_CBox</a></code> or <code><a href="ft2-outline_processing.html#ft_outline_get_bbox">FT_Outline_Get_BBox</a></code>.</p>
<p>To get meaningful results, font scaling values must be set with functions like <code><a href="ft2-base_interface.html#ft_set_char_size">FT_Set_Char_Size</a></code> before calling FT_Render_Glyph.</p>
<h4>example</h4>

<div class="highlight"><pre><span></span><code>  FT_Load_Glyph( face, index, FT_LOAD_DEFAULT );

  if ( face-&gt;glyph-&gt;format == FT_GLYPH_FORMAT_OUTLINE )
    FT_Outline_Embolden( &amp;face-&gt;glyph-&gt;outline, strength );
</code></pre></div>

<hr>

<h2 id="ft_outline_emboldenxy">FT_Outline_EmboldenXY<a class="headerlink" href="#ft_outline_emboldenxy" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_EmboldenXY</b>( <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline,
                         <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>       xstrength,
                         <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>       ystrength );
</code></pre></div>

<p>Embolden an outline. The new outline will be <code>xstrength</code> pixels wider and <code>ystrength</code> pixels higher. Otherwise, it is similar to <code><a href="ft2-outline_processing.html#ft_outline_embolden">FT_Outline_Embolden</a></code>, which uses the same strength in both directions.</p>
<h4>since</h4>

<p>2.4.10</p>
<hr>

<h2 id="ft_outline_reverse">FT_Outline_Reverse<a class="headerlink" href="#ft_outline_reverse" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Outline_Reverse</b>( <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline );
</code></pre></div>

<p>Reverse the drawing direction of an outline. This is used to ensure consistent fill conventions for mirrored glyphs.</p>
<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the target outline descriptor.</p>
</td></tr>
</table>

<h4>note</h4>

<p>This function toggles the bit flag <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_REVERSE_FILL</a></code> in the outline's <code>flags</code> field.</p>
<p>It shouldn't be used by a normal client application, unless it knows what it is doing.</p>
<hr>

<h2 id="ft_outline_check">FT_Outline_Check<a class="headerlink" href="#ft_outline_check" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_Check</b>( <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline );
</code></pre></div>

<p>Check the contents of an outline descriptor.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A handle to a source outline.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>An empty outline, or an outline with a single point only is also valid.</p>
<hr>

<h2 id="ft_outline_get_cbox">FT_Outline_Get_CBox<a class="headerlink" href="#ft_outline_get_cbox" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Outline_Get_CBox</b>( <span class="keyword">const</span> <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline,
                       <a href="ft2-basic_types.html#ft_bbox">FT_BBox</a>           *acbox );
</code></pre></div>

<p>Return an outline's &lsquo;control box&rsquo;. The control box encloses all the outline's points, including Bezier control points. Though it coincides with the exact bounding box for most glyphs, it can be slightly larger in some situations (like when rotating an outline that contains Bezier outside arcs).</p>
<p>Computing the control box is very fast, while getting the bounding box can take much more time as it needs to walk over all segments and arcs in the outline. To get the latter, you can use the &lsquo;ftbbox&rsquo; component, which is dedicated to this single task.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the source outline descriptor.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="acbox">acbox</td><td class="desc">
<p>The outline's control box.</p>
</td></tr>
</table>

<h4>note</h4>

<p>See <code><a href="ft2-glyph_management.html#ft_glyph_get_cbox">FT_Glyph_Get_CBox</a></code> for a discussion of tricky fonts.</p>
<hr>

<h2 id="ft_outline_get_bbox">FT_Outline_Get_BBox<a class="headerlink" href="#ft_outline_get_bbox" title="Permanent link">&para;</a></h2>
<p>Defined in FT_BBOX_H (freetype/ftbbox.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_Get_BBox</b>( <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline,
                       <a href="ft2-basic_types.html#ft_bbox">FT_BBox</a>     *abbox );
</code></pre></div>

<p>Compute the exact bounding box of an outline. This is slower than computing the control box. However, it uses an advanced algorithm that returns <em>very</em> quickly when the two boxes coincide. Otherwise, the outline Bezier arcs are traversed to extract their extrema.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the source outline.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="abbox">abbox</td><td class="desc">
<p>The outline's exact bounding box.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>If the font is tricky and the glyph has been loaded with <code><a href="ft2-base_interface.html#ft_load_xxx">FT_LOAD_NO_SCALE</a></code>, the resulting BBox is meaningless. To get reasonable values for the BBox it is necessary to load the glyph at a large ppem value (so that the hinting instructions can properly shift and scale the subglyphs), then extracting the BBox, which can be eventually converted back to font units.</p>
<hr>

<h2 id="ft_outline_get_bitmap">FT_Outline_Get_Bitmap<a class="headerlink" href="#ft_outline_get_bitmap" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_Get_Bitmap</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>        library,
                         <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*       outline,
                         <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_bitmap">FT_Bitmap</a>  *abitmap );
</code></pre></div>

<p>Render an outline within a bitmap. The outline's image is simply OR-ed to the target bitmap.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to a FreeType library object.</p>
</td></tr>
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the source outline descriptor.</p>
</td></tr>
</table>

<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="abitmap">abitmap</td><td class="desc">
<p>A pointer to the target bitmap descriptor.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>This function does <strong>not create</strong> the bitmap, it only renders an outline image within the one you pass to it! Consequently, the various fields in <code>abitmap</code> should be set accordingly.</p>
<p>It will use the raster corresponding to the default glyph format.</p>
<p>The value of the <code>num_grays</code> field in <code>abitmap</code> is ignored. If you select the gray-level rasterizer, and you want less than 256 gray levels, you have to use <code><a href="ft2-outline_processing.html#ft_outline_render">FT_Outline_Render</a></code> directly.</p>
<hr>

<h2 id="ft_outline_render">FT_Outline_Render<a class="headerlink" href="#ft_outline_render" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_Render</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>         library,
                     <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*        outline,
                     <a href="ft2-raster.html#ft_raster_params">FT_Raster_Params</a>*  params );
</code></pre></div>

<p>Render an outline within a bitmap using the current scan-convert.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to a FreeType library object.</p>
</td></tr>
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the source outline descriptor.</p>
</td></tr>
</table>

<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="params">params</td><td class="desc">
<p>A pointer to an <code><a href="ft2-raster.html#ft_raster_params">FT_Raster_Params</a></code> structure used to describe the rendering operation.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>This advanced function uses <code><a href="ft2-raster.html#ft_raster_params">FT_Raster_Params</a></code> as an argument. The field <code>params.source</code> will be set to <code>outline</code> before the scan converter is called, which means that the value you give to it is actually ignored. Either <code>params.target</code> must point to preallocated bitmap, or <code><a href="ft2-raster.html#ft_raster_flag_xxx">FT_RASTER_FLAG_DIRECT</a></code> must be set in <code>params.flags</code> allowing FreeType rasterizer to be used for direct composition, translucency, etc. See <code><a href="ft2-raster.html#ft_raster_params">FT_Raster_Params</a></code> for more details.</p>
<hr>

<h2 id="ft_outline_decompose">FT_Outline_Decompose<a class="headerlink" href="#ft_outline_decompose" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Outline_Decompose</b>( <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*              outline,
                        <span class="keyword">const</span> <a href="ft2-outline_processing.html#ft_outline_funcs">FT_Outline_Funcs</a>*  func_interface,
                        <span class="keyword">void</span>*                    user );
</code></pre></div>

<p>Walk over an outline's structure to decompose it into individual segments and Bezier arcs. This function also emits &lsquo;move to&rsquo; operations to indicate the start of new contours in the outline.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A pointer to the source target.</p>
</td></tr>
<tr><td class="val" id="func_interface">func_interface</td><td class="desc">
<p>A table of &lsquo;emitters&rsquo;, i.e., function pointers called during decomposition to indicate path operations.</p>
</td></tr>
</table>

<h4>inout</h4>

<table class="fields">
<tr><td class="val" id="user">user</td><td class="desc">
<p>A typeless pointer that is passed to each emitter during the decomposition. It can be used to store the state during the decomposition.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>A contour that contains a single point only is represented by a &lsquo;move to&rsquo; operation followed by &lsquo;line to&rsquo; to the same point. In most cases, it is best to filter this out before using the outline for stroking purposes (otherwise it would result in a visible dot when round caps are used).</p>
<p>Similarly, the function returns success for an empty outline also (doing nothing, this is, not calling any emitter); if necessary, you should filter this out, too.</p>
<hr>

<h2 id="ft_outline_funcs">FT_Outline_Funcs<a class="headerlink" href="#ft_outline_funcs" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Outline_Funcs_
  {
    <a href="ft2-outline_processing.html#ft_outline_movetofunc">FT_Outline_MoveToFunc</a>   move_to;
    <a href="ft2-outline_processing.html#ft_outline_linetofunc">FT_Outline_LineToFunc</a>   line_to;
    <a href="ft2-outline_processing.html#ft_outline_conictofunc">FT_Outline_ConicToFunc</a>  conic_to;
    <a href="ft2-outline_processing.html#ft_outline_cubictofunc">FT_Outline_CubicToFunc</a>  cubic_to;

    <span class="keyword">int</span>                     shift;
    <a href="ft2-basic_types.html#ft_pos">FT_Pos</a>                  delta;

  } <b>FT_Outline_Funcs</b>;
</code></pre></div>

<p>A structure to hold various function pointers used during outline decomposition in order to emit segments, conic, and cubic Beziers.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="move_to">move_to</td><td class="desc">
<p>The &lsquo;move to&rsquo; emitter.</p>
</td></tr>
<tr><td class="val" id="line_to">line_to</td><td class="desc">
<p>The segment emitter.</p>
</td></tr>
<tr><td class="val" id="conic_to">conic_to</td><td class="desc">
<p>The second-order Bezier arc emitter.</p>
</td></tr>
<tr><td class="val" id="cubic_to">cubic_to</td><td class="desc">
<p>The third-order Bezier arc emitter.</p>
</td></tr>
<tr><td class="val" id="shift">shift</td><td class="desc">
<p>The shift that is applied to coordinates before they are sent to the emitter.</p>
</td></tr>
<tr><td class="val" id="delta">delta</td><td class="desc">
<p>The delta that is applied to coordinates before they are sent to the emitter, but after the shift.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The point coordinates sent to the emitters are the transformed version of the original coordinates (this is important for high accuracy during scan-conversion). The transformation is simple:
<div class="highlight"><pre><span></span><code>  x&#39; = (x &lt;&lt; shift) - delta
  y&#39; = (y &lt;&lt; shift) - delta
</code></pre></div></p>
<p>Set the values of <code>shift</code> and <code>delta</code> to&nbsp;0 to get the original point coordinates.</p>
<hr>

<h2 id="ft_outline_movetofunc">FT_Outline_MoveToFunc<a class="headerlink" href="#ft_outline_movetofunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Outline_MoveToFunc</b>)( <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  to,
                            <span class="keyword">void</span>*             user );

#<span class="keyword">define</span> FT_Outline_MoveTo_Func  <b>FT_Outline_MoveToFunc</b>
</code></pre></div>

<p>A function pointer type used to describe the signature of a &lsquo;move to&rsquo; function during outline walking/decomposition.</p>
<p>A &lsquo;move to&rsquo; is emitted to start a new contour in an outline.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="to">to</td><td class="desc">
<p>A pointer to the target point of the &lsquo;move to&rsquo;.</p>
</td></tr>
<tr><td class="val" id="user">user</td><td class="desc">
<p>A typeless pointer, which is passed from the caller of the decomposition function.</p>
</td></tr>
</table>

<h4>return</h4>

<p>Error code. 0&nbsp;means success.</p>
<hr>

<h2 id="ft_outline_linetofunc">FT_Outline_LineToFunc<a class="headerlink" href="#ft_outline_linetofunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Outline_LineToFunc</b>)( <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  to,
                            <span class="keyword">void</span>*             user );

#<span class="keyword">define</span> FT_Outline_LineTo_Func  <b>FT_Outline_LineToFunc</b>
</code></pre></div>

<p>A function pointer type used to describe the signature of a &lsquo;line to&rsquo; function during outline walking/decomposition.</p>
<p>A &lsquo;line to&rsquo; is emitted to indicate a segment in the outline.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="to">to</td><td class="desc">
<p>A pointer to the target point of the &lsquo;line to&rsquo;.</p>
</td></tr>
<tr><td class="val" id="user">user</td><td class="desc">
<p>A typeless pointer, which is passed from the caller of the decomposition function.</p>
</td></tr>
</table>

<h4>return</h4>

<p>Error code. 0&nbsp;means success.</p>
<hr>

<h2 id="ft_outline_conictofunc">FT_Outline_ConicToFunc<a class="headerlink" href="#ft_outline_conictofunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Outline_ConicToFunc</b>)( <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  control,
                             <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  to,
                             <span class="keyword">void</span>*             user );

#<span class="keyword">define</span> FT_Outline_ConicTo_Func  <b>FT_Outline_ConicToFunc</b>
</code></pre></div>

<p>A function pointer type used to describe the signature of a &lsquo;conic to&rsquo; function during outline walking or decomposition.</p>
<p>A &lsquo;conic to&rsquo; is emitted to indicate a second-order Bezier arc in the outline.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="control">control</td><td class="desc">
<p>An intermediate control point between the last position and the new target in <code>to</code>.</p>
</td></tr>
<tr><td class="val" id="to">to</td><td class="desc">
<p>A pointer to the target end point of the conic arc.</p>
</td></tr>
<tr><td class="val" id="user">user</td><td class="desc">
<p>A typeless pointer, which is passed from the caller of the decomposition function.</p>
</td></tr>
</table>

<h4>return</h4>

<p>Error code. 0&nbsp;means success.</p>
<hr>

<h2 id="ft_outline_cubictofunc">FT_Outline_CubicToFunc<a class="headerlink" href="#ft_outline_cubictofunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Outline_CubicToFunc</b>)( <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  control1,
                             <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  control2,
                             <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>*  to,
                             <span class="keyword">void</span>*             user );

#<span class="keyword">define</span> FT_Outline_CubicTo_Func  <b>FT_Outline_CubicToFunc</b>
</code></pre></div>

<p>A function pointer type used to describe the signature of a &lsquo;cubic to&rsquo; function during outline walking or decomposition.</p>
<p>A &lsquo;cubic to&rsquo; is emitted to indicate a third-order Bezier arc.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="control1">control1</td><td class="desc">
<p>A pointer to the first Bezier control point.</p>
</td></tr>
<tr><td class="val" id="control2">control2</td><td class="desc">
<p>A pointer to the second Bezier control point.</p>
</td></tr>
<tr><td class="val" id="to">to</td><td class="desc">
<p>A pointer to the target end point.</p>
</td></tr>
<tr><td class="val" id="user">user</td><td class="desc">
<p>A typeless pointer, which is passed from the caller of the decomposition function.</p>
</td></tr>
</table>

<h4>return</h4>

<p>Error code. 0&nbsp;means success.</p>
<hr>

<h2 id="ft_orientation">FT_Orientation<a class="headerlink" href="#ft_orientation" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Orientation_
  {
    <a href="ft2-outline_processing.html#ft_orientation_truetype">FT_ORIENTATION_TRUETYPE</a>   = 0,
    <a href="ft2-outline_processing.html#ft_orientation_postscript">FT_ORIENTATION_POSTSCRIPT</a> = 1,
    <a href="ft2-outline_processing.html#ft_orientation_fill_right">FT_ORIENTATION_FILL_RIGHT</a> = <a href="ft2-outline_processing.html#ft_orientation_truetype">FT_ORIENTATION_TRUETYPE</a>,
    <a href="ft2-outline_processing.html#ft_orientation_fill_left">FT_ORIENTATION_FILL_LEFT</a>  = <a href="ft2-outline_processing.html#ft_orientation_postscript">FT_ORIENTATION_POSTSCRIPT</a>,
    <a href="ft2-outline_processing.html#ft_orientation_none">FT_ORIENTATION_NONE</a>

  } <b>FT_Orientation</b>;
</code></pre></div>

<p>A list of values used to describe an outline's contour orientation.</p>
<p>The TrueType and PostScript specifications use different conventions to determine whether outline contours should be filled or unfilled.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_orientation_truetype">FT_ORIENTATION_TRUETYPE</td><td class="desc">
<p>According to the TrueType specification, clockwise contours must be filled, and counter-clockwise ones must be unfilled.</p>
</td></tr>
<tr><td class="val" id="ft_orientation_postscript">FT_ORIENTATION_POSTSCRIPT</td><td class="desc">
<p>According to the PostScript specification, counter-clockwise contours must be filled, and clockwise ones must be unfilled.</p>
</td></tr>
<tr><td class="val" id="ft_orientation_fill_right">FT_ORIENTATION_FILL_RIGHT</td><td class="desc">
<p>This is identical to <code><a href="ft2-outline_processing.html#ft_orientation">FT_ORIENTATION_TRUETYPE</a></code>, but is used to remember that in TrueType, everything that is to the right of the drawing direction of a contour must be filled.</p>
</td></tr>
<tr><td class="val" id="ft_orientation_fill_left">FT_ORIENTATION_FILL_LEFT</td><td class="desc">
<p>This is identical to <code><a href="ft2-outline_processing.html#ft_orientation">FT_ORIENTATION_POSTSCRIPT</a></code>, but is used to remember that in PostScript, everything that is to the left of the drawing direction of a contour must be filled.</p>
</td></tr>
<tr><td class="val" id="ft_orientation_none">FT_ORIENTATION_NONE</td><td class="desc">
<p>The orientation cannot be determined. That is, different parts of the glyph have different orientation.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_outline_get_orientation">FT_Outline_Get_Orientation<a class="headerlink" href="#ft_outline_get_orientation" title="Permanent link">&para;</a></h2>
<p>Defined in FT_OUTLINE_H (freetype/ftoutln.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-outline_processing.html#ft_orientation">FT_Orientation</a> )
  <b>FT_Outline_Get_Orientation</b>( <a href="ft2-outline_processing.html#ft_outline">FT_Outline</a>*  outline );
</code></pre></div>

<p>This function analyzes a glyph outline and tries to compute its fill orientation (see <code><a href="ft2-outline_processing.html#ft_orientation">FT_Orientation</a></code>). This is done by integrating the total area covered by the outline. The positive integral corresponds to the clockwise orientation and <code><a href="ft2-outline_processing.html#ft_orientation">FT_ORIENTATION_POSTSCRIPT</a></code> is returned. The negative integral corresponds to the counter-clockwise orientation and <code><a href="ft2-outline_processing.html#ft_orientation">FT_ORIENTATION_TRUETYPE</a></code> is returned.</p>
<p>Note that this will return <code><a href="ft2-outline_processing.html#ft_orientation">FT_ORIENTATION_TRUETYPE</a></code> for empty outlines.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="outline">outline</td><td class="desc">
<p>A handle to the source outline.</p>
</td></tr>
</table>

<h4>return</h4>

<p>The orientation.</p>
<hr>

<h2 id="ft_outline_xxx">FT_OUTLINE_XXX<a class="headerlink" href="#ft_outline_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_IMAGE_H (freetype/ftimage.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_none">FT_OUTLINE_NONE</a>             0x0
#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_owner">FT_OUTLINE_OWNER</a>            0x1
#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_even_odd_fill">FT_OUTLINE_EVEN_ODD_FILL</a>    0x2
#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_reverse_fill">FT_OUTLINE_REVERSE_FILL</a>     0x4
#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_ignore_dropouts">FT_OUTLINE_IGNORE_DROPOUTS</a>  0x8
#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_smart_dropouts">FT_OUTLINE_SMART_DROPOUTS</a>   0x10
#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_include_stubs">FT_OUTLINE_INCLUDE_STUBS</a>    0x20
#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_overlap">FT_OUTLINE_OVERLAP</a>          0x40

#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_high_precision">FT_OUTLINE_HIGH_PRECISION</a>   0x100
#<span class="keyword">define</span> <a href="ft2-outline_processing.html#ft_outline_single_pass">FT_OUTLINE_SINGLE_PASS</a>      0x200


  /* these constants are deprecated; use the corresponding */
  /* `<b>FT_OUTLINE_XXX</b>` values instead                       */
#<span class="keyword">define</span> ft_outline_none             <a href="ft2-outline_processing.html#ft_outline_none">FT_OUTLINE_NONE</a>
#<span class="keyword">define</span> ft_outline_owner            <a href="ft2-outline_processing.html#ft_outline_owner">FT_OUTLINE_OWNER</a>
#<span class="keyword">define</span> ft_outline_even_odd_fill    <a href="ft2-outline_processing.html#ft_outline_even_odd_fill">FT_OUTLINE_EVEN_ODD_FILL</a>
#<span class="keyword">define</span> ft_outline_reverse_fill     <a href="ft2-outline_processing.html#ft_outline_reverse_fill">FT_OUTLINE_REVERSE_FILL</a>
#<span class="keyword">define</span> ft_outline_ignore_dropouts  <a href="ft2-outline_processing.html#ft_outline_ignore_dropouts">FT_OUTLINE_IGNORE_DROPOUTS</a>
#<span class="keyword">define</span> ft_outline_high_precision   <a href="ft2-outline_processing.html#ft_outline_high_precision">FT_OUTLINE_HIGH_PRECISION</a>
#<span class="keyword">define</span> ft_outline_single_pass      <a href="ft2-outline_processing.html#ft_outline_single_pass">FT_OUTLINE_SINGLE_PASS</a>
</code></pre></div>

<p>A list of bit-field constants used for the flags in an outline's <code>flags</code> field.</p>
<h4>values</h4>

<table class="fields long">
<tr><td class="val" id="ft_outline_none">FT_OUTLINE_NONE</td><td class="desc">
<p>Value&nbsp;0 is reserved.</p>
</td></tr>
<tr><td class="val" id="ft_outline_owner">FT_OUTLINE_OWNER</td><td class="desc">
<p>If set, this flag indicates that the outline's field arrays (i.e., <code>points</code>, <code>flags</code>, and <code>contours</code>) are &lsquo;owned&rsquo; by the outline object, and should thus be freed when it is destroyed.</p>
</td></tr>
<tr><td class="val" id="ft_outline_even_odd_fill">FT_OUTLINE_EVEN_ODD_FILL</td><td class="desc">
<p>By default, outlines are filled using the non-zero winding rule. If set to 1, the outline will be filled using the even-odd fill rule (only works with the smooth rasterizer).</p>
</td></tr>
<tr><td class="val" id="ft_outline_reverse_fill">FT_OUTLINE_REVERSE_FILL</td><td class="desc">
<p>By default, outside contours of an outline are oriented in clock-wise direction, as defined in the TrueType specification. This flag is set if the outline uses the opposite direction (typically for Type&nbsp;1 fonts). This flag is ignored by the scan converter.</p>
</td></tr>
<tr><td class="val" id="ft_outline_ignore_dropouts">FT_OUTLINE_IGNORE_DROPOUTS</td><td class="desc">
<p>By default, the scan converter will try to detect drop-outs in an outline and correct the glyph bitmap to ensure consistent shape continuity. If set, this flag hints the scan-line converter to ignore such cases. See below for more information.</p>
</td></tr>
<tr><td class="val" id="ft_outline_smart_dropouts">FT_OUTLINE_SMART_DROPOUTS</td><td class="desc">
<p>Select smart dropout control. If unset, use simple dropout control. Ignored if <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_IGNORE_DROPOUTS</a></code> is set. See below for more information.</p>
</td></tr>
<tr><td class="val" id="ft_outline_include_stubs">FT_OUTLINE_INCLUDE_STUBS</td><td class="desc">
<p>If set, turn pixels on for &lsquo;stubs&rsquo;, otherwise exclude them. Ignored if <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_IGNORE_DROPOUTS</a></code> is set. See below for more information.</p>
</td></tr>
<tr><td class="val" id="ft_outline_overlap">FT_OUTLINE_OVERLAP</td><td class="desc">
<p>This flag indicates that this outline contains overlapping contrours and the anti-aliased renderer should perform oversampling to mitigate possible artifacts. This flag should <em>not</em> be set for well designed glyphs without overlaps because it quadruples the rendering time.</p>
</td></tr>
<tr><td class="val" id="ft_outline_high_precision">FT_OUTLINE_HIGH_PRECISION</td><td class="desc">
<p>This flag indicates that the scan-line converter should try to convert this outline to bitmaps with the highest possible quality. It is typically set for small character sizes. Note that this is only a hint that might be completely ignored by a given scan-converter.</p>
</td></tr>
<tr><td class="val" id="ft_outline_single_pass">FT_OUTLINE_SINGLE_PASS</td><td class="desc">
<p>This flag is set to force a given scan-converter to only use a single pass over the outline to render a bitmap glyph image. Normally, it is set for very large character sizes. It is only a hint that might be completely ignored by a given scan-converter.</p>
</td></tr>
</table>

<h4>note</h4>

<p>The flags <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_IGNORE_DROPOUTS</a></code>, <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_SMART_DROPOUTS</a></code>, and <code><a href="ft2-outline_processing.html#ft_outline_xxx">FT_OUTLINE_INCLUDE_STUBS</a></code> are ignored by the smooth rasterizer.</p>
<p>There exists a second mechanism to pass the drop-out mode to the B/W rasterizer; see the <code>tags</code> field in <code><a href="ft2-outline_processing.html#ft_outline">FT_Outline</a></code>.</p>
<p>Please refer to the description of the &lsquo;SCANTYPE&rsquo; instruction in the OpenType specification (in file <code>ttinst1.doc</code>) how simple drop-outs, smart drop-outs, and stubs are defined.</p>
<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-list_processing.html" title="List Processing" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                List Processing
              </span>
            </div>
          </a>
        
        
          <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Quick retrieval of advance values
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>