



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>Glyph Color Management - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#glyph-color-management" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                Glyph Color Management
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4" checked>
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link">
      Base Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        Glyph Color Management
      </label>
    
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link md-nav__link--active">
      Glyph Color Management
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_color" class="md-nav__link">
    FT_Color
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_xxx" class="md-nav__link">
    FT_PALETTE_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_data" class="md-nav__link">
    FT_Palette_Data
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_data_get" class="md-nav__link">
    FT_Palette_Data_Get
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_select" class="md-nav__link">
    FT_Palette_Select
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_set_foreground_color" class="md-nav__link">
    FT_Palette_Set_Foreground_Color
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link">
      Header File Macros
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5">
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link">
      Window FNT Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6">
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link">
      Subpixel Rendering
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8">
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link">
      Outline Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link">
      Scanline Converter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link">
      System Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10">
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link">
      TrueTypeGX/AAT Validation
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_color" class="md-nav__link">
    FT_Color
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_xxx" class="md-nav__link">
    FT_PALETTE_XXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_data" class="md-nav__link">
    FT_Palette_Data
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_data_get" class="md-nav__link">
    FT_Palette_Data_Get
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_select" class="md-nav__link">
    FT_Palette_Select
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_palette_set_foreground_color" class="md-nav__link">
    FT_Palette_Set_Foreground_Color
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#core-api">Core API</a> &raquo; Glyph Color Management</p>
<hr />
<h1 id="glyph-color-management">Glyph Color Management<a class="headerlink" href="#glyph-color-management" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>The functions described here allow access and manipulation of color palette entries in OpenType's &lsquo;CPAL&rsquo; tables.</p>
<h2 id="ft_color">FT_Color<a class="headerlink" href="#ft_color" title="Permanent link">&para;</a></h2>
<p>Defined in FT_COLOR_H (freetype/ftcolor.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Color_
  {
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>  blue;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>  green;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>  red;
    <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>  alpha;

  } <b>FT_Color</b>;
</code></pre></div>

<p>This structure models a BGRA color value of a &lsquo;CPAL&rsquo; palette entry.</p>
<p>The used color space is sRGB; the colors are not pre-multiplied, and alpha values must be explicitly set.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="blue">blue</td><td class="desc">
<p>Blue value.</p>
</td></tr>
<tr><td class="val" id="green">green</td><td class="desc">
<p>Green value.</p>
</td></tr>
<tr><td class="val" id="red">red</td><td class="desc">
<p>Red value.</p>
</td></tr>
<tr><td class="val" id="alpha">alpha</td><td class="desc">
<p>Alpha value, giving the red, green, and blue color's opacity.</p>
</td></tr>
</table>

<h4>since</h4>

<p>2.10</p>
<hr>

<h2 id="ft_palette_xxx">FT_PALETTE_XXX<a class="headerlink" href="#ft_palette_xxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_COLOR_H (freetype/ftcolor.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-color_management.html#ft_palette_for_light_background">FT_PALETTE_FOR_LIGHT_BACKGROUND</a>  0x01
#<span class="keyword">define</span> <a href="ft2-color_management.html#ft_palette_for_dark_background">FT_PALETTE_FOR_DARK_BACKGROUND</a>   0x02
</code></pre></div>

<p>A list of bit field constants used in the <code>palette_flags</code> array of the <code><a href="ft2-color_management.html#ft_palette_data">FT_Palette_Data</a></code> structure to indicate for which background a palette with a given index is usable.</p>
<h4>values</h4>

<table class="fields long">
<tr><td class="val" id="ft_palette_for_light_background">FT_PALETTE_FOR_LIGHT_BACKGROUND</td><td class="desc">
<p>The palette is appropriate to use when displaying the font on a light background such as white.</p>
</td></tr>
<tr><td class="val" id="ft_palette_for_dark_background">FT_PALETTE_FOR_DARK_BACKGROUND</td><td class="desc">
<p>The palette is appropriate to use when displaying the font on a dark background such as black.</p>
</td></tr>
</table>

<h4>since</h4>

<p>2.10</p>
<hr>

<h2 id="ft_palette_data">FT_Palette_Data<a class="headerlink" href="#ft_palette_data" title="Permanent link">&para;</a></h2>
<p>Defined in FT_COLOR_H (freetype/ftcolor.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Palette_Data_ {
    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>         num_palettes;
    <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>*  palette_name_ids;
    <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>*  palette_flags;

    <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>         num_palette_entries;
    <span class="keyword">const</span> <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>*  palette_entry_name_ids;

  } <b>FT_Palette_Data</b>;
</code></pre></div>

<p>This structure holds the data of the &lsquo;CPAL&rsquo; table.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="num_palettes">num_palettes</td><td class="desc">
<p>The number of palettes.</p>
</td></tr>
<tr><td class="val" id="palette_name_ids">palette_name_ids</td><td class="desc">
<p>An optional read-only array of palette name IDs with <code>num_palettes</code> elements, corresponding to entries like &lsquo;dark&rsquo; or &lsquo;light&rsquo; in the font's &lsquo;name&rsquo; table.</p>
<p>An empty name ID in the &lsquo;CPAL&rsquo; table gets represented as value 0xFFFF.</p>
<p><code>NULL</code> if the font's &lsquo;CPAL&rsquo; table doesn't contain appropriate data.</p>
</td></tr>
<tr><td class="val" id="palette_flags">palette_flags</td><td class="desc">
<p>An optional read-only array of palette flags with <code>num_palettes</code> elements. Possible values are an ORed combination of <code><a href="ft2-color_management.html#ft_palette_xxx">FT_PALETTE_FOR_LIGHT_BACKGROUND</a></code> and <code><a href="ft2-color_management.html#ft_palette_xxx">FT_PALETTE_FOR_DARK_BACKGROUND</a></code>.</p>
<p><code>NULL</code> if the font's &lsquo;CPAL&rsquo; table doesn't contain appropriate data.</p>
</td></tr>
<tr><td class="val" id="num_palette_entries">num_palette_entries</td><td class="desc">
<p>The number of entries in a single palette. All palettes have the same size.</p>
</td></tr>
<tr><td class="val" id="palette_entry_name_ids">palette_entry_name_ids</td><td class="desc">
<p>An optional read-only array of palette entry name IDs with <code>num_palette_entries</code>. In each palette, entries with the same index have the same function. For example, index&nbsp;0 might correspond to string &lsquo;outline&rsquo; in the font's &lsquo;name&rsquo; table to indicate that this palette entry is used for outlines, index&nbsp;1 might correspond to &lsquo;fill&rsquo; to indicate the filling color palette entry, etc.</p>
<p>An empty entry name ID in the &lsquo;CPAL&rsquo; table gets represented as value 0xFFFF.</p>
<p><code>NULL</code> if the font's &lsquo;CPAL&rsquo; table doesn't contain appropriate data.</p>
</td></tr>
</table>

<h4>note</h4>

<p>Use function <code><a href="ft2-sfnt_names.html#ft_get_sfnt_name">FT_Get_Sfnt_Name</a></code> to map name IDs and entry name IDs to name strings.</p>
<p>Use function <code><a href="ft2-color_management.html#ft_palette_select">FT_Palette_Select</a></code> to get the colors associated with a palette entry.</p>
<h4>since</h4>

<p>2.10</p>
<hr>

<h2 id="ft_palette_data_get">FT_Palette_Data_Get<a class="headerlink" href="#ft_palette_data_get" title="Permanent link">&para;</a></h2>
<p>Defined in FT_COLOR_H (freetype/ftcolor.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Palette_Data_Get</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>           face,
                       <a href="ft2-color_management.html#ft_palette_data">FT_Palette_Data</a>  *apalette );
</code></pre></div>

<p>Retrieve the face's color palette data.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>The source face handle.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="apalette">apalette</td><td class="desc">
<p>A pointer to an <code><a href="ft2-color_management.html#ft_palette_data">FT_Palette_Data</a></code> structure.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>All arrays in the returned <code><a href="ft2-color_management.html#ft_palette_data">FT_Palette_Data</a></code> structure are read-only.</p>
<p>This function always returns an error if the config macro <code>TT_CONFIG_OPTION_COLOR_LAYERS</code> is not defined in <code>ftoption.h</code>.</p>
<h4>since</h4>

<p>2.10</p>
<hr>

<h2 id="ft_palette_select">FT_Palette_Select<a class="headerlink" href="#ft_palette_select" title="Permanent link">&para;</a></h2>
<p>Defined in FT_COLOR_H (freetype/ftcolor.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Palette_Select</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>     face,
                     <a href="ft2-basic_types.html#ft_ushort">FT_UShort</a>   palette_index,
                     <a href="ft2-color_management.html#ft_color">FT_Color</a>*  *apalette );
</code></pre></div>

<p>This function has two purposes.</p>
<p>(1) It activates a palette for rendering color glyphs, and</p>
<p>(2) it retrieves all (unmodified) color entries of this palette. This function returns a read-write array, which means that a calling application can modify the palette entries on demand.</p>
<p>A corollary of (2) is that calling the function, then modifying some values, then calling the function again with the same arguments resets all color entries to the original &lsquo;CPAL&rsquo; values; all user modifications are lost.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>The source face handle.</p>
</td></tr>
<tr><td class="val" id="palette_index">palette_index</td><td class="desc">
<p>The palette index.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="apalette">apalette</td><td class="desc">
<p>An array of color entries for a palette with index <code>palette_index</code>, having <code>num_palette_entries</code> elements (as found in the <code>FT_Palette_Data</code> structure). If <code>apalette</code> is set to <code>NULL</code>, no array gets returned (and no color entries can be modified).</p>
<p>In case the font doesn't support color palettes, <code>NULL</code> is returned.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>The array pointed to by <code>apalette_entries</code> is owned and managed by FreeType.</p>
<p>This function always returns an error if the config macro <code>TT_CONFIG_OPTION_COLOR_LAYERS</code> is not defined in <code>ftoption.h</code>.</p>
<h4>since</h4>

<p>2.10</p>
<hr>

<h2 id="ft_palette_set_foreground_color">FT_Palette_Set_Foreground_Color<a class="headerlink" href="#ft_palette_set_foreground_color" title="Permanent link">&para;</a></h2>
<p>Defined in FT_COLOR_H (freetype/ftcolor.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Palette_Set_Foreground_Color</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>   face,
                                   <a href="ft2-color_management.html#ft_color">FT_Color</a>  foreground_color );
</code></pre></div>

<p>&lsquo;COLR&rsquo; uses palette index 0xFFFF to indicate a &lsquo;text foreground color&rsquo;. This function sets this value.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>The source face handle.</p>
</td></tr>
<tr><td class="val" id="foreground_color">foreground_color</td><td class="desc">
<p>An <code>FT_Color</code> structure to define the text foreground color.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>If this function isn't called, the text foreground color is set to white opaque (BGRA value 0xFFFFFFFF) if <code><a href="ft2-color_management.html#ft_palette_xxx">FT_PALETTE_FOR_DARK_BACKGROUND</a></code> is present for the current palette, and black opaque (BGRA value 0x000000FF) otherwise, including the case that no palette types are available in the &lsquo;CPAL&rsquo; table.</p>
<p>This function always returns an error if the config macro <code>TT_CONFIG_OPTION_COLOR_LAYERS</code> is not defined in <code>ftoption.h</code>.</p>
<h4>since</h4>

<p>2.10</p>
<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                Unicode Variation Sequences
              </span>
            </div>
          </a>
        
        
          <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Glyph Layer Management
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>