



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>Header File Macros - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#header-file-macros" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                Header File Macros
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4" checked>
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link">
      Base Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link">
      Glyph Color Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        Header File Macros
      </label>
    
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link md-nav__link--active">
      Header File Macros
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_config_config_h" class="md-nav__link">
    FT_CONFIG_CONFIG_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_config_standard_library_h" class="md-nav__link">
    FT_CONFIG_STANDARD_LIBRARY_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_config_options_h" class="md-nav__link">
    FT_CONFIG_OPTIONS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_config_modules_h" class="md-nav__link">
    FT_CONFIG_MODULES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_freetype_h" class="md-nav__link">
    FT_FREETYPE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_errors_h" class="md-nav__link">
    FT_ERRORS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_module_errors_h" class="md-nav__link">
    FT_MODULE_ERRORS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_system_h" class="md-nav__link">
    FT_SYSTEM_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_image_h" class="md-nav__link">
    FT_IMAGE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_types_h" class="md-nav__link">
    FT_TYPES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_list_h" class="md-nav__link">
    FT_LIST_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_h" class="md-nav__link">
    FT_OUTLINE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_sizes_h" class="md-nav__link">
    FT_SIZES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_module_h" class="md-nav__link">
    FT_MODULE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_render_h" class="md-nav__link">
    FT_RENDER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_driver_h" class="md-nav__link">
    FT_DRIVER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_autohinter_h" class="md-nav__link">
    FT_AUTOHINTER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_cff_driver_h" class="md-nav__link">
    FT_CFF_DRIVER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetype_driver_h" class="md-nav__link">
    FT_TRUETYPE_DRIVER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_pcf_driver_h" class="md-nav__link">
    FT_PCF_DRIVER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_type1_tables_h" class="md-nav__link">
    FT_TYPE1_TABLES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetype_ids_h" class="md-nav__link">
    FT_TRUETYPE_IDS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetype_tables_h" class="md-nav__link">
    FT_TRUETYPE_TABLES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetype_tags_h" class="md-nav__link">
    FT_TRUETYPE_TAGS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bdf_h" class="md-nav__link">
    FT_BDF_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_cid_h" class="md-nav__link">
    FT_CID_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_gzip_h" class="md-nav__link">
    FT_GZIP_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_lzw_h" class="md-nav__link">
    FT_LZW_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bzip2_h" class="md-nav__link">
    FT_BZIP2_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_winfonts_h" class="md-nav__link">
    FT_WINFONTS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_glyph_h" class="md-nav__link">
    FT_GLYPH_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bitmap_h" class="md-nav__link">
    FT_BITMAP_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bbox_h" class="md-nav__link">
    FT_BBOX_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_cache_h" class="md-nav__link">
    FT_CACHE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_mac_h" class="md-nav__link">
    FT_MAC_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_multiple_masters_h" class="md-nav__link">
    FT_MULTIPLE_MASTERS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_sfnt_names_h" class="md-nav__link">
    FT_SFNT_NAMES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_opentype_validate_h" class="md-nav__link">
    FT_OPENTYPE_VALIDATE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_gx_validate_h" class="md-nav__link">
    FT_GX_VALIDATE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_pfr_h" class="md-nav__link">
    FT_PFR_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_stroker_h" class="md-nav__link">
    FT_STROKER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_synthesis_h" class="md-nav__link">
    FT_SYNTHESIS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_font_formats_h" class="md-nav__link">
    FT_FONT_FORMATS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_trigonometry_h" class="md-nav__link">
    FT_TRIGONOMETRY_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_lcd_filter_h" class="md-nav__link">
    FT_LCD_FILTER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_incremental_h" class="md-nav__link">
    FT_INCREMENTAL_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_gasp_h" class="md-nav__link">
    FT_GASP_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_advances_h" class="md-nav__link">
    FT_ADVANCES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_color_h" class="md-nav__link">
    FT_COLOR_H
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5">
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link">
      Window FNT Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6">
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link">
      Subpixel Rendering
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8">
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link">
      Outline Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link">
      Scanline Converter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link">
      System Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10">
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link">
      TrueTypeGX/AAT Validation
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_config_config_h" class="md-nav__link">
    FT_CONFIG_CONFIG_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_config_standard_library_h" class="md-nav__link">
    FT_CONFIG_STANDARD_LIBRARY_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_config_options_h" class="md-nav__link">
    FT_CONFIG_OPTIONS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_config_modules_h" class="md-nav__link">
    FT_CONFIG_MODULES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_freetype_h" class="md-nav__link">
    FT_FREETYPE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_errors_h" class="md-nav__link">
    FT_ERRORS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_module_errors_h" class="md-nav__link">
    FT_MODULE_ERRORS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_system_h" class="md-nav__link">
    FT_SYSTEM_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_image_h" class="md-nav__link">
    FT_IMAGE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_types_h" class="md-nav__link">
    FT_TYPES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_list_h" class="md-nav__link">
    FT_LIST_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_outline_h" class="md-nav__link">
    FT_OUTLINE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_sizes_h" class="md-nav__link">
    FT_SIZES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_module_h" class="md-nav__link">
    FT_MODULE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_render_h" class="md-nav__link">
    FT_RENDER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_driver_h" class="md-nav__link">
    FT_DRIVER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_autohinter_h" class="md-nav__link">
    FT_AUTOHINTER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_cff_driver_h" class="md-nav__link">
    FT_CFF_DRIVER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetype_driver_h" class="md-nav__link">
    FT_TRUETYPE_DRIVER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_pcf_driver_h" class="md-nav__link">
    FT_PCF_DRIVER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_type1_tables_h" class="md-nav__link">
    FT_TYPE1_TABLES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetype_ids_h" class="md-nav__link">
    FT_TRUETYPE_IDS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetype_tables_h" class="md-nav__link">
    FT_TRUETYPE_TABLES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetype_tags_h" class="md-nav__link">
    FT_TRUETYPE_TAGS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bdf_h" class="md-nav__link">
    FT_BDF_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_cid_h" class="md-nav__link">
    FT_CID_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_gzip_h" class="md-nav__link">
    FT_GZIP_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_lzw_h" class="md-nav__link">
    FT_LZW_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bzip2_h" class="md-nav__link">
    FT_BZIP2_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_winfonts_h" class="md-nav__link">
    FT_WINFONTS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_glyph_h" class="md-nav__link">
    FT_GLYPH_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bitmap_h" class="md-nav__link">
    FT_BITMAP_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_bbox_h" class="md-nav__link">
    FT_BBOX_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_cache_h" class="md-nav__link">
    FT_CACHE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_mac_h" class="md-nav__link">
    FT_MAC_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_multiple_masters_h" class="md-nav__link">
    FT_MULTIPLE_MASTERS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_sfnt_names_h" class="md-nav__link">
    FT_SFNT_NAMES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_opentype_validate_h" class="md-nav__link">
    FT_OPENTYPE_VALIDATE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_gx_validate_h" class="md-nav__link">
    FT_GX_VALIDATE_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_pfr_h" class="md-nav__link">
    FT_PFR_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_stroker_h" class="md-nav__link">
    FT_STROKER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_synthesis_h" class="md-nav__link">
    FT_SYNTHESIS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_font_formats_h" class="md-nav__link">
    FT_FONT_FORMATS_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_trigonometry_h" class="md-nav__link">
    FT_TRIGONOMETRY_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_lcd_filter_h" class="md-nav__link">
    FT_LCD_FILTER_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_incremental_h" class="md-nav__link">
    FT_INCREMENTAL_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_gasp_h" class="md-nav__link">
    FT_GASP_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_advances_h" class="md-nav__link">
    FT_ADVANCES_H
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_color_h" class="md-nav__link">
    FT_COLOR_H
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#core-api">Core API</a> &raquo; Header File Macros</p>
<hr />
<h1 id="header-file-macros">Header File Macros<a class="headerlink" href="#header-file-macros" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>In addition to the normal scheme of including header files like
<div class="highlight"><pre><span></span><code>  #include &lt;freetype/freetype.h&gt;
  #include &lt;freetype/ftmm.h&gt;
  #include &lt;freetype/ftglyph.h&gt;
</code></pre></div></p>
<p>it is possible to used named macros instead. They can be used directly in <code>#include</code> statements as in
<div class="highlight"><pre><span></span><code>  #include FT_FREETYPE_H
  #include FT_MULTIPLE_MASTERS_H
  #include FT_GLYPH_H
</code></pre></div></p>
<p>These macros were introduced to overcome the infamous 8.3&nbsp;naming rule required by DOS (and <code>FT_MULTIPLE_MASTERS_H</code> is a lot more meaningful than <code>ftmm.h</code>).</p>
<h2 id="ft_config_config_h">FT_CONFIG_CONFIG_H<a class="headerlink" href="#ft_config_config_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">ifndef</span> <b>FT_CONFIG_CONFIG_H</b>
#<span class="keyword">define</span> <b>FT_CONFIG_CONFIG_H</b>  &lt;freetype/config/ftconfig.h&gt;
#<span class="keyword">endif</span>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing FreeType&nbsp;2 configuration data.</p>
<hr>

<h2 id="ft_config_standard_library_h">FT_CONFIG_STANDARD_LIBRARY_H<a class="headerlink" href="#ft_config_standard_library_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">ifndef</span> <b>FT_CONFIG_STANDARD_LIBRARY_H</b>
#<span class="keyword">define</span> <b>FT_CONFIG_STANDARD_LIBRARY_H</b>  &lt;freetype/config/ftstdlib.h&gt;
#<span class="keyword">endif</span>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing FreeType&nbsp;2 interface to the standard C library functions.</p>
<hr>

<h2 id="ft_config_options_h">FT_CONFIG_OPTIONS_H<a class="headerlink" href="#ft_config_options_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">ifndef</span> <b>FT_CONFIG_OPTIONS_H</b>
#<span class="keyword">define</span> <b>FT_CONFIG_OPTIONS_H</b>  &lt;freetype/config/ftoption.h&gt;
#<span class="keyword">endif</span>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing FreeType&nbsp;2 project-specific configuration options.</p>
<hr>

<h2 id="ft_config_modules_h">FT_CONFIG_MODULES_H<a class="headerlink" href="#ft_config_modules_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">ifndef</span> <b>FT_CONFIG_MODULES_H</b>
#<span class="keyword">define</span> <b>FT_CONFIG_MODULES_H</b>  &lt;freetype/config/ftmodule.h&gt;
#<span class="keyword">endif</span>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the list of FreeType&nbsp;2 modules that are statically linked to new library instances in <code><a href="ft2-base_interface.html#ft_init_freetype">FT_Init_FreeType</a></code>.</p>
<hr>

<h2 id="ft_freetype_h">FT_FREETYPE_H<a class="headerlink" href="#ft_freetype_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_FREETYPE_H</b>  &lt;freetype/freetype.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the base FreeType&nbsp;2 API.</p>
<hr>

<h2 id="ft_errors_h">FT_ERRORS_H<a class="headerlink" href="#ft_errors_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_ERRORS_H</b>  &lt;freetype/fterrors.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the list of FreeType&nbsp;2 error codes (and messages).</p>
<p>It is included by <code><a href="ft2-header_file_macros.html#ft_freetype_h">FT_FREETYPE_H</a></code>.</p>
<hr>

<h2 id="ft_module_errors_h">FT_MODULE_ERRORS_H<a class="headerlink" href="#ft_module_errors_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_MODULE_ERRORS_H</b>  &lt;freetype/ftmoderr.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the list of FreeType&nbsp;2 module error offsets (and messages).</p>
<hr>

<h2 id="ft_system_h">FT_SYSTEM_H<a class="headerlink" href="#ft_system_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_SYSTEM_H</b>  &lt;freetype/ftsystem.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 interface to low-level operations (i.e., memory management and stream i/o).</p>
<p>It is included by <code><a href="ft2-header_file_macros.html#ft_freetype_h">FT_FREETYPE_H</a></code>.</p>
<hr>

<h2 id="ft_image_h">FT_IMAGE_H<a class="headerlink" href="#ft_image_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_IMAGE_H</b>  &lt;freetype/ftimage.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing type definitions related to glyph images (i.e., bitmaps, outlines, scan-converter parameters).</p>
<p>It is included by <code><a href="ft2-header_file_macros.html#ft_freetype_h">FT_FREETYPE_H</a></code>.</p>
<hr>

<h2 id="ft_types_h">FT_TYPES_H<a class="headerlink" href="#ft_types_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_TYPES_H</b>  &lt;freetype/fttypes.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the basic data types defined by FreeType&nbsp;2.</p>
<p>It is included by <code><a href="ft2-header_file_macros.html#ft_freetype_h">FT_FREETYPE_H</a></code>.</p>
<hr>

<h2 id="ft_list_h">FT_LIST_H<a class="headerlink" href="#ft_list_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_LIST_H</b>  &lt;freetype/ftlist.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the list management API of FreeType&nbsp;2.</p>
<p>(Most applications will never need to include this file.)</p>
<hr>

<h2 id="ft_outline_h">FT_OUTLINE_H<a class="headerlink" href="#ft_outline_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_OUTLINE_H</b>  &lt;freetype/ftoutln.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the scalable outline management API of FreeType&nbsp;2.</p>
<hr>

<h2 id="ft_sizes_h">FT_SIZES_H<a class="headerlink" href="#ft_sizes_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_SIZES_H</b>  &lt;freetype/ftsizes.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the API which manages multiple <code><a href="ft2-base_interface.html#ft_size">FT_Size</a></code> objects per face.</p>
<hr>

<h2 id="ft_module_h">FT_MODULE_H<a class="headerlink" href="#ft_module_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_MODULE_H</b>  &lt;freetype/ftmodapi.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the module management API of FreeType&nbsp;2.</p>
<hr>

<h2 id="ft_render_h">FT_RENDER_H<a class="headerlink" href="#ft_render_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_RENDER_H</b>  &lt;freetype/ftrender.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the renderer module management API of FreeType&nbsp;2.</p>
<hr>

<h2 id="ft_driver_h">FT_DRIVER_H<a class="headerlink" href="#ft_driver_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_DRIVER_H</b>  &lt;freetype/ftdriver.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing structures and macros related to the driver modules.</p>
<hr>

<h2 id="ft_autohinter_h">FT_AUTOHINTER_H<a class="headerlink" href="#ft_autohinter_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_AUTOHINTER_H</b>  <a href="ft2-header_file_macros.html#ft_driver_h">FT_DRIVER_H</a>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing structures and macros related to the auto-hinting module.</p>
<p>Deprecated since version&nbsp;2.9; use <code><a href="ft2-header_file_macros.html#ft_driver_h">FT_DRIVER_H</a></code> instead.</p>
<hr>

<h2 id="ft_cff_driver_h">FT_CFF_DRIVER_H<a class="headerlink" href="#ft_cff_driver_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_CFF_DRIVER_H</b>  <a href="ft2-header_file_macros.html#ft_driver_h">FT_DRIVER_H</a>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing structures and macros related to the CFF driver module.</p>
<p>Deprecated since version&nbsp;2.9; use <code><a href="ft2-header_file_macros.html#ft_driver_h">FT_DRIVER_H</a></code> instead.</p>
<hr>

<h2 id="ft_truetype_driver_h">FT_TRUETYPE_DRIVER_H<a class="headerlink" href="#ft_truetype_driver_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_TRUETYPE_DRIVER_H</b>  <a href="ft2-header_file_macros.html#ft_driver_h">FT_DRIVER_H</a>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing structures and macros related to the TrueType driver module.</p>
<p>Deprecated since version&nbsp;2.9; use <code><a href="ft2-header_file_macros.html#ft_driver_h">FT_DRIVER_H</a></code> instead.</p>
<hr>

<h2 id="ft_pcf_driver_h">FT_PCF_DRIVER_H<a class="headerlink" href="#ft_pcf_driver_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_PCF_DRIVER_H</b>  <a href="ft2-header_file_macros.html#ft_driver_h">FT_DRIVER_H</a>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing structures and macros related to the PCF driver module.</p>
<p>Deprecated since version&nbsp;2.9; use <code><a href="ft2-header_file_macros.html#ft_driver_h">FT_DRIVER_H</a></code> instead.</p>
<hr>

<h2 id="ft_type1_tables_h">FT_TYPE1_TABLES_H<a class="headerlink" href="#ft_type1_tables_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_TYPE1_TABLES_H</b>  &lt;freetype/t1tables.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the types and API specific to the Type&nbsp;1 format.</p>
<hr>

<h2 id="ft_truetype_ids_h">FT_TRUETYPE_IDS_H<a class="headerlink" href="#ft_truetype_ids_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_TRUETYPE_IDS_H</b>  &lt;freetype/ttnameid.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the enumeration values which identify name strings, languages, encodings, etc. This file really contains a <em>large</em> set of constant macro definitions, taken from the TrueType and OpenType specifications.</p>
<hr>

<h2 id="ft_truetype_tables_h">FT_TRUETYPE_TABLES_H<a class="headerlink" href="#ft_truetype_tables_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_TRUETYPE_TABLES_H</b>  &lt;freetype/tttables.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the types and API specific to the TrueType (as well as OpenType) format.</p>
<hr>

<h2 id="ft_truetype_tags_h">FT_TRUETYPE_TAGS_H<a class="headerlink" href="#ft_truetype_tags_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_TRUETYPE_TAGS_H</b>  &lt;freetype/tttags.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the definitions of TrueType four-byte &lsquo;tags&rsquo; which identify blocks in SFNT-based font formats (i.e., TrueType and OpenType).</p>
<hr>

<h2 id="ft_bdf_h">FT_BDF_H<a class="headerlink" href="#ft_bdf_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_BDF_H</b>  &lt;freetype/ftbdf.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the definitions of an API which accesses BDF-specific strings from a face.</p>
<hr>

<h2 id="ft_cid_h">FT_CID_H<a class="headerlink" href="#ft_cid_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_CID_H</b>  &lt;freetype/ftcid.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the definitions of an API which access CID font information from a face.</p>
<hr>

<h2 id="ft_gzip_h">FT_GZIP_H<a class="headerlink" href="#ft_gzip_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_GZIP_H</b>  &lt;freetype/ftgzip.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the definitions of an API which supports gzip-compressed files.</p>
<hr>

<h2 id="ft_lzw_h">FT_LZW_H<a class="headerlink" href="#ft_lzw_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_LZW_H</b>  &lt;freetype/ftlzw.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the definitions of an API which supports LZW-compressed files.</p>
<hr>

<h2 id="ft_bzip2_h">FT_BZIP2_H<a class="headerlink" href="#ft_bzip2_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_BZIP2_H</b>  &lt;freetype/ftbzip2.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the definitions of an API which supports bzip2-compressed files.</p>
<hr>

<h2 id="ft_winfonts_h">FT_WINFONTS_H<a class="headerlink" href="#ft_winfonts_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_WINFONTS_H</b>   &lt;freetype/ftwinfnt.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the definitions of an API which supports Windows FNT files.</p>
<hr>

<h2 id="ft_glyph_h">FT_GLYPH_H<a class="headerlink" href="#ft_glyph_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_GLYPH_H</b>  &lt;freetype/ftglyph.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the API of the optional glyph management component.</p>
<hr>

<h2 id="ft_bitmap_h">FT_BITMAP_H<a class="headerlink" href="#ft_bitmap_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_BITMAP_H</b>  &lt;freetype/ftbitmap.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the API of the optional bitmap conversion component.</p>
<hr>

<h2 id="ft_bbox_h">FT_BBOX_H<a class="headerlink" href="#ft_bbox_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_BBOX_H</b>  &lt;freetype/ftbbox.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the API of the optional exact bounding box computation routines.</p>
<hr>

<h2 id="ft_cache_h">FT_CACHE_H<a class="headerlink" href="#ft_cache_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_CACHE_H</b>  &lt;freetype/ftcache.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the API of the optional FreeType&nbsp;2 cache sub-system.</p>
<hr>

<h2 id="ft_mac_h">FT_MAC_H<a class="headerlink" href="#ft_mac_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_MAC_H</b>  &lt;freetype/ftmac.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the Macintosh-specific FreeType&nbsp;2 API. The latter is used to access fonts embedded in resource forks.</p>
<p>This header file must be explicitly included by client applications compiled on the Mac (note that the base API still works though).</p>
<hr>

<h2 id="ft_multiple_masters_h">FT_MULTIPLE_MASTERS_H<a class="headerlink" href="#ft_multiple_masters_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_MULTIPLE_MASTERS_H</b>  &lt;freetype/ftmm.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the optional multiple-masters management API of FreeType&nbsp;2.</p>
<hr>

<h2 id="ft_sfnt_names_h">FT_SFNT_NAMES_H<a class="headerlink" href="#ft_sfnt_names_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_SFNT_NAMES_H</b>  &lt;freetype/ftsnames.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the optional FreeType&nbsp;2 API which accesses embedded &lsquo;name&rsquo; strings in SFNT-based font formats (i.e., TrueType and OpenType).</p>
<hr>

<h2 id="ft_opentype_validate_h">FT_OPENTYPE_VALIDATE_H<a class="headerlink" href="#ft_opentype_validate_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_OPENTYPE_VALIDATE_H</b>  &lt;freetype/ftotval.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the optional FreeType&nbsp;2 API which validates OpenType tables (&lsquo;BASE&rsquo;, &lsquo;GDEF&rsquo;, &lsquo;GPOS&rsquo;, &lsquo;GSUB&rsquo;, &lsquo;JSTF&rsquo;).</p>
<hr>

<h2 id="ft_gx_validate_h">FT_GX_VALIDATE_H<a class="headerlink" href="#ft_gx_validate_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_GX_VALIDATE_H</b>  &lt;freetype/ftgxval.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the optional FreeType&nbsp;2 API which validates TrueTypeGX/AAT tables (&lsquo;feat&rsquo;, &lsquo;mort&rsquo;, &lsquo;morx&rsquo;, &lsquo;bsln&rsquo;, &lsquo;just&rsquo;, &lsquo;kern&rsquo;, &lsquo;opbd&rsquo;, &lsquo;trak&rsquo;, &lsquo;prop&rsquo;).</p>
<hr>

<h2 id="ft_pfr_h">FT_PFR_H<a class="headerlink" href="#ft_pfr_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_PFR_H</b>  &lt;freetype/ftpfr.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which accesses PFR-specific data.</p>
<hr>

<h2 id="ft_stroker_h">FT_STROKER_H<a class="headerlink" href="#ft_stroker_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_STROKER_H</b>  &lt;freetype/ftstroke.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which provides functions to stroke outline paths.</p>
<hr>

<h2 id="ft_synthesis_h">FT_SYNTHESIS_H<a class="headerlink" href="#ft_synthesis_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_SYNTHESIS_H</b>  &lt;freetype/ftsynth.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which performs artificial obliquing and emboldening.</p>
<hr>

<h2 id="ft_font_formats_h">FT_FONT_FORMATS_H<a class="headerlink" href="#ft_font_formats_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_FONT_FORMATS_H</b>  &lt;freetype/ftfntfmt.h&gt;

  /* deprecated */
#<span class="keyword">define</span> FT_XFREE86_H  <b>FT_FONT_FORMATS_H</b>
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which provides functions specific to font formats.</p>
<hr>

<h2 id="ft_trigonometry_h">FT_TRIGONOMETRY_H<a class="headerlink" href="#ft_trigonometry_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_TRIGONOMETRY_H</b>  &lt;freetype/fttrigon.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which performs trigonometric computations (e.g., cosines and arc tangents).</p>
<hr>

<h2 id="ft_lcd_filter_h">FT_LCD_FILTER_H<a class="headerlink" href="#ft_lcd_filter_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_LCD_FILTER_H</b>  &lt;freetype/ftlcdfil.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which performs color filtering for subpixel rendering.</p>
<hr>

<h2 id="ft_incremental_h">FT_INCREMENTAL_H<a class="headerlink" href="#ft_incremental_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_INCREMENTAL_H</b>  &lt;freetype/ftincrem.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which performs incremental glyph loading.</p>
<hr>

<h2 id="ft_gasp_h">FT_GASP_H<a class="headerlink" href="#ft_gasp_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_GASP_H</b>  &lt;freetype/ftgasp.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which returns entries from the TrueType GASP table.</p>
<hr>

<h2 id="ft_advances_h">FT_ADVANCES_H<a class="headerlink" href="#ft_advances_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_ADVANCES_H</b>  &lt;freetype/ftadvanc.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which returns individual and ranged glyph advances.</p>
<hr>

<h2 id="ft_color_h">FT_COLOR_H<a class="headerlink" href="#ft_color_h" title="Permanent link">&para;</a></h2>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_COLOR_H</b>  &lt;freetype/ftcolor.h&gt;
</code></pre></div>

<p>A macro used in <code>#include</code> statements to name the file containing the FreeType&nbsp;2 API which handles the OpenType &lsquo;CPAL&rsquo; table.</p>
<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-sizes_management.html" title="Size Management" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                Size Management
              </span>
            </div>
          </a>
        
        
          <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Multiple Masters
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>