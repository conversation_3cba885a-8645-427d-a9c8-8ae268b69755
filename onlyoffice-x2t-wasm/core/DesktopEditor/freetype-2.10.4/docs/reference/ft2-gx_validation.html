



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>TrueTypeGX/AAT Validation - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#truetypegxaat-validation" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                TrueTypeGX/AAT Validation
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4">
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link">
      Base Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link">
      Glyph Color Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link">
      Header File Macros
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5">
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link">
      Window FNT Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6">
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link">
      Subpixel Rendering
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8">
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link">
      Outline Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link">
      Scanline Converter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link">
      System Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10" checked>
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        TrueTypeGX/AAT Validation
      </label>
    
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link md-nav__link--active">
      TrueTypeGX/AAT Validation
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetypegx_validate" class="md-nav__link">
    FT_TrueTypeGX_Validate
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetypegx_free" class="md-nav__link">
    FT_TrueTypeGX_Free
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_classickern_validate" class="md-nav__link">
    FT_ClassicKern_Validate
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_classickern_free" class="md-nav__link">
    FT_ClassicKern_Free
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_validate_gx_length" class="md-nav__link">
    FT_VALIDATE_GX_LENGTH
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_validate_gxxxx" class="md-nav__link">
    FT_VALIDATE_GXXXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_validate_ckernxxx" class="md-nav__link">
    FT_VALIDATE_CKERNXXX
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetypegx_validate" class="md-nav__link">
    FT_TrueTypeGX_Validate
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_truetypegx_free" class="md-nav__link">
    FT_TrueTypeGX_Free
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_classickern_validate" class="md-nav__link">
    FT_ClassicKern_Validate
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_classickern_free" class="md-nav__link">
    FT_ClassicKern_Free
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_validate_gx_length" class="md-nav__link">
    FT_VALIDATE_GX_LENGTH
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_validate_gxxxx" class="md-nav__link">
    FT_VALIDATE_GXXXX
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_validate_ckernxxx" class="md-nav__link">
    FT_VALIDATE_CKERNXXX
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#miscellaneous">Miscellaneous</a> &raquo; TrueTypeGX/AAT Validation</p>
<hr />
<h1 id="truetypegxaat-validation">TrueTypeGX/AAT Validation<a class="headerlink" href="#truetypegxaat-validation" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>This section contains the declaration of functions to validate some TrueTypeGX tables (feat, mort, morx, bsln, just, kern, opbd, trak, prop, lcar).</p>
<h2 id="ft_truetypegx_validate">FT_TrueTypeGX_Validate<a class="headerlink" href="#ft_truetypegx_validate" title="Permanent link">&para;</a></h2>
<p>Defined in FT_GX_VALIDATE_H (freetype/ftgxval.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_TrueTypeGX_Validate</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>   face,
                          <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>   validation_flags,
                          <a href="ft2-basic_types.html#ft_bytes">FT_Bytes</a>  tables[<a href="ft2-gx_validation.html#ft_validate_gx_length">FT_VALIDATE_GX_LENGTH</a>],
                          <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>   table_length );
</code></pre></div>

<p>Validate various TrueTypeGX tables to assure that all offsets and indices are valid. The idea is that a higher-level library that actually does the text layout can access those tables without error checking (which can be quite time consuming).</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the input face.</p>
</td></tr>
<tr><td class="val" id="validation_flags">validation_flags</td><td class="desc">
<p>A bit field that specifies the tables to be validated. See <code><a href="ft2-gx_validation.html#ft_validate_gxxxx">FT_VALIDATE_GXXXX</a></code> for possible values.</p>
</td></tr>
<tr><td class="val" id="table_length">table_length</td><td class="desc">
<p>The size of the <code>tables</code> array. Normally, <code><a href="ft2-gx_validation.html#ft_validate_gx_length">FT_VALIDATE_GX_LENGTH</a></code> should be passed.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="tables">tables</td><td class="desc">
<p>The array where all validated sfnt tables are stored. The array itself must be allocated by a client.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>This function only works with TrueTypeGX fonts, returning an error otherwise.</p>
<p>After use, the application should deallocate the buffers pointed to by each <code>tables</code> element, by calling <code><a href="ft2-gx_validation.html#ft_truetypegx_free">FT_TrueTypeGX_Free</a></code>. A <code>NULL</code> value indicates that the table either doesn't exist in the font, the application hasn't asked for validation, or the validator doesn't have the ability to validate the sfnt table.</p>
<hr>

<h2 id="ft_truetypegx_free">FT_TrueTypeGX_Free<a class="headerlink" href="#ft_truetypegx_free" title="Permanent link">&para;</a></h2>
<p>Defined in FT_GX_VALIDATE_H (freetype/ftgxval.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_TrueTypeGX_Free</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>   face,
                      <a href="ft2-basic_types.html#ft_bytes">FT_Bytes</a>  table );
</code></pre></div>

<p>Free the buffer allocated by TrueTypeGX validator.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the input face.</p>
</td></tr>
<tr><td class="val" id="table">table</td><td class="desc">
<p>The pointer to the buffer allocated by <code><a href="ft2-gx_validation.html#ft_truetypegx_validate">FT_TrueTypeGX_Validate</a></code>.</p>
</td></tr>
</table>

<h4>note</h4>

<p>This function must be used to free the buffer allocated by <code><a href="ft2-gx_validation.html#ft_truetypegx_validate">FT_TrueTypeGX_Validate</a></code> only.</p>
<hr>

<h2 id="ft_classickern_validate">FT_ClassicKern_Validate<a class="headerlink" href="#ft_classickern_validate" title="Permanent link">&para;</a></h2>
<p>Defined in FT_GX_VALIDATE_H (freetype/ftgxval.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_ClassicKern_Validate</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>    face,
                           <a href="ft2-basic_types.html#ft_uint">FT_UInt</a>    validation_flags,
                           <a href="ft2-basic_types.html#ft_bytes">FT_Bytes</a>  *ckern_table );
</code></pre></div>

<p>Validate classic (16-bit format) kern table to assure that the offsets and indices are valid. The idea is that a higher-level library that actually does the text layout can access those tables without error checking (which can be quite time consuming).</p>
<p>The &lsquo;kern&rsquo; table validator in <code><a href="ft2-gx_validation.html#ft_truetypegx_validate">FT_TrueTypeGX_Validate</a></code> deals with both the new 32-bit format and the classic 16-bit format, while FT_ClassicKern_Validate only supports the classic 16-bit format.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the input face.</p>
</td></tr>
<tr><td class="val" id="validation_flags">validation_flags</td><td class="desc">
<p>A bit field that specifies the dialect to be validated. See <code><a href="ft2-gx_validation.html#ft_validate_ckernxxx">FT_VALIDATE_CKERNXXX</a></code> for possible values.</p>
</td></tr>
</table>

<h4>output</h4>

<table class="fields">
<tr><td class="val" id="ckern_table">ckern_table</td><td class="desc">
<p>A pointer to the kern table.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>After use, the application should deallocate the buffers pointed to by <code>ckern_table</code>, by calling <code><a href="ft2-gx_validation.html#ft_classickern_free">FT_ClassicKern_Free</a></code>. A <code>NULL</code> value indicates that the table doesn't exist in the font.</p>
<hr>

<h2 id="ft_classickern_free">FT_ClassicKern_Free<a class="headerlink" href="#ft_classickern_free" title="Permanent link">&para;</a></h2>
<p>Defined in FT_GX_VALIDATE_H (freetype/ftgxval.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_ClassicKern_Free</b>( <a href="ft2-base_interface.html#ft_face">FT_Face</a>   face,
                       <a href="ft2-basic_types.html#ft_bytes">FT_Bytes</a>  table );
</code></pre></div>

<p>Free the buffer allocated by classic Kern validator.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="face">face</td><td class="desc">
<p>A handle to the input face.</p>
</td></tr>
<tr><td class="val" id="table">table</td><td class="desc">
<p>The pointer to the buffer that is allocated by <code><a href="ft2-gx_validation.html#ft_classickern_validate">FT_ClassicKern_Validate</a></code>.</p>
</td></tr>
</table>

<h4>note</h4>

<p>This function must be used to free the buffer allocated by <code><a href="ft2-gx_validation.html#ft_classickern_validate">FT_ClassicKern_Validate</a></code> only.</p>
<hr>

<h2 id="ft_validate_gx_length">FT_VALIDATE_GX_LENGTH<a class="headerlink" href="#ft_validate_gx_length" title="Permanent link">&para;</a></h2>
<p>Defined in FT_GX_VALIDATE_H (freetype/ftgxval.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <b>FT_VALIDATE_GX_LENGTH</b>  ( FT_VALIDATE_GX_LAST_INDEX + 1 )
</code></pre></div>

<p>The number of tables checked in this module. Use it as a parameter for the <code>table-length</code> argument of function <code><a href="ft2-gx_validation.html#ft_truetypegx_validate">FT_TrueTypeGX_Validate</a></code>.</p>
<hr>

<h2 id="ft_validate_gxxxx">FT_VALIDATE_GXXXX<a class="headerlink" href="#ft_validate_gxxxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_GX_VALIDATE_H (freetype/ftgxval.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_feat">FT_VALIDATE_feat</a>  FT_VALIDATE_GX_BITFIELD( feat )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_mort">FT_VALIDATE_mort</a>  FT_VALIDATE_GX_BITFIELD( mort )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_morx">FT_VALIDATE_morx</a>  FT_VALIDATE_GX_BITFIELD( morx )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_bsln">FT_VALIDATE_bsln</a>  FT_VALIDATE_GX_BITFIELD( bsln )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_just">FT_VALIDATE_just</a>  FT_VALIDATE_GX_BITFIELD( just )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_kern">FT_VALIDATE_kern</a>  FT_VALIDATE_GX_BITFIELD( kern )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_opbd">FT_VALIDATE_opbd</a>  FT_VALIDATE_GX_BITFIELD( opbd )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_trak">FT_VALIDATE_trak</a>  FT_VALIDATE_GX_BITFIELD( trak )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_prop">FT_VALIDATE_prop</a>  FT_VALIDATE_GX_BITFIELD( prop )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_lcar">FT_VALIDATE_lcar</a>  FT_VALIDATE_GX_BITFIELD( lcar )

#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_gx">FT_VALIDATE_GX</a>  ( <a href="ft2-gx_validation.html#ft_validate_feat">FT_VALIDATE_feat</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_mort">FT_VALIDATE_mort</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_morx">FT_VALIDATE_morx</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_bsln">FT_VALIDATE_bsln</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_just">FT_VALIDATE_just</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_kern">FT_VALIDATE_kern</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_opbd">FT_VALIDATE_opbd</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_trak">FT_VALIDATE_trak</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_prop">FT_VALIDATE_prop</a> | \
                          <a href="ft2-gx_validation.html#ft_validate_lcar">FT_VALIDATE_lcar</a> )
</code></pre></div>

<p>A list of bit-field constants used with <code><a href="ft2-gx_validation.html#ft_truetypegx_validate">FT_TrueTypeGX_Validate</a></code> to indicate which TrueTypeGX/AAT Type tables should be validated.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_validate_feat">FT_VALIDATE_feat</td><td class="desc">
<p>Validate &lsquo;feat&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_mort">FT_VALIDATE_mort</td><td class="desc">
<p>Validate &lsquo;mort&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_morx">FT_VALIDATE_morx</td><td class="desc">
<p>Validate &lsquo;morx&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_bsln">FT_VALIDATE_bsln</td><td class="desc">
<p>Validate &lsquo;bsln&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_just">FT_VALIDATE_just</td><td class="desc">
<p>Validate &lsquo;just&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_kern">FT_VALIDATE_kern</td><td class="desc">
<p>Validate &lsquo;kern&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_opbd">FT_VALIDATE_opbd</td><td class="desc">
<p>Validate &lsquo;opbd&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_trak">FT_VALIDATE_trak</td><td class="desc">
<p>Validate &lsquo;trak&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_prop">FT_VALIDATE_prop</td><td class="desc">
<p>Validate &lsquo;prop&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_lcar">FT_VALIDATE_lcar</td><td class="desc">
<p>Validate &lsquo;lcar&rsquo; table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_gx">FT_VALIDATE_GX</td><td class="desc">
<p>Validate all TrueTypeGX tables (feat, mort, morx, bsln, just, kern, opbd, trak, prop and lcar).</p>
</td></tr>
</table>

<hr>

<h2 id="ft_validate_ckernxxx">FT_VALIDATE_CKERNXXX<a class="headerlink" href="#ft_validate_ckernxxx" title="Permanent link">&para;</a></h2>
<p>Defined in FT_GX_VALIDATE_H (freetype/ftgxval.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_ms">FT_VALIDATE_MS</a>     ( FT_VALIDATE_GX_START &lt;&lt; 0 )
#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_apple">FT_VALIDATE_APPLE</a>  ( FT_VALIDATE_GX_START &lt;&lt; 1 )

#<span class="keyword">define</span> <a href="ft2-gx_validation.html#ft_validate_ckern">FT_VALIDATE_CKERN</a>  ( <a href="ft2-gx_validation.html#ft_validate_ms">FT_VALIDATE_MS</a> | <a href="ft2-gx_validation.html#ft_validate_apple">FT_VALIDATE_APPLE</a> )
</code></pre></div>

<p>A list of bit-field constants used with <code><a href="ft2-gx_validation.html#ft_classickern_validate">FT_ClassicKern_Validate</a></code> to indicate the classic kern dialect or dialects. If the selected type doesn't fit, <code><a href="ft2-gx_validation.html#ft_classickern_validate">FT_ClassicKern_Validate</a></code> regards the table as invalid.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_validate_ms">FT_VALIDATE_MS</td><td class="desc">
<p>Handle the &lsquo;kern&rsquo; table as a classic Microsoft kern table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_apple">FT_VALIDATE_APPLE</td><td class="desc">
<p>Handle the &lsquo;kern&rsquo; table as a classic Apple kern table.</p>
</td></tr>
<tr><td class="val" id="ft_validate_ckern">FT_VALIDATE_CKERN</td><td class="desc">
<p>Handle the &lsquo;kern&rsquo; as either classic Apple or Microsoft kern table.</p>
</td></tr>
</table>

<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-error_code_values.html" title="Error Code Values" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                Error Code Values
              </span>
            </div>
          </a>
        
        
          <a href="ft2-incremental.html" title="Incremental Loading" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Incremental Loading
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>