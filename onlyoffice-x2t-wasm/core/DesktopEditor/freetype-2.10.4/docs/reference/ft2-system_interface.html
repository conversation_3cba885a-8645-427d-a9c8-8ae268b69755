



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>System Interface - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#system-interface" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                System Interface
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4">
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link">
      Base Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link">
      Glyph Color Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link">
      Header File Macros
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5">
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link">
      Window FNT Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6">
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link">
      Subpixel Rendering
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8" checked>
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link">
      Outline Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link">
      Scanline Converter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        System Interface
      </label>
    
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link md-nav__link--active">
      System Interface
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_memory" class="md-nav__link">
    FT_Memory
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_alloc_func" class="md-nav__link">
    FT_Alloc_Func
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_free_func" class="md-nav__link">
    FT_Free_Func
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_realloc_func" class="md-nav__link">
    FT_Realloc_Func
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_memoryrec" class="md-nav__link">
    FT_MemoryRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_stream" class="md-nav__link">
    FT_Stream
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_streamdesc" class="md-nav__link">
    FT_StreamDesc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_stream_iofunc" class="md-nav__link">
    FT_Stream_IoFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_stream_closefunc" class="md-nav__link">
    FT_Stream_CloseFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_streamrec" class="md-nav__link">
    FT_StreamRec
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10">
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link">
      TrueTypeGX/AAT Validation
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_memory" class="md-nav__link">
    FT_Memory
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_alloc_func" class="md-nav__link">
    FT_Alloc_Func
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_free_func" class="md-nav__link">
    FT_Free_Func
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_realloc_func" class="md-nav__link">
    FT_Realloc_Func
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_memoryrec" class="md-nav__link">
    FT_MemoryRec
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_stream" class="md-nav__link">
    FT_Stream
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_streamdesc" class="md-nav__link">
    FT_StreamDesc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_stream_iofunc" class="md-nav__link">
    FT_Stream_IoFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_stream_closefunc" class="md-nav__link">
    FT_Stream_CloseFunc
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_streamrec" class="md-nav__link">
    FT_StreamRec
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#support-api">Support API</a> &raquo; System Interface</p>
<hr />
<h1 id="system-interface">System Interface<a class="headerlink" href="#system-interface" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>This section contains various definitions related to memory management and i/o access. You need to understand this information if you want to use a custom memory manager or you own i/o streams.</p>
<h2 id="ft_memory">FT_Memory<a class="headerlink" href="#ft_memory" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_MemoryRec_*  <b>FT_Memory</b>;
</code></pre></div>

<p>A handle to a given memory manager object, defined with an <code><a href="ft2-system_interface.html#ft_memoryrec">FT_MemoryRec</a></code> structure.</p>
<hr>

<h2 id="ft_alloc_func">FT_Alloc_Func<a class="headerlink" href="#ft_alloc_func" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">void</span>*
  (*<b>FT_Alloc_Func</b>)( <a href="ft2-system_interface.html#ft_memory">FT_Memory</a>  memory,
                    <span class="keyword">long</span>       size );
</code></pre></div>

<p>A function used to allocate <code>size</code> bytes from <code>memory</code>.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>A handle to the source memory manager.</p>
</td></tr>
<tr><td class="val" id="size">size</td><td class="desc">
<p>The size in bytes to allocate.</p>
</td></tr>
</table>

<h4>return</h4>

<p>Address of new memory block. 0&nbsp;in case of failure.</p>
<hr>

<h2 id="ft_free_func">FT_Free_Func<a class="headerlink" href="#ft_free_func" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Free_Func</b>)( <a href="ft2-system_interface.html#ft_memory">FT_Memory</a>  memory,
                   <span class="keyword">void</span>*      block );
</code></pre></div>

<p>A function used to release a given block of memory.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>A handle to the source memory manager.</p>
</td></tr>
<tr><td class="val" id="block">block</td><td class="desc">
<p>The address of the target memory block.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_realloc_func">FT_Realloc_Func<a class="headerlink" href="#ft_realloc_func" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">void</span>*
  (*<b>FT_Realloc_Func</b>)( <a href="ft2-system_interface.html#ft_memory">FT_Memory</a>  memory,
                      <span class="keyword">long</span>       cur_size,
                      <span class="keyword">long</span>       new_size,
                      <span class="keyword">void</span>*      block );
</code></pre></div>

<p>A function used to re-allocate a given block of memory.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>A handle to the source memory manager.</p>
</td></tr>
<tr><td class="val" id="cur_size">cur_size</td><td class="desc">
<p>The block's current size in bytes.</p>
</td></tr>
<tr><td class="val" id="new_size">new_size</td><td class="desc">
<p>The block's requested new size.</p>
</td></tr>
<tr><td class="val" id="block">block</td><td class="desc">
<p>The block's current address.</p>
</td></tr>
</table>

<h4>return</h4>

<p>New block address. 0&nbsp;in case of memory shortage.</p>
<h4>note</h4>

<p>In case of error, the old block must still be available.</p>
<hr>

<h2 id="ft_memoryrec">FT_MemoryRec<a class="headerlink" href="#ft_memoryrec" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">struct</span>  FT_MemoryRec_
  {
    <span class="keyword">void</span>*            user;
    <a href="ft2-system_interface.html#ft_alloc_func">FT_Alloc_Func</a>    alloc;
    <a href="ft2-system_interface.html#ft_free_func">FT_Free_Func</a>     free;
    <a href="ft2-system_interface.html#ft_realloc_func">FT_Realloc_Func</a>  realloc;
  };
</code></pre></div>

<p>A structure used to describe a given memory manager to FreeType&nbsp;2.</p>
<h4>fields</h4>

<table class="fields">
<tr><td class="val" id="user">user</td><td class="desc">
<p>A generic typeless pointer for user data.</p>
</td></tr>
<tr><td class="val" id="alloc">alloc</td><td class="desc">
<p>A pointer type to an allocation function.</p>
</td></tr>
<tr><td class="val" id="free">free</td><td class="desc">
<p>A pointer type to an memory freeing function.</p>
</td></tr>
<tr><td class="val" id="realloc">realloc</td><td class="desc">
<p>A pointer type to a reallocation function.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_stream">FT_Stream<a class="headerlink" href="#ft_stream" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_StreamRec_*  <b>FT_Stream</b>;
</code></pre></div>

<p>A handle to an input stream.</p>
<h4>also</h4>

<p>See <code><a href="ft2-system_interface.html#ft_streamrec">FT_StreamRec</a></code> for the publicly accessible fields of a given stream object.</p>
<hr>

<h2 id="ft_streamdesc">FT_StreamDesc<a class="headerlink" href="#ft_streamdesc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">union</span>  FT_StreamDesc_
  {
    <span class="keyword">long</span>   value;
    <span class="keyword">void</span>*  pointer;

  } <b>FT_StreamDesc</b>;
</code></pre></div>

<p>A union type used to store either a long or a pointer. This is used to store a file descriptor or a <code>FILE*</code> in an input stream.</p>
<hr>

<h2 id="ft_stream_iofunc">FT_Stream_IoFunc<a class="headerlink" href="#ft_stream_iofunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">unsigned</span> <span class="keyword">long</span>
  (*<b>FT_Stream_IoFunc</b>)( <a href="ft2-system_interface.html#ft_stream">FT_Stream</a>       stream,
                       <span class="keyword">unsigned</span> <span class="keyword">long</span>   offset,
                       <span class="keyword">unsigned</span> <span class="keyword">char</span>*  buffer,
                       <span class="keyword">unsigned</span> <span class="keyword">long</span>   count );
</code></pre></div>

<p>A function used to seek and read data from a given input stream.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="stream">stream</td><td class="desc">
<p>A handle to the source stream.</p>
</td></tr>
<tr><td class="val" id="offset">offset</td><td class="desc">
<p>The offset of read in stream (always from start).</p>
</td></tr>
<tr><td class="val" id="buffer">buffer</td><td class="desc">
<p>The address of the read buffer.</p>
</td></tr>
<tr><td class="val" id="count">count</td><td class="desc">
<p>The number of bytes to read from the stream.</p>
</td></tr>
</table>

<h4>return</h4>

<p>The number of bytes effectively read by the stream.</p>
<h4>note</h4>

<p>This function might be called to perform a seek or skip operation with a <code>count</code> of&nbsp;0. A non-zero return value then indicates an error.</p>
<hr>

<h2 id="ft_stream_closefunc">FT_Stream_CloseFunc<a class="headerlink" href="#ft_stream_closefunc" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Stream_CloseFunc</b>)( <a href="ft2-system_interface.html#ft_stream">FT_Stream</a>  stream );
</code></pre></div>

<p>A function used to close a given input stream.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="stream">stream</td><td class="desc">
<p>A handle to the target stream.</p>
</td></tr>
</table>

<hr>

<h2 id="ft_streamrec">FT_StreamRec<a class="headerlink" href="#ft_streamrec" title="Permanent link">&para;</a></h2>
<p>Defined in FT_SYSTEM_H (freetype/ftsystem.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_StreamRec_
  {
    <span class="keyword">unsigned</span> <span class="keyword">char</span>*       base;
    <span class="keyword">unsigned</span> <span class="keyword">long</span>        size;
    <span class="keyword">unsigned</span> <span class="keyword">long</span>        pos;

    <a href="ft2-system_interface.html#ft_streamdesc">FT_StreamDesc</a>        descriptor;
    <a href="ft2-system_interface.html#ft_streamdesc">FT_StreamDesc</a>        pathname;
    <a href="ft2-system_interface.html#ft_stream_iofunc">FT_Stream_IoFunc</a>     read;
    <a href="ft2-system_interface.html#ft_stream_closefunc">FT_Stream_CloseFunc</a>  close;

    <a href="ft2-system_interface.html#ft_memory">FT_Memory</a>            memory;
    <span class="keyword">unsigned</span> <span class="keyword">char</span>*       cursor;
    <span class="keyword">unsigned</span> <span class="keyword">char</span>*       limit;

  } <b>FT_StreamRec</b>;
</code></pre></div>

<p>A structure used to describe an input stream.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="base">base</td><td class="desc">
<p>For memory-based streams, this is the address of the first stream byte in memory. This field should always be set to <code>NULL</code> for disk-based streams.</p>
</td></tr>
<tr><td class="val" id="size">size</td><td class="desc">
<p>The stream size in bytes.</p>
<p>In case of compressed streams where the size is unknown before actually doing the decompression, the value is set to 0x7FFFFFFF. (Note that this size value can occur for normal streams also; it is thus just a hint.)</p>
</td></tr>
<tr><td class="val" id="pos">pos</td><td class="desc">
<p>The current position within the stream.</p>
</td></tr>
<tr><td class="val" id="descriptor">descriptor</td><td class="desc">
<p>This field is a union that can hold an integer or a pointer. It is used by stream implementations to store file descriptors or <code>FILE*</code> pointers.</p>
</td></tr>
<tr><td class="val" id="pathname">pathname</td><td class="desc">
<p>This field is completely ignored by FreeType. However, it is often useful during debugging to use it to store the stream's filename (where available).</p>
</td></tr>
<tr><td class="val" id="read">read</td><td class="desc">
<p>The stream's input function.</p>
</td></tr>
<tr><td class="val" id="close">close</td><td class="desc">
<p>The stream's close function.</p>
</td></tr>
<tr><td class="val" id="memory">memory</td><td class="desc">
<p>The memory manager to use to preload frames. This is set internally by FreeType and shouldn't be touched by stream implementations.</p>
</td></tr>
<tr><td class="val" id="cursor">cursor</td><td class="desc">
<p>This field is set and used internally by FreeType when parsing frames. In particular, the <code>FT_GET_XXX</code> macros use this instead of the <code>pos</code> field.</p>
</td></tr>
<tr><td class="val" id="limit">limit</td><td class="desc">
<p>This field is set and used internally by FreeType when parsing frames.</p>
</td></tr>
</table>

<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                Glyph Stroker
              </span>
            </div>
          </a>
        
        
          <a href="ft2-module_management.html" title="Module Management" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Module Management
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>