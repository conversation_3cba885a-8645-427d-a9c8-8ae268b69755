



<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      <meta http-equiv="x-ua-compatible" content="ie=edge">
      
        <meta name="description" content="API Reference Documentation for FreeType-2.10.4">
      
      
      
        <meta name="author" content="FreeType Contributors">
      
      
        <meta name="lang:clipboard.copy" content="Copy to clipboard">
      
        <meta name="lang:clipboard.copied" content="Copied to clipboard">
      
        <meta name="lang:search.language" content="en">
      
        <meta name="lang:search.pipeline.stopwords" content="True">
      
        <meta name="lang:search.pipeline.trimmer" content="True">
      
        <meta name="lang:search.result.none" content="No matching documents">
      
        <meta name="lang:search.result.one" content="1 matching document">
      
        <meta name="lang:search.result.other" content="# matching documents">
      
        <meta name="lang:search.tokenizer" content="[\s\-]+">
      
      <link rel="shortcut icon" href="images/favico.ico">
      <meta name="generator" content="mkdocs-1.1, mkdocs-material-4.6.3">
    
    
      
        <title>Subpixel Rendering - FreeType-2.10.4 API Reference</title>
      
    
    
      <link rel="stylesheet" href="assets/stylesheets/application.adb8469c.css">
      
        <link rel="stylesheet" href="assets/stylesheets/application-palette.a8b3c06d.css">
      
      
        
        
        <meta name="theme-color" content="#4caf50">
      
    
    
      <script src="assets/javascripts/modernizr.86422ebf.js"></script>
    
    
      
        <link href="https://fonts.gstatic.com" rel="preconnect" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Noto+Serif:300,400,400i,700%7CRoboto+Mono&display=fallback">
        <style>body,input{font-family:"Noto Serif","Helvetica Neue",Helvetica,Arial,sans-serif}code,kbd,pre{font-family:"Roboto Mono","Courier New",Courier,monospace}</style>
      
    
    <link rel="stylesheet" href="assets/fonts/material-icons.css">
    
    
      <link rel="stylesheet" href="stylesheets/extra.css">
    
    
      
    
    
  </head>
  
    
    
    <body dir="ltr" data-md-color-primary="green" data-md-color-accent="green">
  
    <svg class="md-svg">
      <defs>
        
        
      </defs>
    </svg>
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" data-md-component="overlay" for="__drawer"></label>
    
      <a href="#subpixel-rendering" tabindex="0" class="md-skip">
        Skip to content
      </a>
    
    
      <header class="md-header" data-md-component="header">
  <nav class="md-header-nav md-grid">
    <div class="md-flex">
      <div class="md-flex__cell md-flex__cell--shrink">
        <a href="." title="FreeType-2.10.4 API Reference" aria-label="FreeType-2.10.4 API Reference" class="md-header-nav__button md-logo">
          
            <img alt="logo" src="images/favico.ico" width="24" height="24">
          
        </a>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        <label class="md-icon md-icon--menu md-header-nav__button" for="__drawer"></label>
      </div>
      <div class="md-flex__cell md-flex__cell--stretch">
        <div class="md-flex__ellipsis md-header-nav__title" data-md-component="title">
          
            <span class="md-header-nav__topic">
              FreeType-2.10.4 API Reference
            </span>
            <span class="md-header-nav__topic">
              
                Subpixel Rendering
              
            </span>
          
        </div>
      </div>
      <div class="md-flex__cell md-flex__cell--shrink">
        
          <label class="md-icon md-icon--search md-header-nav__button" for="__search"></label>
          
<div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" aria-label="search" name="query" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="query" data-md-state="active">
      <label class="md-icon md-search__icon" for="__search"></label>
      <button type="reset" class="md-icon md-search__icon" data-md-component="reset" tabindex="-1">
        &#xE5CD;
      </button>
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="result">
          <div class="md-search-result__meta">
            Type to start searching
          </div>
          <ol class="md-search-result__list"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
        
      </div>
      
    </div>
  </nav>
</header>
    
    <div class="md-container">
      
        
      
      
      <main class="md-main" role="main">
        <div class="md-main__inner md-grid" data-md-component="container">
          
            
              <div class="md-sidebar md-sidebar--primary" data-md-component="navigation">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    <nav class="md-nav md-nav--primary" data-md-level="0">
  <label class="md-nav__title md-nav__title--site" for="__drawer">
    <a href="." title="FreeType-2.10.4 API Reference" class="md-nav__button md-logo">
      
        <img alt="logo" src="images/favico.ico" width="48" height="48">
      
    </a>
    FreeType-2.10.4 API Reference
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
      


  <li class="md-nav__item">
    <a href="index.html" title="TOC" class="md-nav__link">
      TOC
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item">
    <a href="ft2-index.html" title="Index" class="md-nav__link">
      Index
    </a>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-3" type="checkbox" id="nav-3">
    
    <label class="md-nav__link" for="nav-3">
      General Remarks
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-3">
        General Remarks
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_inclusion.html" title="FreeType's header inclusion scheme" class="md-nav__link">
      FreeType's header inclusion scheme
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-user_allocation.html" title="User allocation" class="md-nav__link">
      User allocation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-4" type="checkbox" id="nav-4">
    
    <label class="md-nav__link" for="nav-4">
      Core API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-4">
        Core API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-version.html" title="FreeType Version" class="md-nav__link">
      FreeType Version
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-basic_types.html" title="Basic Data Types" class="md-nav__link">
      Basic Data Types
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-base_interface.html" title="Base Interface" class="md-nav__link">
      Base Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_variants.html" title="Unicode Variation Sequences" class="md-nav__link">
      Unicode Variation Sequences
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-color_management.html" title="Glyph Color Management" class="md-nav__link">
      Glyph Color Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-layer_management.html" title="Glyph Layer Management" class="md-nav__link">
      Glyph Layer Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_management.html" title="Glyph Management" class="md-nav__link">
      Glyph Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-mac_specific.html" title="Mac Specific Interface" class="md-nav__link">
      Mac Specific Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sizes_management.html" title="Size Management" class="md-nav__link">
      Size Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-header_file_macros.html" title="Header File Macros" class="md-nav__link">
      Header File Macros
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-5" type="checkbox" id="nav-5">
    
    <label class="md-nav__link" for="nav-5">
      Format-Specific API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-5">
        Format-Specific API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-multiple_masters.html" title="Multiple Masters" class="md-nav__link">
      Multiple Masters
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_tables.html" title="TrueType Tables" class="md-nav__link">
      TrueType Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-type1_tables.html" title="Type 1 Tables" class="md-nav__link">
      Type 1 Tables
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-sfnt_names.html" title="SFNT Names" class="md-nav__link">
      SFNT Names
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bdf_fonts.html" title="BDF and PCF Files" class="md-nav__link">
      BDF and PCF Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cid_fonts.html" title="CID Fonts" class="md-nav__link">
      CID Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pfr_fonts.html" title="PFR Fonts" class="md-nav__link">
      PFR Fonts
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-winfnt_fonts.html" title="Window FNT Files" class="md-nav__link">
      Window FNT Files
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-font_formats.html" title="Font Formats" class="md-nav__link">
      Font Formats
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gasp_table.html" title="Gasp Table" class="md-nav__link">
      Gasp Table
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      

  


  <li class="md-nav__item md-nav__item--active md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-6" type="checkbox" id="nav-6" checked>
    
    <label class="md-nav__link" for="nav-6">
      Controlling FreeType Modules
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-6">
        Controlling FreeType Modules
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-auto_hinter.html" title="The auto-hinter" class="md-nav__link">
      The auto-hinter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cff_driver.html" title="The CFF driver" class="md-nav__link">
      The CFF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-t1_cid_driver.html" title="The Type 1 and CID drivers" class="md-nav__link">
      The Type 1 and CID drivers
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-tt_driver.html" title="The TrueType driver" class="md-nav__link">
      The TrueType driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-pcf_driver.html" title="The PCF driver" class="md-nav__link">
      The PCF driver
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-properties.html" title="Driver properties" class="md-nav__link">
      Driver properties
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-nav__link">
      Parameter Tags
    </a>
  </li>

        
          
          
          

  


  <li class="md-nav__item md-nav__item--active">
    
    <input class="md-toggle md-nav__toggle" data-md-toggle="toc" type="checkbox" id="__toc">
    
      
    
    
      <label class="md-nav__link md-nav__link--active" for="__toc">
        Subpixel Rendering
      </label>
    
    <a href="ft2-lcd_rendering.html" title="Subpixel Rendering" class="md-nav__link md-nav__link--active">
      Subpixel Rendering
    </a>
    
      
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_lcdfilter" class="md-nav__link">
    FT_LcdFilter
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_library_setlcdfilter" class="md-nav__link">
    FT_Library_SetLcdFilter
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_library_setlcdfilterweights" class="md-nav__link">
    FT_Library_SetLcdFilterWeights
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_lcdfivetapfilter" class="md-nav__link">
    FT_LcdFiveTapFilter
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_library_setlcdgeometry" class="md-nav__link">
    FT_Library_SetLcdGeometry
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
    
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-7" type="checkbox" id="nav-7">
    
    <label class="md-nav__link" for="nav-7">
      Cache Sub-System
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-7">
        Cache Sub-System
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-nav__link">
      Cache Sub-System
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-8" type="checkbox" id="nav-8">
    
    <label class="md-nav__link" for="nav-8">
      Support API
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-8">
        Support API
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-computations.html" title="Computations" class="md-nav__link">
      Computations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-list_processing.html" title="List Processing" class="md-nav__link">
      List Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-outline_processing.html" title="Outline Processing" class="md-nav__link">
      Outline Processing
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-quick_advance.html" title="Quick retrieval of advance values" class="md-nav__link">
      Quick retrieval of advance values
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bitmap_handling.html" title="Bitmap Handling" class="md-nav__link">
      Bitmap Handling
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-raster.html" title="Scanline Converter" class="md-nav__link">
      Scanline Converter
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-glyph_stroker.html" title="Glyph Stroker" class="md-nav__link">
      Glyph Stroker
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-system_interface.html" title="System Interface" class="md-nav__link">
      System Interface
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-module_management.html" title="Module Management" class="md-nav__link">
      Module Management
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gzip.html" title="GZIP Streams" class="md-nav__link">
      GZIP Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-lzw.html" title="LZW Streams" class="md-nav__link">
      LZW Streams
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-bzip2.html" title="BZIP2 Streams" class="md-nav__link">
      BZIP2 Streams
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-9" type="checkbox" id="nav-9">
    
    <label class="md-nav__link" for="nav-9">
      Error Codes
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-9">
        Error Codes
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_enumerations.html" title="Error Enumerations" class="md-nav__link">
      Error Enumerations
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-error_code_values.html" title="Error Code Values" class="md-nav__link">
      Error Code Values
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
      
      
      


  <li class="md-nav__item md-nav__item--nested">
    
      <input class="md-toggle md-nav__toggle" data-md-toggle="nav-10" type="checkbox" id="nav-10">
    
    <label class="md-nav__link" for="nav-10">
      Miscellaneous
    </label>
    <nav class="md-nav" data-md-component="collapsible" data-md-level="1">
      <label class="md-nav__title" for="nav-10">
        Miscellaneous
      </label>
      <ul class="md-nav__list" data-md-scrollfix>
        
        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-gx_validation.html" title="TrueTypeGX/AAT Validation" class="md-nav__link">
      TrueTypeGX/AAT Validation
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-incremental.html" title="Incremental Loading" class="md-nav__link">
      Incremental Loading
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-truetype_engine.html" title="The TrueType Engine" class="md-nav__link">
      The TrueType Engine
    </a>
  </li>

        
          
          
          


  <li class="md-nav__item">
    <a href="ft2-ot_validation.html" title="OpenType Validation" class="md-nav__link">
      OpenType Validation
    </a>
  </li>

        
      </ul>
    </nav>
  </li>

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
              <div class="md-sidebar md-sidebar--secondary" data-md-component="toc">
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    
<nav class="md-nav md-nav--secondary">
  
  
    
  
  
    <label class="md-nav__title" for="__toc">Table of contents</label>
    <ul class="md-nav__list" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#synopsis" class="md-nav__link">
    Synopsis
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_lcdfilter" class="md-nav__link">
    FT_LcdFilter
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_library_setlcdfilter" class="md-nav__link">
    FT_Library_SetLcdFilter
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_library_setlcdfilterweights" class="md-nav__link">
    FT_Library_SetLcdFilterWeights
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_lcdfivetapfilter" class="md-nav__link">
    FT_LcdFiveTapFilter
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#ft_library_setlcdgeometry" class="md-nav__link">
    FT_Library_SetLcdGeometry
  </a>
  
</li>
      
      
      
      
      
    </ul>
  
</nav>
                  </div>
                </div>
              </div>
            
          
          <div class="md-content">
            <article class="md-content__inner md-typeset">
              
                
                
                <p><a href="https://www.freetype.org">FreeType</a> &raquo; <a href="../">Docs</a> &raquo; <a href="index.html#controlling-freetype-modules">Controlling FreeType Modules</a> &raquo; Subpixel Rendering</p>
<hr />
<h1 id="subpixel-rendering">Subpixel Rendering<a class="headerlink" href="#subpixel-rendering" title="Permanent link">&para;</a></h1>
<h2 id="synopsis">Synopsis<a class="headerlink" href="#synopsis" title="Permanent link">&para;</a></h2>
<p>FreeType provides two alternative subpixel rendering technologies. Should you define <code>FT_CONFIG_OPTION_SUBPIXEL_RENDERING</code> in your <code>ftoption.h</code> file, this enables ClearType-style rendering. Otherwise, Harmony LCD rendering is enabled. These technologies are controlled differently and API described below, although always available, performs its function when appropriate method is enabled and does nothing otherwise.</p>
<p>ClearType-style LCD rendering exploits the color-striped structure of LCD pixels, increasing the available resolution in the direction of the stripe (usually horizontal RGB) by a factor of&nbsp;3. Using the subpixels coverages unfiltered can create severe color fringes especially when rendering thin features. Indeed, to produce black-on-white text, the nearby color subpixels must be dimmed equally.</p>
<p>A good 5-tap FIR filter should be applied to subpixel coverages regardless of pixel boundaries and should have these properties:</p>
<ol>
<li>
<p>It should be symmetrical, like {&nbsp;a, b, c, b, a&nbsp;}, to avoid any shifts in appearance.</p>
</li>
<li>
<p>It should be color-balanced, meaning a&nbsp;+ b&nbsp;=&nbsp;c, to reduce color fringes by distributing the computed coverage for one subpixel to all subpixels equally.</p>
</li>
<li>
<p>It should be normalized, meaning 2a&nbsp;+ 2b&nbsp;+ c&nbsp;=&nbsp;1.0 to maintain overall brightness.</p>
</li>
</ol>
<p>Boxy 3-tap filter {0, &#8531;, &#8531;, &#8531;, 0} is sharper but is less forgiving of non-ideal gamma curves of a screen (and viewing angles), beveled filters are fuzzier but more tolerant.</p>
<p>Use the <code><a href="ft2-lcd_rendering.html#ft_library_setlcdfilter">FT_Library_SetLcdFilter</a></code> or <code><a href="ft2-lcd_rendering.html#ft_library_setlcdfilterweights">FT_Library_SetLcdFilterWeights</a></code> API to specify a low-pass filter, which is then applied to subpixel-rendered bitmaps generated through <code><a href="ft2-base_interface.html#ft_render_glyph">FT_Render_Glyph</a></code>.</p>
<p>Harmony LCD rendering is suitable to panels with any regular subpixel structure, not just monitors with 3 color striped subpixels, as long as the color subpixels have fixed positions relative to the pixel center. In this case, each color channel is then rendered separately after shifting the outline opposite to the subpixel shift so that the coverage maps are aligned. This method is immune to color fringes because the shifts do not change integral coverage.</p>
<p>The subpixel geometry must be specified by xy-coordinates for each subpixel. By convention they may come in the RGB order: {{-&#8531;, 0}, {0, 0}, {&#8531;, 0}} for standard RGB striped panel or {{-&#8537;, &frac14;}, {-&#8537;, -&frac14;}, {&#8531;, 0}} for a certain PenTile panel.</p>
<p>Use the <code><a href="ft2-lcd_rendering.html#ft_library_setlcdgeometry">FT_Library_SetLcdGeometry</a></code> API to specify subpixel positions. If one follows the RGB order convention, the same order applies to the resulting <code><a href="ft2-basic_types.html#ft_pixel_mode">FT_PIXEL_MODE_LCD</a></code> and <code><a href="ft2-basic_types.html#ft_pixel_mode">FT_PIXEL_MODE_LCD_V</a></code> bitmaps. Note, however, that the coordinate frame for the latter must be rotated clockwise. Harmony with default LCD geometry is equivalent to ClearType with light filter.</p>
<p>As a result of ClearType filtering or Harmony rendering, the dimensions of LCD bitmaps can be either wider or taller than the dimensions of the corresponding outline with regard to the pixel grid. For example, for <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_LCD</a></code>, the filter adds 2&nbsp;subpixels to the left, and 2&nbsp;subpixels to the right. The bitmap offset values are adjusted accordingly, so clients shouldn't need to modify their layout and glyph positioning code when enabling the filter.</p>
<p>The ClearType and Harmony rendering is applicable to glyph bitmaps rendered through <code><a href="ft2-base_interface.html#ft_render_glyph">FT_Render_Glyph</a></code>, <code><a href="ft2-base_interface.html#ft_load_glyph">FT_Load_Glyph</a></code>, <code><a href="ft2-base_interface.html#ft_load_char">FT_Load_Char</a></code>, and <code><a href="ft2-glyph_management.html#ft_glyph_to_bitmap">FT_Glyph_To_Bitmap</a></code>, when <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_LCD</a></code> or <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_LCD_V</a></code> is specified. This API does not control <code><a href="ft2-outline_processing.html#ft_outline_render">FT_Outline_Render</a></code> and <code><a href="ft2-outline_processing.html#ft_outline_get_bitmap">FT_Outline_Get_Bitmap</a></code>.</p>
<p>The described algorithms can completely remove color artefacts when combined with gamma-corrected alpha blending in linear space. Each of the 3&nbsp;alpha values (subpixels) must by independently used to blend one color channel. That is, red alpha blends the red channel of the text color with the red channel of the background pixel.</p>
<h2 id="ft_lcdfilter">FT_LcdFilter<a class="headerlink" href="#ft_lcdfilter" title="Permanent link">&para;</a></h2>
<p>Defined in FT_LCD_FILTER_H (freetype/ftlcdfil.h).</p>
<div class = "codehilite"><pre><code>  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_LcdFilter_
  {
    <a href="ft2-lcd_rendering.html#ft_lcd_filter_none">FT_LCD_FILTER_NONE</a>    = 0,
    <a href="ft2-lcd_rendering.html#ft_lcd_filter_default">FT_LCD_FILTER_DEFAULT</a> = 1,
    <a href="ft2-lcd_rendering.html#ft_lcd_filter_light">FT_LCD_FILTER_LIGHT</a>   = 2,
    <a href="ft2-lcd_rendering.html#ft_lcd_filter_legacy1">FT_LCD_FILTER_LEGACY1</a> = 3,
    <a href="ft2-lcd_rendering.html#ft_lcd_filter_legacy">FT_LCD_FILTER_LEGACY</a>  = 16,

    FT_LCD_FILTER_MAX   /* do not remove */

  } <b>FT_LcdFilter</b>;
</code></pre></div>

<p>A list of values to identify various types of LCD filters.</p>
<h4>values</h4>

<table class="fields">
<tr><td class="val" id="ft_lcd_filter_none">FT_LCD_FILTER_NONE</td><td class="desc">
<p>Do not perform filtering. When used with subpixel rendering, this results in sometimes severe color fringes.</p>
</td></tr>
<tr><td class="val" id="ft_lcd_filter_default">FT_LCD_FILTER_DEFAULT</td><td class="desc">
<p>This is a beveled, normalized, and color-balanced five-tap filter with weights of [0x08 0x4D 0x56 0x4D 0x08] in 1/256th units.</p>
</td></tr>
<tr><td class="val" id="ft_lcd_filter_light">FT_LCD_FILTER_LIGHT</td><td class="desc">
<p>this is a boxy, normalized, and color-balanced three-tap filter with weights of [0x00 0x55 0x56 0x55 0x00] in 1/256th units.</p>
</td></tr>
<tr><td class="val" id="ft_lcd_filter_legacy">FT_LCD_FILTER_LEGACY</td><td class="desc">

</td></tr>
<tr><td class="val" id="ft_lcd_filter_legacy1">FT_LCD_FILTER_LEGACY1</td><td class="desc">
<p>This filter corresponds to the original libXft color filter. It provides high contrast output but can exhibit really bad color fringes if glyphs are not extremely well hinted to the pixel grid. This filter is only provided for comparison purposes, and might be disabled or stay unsupported in the future. The second value is provided for compatibility with FontConfig, which historically used different enumeration, sometimes incorrectly forwarded to FreeType.</p>
</td></tr>
</table>

<h4>since</h4>

<p>2.3.0 (<code>FT_LCD_FILTER_LEGACY1</code> since 2.6.2)</p>
<hr>

<h2 id="ft_library_setlcdfilter">FT_Library_SetLcdFilter<a class="headerlink" href="#ft_library_setlcdfilter" title="Permanent link">&para;</a></h2>
<p>Defined in FT_LCD_FILTER_H (freetype/ftlcdfil.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Library_SetLcdFilter</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>    library,
                           <a href="ft2-lcd_rendering.html#ft_lcdfilter">FT_LcdFilter</a>  filter );
</code></pre></div>

<p>This function is used to change filter applied to LCD decimated bitmaps, like the ones used when calling <code><a href="ft2-base_interface.html#ft_render_glyph">FT_Render_Glyph</a></code> with <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_LCD</a></code> or <code><a href="ft2-base_interface.html#ft_render_mode">FT_RENDER_MODE_LCD_V</a></code>.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the target library instance.</p>
</td></tr>
<tr><td class="val" id="filter">filter</td><td class="desc">
<p>The filter type.</p>
<p>You can use <code><a href="ft2-lcd_rendering.html#ft_lcdfilter">FT_LCD_FILTER_NONE</a></code> here to disable this feature, or <code><a href="ft2-lcd_rendering.html#ft_lcdfilter">FT_LCD_FILTER_DEFAULT</a></code> to use a default filter that should work well on most LCD screens.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>Since 2.10.3 the LCD filtering is enabled with <code><a href="ft2-lcd_rendering.html#ft_lcdfilter">FT_LCD_FILTER_DEFAULT</a></code>. It is no longer necessary to call this function explicitly except to choose a different filter or disable filtering altogether with <code><a href="ft2-lcd_rendering.html#ft_lcdfilter">FT_LCD_FILTER_NONE</a></code>.</p>
<p>This function does nothing but returns <code>FT_Err_Unimplemented_Feature</code> if the configuration macro <code>FT_CONFIG_OPTION_SUBPIXEL_RENDERING</code> is not defined in your build of the library.</p>
<h4>since</h4>

<p>2.3.0</p>
<hr>

<h2 id="ft_library_setlcdfilterweights">FT_Library_SetLcdFilterWeights<a class="headerlink" href="#ft_library_setlcdfilterweights" title="Permanent link">&para;</a></h2>
<p>Defined in FT_LCD_FILTER_H (freetype/ftlcdfil.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Library_SetLcdFilterWeights</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>      library,
                                  <span class="keyword">unsigned</span> <span class="keyword">char</span>  *weights );
</code></pre></div>

<p>This function can be used to enable LCD filter with custom weights, instead of using presets in <code><a href="ft2-lcd_rendering.html#ft_library_setlcdfilter">FT_Library_SetLcdFilter</a></code>.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the target library instance.</p>
</td></tr>
<tr><td class="val" id="weights">weights</td><td class="desc">
<p>A pointer to an array; the function copies the first five bytes and uses them to specify the filter weights in 1/256th units.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>This function does nothing but returns <code>FT_Err_Unimplemented_Feature</code> if the configuration macro <code>FT_CONFIG_OPTION_SUBPIXEL_RENDERING</code> is not defined in your build of the library.</p>
<p>LCD filter weights can also be set per face using <code><a href="ft2-base_interface.html#ft_face_properties">FT_Face_Properties</a></code> with <code><a href="ft2-parameter_tags.html#ft_param_tag_lcd_filter_weights">FT_PARAM_TAG_LCD_FILTER_WEIGHTS</a></code>.</p>
<h4>since</h4>

<p>2.4.0</p>
<hr>

<h2 id="ft_lcdfivetapfilter">FT_LcdFiveTapFilter<a class="headerlink" href="#ft_lcdfivetapfilter" title="Permanent link">&para;</a></h2>
<p>Defined in FT_LCD_FILTER_H (freetype/ftlcdfil.h).</p>
<div class = "codehilite"><pre><code>#<span class="keyword">define</span> FT_LCD_FILTER_FIVE_TAPS  5

  <span class="keyword">typedef</span> <a href="ft2-basic_types.html#ft_byte">FT_Byte</a>  <b>FT_LcdFiveTapFilter</b>[FT_LCD_FILTER_FIVE_TAPS];
</code></pre></div>

<p>A typedef for passing the five LCD filter weights to <code><a href="ft2-base_interface.html#ft_face_properties">FT_Face_Properties</a></code> within an <code><a href="ft2-base_interface.html#ft_parameter">FT_Parameter</a></code> structure.</p>
<h4>since</h4>

<p>2.8</p>
<hr>

<h2 id="ft_library_setlcdgeometry">FT_Library_SetLcdGeometry<a class="headerlink" href="#ft_library_setlcdgeometry" title="Permanent link">&para;</a></h2>
<p>Defined in FT_LCD_FILTER_H (freetype/ftlcdfil.h).</p>
<div class = "codehilite"><pre><code>  FT_EXPORT( <a href="ft2-basic_types.html#ft_error">FT_Error</a> )
  <b>FT_Library_SetLcdGeometry</b>( <a href="ft2-base_interface.html#ft_library">FT_Library</a>  library,
                             <a href="ft2-basic_types.html#ft_vector">FT_Vector</a>   sub[3] );
</code></pre></div>

<p>This function can be used to modify default positions of color subpixels, which controls Harmony LCD rendering.</p>
<h4>input</h4>

<table class="fields">
<tr><td class="val" id="library">library</td><td class="desc">
<p>A handle to the target library instance.</p>
</td></tr>
<tr><td class="val" id="sub">sub</td><td class="desc">
<p>A pointer to an array of 3 vectors in 26.6 fractional pixel format; the function modifies the default values, see the note below.</p>
</td></tr>
</table>

<h4>return</h4>

<p>FreeType error code. 0&nbsp;means success.</p>
<h4>note</h4>

<p>Subpixel geometry examples:</p>
<ul>
<li>
<p>{{-21, 0}, {0, 0}, {21, 0}} is the default, corresponding to 3 color stripes shifted by a third of a pixel. This could be an RGB panel.</p>
</li>
<li>
<p>{{21, 0}, {0, 0}, {-21, 0}} looks the same as the default but can specify a BGR panel instead, while keeping the bitmap in the same RGB888 format.</p>
</li>
<li>
<p>{{0, 21}, {0, 0}, {0, -21}} is the vertical RGB, but the bitmap stays RGB888 as a result.</p>
</li>
<li>
<p>{{-11, 16}, {-11, -16}, {22, 0}} is a certain PenTile arrangement.</p>
</li>
</ul>
<p>This function does nothing and returns <code>FT_Err_Unimplemented_Feature</code> in the context of ClearType-style subpixel rendering when <code>FT_CONFIG_OPTION_SUBPIXEL_RENDERING</code> is defined in your build of the library.</p>
<h4>since</h4>

<p>2.10.0</p>
<hr>
                
                  
                
                
              
              
                


              
            </article>
          </div>
        </div>
      </main>
      
        
<footer class="md-footer">
  
    <div class="md-footer-nav">
      <nav class="md-footer-nav__inner md-grid">
        
          <a href="ft2-parameter_tags.html" title="Parameter Tags" class="md-flex md-footer-nav__link md-footer-nav__link--prev" rel="prev">
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-back md-footer-nav__button"></i>
            </div>
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Previous
                </span>
                Parameter Tags
              </span>
            </div>
          </a>
        
        
          <a href="ft2-cache_subsystem.html" title="Cache Sub-System" class="md-flex md-footer-nav__link md-footer-nav__link--next" rel="next">
            <div class="md-flex__cell md-flex__cell--stretch md-footer-nav__title">
              <span class="md-flex__ellipsis">
                <span class="md-footer-nav__direction">
                  Next
                </span>
                Cache Sub-System
              </span>
            </div>
            <div class="md-flex__cell md-flex__cell--shrink">
              <i class="md-icon md-icon--arrow-forward md-footer-nav__button"></i>
            </div>
          </a>
        
      </nav>
    </div>
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-footer-copyright">
        
          <div class="md-footer-copyright__highlight">
            Copyright 2020 <a href = "https://www.freetype.org/license.html">The FreeType Project</a>.
          </div>
        
        powered by
        <a href="https://www.mkdocs.org" target="_blank" rel="noopener">MkDocs</a>
        and
        <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
          Material for MkDocs</a>
      </div>
      
    </div>
  </div>
</footer>
      
    </div>
    
      <script src="assets/javascripts/application.c33a9706.js"></script>
      
      <script>app.initialize({version:"1.1",url:{base:"."}})</script>
      
        <script src="javascripts/extra.js"></script>
      
    
  </body>
</html>