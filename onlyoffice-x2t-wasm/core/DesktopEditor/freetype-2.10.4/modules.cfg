# modules.cfg
#
# Copyright (C) 2005-2020 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.
#
#
# In case you compile the FreeType library with GNU make or makepp, this
# file controls which components are built into the library.  Otherwise,
# please read this file for information on the various modules and its
# dependencies, then follow the instructions in the file `docs/INSTALL.ANY'.
#
# To deactivate a module, simply comment out the corresponding line.  To
# activate a module, remove the comment character.
#
# Note that many modules and components are further controlled with macros
# in the file `include/freetype/config/ftoption.h'.


####
#### font modules -- at least one is required
####
####   The order given here (from top to down) is the order used for testing
####   font formats in the compiled library.
####

# TrueType font driver.
#
# This driver needs the `sfnt' module.
FONT_MODULES += truetype

# PostScript Type 1 font driver.
#
# This driver needs the `psaux', `pshinter', and `psnames' modules.
FONT_MODULES += type1

# CFF/OpenType font driver.
#
# This driver needs the `sfnt', `psaux', `pshinter', and `psnames' modules.
FONT_MODULES += cff

# Type 1 CID-keyed font driver.
#
# This driver needs the `psaux', `pshinter', and `psnames' modules.
FONT_MODULES += cid

# PFR/TrueDoc font driver.  See optional extension ftpfr.c below also.
FONT_MODULES += pfr

# PostScript Type 42 font driver.
#
# This driver needs the `truetype' and `psaux' modules.
FONT_MODULES += type42

# Windows FONT/FNT font driver.  See optional extension ftwinfnt.c below
# also.
FONT_MODULES += winfonts

# PCF font driver.  If debugging and tracing is enabled, needs `ftbitmap.c'.
FONT_MODULES += pcf

# BDF font driver.  See optional extension ftbdf.c below also.
FONT_MODULES += bdf

# SFNT files support.  If used without `truetype' or `cff', it supports
# bitmap-only fonts within an SFNT wrapper.
#
# This driver needs the `psnames' module.
FONT_MODULES += sfnt


####
#### hinting modules
####

# FreeType's auto hinter.
HINTING_MODULES += autofit

# PostScript hinter.
HINTING_MODULES += pshinter

# The TrueType hinting engine doesn't have a module of its own but is
# controlled in file include/freetype/config/ftoption.h
# (TT_CONFIG_OPTION_BYTECODE_INTERPRETER and friends).


####
#### raster modules -- at least one is required for vector font formats
####

# Monochrome rasterizer.
RASTER_MODULES += raster

# Anti-aliasing rasterizer.
RASTER_MODULES += smooth


####
#### auxiliary modules
####

# FreeType's cache sub-system (quite stable but still in beta -- this means
# that its public API is subject to change if necessary).  See
# include/freetype/ftcache.h.  Needs `ftglyph.c'.
AUX_MODULES += cache

# TrueType GX/AAT table validation.  Needs `ftgxval.c' below.
#
# AUX_MODULES += gxvalid

# Support for streams compressed with gzip (files with suffix .gz).
#
# See include/freetype/ftgzip.h for the API.
AUX_MODULES += gzip

# Support for streams compressed with LZW (files with suffix .Z).
#
# See include/freetype/ftlzw.h for the API.
AUX_MODULES += lzw

# Support for streams compressed with bzip2 (files with suffix .bz2).
#
# See include/freetype/ftbzip2.h for the API.
AUX_MODULES += bzip2

# OpenType table validation.  Needs `ftotval.c' below.
#
# AUX_MODULES += otvalid

# Auxiliary PostScript driver component to share common code.
#
# This module depends on `psnames'.
AUX_MODULES += psaux

# Support for PostScript glyph names.
#
# This module can be controlled in ftconfig.h
# (FT_CONFIG_OPTION_POSTSCRIPT_NAMES).
AUX_MODULES += psnames


####
#### base module extensions
####

# Exact bounding box calculation.
#
# See include/freetype/ftbbox.h for the API.
BASE_EXTENSIONS += ftbbox.c

# Access BDF-specific strings.  Needs BDF font driver.
#
# See include/freetype/ftbdf.h for the API.
BASE_EXTENSIONS += ftbdf.c

# Utility functions for converting 1bpp, 2bpp, 4bpp, and 8bpp bitmaps into
# 8bpp format, and for emboldening of bitmap glyphs.
#
# See include/freetype/ftbitmap.h for the API.
BASE_EXTENSIONS += ftbitmap.c

# Access CID font information.
#
# See include/freetype/ftcid.h for the API.
BASE_EXTENSIONS += ftcid.c

# Access FSType information.  Needs `fttype1.c'.
#
# See include/freetype/freetype.h for the API.
BASE_EXTENSIONS += ftfstype.c

# Support for GASP table queries.
#
# See include/freetype/ftgasp.h for the API.
BASE_EXTENSIONS += ftgasp.c

# Convenience functions to handle glyphs.  Needs `ftbitmap.c'.
#
# See include/freetype/ftglyph.h for the API.
BASE_EXTENSIONS += ftglyph.c

# Interface for gxvalid module.
#
# See include/freetype/ftgxval.h for the API.
BASE_EXTENSIONS += ftgxval.c

# Multiple Master font interface.
#
# See include/freetype/ftmm.h for the API.
BASE_EXTENSIONS += ftmm.c

# Interface for otvalid module.
#
# See include/freetype/ftotval.h for the API.
BASE_EXTENSIONS += ftotval.c

# Support for FT_Face_CheckTrueTypePatents.
#
# See include/freetype/freetype.h for the API.
BASE_EXTENSIONS += ftpatent.c

# Interface for accessing PFR-specific data.  Needs PFR font driver.
#
# See include/freetype/ftpfr.h for the API.
BASE_EXTENSIONS += ftpfr.c

# Path stroker.  Needs `ftglyph.c'.
#
# See include/freetype/ftstroke.h for the API.
BASE_EXTENSIONS += ftstroke.c

# Support for synthetic emboldening and slanting of fonts.  Needs
# `ftbitmap.c'.
#
# See include/freetype/ftsynth.h for the API.
BASE_EXTENSIONS += ftsynth.c

# Interface to access data specific to PostScript Type 1 and Type 2 (CFF)
# fonts.
#
# See include/freetype/t1tables.h for the API.
BASE_EXTENSIONS += fttype1.c

# Interface for accessing data specific to Windows FNT files.  Needs winfnt
# driver.
#
# See include/freetype/ftwinfnt.h for the API.
BASE_EXTENSIONS += ftwinfnt.c

####
#### The components `ftsystem.c' (for memory allocation and stream I/O
#### management) and `ftdebug.c' (for emitting debug messages to the user)
#### are controlled with the following variables.
####
####   ftsystem.c:  $(FTSYS_SRC)
####   ftdebug.c:   $(FTDEBUG_SRC)
####
#### Please refer to docs/CUSTOMIZE for details.
####


# EOF
