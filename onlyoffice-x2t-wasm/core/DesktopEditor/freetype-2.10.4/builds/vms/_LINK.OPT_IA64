 case_sensitive=YES/  symbol_vector=(FTC_CMapCache_Lookup=PROCEDURE) ,  symbol_vector=(FTC_CMapCache_New=PROCEDURE)0  symbol_vector=(FTC_ImageCache_Lookup=PROCEDURE)6  symbol_vector=(FTC_ImageCache_LookupScaler=PROCEDURE)-  symbol_vector=(FTC_ImageCache_New=PROCEDURE) +  symbol_vector=(FTC_Manager_Done=PROCEDURE) 1  symbol_vector=(FTC_Manager_LookupFace=PROCEDURE) 1  symbol_vector=(FTC_Manager_LookupSize=PROCEDURE) *  symbol_vector=(FTC_Manager_New=PROCEDURE)3  symbol_vector=(FTC_Manager_RemoveFaceID=PROCEDURE) ,  symbol_vector=(FTC_Manager_Reset=PROCEDURE))  symbol_vector=(FTC_Node_Unref=PROCEDURE) /  symbol_vector=(FTC_SBitCache_Lookup=PROCEDURE) 5  symbol_vector=(FTC_SBitCache_LookupScaler=PROCEDURE) ,  symbol_vector=(FTC_SBitCache_New=PROCEDURE)+  symbol_vector=(FT_Activate_Size=PROCEDURE) 1  symbol_vector=(FT_Add_Default_Modules=PROCEDURE) (  symbol_vector=(FT_Add_Module=PROCEDURE)(  symbol_vector=(FT_Angle_Diff=PROCEDURE)#  symbol_vector=(FT_Atan2=PROCEDURE) )  symbol_vector=(FT_Attach_File=PROCEDURE) +  symbol_vector=(FT_Attach_Stream=PROCEDURE) ,  symbol_vector=(FT_Bitmap_Convert=PROCEDURE))  symbol_vector=(FT_Bitmap_Copy=PROCEDURE) )  symbol_vector=(FT_Bitmap_Done=PROCEDURE) -  symbol_vector=(FT_Bitmap_Embolden=PROCEDURE) (  symbol_vector=(FT_Bitmap_New=PROCEDURE)'  symbol_vector=(FT_CMap_Done=PROCEDURE) &  symbol_vector=(FT_CMap_New=PROCEDURE)%  symbol_vector=(FT_CeilFix=PROCEDURE) !  symbol_vector=(FT_Cos=PROCEDURE) $  symbol_vector=(FT_DivFix=PROCEDURE)'  symbol_vector=(FT_Done_Face=PROCEDURE) +  symbol_vector=(FT_Done_FreeType=PROCEDURE) (  symbol_vector=(FT_Done_Glyph=PROCEDURE),  symbol_vector=(FT_Done_GlyphSlot=PROCEDURE)*  symbol_vector=(FT_Done_Library=PROCEDURE))  symbol_vector=(FT_Done_Memory=PROCEDURE) '  symbol_vector=(FT_Done_Size=PROCEDURE) 6  symbol_vector=(FT_Face_GetCharVariantIndex=PROCEDURE):  symbol_vector=(FT_Face_GetCharVariantIsDefault=PROCEDURE)4  symbol_vector=(FT_Face_GetCharsOfVariant=PROCEDURE)6  symbol_vector=(FT_Face_GetVariantSelectors=PROCEDURE)4  symbol_vector=(FT_Face_GetVariantsOfChar=PROCEDURE)&  symbol_vector=(FT_FloorFix=PROCEDURE))  symbol_vector=(FT_Get_Advance=PROCEDURE) *  symbol_vector=(FT_Get_Advances=PROCEDURE)0  symbol_vector=(FT_Get_BDF_Charset_ID=PROCEDURE).  symbol_vector=(FT_Get_BDF_Property=PROCEDURE)-  symbol_vector=(FT_Get_CMap_Format=PROCEDURE) 2  symbol_vector=(FT_Get_CMap_Language_ID=PROCEDURE),  symbol_vector=(FT_Get_Char_Index=PROCEDURE)/  symbol_vector=(FT_Get_Charmap_Index=PROCEDURE) ,  symbol_vector=(FT_Get_First_Char=PROCEDURE)&  symbol_vector=(FT_Get_Gasp=PROCEDURE)'  symbol_vector=(FT_Get_Glyph=PROCEDURE) ,  symbol_vector=(FT_Get_Glyph_Name=PROCEDURE))  symbol_vector=(FT_Get_Kerning=PROCEDURE) (  symbol_vector=(FT_Get_MM_Var=PROCEDURE)(  symbol_vector=(FT_Get_Module=PROCEDURE)2  symbol_vector=(FT_Get_Module_Interface=PROCEDURE).  symbol_vector=(FT_Get_Multi_Master=PROCEDURE),  symbol_vector=(FT_Get_Name_Index=PROCEDURE)+  symbol_vector=(FT_Get_Next_Char=PROCEDURE) -  symbol_vector=(FT_Get_PFR_Advance=PROCEDURE) -  symbol_vector=(FT_Get_PFR_Kerning=PROCEDURE) -  symbol_vector=(FT_Get_PFR_Metrics=PROCEDURE) .  symbol_vector=(FT_Get_PS_Font_Info=PROCEDURE)1  symbol_vector=(FT_Get_PS_Font_Private=PROCEDURE) 1  symbol_vector=(FT_Get_Postscript_Name=PROCEDURE) *  symbol_vector=(FT_Get_Renderer=PROCEDURE)+  symbol_vector=(FT_Get_Sfnt_Name=PROCEDURE) 1  symbol_vector=(FT_Get_Sfnt_Name_Count=PROCEDURE) ,  symbol_vector=(FT_Get_Sfnt_Table=PROCEDURE)/  symbol_vector=(FT_Get_SubGlyph_Info=PROCEDURE) /  symbol_vector=(FT_Get_Track_Kerning=PROCEDURE) 6  symbol_vector=(FT_Get_TrueType_Engine_Type=PROCEDURE)/  symbol_vector=(FT_Get_WinFNT_Header=PROCEDURE) 1  symbol_vector=(FT_Get_X11_Font_Format=PROCEDURE) -  symbol_vector=(FT_GlyphLoader_Add=PROCEDURE) 5  symbol_vector=(FT_GlyphLoader_CheckPoints=PROCEDURE) 8  symbol_vector=(FT_GlyphLoader_CheckSubGlyphs=PROCEDURE)5  symbol_vector=(FT_GlyphLoader_CreateExtra=PROCEDURE) .  symbol_vector=(FT_GlyphLoader_Done=PROCEDURE)-  symbol_vector=(FT_GlyphLoader_New=PROCEDURE) 1  symbol_vector=(FT_GlyphLoader_Prepare=PROCEDURE) /  symbol_vector=(FT_GlyphLoader_Reset=PROCEDURE) 0  symbol_vector=(FT_GlyphLoader_Rewind=PROCEDURE)0  symbol_vector=(FT_GlyphSlot_Embolden=PROCEDURE)/  symbol_vector=(FT_GlyphSlot_Oblique=PROCEDURE) 2  symbol_vector=(FT_GlyphSlot_Own_Bitmap=PROCEDURE)(  symbol_vector=(FT_Glyph_Copy=PROCEDURE),  symbol_vector=(FT_Glyph_Get_CBox=PROCEDURE)*  symbol_vector=(FT_Glyph_Stroke=PROCEDURE)0  symbol_vector=(FT_Glyph_StrokeBorder=PROCEDURE)-  symbol_vector=(FT_Glyph_To_Bitmap=PROCEDURE) -  symbol_vector=(FT_Glyph_Transform=PROCEDURE) 0  symbol_vector=(FT_Has_PS_Glyph_Names=PROCEDURE)+  symbol_vector=(FT_Init_FreeType=PROCEDURE) 2  symbol_vector=(FT_Library_SetLcdFilter=PROCEDURE)9  symbol_vector=(FT_Library_SetLcdFilterWeights=PROCEDURE) -  symbol_vector=(FT_Library_Version=PROCEDURE) &  symbol_vector=(FT_List_Add=PROCEDURE)+  symbol_vector=(FT_List_Finalize=PROCEDURE) '  symbol_vector=(FT_List_Find=PROCEDURE) )  symbol_vector=(FT_List_Insert=PROCEDURE) *  symbol_vector=(FT_List_Iterate=PROCEDURE))  symbol_vector=(FT_List_Remove=PROCEDURE) %  symbol_vector=(FT_List_Up=PROCEDURE) '  symbol_vector=(FT_Load_Char=PROCEDURE) (  symbol_vector=(FT_Load_Glyph=PROCEDURE)-  symbol_vector=(FT_Load_Sfnt_Table=PROCEDURE) -  symbol_vector=(FT_Lookup_Renderer=PROCEDURE) (  symbol_vector=(FT_Match_Size=PROCEDURE)+  symbol_vector=(FT_Matrix_Invert=PROCEDURE) -  symbol_vector=(FT_Matrix_Multiply=PROCEDURE) 4  symbol_vector=(FT_Matrix_Multiply_Scaled=PROCEDURE)$  symbol_vector=(FT_MulDiv=PROCEDURE)-  symbol_vector=(FT_MulDiv_No_Round=PROCEDURE) $  symbol_vector=(FT_MulFix=PROCEDURE)&  symbol_vector=(FT_New_Face=PROCEDURE)+  symbol_vector=(FT_New_GlyphSlot=PROCEDURE) )  symbol_vector=(FT_New_Library=PROCEDURE) (  symbol_vector=(FT_New_Memory=PROCEDURE)-  symbol_vector=(FT_New_Memory_Face=PROCEDURE) &  symbol_vector=(FT_New_Size=PROCEDURE)'  symbol_vector=(FT_Open_Face=PROCEDURE) +  symbol_vector=(FT_Outline_Check=PROCEDURE) *  symbol_vector=(FT_Outline_Copy=PROCEDURE)/  symbol_vector=(FT_Outline_Decompose=PROCEDURE) *  symbol_vector=(FT_Outline_Done=PROCEDURE).  symbol_vector=(FT_Outline_Embolden=PROCEDURE)5  symbol_vector=(FT_Outline_GetInsideBorder=PROCEDURE) 6  symbol_vector=(FT_Outline_GetOutsideBorder=PROCEDURE).  symbol_vector=(FT_Outline_Get_BBox=PROCEDURE)0  symbol_vector=(FT_Outline_Get_Bitmap=PROCEDURE).  symbol_vector=(FT_Outline_Get_CBox=PROCEDURE)5  symbol_vector=(FT_Outline_Get_Orientation=PROCEDURE) )  symbol_vector=(FT_Outline_New=PROCEDURE) ,  symbol_vector=(FT_Outline_Render=PROCEDURE)-  symbol_vector=(FT_Outline_Reverse=PROCEDURE) /  symbol_vector=(FT_Outline_Transform=PROCEDURE) /  symbol_vector=(FT_Outline_Translate=PROCEDURE) 5  symbol_vector=(FT_Raccess_Get_DataOffsets=PROCEDURE) 4  symbol_vector=(FT_Raccess_Get_HeaderInfo=PROCEDURE)+  symbol_vector=(FT_Raccess_Guess=PROCEDURE) ,  symbol_vector=(FT_Reference_Face=PROCEDURE)/  symbol_vector=(FT_Reference_Library=PROCEDURE) +  symbol_vector=(FT_Remove_Module=PROCEDURE) *  symbol_vector=(FT_Render_Glyph=PROCEDURE)3  symbol_vector=(FT_Render_Glyph_Internal=PROCEDURE) -  symbol_vector=(FT_Request_Metrics=PROCEDURE) *  symbol_vector=(FT_Request_Size=PROCEDURE)&  symbol_vector=(FT_RoundFix=PROCEDURE),  symbol_vector=(FT_Select_Charmap=PROCEDURE),  symbol_vector=(FT_Select_Metrics=PROCEDURE))  symbol_vector=(FT_Select_Size=PROCEDURE) +  symbol_vector=(FT_Set_Char_Size=PROCEDURE) )  symbol_vector=(FT_Set_Charmap=PROCEDURE) ,  symbol_vector=(FT_Set_Debug_Hook=PROCEDURE)6  symbol_vector=(FT_Set_MM_Blend_Coordinates=PROCEDURE)7  symbol_vector=(FT_Set_MM_Design_Coordinates=PROCEDURE) -  symbol_vector=(FT_Set_Pixel_Sizes=PROCEDURE) *  symbol_vector=(FT_Set_Renderer=PROCEDURE)+  symbol_vector=(FT_Set_Transform=PROCEDURE) 7  symbol_vector=(FT_Set_Var_Blend_Coordinates=PROCEDURE) 8  symbol_vector=(FT_Set_Var_Design_Coordinates=PROCEDURE)-  symbol_vector=(FT_Sfnt_Table_Info=PROCEDURE) !  symbol_vector=(FT_Sin=PROCEDURE) *  symbol_vector=(FT_Stream_Close=PROCEDURE)/  symbol_vector=(FT_Stream_EnterFrame=PROCEDURE) .  symbol_vector=(FT_Stream_ExitFrame=PROCEDURE)1  symbol_vector=(FT_Stream_ExtractFrame=PROCEDURE) )  symbol_vector=(FT_Stream_Free=PROCEDURE) ,  symbol_vector=(FT_Stream_GetChar=PROCEDURE)-  symbol_vector=(FT_Stream_GetULong=PROCEDURE) /  symbol_vector=(FT_Stream_GetULongLE=PROCEDURE) /  symbol_vector=(FT_Stream_GetUOffset=PROCEDURE) .  symbol_vector=(FT_Stream_GetUShort=PROCEDURE)0  symbol_vector=(FT_Stream_GetUShortLE=PROCEDURE)(  symbol_vector=(FT_Stream_New=PROCEDURE))  symbol_vector=(FT_Stream_Open=PROCEDURE) -  symbol_vector=(FT_Stream_OpenGzip=PROCEDURE) ,  symbol_vector=(FT_Stream_OpenLZW=PROCEDURE)/  symbol_vector=(FT_Stream_OpenMemory=PROCEDURE) (  symbol_vector=(FT_Stream_Pos=PROCEDURE))  symbol_vector=(FT_Stream_Read=PROCEDURE) +  symbol_vector=(FT_Stream_ReadAt=PROCEDURE) -  symbol_vector=(FT_Stream_ReadChar=PROCEDURE) /  symbol_vector=(FT_Stream_ReadFields=PROCEDURE) .  symbol_vector=(FT_Stream_ReadULong=PROCEDURE)0  symbol_vector=(FT_Stream_ReadULongLE=PROCEDURE)0  symbol_vector=(FT_Stream_ReadUOffset=PROCEDURE)/  symbol_vector=(FT_Stream_ReadUShort=PROCEDURE) 1  symbol_vector=(FT_Stream_ReadUShortLE=PROCEDURE) 1  symbol_vector=(FT_Stream_ReleaseFrame=PROCEDURE) )  symbol_vector=(FT_Stream_Seek=PROCEDURE) )  symbol_vector=(FT_Stream_Skip=PROCEDURE) ,  symbol_vector=(FT_Stream_TryRead=PROCEDURE)2  symbol_vector=(FT_Stroker_BeginSubPath=PROCEDURE)-  symbol_vector=(FT_Stroker_ConicTo=PROCEDURE) -  symbol_vector=(FT_Stroker_CubicTo=PROCEDURE) *  symbol_vector=(FT_Stroker_Done=PROCEDURE)0  symbol_vector=(FT_Stroker_EndSubPath=PROCEDURE),  symbol_vector=(FT_Stroker_Export=PROCEDURE)2  symbol_vector=(FT_Stroker_ExportBorder=PROCEDURE)5  symbol_vector=(FT_Stroker_GetBorderCounts=PROCEDURE) /  symbol_vector=(FT_Stroker_GetCounts=PROCEDURE) ,  symbol_vector=(FT_Stroker_LineTo=PROCEDURE))  symbol_vector=(FT_Stroker_New=PROCEDURE) 2  symbol_vector=(FT_Stroker_ParseOutline=PROCEDURE),  symbol_vector=(FT_Stroker_Rewind=PROCEDURE))  symbol_vector=(FT_Stroker_Set=PROCEDURE) !  symbol_vector=(FT_Tan=PROCEDURE) -  symbol_vector=(FT_Trace_Get_Count=PROCEDURE) ,  symbol_vector=(FT_Trace_Get_Name=PROCEDURE)/  symbol_vector=(FT_Vector_From_Polar=PROCEDURE) +  symbol_vector=(FT_Vector_Length=PROCEDURE) -  symbol_vector=(FT_Vector_Polarize=PROCEDURE) +  symbol_vector=(FT_Vector_Rotate=PROCEDURE) .  symbol_vector=(FT_Vector_Transform=PROCEDURE)5  symbol_vector=(FT_Vector_Transform_Scaled=PROCEDURE) )  symbol_vector=(FT_Vector_Unit=PROCEDURE) )  symbol_vector=(TT_New_Context=PROCEDURE) $  symbol_vector=(TT_RunIns=PROCEDURE)&  symbol_vector=(afm_parser_funcs=DATA)*  symbol_vector=(autofit_module_class=DATA)&  symbol_vector=(bdf_driver_class=DATA)1  symbol_vector=(cff_cmap_encoding_class_rec=DATA) 0  symbol_vector=(cff_cmap_unicode_class_rec=DATA)&  symbol_vector=(cff_driver_class=DATA)+  symbol_vector=(ft_bitmap_glyph_class=DATA) ,  symbol_vector=(ft_corner_is_flat=PROCEDURE)0  symbol_vector=(ft_corner_orientation=PROCEDURE)(  symbol_vector=(ft_debug_init=PROCEDURE)4  symbol_vector=(ft_glyphslot_alloc_bitmap=PROCEDURE)3  symbol_vector=(ft_glyphslot_free_bitmap=PROCEDURE) 2  symbol_vector=(ft_glyphslot_set_bitmap=PROCEDURE)%  symbol_vector=(ft_grays_raster=DATA) +  symbol_vector=(ft_lzwstate_done=PROCEDURE) +  symbol_vector=(ft_lzwstate_init=PROCEDURE) )  symbol_vector=(ft_lzwstate_io=PROCEDURE) ,  symbol_vector=(ft_lzwstate_reset=PROCEDURE)'  symbol_vector=(ft_mem_alloc=PROCEDURE) %  symbol_vector=(ft_mem_dup=PROCEDURE) &  symbol_vector=(ft_mem_free=PROCEDURE)(  symbol_vector=(ft_mem_qalloc=PROCEDURE)*  symbol_vector=(ft_mem_qrealloc=PROCEDURE))  symbol_vector=(ft_mem_realloc=PROCEDURE) )  symbol_vector=(ft_mem_strcpyn=PROCEDURE) (  symbol_vector=(ft_mem_strdup=PROCEDURE)0  symbol_vector=(ft_module_get_service=PROCEDURE),  symbol_vector=(ft_outline_glyph_class=DATA)/  symbol_vector=(ft_raster1_renderer_class=DATA) 1  symbol_vector=(ft_service_list_lookup=PROCEDURE) 2  symbol_vector=(ft_smooth_lcd_renderer_class=DATA)3  symbol_vector=(ft_smooth_lcdv_renderer_class=DATA) .  symbol_vector=(ft_smooth_renderer_class=DATA)(  symbol_vector=(ft_standard_raster=DATA)9  symbol_vector=(ft_synthesize_vertical_metrics=PROCEDURE) -  symbol_vector=(ft_validator_error=PROCEDURE) ,  symbol_vector=(ft_validator_init=PROCEDURE)+  symbol_vector=(ft_validator_run=PROCEDURE) &  symbol_vector=(otv_module_class=DATA)&  symbol_vector=(pcf_driver_class=DATA)(  symbol_vector=(pfr_cmap_class_rec=DATA)&  symbol_vector=(pfr_driver_class=DATA))  symbol_vector=(ps_hints_apply=PROCEDURE) %  symbol_vector=(ps_parser_funcs=DATA) $  symbol_vector=(ps_table_funcs=DATA)(  symbol_vector=(psaux_module_class=DATA)+  symbol_vector=(pshinter_module_class=DATA) *  symbol_vector=(psnames_module_class=DATA)'  symbol_vector=(sfnt_module_class=DATA) &  symbol_vector=(t1_builder_funcs=DATA)%  symbol_vector=(t1_cmap_classes=DATA) .  symbol_vector=(t1_cmap_custom_class_rec=DATA).  symbol_vector=(t1_cmap_expert_class_rec=DATA)0  symbol_vector=(t1_cmap_standard_class_rec=DATA)/  symbol_vector=(t1_cmap_unicode_class_rec=DATA) &  symbol_vector=(t1_decoder_funcs=DATA)%  symbol_vector=(t1_driver_class=DATA) (  symbol_vector=(t1cid_driver_class=DATA)&  symbol_vector=(t42_driver_class=DATA)(  symbol_vector=(tt_cmap0_class_rec=DATA))  symbol_vector=(tt_cmap10_class_rec=DATA) )  symbol_vector=(tt_cmap12_class_rec=DATA) )  symbol_vector=(tt_cmap13_class_rec=DATA) )  symbol_vector=(tt_cmap14_class_rec=DATA) (  symbol_vector=(tt_cmap2_class_rec=DATA)(  symbol_vector=(tt_cmap4_class_rec=DATA)(  symbol_vector=(tt_cmap6_class_rec=DATA)(  symbol_vector=(tt_cmap8_class_rec=DATA)/  symbol_vector=(tt_default_graphics_state=DATA) %  symbol_vector=(tt_driver_class=DATA) )  symbol_vector=(winfnt_driver_class=DATA) 0  symbol_vector=(FT_Outline_EmboldenXY=PROCEDURE)*  symbol_vector=(FT_Property_Get=PROCEDURE)*  symbol_vector=(FT_Property_Set=PROCEDURE)-  symbol_vector=(FT_Get_Font_Format=PROCEDURE) )  symbol_vector=(FT_Done_MM_Var=PROCEDURE) 0  symbol_vector=(FT_Get_Var_Axis_Flags=PROCEDURE))  symbol_vector=(FT_Bitmap_Init=PROCEDURE) *  symbol_vector=(FT_Bitmap_Blend=PROCEDURE)3  symbol_vector=(FT_Get_Color_Glyph_Layer=PROCEDURE) 4  symbol_vector=(FT_Library_SetLcdGeometry=PROCEDURE).  symbol_vector=(FT_Palette_Data_Get=PROCEDURE),  symbol_vector=(FT_Palette_Select=PROCEDURE)'  symbol_vector=(FT_New_Glyph=PROCEDURE) 