2020-10-20  <PERSON>  <<EMAIL>>

	* Version 2.10.4 released.
	==========================


	Tag sources with `VER-2-10-4'.

	* docs/VERSION.TXT: Add entry for version 2.10.4.
	* docs/CHANGES: Updated.

	* README, src/base/ftver.rc, builds/windows/vc2010/index.html,
	builds/windows/visualc/index.html,
	builds/windows/visualce/index.html,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/index.html, docs/freetype-config.1:
	s/2.10.3/2.10.4/, s/2103/2104/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 4.

	* builds/unix/configure.raw (version_info): Set to 23:4:17.
	* CMakeLists.txt (VERSION_PATCH): Set to 4.

2020-10-19  <PERSON>  <<EMAIL>>

	[sfnt] Fix heap buffer overflow (#59308).

	This is CVE-2020-15999.

	* src/sfnt/pngshim.c (Load_SBit_Png): Test bitmap size earlier.

2020-10-17  Alexei Podtelezhnikov  <<EMAIL>>

	* src/sfnt/tt{colr,cpal}.c: Fix signedness warnings from VC++.

2020-10-17  Alexei Podtelezhnikov  <<EMAIL>>

	* src/sfnt/sfwoff2.c (Read255UShort): Tweak types to please VC++.

2020-10-10  Werner Lemberg  <<EMAIL>>

	* Version 2.10.3 released.
	==========================


	Tag sources with `VER-2-10-3'.

	* docs/VERSION.TXT: Add entry for version 2.10.3.

	* README, src/base/ftver.rc, builds/windows/vc2010/index.html,
	builds/windows/visualc/index.html,
	builds/windows/visualce/index.html,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/index.html, docs/freetype-config.1:
	s/2.10.2/2.10.3/, s/2102/2103/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 3.

	* builds/unix/configure.raw (version_info): Set to 23:3:17.
	* CMakeLists.txt (VERSION_PATCH): Set to 3.

2020-09-25  Werner Lemberg  <<EMAIL>>

	[autofit] Synchronize with ttfautohint.

	This corresponds to the following commits in the ttfautohint git
	repository:

	  bb6842bd3bd437b7b4a7921b0376c860f5e73d18  Typo, formatting.
	  d5c91ddb1cb310257a3dfe9a8e20e1fc51335faa  Add Medefaidrin script.

	* src/autofit/afblue.dat: Add blue zone data for Medefaidrin.
	* src/autofit/afblue.c, src/autofit/afblue.h: Regenerated.

	* src/autofit/afscript.h: Add Medefaidrin standard characters.

	* src/autofit/afranges.c, src/autofit/afstyles.h: Add Medefaidrin
	data.

2020-09-25  Werner Lemberg  <<EMAIL>>

	Move `scripts/make_distribution_archives.py` to `src/tools`.

	* scr/tools/scripts/make_distribution_archives.py: (_TOP_DIR,
	_SCRIPT_DIR): Updated to new location.
	(main): s/shutils.copyfile/shutils.copy/ to preserve file
	permissions.
	(main): Prefix source file paths with `git_dir` while copying files
	to allow calls of the script from other places than the top-level
	directory.

2020-09-24  Werner Lemberg  <<EMAIL>>

	* src/cff/cffgload.c (cff_slot_load): Scale `vertBearingY`.

	Towards the end of the the function there is a call to
	`FT_Outline_Get_CBox` that retrieves the glyph bbox in scaled units.
	That sets `horiBearing{X,Y}` and `vertBearingX` but `vertBearingY`
	is left alone, and is not scaled.

	Patch from Eric Muller <<EMAIL>>.

2020-09-24  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Load_Glyph): Trace glyph metrics.

2020-09-22  Werner Lemberg  <<EMAIL>>

	[meson] Move auxiliary scripts to `builds/meson`.

	Suggested by Alexei.

	* scripts/*.py: Move meson scripts to...
	* builds/meson/*.py: ... this new location.

	* meson.build: Updated.

2020-09-21  David Turner  <<EMAIL>>

	Add python script for building tarballs.

	* scripts/make_distribution_archives.py: New file.

	This standalone Python script should be equivalent to running `make
	dist` with the Make-based build system, with the following minor
	differences:

	- Since `make distclean` doesn't always clean up `objs/` properly,
	  `make dist` archives may contain some stale binaries like
	  `objs/.libs/libfreetype.so.6` or others.

	- `config.guess` and `config.sub` are not updated unless option
	  `--gnu-config-dir=DIR` is used to specify the location of these
	  files.

	- Some bits of the auto-generated reference documentation may
	  appear in slightly different order, probably due to issues related
	  to mkdocs and docwriter.

	As an example, the call

	  scripts/make_distribution_archives.py /tmp/freetype2-dist

	creates the following files under `/tmp/freetype2-dist`:

	  freetype-<version>.tar.gz
	  freetype-<version>.tar.xz
	  ft<winversion>.zip

2020-09-21  Werner Lemberg  <<EMAIL>>

	* scripts/extract_freetype_version.py: Fix regex typos.

2020-09-21  David Turner  <<EMAIL>>

	Add Meson build project file.

	Example usage:

	  # Configure Meson build in directory `build-meson` to generate
	  # release binaries comparable to to the ones from the
	  # autotools/make build system.
	  meson setup build-meson \
	        --prefix=/usr/local \
	        --buildtype=debugoptimized \
	        --strip \
	        -Db_ndebug=true

	  # After configuring the Meson build with the above command,
	  # compile and install to `/usr/local/`; this includes a pkg-config
	  # file.
	  ninja -C build-meson install

	  # Alternatively, compile and install to `/tmp/aa/usr/local/...`
	  # for packaging.
	  DESTDIR=/tmp/aa ninja -C build-meson install

	  # Generate documentation under `build-meson/docs`.
	  ninja -C build-meson docs

	Library size comparison for stripped `libfreetype.so` generated by
	all three build systems:

	  - Default build (autotools + libtool): 712 KiB
	  - CMake build (RelWithDebInfo):        712 KiB
	  - Meson build:                         712 KiB


	* meson.build: New top-level Meson build file for the library.

	* meson_options.txt: New file.  It holds user-selectable options for
	the build, which can be printed with `meson configure`, and selected
	at `meson setup` or `meson --reconfigure` time with
	`-D<option>=<value>`.

	* scripts/parse_modules_cfg.py: A script invoked by `meson.build` to
	parse `modules.cfg` and extract important information out of it
	(i.e., the list of modules).

	* scripts/process_ftoption_h.py: New script invoked by `meson.build`
	to process the original `ftoption.h` file.  It enables or disables
	configuration macro variables based on the available dependencies.
	This is similar to what other build systems are using (i.e., Meson's
	`configure_file()` command is not used here).

	* scripts/extract_freetype_version.py: New script invoked by
	`meson.build` to extract the FreeType version number from
	`<freetype/freetype.h>`.

	* scripts/extract_libtool_version.py: New script invoked by
	`meson.build` to extract the libtool `revision_info` data from
	`builds/unix/configure.raw`, and to generate the corresponding
	shared library suffix.

	* scripts/generate_reference_docs.py: New script invoked by
	`meson.build` to generate the FreeType 2 reference documentation
	(using the `docwriter` and `mkdocs` packages, which must be already
	installed).

2020-09-11  Alexei Podtelezhnikov  <<EMAIL>>

	[raster] Improve the second pass (#58373).

	Besides dropout control the second horizontal sweep is supposed to
	clean up straight horizontal edges that are mishandled by the first
	vertical sweep when a line passes through pixel centers.  This line
	would present as perfectly aligned span edges in the second sweep.

	* src/raster/ftraster.c (Horizontal_Sweep_Span): Replace the old
	implementation with a better one focusing on aligned span edges only.

2020-09-08  Alexei Podtelezhnikov  <<EMAIL>>

	[raster] Tune SMART macro (#58352).

	Windows seems to perform smart dropout control at 26.6 precision.
	To mimick Windows independent of increased precision, we need to tweak
	the macro so that some close calls break down rather than up.

	* src/raster/ftraster.c (SMART): Tweak the macro.

2020-09-08  Alexei Podtelezhnikov  <<EMAIL>>

	[raster] Introduce SMART macro.

	* src/raster/ftraster.c (SMART): New macro for smart dropout rounding.
	(Verstical_Sweep_Drop, Horizontal_Sweep_Drop): Use it.

2020-09-03  Boris Dalstein  <<EMAIL>>

	[build] Make CMake install basic version information.

	* CMakeLists.txt: Do it.

2020-09-02  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Reduce Infinality footprint (cont'd).

	* src/truetype/ttinterp.c (Ins_DELTAP): Shrink variable scope.
	(Ins_SHPIX, Ins_MIRP): Revise if-logic.

2020-09-02  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Reduce Infinality footprint.

	* src/truetype/ttinterp.c (Ins_SHPIX, Ins_MSIRP, Ins_MIAP, Ins_MDRP,
	Ins_MIRP): Shrink variable scopes and consolidate ifdefs.

2020-09-01  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Refactor compensation color.

	* src/truetype/ttinterp.h (TT_Round_Func): Change the last argument.
	* src/truetype/ttinterp.c (Ins_ROUND, Ins_NROUND, Ins_MDAP, Ins_MIAP,
	Ins_MDRP, Ins_MIRP): Move compensation retrieval from here...
	(Round_*): ... to here.
	* src/truetype/ttobjs.c (tt_size_init_bytecode): Reserve zero
	compensation at color index 3.

2020-08-28  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Don't set target in direct mode.

	* src/smooth/ftsmooth.c (ft_smooth_raster_overlap): Remove assignment.
	(ft_smooth_raster_lcd) [!FT_CONFIG_OPTION_SUBPIXEL_RENDERING]: Ditto.

2020-08-25  Alexei Podtelezhnikov  <<EMAIL>>

	* src/smooth/ftsmooth.c (ft_smooth_raster_overlap): Limit width.

	Segmentation fault reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=24729

2020-08-22  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Get_VMetrics): Add tracing message.

2020-08-05  Alexei Podtelezhnikov  <<EMAIL>>

	[truetype] Retain OVERLAP_SIMPLE and OVERLAP_COMPOUND.

	For glyphs with OVERLAP_SIMPLE or OVERLAP_COMPOUND, set
	FT_OUTLINE_OVERLAP to render them with direct oversampling, which
	mitigates artifacts (see 3bb512bc9f62).

	* include/freetype/ftimage.h (FT_OUTLINE_OVERLAP): Redefine to rhyme
	with OVERLAP_SIMPLE.
	* src/base/ftgloadr.c (FT_GlyphLoader_Rewind): Reset outline flags.
	* src/truetype/ttgload.c
	(TT_Load_Simple_Glyph): Retain OVERLAP_SIMPLE.
	(load_truetype_glyph): Retain OVERLAP_COMPOUND.

2020-08-04  Alexei Podtelezhnikov  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Load_Glyph): More tracing.

2020-07-28  Alexei Podtelezhnikov  <<EMAIL>>

	Hide internal functions with SunPro.

	* include/freetype/internal/compiler-macros.h
	(FT_INTERNAL_FUNCTION_ATTRIBUTE) <__SUNPRO_C>: Define as __hidden.

2020-07-28  Anuj Verma  <<EMAIL>>

	Fix static compilation with Visual C.

	* include/freetype/internal/compiler-macros.h
	(FT_INTERNAL_FUNCTION_ATTRIBUTE) <_WIN32>: Define as empty.

2020-07-28  Priyesh Kumar  <<EMAIL>>

	Fix `-Wformat' compiler warnings.

	* src/*: Fix format specifiers.

	* builds/unix/ftsystem.c (FT_Stream_Open): Ditto.

2020-07-25  Werner Lemberg  <<EMAIL>>

	Fix `-Wformat' compiler warnings.

	Problem reported by Priyesh kumar <<EMAIL>>

	* src/base/ftoutln.c (FT_Outline_Decompose): Fix number of arguments
	to tracing macro.

	* src/bdf/bdfdrivr.c (bdf_cmap_char_next, bdf_get_bdf_property):
	Ditto.

	* src/cache/ftcbasic.c (ftc_basic_family_get_count): Ditto.
	Reformulate message.

	* src/pcf/pcfdrivr.c (pcf_get_bdf_property): Ditto.

	* src/sfnt/sfwoff2.c (woff2_open_font): Ditto.
	Trace table offset, too.

	* src/truetype/ttgxvar.c (ft_var_apply_tuple): Ditto.

2020-07-23  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (woff2_decompress): Fix compiler warning.

	Reported by Hin-Tak.

2020-07-12  Werner Lemberg  <<EMAIL>>

	* builds/unix/configure.raw: Fix inclusion of `ftoption.h'.

2020-07-07  Werner Lemberg  <<EMAIL>>

	Fix clang warnings.

	* include/freetype/internal/autohint.h
	(FT_DECLARE_AUTOHINTER_INTERFACE): New macro.
	* src/autofit/afmodule.h: Use it to declare
	`af_autofitter_interface'.

	* include/freetype/internal/ftobjs.h (FT_DECLARE_GLYPH): New macro.
	* src/base/ftbase.h: Use it to declare `ft_bitmap_glyph_class' and
	`ft_outline_glyph_class'.

	* src/base/ftglyph.c: Include `ftbase.h'.

	* src/cff/cffparse.c (cff_parser_run): Fix type of `t2_size'.

	* src/pcf/pcfdrivr.c (pcf_cmap_char_next): Fix type of `result'.

	* src/psaux/psauxmod.c (psaux_module_class): Use `FT_DEFINE_MODULE'.
	* src/psaux/psauxmod.h: Declare `afm_parser_funcs',
	`t1_cmap_classes', `cff_decoder_funcs', and `psaux_module_class'.

	* src/pshinter/pshmod.c: Include `pshmod.h'.

	* src/sfnt/sfwoff2.c (ROUND4, WRITE_SHORT): Fix implicit sign
	conversion.
	(compute_ULong_sum): Fix return type.
	Fix implicit sign conversion.
	(store_points): Fix type of `last_flag', `repeat_count', and `flag'.
	Use casts to avoid warnings.
	(reconstruct_glyf): Fix implicit sign conversion.
	Use cast to avoid warning.
	(get_x_mins): Fix implicit sign conversion.
	* src/sfnt/ttcmap.c: Undef `TTCMAPCITEM'.
	* src/sfnt/ttcmap.h: Define `TTCMAPCITEM' and include `ttcmapc.h' to
	declare cmap classes.

	* src/smooth/ftsmooth.c (ft_smooth_overlap_spans): Use cast.

	* src/truetype/ttinterp.c (Ins_MIAP): Fix typo.

2020-07-07  David Turner  <<EMAIL>>

	[build] Really fix multi and C++ builds.

	The following builds were still failing due to previous changes:

	  make multi
	  make multi CC="c++"
	  make CC="c++"

	This patch fixes the issues, which were missing includes to get the
	right macro definitions in multi-build mode.

	Also, `FT_UNUSED' is actually used by third-party code, so move it
	back to `public-macros.h' to avoid breaking it.

	* include/freetype/config/public-macros.h (FT_EXPORT): Remove
	special definition for C++.
	(FT_UNUSED): Define here instead of...
	* include/freetype/config/compiler-macros.h: ... here.
	(FT_FUNCTION_DECLARATION): Remove special definition for C++.
	(FT_LOCAL_ARRAY_DEF): Fix definition.

	* src/cache/ftccback.h, src/lzw/ftzopen.h, src/gxvalid/gxvmort.h,
	src/gxvalid/gxvmorx.h: Add `FT_BEGIN_HEADER' and `FT_END_HEADER'.

2020-07-06  David Turner  <<EMAIL>>

	[build] Fix multi and C++ builds.

	The following builds were failing due to previous changes:

	  make multi
	  make multi CC="c++"

	* include/freetype/config/ftconfig.h: Remove `FT_END_HEADER'.

	* include/freetype/config/ftheader.h (FT_BEGIN_HEADER,
	FT_END_HEADER): Protect against redefinition.

	* src/cache/ftccache.h, src/cache/ftcmru.h, src/pcf/pcfutil.h,
	src/psaux/pserror.h, src/psaux/psft.h, src/psaux/psstack.h,
	src/sfnt/woff2tags.h: Include `compiler-macros.h'.

	* src/sfnt/woff2tags.c: Include `woff2tags.h'.

2020-07-06  Werner Lemberg  <<EMAIL>>

	[psaux] Improve `t1_decoder_parse_metrics' (#58646).

	* src/psaux/t1decode.c (t1_decoder_parse_metrics): Copy
	corresponding code from old engine's `t1_decoder_parse_charstrings'
	function to handle `op_callsubr' and `op_return'.

2020-07-05  David Turner  <<EMAIL>>

	[build] Improve visibility support of library function names.

	* include/freetype/config/public-macros.h
	(FT_PUBLIC_FUNCTION_ATTRIBUTE): New macro to tag functions as
	public (and thus exportable).
	(FT_EXPORT): Use it.

	* include/freetype/config/compiler-macros.h
	(FT_INTERNAL_FUNCTION_ATTRIBUTE): New macro to tag functions as
	internal to the library (and thus hidden).  Note that on ELF
	systems, all internal functions have hidden visibility, which avoids
	the need to enforce this when invoking the compiler (e.g., with an
	option like `-fvisibility=hidden').

	(FT_FUNCTION_DECLARATION, FT_FUNCTION_DEFINITION): New base macros
	to deal with C and C++ linkage issues at the same time.

	(FT_LOCAL, FT_LOCAL_DEF, FT_LOCAL_ARRAY, FT_LOCAL_ARRAY_DEF,
	FT_BASE, FT_BASE_DEF, FT_EXPORT_VAR, FT_BASE_CALLBACK,
	FT_BASE_CALLBACK_DEF): Redefined using new macros.

2020-07-05  David Turner  <<EMAIL>>

	[build] Split off more stuff from `ftconfig.h'.

	* builds/unix/ftconfig.h.in, builds/vms/ftconfig.h,
	include/freetype/config/ftconfig.h: Split off macro definitions
	required by the FreeType API headers to...
	* include/freetype/config/public-macros.h: ...this new file.

	* builds/unix/ftconfig.h.in, builds/vms/ftconfig.h,
	include/freetype/config/ftconfig.h: Split off macro definitions used
	by the library but not to be exposed to clients to...
	* include/freetype/config/compiler-macros.h: ...this new file.

	* include/freetype/internal/*.h, src/raster/ftraster.h: Include
	`compiler-macros.h' where needed.

2020-07-05  David Turner  <<EMAIL>>

	[build] Move mac support code to `mac-support.h'.

	* builds/unix/ftconfig.h.in, builds/vms/ftconfig.h,
	include/freetype/config/ftconfig.h: Split off mac-specific stuff
	to...
	* include/freetype/config/mac-support.h: ...this new file.

	* CMakeLists.txt, builds/unix/configure.raw: Remove `/undef ->
	#undef' string replacement; the affected code is no longer part of
	the `ftconfig.h' template.

2020-07-05  David Turner  <<EMAIL>>

	[build] Put integer type definitions into `integer-types.h'.

	Refactor some of the `ftconfig.h' headers and template to move the
	definition of the FreeType integer types (e.g., `FT_Int16') to a
	common header file `freetype/config/integer-types.h'.

	* builds/unix/ftconfig.h.in, builds/vms/ftconfig.h,
	include/freetype/config/ftconfig.h: Split off integer type
	definition stuff to...
	* include/freetype/config/integer-types.h: ...this new file.

	* builds/unix/ftconfig.h.in: Control the definition of
	`FT_SIZEOF_INT' and `FT_SIZEOF_LONG' with macro
	`FT_USE_AUTOCONF_SIZEOF_TYPES'.  If these are not defined, auto
	detection happens in `integer-types.h' as usual based on `INTXX_MAX'
	values.  Otherwise the autoconf-detected values are used.

	* builds/unix/configure.raw (CPPFLAGS): Don't include path to
	`config' directory.  Instead, ...
	(FT_CONFIG_STANDARD_LIBRARY_H): Use complete path.

2020-07-05  David Turner  <<EMAIL>>

	[build] Rename `build/unix/ftconfig.in' to `ftconfig.h.in'.

	Since we are no longer limited to 8.3 file names, it is simpler to
	follow the usual conventions for template files.

	* builds/unix/ftconfig.in: Renamed to...
	* builds/unix/ftconfig.h.in: ...this.

	* CMakeLists.txt, builds/unix/configure.raw: Updated.

2020-07-03  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Introduce direct oversampling for overlaps.

	This implements oversampling to mitigate artifacts in pixels partially
	covered by overlapping contours.  It turns out that the 4x4
	oversampling is sufficient but, at least, quadruples the rendering
	time.  The outline has to set FT_OUTLINE_OVERLAP to use this method.

	* include/freetype/ftimage.h (FT_OUTLINE_OVERLAP): New flag.
	* src/smooth/ftsmooth.c (ft_smooth_render): Check it to...
	(ft_smooth_raster_overlap): ... inflate outline and set up direct
	rendering for oversampling with...
	(ft_smooth_overlap_spans): ... new span function that integrates them.

2020-07-03  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Use direct rendering mode in Harmony.

	Instead of rendering 3 bitmaps side by side and reshuffling, we use
	direct rendering to deliver the bitmaps on each third byte.

	* src/smooth/ftsmooth.c (ft_smooth_raster_lcd)
	[!FT_CONFIG_OPTION_SUBPIXEL_RENDERING]: Set up direct mode with...
	(ft_smooth_lcd_spans): ... new span function.

2020-07-03  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Separate LCD paths from gray rendering.

	This makes `ft_smooth_render' a lot smaller and easier to follow. It
	also cleanly separates Harmony and ClearType-style LCD rendering
	algorithms. Now I only wish to move LCD filtering and geometry from
	FT_Library to FT_Renderer.

	* src/smooth/ftsmooth.c (ft_smooth_render): Move LCD code from here...
	(ft_smooth_raster_lcd, ft_smooth_raster_lcdv): ... to here.
	[FT_CONFIG_OPTION_SUBPIXEL_RENDERING]: Reorganize #ifdef's.

2020-06-20  Sebastian Rasmussen  <<EMAIL>>

	[cff] Fix handling of `style_name == NULL' (#58630).

	* src/cff/cffobjs.c (cff_face_init): If a call to `cff_strcpy' fails
	by returning NULL in `cff_face_init', `remove_style' is still
	called.  This means that the NULL pointer is dereferenced, causing a
	crash.

2020-06-19  Sebastian Rasmussen  <<EMAIL>>

	[cff] Fix another two memory leaks (#58629).

	* src/cff/cffobjs.c (cff_size_init): If a call to `funcs->create'
	fails to allocate one of the `internal->subfont' variables, make
	sure to free `internal->topfont' and any successfully allocated
	subfonts.

2020-06-19  Sebastian Rasmussen  <<EMAIL>>

	[psaux] Fix memory leak (#58626).

	* src/psaux/psstack.c (cf2_stack_init): If `cf2_stack_init' fails to
	allocate the stack, return error early.

2020-06-19  Sebastian Rasmussen  <<EMAIL>>

	[base] Fix memory leak (#58624).

	* src/base/ftobjs.c (FT_New_Size): Avoid trying to free
	`size->internal' unless `size' has been allocated.  This mistake
	appeared in the fix for issue #58611.

2020-06-19  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Rework d1180b5f9598 until further notice.

	* src/base/ftoutln.c (FT_Outline_Get_Orientation): Reject large
	outlines.

2020-06-19  Sebastian Rasmussen  <<EMAIL>>

	[cff, cid] Fix segfaults in case of error (#58621).

	* src/cff/cffobjs.c (cff_slot_done), src/cid/cidobjs.c
	(cid_slot_done): If `ft_glyphslot_init' fails to allocate
	`internal', then the class' `done_slot' callback (called by
	`ft_glyphslot_done') must not dereference the pointer to `internal'.

2020-06-19  Werner Lemberg  <<EMAIL>>

	[base] Fix UBSAN error.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=23166

	* src/base/ftoutln.c (FT_Outline_Get_Orientation): Avoid values
	larger than 32 bits.

2020-06-19  Werner Lemberg  <<EMAIL>>

	[woff2] Fix segfault.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=23402

	* src/sfnt/sfwoff2.c (get_x_mins): Check whether `loca' table
	exists.

2020-06-19  Stephen McDowell  <<EMAIL>>

	[sfnt] Support Intel compilers.

	* src/sfnt/pngshim.c (premultiply_data): Intel compilers do not
	currently support `__builtin_shuffle'.

2020-06-19  Sebastian Rasmussen  <<EMAIL>>

	[base] Fix memory leak (#58611).

	* src/base/ftobjs.c (FT_New_Size): When the call to `clazz->init_size'
	fails, make sure to free `size->internal'.

2020-06-19  Sebastian Rasmussen  <<EMAIL>>

	[cff] Fix memory leak (#58610).

	* src/cff/cffobjs.c (cff_size_init): When the call to
	`funcs->create' fails, make sure to free `internal'.

2020-06-19  Werner Lemberg  <<EMAIL>>

	* src/cff/cffload.c (cff_index_get_pointers): Rename `t' to `tbl'.

2020-06-19  Sebastian Rasmussen  <<EMAIL>>

	[cff] Free table upon error allocating other data (#58609).

	* src/cff/cffload.c (cff_index_get_pointers): When new_bytes fails
	to allocate, make sure to free the table.  Do the same for both
	allocations if there is a later error.

2020-06-13  Werner Lemberg  <<EMAIL>>

	Remove redundant inclusion of `ft2build.h'.

	* */*: Remove `#include <ft2build.h>' where possible.

	* include/freetype/freetype.h: Remove cpp error about missing
	inclusion of `ft2build.h'.

2020-06-08  David Turner  <<EMAIL>>

	Make macros for header file names optional.

	We no longer have to take care of the 8.3 file name limit; this
	allows us (a) to introduce longer, meaningful file names, and (b) to
	avoid macro names in `#include' lines altogether since some
	compilers (most notably Visual C++) doesn't support this properly.

	*/*: Replace

	   #include FOO_H

	with

	   #include <freetype/foo.h>

	or something similar.  Also update the documentation.

2020-06-02  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcmap.c (tt_face_build_cmaps): Trace number of cmaps.

2020-05-18  David Turner  <<EMAIL>>

	Remove obsolete HAVE_STDINT_H probing macro.

	This macro was updated by the unix configure script and the
	`CMakeLists.txt' one, but is never used in the source tree (nor is
	<stdint.h> included anywhere).

	* CMakeLists.txt, builds/unix/ftconfig.in: Don't handle
	`HAVE_STDINT_H'.

2020-05-18  David Turner  <<EMAIL>>

	Remove Jamfile files from the tree.

	These have not been used in a very, very long time, so better remove
	them.  A corresponding patch will be submitted to the
	`freetype2-demos' repository.

	* src/Jamfile, src/*/Jamfile, Jamrules: Delete.

2020-05-12  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Turn on LCD filtering during FreeType initialization.

	* src/smooth/ftsmooth.c (ft_smooth_init): Enable LCD filtering.

	* include/freetype/ftlcdfil.h: Document it, remove patent warnings.
	* include/freetype/freetype.h (FT_Render_Mode): Updated.
	* include/freetype/config/ftoption.h, devel/ftoption.h
	[FT_CONFIG_OPTION_SUBPIXEL_RENDERING]: Do not mention patents.

2020-05-11  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Stop using dedicated LCD modules and classes.

	The LCD modules were never truly independent. They mostly served as
	a way to disable patented LCD rendering, which is no longer necessary.
	The `smooth' module now handles LCD modes as well.

	* src/smooth/ftsmooth.c (ft_smooth_lcd_renderer_class.
	ft_smooth_lcdv_renderer_class): Deleted.
	(ft_render_smooth): Reworked from `ft_render_smooth_generic'.
	* src/smooth/ftsmooth.h: Remove dedicated LCD classes.
	* src/smooth/module.mk: Remove dedicated LCD modules.
	* include/freetype/config/ftmodule.h: Ditto.
	* builds/amiga/include/config/ftmodule.h: Ditto.
	* include/freetype/ftmodapi.h: Do not mention LCD modules.

2020-05-09  Werner Lemberg  <<EMAIL>>

	* Version 2.10.2 released.
	==========================


	Tag sources with `VER-2-10-2'.

	* docs/VERSION.TXT: Add entry for version 2.10.2.

	* README, Jamfile (RefDoc), src/base/ftver.rc,
	builds/windows/vc2010/index.html, builds/windows/visualc/index.html,
	builds/windows/visualce/index.html,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/index.html, docs/freetype-config.1:
	s/2.10.1/2.10.2/, s/2101/2102/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 2.

	* builds/unix/configure.raw (version_info): Set to 23:2:17.
	* CMakeLists.txt (VERSION_PATCH): Set to 2.

	* docs/CHANGES: Updated.

2020-05-08  Jakub Alba  <<EMAIL>>

	* src/truetype/ttinterp.c (TT_RunIns): Adjust loop counter (#58319).

	The font that exceeds the old limit is Icono Regular, version
	1.00000.

2020-05-03  Alexei Podtelezhnikov  <<EMAIL>>

	* builds/freetype.mk: Refactor for readability.

2020-05-02  Alexei Podtelezhnikov  <<EMAIL>>

	[builds] Clean up Windows CE project files.

	Remove version from filenames that caused a lot of polution in the
	release process. Use VERSIONINFO resource instead.

	* builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/windows/visualce/freetype.vcproj,
	builds/windows/visualce/freetype.dsp: s/2101//g, but add `ftver.rc'.
	* builds/wince/vc2008-ce/index.html,
	builds/wince/vc2005-ce/index.html,
	builds/windows/visualce/index.html: s/2101//g.

2020-05-01  Alexei Podtelezhnikov  <<EMAIL>>

	* devel/ft2build.h: Override FT_CONFIG_MODULES_H here as well.

2020-05-01  Alexei Podtelezhnikov  <<EMAIL>>

	[builds/unix] Consolidate marco overrides (for the demos to see them).

	* builds/unix/unix-cc.in (FT_CONFIG_MODULES_H, FT_CONFIG_OPTIONS_H):
	Override them here...
	* builds/freetype.mk: ... instead of here.

2020-04-08  Werner Lemberg  <<EMAIL>>

	Allow setting `CC' in Unix build (#58051).

	* builds/unix/unix-cc.in (CC): Use `override'.  The command line
	  value of `CC' (if any) is stored already in `CCraw'.

2020-04-04  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Return if single stream operation fails.

	* src/sfnt/sfwoff2.c (get_x_mins): Do it.

	* src/sfnt/woff2tags.c: Remove unused include.

2020-03-22  Nikhil Ramakrishnan  <<EMAIL>>

	[docs] Fix building docs if `srcdir' != `builddir'.

	`docs/reference/*' was moved one directory up in commit 237fed6.

	* builds/unix/unix-def.in (PIP): Remove variable.

	* configure: Create `docs' directory and copy assets from
	`docs/markdown'.

	* docs/README: Output directory is `reference'.

2020-03-21  Nikhil Ramakrishnan  <<EMAIL>>

	[docwriter] Drop support for Python < 3.5.

	Python versions < 3.5 have reached end-of-life and as such, no
	security or bug fixes will be provided for those versions.  See

	  https://devguide.python.org/#status-of-python-branches
	
	for more information.

	* Jamfile (RefDoc): Add `site' parameter.

	* builds/detect.mk (std_setup): Update Python version requirement.

	* builds/freetype.mk (refdoc-venv): Use pip as `python -m pip'.

	* builds/unix/ax_compare_version.m4,
	builds/unix/ax_prog_python_version.m4: Macros to detect Python
	version.  New files.

	* builds/unix/configure.raw: Check for Python >= 3.5 and remove
	check for `pip'.

	* docs/CHANGES, docs/INSTALL.GNU, docs/README: Updated.

2020-03-02  Moazin Khatti  <<EMAIL>>

	[gzip] Support `gzip' encoded header conditionally.

	In order to support `gzip' encoded header the call to
	`inflateInit2' was modified in commit 6a92b1fadde26477a9179.
	However, this code breaks with the outdated internal version
	of zlib.  This is a temporary fix to conditionally support
	`gzip' encoded header whenever a system installation of zlib
	is being used.

	Problem report in

	  https://lists.nongnu.org/archive/html/freetype-devel/2020-02/msg00023.html

	* src/gzip/ftgzip.c (FT_Gzip_Uncompress): Change the the call to
	`inflateInit2' depending on whether the system installation is
	being used or the internal copy.

2020-02-29  Ben Wagner  <<EMAIL>>

	[truetype] Fix state of `FT_Face' for buggy `gvar' tables (#57923).

	By resetting the blend as implemented with this commit fonts with
	invalid `gvar' tables may keep calling into `ft_var_load_gvar' from
	`tt_set_mm_blend' and failing, but the font was invalid anyway and
	we want to keep seeing the failure in `tt_set_mm_blend'.

	* src/truetype/ttgxvar.c (ft_var_load_gvar): Calculate length of
	offset array once.
	Allocate arrays after `FT_FRAME_ENTER' (extra check before
	allocating and avoid needing to free array later if error entering
	frame).
	Always call `FT_FRAME_EXIT'.
	Consistently set counts immediately after array initialized.
	Reset the blend (particularly `blend->glyphoffsets') on failure.

2020-03-01  Nikhil Ramakrishnan  <<EMAIL>>

	[docs] Update docwriter stylesheet.

	This change is required to support docwriter 1.2.1.

	See

	  https://github.com/freetype/docwriter/issues/36

	for more information.

	* docs/markdown/stylesheets/extra.css:
	(.md-typeset code) -> (.md-typeset pre>code)
	(pre) -> (pre>code)
	(p, .md-typeset p, h4): Remove commented styles.
	(table.index): Remove unused styles.

2020-02-28  Ben Wagner  <<EMAIL>>

	[truetype] Add better checks for loading `gvar' table (#57905).

	* src/truetype/ttgxvar.c (ft_var_load_gvar): Delay settings of any
	`blend->xxxcount' values until the corresponding data has been
	checked.
	Also do some sanitizing to avoid a too early exit.

	(TT_Vary_Apply_Glyph_Deltas): Improve tracing message.

2020-02-27  Werner Lemberg  <<EMAIL>>

	Make `FT_HAS_*' and `FT_IS_*' really return true (#57906).

	* include/freetype/freetype.h (FT_HAS_*, FT_IS_*): Implement it.

2020-02-25  Dominik Röttsches  <<EMAIL>>

	Fix for CFF space glyph regression (#57541).

	* src/psaux/psft.c (cf2_decoder_parse_substrings): Replace early-out
	  with FT_OFFSET.

2020-02-22  Werner Lemberg  <<EMAIL>>

	[woff2] Fix font table access.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=20778

	* src/sfnt/sfwoff2.c (get_x_mins): Explicitly check for presence of
	`head' table, which might not have been processed yet.

2020-02-21  Werner Lemberg  <<EMAIL>>

	[psaux] Make `t1_decoder_parse_metrics' handle `op_div' (#57519).

	* src/psaux/t1decode.c (t1_decoder_parse_metrics): Copy
	corresponding code from old engine's `t1_decoder_parse_charstrings'
	function.

2020-02-19  Nikolaus Waxweiler  <<EMAIL>>

	[autofit] Add support for Hanifi Rohingya script.

	* src/autofit/afblue.dat: Add blue zone data for Hanifi Rohingya.
	* src/autofit/afblue.c, src/autofit/afblue.h: Regenerated.

	* src/autofit/afscript.h: Add Hanifi Rohingya standard character.

	* src/autofit/afranges.c, src/autofit/afstyles.h: Add Hanifi
	  Rohingya data.

2020-02-19  Werner Lemberg  <<EMAIL>>

	Require HarfBuzz 1.8.

	* builds/unix/configure.raw, CMakeLists.txt: Request HarfBuzz 1.8.0
	or newer.

	We are going to add auto-hinter support for Hanifi Rohingya, which
	was introduced in Unicode 11.0.

2020-02-12  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcmap.c (tt_face_build_cmaps): Ignore version (#57708).

2020-02-04  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttinterp.c (TT_RunIns): Adjust loop counter (#57732).

	The font that exceeds the old limit is Constantine, version 1.001.

2020-01-04  Werner Lemberg  <<EMAIL>>

	[base] Fix `FREETYPE_PROPERTIES=type1:hinting-engine=adobe`.

	* src/base/ftpsprop.c (ps_property_set) [hinting-engine]: Avoid an
	incorrect return value that caused a warning.  The function did the
	right thing, though.

2020-01-03  Werner Lemberg  <<EMAIL>>

	[woff2] Fix memory leaks and a runtime warning.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=19773
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=18101

	* src/sfnt/sfwoff2.c (compute_ULong_sum): Add missing cast.
	(reconstruct_hmtx): Add missing deallocation calls.

2020-01-02  Dominik Röttsches  <<EMAIL>>

	[truetype] Fix UBSan warning on offset to nullptr (#57501).

	* src/truetype/ttinterp.c (Ins_CALL): Fail if `exc->FDefs' is null.

2019-12-31  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Allow bitmap-only fonts (#57394).

	* src/sfnt/sfwoff2.c (reconstruct_font): Fix test for `glyf' and
	`loca' tables.

2019-12-21  Hugh McMaster  <<EMAIL>>

	[docs] (2/2) Fix generation of API documentation (#56745).

	Creating the API Reference in the (new) `reference' sub-directory is
	consistent with other documentation sub-topics, such as `design',
	`glyphs' and `tutorial'.

	This patch fixes broken hyperlinks in the documentation pointing to
	and from the API Reference.  It also allows web assets to load from
	their relative paths.

	* builds/freetype.mk (DOC_DIR): Adjust.
	(refdoc, refdoc-venv): Add `--site' argument.

	* builds/toplevel.mk (do-dist): Updated.

2019-12-21  Hugh McMaster  <<EMAIL>>

	[docs] (1/2) Move static web assets (#56745).

	* docs/reference/*: Move ...
	* docs: ... one directory up.

2019-12-21  Dominik Röttsches  <<EMAIL>>

	Fix more UBSan warnings on adding offset to nullptr (#57432).

	* src/truetype/ttinterp.c (Ins_LOOPCALL), src/psaux/psft.c
	(cf2_initLocalRegionBuffer): Use `FT_OFFSET'.

2019-12-16  Werner Lemberg  <<EMAIL>>

	[truetype] Fix UBSan warnings on adding offsets to nullptr.

	Reported as

	  https://bugs.chromium.org/p/chromium/issues/detail?id=1032152

	* src/truetype/ttinterp.c (Ins_FDEF, Ins_IDEF): Use `FT_OFFSET'.

2019-12-14  Werner Lemberg  <<EMAIL>>

	[truetype] Fix integer overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=19305

	* src/truetype/ttinterp.c (Ins_MIRP): Use `ADD_LONG'.

2019-12-13  Werner Lemberg  <<EMAIL>>

	Another bunch of UBSan warnings on adding offsets to nullptr.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=19427
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=19433
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=19441
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=19451
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=19452
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=19457

	* src/autofit/aflatin.c (af_latin_hints_compute_segments,
	af_latin_hints_compute_edges): Use `FT_OFFSET'.

	* src/base/ftstream.c (FT_Stream_EnterFrame): Use `FT_OFFSET'.

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings): Exit early
	if there is no charstring.

	* src/psaux/psobjs.c (t1_decrypt): Use `FT_OFFSET'.

	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Exit early for
	zero bitmap dimensions.

2019-12-09  Dominik Röttsches  <<EMAIL>>

	Fix more UBSan warnings on adding offset to nullptr (#57384).

	* src/smooth/ftsmooth.c (ft_smooth_render_generic),
	src/psaux/psobjs.c (ps_table_add): Use `FT_OFFSET'.

2019-12-05  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttinterp.c (TT_RunIns): Use `FT_OFFSET'.

	Reported as

	  https://bugs.chromium.org/p/chromium/issues/detail?id=1030614

2019-12-03  Werner Lemberg  <<EMAIL>>

	More nullptr offset UBSan warnings (#57331, #57347).

	* src/autofit/afcjk.c (af_cjk_hints_compute_segments),
	src/psaux/psft.c (cf2_getSeacComponent), src/truetype/ttinterp.c
	(Ins_UNKNOWN): Use `FT_OFFSET'.

2019-11-29  Dominik Röttsches  <<EMAIL>>

	Avoid more nullptr offset UBSan warnings (#57316).

	* src/base/ftoutln.c (FT_Outline_Transform): Bail on empty points.
	* src/cff/cffload.c (cff_subfont_load): Use `FT_OFFSET'.
	* src/psaux/psft.c (cf2_decoder_parse_substrings): Early out if
	`charstring_base' or `charstring_len' are null.
	* src/sfnt/ttload.c (tt_face_load_name): Use `FT_OFFSET'.

2019-11-23  John Stracke  <<EMAIL>>

	[base] Really fix #57194.

	Apply accidentally missed second part of patch.

	* src/base/ftgloadr.c (FT_GlyphLoader_CheckPoints): Call
	`FT_GlyphLoader_CreateExtra'.

2019-11-23  Werner Lemberg  <<EMAIL>>

	[truetype] Avoid sanitizer warning (#57289).

	* src/truetype/ttpload.c (tt_face_get_device_metrics): Use
	`FT_OFFSET'.

2019-11-23  Armin Hasitzka  <<EMAIL>>

	[truetype] Fix integer overflow (#57287).

	* src/truetype/ttgload.c (compute_glyph_metrics): Use `SUB_LONG'.

2019-11-23  Ben Wagner  <<EMAIL>>

	[sfnt] Avoid sanitizer warning (#57286).

	* src/sfnt/ttcmap.c (tt_face_build_cmaps): Avoid possible `NULL +
	offset' computation.
	Tag `table' as `const'.

2019-11-23  John Stracke  <<EMAIL>>
            Werner Lemberg  <<EMAIL>>

	[base] Fix `NULL + offset' sanitizer warnings (#57194).

	* src/base/ftgloadr.c (FT_GlyphLoader_Adjust_Points,
	FT_GlyphLoader_Adjust_Subglyphs): Use `FT_OFFSET'.
	(FT_GlyphLoader_CreateExtra): Add short cut if some values are zero.

2019-11-23  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftmemory.h (FT_OFFSET): New macro.

	Use this for `base + offset' pointer calculations where `base' can
	be NULL (triggering a sanitizer warning even if the resulting
	pointer gets never dereferenced since it is undefined behaviour
	in C).

	Suggested by Ben Wagner.

2019-11-23  Ben Wagner  <<EMAIL>>

	[sfnt] Ensure OTTO fonts have tables (#57285).

	* src/sfnt/ttload.c (tt_face_load_font_dir): Add test.

2019-11-23  Behdad Esfahbod  <<EMAIL>>

	Minor fixes for recent compilers.

	* src/gzip/infutil.h (inflate_mask): Add `const'.

	* src/autofit/aflatin2.c: Include `ft2build.h'.

2019-11-07  Nikolaus Waxweiler  <<EMAIL>>

	* CMakeLists.txt: Minor additions to the notes, compile
	  builds/unix/ftsystem.c instead of src/base/ftsystem.c on UNIX.

	The latter change is based on the code proposed by rim in #55235.

2019-10-25  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (woff2_open_font): Check `num_fonts' for TTCs.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=18494

2019-10-22  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (woff2_open_font): Avoid undefined shift.

	Also improve tracing.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=18390

2019-10-10  Alexei Podtelezhnikov  <<EMAIL>>

	* src/sfnt/pngshim.c (premultiply_data): Optimize for __SSE__ only.

2019-10-10  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (reconstruct_glyf): Check `triplet_size'.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=18108

2019-10-09  John Tytgat  <<EMAIL>>

	[cff] Fix FT_FACE_FLAG_GLYPH_NAMES for CFF2 based fonts (#57023).

	* src/cff/cffobjs.c (cff_face_init): Don't set
	FT_FACE_FLAG_GLYPH_NAMES for CFF2 based fonts.

2019-10-08  Werner Lemberg  <<EMAIL>>

	[woff2] Fix SFNT table checks.

	Also reduce number of SFNT table lookups.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=18065

	* include/freetype/internal/wofftypes.h (WOFF2_InfoRec): Add fields
	`glyf_table', `loca_table', and `head_table'.

	* src/sfnt/sfwoff2.c (reconstruct_glyf): Update signature.
	Use table pointers in `info' parameter.
	(get_x_mins): Check `maxp_table'
	Use table pointers in `info' parameter.
	(reconstruct_font):  Use and set table pointers in `info' parameter.
	Fix check for `glyf' and `loca' tables.
	Update call to `reconstruct_glyf'.
	(woff2_open_font): Updated.

2019-10-06  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (reconstruct_glyf): Fix reallocation.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=18046

2019-10-06  Werner Lemberg  <<EMAIL>>

	Improve memory debugging.

	* include/freetype/internal/ftmemory.h (FT_MEM_FREE): Use
	`FT_DEBUG_INNER' to set source code file name and line.

	* src/base/ftdbgmem.c (ft_mem_table_remove): Better formatting of
	tracing message.

2019-10-03  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2 (reconstruct_font): Fix reallocation.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=17966

2019-10-01  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftstroke.c (ft_stroker_inside): Speed up.

2019-10-01  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2 (woff2_open_font): Initialize `woff2.ttc_fonts'.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=17804

2019-09-30  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (reconstruct_font): Fix memory leak.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=17812

2019-09-30  Werner Lemberg  <<EMAIL>>

	[woff2] Reject fonts without `head' table.

	Also fix memory deallocation in case of error.

	`head' problem reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=17820

	* src/sfnt/sfwoff2.c (reconstruct_glyf): Don't use `stream_close'.
	Abort if `head_table' is NULL.
	Don't free `transformed_buf' in case of error.
	(woff2_open_font): Don't set `uncompressed_buf' to NULL.

2019-09-29  Werner Lemberg  <<EMAIL>>

	[woff2] Fix compiler warnings.

	Problem reported by Alexei.

	* src/sfnt/sfwoff2.c (reconstruct_glyf): Initialize `x_min'.
	(reconstruct_font): Initialize `num_hmetrics'.
	(woff2_open_font): Initialize `info'.

2019-09-28  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (woff2_open_font): Fix sanity check.

	Correct thinkos in patch from 2019-09-01.

2019-09-28  Werner Lemberg  <<EMAIL>>

	[woff2] Fix memory leaks.

	One of them reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=17766

	* src/sfnt/sfwoff2.c (woff2_open_font): Free `info->x_mins' and
	`woff2->ttc_fonts'.

	(reconstruct_glyf): Initialize `info->x_mins'.

2019-09-27  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftstroke.c (ft_stroker_cap): Speed up caps.

2019-09-25  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftstroke.c (ft_stroker_outside): Speed up clipped miter.
	* include/freetype/ftstroke.h: Wordsmith miter docs.

2019-09-25  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (woff2_open_font): Check (sum of) table sizes.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=17684

2019-09-23  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftstroke.c (ft_stroke_border_arcto): Speed up calculations.

2019-09-20  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Fix memory leaks.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16896

	* src/sfnt/sfwoff2.c (woff2_open_font): Fix error handling.
	Free `uncompressed_buf'.
	(reconstruct_font): Free `transformed_buf'.

2019-09-17  Werner Lemberg  <<EMAIL>>

	* src/otvalid/otvcommon.c (otv_Coverage_get_last): Guard `count'.

	Problem reported by Marc Schönefeld <<EMAIL>>.

2019-09-17  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (woff2_open_font): Check table index.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=17100

2019-09-15  Avi Halachmi (:avih)  <<EMAIL>>

	[cmake] Don't fail if brotli is missing (#56894).

	The libs which cmake controls are commented out at

	  include/freetype/config/ftoption.h

	and cmake un-comment each enabled library, but the brotli option was
	not commented out, therefore `FT_CONFIG_OPTION_USE_BROTLI' remained
	defined even if brotli was missing/disabled/etc.

	Comment it such that cmake can control it, which means leaving it
	undefined if brotli is missing.

	* include/freetype/config/ftoption.h: Fix typo.

2019-09-05  Werner Lemberg  <<EMAIL>>

	[cmake] Add brotli support.

	* CMakeLists.txt (FT_WITH_BROTLI): New option.

	* builds/cmake/FindBrotliDec.cmake: New file.

2019-09-05  Werner Lemberg  <<EMAIL>>

	Fix handling of `AF_CONFIG_OPTION_INDIC'.

	* devel/ftoption.h, include/freetype/config/ftoption.h:
	`AF_CONFIG_OPTION_INDIC' needs `AF_CONFIG_OPTION_CJK'.

2019-09-05  Werner Lemberg  <<EMAIL>>

	CMakeLists.txt: Fix generation of DLL related stuff (#56852).

	Extract `version_info' variable from `builds/unix/configure.raw' and
	use the data to correctly set `LIBRARY_VERSION' and
	`LIBRARY_SOVERSION'.

	Also use the data to set `ft_version' field in `freetype2.pc'.
	Also fix the needed minimum version of HarfBuzz in `freetype2.pc'.

2019-09-03  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (compute_ULong_sum): Fix undefined shift.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16933

2019-09-01  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfwoff2.c (woff2_open_font): Add sanity check.

	Don't trust `totalSfntSize' unconditionally.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16893

2019-08-27  Dominik Röttsches  <<EMAIL>>

	[woff2] Don't use `FT_UInt64' (#56815).

	* src/sfnt/sfwoff2.c (woff2_open_font): Use `FT_UInt32' for
	`file_offset'.  This fixes builds on platforms where `FT_LONG64' is
	not defined while still being sufficient to store a file offset.

2019-08-27  Werner Lemberg  <<EMAIL>>

	[truetype] Prevent crash in `TT_Set_Named_Instance' (#56813).

	* src/truetype/ttgxvar.c (TT_Set_Named_Instance): Fix error
	handling.

2019-08-27  Werner Lemberg  <<EMAIL>>

	[woff2] Fix compiler warnings.

	* src/sfnt/sfwoff2.c (read_num_hmetrics): Remove unused argument
	`table_len'.
	Update caller.
	(triplet_decode, compute_bbox, store_loca, reconstruct_glyf): Make
	`i' variable unsigned.
	(reconstruct_glyph): Remove condition which is always false.
	(reconstruct_html): Removed unused argument `transformed_size'.
	Update caller.

	* src/sfnt/woff2tags.c (woff2_known_tags): Remove condition which is
	always false.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Check whether known tag is in array bounds.

	If table tag is not 0x3f, we expect a value between 0 and 62.  If
	this is not the case, exit with errors.

	* src/sfnt/sfwoff2/c: Check whether table tag makes sense.

	* src/sfnt/woff2tags.c: Return 0 if tag is out of bounds.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	* src/sfnt/sfwoff2.c: Improve trace comments.

	Adjust tracing levels for comments, and more formatting.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Support `hmtx' reconstruction when `glyf' is untransformed.

	`reconstruct_hmtx' requires `info->x_mins' and `info->num_glyphs' to
	reconstruct the hmtx table.  In case glyf is not transformed, we
	call `get_x_mins' which does the necessary work.

	* src/sfnt/sfwoff2.c (get_x_mins): New function.
	(reconstruct_font): Call get_x_mins.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[sfnt] Support `face->num_faces' for WOFF2 fonts.

	Set correct value of `face->num_faces' for WOFF2 fonts.  This is
	being handled separately because we only load the tables for the
	requested font face in `woff2_open_font' and create a single-face
	sfnt stream.

	The full discussion is at:

	  https://lists.gnu.org/archive/html/freetype-devel/2019-08/msg00000.html

	* src/sfnt/sfobjs.c (sfnt_open_font): Add parameter
	`woff2_num_faces'.
	(sfnt_init_face): Introduce variable `woff2_num_faces', and change
	`face->root.num_faces' if `woff2_num_faces' is set.

	* src/sfnt/sfwoff2.c (woff2_open_font): Validate requested face
	index and handle negative face indices.

	* src/sfnt/sfwoff2.h (woff2_open_font): Add parameter `num_faces' to
	declaration.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Improve memory and error handling.

	Free up memory after use, and improve error handling.

	* src/sfnt/sfwoff2.c (reconstruct_font, woff2_open_font): Implement
	changes.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Avoid too many calls to `FT_REALLOC'.

	We do this by using `totalSfntSize' as an initial reference, and
	extending the buffer when required.  This reduces rendering time
	considerably.

	* include/freetype/internal/wofftypes.h (WOFF2_HeaderRec): Add
	`totalSfntSize', rename `total_sfnt_size' to `actual_sfnt_size'.

	* src/sfnt/sfwoff2.c (write_buf): Add parameter `dst_size' to keep
	track of and update total size of stream.

	(WRITE_SFNT_BUF, WRITE_SFNT_BUF_AT): Modify macros accordingly.

	(pad4, store_loca, reconstruct_glyf, reconstruct_hmtx,
	reconstruct_font): Update parameters to accept `sfnt_size'.

	(woff2_open_font): Add variable `sfnt_size'.  Use WOFF2 header field
	`totalSfntSize' as initial reference (if value makes sense) and
	allocate `totalSfntSize' bytes for the sfnt stream.  `write_buf'
	handles reallocation if and when required.  Also resize the stream
	to `actual_sfnt_size' after reconstruction.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Reconstruct `loca', `hmtx', and swap out stream.

	Add necessary functions to reconstruct loca and hmtx tables (the two
	remaining tables that can have a transform).  `woff2_open_font' is
	now capable of loading a woff2 font face.  This code may still need
	more refining and better memory management.

	* include/freetype/internal/wofftypes.h (WOFF2_HeaderRec): Add total
	(final) size of sfnt stream.

	(WOFF2_InfoRec): Add header checksum value.

	* src/sfnt/sfobjs.c (sfnt_open_font): Change `face_instance_index'
	parameter to its pointer so its value can be modified by
	`woff2_open_font'.

	* src/sfnt/sfwoff2.c: (WRITE_SFNT_BUF_AT): New macro to write into
	sfnt buffer at given position.

	(write_buf): Add parameter `extend_buf' which allows caller to
	specify whether buffer should be reallocated before copying data.

	(WRITE_SFNT_BUF): Updated.

	(pad4, store_loca, reconstruct_htmx): New functions.

	(reconstruct_glyf): Calculate loca values and store them.

	(reconstruct_font): Call `reconstruct_hmtx', write table record
	entries, and calculate table checksums.  Also calculate font
	checksum and update `checksumAdjustment' entry in head table.

	(woff2_open_font): Open stream for sfnt buffer, swap out input
	stream and return.

	* src/sfnt/sfwoff2.h (woff2_open_font): Modify parameter to accept
	pointer to `face_index'.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Reconstruct transformed `glyf' table.

	Reconstruct `glyf' table if it is transformed in the uncompressed
	table stream.  Also add necessary structures, macros and functions.

	* include/freetype/internal/wofftypes.h (WOFF2_InfoRec,
	WOFF2_SubstreamRec, WOFF2_PointRec): New structures.
	(WOFF2_TableRec): s/OrigLength/dst_length/.

	* src/sfnt/sfwoff2.c (READ_255USHORT, READ_BASE128): Use
	`FT_SET_ERROR' to set implicit `error' variable.

	(WRITE_SHORT): New macro.

	(N_CONTOUR_STREAM, N_POINTS_STREAM, FLAG_STREAM, GLYPH_STREAM,
	COMPOSITE_STREAM, BBOX_STREAM, INSTRUCTION_STREAM): New macros to
	refer to substreams of the transformed `glyf' tables.

	(Read255UShort, ReadBase128): Return errors set by `FT_READ_XXX'
	macros.

	(with_sign, safe_int_addition): New functions to add sign to values
	based on a flag and perform safe addition respectively.

	(triplet_decode): Decode variable-length (flag, xCoordinate,
	yCoordinate) triplet for a simple glyph.   See

	  https://www.w3.org/TR/WOFF2/#triplet_decoding

	(store_points, compute_bbox, composteGlyph_size, reconstruct_glyf):
	New functions.

	(reconstruct_font): Call `reconstruct_glyf'.

	* src/sfnt/sfwoff2.h: Add required constants.

	* src/sfnt/woff2tags.h: Move out constants to `sfwoff2.h'.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Copy un-transformed tables to sfnt stream.

	Copy un-transformed tables to the sfnt stream.

	* src/sfnt/sfwoff2.c: (WRITE_SFNT_BUF): New macro.
	(write_buf): New function.  Extend memory of `dst' buffer and copy
	bytes from `src'.
	(compute_ULong_sum): New function.  Calculate checksum of table.
	(reconstruct_font): Change `FT_Byte* sfnt' to `FT_Byte**
	sfnt_bytes'.  This has been done because we reallocate memory to
	`sfnt' multiple times, which may change the pointer value of `sfnt'.
	This new pointer must be propogated back to the caller.  Same reason
	for using a double pointer in `write_buf'.

	* src/sfnt/woff2tags.h (WOFF2_DEFAULT_MAX_SIZE): New macro used for
	overflow checking.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Create stream for uncompressed buffer.

	Uncompressed buffer is now an `FT_Stream'.

	Perform basic checks and start iterating over tables.

	* src/sfnt/sfwoff2.c (stream_close, find_table, read_num_hmetrics):
	New functions.
	(reconstruct_font): Modify parameters and iterate over tables.
	(woff2_open_font): Updated.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Handle TTCs and start reconstructing font.

	We `handle' TTCs by modifying the `indices' array to point to only
	those tables that are part of the requested `face_index'.

	Set and use `num_tables' in `WOFF2_TtcFont'.

	* src/sfnt/sfwoff2.c (reconstruct_font): New function.
	(woff2_open_font): Start reconstruction of font.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Get known tags from function.

	Change `KnownTags' to a function (`woff2_known_tags').  This avoids
	introducing a global constant array.  This function returns the
	specified index without *any* checks.  The caller must ensure that
	`index' is within array limits.

	* src/sfnt/sfwoff2.c (woff2_open_font): Change `KnownTags[...]'
	notation to `woff2_known_tags( ...  )'.

	* src/sfnt/woff2tags.c: Perform changes.

	* src/sfnt/woff2tags.h: Update definitions.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Minor.

	* src/sfnt/sfwoff2.c (woff2_uncompress): Add error message
	(woff2_open_font): Free `uncompressed_buf'.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Uncompress Brotli streams and `face_index' support.

	WOFF2 compressed stream is now uncompressed if Brotli is available.
	This data is stored in a separate buffer (uncompressed_buf) because
	it does not contain direct table data.  Certain tables have
	transformations applied to them, and they must be reconstructed
	before we can write those tables to the SFNT stream.

	`face_index' is now being passed as a parameter to
	`woff2_open_font'.

	* src/sfnt/sfobjs.c (sfnt_open_font): Add parameter
	`face_instance_index'.

	* src/sfnt/sfwoff2.c (woff2_uncompress): New function.
	(woff2_open_font): Call `woff2_uncompress'.
	(compute_first_table_offset): Fix return type.

	* src/sfnt/sfwoff2.h (woff2_open_font): Modify declaration.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	* builds/unix/configure.raw: Change argument name to `brotli'.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	Add Brotli dependency and required checks.

	Brotli is required for decompressing WOFF2 font directory streams.
	The library is thus being added as an optional dependency for
	FreeType.

	* builds/unix/configure.raw: Add checks for `libbrotlidec'.
	(REQUIRES_PRIVATE, LIBS_PRIVATE, LIBSSTATIC_CONFIG): Updated.

	* devel/ftoption.h, include/freetype/config/ftoption.h
	(FT_CONFIG_OPTION_USE_BROTLI): New macro.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Write SFNT Offset table.

	* src/sfnt/sfwoff2.c (WRITE_USHORT, WRITE_ULONG): New macros.
	(compare_tags): New function.
	(woff2_open_font): Implement it.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	* src/sfnt/sfwoff2.c: #undef macros.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[woff2] Read table and collection directory.

	* include/freetype/internal/wofftypes.h (WOFF2_TtcFontRec): New
	structure.
	(WOFF2_HeaderRec): Add more fields.

	* src/sfnt/sfwoff2.c (READ_255USHORT, READ_BASE128, ROUND4): New
	macros.
	(Read255UShort, CollectionHeaderSize, compute_first_table_offset):
	New functions.
	(ReadBase128): Use `FT_READ_BYTE'.
	(woff2_open_font): Add functionality to read table directory and
	collection directory (if present).

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[sfnt] Include `woff2tags.c' for building.

	* src/sfnt/rules.mk (SFNT_DRV_SRC): Add `woff2tags.c'.

	* src/sfnt/sfnt.c: Include `woff2tags.c'.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[sfnt] Add WOFF2 constants.

	Add constants required for WOFF2, and known table tags as defined in
	the specification.  See

	  https://www.w3.org/TR/WOFF2/#table_dir_format

	for details.

	* src/sfnt/woff2tags.c, src/sfnt/woff2tags.h: New files.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	[sfnt] Read WOFF 2 header.

	Check for WOFF2 tag, call `woff2_open_font', and implement it to read
	header according to specification.

	* include/freetype/internal/fttrace.h: Add `sfwoff2.c'.

	* src/sfnt/rules.mk (SFNT_DRV_SRC): Add `sfwoff2.c'.

	* src/sfnt/sfnt.c: Include `sfwoff2.c'.

	* src/sfnt/sfobjs.c (sfnt_open_font): Check for `wOF2' tag and call
	`woff2_open_font'.

	* src/sfnt/sfwoff2.c, src/sfnt/sfwoff2.h: New files.

2019-08-27  Nikhil Ramakrishnan  <<EMAIL>>

	Add structures for WOFF2.

	Add structures and macro for WOFF 2 header and table directory.

	* include/freetype/internal/wofftypes.h (WOFF2_HeaderRec,
	WOFF2_TableRec_): New structures.

	* include/freetype/tttags.h (TTAG_wOF2): New macro.

2019-08-26  Werner Lemberg  <<EMAIL>>

	* src/psaux/cffdecode.c (cff_operator_seac): Fix numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=16470

2019-08-26  Werner Lemberg  <<EMAIL>>

	[type1] Fix `FT_Get_Var_Axis_Flags' (#56804).

	* src/type1/t1load.c (T1_Get_MM_Var): Allocate space for axis flags.
	Also remove redundant assignment.

2019-07-24  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftbbox.c (cubic_peak): Sanitize left shift (#56586).

2019-07-22  Weiyi Wu  <<EMAIL>>

	* src/cid/cidload.c (cid_hex_to_binary): Fix typo (#56653).

2019-07-12  Werner Lemberg  <<EMAIL>>

	[sfnt, winfonts] Avoid memory leaks in case of error (#56587).

	* src/sfnt/sfwoff.c (woff_open_font): Call `FT_FRAME_EXIT' in case
	of error.

	* src/winfonts/winfnt.c (fnt_face_get_dll_font): Ditto.

2019-07-12  Ben Wagner  <<EMAIL>>

	Properly handle phantom points for variation fonts (#56601).

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph): Scale phantom
	points if HVAR and/or VVAR is present.

2019-07-04  Werner Lemberg  <<EMAIL>>

	[psaux] (2/2) Handle fonts that use SEAC for ligatures (#56580).

	The same as previous commit but for the old engine.

	* src/psaux/t1decode.c (t1operator_seac): Implement it.

2019-07-04  Chris Liddell <<EMAIL>>

	[psaux] (1/2) Handle fonts that use SEAC for ligatures (#56580).

	As originally intended, a Type 1 SEAC charstring would be used for
	an accented glyph (like `acaron' or `uumlaut'), where the advance
	width of the SEAC glyph is the same as that of the `base' glyph
	(like `a' or `u').  In this case it is not uncommon for the SEAC
	glyph to not use an (H)SBW opcode of its own but to rely on the
	value from the base glyph.

	However, out-of-spec fonts also use SEAC glyphs for ligatures (like
	`oe' or `fi'), and in those cases the overall advance width is
	greater than that of the `base' glyph.  For this reason we have to
	allow that the SEAC glyph can have an (H)SBW value of its own, and
	if it has, retain this value, rather than the one from the base
	glyph.

	* src/psaux/psintrp.c (cf2_interpT2CharString) <cf2_escSEAC>:
	Implement it.

2019-07-01  Werner Lemberg  <<EMAIL>>

	* Version 2.10.1 released.
	==========================


	Tag sources with `VER-2-10-1'.

	* docs/VERSION.TXT: Add entry for version 2.10.1.

	* README, Jamfile (RefDoc), src/base/ftver.rc,
	builds/windows/vc2010/freetype.vcxproj,
	builds/windows/vc2010/index.html,
	builds/windows/visualc/freetype.dsp,
	builds/windows/visualc/freetype.vcproj,
	builds/windows/visualc/index.html,
	builds/windows/visualce/freetype.dsp,
	builds/windows/visualce/freetype.vcproj,
	builds/windows/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.10.0/2.10.1/, s/2100/2101/.

	* include/freetype/freetype.h (FREETYPE_PATCH): Set to 1.

	* builds/unix/configure.raw (version_info): Set to 23:1:17.
	* CMakeLists.txt (VERSION_PATCH): Set to 1.

	* include/freetype/fterrors.h (FT_Error_String): Fix C++ compilation.

2019-06-26  Alexei Podtelezhnikov  <<EMAIL>>

	* src/bdf/bdfdrivr.c (bdf_cmap_char_{index,next}): Fix inequality.

	Reported by Armin Hasitzka.

2019-06-16  Werner Lemberg  <<EMAIL>>

	* src/tools/apinames.c: Formatting, minor edits.

2019-06-16  Werner Lemberg  <<EMAIL>>

	[autofit] Disable hinting if no blue zones are available (#56450).

	* src/autofit/afglobal.c (af_face_global_get_metrics): Start again
	(with dummy hinter module) if no blue zones are present.

	* src/autofit/aflatin.c (af_latin_metrics_init_blues): Change
	signature to return error code.
	If no blue zones are found, update `glyph_styles' array to hold
	AF_STYLE_NONE_DFLT instead of the current style.
	(af_latin_metrics_init): Return internal error code if no blue zones
	are found.

2019-06-16  Werner Lemberg  <<EMAIL>>

	Towards better VMS support.

	More to come.

	* builds/vms/LIBS.OPT_IA64, builds/vms/_LINK.OPT_IA64,
	builds/vms/vmslib.dat: New files provided by Jouk Jansen
	<<EMAIL>>.

	* builds/vms/ftconfig.h: Update, also from Jouk.

2019-06-13  Werner Lemberg  <<EMAIL>>

	* src/autofit/aflatin.c (af_latin_metrics_init_widths): Minor.

2019-06-13  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Restore the span buffering for direct mode only.

	The buffer size FT_MAX_GRAY_SPANS is set to 10 spans, which should be
	enough to cover the entire scanline for simple glyphs in most cases:
	each slightly slanted edge needs up to two spans, plus a filling span
	in-between.  This is not new, we used to do it before cb4388783cecc.

	* src/smooth/ftgrays.c (gray_TWorker): Add `spans' and `num_spans'.
	(gray_hline, gray_sweep): Implement the span buffering.
	(gray_raster_render): Use negative `num_spans' to avoid the direct
	mode.

2019-06-12  Alexei Podtelezhnikov  <<EMAIL>>

	* include/freetype/ftmodapi.h (FT_DebugHook_Func): Return error.

	Fix a warning by adding a return value as in `TT_RunIns',
	which should not be a compatibility issue.

2019-06-11  Alexei Podtelezhnikov  <<EMAIL>>

	* src/truetype/ttobjs.c (tt_check_trickyness_family): Add `const'.

2019-06-11  Moazin Khatti  <<EMAIL>>

	[gzip] Add support for `gzip' encoded header.

	* src/gzip/ftgzip.c (FT_Gzip_Uncompress): Modify the the call to
	`inflateInit2' to enable support for `gzip' encoded headers.

2019-06-10  Alexei Podtelezhnikov  <<EMAIL>>

	[type1,type42] Use `const' for string literals.

	* include/freetype/internal/psaux.h (PS_Table_FuncsRec): Updated.
	* include/freetype/internal/t1types.h (T1_EncodingRec): Updated.
	* src/psaux/psobjs.[ch] (ps_table_add): Updated.
	* src/type1/t1load.c (T1_Open_Face, parse_encoding): Updated.
	* src/type42/t42objs.c (T42_Open_Face): Updated.
	* src/type42/t42parse.c (t42_parse_encoding): Updated.

	* src/cff/cffobjs.c (cff_face_init): Minor.

2019-06-10  Alexei Podtelezhnikov  <<EMAIL>>

	[bdf,pcf] Use `const' for string literals.

	* src/bdf/bdf.h (bdf_property_t): Updated `name'.
	* src/bdf/bdflib.c (_bdf_list_split,bdf_create_property,
	_bdf_add_property,_bdf_ato*): Updated.
	* src/bdf/bdfdrivr.c (bdf_interpret_style): Updated.
	* src/pcf/pcfread.c (pcf_intrpret_style): Ditto.

2019-06-07  Philip Race  <<EMAIL>>

	* src/base/ftinit.c (FT_Set_Default_Properties): Fix crash.

	Terminate loop at end of environment.

2019-05-31  Alexei Podtelezhnikov  <<EMAIL>>

	Solidify VC2005 builds.

	* include/freetype/internal/ftcalc.h (FT_MSB) [_MSC_VER]: Explicitly
	declare `_BitScanReverse' intrinsic.
	* builds/windows/visualc/freetype.vcproj [Debug]: Disable intrinsics.

2019-05-30  Nikhil Ramakrishnan  <<EMAIL>>

	[sfnt] Separate WOFF sources and headers.

	Move WOFF sources and headers to separate files.

	* include/freetype/internal/wofftypes.h, src/sfnt/sfwoff.c,
	src/sfnt/sfwoff.h: New files.

	* include/freetype/internal/fttrace.h: Register `sfwoff.c'.

	* include/freetype/internal/internal.h: Define
	FT_INTERNAL_WOFF_TYPES_H.

	* include/freetype/internal/sfnt.h: Include FT_INTERNAL_WOFF_TYPES_H.

	* include/freetype/internal/tttypes.h: Move out WOFF structures.

	* src/sfnt/rules.mk: Add `sfwoff.c'.

	* src/sfnt/sfnt.c: Include `sfwoff.c'.

	* src/sfnt/sfobjs.c: Include `sfwoff.h', move out WOFF sources.

2019-05-30  Werner Lemberg  <<EMAIL>>

	[base] Fix `make multi'.

	Reported by Nikhil.

	* src/base/fterrors.c: Include FT_INTERNAL_DEBUG_H.

2019-05-29  Ben Wagner  <<EMAIL>>

	[truetype] Fix copy-and-paste error (#56409).

	* src/truetype/ttgload.c (load_truetype_glyph): Use correct indices
	into `unrounded' array for phantom points.

2019-05-29  Werner Lemberg  <<EMAIL>>

	[truetype] Fix 32bit builds (#56404).

	Patch suggested by Ben Wagner <<EMAIL>>.

	* src/truetype/ttgxvar.c (FT_fixedToInt, FT_fixedToFdot6): Remove
	harmful cast to unsigned type.

2019-05-26  Ben Wagner  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph): Improve accuracy.

2019-05-23  Werner Lemberg  <<EMAIL>>

	[truetype] Draw glyphs without deltas in variation font (#56374).

	* src/truetype/ttgxvar.c (TT_Vary_Apply_Glyph_Deltas): Always fill
	`unrounded' array.

2019-05-21  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttinterp.c (opcode_name): Improve mnemonics.

2019-05-16  Werner Lemberg  <<EMAIL>>

	[truetype] Actually scale varied CVT values.

	Up to now, only the unscaled CVT values were varied; in other words,
	the `CVAR' data was never used for bytecode hinting.

	* src/truetype/ttgxvar.c (tt_cvt_ready_iterator): New auxiliary
	function.
	(tt_face_vary_cvt): Use it to trigger rescaling of CVT values.

2019-05-16  Werner Lemberg  <<EMAIL>>

	[truetype] Use 26.6 format for storing unscaled CVT values.

	If `CVAR' data is applied to variation fonts, fractional values are
	possible.

	* include/freetype/internal/tttypes.h (TT_FaceRec): Change type of
	`cvt' from `FT_Short' to `FT_Int32'.

	* src/truetype/ttgxvar.c (FT_fdot6ToFixed): New macro.
	(tt_face_vary_cvt): Use it to update code to 26.6 format.

	* src/truetype/ttobjs.c (tt_size_run_prep): Update code to 26.6
	format.

	* src/truetype/ttpload.c (tt_face_load_cvt): Stora data in 26.6
	format.

2019-05-16  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (load_truetype_glyph): Init `unrounded'.

	This fixes linear advance width values for spacing glyphs.  Bug
	introduced 2019-05-09.

2019-05-16  Werner Lemberg  <<EMAIL>>

	[truetype] Avoid code duplication.

	* src/truetype/ttobjs.c (tt_size_run_prep): Scale CVT values in this
	function.
	(tt_size_ready_bytecode): Updated.
	* src/truetype/ttgload.c (tt_loader_init): Updated.

2019-05-13  Jouk Jansen  <<EMAIL>>

	* vms_make.com: Updated.  Handle `bzip2' directory, too.

2019-05-13  Werner Lemberg  <<EMAIL>>

	* src/psaux/psfont.c (cf2_font_setup): Fix compiler warning.

2019-05-12  Werner Lemberg  <<EMAIL>>

	[truetype] Doh.  Fix last commit to make it work.

	Very embarassing :-)

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=14701
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=14705
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=14710

	* src/truetype/ttgload.c (IS_DEFAULT_INSTANCE): Move up and add
	argument; update all callers.
	(TT_Process_Simple_Glyph): Use it.  The `unrounded' array is active
	for variation fonts only, thus also enclose related code with
	`#ifdef TT_CONFIG_OPTION_GX_VAR_SUPPORT ...  #endif' where
	necessary.
	Revert commit a113e5d from 2019-05-09, and don't use `extra_points2'
	but allocate a temporary array.
	Speed up the scaling of the `unrounded' array.

	* src/truetype/ttgxvar.c (FT_fixedToInt, FT_FixedToFdot6): Fix type
	conversions and rounding.  The unsigned type must have more or equal
	bits to the signed type.

2019-05-09  Werner Lemberg  <<EMAIL>>

	[truetype] Increase precision of font variation (#54371).

	This patch makes FreeType use font units in 26.6 format internally
	instead of integers.

	* src/truetype/ttgxvar.c (FT_fixedToFdot6): New macro.
	(TT_Vary_Apply_Glyph_Deltas): Add argument to output unrounded font
	coordinates.
	* src/truetype/ttgxvar.h: Updated.

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph): Use
	`extra_points2' array to temporarily hold unrounded point
	coordinates; use them to compute scaled coordinates and linear
	advance width and height.
	(load_truetype_code): Adjust similarly.

2019-05-09  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Process_Simple_Glyph): Minor.

2019-05-08  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Faster fractions.

	* src/smooth/ftgrays.c (SUBPIXELS): Replace with...
	(FRACT): A fractional coordinate macro to use in...
	(gray_render_line, gray_render_scanline): ... here.

2019-05-07  Alexei Podtelezhnikov  <<EMAIL>>

	* src/raster/ftraster.c (Draw_Sweep): Unbreak.

2019-05-05  Alexei Podtelezhnikov  <<EMAIL>>

	* src/raster/ftraster.c: Clean-ups.

2019-05-05  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgxvar.c: More use of `FT_fdot14ToFixed'.

2019-05-04  Alexei Podtelezhnikov  <<EMAIL>>

	* src/smooth/ftgrays.c (gray_render_line): Small shortcut.

2019-05-04  Werner Lemberg  <<EMAIL>>

	Various clang 8.0 static analyzer fixes.

	Reported by Sender Ghost <<EMAIL>>.

	* src/autofit/afcjk.c (af_cjk_hints_compute_edges): Catch a corner
	case where `edge->first' could be NULL.

	* src/pfr/pfrobjs.c (pfr_slot_load): Remove unnecessary test of
	`size'.

	* src/raster/ftraster.c (Draw_Sweep): Catch a corner case where
	`draw_right' might be NULL.

	* src/sfnt/ttmtx.c (tt_face_get_metrics): Fix limit test for
	`aadvance'.
	Ensure `abearing' always hold a meaningful result.

	* src/truetype/ttgload.c (load_truetype_glyph): Ensure `subglyph' is
	not NULL before accessing it.
	* src/truetype/ttgxvar.c (TT_Set_Named_Instance): Remove unnecessary
	test of `namedstyle'.

	* src/type42/t42parse.c (t42_parser_done): Ensure
	`parser->root.funcs.done' is not NULL before accessing it.

2019-05-03  Alexei Podtelezhnikov  <<EMAIL>>

	Miscellaneous macro updates.

	* src/base/ftoutln.c (SCALED): Updated.
	* src/smooth/ftgrays.c (SCALED): Ditto.
	(FLOOR, ROUND, CEILING): Removed.
	* src/psaux/psfixed.h (cf2_fracToFixed): Updated.

2019-05-02  Alexei Podtelezhnikov  <<EMAIL>>

	Tweak LCD filtering.

	* src/base/ftlcdfil.c (ft_lcd_filter_fir, _ft_lcd_filter_legacy):
	Choose direction from bitmap's pixel_mode.
	* include/freetype/internal/ftobjs.c (FT_Bitmap_LcdFilterFunc):
	Updated.
	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Updated.

2019-05-02  Werner Lemberg  <<EMAIL>>

	* vms_make.com: Updated (#56253).

	Remove no longer existing directories (`autohint', `otlayout').
	Update used base extensions.
	Activate `autofit' module.
	Add `gxvalid' module.
	Update copyright notices.

2019-04-29  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Simplify cubic Bézier flattening.

	The previous implementation is correct but it is too complex.
	The revised algorithm is based on the fact that each split moves
	the control points closer to the trisection points on the chord.
	The corresponding distances are good surrogates for the curve
	deviation from the straight line.

	This cubic flattening algorithm is somewhat similar to the conic
	algorithm based the distance from the control point to the middle of
	the chord.  The cubic distances, however, decrease less predictably
	but are easy enough to calculate on each step.

	The new algorithm produces slightly larger number of splits, which is
	compensated by its simplicity.  The overall rendering performance is
	improved by 1-2%.  The larger number of splits does not necessarily
	result in higher quality, which stays comparable.

	* src/smooth/ftgrays.c (gray_render_cubic): Replace the split
	condition.

2019-04-26  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Bithacks and cosmetics.

	* src/smooth/ftgrays.c (gray_record_cell, gray_set_cell, gray_hline,
	gray_render_conic, gray_convert_glyph_inner): Updated.

2019-04-25  Alexei Podtelezhnikov  <<EMAIL>>

	Optimize Bézier bisections.

	This change makes bisections faster by 20-30%. When inlined into
	`gray_render_cubic', this makes the function faster by 10% and is
	noticeable in the overall rendering performance.

	* src/raster/ftraster.c (Split_Conic, Split_Cubic): Use shifts and
	refactor.
	* src/smooth/ftgrays.c (gray_split_conic, gray_split_cubic): Ditto.
	* src/base/ftstroke.c (ft_conic_split, ft_cubic_split): Ditto.
	* src/base/ftbbox.c (cubic_peak): Use shifts.

2019-04-23  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcmap.c (tt_cmap12_next): Remove dead code.

	Found by clang 8.0's static analyzer and reported by Sender Ghost
	<<EMAIL>>.

2019-04-23  Werner Lemberg  <<EMAIL>>

	[base] Fix thinko in previous commit.

	* src/base/ftbitmap.c (FT_Bitmap_Blend): Check final width, not
	target pitch.

	Problem reported by Sender Ghost <<EMAIL>>.

2019-04-22  Werner Lemberg  <<EMAIL>>

	* src/base/ftbitmap.c (FT_Bitmap_Blend): Check target pitch.

	Problem reported by Sender Ghost <<EMAIL>>.

2019-04-22  Werner Lemberg  <<EMAIL>>

	Fix return value of `FT_Set_Named_Instance' (#56186).

	* src/truetype/ttgxvar.c (TT_Set_Named_Instance): Correctly handle
	internal return value -1 of `TT_Set_Var_Design'.

2019-04-18  Werner Lemberg  <<EMAIL>>

	[pcf] Fix handling of undefined glyph (#56067).

	This commit fixes the changes from 2018-07-21, which broke charmap
	iteration.  We now add the default character as a new glyph with
	index 0, thus increasing the number of glyphs by one (as before).

	* src/pcf/pcfread.c (pcf_get_metrics): Adjust to new artificial
	glyph with index 0.
	Limit number of elements to 65534.
	(pcf_get_bitmaps): Ditto.
	Unify two loops into one; this avoids allocation of an intermediate
	array.
	(pcf_get_encodings): Don't flip indices but copy glyph metrics of
	default character to index 0.
	Also handle invalid default character.

	* docs/CHANGES: Updated.

2019-04-15  Minmin Gong  <<EMAIL>>

	* CMakeLists.txt: Avoid rewriting of unchanged configuration files.

	Reported as

	  https://savannah.nongnu.org/patch/index.php?9755

2019-04-15  JDG  <<EMAIL>>

	* src/tools/apinames.c (main): Fix error message.

	Reported as

	  https://savannah.nongnu.org/patch/?9796

2019-04-11  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Fix segfault in direct mode (#56092).

	* src/base/ftoutln.c (FT_Outline_Render): Set missing clip_box for
	direct mode.
	* src/smooth/ftgrays.c (gray_raster_render): Use it.

2019-04-06  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcmap.c (tt_get_glyph_name): Pacify compiler (#56061).

	This is for Visual Studio 2019 on ARM.

2019-04-06  Werner Lemberg  <<EMAIL>>

	For distribution, replace `.tar.bz2' with `.tar.xz' bundles.

	* builds/toplevel.mk (build): Do it.

	* README, docs/CHANGES, docs/release: Updated.

2019-04-06  Antony Lee  <<EMAIL>>

	Make `glyph_name' parameter to `FT_Get_Name_Index' a `const'.

	* include/freetype/freetype.h (FT_Get_Name_Index),
	include/freetype/internal/ftobjs.h (FT_Face_GetGlyphNameIndexFunc),
	include/freetype/internal/services/svgldict.h
	(FT_GlyphDict_NameIndexFunc), src/base/ftobjs.c (FT_Get_Name_Index),
	src/cff/cffdrivr.c (cff_get_name_index), src/sfnt/sfdriver.c
	(sfnt_get_name_index), src/type1/t1driver.c (t1_get_name_index),
	src/type42/t42drivr.c (t42_get_name_index): Add `const' to second
	argument.

2019-03-31  Armin Hasitzka  <<EMAIL>>

	[cff] Fix boundary checks.

	642bc7590c701c8cd35a9f60fa899cfa518b17ff introduced dynamically
	allocated memory when parsing CFF files with the "old" engine.  Bounds
	checks have never been updated, however, leading to pointless
	comparisons of pointers in some cases.  This commit presents a
	solution for bounds checks in the CFF module with an extended logic
	for the "old" engine while staying as concise as possible for the
	"new" one.

	* src/cff/cffparse.h: Introduce the struct `CFF_T2_StringRec' and
	the additional field `t2_strings' within `CFF_ParserRec'.

	* src/cff/cffparse.c (cff_parser_within_limits): Move all boundary
	checks into this new function and update the rest of `cffparse.c' to
	use it.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=12137

2019-03-20  Werner Lemberg  <<EMAIL>>

	[autofit] Fix Mongolian blue zone characters.

	* src/autofit/afblue.dat: Use U+200D (ZERO-WIDTH JOINER) characters
	to get medial forms for some Mongolian characters.
	* src/autofit/afblue.c, src/autofit/afblue.h: Regenerated.

2019-03-19  Werner Lemberg  <<EMAIL>>

	[autofit] Add support for Mongolian script.

	As a de-facto standard, layouts using this script are constructed
	horizontally line by line, then the lines are rotated clockwise for
	vertical display.

	* src/autofit/afblue.dat: Add blue zone data for Mongolian.
	* src/autofit/afblue.c, src/autofit/afblue.h: Regenerated.

	* src/autofit/afscript.h: Add Mongolian standard characters.

	* src/autofit/afranges.c, src/autofit/afstyles.h: Add Mongolian
	data.

2019-03-15  Werner Lemberg  <<EMAIL>>

	* Version 2.10.0 released.
	==========================


	Tag sources with `VER-2-10-0'.

	* docs/VERSION.TXT: Add entry for version 2.10.0.
	* docs/CHANGES: Updated.

	* README, Jamfile (RefDoc), src/base/ftver.rc,
	builds/windows/vc2010/freetype.vcxproj,
	builds/windows/vc2010/index.html,
	builds/windows/visualc/freetype.dsp,
	builds/windows/visualc/freetype.vcproj,
	builds/windows/visualc/index.html,
	builds/windows/visualce/freetype.dsp,
	builds/windows/visualce/freetype.vcproj,
	builds/windows/visualce/index.html,
	builds/wince/vc2005-ce/freetype.vcproj,
	builds/wince/vc2005-ce/index.html,
	builds/wince/vc2008-ce/freetype.vcproj,
	builds/wince/vc2008-ce/index.html: s/2.9.1/2.10.0/, s/291/2100/.

	* include/freetype/freetype.h (FREETYPE_MINOR): Set to 10.
	(FREETYPE_PATCH): Set to 0.

	* builds/unix/configure.raw (version_info): Set to 23:0:17.
	* CMakeLists.txt (VERSION_MINOR): Set to 10.
	(VERSION_PATCH): Set to 0.

	* builds/toplevel.mk (version, winversion): Since the minor version
	number has two digits now, never omit the patch number.  We would
	get ambiguous zip file names otherwise.
	(dist): Remove remnants of `docmaker' tool.
	(do-dist): Remove unused intermediate files.

	* src/cff/cffparse.c (destrict_c2s_item): Guard function with
	CFF_CONFIG_OPTION_OLD_ENGINE macro.

2019-03-07  Andrei Alexeyev  <<EMAIL>>
            Werner Lemberg  <<EMAIL>>

	Fix invalid function pointer casts.

	This change should allow Freetype to work on WASM/Emscripten without
	needing `-s EMULATE_FUNCTION_POINTER_CASTS=1'.

	* src/autofit/afdummy.c (af_dummy_hints_apply): Fix signature.

	* src/cid/cidload.c (cid_parse_font_matrix, parse_fd_array,
	parse_expansion_factor, parse_font_name): Return `void', not
	`FT_Error'.

	* include/freetype/internal/ftobjs.h (FT_CMap_CharVarIsDefaultFunc):
	Fix signature.

2019-03-05  Werner Lemberg  <<EMAIL>>

	[base] Handle numeric overflow (#55827).

	* src/base/ftglyph.c (FT_Glyph_Get_CBox): Use `FT_PIX_CEIL_LONG'.

2019-03-05  Sebastian Rasmussen  <<EMAIL>>

	[psaux] Fix use of uninitialized memory (#55832).

	* src/psaux/psintrp.c (cf2_interpT2CharString): The call to
	`cf2_arrstack_setCount' may fail because the allocator ran out of
	memory.  When this happens the stack is still written to before the
	error condition is checked.  This means that FreeType writes outside
	of allocated memory.  This commit moves the error check prior to the
	stack assignment, hence the function now properly returns with an
	error condition.

2019-02-23  Werner Lemberg  <<EMAIL>>

	* src/base/ftbitmap.c (FT_Bitmap_Blend): No fractional offsets.

	The function only provided a framework without an actual
	implementation, which this commit removes.

2019-02-23  Werner Lemberg  <<EMAIL>>

	* src/tools/update-copyright-year: Insert `(C)'.

2019-02-21  Armin Hasitzka  <<EMAIL>>

	[truetype] Mask numeric overflows.

	* src/truetype/ttinterp.c (Move_CVT, Move_CVT_Stretched, Ins_MIRP):
	Mask numeric overflows.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=11681
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=11734

2019-02-21  Armin Hasitzka  <<EMAIL>>

	[psaux] Mask numeric overflow.

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings): Mask numeric
	overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=13041

2019-02-16  Wink Saville  <<EMAIL>>

	* src/autofit/afwarp.h (af_warper_compute): Fix declaration.

2019-02-02  Nikolaus Waxweiler  <<EMAIL>>

	[truetype] Apply MVAR hasc, hdsc and hlgp metrics to current FT_Face metrics.

	Instead of setting typo or win metrics as the new `FT_Face' metrics
	indiscriminately, apply only typo deltas to the currently active
	`FT_Face' metrics.  This prevents line height differences when the
	default outlines were used as the regular face and instances for
	everything else, for example.

	* src/truetype/ttgxvar.c (tt_apply_mvar): Implement.

2019-02-02  Nikolaus Waxweiler  <<EMAIL>>

	[sfnt] Use typo metrics if OS/2 fsSelection USE_TYPO_METRICS bit is set.

	If the `OS/2' table exists and `fsSelection' bit 7
	(USE_TYPO_METRICS) is set, use the `sTypo*' set of values to compute
	the `FT_Face's ascender, descender, and height.  Otherwise, fall
	back to old behavior.

	* src/sfnt/sfobjs.c (sfnt_load_face): Implement.

2019-01-18  John Tytgat  <<EMAIL>>

	[sfnt] Handle TT fonts having two PostScript font names (#55471).

	* src/sfnt/sfdriver.c (sfnt_get_name_id): Prefer English over any
	other language found for PostScript font names.

2019-01-08  Chris Liddell <<EMAIL>>

	[psaux] Fix closepath (#55414).

	All of the Type 1 path building is done with code common to the
	revised CFF engine, with the exception of closepath, which was still
	calling ps_builder_close_contour(), thus previously cached segments
	were not always written to the path, and glyph corruption, or even
	invalid outlines were possible.

	* src/psauc/psinterp.c (cf2_interpT2CharString) <cf2_cmdCLOSEPATH>:
	Switch to calling `cf2_glyphpath_closeOpenPath'.

2018-12-29  Werner Lemberg  <<EMAIL>>

	* src/autofit/aflatin2.c: Some fixes from `aflatin.c' (#55310).

2018-12-25  Werner Lemberg  <<EMAIL>>

	* src/psaux/cffdecode.c (cff_operaor_seac): Fix numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=11915

2018-12-12  Werner Lemberg  <<EMAIL>>

	[gxvalid] Fix compiler warnings.

	* src/gxvalid/gxvjust.c (gxv_just_check_max_gid),
	src/gxvalid/gxvmort.c (gxv_mort_coverage_validate): Use `FT_UNUSED'.

2018-12-11  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Hint_Glyph): Remove useless test.

	`control_len' only gets its value from `n_ins' (and vice versa),
	which is always read as `unsigned short' and thus can't be larger
	than 0xFFFF.

2018-12-04  Werner Lemberg  <<EMAIL>>

	[bdf] Ignore data after `ENDFONT'.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10798

	* src/bdf/bdflib.c (_bdf_parse_end): New function.
	(_bdf_parse_glyphs): Switch to `_bdf_parse_end' after `ENDFONT' has
	been encountered.

2018-12-02  Alexei Podtelezhnikov  <<EMAIL>>

	* builds/windows/visualc/freetype.dsp: Dust off.

2018-11-27  Alexei Podtelezhnikov  <<EMAIL>>

	* builds/windows/vc2010/freetype.vcxproj: Simplify.

2018-11-27  Chris Liddell  <<EMAIL>>

	[type1,cff] Add FT_{Set,Get}_MM_WeightVector API calls.

	For multiple master fonts, common usage (in Postscript) is to modify
	the WeightVector of an existing font instance, this addition
	supports that use.

	* include/freetype/ftmm.h, src/base/ftmm.c (FT_Set_MM_WeightVector,
	FT_Get_MM_WeightVector): New API functions.

	* include/freetype/internalservices/svmm.h
	(FT_Set_MM_WeightVector_Func, FT_Get_MM_WeightVector_Func): New
	function types.
	(MultiMasters): Add `set_mm_weightvector' and `get_mm_weightvector'
	members.
	(FT_DEFINE_SERVICE_MULTIMASTERSREC): Updated.

	* src/cffcffdrivr.c (cff_set_mm_weightvector,
	cff_get_mm_weightvector): New functions.
	(cff_service_multi_masters): Register them.

	* src/truetype/ttdriver.c (tt_service_gx_multi_masters): Updated.
	This driver doesn't use the new interface.

	* src/type1/t1load.c (T1_Set_MM_WeightVector,
	T1_Get_MM_WeightVector): New functions.
	* src/type1/t1driver.c (t1_service_multi_masters): Register them.
	* src/type1/t1load.h: Updated.

2018-11-27  Ben Wagner  <<EMAIL>>

	[cff] Fix compiler warning (#55105).

	* src/cff/cffparse.c (cff_parser_run): Guard label only used if
	CFF_CONFIG_OPTION_OLD_ENGINE is active.

2018-11-27  Ben Wagner  <<EMAIL>>

	[truetype] Fix numeric overflow (#55103).

	* src/truetype/ttgload.c (compute_glyph_metrics): Use `SUB_LONG'.

2018-11-25  Alexei Podtelezhnikov  <<EMAIL>>

	[builds] Belated DLL support with vc2002-vc2008.

	The solution and project files should be automatically upgraded for
	the approriate Visual C++ version.

	* builds/windows/visualc/freetype.{sln,vcproj}: Major upgrades.
	* builds/windows/visualc/index.html: Document the change.
	* builds/windows/vc2005, builds/windows/vc2008: Removed as redundant.

2018-11-22  Armin Hasitzka  <<EMAIL>>

	* src/cff/cffparse.c: Please the compiler.

2018-11-22  Armin Hasitzka  <<EMAIL>>

	[cff] Fix memory overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9869
	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10869

	* src/cff/cffparse.c (destruct_t2s_item, cff_parser_run): Store
	evaluated T2 charstrings in separately allocated memory.

2018-11-18  Alexei Podtelezhnikov  <<EMAIL>>

	* builds/windows/{visualc,vc2005,vc2008}/freetype.vcproj: Fix it.

2018-11-10  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Placeholder only for library-enabled LCD filtering.

	* src/smooth/ftsmooth.c (ft_smooth_init): Add disabled
	`FT_Library_SetLcdFilter' call.

2018-11-09  Young Xiao  <<EMAIL>>

	[psaux] Add safety guard (#54985).

	* src/psaux/psobjs.c (cff_builder_close_contour): Do it.

2018-11-08  Alexei Podtelezhnikov  <<EMAIL>>

	* builds/unix/configure.raw: Require `windows.h' for windres.

2018-11-08  Alexei Podtelezhnikov  <<EMAIL>>

	[ftstroke] Fix unpredictable failures (#54986).

	* src/base/ftstroke.c (ft_sroke_border_lineto): Fix lineto check.

2018-11-08  Alexei Podtelezhnikov  <<EMAIL>>

	[ftstroke] Fix unpredictable failures (#54976).

	* src/base/ftstroke.c (ft_sroke_border_close): Set the start tags.

2018-11-07  Ben Wagner  <<EMAIL>>

	[truetype] Fix VF check from 2018-09-12 (#54973).

	* src/truetype/ttgxvar.c (TT_Vary_Apply_Glyph_Deltas): Use correct
	offsets for estimates.

2018-11-06  Werner Lemberg  <<EMAIL>>

	[pshinter] Fix numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=11242

	* src/pshinter/pshrec.c (ps_dimension_add_t1stem): Implement it.

2018-11-06  Werner Lemberg  <<EMAIL>>

	[psaux] Fix timeout in old CFF engine.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=11260

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings)
	<cff_op_sqrt> [CFF_CONFIG_OPTION_OLD_ENGINE]: Fix potential endless
	loop.

2018-11-04  Alexei Podtelezhnikov  <<EMAIL>>

	* src/truetype/ttgxvar.c: Use enum definitions.

2018-11-03  Alexei Podtelezhnikov  <<EMAIL>>

	* src/truetype/ttgxvar.c (ft_var_apply_tuple): Adjust condition.

2018-11-03  Alexei Podtelezhnikov  <<EMAIL>>

	* src/truetype/ttgxvar.c (ft_var_apply_tuple): Tracing tweaks.

2018-11-03  Alexei Podtelezhnikov  <<EMAIL>>

	Revert due to specs: [truetype] Speed up variation IUP.

2018-11-02  Alexei Podtelezhnikov  <<EMAIL>>

	* src/truetype/ttgxvar.c (ft_var_get_item_delta): Fixed logic.

	Reported and tested by Behdad.

2018-11-02  Shailesh Mistry  <<EMAIL>>

	[autofit] Prevent SEGV.

	See

	  https://bugs.ghostscript.com/show_bug.cgi?id=697545

	for more details on how the bug was found.

	* src/autofit/afloader.c (af_loader_load_glyph): Propagate error
	code.

2018-10-31  Alexei Podtelezhnikov <<EMAIL>>

	[truetype] Speed up variation IUP.

	* src/truetype/ttgxvar.c (tt_delta_interpolate): Separate trivial
	snapping to the same position from true interpolation.

2018-10-31  Alexei Podtelezhnikov  <<EMAIL>>

	* src/type1/t1load.c (t1_set_mm_blend): Optimized.

2018-10-31  Alexei Podtelezhnikov  <<EMAIL>>

	* src/truetype/ttgxvar.c (ft_var_get_item_delta): Optimized.

2018-10-29  Werner Lemberg  <<EMAIL>>

	[base] Fix numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=11080

	* src/base/ftoutln.c (FT_Outline_Get_Orientation): Use `MUL_LONG'.

2018-10-29  Werner Lemberg  <<EMAIL>>

	[cff] Fix numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10988

	* src/cff/cffparse.c (cff_parser_run)
	[CFF_CONFIG_OPTION_OLD_ENGINE]: Use `NEG_LONG'.

2018-10-27  Alexei Podtelezhnikov  <<EMAIL>>

	[sfnt] Make `head' timestamps unsigned.

	It's been more than 2^31 seconds since 1904.

	* include/freetype/tttables.h (TT_Header): Change field types.
	* src/sfnt/ttload.c (tt_face_load_generic_header): Updated.

2018-10-27  Alexei Podtelezhnikov  <<EMAIL>>

	Revert: Align FreeType with standard C memory management.

2018-10-27  Werner Lemberg  <<EMAIL>>

	[psaux] Fix numeric overflow.

	Triggered by

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=11157

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings) <cff_op_blend>
	[CFF_CONFIG_OPTION_OLD_ENGINE]: Fix integer overflow.

2018-10-20  Werner Lemberg  <<EMAIL>>

	Avoid endless loop while tracing (#54858).

	* src/type1/t1load.c (parse_buildchar): Guard tracing stuff with
	FT_DEBUG_LEVEL_TRACE.

2018-10-17  David Demelier  <<EMAIL>>

	* CMakeLists.txt: Specify `RUNTIME DESTINATION'.

	This is needed for DLL builds.

2018-10-07  Werner Lemberg  <<EMAIL>>

	A missing Unicode cmap is not a fatal error.

	This is a follow-up to the previous commit.

	* src/cff/cffobjs.c (cff_face_init), src/sfnt/sfobjs.c
	(sfnt_load_face), src/type1/t1objs.c (T1_Face_Init),
	src/type42/t42objs.c (T42_Face_Init): Implement it.

2018-10-07  Werner Lemberg  <<EMAIL>>

	Fix handling of FT_CONFIG_OPTION_ADOBE_GLYPH_LIST (#54794).

	* src/cff/cffcmap.c (cff_cmap_unicode_init), src/psaux/t1cmap.c
	(t1_cmap_unicode_init), src/sfnt/ttcmap.c (tt_cmap_unicode_init):
	Check `unicodes_init' field.

2018-10-03  Werner Lemberg  <<EMAIL>>

	[ftgrays] Fix typo in stand-alone mode (#54771).

	* src/smooth/ftgrays.c (FT_THROW) [STANDALONE_ &&
	FT_DEBUG_LEVEL_TRACE]: Fix call to `FT_ERR_CAT'.

2018-10-02  Werner Lemberg  <<EMAIL>>

	[psaux] Fix segfault.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10768

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings)
	<cff_op_callothersubr> [CFF_CONFIG_OPTION_OLD_ENGINE]: Check
	argument.

2018-10-02  Werner Lemberg  <<EMAIL>>

	[psaux] Fix numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10740

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings) <cff_op_roll>
	[CFF_CONFIG_OPTION_OLD_ENGINE]: Use NEG_INT.

2018-10-02  Werner Lemberg  <<EMAIL>>

	[pshinter] Handle numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10550

	* src/pshinter/pshglob.c (psh_blues_snap_stem): Mask numeric
	overflow.

2018-09-27  Alexei Podtelezhnikov  <<EMAIL>>

	Align FreeType with standard C memory management.

	* include/freetype/ftsystem.h: Include FT_TYPES_H.
	(*FT_Alloc_Func, *FT_Realloc_Func): Use size_t for the size arguments.
	* src/raster/ftmisc.h: Ditto.

	* builds/amiga/src/base/ftsystem.c, builds/unix/ftsystem.c,
	* builds/vms/ftsystem.c, src/base/ftsystem.c (ft_alloc, ft_realloc):
	Use size_t for the size arguments.

	* src/base/ftdbgmem.c (ft_mem_debug_alloc, ft_mem_debug_realloc): Use
	FT_Offset, aka size_t, for the size arguments.

2018-09-25  Werner Lemberg  <<EMAIL>>

	Fix handling of `FT_Bool'.

	Before this commit we had code like

	  (FT_Bool)( globals->glyph_styles[gindex] & 0x8000)

	Since `FT_Bool' is defined to be an `unsigned char', the code
	evaluated to something like

	  (unsigned char)( 0x8532 & 0x8000)

	which in turn expanded to

	  (unsigned char)( 0x8000)

	and finally yielded 0x00 – i.e., false – not as expected.

	Problem reported and analyzed by Tony Smith <<EMAIL>>.

	* include/freetype/fttypes.h (FT_BOOL): Add a comparison against
	zero so that we always have a Boolean expression.

	*/*: Replace castings to `FT_Bool' with calls to `FT_BOOL' where
	possible.

2018-09-23  Alexei Podtelezhnikov  <<EMAIL>>

	[bdf] Speed up charmap access.

	This makes FT_Get_Char_Index and FT_Get_Next_Char 4-5 times faster.

	* src/bdf/bdfdrivr.c (bdf_cmap_char_{index,next}): Help binary search
	with continuous prediction.

2018-09-22  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftobjs.c (ft_glyphslot_preset_bimap): Another tweak.

	This one should be clearer. When the rounded monochrome bbox collapses
	we add a pixel that covers most if not all original cbox.

2018-09-21  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftobjs.c (ft_glyphslot_preset_bimap): Further tweak.

2018-09-21  Ben Wagner  <<EMAIL>>

	Improve auto-hinter handling of bitmap fonts (#54681).

	For bitmap fonts, `FT_Load_Glyph' should either return an error or
	not set the format to `FT_GLYPH_FORMAT_OUTLINE'.  However, in this
	case `FT_Load_Glyph' calls into the auto-hinter which calls back
	into `FT_Load_Glyph' with `FT_LOAD_NO_SCALE' in the flags, which
	marks the glyph as `FT_GLYPH_FORMAT_OUTLINE' with an empty path
	(even though it doesn't have any path).  It appears that the
	auto-hinter should not be called when the face doesn't have
	outlines.  The current test for using the auto-hinter in
	`FT_Load_Glyph' checks whether the driver supports scalable
	outlines, but not if the face supports scalable outlines.

	* src/base/ftobjs.c (FT_Load_Glyph): Directly check whether we have
	scalable outlines.

2018-09-21  Werner Lemberg  <<EMAIL>>

	[raster] Fix disappearing vertical lines (#54589).

	* src/raster/ftraster.c (Vertical_Sweep_Span): Handle special case
	where both left and right outline exactly pass pixel centers.

2018-09-20  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftobjs.c (ft_glyphslot_preset_bimap): Tiny rounding tweak.

	This adds pixels in case a contour goes through the center
	and they need to be turned on in the b/w rasterizer.

2018-09-20  Alexei Podtelezhnikov  <<EMAIL>>

	[pcf] Replace charmap implementation.

	PCF comes with charmap lookup table, aka PCF encodings.  Using it
	directly makes FT_Get_Char_Index and FT_Get_Next_Char 4-5 times
	faster than the original BDF-like binary searches.

	* src/pcf/pcf.h (PCF_EncodingRec): Removed.
	(PCF_FaceRec): Remove `nencodings' and `encodings'.
	* src/pcf/pcfdrivr.c (pcf_cmap_char_{index,next}): Replaced.
	* src/pcf/pcfread.c (pcf_get_encodings): Store data differently.

2018-09-20  Werner Lemberg  <<EMAIL>>

	[base] Remove unused function `FT_GlyphLoader_CopyPoints'.

	* include/freetype/internal/ftgloadr.h, src/base/ftgloadr.c
	(FT_GlyphLoader_CopyPoints): Do it.

2018-09-19  Alexei Podtelezhnikov  <<EMAIL>>

	[pcf] Prepare to replace charmap implementation.

	* src/pcf/pcf.h (PCF_FaceRec): Updated to include...
	(PCF_EncRec): ... this new structure to store charmap geometry.

	* src/pcf/pcfread.c (pcf_get_encodings): Store charmap geometry.

2018-09-18  Alexei Podtelezhnikov  <<EMAIL>>

	Remove unused fields.

	* src/pcf.h (PCF_FaceRec): Remove `charmap' and `charmap_handle'.
	* src/bdfdrvr.h (BDF_FaceRec): Ditto.
	* src/winfonts/winfnt.h (FNT_FaceRec): Ditto.

2018-09-17  Werner Lemberg  <<EMAIL>>

	[pshinter] Handle numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10396

	* src/pshinter/pshglob.c: Include FT_INTERNAL_CALC_H.
	(psh_blues_snap_stem): Mask numeric overflow.

2018-09-13  Werner Lemberg  <<EMAIL>>

	[truetype] Some fixes for VF checks.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10317

	* src/truetype/ttgxvar.c (ft_var_load_gvar): Properly exit memory
	frame if we have invalid glyph variation data offsets.
	(tt_face_vary_cvt): Protect against missing `tuplecoords' array.
	Fix typo.

2018-09-13  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfdriver.c (sfnt_get_var_ps_name): Fix last commit.

2018-09-13  Werner Lemberg  <<EMAIL>>

	* src/sfnt/sfdriver.c (sfnt_get_var_ps_name): Check `result'.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10316

2018-09-12  John Tytgat  <<EMAIL>>

	[sfnt] Better PS name handling (#54629).

	* src/sfnt/sfdriver (IS_WIN, IS_APPLE): Omit language ID checks.
	(get_win_string, get_apple_string): Return NULL when the PostScript
	font name characters is not according to specification.
	(get_win_string): Make trace output work if the high byte if
	non-zero.
	(sfnt_get_var_ps_name, sfnt_get_ps_name): Previously we preferred
	Win PS name (when there is also an Apple PS name); change this into
	a fallback to Apple PS name in case the Win PS name is invalid.

2018-09-12  Werner Lemberg  <<EMAIL>>

	[truetype] Improve VF check.

	Triggered by

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10255

	* src/truetype/ttgxvar.c (ft_var_load_gvar): Use better limit check
	for `tupleCount'.

2018-09-12  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgxvar.c (ft_var_load_gvar): Check `glyphoffsets'.

2018-09-10  Armin Hasitzka  <<EMAIL>>

	* src/pshinter/pshrec.c (t2_hints_stems): Mask numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10215

2018-09-09  Ben Wagner  <<EMAIL>>

	* builds/freetype.mk (refdoc-venv): Ensure python version (#54631).

2018-09-07  Werner Lemberg  <<EMAIL>>

	[truetype] Fix assertion failure.

	Triggered by

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10212

	* src/truetype/ttgload.c (load_truetype_glyph): Reintroduce
	`opened_frame' (removed in a change from 2018-08-26) to handle
	deallocation of the second frame.

2018-09-05  Werner Lemberg  <<EMAIL>>

	Synchronize `ftdebug.c' files.

	* builds/amiga/src/base/ftdebug.c, builds/wince/ftdebug.c,
	builds/windows/ftdebug.c: Synchronize with `src/base/ftdebug.c'.

2018-09-05  Nikhil Ramakrishnan  <<EMAIL>>

	Add documentation guidelines file.

	* docs/DOCGUIDE: New file.

2018-09-04  Werner Lemberg  <<EMAIL>>

	* devel/ftoption.h: Synchronize with master `ftoption.h'.

2018-09-03  Nikhil Ramakrishnan  <<EMAIL>>

	[docwriter] Don't break code snippets accross lines.

	Reported as

	  https://lists.nongnu.org/archive/html/freetype-devel/2018-08/msg00124.html

	* docs/reference/markdown/stylesheets/extra.css (.md-typeset code):
	Add rule `white-space'.

2018-09-03  Werner Lemberg  <<EMAIL>>

	*/*: s/PSNames/psnames/.

	Only tracing messages are affected.

2018-09-03  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix heap buffer overflow in CPAL handling.

	* src/sfnt/ttcpal.c (tt_face_palette_set): Fix boundary test.
	(tt_face_load_cpal): Updated.

2018-09-01  Werner Lemberg  <<EMAIL>>

	Remove `FT_Outline_{New,Done}_Internal'.

	These public API functions(!) were always undocumented and have
	escaped all clean-up efforts until now.

	* include/freetype/ftoutln.h (FT_Outline_New_Internal,
	FT_Outline_Done_Internal): Removed.

	* src/base/ftoutln.h (FT_Outline_New_Internal,
	FT_Outline_Done_Internal): Merge into...
	(FT_Outline_New, FT_Outline_Done): ... these functions.

	* docs/README: Updated.

2018-08-30  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftobjs.c (ft_glyphslot_preset_bitmap): Check glyph format.

2018-08-31  Armin Hasitzka  <<EMAIL>>

	[errors] Refine the macro logic surrounding `FT_Error_String'.

	* include/freetype/fterrors.h (FT_INCLUDE_ERR_PROTOS,
	FT_ERR_PROTOS_DEFINED): Undefine `FT_INCLUDE_ERR_PROTOS' after
	checking it and introduce a new macro that takes proper care of
	multiple-inclusion protection.

2018-08-31  Werner Lemberg  <<EMAIL>>

	* src/base/ftdebug.c (FT_Throw): Restore missing `FT_UNUSED' calls.

2018-08-31  Werner Lemberg  <<EMAIL>>

	* src/base/ftdebug.c (FT_Throw): Reduce chattiness.

2018-08-31  Werner Lemberg  <<EMAIL>>

	* src/autofit/afhints.c (af_glyph_hints_reload): Add initialization.

2018-08-30  Alexei Podtelezhnikov  <<EMAIL>>

	Consolidate bitmap presetting and size assessment.

	* include/freetype/internal/ftobjs.h (ft_glyphslot_preset_bitmap):
	Change return type.
	* src/base/ftobjs.c (ft_glyphslot_preset_bitmap): Return the bitmap
	size assessment.

	* src/raster/ftrend1.c (ft_raster1_render): Use it to refuse the
	rendering of enourmous or far-fetched outlines.
	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Ditto.

2018-08-30  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftobjs.c (ft_glyphslot_preset_bitmap): Correct mono.

2018-08-30  Armin Hasitzka  <<EMAIL>>

	[errors] Introduce a macro to control `FT_Error_String'.

	* devel/ftoption.h (FT_CONFIG_OPTION_ERROR_STRINGS),
	include/freetype/config/ftoption.h (FT_CONFIG_OPTION_ERROR_STRINGS):
	New macro.

2018-08-30  Armin Hasitzka  <<EMAIL>>

	[errors] Introduce `FT_Error_String'.

	* include/freetype/fterrors.h (FT_Error_String),
	src/base/fterrors.c (FT_Error_String): Implement `FT_Error_String'.

	* src/base/ftbase.c, src/base/Jamfile (_source),
	src/base/rules.mk (BASE_SRC): Add `fterrors.c' to the build logic.

	* src/base/ftdebug.c (FT_Throw): Use `FT_Error_String'.

2018-08-30  Werner Lemberg  <<EMAIL>>

	[autofit] Trace `before' and `after' edges of strong points.

	* src/autofit/afhints.h (AF_PointRec) [FT_DEBUG_AUTOFIT]: New arrays
	`before' and `after'.

	* src/autofit/afhints.c (af_get_strong_edge_index): New auxiliary
	function.
	(af_glyph_hints_dump_points): Trace `before' and `after' edges.
	(af_glyph_hints_align_strong_points) [FT_DEBUG_AUTOFIT]: Set
	`before' and `after' information.

2018-08-30  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Overflow-resistant bitmap presetting.

	* src/base/ftobjs.c (ft_glyphslot_preset_bitmap): Implement it.

2018-08-29  Armin Hasitzka  <<EMAIL>>

	Fix numeric overflows.

	* src/pshint/pshalgo.c (psh_hint_align, psh_hint_align_light,
	psh_hint_table_find_strong_points): Fix numeric overflows.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10083

2018-08-29  Werner Lemberg  <<EMAIL>>

	[cff] Fix handling of `roll' op in old engine.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10080

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings) <cff_op_roll>
	[CFF_CONFIG_OPTION_OLD_ENGINE]: Use modulo for loop count, as
	documented in the specification.

2018-08-26  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttobjs.c (tt_size_read_bytecode): Trace CVT values.

2018-08-26  Nikhil Ramakrishnan  <<EMAIL>>

	* configure: Copy assets required by docwriter.

	Copy directory `docs/reference/markdown' when FreeType is compiled in a
	different directory.

	Fixes `make refdoc' if builddir != srcdir.

	Reported as

	  https://lists.nongnu.org/archive/html/freetype-devel/2018-08/msg00083.html

2018-08-26  Werner Lemberg  <<EMAIL>>

	* src/pshint/pshalgo.c (psh_hint_overlap): Fix numeric overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10057

2018-08-26  Werner Lemberg  <<EMAIL>>

	Minor tracing adjustments.

	* src/base/ftstream.c (FT_Stream_EnterFrame, FT_Stream_ExitFrame):
	Trace.

	* src/truetype/ttgload.c (TT_Access_Glyph_Frame): Remove tracing.

2018-08-26  Werner Lemberg  <<EMAIL>>

	[truetype] Avoid nested frames.

	Triggered by

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10054

	* src/truetype/ttgload.c (load_truetype_glyph): Don't use variable
	`opened_frame' to trace whether a frame must be closed at the end of
	function: This fails because `TT_Vary_Apply_Glyph_Deltas' (which
	gets called for space glyphs) uses a frame by itself.  Instead,
	close the frame after loading the header, then use another frame for
	the remaining part of the glyph later on.

	Also avoid calling `tt_get_metrics' twice under some circumstances.

2018-08-26  Werner Lemberg  <<EMAIL>>

	Various minor clean-ups.

	* src/base/ftapi.c: Remove.  Unused.
	* src/base/Jamfile (_sources): Updated.

	* src/base/ftstream.c (FT_Stream_ReleaseFrame): Remove redundant
	code.

2018-08-25  Nikhil Ramakrishnan  <<EMAIL>>

	Convert documentation markup to Markdown.

	It is the result of a GSoC 2018 project; this separate ChangeLog
	commit covers the last four commits

	  ae5d1a4cec37557f31aec270332cfe886a62f9a0
	  53c69ce04faed3dcc68ca0f54cb8d703d8babf69
	  195728d5ba38f34fb2c2c20807c01656f2f59b66
	  c962db28ea59225f0105c03d907d4a9b71765687

	* docs/reference/markdown/images/favico.ico,
	docs/reference/markdown/javascripts/extra.js,
	docs/reference/markdown/stylesheets/extra.css: New files.

	* docs/reference/.gitignore, docs/reference/README: Updated.

	* src/tools/docmaker/*: Removed.  It has been replaced with
	`docwriter', a python package available at

	  https://pypi.org/project/docwriter/

	* Jamfile: Updated.
	* builds/ansi/ansi-def.mk, builds/beos/beos-def.mk,
	builds/dos/dos-def.mk, builds/os2/os2-def.mk (BIN),
	builds/unix/unixddef.mk, builds/windows/win32-def.mk: New variable.

	* builds/unix/configure.raw: Check for `python' and `pip'.
	If not present, warn that `make refdoc' will fail.
	* builds/unix/unix-def.in (PYTHON, PIP, BIN): New variables.

	* builds/freetype.mk (PYTHON, PIP, VENV_NAME, VENV_DIR, ENV_PYTHON,
	ENV_PIP): New variables.
	(refdoc): Updated.
	(refdoc-venv): New target.
	(.PHONY): Updated.

2018-08-23  Werner Lemberg  <<EMAIL>>

	Add macros for handling over-/underflowing `FT_Int64' values.

	* include/freetype/internal/ftcalc.h (ADD_INT64, SUB_INT64,
	MUL_INT64, DIV_INT64) [FT_LONG64]: New macros.

	* src/base/ftcalc.c (ft_corner_orientation) [FT_LONG64]: Use
	`SUB_INT64' and `MUL_INT64'.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=10028

2018-08-22  Werner Lemberg  <<EMAIL>>

	[truetype] Improve legibility of `glyf' parsing.

	* src/truetype/ttgload.c (ON_CURVE_POINT, X_SHORT_VECTOR,
	Y_SHORT_VECTOR, REPEAT_FLAG, X_POSITIVE, SAME_X, Y_POSITIVE, SAME_Y,
	OVERLAP_SIMPLE): New macros.
	(TT_Load_Simple_Glyph): Use new macros to make code more readable.
	Remove useless adjustment of `outline->tags' elements.

2018-08-21  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcpal.c (tt_face_load_cpal): Add missing safety check.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9981

2018-08-18  Werner Lemberg  <<EMAIL>>

	[psaux] Avoid slow PS font parsing in case of error.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9955

	* src/psaux/psobjs.c (ps_parser_to_bytes): Set `parser->cursor' even
	in case of error to avoid potential re-scanning.

2018-08-18  Werner Lemberg  <<EMAIL>>

	[cff] Fix heap buffer overflow in old engine.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9967

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings)
	<cff_op_blend> [CFF_CONFIG_OPTION_OLD_ENGINE]: `num_designs' must be
	non-zero.

2018-08-16  Young Xiao  <<EMAIL>>

	* builds/mac/ftmac.c (parse_fond): Fix buffer overrun.

	Reported as bug #54515, duplicate of #43540.

2018-08-16  Werner Lemberg  <<EMAIL>>

	* builds/*/ftsystem.c (FT_COMPONENT): Updated also.

2018-08-15  Alexei Podtelezhnikov  <<EMAIL>>

	[bdf] Don't track duplicate encodings.

	There is no harm except some umbiguity in broken fonts with duplicate
	encodings.

	* src/bdf/bdflib.c (_bdf_parse_glyphs): Remove duplicate tracking.
	(_bdf_parse_t): Remove large `have' bitfield.

2018-08-15  Werner Lemberg  <<EMAIL>>

	Don't use `trace_' prefix for FT_COMPONENT arguments.

	* include/freetype/internal/ftdebug.h (FT_TRACE_COMP,
	FT_TRACE_COMP_): New auxiliary macros to add `trace_' prefix.
	(FT_TRACE): Use `FT_TRACE_COMP'.

	*/* (FT_COMPONENT): Updated.

2018-08-14  Werner Lemberg  <<EMAIL>>

	Use formatting string in FT_TRACEX calls for non-simple arguments.

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings)
	<cff_op_hstem, cff_op_hintmask, cff_op_hlineto, cff_op_vhcurveto>:
	Do it.

	* src/psaux/pshints.c (cf2_hintmap_build): Ditto.

	* src/psaux/psintrp.c (cf2_interpT2CharString) <cf2_cmdHSTEM,
	cf2_cmdVSTEM, cf2_cmdHLINETO, cf2_cmdRRCURVETO, cf2_cmdCALLSUBR,
	cf2_escHSTEM3, cf2_cmdHINTMASK, cf2_cmdHVCURVETO>: Ditto.

	* src/truetype/ttinterp.c (TT_RunIns): Ditto.

2018-08-14  Alexei Podtelezhnikov  <<EMAIL>>

	[bdf] Remove unused fields.

	* src/bdf/bdf.h (bdf_font_t): Remove `nmod', `umod', and `modified',
	which were set but never used.
	* src/bdf/bdflib.c (_bdf_parse_{glyphs,properties}, bdf_load_font):
	Updated accordingly.

2018-08-14  Werner Lemberg  <<EMAIL>>

	[cff] Fix another segv in old engine.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9872

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings)
	[CFF_CONFIG_OPTION_OLD_ENGINE]: Disallow invalid T1 opcodes in
	dictionaries.

2018-08-14  Werner Lemberg  <<EMAIL>>

	[cff] Fix missing error handling.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9865

	* src/psaux/cffparse.c (cff_parser_run)
	[CFF_CONFIG_OPTION_OLD_ENGINE]: Don't ignore return value of
	`parse_charstrings_old'.

2018-08-14  Alexei Podtelezhnikov  <<EMAIL>>

	[bdf] Remove unused overflow storage.

	* src/bdf/bdf.h (bdf_glyphlist_t): Remove this type.
	(bdf_font_t): Remove `overflow' field.
	* src/bdf/bdflib.c (bdf_free_font): Remove `overflow' freeing.

2018-08-14  Werner Lemberg  <<EMAIL>>

	[cff] Fix segv in old engine.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9864

	* src/psaux/cffdecode.c (cff_decoder_parse_charstrings)
	<cff_op_random> [CFF_CONFIG_OPTION_OLD_ENGINE]: Use top dict's
	`random' field directly if parsing dictionaries.

2018-08-13  Alexei Podtelezhnikov  <<EMAIL>>

	[bdf] Use unsigned types.

	* src/bdf/bdf.h (bdf_glyph_t): Unsign `encoding'.
	(bdf_font_t): Unsign `default_char'.
	* src/bdf/bdfdrivr.h (BDF_encoding_el): Unsign `enc'.

	* src/bdf/bdflib.c (_bdf_add_property, _bdf_parse_glyphs,
	_bdf_parse_start): Updated accordingly.
	* src/bdf/bdfdrivr.c (bdf_cmap_char_{index,next}): Ditto.

2018-08-13  Werner Lemberg  <<EMAIL>>

	* src/type42/t42parse.c (t42_parse_sfnts): One more format check.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9832

2018-08-11  Werner Lemberg  <<EMAIL>>

	* src/base/ftcalc.c (FT_Matrix_Check): Fix integer overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9811

2018-08-10  Alexei Podtelezhnikov  <<EMAIL>>

	* src/sfnt/ttsbit.c (tt_sbit_decoder_load_compound): Follow specs.

2018-08-10  Ben Wagner  <<EMAIL>>

	* src/sfnt/sfobjs.c (sfnt_done_face): Fix memory leak (#54435).

2018-08-10  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Render_Glyph_Internal): Improve tracing.

2018-08-10  Werner Lemberg  <<EMAIL>>

	Fix clang warnings.

	* src/base/ftdebug.c (ft_trace_level_enabled,
	ft_trace_level_disabled): Add `static' keyword.

2018-08-09  Alexei Podtelezhnikov  <<EMAIL>>

	[raster, smooth] Reinstate bitmap size limits.

	This again moves outline and bitmap size checks one level up.

	* src/base/ftoutln.c (FT_Outline_Render): Explicitly reject enormous
	outlines.
	* src/raster/ftrend1.c (ft_raster1_render): Reject enormous bitmaps
	and, therefore, outlines that require them.
	* src/smooth/ftsmooth.c (ft_smooth_render_generic): Ditto.

	* src/raster/ftraster.c (ft_black_render): Remove outline size checks.
	* src/smooth/ftgrays.c (gray_raster_render): Ditto.
	[STANDALONE]: Remove `FT_Outline_Get_CBox' copy.

2018-08-08  Alexei Podtelezhnikov  <<EMAIL>>

	[pcf] Revert massive unsigning.

2018-08-08  Werner Lemberg  <<EMAIL>>

	[smooth] Improve tracing.

	* src/smooth/ftgrays.c (gray_convert_glyph_inner): Only use tracing
	if called the first time.
	(gray_convert_glyph): Updated.

2018-08-08  Werner Lemberg  <<EMAIL>>

	Add internal functions `FT_Trace_Disable' and `FT_Trace_Enable'.

	It sometimes makes sense to suppress tracing informations, for
	example, if it outputs identical messages again and again.

	* include/freetype/internal/ftdebug.h: Make `ft_trace_levels' a
	pointer.
	(FT_Trace_Disable, FT_Trace_Enable): New declarations.

	* src/base/ftdebug.c (ft_trace_levels): Rename to...
	(ft_trace_levels_enabled): ... this.
	(ft_trace_levels_disabled): New array.
	(ft_trace_levels): New pointer.
	(FT_Trace_Disable, FT_Trace_Enable): Implement.
	(ft_debug_init): Updated.

2018-08-08  Werner Lemberg  <<EMAIL>>

	Debugging improvements.

	* src/base/ftobjs.c (pixel_modes): Move this array to top level
	from ...
	(FT_Load_Glyph): ... here.
	(FT_Render_Glyph_Internal): Use `width' x `height' in trace message.
	Use `pixel_modes'.

2018-08-08  Alexei Podtelezhnikov  <<EMAIL>>

	[pcf] Massive unsigning (part 2).

	Treat all size related properties as unsigned values.

	* src/pcf/pcf.h (PCF_ParsePropertyRec): Use unsigned `name' and
	`value'.
	* src/pcf/pcfread.c (pcf_get_properties, pcf_load_font): Updated
	parsing code and handling of AVERAGE_WIDTH, POINT_SIZE, PIXEL_SIZE,
	RESOLUTION_X and RESOLUTION_Y.

2018-08-08  Alexei Podtelezhnikov  <<EMAIL>>

	[pcf] Massive unsigning (part 1).

	Unofficial specifications hesitate to use unsigned 32-bit integers.
	Negative values caused a lot of trouble in the past and it is safer
	and easier to treat some properties as unsigned.

	* src/pcf/pcf.h (PCF_AccelRec): Use unsigned values for `fontAscent',
	`fontDescent', and `maxOverlap'.
	* src/pcf/pcfread.c (pcf_load_font, pcf_get_accel): Updated.
	* src/pcf/pcfdrivr.c (PCF_Glyph_Load, PCF_Size_Select,
	PCF_Size_Request): Updated.

2018-08-07  Alexei Podtelezhnikov  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_get_bitmaps): Unsign `offsets' and
	`bitmapSizes'.

2018-08-06  Werner Lemberg  <<EMAIL>>

	* devel/ftoption.h: Synchronize with main `ftoption.h'.

2018-08-06  Alexei Podtelezhnikov  <<EMAIL>>

	[pcf] Use unsigned types.

	* src/pcf/pcf.h (PCF_Encoding): Use unsigned `enc'.
	* src/pcf/pcfdrivr.c (pcf_cmap_char_{index,next}): Ditto.
	* src/pcf/pcfread.c (pcf_get_encodings): Use unsigned types.

2018-08-05  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (compute_glyph_metrics): Fix overflow.

	Reported as

	  https://bugs.chromium.org/p/chromium/issues/detail?id=777151

2018-08-04  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttinterp.c (opcode_name): Fix typos.

2018-08-04  Werner Lemberg  <<EMAIL>>

	Fix clang warnings.

	* src/base/ftoutln.c (FT_Outline_EmboldenXY): Fix type of
	`orientation'.

	* src/gxvalid/gxvcommn.c (gx_lookup_value_read): Fix signature.

	* src/pcf/pcfread.c (pcf_get_encodings): Fix type of some variables.
	Add cast.

	* src/type1/t1load.c (parse_weight_vector): Fix cast.

2018-07-31  Werner Lemberg  <<EMAIL>>

	* src/cid/cidtoken.h: Handle `XUID' keyword.

2018-07-31  Werner Lemberg  <<EMAIL>>

	[cid] Trace PostScript dictionaries.

	* src/cid/cidload.c: Include FT_INTERNAL_POSTSCRIPT_AUX_H.
	(cid_load_keyword, cid_parse_font_matrix, parse_fd_array,
	parse_expansion_factor, cid_parse_dict): Add tracing calls.
	(parse_font_name): New function to trace `/FontName' keywords in
	/FDArray dict.
	(cid_field_records): Register `parse_font_name'.

2018-07-30  Werner Lemberg  <<EMAIL>>

	[cff] Fix typo.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9409

	* src/cff/cffdrivr.c (cff_get_cid_from_glyph_index): Fix boundary
	check.

2018-07-29  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_get_encodings): Another thinko.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9608

2018-07-28  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Fix Harmony memory management.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9501

	* src/smooth/ftgrays.c (ft_smooth_render_generic): Restore buffer
	after each rendering in case of failure.

2018-07-28  Werner Lemberg  <<EMAIL>>

	[type1] Avoid segfaults with `FT_Get_PS_Font_Value'.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9610

	* src/type1/t1driver.c (t1_ps_get_font_value): Protect against NULL.

2018-07-27  Werner Lemberg  <<EMAIL>>

	[truetype] Make `TT_Set_MM_Blend' idempotent (#54388).

	* src/truetype/ttgxvar.c (tt_set_mm_blend): Correctly set
	`face->doblend' if the current call to the function yields the same
	blend coordinates as the previous call.

2018-07-27  Werner Lemberg  <<EMAIL>>

	[psaux, type1]: More tracing improvements.

	* src/psaux/psintrp.c (cf2_interpT2CharString): Trace skipped
	outline commands.

	* src/psaux/t1decode.c (t1_decoder_parse_charstring): Fix
	missing case.
	(t1_decoder_parse_metrics): Make tracing output more compact.

	* src/type1/t1gload.c (T1_Compute_Max_Advance): Be less verbose.
	(T1_Get_Advances): Add tracing.

2018-07-25  Werner Lemberg  <<EMAIL>>

	[psaux, type1] Trace PostScript dictionaries and other things.

	The tracing of /Encoding, /Subrs, and /Charstrings is rudimentary
	right now.

	* src/psaux/psobjs.c (ps_parser_load_field,
	ps_parser_load_field_table): Add tracing calls.

	* src/psaux/t1decode.c (t1_decoder_parse_charstrings): Make tracing
	output more compact.

	* src/type1/t1gload.c (T1_Compute_Max_Advance, T1_Get_Advances): Add
	tracing messages.

	* src/type1/t1load.c (parse_blend_axis_types,
	parse_blend_design_positions, parse_blend_design_map,
	parse_weight_vector, t1_load_keyword, t1_parse_font_matrix,
	parse_encoding, parse_subrs, parse_charstrings, T1_Open_Face): Add
	tracing calls.

	* src/type1/t1objs.c (T1_Face_Init): Add tracing call.

	* src/sfnt/sfobjs.c (sfnt_init_face): Make tracing message more
	verbose.

2018-07-25  Werner Lemberg  <<EMAIL>>

	Fix minor ASAN run-time warnings.

	* src/base/ftutil.c (ft_mem_alloc, ft_mem_realloc): Only call
	`FT_MEM_ZERO' if we actually have a buffer.
	(ft_mem_dup): Only call `ft_memcpy' if we actually have a buffer.

2018-07-24  Alexei Podtelezhnikov  <<EMAIL>>

	[build] Fortify dllexport/dllimport attributes (#53969,#54330).

	We no longer use predefined _DLL, which can be defined for static
	builds too with /MD. We use DLL_EXPORT and DLL_IMPORT instead,
	following libtool convention.

	* CMakeLists.txt [WIN32], builds/windows/vc2010/freetype.vcxproj:
	Define DLL_EXPORT manually.

	* include/freetype/config/ftconfig.h, builds/unix/ftconfig.in,
	builds/vms/ftconfig.h, builds/windows/vc2010/index.html,
	src/base/ftver.rc: /_DLL/d, s/FT2_DLLIMPORT/DLL_IMPORT/.

2018-07-24  Werner Lemberg  <<EMAIL>>

	[type1] Check relationship between number of axes and designs.

	For Multiple Masters fonts we don't support intermediate designs;
	this implies that

	  number_of_designs == 2 ^^ number_of_axes

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9557

	* src/type1/t1load.c (T1_Open_Face): Ensure above constraint.
	(T1_Get_MM_Var): Remove now redundant test.

2018-07-24  Hin-Tak Leung  <<EMAIL>>

	[truetype] Match ttdebug's naming of instruction mnemonics.

	* src/truetype/ttinterp.c: The form used in ttdebug,
	"MDRP[G,B,W,?]", etc., is slightly more readable than
	"MDRP[00,01,02,03]".

2018-07-24  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_get_encodings): Thinko.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9561

2018-07-22  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_get_encodings): Check index of defaultChar.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9527

2018-07-22  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_load_font): Fix number of glyphs.

	This is an oversight of the module change 2018-07-21.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9524

2018-07-22  Werner Lemberg  <<EMAIL>>

	[cid] Sanitize `BlueShift' and `BlueFuzz'.

	This code is taken from the type1 module.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9510

	* src/cid/cidload.c (parse_fd_array): Set some private dict default
	values.
	(cid_face_open): Do the sanitizing.
	Fix some tracing messages.

2018-07-21  Werner Lemberg  <<EMAIL>>

	[pcf] Fix handling of the undefined glyph.

	This change makes the driver use the `defaultChar' property of PCF
	files.

	* src/pcf/pcf.h (PCF_FaceRec): Change type of `defaultChar' to
	unsigned.

	* src/pcf/pcfread.c (pcf_get_encodings): Read `defaultChar' as
	unsigned.
	Validate `defaultChar'.
	If `defaultChar' doesn't point to glyph index zero, swap glyphs with
	index zero and index `defaultChar' and adjust the encodings
	accordingly.

	* src/pcf/pcfdrivr.c (pcf_cmap_char_index, pcf_cmap_char_next,
	PCF_Glyph_Load): Undo change from 2002-06-16 which always enforced
	the first character in the font to be the default character.

2018-07-20  Armin Hasitzka  <<EMAIL>>

	Move the legacy fuzz target to the `freetype-testing' repository.

	It can now be found at

	  https://github.com/freetype/freetype2-testing/tree/master/fuzzing/src/legacy

	* src/tools/ftfuzzer: Remove this folder and its contents from the
	repository.

2018-07-20  Werner Lemberg  <<EMAIL>>

	[cff] Avoid left-shift of negative numbers (#54322).

	* src/cff/cffgload.c (cff_slot_load): Use multiplication.

2018-07-17  Werner Lemberg  <<EMAIL>>

	Allow FT_ENCODING_NONE for `FT_Select_Charmap'.

	This is a valid encoding tag for BDF, PCF, and Windows FNT, and
	there is no reason to disallow it for these formats.

	* src/base/ftobjs.c (FT_Select_Charmap): Implement it.

2018-07-17  Werner Lemberg  <<EMAIL>>

	* src/pcf/pcfread.c (pcf_get_encodings): Trace `defaultChar'.

2018-07-16  Armin Hasitzka  <<EMAIL>>

	* include/freetype/internal/ftcalc.h: Add macros for handling
	harmless over-/underflowing `FT_Int' values.

	* src/sfnt/sfdriver.c (fixed2float): Fix negation of
	`(int)(-2147483648)'.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9423

2018-07-16  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgxvar.c (tt_set_mm_blend): Fix off-by-one error.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9412

2018-07-12  Werner Lemberg  <<EMAIL>>

	* src/base/ftoutln.c (FT_Outline_Get_Orientation): Init `cbox'.

	Taken from patch #9667, written by Steve Langasek
	<<EMAIL>>.

	This fixes a build failure (most probably a bug in gcc) on ppc64el
	when building with -O3.

2018-07-05  Werner Lemberg  <<EMAIL>>

	Fix typo (#54238).

	* src/base/ftcolor.c (FT_Palette_Set_Foreground_Color)
	[!TT_CONFIG_OPTION_COLOR_LAYERS]: Add return value.

2018-07-05  Werner Lemberg  <<EMAIL>>

	Adjust table size comparisons (#54242).

	* src/sfnt/ttcpal.c (tt_face_load_cpal): Implement it.

2018-07-05  Werner Lemberg  <<EMAIL>>

	Fix more 32bit issues (#54208).

	* src/cff/cffload.c (cff_blend_build_vector): Convert assertion into
	run-time error.

	* src/truetype/ttgxvar.c (ft_var_to_normalized): Protect against
	numeric overflow.

2018-07-04  Werner Lemberg  <<EMAIL>>

	Fix 32bit build warnings (#54239).

	* src/base/ftbitmap.c (FT_Bitmap_Blend): Add casts to avoid signed
	vs. unsigned comparisons.

	* srb/sfnt/ttcolr.c (tt_face_get_colr_layer): Ditto.

2018-07-02  Jeff Carey  <<EMAIL>>

	* src/psnames/psmodule.c (ps_unicodes_init): Fix alloc debugging.

2018-07-02  Werner Lemberg  <<EMAIL>>

	s/palette_types/palette_flags/.

	Suggested by Behdad.

2018-07-02  Werner Lemberg  <<EMAIL>>

	Make `FT_Get_Color_Glyph_Layer' return FT_Bool.

	* include/freetype/freetype.h, src/base/ftobjs.c
	(FT_Get_Color_Glyph_Layer, FT_Render_Glyph_Internal): Updated.

	* include/freetype/internal/sfnt.h (TT_Get_Colr_Layer_Func),
	src/sfnt/ttcolr.h, src/sfnt/ttcolr.c (tt_face_get_colr_layer):
	Updated.

2018-07-01  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Get_Color_Glyph_Layer): Guard SFNT function.

	Reported by Behdad.

2018-06-28  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/fttrigon.c (FT_Tan): Improve accuracy.
	(FT_Vector_Rotate): Simplify.

2018-06-28  Alexei Podtelezhnikov  <<EMAIL>>

	* src/base/ftobjs.c (FT_Set_Charmap): Robustify.

2018-06-25  Werner Lemberg  <<EMAIL>>

	[truetype] Fix memory leak.

	* src/truetype/ttgxvar.c (TT_Vary_Apply_Glyph_Deltas): Add initializers.
	Fix typo in `goto' destination.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9071

2018-06-25  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgxvar.c (tt_face_vary_cvt): Add initializers.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=9070

2018-06-24  Werner Lemberg  <<EMAIL>>

	[truetype] Increase precision while applying VF deltas.

	It turned out that we incorrectly round CVT and glyph point deltas
	before accumulation, leading to severe positioning errors if there
	are many delta values to sum up.

	Problem reported by Akiem Helmling <<EMAIL>> and analyzed
	by Behdad.

	* src/truetype/ttgxvar.c (ft_var_readpackeddelta): Return deltas in
	16.16 format.
	(tt_face_var_cvt): Collect deltas in `cvt_deltas', which is a 16.16
	format array, and add the accumulated values to the CVT at the end
	of the function.
	(TT_Vary_Apply_Glyph_Deltas): Store data in `points_org' and
	`points_out' in 16.16 format.
	Collect deltas in `point_deltas_x' and `point_deltas_y', which are
	16.16 format arrays, and add the accumulated values to the glyph
	coordinates at the end of the function.

2018-06-24  Werner Lemberg  <<EMAIL>>

	New base function `FT_Matrix_Check' (#54019).

	* src/base/ftcalc.c (FT_Matrix_Check): New base function to properly
	reject degenerate font matrices.

	* include/freetype/internal/ftcalc.h: Updated.

	* src/cff/cffparse.c (cff_parse_font_matrix), src/cid/cidload.c
	(cid_parse_font_matrix), src/type1/t1load.c (t1_parse_font_matrix),
	src/type42/t42parse.c (t42_parse_font_matrix): Use
	`FT_Matrix_Check'.

2018-06-23  Werner Lemberg  <<EMAIL>>

	Fix typo.

	Reported by Behdad.

	* src/base/ftcolor.c (FT_Palette_Data_Get)
	[!TT_CONFIG_OPTION_COLOR_LAYERS]: s/apalette/apalette_data/.

2018-06-21  Werner Lemberg  <<EMAIL>>

	s/FT_PALETTE_USABLE_WITH_/FT_PALETTE_FOR_/.

	* include/freetype/ftcolor.h, include/freetype/internal/sfnt.h,
	src/sfnt/ttcolr.c: Do it.

2018-06-19  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix CPAL heap buffer overflow.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=8968

	* src/sfnt/ttcpal.c (tt_face_load_cpal): Guard CPAL version 1
	offsets.

2018-06-19  Werner Lemberg  <<EMAIL>>

	Doh.  Don't use CPAL or COLR data if tables are missing.

	Reported by Alexei.

	* src/sfnt/ttcolr.c (tt_face_get_colr_layer): Return immediately if
	`colr' is NULL.

	* src/sfnt/ttcpal.c (tt_face_palette_set): Return immediately, if
	`cpal' is NULL.

2018-06-17  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Introduce `FT_New_Glyph'.

	This function facilitates access to full capabilities of FreeType
	rendering engine for custom glyphs. This can be quite useful for
	consistent rendering of mathematical and chemical formulas, e.g.

	  https://bugs.chromium.org/p/chromium/issues/detail?id=757078

	* include/freetype/ftglyph.h, src/base/ftglyph.c (FT_New_Glyph): New
	function.

2018-06-17  Armin Hasitzka  <<EMAIL>>

	[bdf] Fix underflow of an unsigned value.

	bdflib.c:1562 could be reached with `font->glyphs_used == 0'.  That
	caused an underflow of the unsigned value which results in undefined
	behaviour.

	* src/bdf/bdflib.c (_bdf_parse_glyphs): Bail out earlier than before
	if the `ENCODING' keyword cannot be found.

2018-06-17  Werner Lemberg  <<EMAIL>>

	[base] Add tracing for `FT_Bitmap_Blend'.

	* include/freetype/internal/fttrace.h (trace_bitmap): New
	enumeration.

	* src/base/ftbitmap.c (FT_COMPONENT): Define.
	(FT_Bitmap_Blend): Add `FT_TRACE5' calls.

2018-06-17  Werner Lemberg  <<EMAIL>>

	s/trace_bitmap/trace_checksum/.

	* include/freetype/internal/fttrace.h: s/bitmap/checksum/.

	* src/base/ftobjs.c (FT_COMPONENT): s/trace_bitmap/trace_checksum/.
	Adjust code.

2018-06-16  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix color glyph layer loading.

	* src/sfnt/ttcolr.c (Colr): Add `table_size' field.
	(tt_face_load_colr): Set it.
	(tt_face_get_colr_layer): Check pointer limit for layer entries.

2018-06-16  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix color palette loading.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=8933

	* src/sfnt/ttcpal.c (Cpal): Add `table_size' field.
	(tt_face_load_cpal): Set it.
	(tt_face_palette_set): Check pointer limit for color entries.

2018-06-16  Werner Lemberg  <<EMAIL>>

	* src/base/ftbitmap.c (FT_Bitmap_Blend): Avoid integer overflow.

2018-06-16  Werner Lemberg  <<EMAIL>>

	Add `FT_Bitmap_Blend' API.

	Still missing: Support for negative bitmap pitch and subpixel offset
	of source bitmap.

	* include/freetype/ftbitmap.h, src/base/ftbitmap.c
	(FT_Bitmap_Blend): New function.

2018-06-14  Werner Lemberg  <<EMAIL>>

	Replace `FT_Get_GlyphLayers' with `FT_Get_Color_Glyph_Layer'.

	This avoids any additional allocation of COLR related structures in
	a glyph slot.

	* include/freetype/freetype.h (FT_Glyph_Layer, FT_Glyph_LayerRec,
	FT_Get_GlyphLayers): Removed.

	* include/freetype/internal/ftobjs.h (FT_Colr_InternalRec): Removed.
	(FT_Slot_InternalRec): Remove `color_layers'.

	* include/freetype/internal/sfnt.h (TT_Load_Colr_Layer_Func):
	Removed.
	(SFNT_Interface, FT_DEFINE_SFNT_INTERFACE): Remove
	`load_colr_layer'.

	* src/base/ftobjs.c (ft_glyph_slot_done): Updated.
	(FT_Render_Glyph_Internal): Use `FT_Get_Color_Glyph_Layer'.
	(FT_Get_GlyphLayers): Removed.

	* src/sfnt/sfdriver.c (sfnt_interface): Updated.

	* src/sfnt/ttcolr.c (tt_face_load_colr_layers): Removed.
	* src/sfnt/ttcolr.h: Updated.

	* src/truetype/ttgload.c (TT_Load_Glyph): Updated.

2018-06-14  Werner Lemberg  <<EMAIL>>

	Provide iterative API to access `COLR' data.

	This solution doesn't store any data in an `FT_GlyphSlot' object.

	* include/freetype/freetype.h (FT_LayerIterator): New structure.
	(FT_Get_Color_Glyph_Layer): New function.

	* include/freetype/internal/sfnt.h (TT_Get_Colr_Layer_Func): New
	function type.
	(SFNT_Interface, FT_DEFINE_SFNT_INTERFACE): Add it.

	* src/base/ftobjs.c (FT_Get_Color_Glyph_Layer): Implement it.

	* src/sfnt/ttcolr.c (tt_face_get_colr_layer): New function.
	* src/sfnt/ttcolr.h: Updated.

	* src/sfnt/sfdriver.c (sfnt_interface): Updated.

2018-06-14  Werner Lemberg  <<EMAIL>>

	Add glyph index and glyph load flags to glyph slot.

	* include/freetype/freetype.h (FT_GlyphSlotRec): Rename unused
	`reserved' field to `glyph_index'.

	* include/freetype/internal/ftobjs.h (FT_Slot_InternalRec): Add
	`load_flags' field.

	* src/base/ftobjs.c (FT_Load_Glyph): Set new fields.

2018-06-14  Werner Lemberg  <<EMAIL>>

	[sfnt] Move `CPAL' stuff into separate files.

	* src/sfnt/sfdriver.c: Include `ttcpal.h'.
	* src/sfnt/sfnt.c: Include `ttcpal.c'.

	* src/sfnt/ttcolr.c, src/sfnt/ttcolr.h: Move CPAL stuff to ...
	* src/sfnt/ttcpal.c, src/sfnt/ttcpal.c: ... these new files.

	* src/sfnt/Jamfile (_sources), src/sfnt/rules.mk (SFNT_DRV_SRC):
	Updated.

	* include/freetype/internal/fttrace.h: Add support for `colr' and
	`cpal'.
	Sort entries.

2018-06-13  Werner Lemberg  <<EMAIL>>

	[sfnt] Separate `CPAL' and `COLR' table handling.

	Later on we want to support the `SVG' table also, which needs `CPAL'
	(but not `COLR').

	* include/freetype/internal/sfnt.h (SFNT_Interface): Add `load_cpal'
	and `free_cpal' fields.
	(FT_DEFINE_SFNT_INTERFACE): Updated.

	* include/freetype/internal/tttypes.h (TT_FaceRec): Replace
	`colr_and_cpal' fields with `cpal' and `colr'.

	* src/sfnt/sfdriver.c (sfnt_interface): Updated.

	* src/sfnt/sfobjs.c (sfnt_load_face, sfnt_done_face): Updated.

	* src/sfnt/ttcolr.c (Colr, Cpal): Add `table' field.
	(ColrCpal): Removed.
	(tt_face_load_colr): Split off CPAL handling into...
	(tt_face_load_cpal): ... this new function.
	(tt_face_free_colr): Split off CPAL handling into...
	(tt_face_free_cpal): ... this new function.
	(tt_face_load_colr_layers, tt_face_palette_set): Updated.

	* src/sfnt/ttcolr.h: Updated.

	* src/truetype/ttgload.c (TT_Load_Glyph): Updated.

2018-06-12  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix `sizeof' thinko.

	* src/sfnt/ttcolr.c (tt_face_load_colr, tt_face_palette_set): Don't
	use `sizeof' for computing array limit.

2018-06-12  Werner Lemberg  <<EMAIL>>

	Finish CPAL/COLR support (4/4).

	* src/sfnt/ttcolr.c (tt_face_find_color): Removed.
	(tt_face_colr_blend_layer): Use `face->palette' instead of calling
	`tt_face_find_color'.
	Use and set text foreground color.

2018-06-12  Werner Lemberg  <<EMAIL>>

	Finish CPAL/COLR support (3/4).

	* src/base/ftcolor.c: Include FT_INTERNAL_SFNT_H.
	(FT_Palette_Select, FT_Palette_Set_Foreground_Color): Implement
	functions.

2018-06-12  Werner Lemberg  <<EMAIL>>

	Finish CPAL/COLR support (2/4).

	* src/sfnt/ttcolr.c (tt_face_palette_set): New function.
	(tt_face_load_colr): Allocate `face->palette' and call
	`tt_face_palette_set'.
	Adjust return error code in case of error.

	* src/sfnt/ttcolr.h: Updated.

	* include/freetype/internal/sfnt.h (TT_Set_Palette_Func): New
	function type.
	(SFNT_Interface, FT_DEFINE_SFNT_INTERFACE): Add it.

	* src/sfnt/sfdriver.c (sfnt_interface), src/sfnt/sfobjs.c
	(sfnt_done_face): Updated.

2018-06-12  Werner Lemberg  <<EMAIL>>

	Finish CPAL/COLR support (1/4).

	* include/freetype/internal/tttypes.h (TT_FaceRec): New fields
	`palette_index', `palette', `have_foreground_color' and
	`foreground_color'.

2018-06-12  Werner Lemberg  <<EMAIL>>

	[sfnt] Minor.

	* src/sfnt/ttcolr.c (tt_face_load_colr_layers):
	s/palette_index/palette_entry_index/ for consistency.
	Adjust return error code in case of error.

2018-06-11  Alexei Podtelezhnikov  <<EMAIL>>

	[raster] Clean up.

	* src/raster/ftraster.c (black_TWorker, SCALED, Set_High_Precision):
	Clean up after 5-level gray removal (8dc8635874).
	(Vertical_Sweep_Span): Be brief.

2018-06-10  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix compiler warnings.

	* src/sfnt/ttcolr.c (tt_face_load_colr, tt_face_load_colr_layers,
	tt_face_colr_blend_layer): Add `NULL' initializers.

2018-06-10  Werner Lemberg  <<EMAIL>>

	s/FT_Palette/FT_Palette_Data/, s/palette/palette_data/.

	* include/freetype/ftcolor.h, include/freetype/internal/tttypes.h,
	src/base/ftcolor.c, src/sfnt/sfobjs.c, src/sfnt/ttcolr.c: Updated.

2018-06-10  Nikolaus Waxweiler  <<EMAIL>>

	CMakeLists: also accept IOS_PLATFORM=SIMULATOR64

	This might be needed to build FreeType for the iOS simulator. See
	https://savannah.nongnu.org/bugs/index.php?54048. Patch contributed
	by Steve Robinson.

	* CMakeLists.txt: Accept IOS_PLATFORM=SIMULATOR64

2018-06-10  Werner Lemberg  <<EMAIL>>

	Implement `FT_Palette_Get'.

	* src/base/ftcolor.c: New file.

	* src/base/Jamefile (_sources), src/base/rules.mk (BASE_SRC),
	src/base/ftbase.c: Add `ftcolor.c'.

2018-06-10  Werner Lemberg  <<EMAIL>>

	* src/sfnt/ttcolr.c (tt_face_load_colr): Improve overflow checks.

2018-06-09  Alexei Podtelezhnikov  <<EMAIL>>

	[raster] Deal with pitch sign earlier.

	* src/raster/ftraster.c (black_TWorker): Remove unused `traceG',
	s/bTarget/bOrigin/.
	(Render_Glyph): Set `ras.bOrigin' at the bottom-left corner.
	(Vertical_Sweep_Init, {Vertical,Horizontal}_Sweep_{Span,Drop}):
	Updated accordingly.

2018-06-09  Werner Lemberg  <<EMAIL>>

	[sfnt] Read `CPAL' version 1 tables.

	* include/freetype/internal.tttypes.h: Include FT_COLOR_H.
	(TT_FaceRec): Add `palette' field.

	* src/sfnt/ttcolr.c: Include FT_COLOR_H.
	(Cpal): Remove all data covered by the new `palette' field in
	`TT_FaceRec'.
	(tt_face_load_colr): Updated.
	Read `CPAL' version 1 data.
	(tt_face_load_colr_layers, tt_face_find_color): Updated.

	* src/sfnt/sfobjs.c (sfnt_done_face): Free glyph color palette data.

2018-06-07  Alexei Podtelezhnikov  <<EMAIL>>

	[base] API for Harmony LCD rendering.

	This introduces `FT_Library_SetLcdGeometry' for setting up arbitrary
	LCD subpixel geometry including non-striped patterns.

	* src/base/ftlcdfil.c (FT_Library_SetLcdGeometry): New function.
	* include/freetype/ftlcdfil.h: Document it.
	* include/freetype/freetype.h: Minor.
	* include/freetype/ftchapters.h: Minor.

2018-06-06  Werner Lemberg  <<EMAIL>>

	ftcolor.h: Redesign API.

	While going to implement it I noticed that I need access to most of
	the `CPAL' elements; I thus plan to add a `cpal' field to
	`TT_FaceRec', which makes most of the previously suggested API
	functions obsolete because the fields will be directly accessible.

2018-06-06  Parth Wazurkar  <<EMAIL>>

	[bdf, pcf] Remove deprecated FT_FACE_FLAG_FAST_GLYPHS flag.

	* src/bdf/bdfdrivr.c (BDF_Face_Init): Remove deprecated
	FT_FACE_FLAG_FAST_GLYPHS flag.

	* src/pcf/pcfread.c (pcf_load_font): Remove deprecated
	FT_FACE_FLAG_FAST_GLYPHS flag.

2018-06-06  Werner Lemberg  <<EMAIL>>

	[smooth, raster] Limit bitmap size (#54019).

	* src/raster/ftraster.c [STANDALONE] (FT_Outline_Get_CBox): Add
	function.
	[!STANDALONE]: Include FT_OUTLINE_H.
	(ft_black_render): Compute CBox and reject glyphs larger than
	0xFFFF x 0xFFFF.

	* src/smooth/ftgrays.c (gray_raster_render): Reject glyphs larger
	than 0xFFFF x 0xFFFF.

2018-06-03  Armin Hasitzka  <<EMAIL>>

	* src/smooth/ftgrays.c (gray_convert_glyph): Remove unused variables.

2018-06-03  Werner Lemberg  <<EMAIL>>

	* src/tools/glnames.py (main): Emit header in `light' comment style.

2018-06-02  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Attempt to mitigate bug #54019.

	The robust rendering of estra large glyphs came with unbearable cost.
	The old way of bisecting should fail but fail faster.

	* src/smooth/ftgrays.c (gray_convert_glyph): Switch back to bisecting
	in y-direction.

2018-06-02  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttinterp.c (Ins_MIRP): Use SUB_LONG; avoid FT_ABS.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=8706

2018-06-02  Werner Lemberg  <<EMAIL>>

	* src/autofit/afwarp.h: Use AF_CONFIG_OPTION_USE_WARPER (#54033).

2018-05-31  Werner Lemberg  <<EMAIL>>

	* src/raster/ftraster.c (black_TWorker_): Remove `gTarget' field.

	This is no longer used.

2018-05-31  Werner Lemberg  <<EMAIL>>

	[sfnt] Get colors from `CPAL' table in right order (#54015).

	* src/sfnt/ttcolr.c (tt_face_find_color): Fix it.

2018-05-30  Werner Lemberg  <<EMAIL>>

	ftcolor.h: Improve API design, fix typos (#54011, #54014).

	* include/freetype/ftcolor.h (FT_Palette_Get_Names): Replace with...
	(FT_Palette_Get_Name_IDs): ... this function.
	(FT_Palette_Get_Entry_Names): Replace with...
	(FT_Palette_Get_Entry_Name_IDs): ... this function
	s/FT_Palette_Set_Foreground_COlor/FT_Palette_Set_Foreground_Color/.

2018-05-30  Armin Hasitzka  <<EMAIL>>

	Beautify a3cfed5e87232c933bdc64f43e8ebebcfd18b41b.

	* src/autofit/afloader.c (af_loader_load_glyph): Move the
	initialisationand declaration of variables into the if-block.

2018-05-30  Armin Hasitzka  <<EMAIL>>

	Fix pointer underflow.

	The declaration of `edge2' can be reached with `edge1 == NULL' and
	`axis->edges == 0' which results in undefined behaviour.

	* src/autofit/afloader.c (af_loader_load_glyph): Initialise `edge2'
	after checking `axis->num_edges > 1'.  `edge1 != NULL' can be assumed.

2018-05-30  Werner Lemberg  <<EMAIL>>

	Various minor color fixes.

	* include/freetype/config/ftheader.h (FT_COLOR_H): New macro.

	* include/freetype/internal/ftobjs.h (FT_Colr_Internal): Change
	type of `load_flags' to `FT_Int32'.

	* include/freetype/internal/sfnt.h (TT_Load_Colr_Layer_Func): Change
	type of `idx' to `FT_UInt'.
	(TT_Blend_Colr_Func): Change type of `color_index' to `FT_UInt'.

	* src/base/ftobjs.c (FT_Render_Glyph_Internal): Change type of
	`load_flags' to `FT_Int32'.

	* src/sfnt/ttcolr.c (find_base_glyph_record,
	tt_face_load_colr_layers): Change type of `glyph_id' to `FT_UInt'.
	(tt_face_find_color, tt_face_colr_blend_layer): Change type of
	`color_index' to `FT_UInt'.
	Fix signedness and type issues.

	* src/sfnt/ttcolr.h: Updated.

2018-05-25  Nikhil Ramakrishnan  <<EMAIL>>

	[docmaker] Fix missing `Defined in (...)' under Windows/Cygwin.

	This platform uses backslashes for paths, which docmaker didn't
	understand correctly.

	* src/tools/docmaker/tohtml.py (HtmlFormatter::blockEnter): Use
	`os.path.normpath' to normalize the path for the platform being
	used.

2018-05-24  Alexei Podtelezhnikov  <<EMAIL>>

	[smooth] Formalize Harmony LCD rendering.

	This generalizes magic outline shifts that make Harmony LCD
	rendering work in terms of precise two-dimensional RGB subpixel
	positions. These coordinates are now set in time of the `smooth'
	module initialization and later used to shift a glyph outline for
	rendering. FT_RENDER_MODE_LCD and FT_RENDER_MODE_LCD_V use the same
	coordinates. The letter, however, rotates them before using.
	The LCD bitmap padding is also calculated using these coordinates.

	* include/freetype/internal/ftobjs.h (FT_LibraryRec): New array field
	`lcd_geometry'.
	* src/base/ftlcdfil.c (ft_lcd_padding): Reworked.
	* src/base/ftobjs.c (ft_glyphslot_preset_bitmap): Updated accordingly.

	* src/smooth/ftsmooth.c [!FT_CONFIG_OPTION_SUBPIXEL_RENDERING]
	(ft_smooth_init): Initialize `lcd_geometry'.
	(ft_smooth_render_generic): Formalize outline shifts.

2018-05-22  Werner Lemberg  <<EMAIL>>

	[truetype] Reject elements of composites with invalid glyph indices.

	Reported as

	  https://bugs.chromium.org/p/oss-fuzz/issues/detail?id=8413

	* src/truetype/ttgload.c (TT_Load_Composite_Glyph): Implement it.

2018-05-22  Werner Lemberg  <<EMAIL>>

	* src/truetype/ttgload.c (TT_Load_Simple_Glyph): Trace # of points.

2018-05-20  Werner Lemberg  <<EMAIL>>

	* include/freetype/ftcolor.h: New file.

	This is an interface to the `CPAL' OpenType table.  No
	implementation yet.

2018-05-18  Alexei Podtelezhnikov  <<EMAIL>>

	* include/freetype/internal/ftcalc.h (FT_MSB): Verified `_MSC_VER'.

	Actually `_BitScanReverse' is available since VS2005.

2018-05-18  Werner Lemberg  <<EMAIL>>

	* include/freetype/internal/ftcalc.h (FT_MSB): Use `_MSC_VER' value.

	Older VC versions don't provide `_BitScanReverse'.  We test for VC
	2013.

	Reported by John Emmas <<EMAIL>>.

2018-05-17  Werner Lemberg  <<EMAIL>>

	s/inline/__inline/ for MSVC.

	Reported by John Emmas <<EMAIL>>.

	* include/freetype/internal/ftcalc.h (FT_MSB) [_MSC_VER]: Do it.

2018-05-16  Werner Lemberg  <<EMAIL>>

	Add function `FT_Get_GlyphLayers' to access `COLR' table data.

	* include/freetype/internal/ftobjs.h (FT_Glyph_LayerRec): Move this
	structure to...
	* include/freetype/freetype.h (FT_Glyph_LayerRec): ... this
	header file.
	(FT_Glyph_Layer): New typedef.
	Update code to use it where appropriate.

	* src/base/ftobjs.c (FT_Get_GlyphLayers): New function.

2018-05-15  Alexei Podtelezhnikov  <<EMAIL>>

	[base] Fix mono bitmap presetting (#53896).

	It is rather fundamental to set monochrome bitmap based on rounded
	CBox because the b/w rasterizer turns on pixels when their centers are
	inside the glyph outline. The dropout control is unpredictable and can
	distort narrow glyphs if the bitmap is too wide.

	Reported by Chris Liddell.

	* src/base/ftobjs.c (ft_glyphslot_preset_bitmap): If BBox boundaries
	are too close, adjust them before rounding.

2018-05-15  Werner Lemberg  <<EMAIL>>

	[psaux] Fix compiler warning (#53915).

	* src/psaux/psft.c (cf2_freeT1SeacComponent): Do it.

2018-05-15  Werner Lemberg  <<EMAIL>>

	[sfnt] Fix memory leak in handling `COLR' data.

	* src/truetype/ttgload.c (TT_Load_Glyph): Free old `layers' array
	before reassigning allocated memory.
	Only allocate `color_layers' if we don't have one already.

2018-05-15  Werner Lemberg  <<EMAIL>>

	[sfnt] If `COLR' is present, don't assume that all glyphs use it.

	* src/sfnt/ttcolr.c (tt_face_load_colr_layers): Return FT_Err_Ok if
	current glyph is not a `COLR' base glyph.

	* src/truetype/ttgload.c (TT_Load_Glyph): Don't allocate
	`color_layers' if there are no color layers.

2018-05-14  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Load_Glyph): Fix signature of `pixel_modes'.

2018-05-14  Werner Lemberg  <<EMAIL>>

	Provide dummy functions if `TT_CONFIG_OPTION_SFNT_NAMES' is not set.

	* src/base/ftsnames.c [!TT_CONFIG_OPTION_SFNT_NAMES]: Implement it.

2018-05-13  Werner Lemberg  <<EMAIL>>

	* src/base/ftobjs.c (FT_Load_Glyph): Improve tracing.

2018-05-13  Shao Yu Zhang  <<EMAIL>>
	    Werner Lemberg  <<EMAIL>>

	[sfnt] Preliminary support of colored layer outlines (#44689).

	This commit enables OpenType's COLR/CPAL table handling; a typical
	application are color emojis that can be scaled to any size.

	If the color palette does not exist or is invalid, the rendering
	step rasterizes the outline instead.  The current implementation
	assumes that the foreground is black.

	Enable this by defining option TT_CONFIG_OPTION_COLOR_LAYERS.

	There are still some issues with metrics; additionally, an API to
	fetch color layers is missing.

	* devel/ftoption.h, include/freetype/config/ftoption.h
	(TT_CONFIG_OPTION_COLOR_LAYERS): New macro.

	* include/freetype/internal/ftobjs.h (FT_Glyph_LayerRec,
	FT_Colr_InternalRec): New structures.
	(FT_Slot_InternalRec): Add `color_layers' field.

	* include/freetype/internal/sfnt.h (TT_Load_Colr_Layer_Func,
	TT_Blend_Colr_Func): New function types.
	(SFNT_Interface): Add `load_colr', `free_colr', `load_colr_layer',
	and `colr_blend' fields.

	* include/freetype/internal/tttypes.h (TT_FaceRec): Add
	`colr_and_cpal' field.

	* include/freetype/internal/tttags. (TTAG_COLR, TTAG_CPAL): New
	macros.

	* src/sfnt/ttcolr.c, src/sfnt/ttcolr.h: New files.

	* src/base/ftobjs.c (ft_glyphslot_done, FT_Render_Glyph_Internal):
	Handle glyph color layers.

	* src/sfnt/Jamfile (_sources), src/sfnt/rules.mk (SFNT_DRV_SRC): Add
	`ttcolr.c'.

	* src/sfnt/sfdriver.c: Include `ttcolr.h'.
	(PUT_COLOR_LAYERS): New macro.
	Update call to `FT_DEFINE_SFNT_INTERFACE'.

	* src/sfnt/sfnt.c: Include `ttcolr.c'.

	* src/sfnt/sfobjs.c (sfnt_load_face): Load `COLR' and `CPAL' tables.
	(sfnt_done_face): Updated.

	* src/truetype/ttgload.c (TT_Load_Glyph): Handle color layers.

2018-05-12  Arkady Shapkin  <<EMAIL>>

	Use MS VC++'s _BitScanReverse to calculate MSB (patch #9636).

	* include/freetype/internal/ftcalc.h (FT_MSB) [_MSC_VER]: Implement
	it.

2018-05-10  Alan Coopersmith  <<EMAIL>>

	Fix DLL compilation on Solaris.

	AC_COMPILE_IFELSE only tries to compile a `*.c' to a `*.o'.  The
	Solaris Studio 12.1 through 12.5 compilers see the
	`-fvisibility=hidden' flag, but ignore it with a warning of:

	  cc: Warning: Option -fvisibility=hidden passed to ld,
	               if ld is invoked, ignored otherwise

	AC_LINK_IFELSE does the compile and then tries to link the result,
	at which point the Solaris linker will issue an error:

	  ld: fatal: option '-fvisibility=hidden' is incompatible with
	      building a dynamic executable

	If we don't use AC_LINK_IFELSE to catch the error, then configure
	will fail further tests which attempt to link, such as those testing
	dependencies like `libbz2'.

	Also, don't try adding `-fvisibility' if we have already added
	`-xldscope', just use one of them, since Sun Studio 12 and earlier
	compilers only issue a warning, and don't try passing through to the
	linker to generate an error, so AC_LINK_IFELSE doesn't catch them.

	Tested on Solaris 11.4 beta with compiler versions:

	  Sun Studio 8 (Sun C 5.5)
	  Sun Studio 10 (Sun C 5.7)
	  Sun Studio 11 (Sun C 5.8)
	  Sun Studio 12 (Sun C 5.9)
	  Sun Studio 12.1 (Sun C 5.10)
	  Oracle Solaris Studio 12.2 (Sun C 5.11)
	  Oracle Solaris Studio 12.3 (Sun C 5.12)
	  Oracle Solaris Studio 12.4 (Sun C 5.13)
	  Oracle Developer Studio 12.5 (Sun C 5.14)
	  Oracle Developer Studio 12.6 (Sun C 5.15)
	  gcc 5.5.0
	  gcc 7.3.0

	and verified the libfreetype.so.6 generated by each of those
	compilers exported the same set of symbols.

	* builds/unix/configure.raw: Implement it.

2018-05-08  Werner Lemberg  <<EMAIL>>

	[autofit] Avoid potential SEGV if running out of memory.

	Problem reported by Shailesh Mistry <<EMAIL>>.

	* src/autofit/afshaper.c (af_shaper_buf_create,
	af_shaper_buf_destroy) [!FT_CONFIG_OPTION_USE_HARFBUZZ]: Don't
	allocate and free a four-byte buffer.  Instead, make those functions
	no-ops; the calling functions will provide a pointer to a buffer
	instead.

	* src/autofit/afcjk.c (af_cjk_metrics_init_widths,
	af_cjk_metrics_init_blues, af_cjk_metrics_check_digits),
	src/autofit/aflatin.c (af_latin_metrics_init_widths,
	af_latin_metrics_init_blues, af_latin_metrics_check_digits)
	[!FT_CONFIG_OPTION_USE_HARFBUZZ]: Use pointer to local variable for
	`shaper_buf'.

2018-05-07  Nikolaus Waxweiler  <<EMAIL>>

	[cmake] Allow using project as subfolder in other project.

	* CMakeLists.txt: Test for CMake build directory being different
	from source directory.  Provide other parts of the build system
	access the full include directory.

2018-05-07  Werner Lemberg  <<EMAIL>>

	[build] Suppress configure's `nothing to be done' message.

	This is due to calling the configure script via `make' (within the
	top-level `configure' wrapper script).  The same can happen for all
	other secondary make targets that are used to only modify the
	primary one, e.g., `make setup devel'.

	* builds/dos/detect.mk (emx, turboc, watcom, borlandc, borlandc16),
	builds/os2/detect (visualage, watcom, borlandc, devel),
	builds/unix/detect.mk (devel, lcc, unix), builds/windows/detect.mk
	(visualc, watcom, visualage, lcc, mingw32, bcc32, devel-bcc,
	devel-gcc): Use no-op recipe.

2018-05-04  suzuki toshiya  <<EMAIL>>

	Support symbol visibility features of Sun / Oracle C compilers.

	Reported by Kiyoshi Kanazawa:
	https://lists.gnu.org/archive/html/freetype-devel/2018-05/msg00008.html
	Thanks to the suggestions by Alexei and Alan Coopersmith.

	* builds/unix/configure.raw: Check if "-xldscope=hidden" is
	accepted, and if so, it is added to CFLAGS.  This is the option
	making Sun / Oracle C compilers hide the symbols from global
	scope.
	* include/freetype/config/ftconfig.h: Use "__global" prefix
	for FT_EXPORT() macro, if SunPro C is newer than Sun ONE
	Studio 8 (2003).
	* builds/unix/ftconfig.in: Ditto.
	* builds/vms/ftconfig.h: Ditto.

2018-05-02  Nikolaus Waxweiler  <<EMAIL>>

	Unbreak CMake Windows installation

	* CMakeLists.txt: Generate ftconfig.h on non-UNIX.

2018-05-02  Werner Lemberg  <<EMAIL>>

	Remove FT_CONFIG_OPTION_PIC and related code.

	*/* [FT_CONFIG_OPTION_PIC]: Remove all code guarded by this
	preprocessor symbol.

	*/*: Replace `XXX_GET' macros (which could be either a function in
	PIC mode or an array in non-PIC mode) with `xxx' arrays.

	* include/freetype/internal/ftpic.h, src/autofit/afpic.c,
	src/autofit/afpic.h, src/base/basepic.c, src/base/basepic.h,
	src/base/ftpic.c, src/cff/cffpic.c, src/cff/cffpic.h,
	src/pshinter/pshpic.c, src/pshinter/pshpic.h, src/psnames/pspic.c,
	src/psnames/pspic.h, src/raster/rastpic.c, src/raster/rastpic.h,
	src/sfnt/sfntpic.c, src/sfnt/sfntpic.h, src/smooth/ftspic.c,
	src/smooth/ftspic.h, src/truetype/ttpic.c, src/truetype/ttpic.h:
	Removed.


----------------------------------------------------------------------------

Copyright (C) 2018-2020 by
David Turner, Robert Wilhelm, and Werner Lemberg.

This file is part of the FreeType project, and may only be used, modified,
and distributed under the terms of the FreeType project license,
LICENSE.TXT.  By continuing to use, modify, or distribute this file you
indicate that you have read the license and understand and accept it
fully.


Local Variables:
version-control: never
coding: utf-8
End:
