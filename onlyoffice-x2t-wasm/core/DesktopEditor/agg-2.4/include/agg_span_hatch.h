﻿#ifndef AGG_SPAN_HATCH_INCLUDED
#define AG<PERSON>_<PERSON>AN_HATCH_INCLUDED

#include <stdlib.h>
#include <string>
#include "agg_math.h"
#include "agg_array.h"
#include "agg_trans_affine.h"

namespace agg
{
	#define HATCH_TX_SIZE			8
	#define HATCH_TX_SIZE_MASK		7
	#define HATCH_TX_COUNT			54

	// 8 * 8 * 54
	#define HATCH_RESOURCE_SIZE		3456

	static const BYTE c_resource_hatches[HATCH_RESOURCE_SIZE] = {
		/* cross */
		0,0,0,0,0,0,0,0,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,0,0,0,0,0,0,0,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,

		/* dashDnDiag */
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		0,1,1,1,0,1,1,1,
		1,0,1,1,1,0,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,0,1,1,1,0,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* dashHorz */
		0,0,0,0,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,0,0,0,0,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* dashUpDiag */
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,0,1,1,1,0,
		1,1,0,1,1,1,0,1,
		1,0,1,1,1,0,1,1,
		0,1,1,1,0,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* dashVert */
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,1,0,1,1,1,

		/* diagBrick */
		1,1,1,1,1,1,1,0,
		1,1,1,1,1,1,0,1,
		1,1,1,1,1,0,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,0,0,1,1,1,
		1,1,0,1,1,0,1,1,
		1,0,1,1,1,1,0,1,
		0,1,1,1,1,1,1,0,

		/* diagCross */
		0,1,1,1,1,1,0,1,
		1,0,1,1,1,0,1,1,
		1,1,0,1,0,1,1,1,
		1,1,1,0,1,1,1,1,
		1,1,0,1,0,1,1,1,
		1,0,1,1,1,0,1,1,
		0,1,1,1,1,1,0,1,
		1,1,1,1,1,1,1,0,

		/* divot */
		1,1,1,1,1,1,1,1,
		1,1,1,0,1,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,0,1,1,1,1,
		1,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,0,
		0,1,1,1,1,1,1,1,

		/* dkDnDiag */
		0,0,1,1,0,0,1,1,
		1,0,0,1,1,0,0,1,
		1,1,0,0,1,1,0,0,
		0,1,1,0,0,1,1,0,
		0,0,1,1,0,0,1,1,
		1,0,0,1,1,0,0,1,
		1,1,0,0,1,1,0,0,
		0,1,1,0,0,1,1,0,

		/* dkHorz */
		0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* dkUpDiag */
		1,1,0,0,1,1,0,0,
		1,0,0,1,1,0,0,1,
		0,0,1,1,0,0,1,1,
		0,1,1,0,0,1,1,0,
		1,1,0,0,1,1,0,0,
		1,0,0,1,1,0,0,1,
		0,0,1,1,0,0,1,1,
		0,1,1,0,0,1,1,0,

		/* dkVert */
		0,0,1,1,0,0,1,1,
		0,0,1,1,0,0,1,1,
		0,0,1,1,0,0,1,1,
		0,0,1,1,0,0,1,1,
		0,0,1,1,0,0,1,1,
		0,0,1,1,0,0,1,1,
		0,0,1,1,0,0,1,1,
		0,0,1,1,0,0,1,1,

		/* dnDiag */
		0,1,1,1,0,1,1,1,
		1,0,1,1,1,0,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,0,1,1,1,0,
		0,1,1,1,0,1,1,1,
		1,0,1,1,1,0,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,0,1,1,1,0,

		/* dotDmnd */
		0,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,1,1,1,1,1,

		/* dotGrid */
		0,1,0,1,0,1,0,1,
		1,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* horz */
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* horzBrick */
		0,0,0,0,0,0,0,0,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,0,0,0,0,0,0,0,
		1,1,1,1,0,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,1,0,1,1,1,

		/* lgCheck */
		0,0,0,0,1,1,1,1,
		0,0,0,0,1,1,1,1,
		0,0,0,0,1,1,1,1,
		0,0,0,0,1,1,1,1,
		1,1,1,1,0,0,0,0,
		1,1,1,1,0,0,0,0,
		1,1,1,1,0,0,0,0,
		1,1,1,1,0,0,0,0,

		/* lgConfetti */
		0,1,0,0,1,1,1,0,
		1,1,0,0,1,1,1,1,
		1,1,1,1,1,1,0,0,
		1,1,1,0,0,1,0,0,
		0,0,1,0,0,1,1,1,
		0,0,1,1,1,1,1,1,
		1,1,1,1,0,0,1,1,
		0,1,1,1,0,0,1,0,

		/* lgGrid */
		0,0,0,0,0,0,0,0,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,

		/* ltDnDiag */
		0,1,1,1,0,1,1,1,
		1,0,1,1,1,0,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,0,1,1,1,0,
		0,1,1,1,0,1,1,1,
		1,0,1,1,1,0,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,0,1,1,1,0,

		/* ltHorz */
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* ltUpDiag */
		1,1,1,0,1,1,1,0,
		1,1,0,1,1,1,0,1,
		1,0,1,1,1,0,1,1,
		0,1,1,1,0,1,1,1,
		1,1,1,0,1,1,1,0,
		1,1,0,1,1,1,0,1,
		1,0,1,1,1,0,1,1,
		0,1,1,1,0,1,1,1,

		/* ltVert */
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,

		/* narHorz */
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,
		0,0,0,0,0,0,0,0,
		1,1,1,1,1,1,1,1,

		/* narVert */
		1,0,1,0,1,0,1,0,
		1,0,1,0,1,0,1,0,
		1,0,1,0,1,0,1,0,
		1,0,1,0,1,0,1,0,
		1,0,1,0,1,0,1,0,
		1,0,1,0,1,0,1,0,
		1,0,1,0,1,0,1,0,
		1,0,1,0,1,0,1,0,

		/* openDmnd */
		0,1,1,1,1,1,0,1,
		1,0,1,1,1,0,1,1,
		1,1,0,1,0,1,1,1,
		1,1,1,0,1,1,1,1,
		1,1,0,1,0,1,1,1,
		1,0,1,1,1,0,1,1,
		0,1,1,1,1,1,0,1,
		1,1,1,1,1,1,1,0,

		/* pct10 */
		0,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,1,1,1,1,1,
		0,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,1,1,1,1,1,

		/* pct20 */
		0,1,1,1,0,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,1,1,1,1,1,
		0,1,1,1,0,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,0,1,1,1,0,1,
		1,1,1,1,1,1,1,1,

		/* pct25 */
		0,1,1,1,0,1,1,1,
		1,1,0,1,1,1,0,1,
		0,1,1,1,0,1,1,1,
		1,1,0,1,1,1,0,1,
		0,1,1,1,0,1,1,1,
		1,1,0,1,1,1,0,1,
		0,1,1,1,0,1,1,1,
		1,1,0,1,1,1,0,1,

		/* pct30 */
		0,1,0,1,0,1,0,1,
		1,0,1,1,1,0,1,1,
		0,1,0,1,0,1,0,1,
		1,1,1,0,1,1,1,0,
		0,1,0,1,0,1,0,1,
		1,0,1,1,1,0,1,1,
		0,1,0,1,0,1,0,1,
		1,1,1,0,1,1,1,0,

		/* pct40 */
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,0,1,0,
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,1,1,0,
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,0,1,0,
		0,1,0,1,0,1,0,1,
		1,1,1,0,1,0,1,0,

		/* pct5 */
		0,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,0,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* pct50 */
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,0,1,0,
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,0,1,0,
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,0,1,0,
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,0,1,0,

		/* pct60 */
		0,0,0,1,0,0,0,1,
		1,0,1,0,1,0,1,0,
		0,1,0,0,0,1,0,0,
		1,0,1,0,1,0,1,0,
		0,0,0,1,0,0,0,1,
		1,0,1,0,1,0,1,0,
		0,1,0,0,0,1,0,0,
		1,0,1,0,1,0,1,0,

		/* pct70 */
		1,0,0,0,1,0,0,0,
		0,0,1,0,0,0,1,0,
		1,0,0,0,1,0,0,0,
		0,0,1,0,0,0,1,0,
		1,0,0,0,1,0,0,0,
		0,0,1,0,0,0,1,0,
		1,0,0,0,1,0,0,0,
		0,0,1,0,0,0,1,0,

		/* pct75 */
		1,0,0,0,1,0,0,0,
		0,0,0,0,0,0,0,0,
		0,0,1,0,0,0,1,0,
		0,0,0,0,0,0,0,0,
		1,0,0,0,1,0,0,0,
		0,0,0,0,0,0,0,0,
		0,0,1,0,0,0,1,0,
		0,0,0,0,0,0,0,0,

		/* pct80 */
		0,0,0,1,0,0,0,0,
		0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,1,
		0,0,0,0,0,0,0,0,
		0,0,0,1,0,0,0,0,
		0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,1,
		0,0,0,0,0,0,0,0,

		/* pct90 */
		0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,
		0,0,0,0,1,0,0,0,
		0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,
		0,0,0,0,0,0,0,0,
		1,0,0,0,0,0,0,0,

		/* plaid */
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,0,1,0,
		0,1,0,1,0,1,0,1,
		1,0,1,0,1,0,1,0,
		0,0,0,0,1,1,1,1,
		0,0,0,0,1,1,1,1,
		0,0,0,0,1,1,1,1,
		0,0,0,0,1,1,1,1,

		/* shingle */
		1,1,1,1,1,1,0,0,
		0,1,1,1,1,0,1,1,
		1,0,1,1,0,1,1,1,
		1,1,0,0,1,1,1,1,
		1,1,1,1,0,0,1,1,
		1,1,1,1,1,1,0,1,
		1,1,1,1,1,1,1,0,
		1,1,1,1,1,1,1,0,

		/* smCheck */
		0,1,1,0,0,1,1,0,
		1,0,0,1,1,0,0,1,
		1,0,0,1,1,0,0,1,
		0,1,1,0,0,1,1,0,
		0,1,1,0,0,1,1,0,
		1,0,0,1,1,0,0,1,
		1,0,0,1,1,0,0,1,
		0,1,1,0,0,1,1,0,

		/* smConfetti */
		0,1,1,1,1,1,1,1,
		1,1,1,1,0,1,1,1,
		1,0,1,1,1,1,1,1,
		1,1,1,1,1,1,0,1,
		1,1,1,0,1,1,1,1,
		1,1,1,1,1,1,1,0,
		1,1,0,1,1,1,1,1,
		1,1,1,1,1,0,1,1,

		/* smGrid */
		0,0,0,0,0,0,0,0,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,0,0,0,0,0,0,0,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,

		/* solidDmnd */
		1,1,1,0,1,1,1,1,
		1,1,0,0,0,1,1,1,
		1,0,0,0,0,0,1,1,
		0,0,0,0,0,0,0,1,
		1,0,0,0,0,0,1,1,
		1,1,0,0,0,1,1,1,
		1,1,1,0,1,1,1,1,
		1,1,1,1,1,1,1,1,

		/* sphere */
		1,0,0,0,1,0,0,0,
		0,1,1,1,0,1,1,0,
		0,1,1,1,0,0,0,0,
		0,1,1,1,0,0,0,0,
		1,0,0,0,1,0,0,0,
		0,1,1,0,0,1,1,1,
		0,0,0,0,0,1,1,1,
		0,0,0,0,0,1,1,1,

		/* trellis */
		0,0,0,0,0,0,0,0,
		1,0,0,1,1,0,0,1,
		0,0,0,0,0,0,0,0,
		0,1,1,0,0,1,1,0,
		0,0,0,0,0,0,0,0,
		1,0,0,1,1,0,0,1,
		0,0,0,0,0,0,0,0,
		0,1,1,0,0,1,1,0,

		/* upDiag */
		1,1,0,0,1,1,0,0,
		1,0,0,1,1,0,0,1,
		0,0,1,1,0,0,1,1,
		0,1,1,0,0,1,1,0,
		1,1,0,0,1,1,0,0,
		1,0,0,1,1,0,0,1,
		0,0,1,1,0,0,1,1,
		0,1,1,0,0,1,1,0,

		/* vert */
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,
		0,1,1,1,0,1,1,1,

		/* wave */
		1,1,1,1,1,1,1,1,
		1,1,1,0,0,1,1,1,
		1,1,0,1,1,0,1,0,
		0,0,1,1,1,1,1,1,
		1,1,1,1,1,1,1,1,
		1,1,1,0,0,1,1,1,
		1,1,0,1,1,0,1,0,
		0,0,1,1,1,1,1,1,

		/* wdDnDiag */
		0,0,1,1,1,1,1,0,
		0,0,0,1,1,1,1,1,
		1,0,0,0,1,1,1,1,
		1,1,0,0,0,1,1,1,
		1,1,1,0,0,0,1,1,
		1,1,1,1,0,0,0,1,
		1,1,1,1,1,0,0,0,
		0,1,1,1,1,1,0,0,

		/* wdUpDiag */
		0,1,1,1,1,1,0,0,
		1,1,1,1,1,0,0,0,
		1,1,1,1,0,0,0,1,
		1,1,1,0,0,0,1,1,
		1,1,0,0,0,1,1,1,
		1,0,0,0,1,1,1,1,
		0,0,0,1,1,1,1,1,
		0,0,1,1,1,1,1,0,

		/* weave */
		0,1,1,1,0,1,1,1,
		1,0,1,0,1,0,1,1,
		1,1,0,1,1,1,0,1,
		1,0,1,1,1,0,1,1,
		0,1,1,1,0,1,1,1,
		1,1,1,0,1,0,1,1,
		1,1,0,1,1,1,0,1,
		1,0,1,0,1,1,1,0,

		/* zigZag */
		0,1,1,1,1,1,1,0,
		1,0,1,1,1,1,0,1,
		1,1,0,1,1,0,1,1,
		1,1,1,0,0,1,1,1,
		0,1,1,1,1,1,1,0,
		1,0,1,1,1,1,0,1,
		1,1,0,1,1,0,1,1,
		1,1,1,0,0,1,1,1
	};

	static const std::wstring c_resource_hatches_names[HATCH_TX_COUNT] = {
		L"cross",
		L"dashDnDiag",
		L"dashHorz",
		L"dashUpDiag",
		L"dashVert",
		L"diagBrick",
		L"diagCross",
		L"divot",
		L"dkDnDiag",
		L"dkHorz",
		L"dkUpDiag",
		L"dkVert",
		L"dnDiag",
		L"dotDmnd",
		L"dotGrid",
		L"horz",
		L"horzBrick",
		L"lgCheck",
		L"lgConfetti",
		L"lgGrid",
		L"ltDnDiag",
		L"ltHorz",
		L"ltUpDiag",
		L"ltVert",
		L"narHorz",
		L"narVert",
		L"openDmnd",
		L"pct10",
		L"pct20",
		L"pct25",
		L"pct30",
		L"pct40",
		L"pct5",
		L"pct50",
		L"pct60",
		L"pct70",
		L"pct75",
		L"pct80",
		L"pct90",
		L"plaid",
		L"shingle",
		L"smCheck",
		L"smConfetti",
		L"smGrid",
		L"solidDmnd",
		L"sphere",
		L"trellis",
		L"upDiag",
		L"vert",
		L"wave",
		L"wdDnDiag",
		L"wdUpDiag",
		L"weave",
		L"zigZag"
	};

	template <class color_type>
	static void GetHatchPattern(const std::wstring& name, color_type* data, const color_type& c1, const color_type& c2)
	{
		int offset = 0;
		int size = HATCH_TX_SIZE * HATCH_TX_SIZE;

		for (int i = 0; i < HATCH_TX_COUNT; ++i)
		{
			if (c_resource_hatches_names[i] == name)
			{
				offset = i * size;
				break;
			}
		}
		for (int i = 0; i < size; ++i)
		{
			*data++ = (c_resource_hatches[offset + i] == 1) ? c2 : c1;
		}
	}

	template<class ColorT>
    class agg_span_hatch
	{
	public:
		typedef ColorT color_type;

	private:
		enum
		{
			StateInit = 0,
			StateReady = 1,
		};

	private:
		int m_state;
		agg::trans_affine m_trans;

		int				m_offset;
		color_type		m_color1;
		color_type		m_color2;

		double Ax;
		double Bx;
		double Cx;

		double Ay;
		double By;
		double Cy;

	public:
		agg_span_hatch()
			: m_state( StateInit ), 
			m_offset( 0 )
		{
			Ax = 0;
			Bx = 0;
			Cx = 0;

			Ay = 0;
			By = 0;
			Cy = 0;
		}

		void SetDirection(const std::wstring& name, const rect_d& bounds, const agg::trans_affine& trans, const color_type& color1, const color_type& color2)
		{
			m_trans = trans;
			m_color1 = color1;
			m_color2 = color2;
			
			for (int i = 0; i < HATCH_TX_COUNT; ++i)
			{
				if (c_resource_hatches_names[i] == name)
				{
					m_offset = i * HATCH_TX_SIZE * HATCH_TX_SIZE;
					break;
				}
			}

			double x0 = bounds.x1;
			double y0 = bounds.y1;

			double x1 = bounds.x2;
			double y1 = y0;

			double x2 = x0;
			double y2 = bounds.y2;

			trans.transform(&x0, &y0);
			trans.transform(&x1, &y1);
			trans.transform(&x2, &y2);

			double dFactor1 = _hypot(x1 - x0, y1 - y0);
			if (dFactor1 < FLT_EPSILON)
			{
				Ax = 0;
				Bx = 0;
				Cx = 0;
			}
			else
			{
				Ax = (y1 - y0) / dFactor1;
				Bx = (x0 - x1) / dFactor1;
				Cx = (y0 * (x1 - x0) - x0 * (y1 - y0)) / dFactor1;
			}

			double dFactor2 = _hypot(x2 - x0, y2 - y0);
			if (dFactor2 < FLT_EPSILON)
			{
				Ay = 0;
				By = 0;
				Cy = 0;
			}
			else
			{
				Ay = (y2 - y0) / dFactor2;
				By = (x0 - x2) / dFactor2;
				Cy = (y0 * (x2 - x0) - x0 * (y2 - y0)) / dFactor2;
			}			
		}
		
        void prepare()
		{
			if( m_state != StateReady )
			{
				m_state = StateReady;
			}
		}

        void generate( color_type* span, int x, int y, unsigned len)
		{
			double plusX = Bx * y + Cx;
			double plusY = By * y + Cy;

			for( unsigned count = 0; count < len; ++count, ++x )
			{
#if 1 // NN
				int _x = (int)(Ax * x + plusX + 0.5);
				int _y = (int)(Ay * x + plusY + 0.5);
				_x = _x & HATCH_TX_SIZE_MASK;
				_y = _y & HATCH_TX_SIZE_MASK;
				
				BYTE val = c_resource_hatches[m_offset + (_y << 3) + _x];

				if (val)
					*span++ = m_color2;
				else
					*span++ = m_color1;
#endif

			}
		}
	};
}

#endif
