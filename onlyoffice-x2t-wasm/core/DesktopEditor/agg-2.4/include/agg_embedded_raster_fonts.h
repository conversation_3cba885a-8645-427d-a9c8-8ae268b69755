﻿//----------------------------------------------------------------------------
// Anti-Grain Geometry - Version 2.4
// Copyright (C) 2002-2005 Maxim Shemanarev (http://www.antigrain.com)
//
// Permission to copy, use, modify, sell and distribute this software 
// is granted provided this copyright notice appears in all copies. 
// This software is provided "as is" without express or implied
// warranty, and with no claim as to its suitability for any purpose.
//
//----------------------------------------------------------------------------
// Contact: <EMAIL>
//          <EMAIL>
//          http://www.antigrain.com
//----------------------------------------------------------------------------

#ifndef AGG_EMBEDDED_RASTER_FONTS_INCLUDED
#define AGG_EMBEDDED_RASTER_FONTS_INCLUDED

#include "agg_basics.h"

namespace agg
{
    extern const int8u gse4x6[];
    extern const int8u gse4x8[];
    extern const int8u gse5x7[];
    extern const int8u gse5x9[];
    extern const int8u gse6x12[];
    extern const int8u gse6x9[];
    extern const int8u gse7x11[];
    extern const int8u gse7x11_bold[];
    extern const int8u gse7x15[];
    extern const int8u gse7x15_bold[];
    extern const int8u gse8x16[];
    extern const int8u gse8x16_bold[];
    extern const int8u mcs11_prop[];
    extern const int8u mcs11_prop_condensed[];
    extern const int8u mcs12_prop[];
    extern const int8u mcs13_prop[];
    extern const int8u mcs5x10_mono[];
    extern const int8u mcs5x11_mono[];
    extern const int8u mcs6x10_mono[];
    extern const int8u mcs6x11_mono[];
    extern const int8u mcs7x12_mono_high[];
    extern const int8u mcs7x12_mono_low[];
    extern const int8u verdana12[];
    extern const int8u verdana12_bold[];
    extern const int8u verdana13[];
    extern const int8u verdana13_bold[];
    extern const int8u verdana14[];
    extern const int8u verdana14_bold[];
    extern const int8u verdana16[];
    extern const int8u verdana16_bold[];
    extern const int8u verdana17[];
    extern const int8u verdana17_bold[];
    extern const int8u verdana18[];
    extern const int8u verdana18_bold[];
}

#endif
