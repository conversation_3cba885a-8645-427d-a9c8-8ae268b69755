﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectName>agg2d</ProjectName>
    <ProjectGuid>{617F9069-5E37-4B80-9A3A-E77AFC4CC7AD}</ProjectGuid>
    <RootNamespace>agg2d</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <UseOfAtl>false</UseOfAtl>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <UseOfAtl>false</UseOfAtl>
    <CharacterSet>NotSet</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.CPP.UpgradeFromVC71.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>14.0.25431.1</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <ExtensionsToDeleteOnClean />
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
    <IncludePath>D:\_Work\core\DesktopEditor\freetype-2.5.2;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <ExtensionsToDeleteOnClean />
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <ExtensionsToDeleteOnClean />
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <ExtensionsToDeleteOnClean />
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>include;font_win32_tt;..\Expat;..\Objects\Font\FreeType;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;AGG_BMP_ALPHA_BLEND;_CRT_SECURE_NO_WARNINGS;_CRT_NON_CONFORMING_SWPRINTFS;GRAPHICS_NO_USE_DYNAMIC_LIBRARY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <PrecompiledHeaderOutputFile />
      <AssemblerListingLocation>$(Configuration)\agg2d.pch</AssemblerListingLocation>
      <ObjectFileName>$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(Configuration)\agg2D.pdb</ProgramDataBaseFileName>
      <BrowseInformation />
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <DisableSpecificWarnings>4005;4311;4312;%(DisableSpecificWarnings)</DisableSpecificWarnings>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)$(ProjectName).lib</OutputFile>
      <AdditionalLibraryDirectories>..\Expat\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>true</IgnoreAllDefaultLibraries>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <AdditionalOptions> /Wp64 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>include;font_win32_tt;..\Expat;..\Objects\Font\FreeType;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;AGG_BMP_ALPHA_BLEND;_CRT_SECURE_NO_WARNINGS;_CRT_NON_CONFORMING_SWPRINTFS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <StructMemberAlignment>Default</StructMemberAlignment>
      <PrecompiledHeaderOutputFile />
      <AssemblerListingLocation>$(Platform)\$(Configuration)\agg2d.pch</AssemblerListingLocation>
      <ObjectFileName>$(Platform)\$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(Platform)\$(Configuration)\agg2D.pdb</ProgramDataBaseFileName>
      <BrowseInformation />
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)$(ProjectName).lib</OutputFile>
      <AdditionalLibraryDirectories>..\Expat\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>true</IgnoreAllDefaultLibraries>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <AdditionalIncludeDirectories>include;font_win32_tt;..\Expat;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;AGG_BMP_ALPHA_BLEND;_CRT_SECURE_NO_WARNINGS;_CRT_NON_CONFORMING_SWPRINTFS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <PrecompiledHeaderOutputFile />
      <AssemblerListingLocation>$(Configuration)\agg2d.pch</AssemblerListingLocation>
      <ObjectFileName>$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(Configuration)\agg2D.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <AdditionalDependencies>libexpat.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(ProjectName).lib</OutputFile>
      <AdditionalLibraryDirectories>..\Expat\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <AdditionalIncludeDirectories>include;font_win32_tt;..\Expat;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;AGG_BMP_ALPHA_BLEND;_CRT_SECURE_NO_WARNINGS;_CRT_NON_CONFORMING_SWPRINTFS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <PrecompiledHeaderOutputFile>..\Release\agg2d.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>$(Platform)\$(Configuration)\agg2d.pch</AssemblerListingLocation>
      <ObjectFileName>$(Platform)\$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(Platform)\$(Configuration)\agg2D.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Lib>
      <AdditionalDependencies>libexpat.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>$(OutDir)$(ProjectName).lib</OutputFile>
      <AdditionalLibraryDirectories>..\Expat\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="src\agg_arc.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_arrowhead.cpp" />
    <ClCompile Include="src\agg_bezier_arc.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_bspline.cpp" />
    <ClCompile Include="src\agg_curves.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_embedded_raster_fonts.cpp" />
    <ClCompile Include="src\agg_gsv_text.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_image_filters.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_line_aa_basics.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_line_profile_aa.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_rounded_rect.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_sqrt_tables.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_trans_affine.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_trans_double_path.cpp" />
    <ClCompile Include="src\agg_trans_single_path.cpp" />
    <ClCompile Include="src\agg_trans_warp_magnifier.cpp" />
    <ClCompile Include="src\agg_vcgen_bspline.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
    </ClCompile>
    <ClCompile Include="src\agg_vcgen_contour.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
    </ClCompile>
    <ClCompile Include="src\agg_vcgen_dash.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
    </ClCompile>
    <ClCompile Include="src\agg_vcgen_markers_term.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
    </ClCompile>
    <ClCompile Include="src\agg_vcgen_smooth_poly1.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
    </ClCompile>
    <ClCompile Include="src\agg_vcgen_stroke.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\agg_vpgen_clip_polygon.cpp" />
    <ClCompile Include="src\agg_vpgen_clip_polyline.cpp" />
    <ClCompile Include="src\agg_vpgen_segmentator.cpp" />
    <ClCompile Include="src\ctrl\agg_cbox_ctrl.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\ctrl\agg_slider_ctrl.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
    <ClCompile Include="src\platform\win32\agg_win32_bmp.cpp">
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <AssemblerListingLocation Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</AssemblerListingLocation>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\</ObjectFileName>
      <ProgramDataBaseFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.\Debug\agg2D.pdb</ProgramDataBaseFileName>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Neither</FavorSizeOrSpeed>
      <Optimization Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MaxSpeed</Optimization>
      <FavorSizeOrSpeed Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Neither</FavorSizeOrSpeed>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\agg_vcgen_bspline.h" />
    <ClInclude Include="include\agg_vcgen_contour.h" />
    <ClInclude Include="include\agg_vcgen_dash.h" />
    <ClInclude Include="include\agg_vcgen_markers_term.h" />
    <ClInclude Include="include\agg_vcgen_smooth_poly1.h" />
    <ClInclude Include="include\agg_vcgen_stroke.h" />
    <ClInclude Include="include\agg_vcgen_vertex_sequence.h" />
    <ClInclude Include="include\platform\win32\agg_win32_bmp.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>