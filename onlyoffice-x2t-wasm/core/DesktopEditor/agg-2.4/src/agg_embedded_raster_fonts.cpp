﻿//----------------------------------------------------------------------------
// Anti-Grain Geometry - Version 2.4
// Copyright (C) 2002-2005 Maxim Shemanarev (http://www.antigrain.com)
//
// Permission to copy, use, modify, sell and distribute this software 
// is granted provided this copyright notice appears in all copies. 
// This software is provided "as is" without express or implied
// warranty, and with no claim as to its suitability for any purpose.
//
//----------------------------------------------------------------------------
// Contact: <EMAIL>
//          <EMAIL>
//          http://www.antigrain.com
//----------------------------------------------------------------------------

#include "agg_embedded_raster_fonts.h"

namespace agg
{

    const int8u gse4x6[] = 
    {
        6, 0, 32, 128-32,

        0x00,0x00,0x07,0x00,0x0e,0x00,0x15,0x00,0x1c,0x00,0x23,0x00,0x2a,0x00,0x31,0x00,0x38,0x00,
        0x3f,0x00,0x46,0x00,0x4d,0x00,0x54,0x00,0x5b,0x00,0x62,0x00,0x69,0x00,0x70,0x00,0x77,0x00,
        0x7e,0x00,0x85,0x00,0x8c,0x00,0x93,0x00,0x9a,0x00,0xa1,0x00,0xa8,0x00,0xaf,0x00,0xb6,0x00,
        0xbd,0x00,0xc4,0x00,0xcb,0x00,0xd2,0x00,0xd9,0x00,0xe0,0x00,0xe7,0x00,0xee,0x00,0xf5,0x00,
        0xfc,0x00,0x03,0x01,0x0a,0x01,0x11,0x01,0x18,0x01,0x1f,0x01,0x26,0x01,0x2d,0x01,0x34,0x01,
        0x3b,0x01,0x42,0x01,0x49,0x01,0x50,0x01,0x57,0x01,0x5e,0x01,0x65,0x01,0x6c,0x01,0x73,0x01,
        0x7a,0x01,0x81,0x01,0x88,0x01,0x8f,0x01,0x96,0x01,0x9d,0x01,0xa4,0x01,0xab,0x01,0xb2,0x01,
        0xb9,0x01,0xc0,0x01,0xc7,0x01,0xce,0x01,0xd5,0x01,0xdc,0x01,0xe3,0x01,0xea,0x01,0xf1,0x01,
        0xf8,0x01,0xff,0x01,0x06,0x02,0x0d,0x02,0x14,0x02,0x1b,0x02,0x22,0x02,0x29,0x02,0x30,0x02,
        0x37,0x02,0x3e,0x02,0x45,0x02,0x4c,0x02,0x53,0x02,0x5a,0x02,0x61,0x02,0x68,0x02,0x6f,0x02,
        0x76,0x02,0x7d,0x02,0x84,0x02,0x8b,0x02,0x92,0x02,0x99,0x02,

        4, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x21 '!'
        0x40,0x40,0x40,0x00,0x40,0x00,

        4, // 0x22 '"'
        0xa0,0xa0,0x00,0x00,0x00,0x00,

        4, // 0x23 '#'
        0x60,0xf0,0x60,0xf0,0x60,0x00,

        4, // 0x24 '$'
        0x40,0x60,0xc0,0x60,0xc0,0x40,

        4, // 0x25 '%'
        0xa0,0x20,0x40,0x80,0xa0,0x00,

        4, // 0x26 '&'
        0xe0,0xa0,0x50,0xa0,0xd0,0x00,

        4, // 0x27 '''
        0x40,0x40,0x00,0x00,0x00,0x00,

        4, // 0x28 '('
        0x20,0x40,0x40,0x40,0x20,0x00,

        4, // 0x29 ')'
        0x40,0x20,0x20,0x20,0x40,0x00,

        4, // 0x2a '*'
        0xa0,0x40,0xe0,0x40,0xa0,0x00,

        4, // 0x2b '+'
        0x40,0x40,0xe0,0x40,0x40,0x00,

        4, // 0x2c ','
        0x00,0x00,0x00,0x40,0x40,0x80,

        4, // 0x2d '-'
        0x00,0x00,0xe0,0x00,0x00,0x00,

        4, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x40,0x00,

        4, // 0x2f '/'
        0x10,0x20,0x20,0x40,0x40,0x80,

        4, // 0x30 '0'
        0xe0,0xa0,0xa0,0xa0,0xe0,0x00,

        4, // 0x31 '1'
        0x40,0xc0,0x40,0x40,0xe0,0x00,

        4, // 0x32 '2'
        0xe0,0xa0,0x20,0x40,0xe0,0x00,

        4, // 0x33 '3'
        0xe0,0x20,0x40,0x20,0xe0,0x00,

        4, // 0x34 '4'
        0xa0,0xa0,0xe0,0x20,0x20,0x00,

        4, // 0x35 '5'
        0xe0,0x80,0xc0,0x20,0xc0,0x00,

        4, // 0x36 '6'
        0x40,0x80,0xe0,0xa0,0xe0,0x00,

        4, // 0x37 '7'
        0xe0,0xa0,0x20,0x40,0x40,0x00,

        4, // 0x38 '8'
        0xe0,0xa0,0x40,0xa0,0xe0,0x00,

        4, // 0x39 '9'
        0xe0,0xa0,0xe0,0x20,0xc0,0x00,

        4, // 0x3a ':'
        0x00,0x40,0x00,0x40,0x00,0x00,

        4, // 0x3b ';'
        0x00,0x40,0x00,0x40,0x40,0x80,

        4, // 0x3c '<'
        0x20,0x40,0x80,0x40,0x20,0x00,

        4, // 0x3d '='
        0x00,0xe0,0x00,0xe0,0x00,0x00,

        4, // 0x3e '>'
        0x80,0x40,0x20,0x40,0x80,0x00,

        4, // 0x3f '?'
        0xc0,0x20,0x40,0x00,0x40,0x00,

        4, // 0x40 '@'
        0x40,0xa0,0xe0,0xe0,0x80,0x60,

        4, // 0x41 'A'
        0x40,0xa0,0xe0,0xa0,0xa0,0x00,

        4, // 0x42 'B'
        0xc0,0xa0,0xc0,0xa0,0xc0,0x00,

        4, // 0x43 'C'
        0x60,0x80,0x80,0x80,0x60,0x00,

        4, // 0x44 'D'
        0xc0,0xa0,0xa0,0xa0,0xc0,0x00,

        4, // 0x45 'E'
        0xe0,0x80,0xc0,0x80,0xe0,0x00,

        4, // 0x46 'F'
        0xe0,0x80,0xc0,0x80,0x80,0x00,

        4, // 0x47 'G'
        0x60,0x80,0xa0,0xa0,0x40,0x00,

        4, // 0x48 'H'
        0xa0,0xa0,0xe0,0xa0,0xa0,0x00,

        4, // 0x49 'I'
        0xe0,0x40,0x40,0x40,0xe0,0x00,

        4, // 0x4a 'J'
        0x20,0x20,0x20,0x20,0xa0,0x40,

        4, // 0x4b 'K'
        0xa0,0xa0,0xc0,0xc0,0xa0,0x00,

        4, // 0x4c 'L'
        0x80,0x80,0x80,0x80,0xe0,0x00,

        4, // 0x4d 'M'
        0xa0,0xe0,0xa0,0xa0,0xa0,0x00,

        4, // 0x4e 'N'
        0x90,0xd0,0xb0,0x90,0x90,0x00,

        4, // 0x4f 'O'
        0x40,0xa0,0xa0,0xa0,0x40,0x00,

        4, // 0x50 'P'
        0xc0,0xa0,0xa0,0xc0,0x80,0x00,

        4, // 0x51 'Q'
        0x40,0xa0,0xa0,0xa0,0x60,0x00,

        4, // 0x52 'R'
        0xc0,0xa0,0xa0,0xc0,0xa0,0x00,

        4, // 0x53 'S'
        0x60,0x80,0x40,0x20,0xc0,0x00,

        4, // 0x54 'T'
        0xe0,0x40,0x40,0x40,0x40,0x00,

        4, // 0x55 'U'
        0xa0,0xa0,0xa0,0xa0,0xe0,0x00,

        4, // 0x56 'V'
        0xa0,0xa0,0xa0,0xa0,0x40,0x00,

        4, // 0x57 'W'
        0xa0,0xa0,0xa0,0xe0,0xa0,0x00,

        4, // 0x58 'X'
        0xa0,0xa0,0x40,0xa0,0xa0,0x00,

        4, // 0x59 'Y'
        0xa0,0xa0,0x40,0x40,0x40,0x00,

        4, // 0x5a 'Z'
        0xe0,0x20,0x40,0x80,0xe0,0x00,

        4, // 0x5b '['
        0xc0,0x80,0x80,0x80,0xc0,0x00,

        4, // 0x5c '\'
        0x80,0x40,0x40,0x20,0x20,0x10,

        4, // 0x5d ']'
        0xc0,0x40,0x40,0x40,0xc0,0x00,

        4, // 0x5e '^'
        0x40,0xa0,0x00,0x00,0x00,0x00,

        4, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0xf0,

        4, // 0x60 '`'
        0x40,0x20,0x00,0x00,0x00,0x00,

        4, // 0x61 'a'
        0x00,0x60,0xa0,0xa0,0x70,0x00,

        4, // 0x62 'b'
        0x80,0x80,0xc0,0xa0,0xc0,0x00,

        4, // 0x63 'c'
        0x00,0x60,0x80,0x80,0x60,0x00,

        4, // 0x64 'd'
        0x20,0x20,0x60,0xa0,0x60,0x00,

        4, // 0x65 'e'
        0x00,0x40,0xe0,0x80,0x60,0x00,

        4, // 0x66 'f'
        0x20,0x40,0xe0,0x40,0x40,0x00,

        4, // 0x67 'g'
        0x00,0x60,0xa0,0x60,0x20,0xc0,

        4, // 0x68 'h'
        0x80,0x80,0xc0,0xa0,0xa0,0x00,

        4, // 0x69 'i'
        0x40,0x00,0xc0,0x40,0xe0,0x00,

        4, // 0x6a 'j'
        0x40,0x00,0xc0,0x40,0x40,0x80,

        4, // 0x6b 'k'
        0x80,0x80,0xa0,0xc0,0xa0,0x00,

        4, // 0x6c 'l'
        0xc0,0x40,0x40,0x40,0xe0,0x00,

        4, // 0x6d 'm'
        0x00,0xa0,0xf0,0xf0,0x90,0x00,

        4, // 0x6e 'n'
        0x00,0xc0,0xa0,0xa0,0xa0,0x00,

        4, // 0x6f 'o'
        0x00,0x40,0xa0,0xa0,0x40,0x00,

        4, // 0x70 'p'
        0x00,0xc0,0xa0,0xc0,0x80,0x80,

        4, // 0x71 'q'
        0x00,0x60,0xa0,0x60,0x20,0x20,

        4, // 0x72 'r'
        0x00,0xa0,0x50,0x40,0x40,0x00,

        4, // 0x73 's'
        0x00,0x60,0xc0,0x20,0xc0,0x00,

        4, // 0x74 't'
        0x40,0x40,0xe0,0x40,0x60,0x00,

        4, // 0x75 'u'
        0x00,0xa0,0xa0,0xa0,0x60,0x00,

        4, // 0x76 'v'
        0x00,0xa0,0xa0,0xa0,0x40,0x00,

        4, // 0x77 'w'
        0x00,0xa0,0xa0,0xe0,0xa0,0x00,

        4, // 0x78 'x'
        0x00,0xa0,0x40,0xa0,0xa0,0x00,

        4, // 0x79 'y'
        0x00,0xa0,0xa0,0x60,0x20,0xc0,

        4, // 0x7a 'z'
        0x00,0xe0,0x40,0x80,0xe0,0x00,

        4, // 0x7b '{'
        0x30,0x20,0xc0,0x20,0x30,0x00,

        4, // 0x7c '|'
        0x40,0x40,0x00,0x40,0x40,0x40,

        4, // 0x7d '}'
        0xc0,0x40,0x30,0x40,0xc0,0x00,

        4, // 0x7e '~'
        0x50,0xa0,0x00,0x00,0x00,0x00,

        4, // 0x7f ''
        0x00,0x60,0x90,0xf0,0x00,0x00,
        0
    };

    const int8u gse4x8[] = 
    {
        8, 0, 32, 128-32,

        0x00,0x00,0x09,0x00,0x12,0x00,0x1b,0x00,0x24,0x00,0x2d,0x00,0x36,0x00,0x3f,0x00,0x48,0x00,
        0x51,0x00,0x5a,0x00,0x63,0x00,0x6c,0x00,0x75,0x00,0x7e,0x00,0x87,0x00,0x90,0x00,0x99,0x00,
        0xa2,0x00,0xab,0x00,0xb4,0x00,0xbd,0x00,0xc6,0x00,0xcf,0x00,0xd8,0x00,0xe1,0x00,0xea,0x00,
        0xf3,0x00,0xfc,0x00,0x05,0x01,0x0e,0x01,0x17,0x01,0x20,0x01,0x29,0x01,0x32,0x01,0x3b,0x01,
        0x44,0x01,0x4d,0x01,0x56,0x01,0x5f,0x01,0x68,0x01,0x71,0x01,0x7a,0x01,0x83,0x01,0x8c,0x01,
        0x95,0x01,0x9e,0x01,0xa7,0x01,0xb0,0x01,0xb9,0x01,0xc2,0x01,0xcb,0x01,0xd4,0x01,0xdd,0x01,
        0xe6,0x01,0xef,0x01,0xf8,0x01,0x01,0x02,0x0a,0x02,0x13,0x02,0x1c,0x02,0x25,0x02,0x2e,0x02,
        0x37,0x02,0x40,0x02,0x49,0x02,0x52,0x02,0x5b,0x02,0x64,0x02,0x6d,0x02,0x76,0x02,0x7f,0x02,
        0x88,0x02,0x91,0x02,0x9a,0x02,0xa3,0x02,0xac,0x02,0xb5,0x02,0xbe,0x02,0xc7,0x02,0xd0,0x02,
        0xd9,0x02,0xe2,0x02,0xeb,0x02,0xf4,0x02,0xfd,0x02,0x06,0x03,0x0f,0x03,0x18,0x03,0x21,0x03,
        0x2a,0x03,0x33,0x03,0x3c,0x03,0x45,0x03,0x4e,0x03,0x57,0x03,

        4, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x21 '!'
        0x00,0x40,0x40,0x40,0x40,0x00,0x40,0x00,

        4, // 0x22 '"'
        0x00,0xa0,0xa0,0x00,0x00,0x00,0x00,0x00,

        4, // 0x23 '#'
        0x60,0x60,0xf0,0x60,0x60,0xf0,0x60,0x60,

        4, // 0x24 '$'
        0x40,0x60,0xc0,0xc0,0x60,0x60,0xc0,0x40,

        4, // 0x25 '%'
        0x00,0xa0,0x20,0x40,0x40,0x80,0xa0,0x00,

        4, // 0x26 '&'
        0x00,0x40,0xa0,0xa0,0x40,0xb0,0xa0,0x70,

        4, // 0x27 '''
        0x00,0x40,0x40,0x00,0x00,0x00,0x00,0x00,

        4, // 0x28 '('
        0x20,0x40,0x80,0x80,0x80,0x80,0x40,0x20,

        4, // 0x29 ')'
        0x80,0x40,0x20,0x20,0x20,0x20,0x40,0x80,

        4, // 0x2a '*'
        0x00,0xa0,0x40,0xe0,0x40,0xa0,0x00,0x00,

        4, // 0x2b '+'
        0x00,0x40,0x40,0xe0,0x40,0x40,0x00,0x00,

        4, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x80,

        4, // 0x2d '-'
        0x00,0x00,0x00,0xe0,0x00,0x00,0x00,0x00,

        4, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,

        4, // 0x2f '/'
        0x10,0x10,0x20,0x20,0x40,0x40,0x80,0x80,

        4, // 0x30 '0'
        0x00,0xe0,0xa0,0xa0,0xa0,0xa0,0xe0,0x00,

        4, // 0x31 '1'
        0x00,0x40,0xc0,0x40,0x40,0x40,0xe0,0x00,

        4, // 0x32 '2'
        0x00,0xe0,0xa0,0x20,0x40,0x80,0xe0,0x00,

        4, // 0x33 '3'
        0x00,0xe0,0x20,0x40,0x20,0x20,0xe0,0x00,

        4, // 0x34 '4'
        0x00,0x60,0xa0,0xa0,0xf0,0x20,0x20,0x00,

        4, // 0x35 '5'
        0x00,0xe0,0x80,0xc0,0x20,0x20,0xc0,0x00,

        4, // 0x36 '6'
        0x00,0x40,0x80,0xe0,0xa0,0xa0,0xe0,0x00,

        4, // 0x37 '7'
        0x00,0xe0,0xa0,0x20,0x40,0x40,0x40,0x00,

        4, // 0x38 '8'
        0x00,0xe0,0xa0,0x40,0xa0,0xa0,0xe0,0x00,

        4, // 0x39 '9'
        0x00,0xe0,0xa0,0xe0,0x20,0x20,0x40,0x00,

        4, // 0x3a ':'
        0x00,0x00,0x40,0x00,0x00,0x40,0x00,0x00,

        4, // 0x3b ';'
        0x00,0x00,0x40,0x00,0x00,0x40,0x40,0x80,

        4, // 0x3c '<'
        0x00,0x20,0x40,0x80,0x40,0x20,0x00,0x00,

        4, // 0x3d '='
        0x00,0x00,0xe0,0x00,0xe0,0x00,0x00,0x00,

        4, // 0x3e '>'
        0x00,0x80,0x40,0x20,0x40,0x80,0x00,0x00,

        4, // 0x3f '?'
        0x00,0x40,0xa0,0x20,0x40,0x00,0x40,0x00,

        4, // 0x40 '@'
        0x00,0x40,0xa0,0xe0,0xe0,0x80,0x60,0x00,

        4, // 0x41 'A'
        0x00,0x40,0xa0,0xa0,0xe0,0xa0,0xa0,0x00,

        4, // 0x42 'B'
        0x00,0xc0,0xa0,0xc0,0xa0,0xa0,0xc0,0x00,

        4, // 0x43 'C'
        0x00,0x40,0xa0,0x80,0x80,0xa0,0x40,0x00,

        4, // 0x44 'D'
        0x00,0xc0,0xa0,0xa0,0xa0,0xa0,0xc0,0x00,

        4, // 0x45 'E'
        0x00,0xe0,0x80,0xc0,0x80,0x80,0xe0,0x00,

        4, // 0x46 'F'
        0x00,0xe0,0x80,0xc0,0x80,0x80,0x80,0x00,

        4, // 0x47 'G'
        0x00,0x60,0x80,0xa0,0xa0,0xa0,0x40,0x00,

        4, // 0x48 'H'
        0x00,0xa0,0xa0,0xe0,0xa0,0xa0,0xa0,0x00,

        4, // 0x49 'I'
        0x00,0xe0,0x40,0x40,0x40,0x40,0xe0,0x00,

        4, // 0x4a 'J'
        0x00,0x20,0x20,0x20,0x20,0xa0,0x40,0x00,

        4, // 0x4b 'K'
        0x00,0xa0,0xa0,0xc0,0xc0,0xa0,0xa0,0x00,

        4, // 0x4c 'L'
        0x00,0x80,0x80,0x80,0x80,0x80,0xe0,0x00,

        4, // 0x4d 'M'
        0x00,0xa0,0xe0,0xa0,0xa0,0xa0,0xa0,0x00,

        4, // 0x4e 'N'
        0x00,0x90,0x90,0xd0,0xb0,0x90,0x90,0x00,

        4, // 0x4f 'O'
        0x00,0x40,0xa0,0xa0,0xa0,0xa0,0x40,0x00,

        4, // 0x50 'P'
        0x00,0xc0,0xa0,0xa0,0xc0,0x80,0x80,0x00,

        4, // 0x51 'Q'
        0x00,0x40,0xa0,0xa0,0xa0,0xa0,0x60,0x00,

        4, // 0x52 'R'
        0x00,0xc0,0xa0,0xa0,0xc0,0xc0,0xa0,0x00,

        4, // 0x53 'S'
        0x00,0x60,0x80,0x40,0x20,0x20,0xc0,0x00,

        4, // 0x54 'T'
        0x00,0xe0,0x40,0x40,0x40,0x40,0x40,0x00,

        4, // 0x55 'U'
        0x00,0xa0,0xa0,0xa0,0xa0,0xa0,0x40,0x00,

        4, // 0x56 'V'
        0x00,0xa0,0xa0,0xa0,0xa0,0x40,0x40,0x00,

        4, // 0x57 'W'
        0x00,0xa0,0xa0,0xa0,0xa0,0xe0,0xa0,0x00,

        4, // 0x58 'X'
        0x00,0xa0,0xa0,0x40,0xa0,0xa0,0xa0,0x00,

        4, // 0x59 'Y'
        0x00,0xa0,0xa0,0x40,0x40,0x40,0x40,0x00,

        4, // 0x5a 'Z'
        0x00,0xe0,0x20,0x40,0x40,0x80,0xe0,0x00,

        4, // 0x5b '['
        0xc0,0x80,0x80,0x80,0x80,0x80,0x80,0xc0,

        4, // 0x5c '\'
        0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,

        4, // 0x5d ']'
        0xc0,0x40,0x40,0x40,0x40,0x40,0x40,0xc0,

        4, // 0x5e '^'
        0x00,0x40,0xa0,0x00,0x00,0x00,0x00,0x00,

        4, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf0,

        4, // 0x60 '`'
        0x00,0x40,0x20,0x00,0x00,0x00,0x00,0x00,

        4, // 0x61 'a'
        0x00,0x00,0x60,0xa0,0xa0,0xa0,0x70,0x00,

        4, // 0x62 'b'
        0x00,0x80,0x80,0xc0,0xa0,0xa0,0xc0,0x00,

        4, // 0x63 'c'
        0x00,0x00,0x40,0xa0,0x80,0xa0,0x40,0x00,

        4, // 0x64 'd'
        0x00,0x20,0x20,0x60,0xa0,0xa0,0x60,0x00,

        4, // 0x65 'e'
        0x00,0x00,0x40,0xa0,0xe0,0x80,0x60,0x00,

        4, // 0x66 'f'
        0x00,0x20,0x40,0x40,0xe0,0x40,0x40,0x00,

        4, // 0x67 'g'
        0x00,0x00,0x60,0xa0,0xa0,0x60,0x20,0xc0,

        4, // 0x68 'h'
        0x00,0x80,0x80,0xc0,0xa0,0xa0,0xa0,0x00,

        4, // 0x69 'i'
        0x00,0x40,0x00,0xc0,0x40,0x40,0xe0,0x00,

        4, // 0x6a 'j'
        0x00,0x40,0x00,0xc0,0x40,0x40,0x40,0x80,

        4, // 0x6b 'k'
        0x00,0x80,0x80,0xa0,0xc0,0xc0,0xa0,0x00,

        4, // 0x6c 'l'
        0x00,0xc0,0x40,0x40,0x40,0x40,0xe0,0x00,

        4, // 0x6d 'm'
        0x00,0x00,0xa0,0xf0,0xf0,0xf0,0x90,0x00,

        4, // 0x6e 'n'
        0x00,0x00,0xc0,0xa0,0xa0,0xa0,0xa0,0x00,

        4, // 0x6f 'o'
        0x00,0x00,0x40,0xa0,0xa0,0xa0,0x40,0x00,

        4, // 0x70 'p'
        0x00,0x00,0xc0,0xa0,0xa0,0xc0,0x80,0x80,

        4, // 0x71 'q'
        0x00,0x00,0x60,0xa0,0xa0,0x60,0x20,0x20,

        4, // 0x72 'r'
        0x00,0x00,0xa0,0x50,0x40,0x40,0x40,0x00,

        4, // 0x73 's'
        0x00,0x00,0x60,0x80,0x40,0x20,0xc0,0x00,

        4, // 0x74 't'
        0x00,0x40,0x40,0xe0,0x40,0x40,0x20,0x00,

        4, // 0x75 'u'
        0x00,0x00,0xa0,0xa0,0xa0,0xa0,0x60,0x00,

        4, // 0x76 'v'
        0x00,0x00,0xa0,0xa0,0xa0,0x40,0x40,0x00,

        4, // 0x77 'w'
        0x00,0x00,0xa0,0xa0,0xa0,0xe0,0xa0,0x00,

        4, // 0x78 'x'
        0x00,0x00,0xa0,0xa0,0x40,0xa0,0xa0,0x00,

        4, // 0x79 'y'
        0x00,0x00,0xa0,0xa0,0xa0,0x60,0x20,0xc0,

        4, // 0x7a 'z'
        0x00,0x00,0xe0,0x20,0x40,0x80,0xe0,0x00,

        4, // 0x7b '{'
        0x10,0x20,0x20,0xc0,0x20,0x20,0x10,0x00,

        4, // 0x7c '|'
        0x00,0x40,0x40,0x40,0x00,0x40,0x40,0x40,

        4, // 0x7d '}'
        0x80,0x40,0x40,0x30,0x40,0x40,0x80,0x00,

        4, // 0x7e '~'
        0x00,0x50,0xa0,0x00,0x00,0x00,0x00,0x00,

        4, // 0x7f ''
        0x00,0x00,0x00,0x60,0x90,0xf0,0x00,0x00,
        0
    };

    const int8u gse5x7[] = 
    {
        7, 0, 32, 128-32,

        0x00,0x00,0x08,0x00,0x10,0x00,0x18,0x00,0x20,0x00,0x28,0x00,0x30,0x00,0x38,0x00,0x40,0x00,
        0x48,0x00,0x50,0x00,0x58,0x00,0x60,0x00,0x68,0x00,0x70,0x00,0x78,0x00,0x80,0x00,0x88,0x00,
        0x90,0x00,0x98,0x00,0xa0,0x00,0xa8,0x00,0xb0,0x00,0xb8,0x00,0xc0,0x00,0xc8,0x00,0xd0,0x00,
        0xd8,0x00,0xe0,0x00,0xe8,0x00,0xf0,0x00,0xf8,0x00,0x00,0x01,0x08,0x01,0x10,0x01,0x18,0x01,
        0x20,0x01,0x28,0x01,0x30,0x01,0x38,0x01,0x40,0x01,0x48,0x01,0x50,0x01,0x58,0x01,0x60,0x01,
        0x68,0x01,0x70,0x01,0x78,0x01,0x80,0x01,0x88,0x01,0x90,0x01,0x98,0x01,0xa0,0x01,0xa8,0x01,
        0xb0,0x01,0xb8,0x01,0xc0,0x01,0xc8,0x01,0xd0,0x01,0xd8,0x01,0xe0,0x01,0xe8,0x01,0xf0,0x01,
        0xf8,0x01,0x00,0x02,0x08,0x02,0x10,0x02,0x18,0x02,0x20,0x02,0x28,0x02,0x30,0x02,0x38,0x02,
        0x40,0x02,0x48,0x02,0x50,0x02,0x58,0x02,0x60,0x02,0x68,0x02,0x70,0x02,0x78,0x02,0x80,0x02,
        0x88,0x02,0x90,0x02,0x98,0x02,0xa0,0x02,0xa8,0x02,0xb0,0x02,0xb8,0x02,0xc0,0x02,0xc8,0x02,
        0xd0,0x02,0xd8,0x02,0xe0,0x02,0xe8,0x02,0xf0,0x02,0xf8,0x02,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x00,0x20,0x00,

        5, // 0x22 '"'
        0x00,0x50,0x50,0x00,0x00,0x00,0x00,

        5, // 0x23 '#'
        0x00,0x50,0xf8,0x50,0xf8,0x50,0x00,

        5, // 0x24 '$'
        0x20,0x78,0xa0,0x70,0x28,0xf0,0x20,

        5, // 0x25 '%'
        0x00,0x88,0x10,0x20,0x40,0x88,0x00,

        5, // 0x26 '&'
        0x00,0x40,0xa0,0x68,0x90,0x68,0x00,

        5, // 0x27 '''
        0x00,0x20,0x20,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x10,0x20,0x40,0x40,0x40,0x20,0x10,

        5, // 0x29 ')'
        0x80,0x40,0x20,0x20,0x20,0x40,0x80,

        5, // 0x2a '*'
        0x00,0x20,0xa8,0x70,0xa8,0x20,0x00,

        5, // 0x2b '+'
        0x00,0x20,0x20,0xf8,0x20,0x20,0x00,

        5, // 0x2c ','
        0x00,0x00,0x00,0x00,0x20,0x20,0x40,

        5, // 0x2d '-'
        0x00,0x00,0x00,0xf0,0x00,0x00,0x00,

        5, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x40,0x00,

        5, // 0x2f '/'
        0x00,0x08,0x10,0x20,0x40,0x80,0x00,

        5, // 0x30 '0'
        0x00,0x60,0x90,0x90,0x90,0x60,0x00,

        5, // 0x31 '1'
        0x00,0x20,0x60,0x20,0x20,0x70,0x00,

        5, // 0x32 '2'
        0x00,0x60,0x90,0x20,0x40,0xf0,0x00,

        5, // 0x33 '3'
        0x00,0xf0,0x20,0x60,0x10,0xe0,0x00,

        5, // 0x34 '4'
        0x00,0x30,0x50,0x90,0xf0,0x10,0x00,

        5, // 0x35 '5'
        0x00,0xf0,0x80,0xe0,0x10,0xe0,0x00,

        5, // 0x36 '6'
        0x00,0x60,0x80,0xe0,0x90,0x60,0x00,

        5, // 0x37 '7'
        0x00,0xf0,0x90,0x20,0x40,0x40,0x00,

        5, // 0x38 '8'
        0x00,0x60,0x90,0x60,0x90,0x60,0x00,

        5, // 0x39 '9'
        0x00,0x60,0x90,0x70,0x10,0x60,0x00,

        5, // 0x3a ':'
        0x00,0x00,0x20,0x00,0x20,0x00,0x00,

        5, // 0x3b ';'
        0x00,0x00,0x20,0x00,0x20,0x20,0x40,

        5, // 0x3c '<'
        0x00,0x10,0x20,0x40,0x20,0x10,0x00,

        5, // 0x3d '='
        0x00,0x00,0xf0,0x00,0xf0,0x00,0x00,

        5, // 0x3e '>'
        0x00,0x80,0x40,0x20,0x40,0x80,0x00,

        5, // 0x3f '?'
        0x00,0x60,0x90,0x20,0x00,0x20,0x00,

        5, // 0x40 '@'
        0x00,0x60,0x90,0xb0,0x80,0x70,0x00,

        5, // 0x41 'A'
        0x00,0x60,0x90,0xf0,0x90,0x90,0x00,

        5, // 0x42 'B'
        0x00,0xe0,0x90,0xe0,0x90,0xe0,0x00,

        5, // 0x43 'C'
        0x00,0x60,0x90,0x80,0x90,0x60,0x00,

        5, // 0x44 'D'
        0x00,0xe0,0x90,0x90,0x90,0xe0,0x00,

        5, // 0x45 'E'
        0x00,0xf0,0x80,0xe0,0x80,0xf0,0x00,

        5, // 0x46 'F'
        0x00,0xf0,0x80,0xe0,0x80,0x80,0x00,

        5, // 0x47 'G'
        0x00,0x70,0x80,0xb0,0x90,0x60,0x00,

        5, // 0x48 'H'
        0x00,0x90,0x90,0xf0,0x90,0x90,0x00,

        5, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x70,0x00,

        5, // 0x4a 'J'
        0x00,0x70,0x20,0x20,0xa0,0x40,0x00,

        5, // 0x4b 'K'
        0x00,0x90,0xa0,0xc0,0xa0,0x90,0x00,

        5, // 0x4c 'L'
        0x00,0x80,0x80,0x80,0x80,0xf0,0x00,

        5, // 0x4d 'M'
        0x00,0x90,0xf0,0x90,0x90,0x90,0x00,

        5, // 0x4e 'N'
        0x00,0x90,0xd0,0xb0,0x90,0x90,0x00,

        5, // 0x4f 'O'
        0x00,0x60,0x90,0x90,0x90,0x60,0x00,

        5, // 0x50 'P'
        0x00,0xe0,0x90,0xe0,0x80,0x80,0x00,

        5, // 0x51 'Q'
        0x00,0x60,0x90,0x90,0xa0,0x50,0x00,

        5, // 0x52 'R'
        0x00,0xe0,0x90,0xe0,0xa0,0x90,0x00,

        5, // 0x53 'S'
        0x00,0x70,0x80,0x60,0x10,0xe0,0x00,

        5, // 0x54 'T'
        0x00,0x70,0x20,0x20,0x20,0x20,0x00,

        5, // 0x55 'U'
        0x00,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x56 'V'
        0x00,0x50,0x50,0x50,0x20,0x20,0x00,

        5, // 0x57 'W'
        0x00,0x90,0x90,0x90,0xf0,0x90,0x00,

        5, // 0x58 'X'
        0x00,0x90,0x90,0x60,0x90,0x90,0x00,

        5, // 0x59 'Y'
        0x00,0x50,0x50,0x20,0x20,0x20,0x00,

        5, // 0x5a 'Z'
        0x00,0xf0,0x10,0x20,0x40,0xf0,0x00,

        5, // 0x5b '['
        0x70,0x40,0x40,0x40,0x40,0x40,0x70,

        5, // 0x5c '\'
        0x00,0x80,0x40,0x20,0x10,0x08,0x00,

        5, // 0x5d ']'
        0xe0,0x20,0x20,0x20,0x20,0x20,0xe0,

        5, // 0x5e '^'
        0x00,0x20,0x50,0x00,0x00,0x00,0x00,

        5, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0xf8,0x00,

        5, // 0x60 '`'
        0x00,0x40,0x20,0x00,0x00,0x00,0x00,

        5, // 0x61 'a'
        0x00,0x00,0x60,0xa0,0xa0,0x50,0x00,

        5, // 0x62 'b'
        0x00,0x80,0x80,0xe0,0x90,0xe0,0x00,

        5, // 0x63 'c'
        0x00,0x00,0x70,0x80,0x80,0x70,0x00,

        5, // 0x64 'd'
        0x00,0x10,0x10,0x70,0x90,0x70,0x00,

        5, // 0x65 'e'
        0x00,0x00,0x60,0xf0,0x80,0x70,0x00,

        5, // 0x66 'f'
        0x00,0x30,0x40,0xe0,0x40,0x40,0x00,

        5, // 0x67 'g'
        0x00,0x00,0x70,0x90,0x70,0x10,0x60,

        5, // 0x68 'h'
        0x00,0x80,0x80,0xe0,0x90,0x90,0x00,

        5, // 0x69 'i'
        0x20,0x00,0x60,0x20,0x20,0x70,0x00,

        5, // 0x6a 'j'
        0x20,0x00,0x60,0x20,0x20,0xa0,0x40,

        5, // 0x6b 'k'
        0x80,0x80,0x90,0xa0,0xe0,0x90,0x00,

        5, // 0x6c 'l'
        0x00,0x60,0x20,0x20,0x20,0x70,0x00,

        5, // 0x6d 'm'
        0x00,0x00,0xa0,0xf0,0xf0,0x90,0x00,

        5, // 0x6e 'n'
        0x00,0x00,0xa0,0xd0,0x90,0x90,0x00,

        5, // 0x6f 'o'
        0x00,0x00,0x60,0x90,0x90,0x60,0x00,

        5, // 0x70 'p'
        0x00,0x00,0xe0,0x90,0xe0,0x80,0x80,

        5, // 0x71 'q'
        0x00,0x00,0x70,0x90,0x70,0x10,0x10,

        5, // 0x72 'r'
        0x00,0x00,0xe0,0x90,0x80,0x80,0x00,

        5, // 0x73 's'
        0x00,0x00,0x70,0xe0,0x10,0xe0,0x00,

        5, // 0x74 't'
        0x40,0x40,0xe0,0x40,0x40,0x70,0x00,

        5, // 0x75 'u'
        0x00,0x00,0x90,0x90,0x90,0x70,0x00,

        5, // 0x76 'v'
        0x00,0x00,0x50,0x50,0x50,0x20,0x00,

        5, // 0x77 'w'
        0x00,0x00,0x90,0x90,0xf0,0x90,0x00,

        5, // 0x78 'x'
        0x00,0x00,0x90,0x60,0x60,0x90,0x00,

        5, // 0x79 'y'
        0x00,0x00,0x90,0x90,0x70,0x10,0x60,

        5, // 0x7a 'z'
        0x00,0x00,0xf0,0x20,0x40,0xf0,0x00,

        5, // 0x7b '{'
        0x10,0x20,0x20,0xc0,0x20,0x20,0x10,

        5, // 0x7c '|'
        0x20,0x20,0x20,0x00,0x20,0x20,0x20,

        5, // 0x7d '}'
        0x40,0x20,0x20,0x18,0x20,0x20,0x40,

        5, // 0x7e '~'
        0x00,0x40,0xa8,0x10,0x00,0x00,0x00,

        5, // 0x7f ''
        0x00,0x00,0x20,0x50,0x88,0xf8,0x00,
        0
    };

    const int8u gse5x9[] = 
    {
        9, 0, 32, 128-32,

        0x00,0x00,0x0a,0x00,0x14,0x00,0x1e,0x00,0x28,0x00,0x32,0x00,0x3c,0x00,0x46,0x00,0x50,0x00,
        0x5a,0x00,0x64,0x00,0x6e,0x00,0x78,0x00,0x82,0x00,0x8c,0x00,0x96,0x00,0xa0,0x00,0xaa,0x00,
        0xb4,0x00,0xbe,0x00,0xc8,0x00,0xd2,0x00,0xdc,0x00,0xe6,0x00,0xf0,0x00,0xfa,0x00,0x04,0x01,
        0x0e,0x01,0x18,0x01,0x22,0x01,0x2c,0x01,0x36,0x01,0x40,0x01,0x4a,0x01,0x54,0x01,0x5e,0x01,
        0x68,0x01,0x72,0x01,0x7c,0x01,0x86,0x01,0x90,0x01,0x9a,0x01,0xa4,0x01,0xae,0x01,0xb8,0x01,
        0xc2,0x01,0xcc,0x01,0xd6,0x01,0xe0,0x01,0xea,0x01,0xf4,0x01,0xfe,0x01,0x08,0x02,0x12,0x02,
        0x1c,0x02,0x26,0x02,0x30,0x02,0x3a,0x02,0x44,0x02,0x4e,0x02,0x58,0x02,0x62,0x02,0x6c,0x02,
        0x76,0x02,0x80,0x02,0x8a,0x02,0x94,0x02,0x9e,0x02,0xa8,0x02,0xb2,0x02,0xbc,0x02,0xc6,0x02,
        0xd0,0x02,0xda,0x02,0xe4,0x02,0xee,0x02,0xf8,0x02,0x02,0x03,0x0c,0x03,0x16,0x03,0x20,0x03,
        0x2a,0x03,0x34,0x03,0x3e,0x03,0x48,0x03,0x52,0x03,0x5c,0x03,0x66,0x03,0x70,0x03,0x7a,0x03,
        0x84,0x03,0x8e,0x03,0x98,0x03,0xa2,0x03,0xac,0x03,0xb6,0x03,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,

        5, // 0x22 '"'
        0x00,0x50,0x50,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x23 '#'
        0x00,0x50,0x50,0xf8,0x50,0xf8,0x50,0x50,0x00,

        5, // 0x24 '$'
        0x00,0x20,0x78,0xa0,0x70,0x28,0xf0,0x20,0x00,

        5, // 0x25 '%'
        0x00,0xc8,0xc8,0x10,0x20,0x40,0x98,0x98,0x00,

        5, // 0x26 '&'
        0x00,0x40,0xa0,0xa0,0x40,0xa8,0x90,0x68,0x00,

        5, // 0x27 '''
        0x00,0x20,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x10,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x10,

        5, // 0x29 ')'
        0x80,0x40,0x20,0x20,0x20,0x20,0x20,0x40,0x80,

        5, // 0x2a '*'
        0x00,0x00,0x20,0xa8,0x70,0xa8,0x20,0x00,0x00,

        5, // 0x2b '+'
        0x00,0x00,0x20,0x20,0xf8,0x20,0x20,0x00,0x00,

        5, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x40,

        5, // 0x2d '-'
        0x00,0x00,0x00,0x00,0xf0,0x00,0x00,0x00,0x00,

        5, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,

        5, // 0x2f '/'
        0x00,0x10,0x10,0x20,0x20,0x40,0x40,0x80,0x80,

        5, // 0x30 '0'
        0x00,0x60,0x90,0xb0,0xd0,0x90,0x90,0x60,0x00,

        5, // 0x31 '1'
        0x00,0x20,0x60,0x20,0x20,0x20,0x20,0x70,0x00,

        5, // 0x32 '2'
        0x00,0x60,0x90,0x10,0x20,0x40,0x80,0xf0,0x00,

        5, // 0x33 '3'
        0x00,0xf0,0x10,0x20,0x60,0x10,0x90,0x60,0x00,

        5, // 0x34 '4'
        0x00,0x30,0x50,0x90,0x90,0xf8,0x10,0x10,0x00,

        5, // 0x35 '5'
        0x00,0xf0,0x80,0xe0,0x10,0x10,0x10,0xe0,0x00,

        5, // 0x36 '6'
        0x00,0x60,0x80,0xe0,0x90,0x90,0x90,0x60,0x00,

        5, // 0x37 '7'
        0x00,0xf0,0x90,0x10,0x20,0x40,0x40,0x40,0x00,

        5, // 0x38 '8'
        0x00,0x60,0x90,0x90,0x60,0x90,0x90,0x60,0x00,

        5, // 0x39 '9'
        0x00,0x60,0x90,0x90,0x70,0x10,0x90,0x60,0x00,

        5, // 0x3a ':'
        0x00,0x00,0x00,0x20,0x00,0x00,0x20,0x00,0x00,

        5, // 0x3b ';'
        0x00,0x00,0x00,0x20,0x00,0x00,0x20,0x20,0x40,

        5, // 0x3c '<'
        0x00,0x10,0x20,0x40,0x80,0x40,0x20,0x10,0x00,

        5, // 0x3d '='
        0x00,0x00,0x00,0xf0,0x00,0xf0,0x00,0x00,0x00,

        5, // 0x3e '>'
        0x00,0x80,0x40,0x20,0x10,0x20,0x40,0x80,0x00,

        5, // 0x3f '?'
        0x00,0x60,0x90,0x10,0x20,0x20,0x00,0x20,0x00,

        5, // 0x40 '@'
        0x00,0x60,0x90,0xb0,0xb0,0xb0,0x80,0x70,0x00,

        5, // 0x41 'A'
        0x00,0x60,0x90,0x90,0xf0,0x90,0x90,0x90,0x00,

        5, // 0x42 'B'
        0x00,0xe0,0x90,0x90,0xe0,0x90,0x90,0xe0,0x00,

        5, // 0x43 'C'
        0x00,0x60,0x90,0x80,0x80,0x80,0x90,0x60,0x00,

        5, // 0x44 'D'
        0x00,0xe0,0x90,0x90,0x90,0x90,0x90,0xe0,0x00,

        5, // 0x45 'E'
        0x00,0xf0,0x80,0x80,0xe0,0x80,0x80,0xf0,0x00,

        5, // 0x46 'F'
        0x00,0xf0,0x80,0x80,0xe0,0x80,0x80,0x80,0x00,

        5, // 0x47 'G'
        0x00,0x60,0x90,0x80,0xb0,0x90,0x90,0x60,0x00,

        5, // 0x48 'H'
        0x00,0x90,0x90,0x90,0xf0,0x90,0x90,0x90,0x00,

        5, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x70,0x00,

        5, // 0x4a 'J'
        0x00,0x70,0x20,0x20,0x20,0x20,0xa0,0x40,0x00,

        5, // 0x4b 'K'
        0x00,0x90,0x90,0xa0,0xc0,0xa0,0x90,0x90,0x00,

        5, // 0x4c 'L'
        0x00,0x80,0x80,0x80,0x80,0x80,0x80,0xf0,0x00,

        5, // 0x4d 'M'
        0x00,0x90,0xf0,0x90,0x90,0x90,0x90,0x90,0x00,

        5, // 0x4e 'N'
        0x00,0x90,0x90,0xd0,0xb0,0x90,0x90,0x90,0x00,

        5, // 0x4f 'O'
        0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x50 'P'
        0x00,0xe0,0x90,0x90,0xe0,0x80,0x80,0x80,0x00,

        5, // 0x51 'Q'
        0x00,0x60,0x90,0x90,0x90,0x90,0xa0,0x50,0x00,

        5, // 0x52 'R'
        0x00,0xe0,0x90,0x90,0xe0,0xa0,0x90,0x90,0x00,

        5, // 0x53 'S'
        0x00,0x60,0x90,0x80,0x60,0x10,0x90,0x60,0x00,

        5, // 0x54 'T'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x00,

        5, // 0x55 'U'
        0x00,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x56 'V'
        0x00,0x50,0x50,0x50,0x50,0x50,0x20,0x20,0x00,

        5, // 0x57 'W'
        0x00,0x90,0x90,0x90,0x90,0x90,0xf0,0x90,0x00,

        5, // 0x58 'X'
        0x00,0x90,0x90,0x60,0x60,0x90,0x90,0x90,0x00,

        5, // 0x59 'Y'
        0x00,0x50,0x50,0x50,0x20,0x20,0x20,0x20,0x00,

        5, // 0x5a 'Z'
        0x00,0xf0,0x10,0x10,0x20,0x40,0x80,0xf0,0x00,

        5, // 0x5b '['
        0x70,0x40,0x40,0x40,0x40,0x40,0x40,0x70,0x00,

        5, // 0x5c '\'
        0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,0x00,

        5, // 0x5d ']'
        0xe0,0x20,0x20,0x20,0x20,0x20,0x20,0xe0,0x00,

        5, // 0x5e '^'
        0x00,0x20,0x50,0x88,0x00,0x00,0x00,0x00,0x00,

        5, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf8,0x00,

        5, // 0x60 '`'
        0x00,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x61 'a'
        0x00,0x00,0x60,0x10,0x70,0x90,0x90,0x70,0x00,

        5, // 0x62 'b'
        0x00,0x80,0x80,0xe0,0x90,0x90,0x90,0xe0,0x00,

        5, // 0x63 'c'
        0x00,0x00,0x60,0x90,0x80,0x80,0x90,0x60,0x00,

        5, // 0x64 'd'
        0x00,0x10,0x10,0x70,0x90,0x90,0x90,0x70,0x00,

        5, // 0x65 'e'
        0x00,0x00,0x60,0x90,0xf0,0x80,0x80,0x70,0x00,

        5, // 0x66 'f'
        0x00,0x30,0x40,0x40,0xe0,0x40,0x40,0x40,0x00,

        5, // 0x67 'g'
        0x00,0x00,0x70,0x90,0x90,0x70,0x10,0x90,0x60,

        5, // 0x68 'h'
        0x00,0x80,0x80,0xe0,0x90,0x90,0x90,0x90,0x00,

        5, // 0x69 'i'
        0x00,0x20,0x00,0x60,0x20,0x20,0x20,0x70,0x00,

        5, // 0x6a 'j'
        0x00,0x20,0x00,0x60,0x20,0x20,0x20,0xa0,0x40,

        5, // 0x6b 'k'
        0x00,0x80,0x80,0x90,0xa0,0xc0,0xa0,0x90,0x00,

        5, // 0x6c 'l'
        0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x70,0x00,

        5, // 0x6d 'm'
        0x00,0x00,0xa0,0xf0,0xf0,0xf0,0x90,0x90,0x00,

        5, // 0x6e 'n'
        0x00,0x00,0xa0,0xd0,0x90,0x90,0x90,0x90,0x00,

        5, // 0x6f 'o'
        0x00,0x00,0x60,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x70 'p'
        0x00,0x00,0xe0,0x90,0x90,0x90,0xe0,0x80,0x80,

        5, // 0x71 'q'
        0x00,0x00,0x70,0x90,0x90,0x90,0x70,0x10,0x10,

        5, // 0x72 'r'
        0x00,0x00,0xe0,0x90,0x80,0x80,0x80,0x80,0x00,

        5, // 0x73 's'
        0x00,0x00,0x60,0x90,0x40,0x20,0x90,0x60,0x00,

        5, // 0x74 't'
        0x00,0x40,0x40,0xe0,0x40,0x40,0x50,0x20,0x00,

        5, // 0x75 'u'
        0x00,0x00,0x90,0x90,0x90,0x90,0x90,0x70,0x00,

        5, // 0x76 'v'
        0x00,0x00,0x50,0x50,0x50,0x50,0x20,0x20,0x00,

        5, // 0x77 'w'
        0x00,0x00,0x90,0x90,0x90,0x90,0xf0,0x90,0x00,

        5, // 0x78 'x'
        0x00,0x00,0x90,0x90,0x60,0x60,0x90,0x90,0x00,

        5, // 0x79 'y'
        0x00,0x00,0x90,0x90,0x90,0x90,0x70,0x10,0xe0,

        5, // 0x7a 'z'
        0x00,0x00,0xf0,0x10,0x20,0x40,0x80,0xf0,0x00,

        5, // 0x7b '{'
        0x10,0x20,0x20,0x20,0xc0,0x20,0x20,0x20,0x10,

        5, // 0x7c '|'
        0x00,0x20,0x20,0x20,0x00,0x20,0x20,0x20,0x00,

        5, // 0x7d '}'
        0x80,0x40,0x40,0x40,0x30,0x40,0x40,0x40,0x80,

        5, // 0x7e '~'
        0x00,0x40,0xa8,0x10,0x00,0x00,0x00,0x00,0x00,

        5, // 0x7f ''
        0x00,0x00,0x00,0x20,0x50,0x88,0xf8,0x00,0x00,
        0
    };

    const int8u gse6x12[] = 
    {
        12, 0, 32, 128-32,

        0x00,0x00,0x0d,0x00,0x1a,0x00,0x27,0x00,0x34,0x00,0x41,0x00,0x4e,0x00,0x5b,0x00,0x68,0x00,
        0x75,0x00,0x82,0x00,0x8f,0x00,0x9c,0x00,0xa9,0x00,0xb6,0x00,0xc3,0x00,0xd0,0x00,0xdd,0x00,
        0xea,0x00,0xf7,0x00,0x04,0x01,0x11,0x01,0x1e,0x01,0x2b,0x01,0x38,0x01,0x45,0x01,0x52,0x01,
        0x5f,0x01,0x6c,0x01,0x79,0x01,0x86,0x01,0x93,0x01,0xa0,0x01,0xad,0x01,0xba,0x01,0xc7,0x01,
        0xd4,0x01,0xe1,0x01,0xee,0x01,0xfb,0x01,0x08,0x02,0x15,0x02,0x22,0x02,0x2f,0x02,0x3c,0x02,
        0x49,0x02,0x56,0x02,0x63,0x02,0x70,0x02,0x7d,0x02,0x8a,0x02,0x97,0x02,0xa4,0x02,0xb1,0x02,
        0xbe,0x02,0xcb,0x02,0xd8,0x02,0xe5,0x02,0xf2,0x02,0xff,0x02,0x0c,0x03,0x19,0x03,0x26,0x03,
        0x33,0x03,0x40,0x03,0x4d,0x03,0x5a,0x03,0x67,0x03,0x74,0x03,0x81,0x03,0x8e,0x03,0x9b,0x03,
        0xa8,0x03,0xb5,0x03,0xc2,0x03,0xcf,0x03,0xdc,0x03,0xe9,0x03,0xf6,0x03,0x03,0x04,0x10,0x04,
        0x1d,0x04,0x2a,0x04,0x37,0x04,0x44,0x04,0x51,0x04,0x5e,0x04,0x6b,0x04,0x78,0x04,0x85,0x04,
        0x92,0x04,0x9f,0x04,0xac,0x04,0xb9,0x04,0xc6,0x04,0xd3,0x04,

        6, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,0x00,

        6, // 0x22 '"'
        0x00,0x50,0x50,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x23 '#'
        0x00,0x50,0x50,0xf8,0x50,0x50,0x50,0xf8,0x50,0x50,0x00,0x00,

        6, // 0x24 '$'
        0x00,0x20,0x70,0xa8,0xa0,0x70,0x28,0xa8,0x70,0x20,0x00,0x00,

        6, // 0x25 '%'
        0x00,0xc8,0xd8,0x10,0x30,0x20,0x60,0x40,0xd8,0x98,0x00,0x00,

        6, // 0x26 '&'
        0x00,0x60,0x90,0x90,0x90,0x60,0xa8,0x90,0x90,0x68,0x00,0x00,

        6, // 0x27 '''
        0x00,0x20,0x20,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x28 '('
        0x00,0x10,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x10,0x00,0x00,

        6, // 0x29 ')'
        0x00,0x40,0x20,0x10,0x10,0x10,0x10,0x10,0x20,0x40,0x00,0x00,

        6, // 0x2a '*'
        0x00,0x00,0x00,0x50,0x20,0xf8,0x20,0x50,0x00,0x00,0x00,0x00,

        6, // 0x2b '+'
        0x00,0x00,0x20,0x20,0x20,0xf8,0x20,0x20,0x20,0x00,0x00,0x00,

        6, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x40,

        6, // 0x2d '-'
        0x00,0x00,0x00,0x00,0x00,0xf8,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x00,

        6, // 0x2f '/'
        0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x80,0x80,0x00,0x00,

        6, // 0x30 '0'
        0x00,0x70,0x88,0x88,0x98,0xa8,0xc8,0x88,0x88,0x70,0x00,0x00,

        6, // 0x31 '1'
        0x00,0x20,0x20,0x60,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        6, // 0x32 '2'
        0x00,0x70,0x88,0x88,0x08,0x10,0x20,0x40,0x80,0xf8,0x00,0x00,

        6, // 0x33 '3'
        0x00,0xf8,0x10,0x20,0x70,0x08,0x08,0x08,0x88,0x70,0x00,0x00,

        6, // 0x34 '4'
        0x00,0x10,0x20,0x40,0x90,0x90,0xf8,0x10,0x10,0x10,0x00,0x00,

        6, // 0x35 '5'
        0x00,0xf8,0x80,0x80,0xf0,0x08,0x08,0x08,0x88,0x70,0x00,0x00,

        6, // 0x36 '6'
        0x00,0x70,0x88,0x80,0x80,0xf0,0x88,0x88,0x88,0x70,0x00,0x00,

        6, // 0x37 '7'
        0x00,0xf8,0x88,0x08,0x08,0x10,0x20,0x20,0x20,0x20,0x00,0x00,

        6, // 0x38 '8'
        0x00,0x70,0x88,0x88,0x88,0x70,0x88,0x88,0x88,0x70,0x00,0x00,

        6, // 0x39 '9'
        0x00,0x70,0x88,0x88,0x88,0x78,0x08,0x08,0x88,0x70,0x00,0x00,

        6, // 0x3a ':'
        0x00,0x00,0x00,0x00,0x20,0x00,0x00,0x00,0x20,0x00,0x00,0x00,

        6, // 0x3b ';'
        0x00,0x00,0x00,0x00,0x20,0x00,0x00,0x00,0x20,0x20,0x20,0x40,

        6, // 0x3c '<'
        0x00,0x08,0x10,0x20,0x40,0x80,0x40,0x20,0x10,0x08,0x00,0x00,

        6, // 0x3d '='
        0x00,0x00,0x00,0x00,0xf8,0x00,0xf8,0x00,0x00,0x00,0x00,0x00,

        6, // 0x3e '>'
        0x00,0x80,0x40,0x20,0x10,0x08,0x10,0x20,0x40,0x80,0x00,0x00,

        6, // 0x3f '?'
        0x00,0x70,0x88,0x88,0x08,0x10,0x20,0x20,0x00,0x20,0x00,0x00,

        6, // 0x40 '@'
        0x00,0x70,0x88,0x88,0xb8,0xb8,0xb0,0x80,0x88,0x70,0x00,0x00,

        6, // 0x41 'A'
        0x00,0x20,0x50,0x88,0x88,0x88,0xf8,0x88,0x88,0x88,0x00,0x00,

        6, // 0x42 'B'
        0x00,0xf0,0x88,0x88,0x88,0xf0,0x88,0x88,0x88,0xf0,0x00,0x00,

        6, // 0x43 'C'
        0x00,0x70,0x88,0x88,0x80,0x80,0x80,0x88,0x88,0x70,0x00,0x00,

        6, // 0x44 'D'
        0x00,0xe0,0x90,0x88,0x88,0x88,0x88,0x88,0x90,0xe0,0x00,0x00,

        6, // 0x45 'E'
        0x00,0xf8,0x80,0x80,0x80,0xf0,0x80,0x80,0x80,0xf8,0x00,0x00,

        6, // 0x46 'F'
        0x00,0xf8,0x80,0x80,0x80,0xf0,0x80,0x80,0x80,0x80,0x00,0x00,

        6, // 0x47 'G'
        0x00,0x70,0x88,0x80,0x80,0xb8,0x88,0x88,0x88,0x70,0x00,0x00,

        6, // 0x48 'H'
        0x00,0x88,0x88,0x88,0x88,0xf8,0x88,0x88,0x88,0x88,0x00,0x00,

        6, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        6, // 0x4a 'J'
        0x00,0x38,0x10,0x10,0x10,0x10,0x10,0x10,0x90,0x60,0x00,0x00,

        6, // 0x4b 'K'
        0x00,0x88,0x88,0x90,0xa0,0xc0,0xa0,0x90,0x88,0x88,0x00,0x00,

        6, // 0x4c 'L'
        0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0xf8,0x00,0x00,

        6, // 0x4d 'M'
        0x00,0x88,0x88,0xd8,0xa8,0x88,0x88,0x88,0x88,0x88,0x00,0x00,

        6, // 0x4e 'N'
        0x00,0x88,0x88,0xc8,0xa8,0x98,0x88,0x88,0x88,0x88,0x00,0x00,

        6, // 0x4f 'O'
        0x00,0x70,0x88,0x88,0x88,0x88,0x88,0x88,0x88,0x70,0x00,0x00,

        6, // 0x50 'P'
        0x00,0xf0,0x88,0x88,0x88,0xf0,0x80,0x80,0x80,0x80,0x00,0x00,

        6, // 0x51 'Q'
        0x00,0x70,0x88,0x88,0x88,0x88,0x88,0xa8,0x90,0x68,0x00,0x00,

        6, // 0x52 'R'
        0x00,0xf0,0x88,0x88,0x88,0x88,0xf0,0xa0,0x90,0x88,0x00,0x00,

        6, // 0x53 'S'
        0x00,0x70,0x88,0x80,0x80,0x70,0x08,0x08,0x88,0x70,0x00,0x00,

        6, // 0x54 'T'
        0x00,0xf8,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,

        6, // 0x55 'U'
        0x00,0x88,0x88,0x88,0x88,0x88,0x88,0x88,0x88,0x70,0x00,0x00,

        6, // 0x56 'V'
        0x00,0x88,0x88,0x88,0x88,0x88,0x50,0x50,0x20,0x20,0x00,0x00,

        6, // 0x57 'W'
        0x00,0x88,0x88,0x88,0x88,0x88,0xa8,0xa8,0xd8,0x88,0x00,0x00,

        6, // 0x58 'X'
        0x00,0x88,0x88,0x88,0x50,0x20,0x50,0x88,0x88,0x88,0x00,0x00,

        6, // 0x59 'Y'
        0x00,0x88,0x88,0x88,0x50,0x20,0x20,0x20,0x20,0x20,0x00,0x00,

        6, // 0x5a 'Z'
        0x00,0xf8,0x08,0x08,0x10,0x20,0x40,0x80,0x80,0xf8,0x00,0x00,

        6, // 0x5b '['
        0x70,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x70,0x00,

        6, // 0x5c '\'
        0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x00,0x00,

        6, // 0x5d ']'
        0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x70,0x00,

        6, // 0x5e '^'
        0x00,0x00,0x20,0x50,0x88,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf8,0x00,0x00,

        6, // 0x60 '`'
        0x00,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x61 'a'
        0x00,0x00,0x00,0x70,0x88,0x08,0x78,0x88,0x88,0x78,0x00,0x00,

        6, // 0x62 'b'
        0x00,0x80,0x80,0x80,0xf0,0x88,0x88,0x88,0x88,0xf0,0x00,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x70,0x88,0x80,0x80,0x80,0x88,0x70,0x00,0x00,

        6, // 0x64 'd'
        0x00,0x08,0x08,0x08,0x78,0x88,0x88,0x88,0x88,0x78,0x00,0x00,

        6, // 0x65 'e'
        0x00,0x00,0x00,0x70,0x88,0x88,0xf8,0x80,0x80,0x78,0x00,0x00,

        6, // 0x66 'f'
        0x00,0x18,0x20,0x20,0xf8,0x20,0x20,0x20,0x20,0x20,0x00,0x00,

        6, // 0x67 'g'
        0x00,0x00,0x00,0x78,0x88,0x88,0x88,0x88,0x78,0x08,0x08,0xf0,

        6, // 0x68 'h'
        0x00,0x80,0x80,0x80,0xf0,0x88,0x88,0x88,0x88,0x88,0x00,0x00,

        6, // 0x69 'i'
        0x00,0x20,0x00,0x00,0x60,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        6, // 0x6a 'j'
        0x00,0x10,0x00,0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x90,0x60,

        6, // 0x6b 'k'
        0x00,0x80,0x80,0x80,0x88,0x90,0xa0,0xd0,0x88,0x88,0x00,0x00,

        6, // 0x6c 'l'
        0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        6, // 0x6d 'm'
        0x00,0x00,0x00,0xd0,0xa8,0xa8,0xa8,0xa8,0xa8,0xa8,0x00,0x00,

        6, // 0x6e 'n'
        0x00,0x00,0x00,0xb0,0xc8,0x88,0x88,0x88,0x88,0x88,0x00,0x00,

        6, // 0x6f 'o'
        0x00,0x00,0x00,0x70,0x88,0x88,0x88,0x88,0x88,0x70,0x00,0x00,

        6, // 0x70 'p'
        0x00,0x00,0x00,0xf0,0x88,0x88,0x88,0x88,0xf0,0x80,0x80,0x80,

        6, // 0x71 'q'
        0x00,0x00,0x00,0x78,0x88,0x88,0x88,0x88,0x78,0x08,0x08,0x08,

        6, // 0x72 'r'
        0x00,0x00,0x00,0xb0,0xc8,0x88,0x80,0x80,0x80,0x80,0x00,0x00,

        6, // 0x73 's'
        0x00,0x00,0x00,0x70,0x88,0x80,0x70,0x08,0x88,0x70,0x00,0x00,

        6, // 0x74 't'
        0x00,0x40,0x40,0x40,0xe0,0x40,0x40,0x40,0x48,0x30,0x00,0x00,

        6, // 0x75 'u'
        0x00,0x00,0x00,0x88,0x88,0x88,0x88,0x88,0x88,0x78,0x00,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x88,0x88,0x88,0x50,0x50,0x20,0x20,0x00,0x00,

        6, // 0x77 'w'
        0x00,0x00,0x00,0x88,0x88,0x88,0xa8,0xa8,0xd8,0x88,0x00,0x00,

        6, // 0x78 'x'
        0x00,0x00,0x00,0x88,0x88,0x50,0x20,0x50,0x88,0x88,0x00,0x00,

        6, // 0x79 'y'
        0x00,0x00,0x00,0x88,0x88,0x88,0x88,0x88,0x78,0x08,0x10,0xe0,

        6, // 0x7a 'z'
        0x00,0x00,0x00,0xf8,0x08,0x10,0x20,0x40,0x80,0xf8,0x00,0x00,

        6, // 0x7b '{'
        0x18,0x20,0x20,0x20,0x20,0xc0,0x20,0x20,0x20,0x20,0x18,0x00,

        6, // 0x7c '|'
        0x00,0x20,0x20,0x20,0x20,0x00,0x20,0x20,0x20,0x20,0x00,0x00,

        6, // 0x7d '}'
        0xc0,0x20,0x20,0x20,0x20,0x18,0x20,0x20,0x20,0x20,0xc0,0x00,

        6, // 0x7e '~'
        0x00,0x00,0x40,0xa8,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x7f ''
        0x00,0x00,0x00,0x00,0x20,0x50,0x88,0xf8,0x00,0x00,0x00,0x00,
        0
    };

    const int8u gse6x9[] = 
    {
        9, 0, 32, 128-32,

        0x00,0x00,0x0a,0x00,0x14,0x00,0x1e,0x00,0x28,0x00,0x32,0x00,0x3c,0x00,0x46,0x00,0x50,0x00,
        0x5a,0x00,0x64,0x00,0x6e,0x00,0x78,0x00,0x82,0x00,0x8c,0x00,0x96,0x00,0xa0,0x00,0xaa,0x00,
        0xb4,0x00,0xbe,0x00,0xc8,0x00,0xd2,0x00,0xdc,0x00,0xe6,0x00,0xf0,0x00,0xfa,0x00,0x04,0x01,
        0x0e,0x01,0x18,0x01,0x22,0x01,0x2c,0x01,0x36,0x01,0x40,0x01,0x4a,0x01,0x54,0x01,0x5e,0x01,
        0x68,0x01,0x72,0x01,0x7c,0x01,0x86,0x01,0x90,0x01,0x9a,0x01,0xa4,0x01,0xae,0x01,0xb8,0x01,
        0xc2,0x01,0xcc,0x01,0xd6,0x01,0xe0,0x01,0xea,0x01,0xf4,0x01,0xfe,0x01,0x08,0x02,0x12,0x02,
        0x1c,0x02,0x26,0x02,0x30,0x02,0x3a,0x02,0x44,0x02,0x4e,0x02,0x58,0x02,0x62,0x02,0x6c,0x02,
        0x76,0x02,0x80,0x02,0x8a,0x02,0x94,0x02,0x9e,0x02,0xa8,0x02,0xb2,0x02,0xbc,0x02,0xc6,0x02,
        0xd0,0x02,0xda,0x02,0xe4,0x02,0xee,0x02,0xf8,0x02,0x02,0x03,0x0c,0x03,0x16,0x03,0x20,0x03,
        0x2a,0x03,0x34,0x03,0x3e,0x03,0x48,0x03,0x52,0x03,0x5c,0x03,0x66,0x03,0x70,0x03,0x7a,0x03,
        0x84,0x03,0x8e,0x03,0x98,0x03,0xa2,0x03,0xac,0x03,0xb6,0x03,

        6, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,

        6, // 0x22 '"'
        0x00,0x50,0x50,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x23 '#'
        0x00,0x50,0x50,0xf8,0x50,0xf8,0x50,0x50,0x00,

        6, // 0x24 '$'
        0x00,0x70,0xa8,0xa0,0x70,0x28,0xa8,0x70,0x00,

        6, // 0x25 '%'
        0x00,0xc8,0xc8,0x10,0x20,0x40,0x98,0x98,0x00,

        6, // 0x26 '&'
        0x00,0x60,0x90,0x90,0x60,0xa8,0x90,0x68,0x00,

        6, // 0x27 '''
        0x00,0x20,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x28 '('
        0x10,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x10,

        6, // 0x29 ')'
        0x40,0x20,0x10,0x10,0x10,0x10,0x10,0x20,0x40,

        6, // 0x2a '*'
        0x00,0x00,0x20,0xa8,0x70,0xa8,0x20,0x00,0x00,

        6, // 0x2b '+'
        0x00,0x00,0x20,0x20,0xf8,0x20,0x20,0x00,0x00,

        6, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x40,

        6, // 0x2d '-'
        0x00,0x00,0x00,0x00,0xf8,0x00,0x00,0x00,0x00,

        6, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,

        6, // 0x2f '/'
        0x00,0x08,0x08,0x10,0x20,0x40,0x80,0x80,0x00,

        6, // 0x30 '0'
        0x00,0x70,0x88,0x98,0xa8,0xc8,0x88,0x70,0x00,

        6, // 0x31 '1'
        0x00,0x20,0x60,0x20,0x20,0x20,0x20,0x70,0x00,

        6, // 0x32 '2'
        0x00,0x70,0x88,0x08,0x10,0x20,0x40,0xf8,0x00,

        6, // 0x33 '3'
        0x00,0xf8,0x10,0x20,0x70,0x08,0x88,0x70,0x00,

        6, // 0x34 '4'
        0x00,0x10,0x20,0x40,0x90,0xf8,0x10,0x10,0x00,

        6, // 0x35 '5'
        0x00,0xf8,0x80,0xf0,0x08,0x08,0x88,0x70,0x00,

        6, // 0x36 '6'
        0x00,0x70,0x88,0x80,0xf0,0x88,0x88,0x70,0x00,

        6, // 0x37 '7'
        0x00,0xf8,0x08,0x08,0x10,0x20,0x40,0x40,0x00,

        6, // 0x38 '8'
        0x00,0x70,0x88,0x88,0x70,0x88,0x88,0x70,0x00,

        6, // 0x39 '9'
        0x00,0x70,0x88,0x88,0x78,0x08,0x88,0x70,0x00,

        6, // 0x3a ':'
        0x00,0x00,0x00,0x20,0x00,0x00,0x20,0x00,0x00,

        6, // 0x3b ';'
        0x00,0x00,0x00,0x20,0x00,0x00,0x20,0x20,0x40,

        6, // 0x3c '<'
        0x00,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x00,

        6, // 0x3d '='
        0x00,0x00,0x00,0xf8,0x00,0xf8,0x00,0x00,0x00,

        6, // 0x3e '>'
        0x00,0x80,0x40,0x20,0x10,0x20,0x40,0x80,0x00,

        6, // 0x3f '?'
        0x00,0x70,0x88,0x08,0x10,0x20,0x00,0x20,0x00,

        6, // 0x40 '@'
        0x00,0x70,0x88,0x88,0xb8,0xb8,0x80,0x70,0x00,

        6, // 0x41 'A'
        0x00,0x20,0x50,0x88,0x88,0xf8,0x88,0x88,0x00,

        6, // 0x42 'B'
        0x00,0xf0,0x88,0x88,0xf0,0x88,0x88,0xf0,0x00,

        6, // 0x43 'C'
        0x00,0x70,0x88,0x80,0x80,0x80,0x88,0x70,0x00,

        6, // 0x44 'D'
        0x00,0xe0,0x90,0x88,0x88,0x88,0x90,0xe0,0x00,

        6, // 0x45 'E'
        0x00,0xf8,0x80,0x80,0xf0,0x80,0x80,0xf8,0x00,

        6, // 0x46 'F'
        0x00,0xf8,0x80,0x80,0xf0,0x80,0x80,0x80,0x00,

        6, // 0x47 'G'
        0x00,0x70,0x88,0x80,0xb8,0x88,0x88,0x70,0x00,

        6, // 0x48 'H'
        0x00,0x88,0x88,0x88,0xf8,0x88,0x88,0x88,0x00,

        6, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x70,0x00,

        6, // 0x4a 'J'
        0x00,0x38,0x10,0x10,0x10,0x10,0x90,0x60,0x00,

        6, // 0x4b 'K'
        0x00,0x88,0x90,0xa0,0xc0,0xa0,0x90,0x88,0x00,

        6, // 0x4c 'L'
        0x00,0x80,0x80,0x80,0x80,0x80,0x80,0xf8,0x00,

        6, // 0x4d 'M'
        0x00,0x88,0xd8,0xa8,0x88,0x88,0x88,0x88,0x00,

        6, // 0x4e 'N'
        0x00,0x88,0x88,0xc8,0xa8,0x98,0x88,0x88,0x00,

        6, // 0x4f 'O'
        0x00,0x70,0x88,0x88,0x88,0x88,0x88,0x70,0x00,

        6, // 0x50 'P'
        0x00,0xf0,0x88,0x88,0xf0,0x80,0x80,0x80,0x00,

        6, // 0x51 'Q'
        0x00,0x70,0x88,0x88,0x88,0xa8,0x90,0x68,0x00,

        6, // 0x52 'R'
        0x00,0xf0,0x88,0x88,0x88,0xf0,0x90,0x88,0x00,

        6, // 0x53 'S'
        0x00,0x70,0x88,0x80,0x70,0x08,0x88,0x70,0x00,

        6, // 0x54 'T'
        0x00,0xf8,0x20,0x20,0x20,0x20,0x20,0x20,0x00,

        6, // 0x55 'U'
        0x00,0x88,0x88,0x88,0x88,0x88,0x88,0x70,0x00,

        6, // 0x56 'V'
        0x00,0x88,0x88,0x88,0x50,0x50,0x20,0x20,0x00,

        6, // 0x57 'W'
        0x00,0x88,0x88,0x88,0xa8,0xa8,0xd8,0x88,0x00,

        6, // 0x58 'X'
        0x00,0x88,0x88,0x50,0x20,0x50,0x88,0x88,0x00,

        6, // 0x59 'Y'
        0x00,0x88,0x88,0x50,0x20,0x20,0x20,0x20,0x00,

        6, // 0x5a 'Z'
        0x00,0xf8,0x08,0x10,0x20,0x40,0x80,0xf8,0x00,

        6, // 0x5b '['
        0x70,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x70,

        6, // 0x5c '\'
        0x00,0x80,0x80,0x40,0x20,0x10,0x08,0x08,0x00,

        6, // 0x5d ']'
        0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x70,

        6, // 0x5e '^'
        0x00,0x00,0x20,0x50,0x88,0x00,0x00,0x00,0x00,

        6, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf8,0x00,

        6, // 0x60 '`'
        0x00,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x61 'a'
        0x00,0x00,0x00,0x70,0x08,0x78,0x88,0x78,0x00,

        6, // 0x62 'b'
        0x00,0x80,0x80,0xf0,0x88,0x88,0x88,0xf0,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x70,0x88,0x80,0x88,0x70,0x00,

        6, // 0x64 'd'
        0x00,0x08,0x08,0x78,0x88,0x88,0x88,0x78,0x00,

        6, // 0x65 'e'
        0x00,0x00,0x00,0x70,0x88,0xf8,0x80,0x78,0x00,

        6, // 0x66 'f'
        0x00,0x18,0x20,0x20,0xf8,0x20,0x20,0x20,0x00,

        6, // 0x67 'g'
        0x00,0x00,0x00,0x78,0x88,0x88,0x78,0x08,0x70,

        6, // 0x68 'h'
        0x00,0x80,0x80,0xf0,0x88,0x88,0x88,0x88,0x00,

        6, // 0x69 'i'
        0x00,0x20,0x00,0x60,0x20,0x20,0x20,0x70,0x00,

        6, // 0x6a 'j'
        0x00,0x10,0x00,0x30,0x10,0x10,0x10,0x90,0x60,

        6, // 0x6b 'k'
        0x00,0x00,0x80,0x88,0x90,0xa0,0xd0,0x88,0x00,

        6, // 0x6c 'l'
        0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x70,0x00,

        6, // 0x6d 'm'
        0x00,0x00,0x00,0xd0,0xa8,0xa8,0xa8,0xa8,0x00,

        6, // 0x6e 'n'
        0x00,0x00,0x00,0xb0,0xc8,0x88,0x88,0x88,0x00,

        6, // 0x6f 'o'
        0x00,0x00,0x00,0x70,0x88,0x88,0x88,0x70,0x00,

        6, // 0x70 'p'
        0x00,0x00,0x00,0xf0,0x88,0x88,0xf0,0x80,0x80,

        6, // 0x71 'q'
        0x00,0x00,0x00,0x78,0x88,0x88,0x78,0x08,0x08,

        6, // 0x72 'r'
        0x00,0x00,0x00,0xb8,0xc0,0x80,0x80,0x80,0x00,

        6, // 0x73 's'
        0x00,0x00,0x00,0x78,0x80,0x70,0x08,0xf0,0x00,

        6, // 0x74 't'
        0x00,0x40,0x40,0xe0,0x40,0x40,0x48,0x30,0x00,

        6, // 0x75 'u'
        0x00,0x00,0x00,0x88,0x88,0x88,0x88,0x78,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x88,0x88,0x88,0x50,0x20,0x00,

        6, // 0x77 'w'
        0x00,0x00,0x00,0x88,0x88,0xa8,0xd8,0x88,0x00,

        6, // 0x78 'x'
        0x00,0x00,0x00,0x88,0x50,0x20,0x50,0x88,0x00,

        6, // 0x79 'y'
        0x00,0x00,0x00,0x88,0x88,0x88,0x78,0x08,0x70,

        6, // 0x7a 'z'
        0x00,0x00,0x00,0xf8,0x10,0x20,0x40,0xf8,0x00,

        6, // 0x7b '{'
        0x18,0x20,0x20,0x20,0xc0,0x20,0x20,0x20,0x18,

        6, // 0x7c '|'
        0x00,0x20,0x20,0x20,0x00,0x20,0x20,0x20,0x00,

        6, // 0x7d '}'
        0xc0,0x20,0x20,0x20,0x18,0x20,0x20,0x20,0xc0,

        6, // 0x7e '~'
        0x00,0x40,0xa8,0x10,0x00,0x00,0x00,0x00,0x00,

        6, // 0x7f ''
        0x00,0x00,0x00,0x20,0x50,0x88,0xf8,0x00,0x00,
        0
    };

    const int8u gse7x11[] = 
    {
        11, 0, 32, 128-32,

        0x00,0x00,0x0c,0x00,0x18,0x00,0x24,0x00,0x30,0x00,0x3c,0x00,0x48,0x00,0x54,0x00,0x60,0x00,
        0x6c,0x00,0x78,0x00,0x84,0x00,0x90,0x00,0x9c,0x00,0xa8,0x00,0xb4,0x00,0xc0,0x00,0xcc,0x00,
        0xd8,0x00,0xe4,0x00,0xf0,0x00,0xfc,0x00,0x08,0x01,0x14,0x01,0x20,0x01,0x2c,0x01,0x38,0x01,
        0x44,0x01,0x50,0x01,0x5c,0x01,0x68,0x01,0x74,0x01,0x80,0x01,0x8c,0x01,0x98,0x01,0xa4,0x01,
        0xb0,0x01,0xbc,0x01,0xc8,0x01,0xd4,0x01,0xe0,0x01,0xec,0x01,0xf8,0x01,0x04,0x02,0x10,0x02,
        0x1c,0x02,0x28,0x02,0x34,0x02,0x40,0x02,0x4c,0x02,0x58,0x02,0x64,0x02,0x70,0x02,0x7c,0x02,
        0x88,0x02,0x94,0x02,0xa0,0x02,0xac,0x02,0xb8,0x02,0xc4,0x02,0xd0,0x02,0xdc,0x02,0xe8,0x02,
        0xf4,0x02,0x00,0x03,0x0c,0x03,0x18,0x03,0x24,0x03,0x30,0x03,0x3c,0x03,0x48,0x03,0x54,0x03,
        0x60,0x03,0x6c,0x03,0x78,0x03,0x84,0x03,0x90,0x03,0x9c,0x03,0xa8,0x03,0xb4,0x03,0xc0,0x03,
        0xcc,0x03,0xd8,0x03,0xe4,0x03,0xf0,0x03,0xfc,0x03,0x08,0x04,0x14,0x04,0x20,0x04,0x2c,0x04,
        0x38,0x04,0x44,0x04,0x50,0x04,0x5c,0x04,0x68,0x04,0x74,0x04,

        7, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x21 '!'
        0x00,0x10,0x38,0x38,0x38,0x10,0x10,0x00,0x10,0x00,0x00,

        7, // 0x22 '"'
        0x00,0x00,0x24,0x24,0x24,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x23 '#'
        0x00,0x48,0x48,0xfc,0x48,0x48,0xfc,0x48,0x48,0x00,0x00,

        7, // 0x24 '$'
        0x00,0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x38,0x10,0x00,

        7, // 0x25 '%'
        0x00,0x00,0x42,0xa4,0x48,0x10,0x24,0x4a,0x84,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x30,0x48,0x48,0x30,0x60,0x94,0x98,0x6c,0x00,0x00,

        7, // 0x27 '''
        0x00,0x20,0x20,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x00,0x04,0x08,0x10,0x10,0x10,0x10,0x08,0x04,0x00,0x00,

        7, // 0x29 ')'
        0x00,0x40,0x20,0x10,0x10,0x10,0x10,0x20,0x40,0x00,0x00,

        7, // 0x2a '*'
        0x00,0x00,0x00,0x20,0xa8,0x70,0xa8,0x20,0x00,0x00,0x00,

        7, // 0x2b '+'
        0x00,0x00,0x00,0x10,0x10,0x7c,0x10,0x10,0x00,0x00,0x00,

        7, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x60,

        7, // 0x2d '-'
        0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,

        7, // 0x2f '/'
        0x00,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,0x00,

        7, // 0x30 '0'
        0x00,0x38,0x44,0x4c,0x54,0x64,0x44,0x44,0x38,0x00,0x00,

        7, // 0x31 '1'
        0x00,0x10,0x30,0x10,0x10,0x10,0x10,0x10,0x7c,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x38,0x44,0x04,0x08,0x10,0x20,0x44,0x7c,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x7c,0x48,0x10,0x38,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x34 '4'
        0x00,0x08,0x10,0x20,0x48,0x48,0x7c,0x08,0x1c,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x7c,0x40,0x40,0x78,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x18,0x20,0x40,0x78,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x37 '7'
        0x00,0x7c,0x44,0x04,0x08,0x10,0x10,0x10,0x10,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x38,0x44,0x44,0x38,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x38,0x44,0x44,0x44,0x3c,0x04,0x08,0x30,0x00,0x00,

        7, // 0x3a ':'
        0x00,0x00,0x00,0x30,0x00,0x00,0x00,0x30,0x00,0x00,0x00,

        7, // 0x3b ';'
        0x00,0x00,0x00,0x30,0x00,0x00,0x00,0x30,0x30,0x60,0x00,

        7, // 0x3c '<'
        0x00,0x00,0x04,0x08,0x10,0x20,0x10,0x08,0x04,0x00,0x00,

        7, // 0x3d '='
        0x00,0x00,0x00,0x00,0xfc,0x00,0xfc,0x00,0x00,0x00,0x00,

        7, // 0x3e '>'
        0x00,0x00,0x40,0x20,0x10,0x08,0x10,0x20,0x40,0x00,0x00,

        7, // 0x3f '?'
        0x00,0x70,0x88,0x88,0x10,0x20,0x20,0x00,0x20,0x00,0x00,

        7, // 0x40 '@'
        0x00,0x30,0x48,0x04,0x34,0x54,0x54,0x54,0x28,0x00,0x00,

        7, // 0x41 'A'
        0x00,0x10,0x28,0x44,0x44,0x7c,0x44,0x44,0x44,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x78,0x44,0x44,0x78,0x44,0x44,0x44,0x78,0x00,0x00,

        7, // 0x43 'C'
        0x00,0x38,0x44,0x40,0x40,0x40,0x40,0x44,0x38,0x00,0x00,

        7, // 0x44 'D'
        0x00,0x70,0x48,0x44,0x44,0x44,0x44,0x48,0x70,0x00,0x00,

        7, // 0x45 'E'
        0x00,0x7c,0x40,0x40,0x70,0x40,0x40,0x40,0x7c,0x00,0x00,

        7, // 0x46 'F'
        0x00,0x7c,0x40,0x40,0x70,0x40,0x40,0x40,0x40,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x38,0x44,0x40,0x40,0x5c,0x44,0x44,0x38,0x00,0x00,

        7, // 0x48 'H'
        0x00,0x44,0x44,0x44,0x7c,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x49 'I'
        0x00,0x38,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x4a 'J'
        0x00,0x1c,0x08,0x08,0x08,0x08,0x08,0x48,0x30,0x00,0x00,

        7, // 0x4b 'K'
        0x00,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x44,0x00,0x00,

        7, // 0x4c 'L'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7c,0x00,0x00,

        7, // 0x4d 'M'
        0x00,0x44,0x6c,0x54,0x54,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x4e 'N'
        0x00,0x44,0x44,0x64,0x54,0x4c,0x44,0x44,0x44,0x00,0x00,

        7, // 0x4f 'O'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x78,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x00,0x00,

        7, // 0x51 'Q'
        0x00,0x38,0x44,0x44,0x44,0x44,0x54,0x48,0x34,0x00,0x00,

        7, // 0x52 'R'
        0x00,0x78,0x44,0x44,0x44,0x78,0x50,0x48,0x44,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x44,0x38,0x00,0x00,

        7, // 0x54 'T'
        0x00,0x7c,0x54,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        7, // 0x55 'U'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x56 'V'
        0x00,0x44,0x44,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,

        7, // 0x57 'W'
        0x00,0x44,0x44,0x44,0x44,0x54,0x54,0x6c,0x44,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x5a 'Z'
        0x00,0x7c,0x04,0x08,0x10,0x20,0x40,0x44,0x7c,0x00,0x00,

        7, // 0x5b '['
        0x00,0x38,0x20,0x20,0x20,0x20,0x20,0x20,0x38,0x00,0x00,

        7, // 0x5c '\'
        0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x00,0x00,

        7, // 0x5d ']'
        0x00,0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x38,0x00,0x00,

        7, // 0x5e '^'
        0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,

        7, // 0x60 '`'
        0x00,0x20,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x38,0x04,0x3c,0x44,0x44,0x3c,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x78,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x44,0x38,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x04,0x04,0x3c,0x44,0x44,0x44,0x44,0x3c,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x38,0x44,0x7c,0x40,0x44,0x38,0x00,0x00,

        7, // 0x66 'f'
        0x00,0x18,0x24,0x20,0x70,0x20,0x20,0x20,0x70,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x3c,0x44,0x44,0x44,0x3c,0x04,0x44,0x38,

        7, // 0x68 'h'
        0x00,0x40,0x40,0x40,0x58,0x64,0x44,0x44,0x44,0x00,0x00,

        7, // 0x69 'i'
        0x00,0x10,0x00,0x30,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x6a 'j'
        0x00,0x08,0x00,0x18,0x08,0x08,0x08,0x08,0x48,0x30,0x00,

        7, // 0x6b 'k'
        0x00,0x40,0x40,0x44,0x48,0x50,0x68,0x44,0x44,0x00,0x00,

        7, // 0x6c 'l'
        0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x6d 'm'
        0x00,0x00,0x00,0xa8,0x54,0x54,0x54,0x54,0x54,0x00,0x00,

        7, // 0x6e 'n'
        0x00,0x00,0x00,0xb8,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x6f 'o'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x3c,0x44,0x44,0x44,0x44,0x3c,0x04,0x04,

        7, // 0x72 'r'
        0x00,0x00,0x00,0x58,0x64,0x44,0x40,0x40,0x40,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x3c,0x40,0x38,0x04,0x04,0x78,0x00,0x00,

        7, // 0x74 't'
        0x00,0x20,0x20,0x70,0x20,0x20,0x20,0x24,0x18,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x3a,0x00,0x00,

        7, // 0x76 'v'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x28,0x10,0x00,0x00,

        7, // 0x77 'w'
        0x00,0x00,0x00,0x44,0x44,0x54,0x54,0x6c,0x44,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x44,0x44,0x44,0x3c,0x04,0x08,0x30,0x00,

        7, // 0x7a 'z'
        0x00,0x00,0x00,0x7c,0x08,0x10,0x20,0x44,0x7c,0x00,0x00,

        7, // 0x7b '{'
        0x00,0x0c,0x10,0x10,0x10,0x60,0x10,0x10,0x0c,0x00,0x00,

        7, // 0x7c '|'
        0x00,0x20,0x20,0x20,0x00,0x20,0x20,0x20,0x20,0x00,0x00,

        7, // 0x7d '}'
        0x00,0x60,0x10,0x10,0x10,0x0c,0x10,0x10,0x60,0x00,0x00,

        7, // 0x7e '~'
        0x00,0x00,0x64,0x98,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x7f ''
        0x00,0x00,0x00,0x10,0x28,0x44,0x44,0x7c,0x00,0x00,0x00,
        0
    };

    const int8u gse7x11_bold[] = 
    {
        11, 0, 32, 128-32,

        0x00,0x00,0x0c,0x00,0x18,0x00,0x24,0x00,0x30,0x00,0x3c,0x00,0x48,0x00,0x54,0x00,0x60,0x00,
        0x6c,0x00,0x78,0x00,0x84,0x00,0x90,0x00,0x9c,0x00,0xa8,0x00,0xb4,0x00,0xc0,0x00,0xcc,0x00,
        0xd8,0x00,0xe4,0x00,0xf0,0x00,0xfc,0x00,0x08,0x01,0x14,0x01,0x20,0x01,0x2c,0x01,0x38,0x01,
        0x44,0x01,0x50,0x01,0x5c,0x01,0x68,0x01,0x74,0x01,0x80,0x01,0x8c,0x01,0x98,0x01,0xa4,0x01,
        0xb0,0x01,0xbc,0x01,0xc8,0x01,0xd4,0x01,0xe0,0x01,0xec,0x01,0xf8,0x01,0x04,0x02,0x10,0x02,
        0x1c,0x02,0x28,0x02,0x34,0x02,0x40,0x02,0x4c,0x02,0x58,0x02,0x64,0x02,0x70,0x02,0x7c,0x02,
        0x88,0x02,0x94,0x02,0xa0,0x02,0xac,0x02,0xb8,0x02,0xc4,0x02,0xd0,0x02,0xdc,0x02,0xe8,0x02,
        0xf4,0x02,0x00,0x03,0x0c,0x03,0x18,0x03,0x24,0x03,0x30,0x03,0x3c,0x03,0x48,0x03,0x54,0x03,
        0x60,0x03,0x6c,0x03,0x78,0x03,0x84,0x03,0x90,0x03,0x9c,0x03,0xa8,0x03,0xb4,0x03,0xc0,0x03,
        0xcc,0x03,0xd8,0x03,0xe4,0x03,0xf0,0x03,0xfc,0x03,0x08,0x04,0x14,0x04,0x20,0x04,0x2c,0x04,
        0x38,0x04,0x44,0x04,0x50,0x04,0x5c,0x04,0x68,0x04,0x74,0x04,

        7, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x21 '!'
        0x00,0x30,0x30,0x30,0x30,0x30,0x00,0x30,0x30,0x00,0x00,

        7, // 0x22 '"'
        0x00,0x6c,0x6c,0x28,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x23 '#'
        0x00,0x48,0x48,0xfc,0x48,0x48,0xfc,0x48,0x48,0x00,0x00,

        7, // 0x24 '$'
        0x30,0x30,0x78,0xcc,0xc0,0x78,0x0c,0xcc,0x78,0x30,0x30,

        7, // 0x25 '%'
        0x00,0x00,0xc4,0x0c,0x18,0x30,0x60,0xc0,0x8c,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x30,0x58,0x58,0x30,0x74,0xdc,0xd8,0x6c,0x00,0x00,

        7, // 0x27 '''
        0x00,0x30,0x30,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x00,0x0c,0x18,0x30,0x30,0x30,0x30,0x18,0x0c,0x00,0x00,

        7, // 0x29 ')'
        0x00,0xc0,0x60,0x30,0x30,0x30,0x30,0x60,0xc0,0x00,0x00,

        7, // 0x2a '*'
        0x00,0x00,0x00,0x20,0xa8,0x70,0xa8,0x20,0x00,0x00,0x00,

        7, // 0x2b '+'
        0x00,0x00,0x00,0x30,0x30,0xfc,0x30,0x30,0x00,0x00,0x00,

        7, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x60,0x00,

        7, // 0x2d '-'
        0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,

        7, // 0x2f '/'
        0x00,0x0c,0x0c,0x18,0x18,0x30,0x30,0x60,0x60,0x00,0x00,

        7, // 0x30 '0'
        0x00,0x78,0xcc,0xcc,0xdc,0xec,0xcc,0xcc,0x78,0x00,0x00,

        7, // 0x31 '1'
        0x00,0x30,0x70,0xf0,0x30,0x30,0x30,0x30,0xfc,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x78,0xcc,0xcc,0x18,0x30,0x60,0xcc,0xfc,0x00,0x00,

        7, // 0x33 '3'
        0x00,0xfc,0x98,0x30,0x78,0x0c,0x0c,0xcc,0x78,0x00,0x00,

        7, // 0x34 '4'
        0x00,0x18,0x30,0x68,0xd8,0xd8,0xfc,0x18,0x3c,0x00,0x00,

        7, // 0x35 '5'
        0x00,0xfc,0xc0,0xc0,0xf8,0x0c,0x0c,0xcc,0x78,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x38,0x60,0xc0,0xf8,0xcc,0xcc,0xcc,0x78,0x00,0x00,

        7, // 0x37 '7'
        0x00,0xfc,0x8c,0x0c,0x18,0x30,0x30,0x30,0x30,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x78,0xcc,0xcc,0x78,0xcc,0xcc,0xcc,0x78,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x78,0xcc,0xcc,0xcc,0x7c,0x0c,0x18,0x70,0x00,0x00,

        7, // 0x3a ':'
        0x00,0x00,0x30,0x30,0x00,0x00,0x30,0x30,0x00,0x00,0x00,

        7, // 0x3b ';'
        0x00,0x00,0x30,0x30,0x00,0x00,0x30,0x30,0x30,0x60,0x00,

        7, // 0x3c '<'
        0x00,0x00,0x0c,0x18,0x30,0x60,0x30,0x18,0x0c,0x00,0x00,

        7, // 0x3d '='
        0x00,0x00,0x00,0x00,0xfc,0x00,0xfc,0x00,0x00,0x00,0x00,

        7, // 0x3e '>'
        0x00,0x00,0x60,0x30,0x18,0x0c,0x18,0x30,0x60,0x00,0x00,

        7, // 0x3f '?'
        0x00,0x78,0xcc,0xcc,0x18,0x30,0x30,0x00,0x30,0x00,0x00,

        7, // 0x40 '@'
        0x00,0x70,0x88,0x04,0x74,0xb4,0xb4,0xb4,0x68,0x00,0x00,

        7, // 0x41 'A'
        0x00,0x30,0x78,0xcc,0xcc,0xfc,0xcc,0xcc,0xcc,0x00,0x00,

        7, // 0x42 'B'
        0x00,0xf8,0xcc,0xcc,0xf8,0xcc,0xcc,0xcc,0xf8,0x00,0x00,

        7, // 0x43 'C'
        0x00,0x78,0xcc,0xc0,0xc0,0xc0,0xc0,0xcc,0x78,0x00,0x00,

        7, // 0x44 'D'
        0x00,0xf0,0xd8,0xcc,0xcc,0xcc,0xcc,0xd8,0xf0,0x00,0x00,

        7, // 0x45 'E'
        0x00,0xfc,0xc4,0xd0,0xf0,0xd0,0xc0,0xc4,0xfc,0x00,0x00,

        7, // 0x46 'F'
        0x00,0xfc,0xc4,0xd0,0xf0,0xd0,0xc0,0xc0,0xc0,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x78,0xcc,0xc0,0xc0,0xdc,0xcc,0xcc,0x78,0x00,0x00,

        7, // 0x48 'H'
        0x00,0xcc,0xcc,0xcc,0xfc,0xcc,0xcc,0xcc,0xcc,0x00,0x00,

        7, // 0x49 'I'
        0x00,0x78,0x30,0x30,0x30,0x30,0x30,0x30,0x78,0x00,0x00,

        7, // 0x4a 'J'
        0x00,0x3c,0x18,0x18,0x18,0x18,0xd8,0xd8,0x70,0x00,0x00,

        7, // 0x4b 'K'
        0x00,0xcc,0xcc,0xd8,0xf0,0xd8,0xcc,0xcc,0xcc,0x00,0x00,

        7, // 0x4c 'L'
        0x00,0xc0,0xc0,0xc0,0xc0,0xc0,0xc0,0xc4,0xfc,0x00,0x00,

        7, // 0x4d 'M'
        0x00,0x84,0xcc,0xfc,0xb4,0xcc,0xcc,0xcc,0xcc,0x00,0x00,

        7, // 0x4e 'N'
        0x00,0xcc,0xcc,0xec,0xfc,0xdc,0xcc,0xcc,0xcc,0x00,0x00,

        7, // 0x4f 'O'
        0x00,0x78,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,

        7, // 0x50 'P'
        0x00,0xf8,0xcc,0xcc,0xcc,0xf8,0xc0,0xc0,0xc0,0x00,0x00,

        7, // 0x51 'Q'
        0x00,0x78,0xcc,0xcc,0xcc,0xcc,0xdc,0x78,0x18,0x0c,0x00,

        7, // 0x52 'R'
        0x00,0xf8,0xcc,0xcc,0xcc,0xf8,0xd8,0xcc,0xcc,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x78,0xcc,0xe0,0x70,0x38,0x1c,0xcc,0x78,0x00,0x00,

        7, // 0x54 'T'
        0x00,0xfc,0xb4,0x30,0x30,0x30,0x30,0x30,0x30,0x00,0x00,

        7, // 0x55 'U'
        0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,

        7, // 0x56 'V'
        0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x78,0x30,0x00,0x00,

        7, // 0x57 'W'
        0x00,0xcc,0xcc,0xcc,0xcc,0xb4,0xfc,0xcc,0x84,0x00,0x00,

        7, // 0x58 'X'
        0x00,0xcc,0xcc,0x78,0x30,0x78,0xcc,0xcc,0xcc,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0xcc,0xcc,0xcc,0x78,0x30,0x30,0x30,0x78,0x00,0x00,

        7, // 0x5a 'Z'
        0x00,0xfc,0x8c,0x18,0x30,0x60,0xc0,0xc4,0xfc,0x00,0x00,

        7, // 0x5b '['
        0x00,0x78,0x60,0x60,0x60,0x60,0x60,0x60,0x78,0x00,0x00,

        7, // 0x5c '\'
        0x00,0x60,0x60,0x30,0x30,0x18,0x18,0x0c,0x0c,0x00,0x00,

        7, // 0x5d ']'
        0x00,0x78,0x18,0x18,0x18,0x18,0x18,0x18,0x78,0x00,0x00,

        7, // 0x5e '^'
        0x00,0x10,0x38,0x6c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,

        7, // 0x60 '`'
        0x00,0x30,0x30,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x70,0x18,0x78,0xd8,0xd8,0x6c,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x60,0x60,0x60,0x78,0x6c,0x6c,0x6c,0x78,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x78,0xcc,0xc0,0xc0,0xcc,0x78,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x18,0x18,0x18,0x78,0xd8,0xd8,0xd8,0x6c,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x78,0xcc,0xfc,0xc0,0xcc,0x78,0x00,0x00,

        7, // 0x66 'f'
        0x00,0x18,0x34,0x30,0x78,0x30,0x30,0x30,0x78,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x6c,0xd8,0xd8,0xd8,0x78,0x18,0xd8,0x70,

        7, // 0x68 'h'
        0x00,0xc0,0xc0,0xd8,0xec,0xcc,0xcc,0xcc,0xcc,0x00,0x00,

        7, // 0x69 'i'
        0x00,0x30,0x00,0x70,0x30,0x30,0x30,0x30,0x78,0x00,0x00,

        7, // 0x6a 'j'
        0x00,0x0c,0x00,0x1c,0x0c,0x0c,0x0c,0x0c,0x6c,0x6c,0x38,

        7, // 0x6b 'k'
        0x00,0xc0,0xc0,0xcc,0xcc,0xd8,0xf0,0xd8,0xcc,0x00,0x00,

        7, // 0x6c 'l'
        0x00,0x70,0x30,0x30,0x30,0x30,0x30,0x30,0x78,0x00,0x00,

        7, // 0x6d 'm'
        0x00,0x00,0x00,0xe8,0xfc,0xd4,0xd4,0xc4,0xc4,0x00,0x00,

        7, // 0x6e 'n'
        0x00,0x00,0x00,0xd8,0x6c,0x6c,0x6c,0x6c,0x6c,0x00,0x00,

        7, // 0x6f 'o'
        0x00,0x00,0x00,0x78,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0xf8,0xcc,0xcc,0xcc,0xf8,0xc0,0xc0,0xc0,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x7c,0xcc,0xcc,0xcc,0x7c,0x0c,0x0c,0x0c,

        7, // 0x72 'r'
        0x00,0x00,0x00,0xd8,0xec,0xcc,0xc0,0xc0,0xc0,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x78,0xcc,0x60,0x18,0xcc,0x78,0x00,0x00,

        7, // 0x74 't'
        0x00,0x20,0x60,0x60,0xf0,0x60,0x60,0x68,0x30,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0xd8,0xd8,0xd8,0xd8,0xd8,0x6c,0x00,0x00,

        7, // 0x76 'v'
        0x00,0x00,0x00,0xcc,0xcc,0xcc,0xcc,0x78,0x30,0x00,0x00,

        7, // 0x77 'w'
        0x00,0x00,0x00,0xcc,0xcc,0xb4,0xfc,0xcc,0x84,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0xcc,0x78,0x30,0x78,0xcc,0xcc,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0xcc,0xcc,0xcc,0xcc,0x7c,0x0c,0x18,0xf0,

        7, // 0x7a 'z'
        0x00,0x00,0x00,0xfc,0x98,0x30,0x60,0xc4,0xfc,0x00,0x00,

        7, // 0x7b '{'
        0x1c,0x30,0x30,0x30,0xe0,0x30,0x30,0x30,0x1c,0x00,0x00,

        7, // 0x7c '|'
        0x30,0x30,0x30,0x30,0x00,0x30,0x30,0x30,0x30,0x00,0x00,

        7, // 0x7d '}'
        0xe0,0x30,0x30,0x30,0x1c,0x30,0x30,0x30,0xe0,0x00,0x00,

        7, // 0x7e '~'
        0x00,0x34,0x58,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x7f ''
        0x00,0x00,0x00,0x30,0x78,0xcc,0xcc,0xfc,0x00,0x00,0x00,
        0
    };

    const int8u gse7x15[] = 
    {
        15, 0, 32, 128-32,

        0x00,0x00,0x10,0x00,0x20,0x00,0x30,0x00,0x40,0x00,0x50,0x00,0x60,0x00,0x70,0x00,0x80,0x00,
        0x90,0x00,0xa0,0x00,0xb0,0x00,0xc0,0x00,0xd0,0x00,0xe0,0x00,0xf0,0x00,0x00,0x01,0x10,0x01,
        0x20,0x01,0x30,0x01,0x40,0x01,0x50,0x01,0x60,0x01,0x70,0x01,0x80,0x01,0x90,0x01,0xa0,0x01,
        0xb0,0x01,0xc0,0x01,0xd0,0x01,0xe0,0x01,0xf0,0x01,0x00,0x02,0x10,0x02,0x20,0x02,0x30,0x02,
        0x40,0x02,0x50,0x02,0x60,0x02,0x70,0x02,0x80,0x02,0x90,0x02,0xa0,0x02,0xb0,0x02,0xc0,0x02,
        0xd0,0x02,0xe0,0x02,0xf0,0x02,0x00,0x03,0x10,0x03,0x20,0x03,0x30,0x03,0x40,0x03,0x50,0x03,
        0x60,0x03,0x70,0x03,0x80,0x03,0x90,0x03,0xa0,0x03,0xb0,0x03,0xc0,0x03,0xd0,0x03,0xe0,0x03,
        0xf0,0x03,0x00,0x04,0x10,0x04,0x20,0x04,0x30,0x04,0x40,0x04,0x50,0x04,0x60,0x04,0x70,0x04,
        0x80,0x04,0x90,0x04,0xa0,0x04,0xb0,0x04,0xc0,0x04,0xd0,0x04,0xe0,0x04,0xf0,0x04,0x00,0x05,
        0x10,0x05,0x20,0x05,0x30,0x05,0x40,0x05,0x50,0x05,0x60,0x05,0x70,0x05,0x80,0x05,0x90,0x05,
        0xa0,0x05,0xb0,0x05,0xc0,0x05,0xd0,0x05,0xe0,0x05,0xf0,0x05,

        7, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x21 '!'
        0x00,0x00,0x10,0x38,0x38,0x38,0x38,0x10,0x10,0x00,0x10,0x10,0x00,0x00,0x00,

        7, // 0x22 '"'
        0x00,0x00,0x24,0x24,0x24,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x23 '#'
        0x00,0x00,0x48,0x48,0x48,0xfc,0x48,0x48,0xfc,0x48,0x48,0x48,0x00,0x00,0x00,

        7, // 0x24 '$'
        0x00,0x00,0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x54,0x38,0x10,0x00,0x00,0x00,

        7, // 0x25 '%'
        0x00,0x00,0x44,0x44,0x08,0x08,0x10,0x10,0x20,0x20,0x44,0x44,0x00,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x00,0x00,0x30,0x48,0x48,0x30,0x60,0x94,0x98,0x90,0x6c,0x00,0x00,0x00,

        7, // 0x27 '''
        0x00,0x00,0x20,0x20,0x20,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x00,0x04,0x08,0x10,0x20,0x20,0x20,0x20,0x20,0x10,0x08,0x04,0x00,0x00,0x00,

        7, // 0x29 ')'
        0x00,0x40,0x20,0x10,0x08,0x08,0x08,0x08,0x08,0x10,0x20,0x40,0x00,0x00,0x00,

        7, // 0x2a '*'
        0x00,0x00,0x00,0x00,0x00,0x20,0xa8,0x70,0xa8,0x20,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2b '+'
        0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x7c,0x10,0x10,0x10,0x00,0x00,0x00,0x00,

        7, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x60,0x00,

        7, // 0x2d '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,

        7, // 0x2f '/'
        0x00,0x00,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,0x00,0x00,

        7, // 0x30 '0'
        0x00,0x00,0x38,0x44,0x44,0x4c,0x54,0x64,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x31 '1'
        0x00,0x00,0x10,0x10,0x30,0x10,0x10,0x10,0x10,0x10,0x10,0x7c,0x00,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x00,0x38,0x44,0x44,0x04,0x08,0x10,0x20,0x40,0x44,0x7c,0x00,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x00,0x7c,0x44,0x08,0x10,0x38,0x04,0x04,0x04,0x44,0x38,0x00,0x00,0x00,

        7, // 0x34 '4'
        0x00,0x00,0x08,0x10,0x20,0x40,0x48,0x48,0x7c,0x08,0x08,0x1c,0x00,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x00,0x7c,0x40,0x40,0x40,0x78,0x04,0x04,0x04,0x44,0x38,0x00,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x00,0x18,0x20,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x37 '7'
        0x00,0x00,0x7c,0x44,0x04,0x04,0x08,0x08,0x10,0x10,0x10,0x10,0x00,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x00,0x38,0x44,0x44,0x44,0x38,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x3c,0x04,0x04,0x08,0x30,0x00,0x00,0x00,

        7, // 0x3a ':'
        0x00,0x00,0x00,0x00,0x00,0x30,0x00,0x00,0x00,0x00,0x30,0x00,0x00,0x00,0x00,

        7, // 0x3b ';'
        0x00,0x00,0x00,0x00,0x00,0x30,0x00,0x00,0x00,0x00,0x30,0x30,0x60,0x00,0x00,

        7, // 0x3c '<'
        0x00,0x00,0x00,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x00,0x00,0x00,

        7, // 0x3d '='
        0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x3e '>'
        0x00,0x00,0x00,0x40,0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x40,0x00,0x00,0x00,

        7, // 0x3f '?'
        0x00,0x00,0x78,0x84,0x84,0x84,0x08,0x10,0x20,0x20,0x00,0x20,0x00,0x00,0x00,

        7, // 0x40 '@'
        0x00,0x00,0x00,0x30,0x48,0x04,0x34,0x54,0x54,0x54,0x54,0x28,0x00,0x00,0x00,

        7, // 0x41 'A'
        0x00,0x00,0x10,0x28,0x44,0x44,0x44,0x7c,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x00,0x78,0x44,0x44,0x44,0x78,0x44,0x44,0x44,0x44,0x78,0x00,0x00,0x00,

        7, // 0x43 'C'
        0x00,0x00,0x38,0x44,0x44,0x40,0x40,0x40,0x40,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x44 'D'
        0x00,0x00,0x70,0x48,0x44,0x44,0x44,0x44,0x44,0x44,0x48,0x70,0x00,0x00,0x00,

        7, // 0x45 'E'
        0x00,0x00,0x7c,0x40,0x40,0x40,0x70,0x40,0x40,0x40,0x40,0x7c,0x00,0x00,0x00,

        7, // 0x46 'F'
        0x00,0x00,0x7c,0x40,0x40,0x40,0x70,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x00,0x38,0x44,0x40,0x40,0x40,0x5c,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x48 'H'
        0x00,0x00,0x44,0x44,0x44,0x44,0x7c,0x44,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x49 'I'
        0x00,0x00,0x38,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,

        7, // 0x4a 'J'
        0x00,0x00,0x1c,0x08,0x08,0x08,0x08,0x08,0x08,0x48,0x48,0x30,0x00,0x00,0x00,

        7, // 0x4b 'K'
        0x00,0x00,0x44,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x4c 'L'
        0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7c,0x00,0x00,0x00,

        7, // 0x4d 'M'
        0x00,0x00,0x44,0x6c,0x54,0x54,0x44,0x44,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x4e 'N'
        0x00,0x00,0x44,0x44,0x44,0x64,0x54,0x4c,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x4f 'O'
        0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x51 'Q'
        0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x54,0x48,0x34,0x00,0x00,0x00,

        7, // 0x52 'R'
        0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x50,0x48,0x44,0x44,0x00,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x00,0x38,0x44,0x44,0x40,0x38,0x04,0x04,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x54 'T'
        0x00,0x00,0x7c,0x54,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,0x00,

        7, // 0x55 'U'
        0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x56 'V'
        0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,0x00,

        7, // 0x57 'W'
        0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x54,0x54,0x6c,0x44,0x00,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x00,0x44,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x00,0x44,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,

        7, // 0x5a 'Z'
        0x00,0x00,0x7c,0x04,0x04,0x08,0x10,0x20,0x40,0x40,0x40,0x7c,0x00,0x00,0x00,

        7, // 0x5b '['
        0x00,0x38,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x38,0x00,0x00,

        7, // 0x5c '\'
        0x00,0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x00,0x00,0x00,

        7, // 0x5d ']'
        0x00,0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x38,0x00,0x00,

        7, // 0x5e '^'
        0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,

        7, // 0x60 '`'
        0x00,0x20,0x20,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x38,0x44,0x04,0x3c,0x44,0x44,0x44,0x3a,0x00,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x00,0x40,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x00,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x40,0x40,0x44,0x38,0x00,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x00,0x04,0x04,0x04,0x3c,0x44,0x44,0x44,0x44,0x44,0x3a,0x00,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x38,0x44,0x44,0x7c,0x40,0x40,0x44,0x38,0x00,0x00,0x00,

        7, // 0x66 'f'
        0x00,0x00,0x18,0x24,0x20,0x70,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x3a,0x44,0x44,0x44,0x44,0x44,0x3c,0x04,0x44,0x38,0x00,

        7, // 0x68 'h'
        0x00,0x00,0x40,0x40,0x40,0x58,0x64,0x44,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x69 'i'
        0x00,0x00,0x10,0x10,0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,

        7, // 0x6a 'j'
        0x00,0x00,0x08,0x08,0x00,0x18,0x08,0x08,0x08,0x08,0x08,0x48,0x48,0x30,0x00,

        7, // 0x6b 'k'
        0x00,0x00,0x40,0x40,0x44,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00,0x00,0x00,

        7, // 0x6c 'l'
        0x00,0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,

        7, // 0x6d 'm'
        0x00,0x00,0x00,0x00,0xa8,0x54,0x54,0x54,0x54,0x54,0x54,0x54,0x00,0x00,0x00,

        7, // 0x6e 'n'
        0x00,0x00,0x00,0x00,0xb8,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x6f 'o'
        0x00,0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x00,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x3c,0x44,0x44,0x44,0x44,0x44,0x3c,0x04,0x04,0x04,0x00,

        7, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x58,0x64,0x44,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x74 't'
        0x00,0x00,0x20,0x20,0x20,0x70,0x20,0x20,0x20,0x20,0x24,0x18,0x00,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x3a,0x00,0x00,0x00,

        7, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,0x00,

        7, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x54,0x54,0x6c,0x44,0x00,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x3c,0x04,0x08,0x70,0x00,

        7, // 0x7a 'z'
        0x00,0x00,0x00,0x00,0x7c,0x04,0x08,0x10,0x20,0x40,0x40,0x7c,0x00,0x00,0x00,

        7, // 0x7b '{'
        0x00,0x0c,0x10,0x10,0x10,0x10,0x10,0x60,0x10,0x10,0x10,0x10,0x0c,0x00,0x00,

        7, // 0x7c '|'
        0x00,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x00,0x00,

        7, // 0x7d '}'
        0x00,0x60,0x10,0x10,0x10,0x10,0x10,0x0c,0x10,0x10,0x10,0x10,0x60,0x00,0x00,

        7, // 0x7e '~'
        0x00,0x00,0x64,0x98,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x7f ''
        0x00,0x00,0x00,0x00,0x00,0x10,0x28,0x44,0x44,0x7c,0x00,0x00,0x00,0x00,0x00,
        0
    };

    const int8u gse7x15_bold[] = 
    {
        15, 0, 32, 128-32,

        0x00,0x00,0x10,0x00,0x20,0x00,0x30,0x00,0x40,0x00,0x50,0x00,0x60,0x00,0x70,0x00,0x80,0x00,
        0x90,0x00,0xa0,0x00,0xb0,0x00,0xc0,0x00,0xd0,0x00,0xe0,0x00,0xf0,0x00,0x00,0x01,0x10,0x01,
        0x20,0x01,0x30,0x01,0x40,0x01,0x50,0x01,0x60,0x01,0x70,0x01,0x80,0x01,0x90,0x01,0xa0,0x01,
        0xb0,0x01,0xc0,0x01,0xd0,0x01,0xe0,0x01,0xf0,0x01,0x00,0x02,0x10,0x02,0x20,0x02,0x30,0x02,
        0x40,0x02,0x50,0x02,0x60,0x02,0x70,0x02,0x80,0x02,0x90,0x02,0xa0,0x02,0xb0,0x02,0xc0,0x02,
        0xd0,0x02,0xe0,0x02,0xf0,0x02,0x00,0x03,0x10,0x03,0x20,0x03,0x30,0x03,0x40,0x03,0x50,0x03,
        0x60,0x03,0x70,0x03,0x80,0x03,0x90,0x03,0xa0,0x03,0xb0,0x03,0xc0,0x03,0xd0,0x03,0xe0,0x03,
        0xf0,0x03,0x00,0x04,0x10,0x04,0x20,0x04,0x30,0x04,0x40,0x04,0x50,0x04,0x60,0x04,0x70,0x04,
        0x80,0x04,0x90,0x04,0xa0,0x04,0xb0,0x04,0xc0,0x04,0xd0,0x04,0xe0,0x04,0xf0,0x04,0x00,0x05,
        0x10,0x05,0x20,0x05,0x30,0x05,0x40,0x05,0x50,0x05,0x60,0x05,0x70,0x05,0x80,0x05,0x90,0x05,
        0xa0,0x05,0xb0,0x05,0xc0,0x05,0xd0,0x05,0xe0,0x05,0xf0,0x05,

        7, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x21 '!'
        0x00,0x00,0x00,0x30,0x78,0x78,0x78,0x30,0x30,0x00,0x30,0x30,0x00,0x00,0x00,

        7, // 0x22 '"'
        0x00,0x00,0x6c,0x6c,0x6c,0x28,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x23 '#'
        0x00,0x00,0x48,0x48,0x48,0xfc,0x48,0x48,0xfc,0x48,0x48,0x48,0x00,0x00,0x00,

        7, // 0x24 '$'
        0x00,0x30,0x30,0x78,0xcc,0xe0,0x70,0x38,0x1c,0xcc,0x78,0x30,0x30,0x00,0x00,

        7, // 0x25 '%'
        0x00,0x00,0x00,0x64,0x6c,0x08,0x18,0x10,0x30,0x20,0x6c,0x4c,0x00,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x00,0x00,0x30,0x58,0x58,0x30,0x74,0xdc,0xd8,0xd8,0x6c,0x00,0x00,0x00,

        7, // 0x27 '''
        0x00,0x00,0x30,0x30,0x30,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x00,0x0c,0x18,0x30,0x60,0x60,0x60,0x60,0x60,0x30,0x18,0x0c,0x00,0x00,0x00,

        7, // 0x29 ')'
        0x00,0xc0,0x60,0x30,0x18,0x18,0x18,0x18,0x18,0x30,0x60,0xc0,0x00,0x00,0x00,

        7, // 0x2a '*'
        0x00,0x00,0x00,0x00,0x00,0x20,0xa8,0x70,0xa8,0x20,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2b '+'
        0x00,0x00,0x00,0x00,0x00,0x30,0x30,0xfc,0x30,0x30,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x60,0x00,

        7, // 0x2d '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,

        7, // 0x2f '/'
        0x00,0x00,0x0c,0x0c,0x18,0x18,0x30,0x30,0x60,0x60,0xc0,0xc0,0x00,0x00,0x00,

        7, // 0x30 '0'
        0x00,0x00,0x78,0xcc,0xcc,0xcc,0xdc,0xec,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x31 '1'
        0x00,0x00,0x30,0x30,0x70,0xf0,0x30,0x30,0x30,0x30,0x30,0xfc,0x00,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x00,0x78,0xcc,0xcc,0x0c,0x18,0x30,0x60,0xc0,0xcc,0xfc,0x00,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x00,0xfc,0x8c,0x18,0x30,0x78,0x0c,0x0c,0x0c,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x34 '4'
        0x00,0x00,0x18,0x30,0x60,0xc8,0xd8,0xd8,0xfc,0x18,0x18,0x3c,0x00,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x00,0xfc,0xc0,0xc0,0xc0,0xf8,0x0c,0x0c,0x0c,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x00,0x38,0x60,0xc0,0xc0,0xf8,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x37 '7'
        0x00,0x00,0xfc,0x8c,0x0c,0x0c,0x18,0x18,0x30,0x30,0x30,0x30,0x00,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x00,0x78,0xcc,0xcc,0xcc,0x78,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x00,0x78,0xcc,0xcc,0xcc,0xcc,0x7c,0x0c,0x0c,0x18,0x70,0x00,0x00,0x00,

        7, // 0x3a ':'
        0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x00,

        7, // 0x3b ';'
        0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x60,0x00,

        7, // 0x3c '<'
        0x00,0x00,0x00,0x0c,0x18,0x30,0x60,0xc0,0x60,0x30,0x18,0x0c,0x00,0x00,0x00,

        7, // 0x3d '='
        0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x3e '>'
        0x00,0x00,0x00,0xc0,0x60,0x30,0x18,0x0c,0x18,0x30,0x60,0xc0,0x00,0x00,0x00,

        7, // 0x3f '?'
        0x00,0x00,0x78,0xcc,0xcc,0x18,0x30,0x30,0x30,0x00,0x30,0x30,0x00,0x00,0x00,

        7, // 0x40 '@'
        0x00,0x00,0x00,0x70,0x88,0x04,0x74,0xb4,0xb4,0xb4,0xb4,0x68,0x00,0x00,0x00,

        7, // 0x41 'A'
        0x00,0x00,0x30,0x78,0xcc,0xcc,0xcc,0xfc,0xcc,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x00,0xf8,0xcc,0xcc,0xcc,0xf8,0xcc,0xcc,0xcc,0xcc,0xf8,0x00,0x00,0x00,

        7, // 0x43 'C'
        0x00,0x00,0x78,0xcc,0xc4,0xc0,0xc0,0xc0,0xc0,0xc4,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x44 'D'
        0x00,0x00,0xf0,0xd8,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xd8,0xf0,0x00,0x00,0x00,

        7, // 0x45 'E'
        0x00,0x00,0xfc,0xc4,0xc0,0xd0,0xf0,0xd0,0xc0,0xc0,0xc4,0xfc,0x00,0x00,0x00,

        7, // 0x46 'F'
        0x00,0x00,0xfc,0xc4,0xc0,0xd0,0xf0,0xd0,0xc0,0xc0,0xc0,0xc0,0x00,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x00,0x78,0xcc,0xc0,0xc0,0xc0,0xdc,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x48 'H'
        0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xfc,0xcc,0xcc,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x49 'I'
        0x00,0x00,0x78,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x78,0x00,0x00,0x00,

        7, // 0x4a 'J'
        0x00,0x00,0x3c,0x18,0x18,0x18,0x18,0x18,0x18,0xd8,0xd8,0x70,0x00,0x00,0x00,

        7, // 0x4b 'K'
        0x00,0x00,0xcc,0xcc,0xd8,0xd8,0xf0,0xd8,0xd8,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x4c 'L'
        0x00,0x00,0xc0,0xc0,0xc0,0xc0,0xc0,0xc0,0xc0,0xc0,0xc4,0xfc,0x00,0x00,0x00,

        7, // 0x4d 'M'
        0x00,0x00,0x84,0xcc,0xfc,0xb4,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x4e 'N'
        0x00,0x00,0xcc,0xcc,0xcc,0xec,0xfc,0xdc,0xcc,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x4f 'O'
        0x00,0x00,0x78,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x00,0xf8,0xcc,0xcc,0xcc,0xcc,0xf8,0xc0,0xc0,0xc0,0xc0,0x00,0x00,0x00,

        7, // 0x51 'Q'
        0x00,0x00,0x78,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xdc,0x78,0x18,0x0c,0x00,0x00,

        7, // 0x52 'R'
        0x00,0x00,0xf8,0xcc,0xcc,0xcc,0xcc,0xf8,0xd8,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x00,0x78,0xcc,0xcc,0xe0,0x70,0x38,0x1c,0xcc,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x54 'T'
        0x00,0x00,0xfc,0xb4,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x00,0x00,0x00,

        7, // 0x55 'U'
        0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x56 'V'
        0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x78,0x30,0x00,0x00,0x00,

        7, // 0x57 'W'
        0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0xb4,0xfc,0xcc,0x84,0x00,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x00,0xcc,0xcc,0xcc,0x78,0x30,0x78,0xcc,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x00,0xcc,0xcc,0xcc,0xcc,0x78,0x30,0x30,0x30,0x30,0x78,0x00,0x00,0x00,

        7, // 0x5a 'Z'
        0x00,0x00,0xfc,0x8c,0x0c,0x18,0x30,0x60,0xc0,0xc0,0xc4,0xfc,0x00,0x00,0x00,

        7, // 0x5b '['
        0x00,0x78,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x78,0x00,0x00,

        7, // 0x5c '\'
        0x00,0x00,0xc0,0xc0,0x60,0x60,0x30,0x30,0x18,0x18,0x0c,0x0c,0x00,0x00,0x00,

        7, // 0x5d ']'
        0x00,0x78,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x78,0x00,0x00,

        7, // 0x5e '^'
        0x00,0x10,0x38,0x6c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0x00,0x00,

        7, // 0x60 '`'
        0x00,0x30,0x30,0x30,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x70,0xd8,0x18,0x78,0xd8,0xd8,0xd8,0x6c,0x00,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x00,0x60,0x60,0x60,0x78,0x6c,0x6c,0x6c,0x6c,0x6c,0x78,0x00,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x78,0xcc,0xc0,0xc0,0xc0,0xc0,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x00,0x18,0x18,0x18,0x78,0xd8,0xd8,0xd8,0xd8,0xd8,0x6c,0x00,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x78,0xcc,0xcc,0xfc,0xc0,0xc0,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x66 'f'
        0x00,0x00,0x30,0x68,0x60,0x60,0xf0,0x60,0x60,0x60,0x60,0xf0,0x00,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x6c,0xd8,0xd8,0xd8,0xd8,0xd8,0x78,0x18,0xd8,0x70,0x00,

        7, // 0x68 'h'
        0x00,0x00,0xc0,0xc0,0xc0,0xd8,0xec,0xcc,0xcc,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x69 'i'
        0x00,0x00,0x30,0x30,0x00,0x70,0x30,0x30,0x30,0x30,0x30,0x78,0x00,0x00,0x00,

        7, // 0x6a 'j'
        0x00,0x00,0x18,0x18,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0xd8,0xd8,0x70,0x00,

        7, // 0x6b 'k'
        0x00,0x00,0xc0,0xc0,0xcc,0xcc,0xcc,0xd8,0xf0,0xd8,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x6c 'l'
        0x00,0x00,0x70,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x78,0x00,0x00,0x00,

        7, // 0x6d 'm'
        0x00,0x00,0x00,0x00,0xe8,0xfc,0xd4,0xd4,0xd4,0xc4,0xc4,0xc4,0x00,0x00,0x00,

        7, // 0x6e 'n'
        0x00,0x00,0x00,0x00,0xd8,0x6c,0x6c,0x6c,0x6c,0x6c,0x6c,0x6c,0x00,0x00,0x00,

        7, // 0x6f 'o'
        0x00,0x00,0x00,0x00,0x78,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0x00,0xf8,0xcc,0xcc,0xcc,0xcc,0xcc,0xf8,0xc0,0xc0,0xc0,0x00,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x7c,0xcc,0xcc,0xcc,0xcc,0xcc,0x7c,0x0c,0x0c,0x0c,0x00,

        7, // 0x72 'r'
        0x00,0x00,0x00,0x00,0xd8,0xec,0xcc,0xc0,0xc0,0xc0,0xc0,0xc0,0x00,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x00,0x78,0xcc,0xe0,0x70,0x38,0x1c,0xcc,0x78,0x00,0x00,0x00,

        7, // 0x74 't'
        0x00,0x00,0x20,0x60,0x60,0xf0,0x60,0x60,0x60,0x60,0x6c,0x38,0x00,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0x00,0xd8,0xd8,0xd8,0xd8,0xd8,0xd8,0xd8,0x6c,0x00,0x00,0x00,

        7, // 0x76 'v'
        0x00,0x00,0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x78,0x30,0x00,0x00,0x00,

        7, // 0x77 'w'
        0x00,0x00,0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xb4,0xfc,0xcc,0x84,0x00,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x00,0xcc,0xcc,0x78,0x30,0x78,0xcc,0xcc,0xcc,0x00,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x00,0xcc,0xcc,0xcc,0xcc,0xcc,0xcc,0x7c,0x0c,0x18,0xf0,0x00,

        7, // 0x7a 'z'
        0x00,0x00,0x00,0x00,0xfc,0x8c,0x18,0x30,0x60,0xc0,0xc4,0xfc,0x00,0x00,0x00,

        7, // 0x7b '{'
        0x00,0x1c,0x30,0x30,0x30,0x30,0x30,0xe0,0x30,0x30,0x30,0x30,0x1c,0x00,0x00,

        7, // 0x7c '|'
        0x00,0x30,0x30,0x30,0x30,0x30,0x00,0x00,0x30,0x30,0x30,0x30,0x30,0x00,0x00,

        7, // 0x7d '}'
        0x00,0xe0,0x30,0x30,0x30,0x30,0x30,0x1c,0x30,0x30,0x30,0x30,0xe0,0x00,0x00,

        7, // 0x7e '~'
        0x00,0x00,0x34,0x58,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x7f ''
        0x00,0x00,0x00,0x00,0x00,0x30,0x78,0xcc,0xcc,0xfc,0x00,0x00,0x00,0x00,0x00,
        0
    };

    const int8u gse8x16[] = 
    {
        16, 0, 32, 128-32,

        0x00,0x00,0x11,0x00,0x22,0x00,0x33,0x00,0x44,0x00,0x55,0x00,0x66,0x00,0x77,0x00,0x88,0x00,
        0x99,0x00,0xaa,0x00,0xbb,0x00,0xcc,0x00,0xdd,0x00,0xee,0x00,0xff,0x00,0x10,0x01,0x21,0x01,
        0x32,0x01,0x43,0x01,0x54,0x01,0x65,0x01,0x76,0x01,0x87,0x01,0x98,0x01,0xa9,0x01,0xba,0x01,
        0xcb,0x01,0xdc,0x01,0xed,0x01,0xfe,0x01,0x0f,0x02,0x20,0x02,0x31,0x02,0x42,0x02,0x53,0x02,
        0x64,0x02,0x75,0x02,0x86,0x02,0x97,0x02,0xa8,0x02,0xb9,0x02,0xca,0x02,0xdb,0x02,0xec,0x02,
        0xfd,0x02,0x0e,0x03,0x1f,0x03,0x30,0x03,0x41,0x03,0x52,0x03,0x63,0x03,0x74,0x03,0x85,0x03,
        0x96,0x03,0xa7,0x03,0xb8,0x03,0xc9,0x03,0xda,0x03,0xeb,0x03,0xfc,0x03,0x0d,0x04,0x1e,0x04,
        0x2f,0x04,0x40,0x04,0x51,0x04,0x62,0x04,0x73,0x04,0x84,0x04,0x95,0x04,0xa6,0x04,0xb7,0x04,
        0xc8,0x04,0xd9,0x04,0xea,0x04,0xfb,0x04,0x0c,0x05,0x1d,0x05,0x2e,0x05,0x3f,0x05,0x50,0x05,
        0x61,0x05,0x72,0x05,0x83,0x05,0x94,0x05,0xa5,0x05,0xb6,0x05,0xc7,0x05,0xd8,0x05,0xe9,0x05,
        0xfa,0x05,0x0b,0x06,0x1c,0x06,0x2d,0x06,0x3e,0x06,0x4f,0x06,

        8, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x21 '!'
        0x00,0x00,0x10,0x38,0x38,0x38,0x38,0x10,0x10,0x00,0x10,0x10,0x00,0x00,0x00,0x00,

        8, // 0x22 '"'
        0x00,0x24,0x24,0x24,0x24,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x23 '#'
        0x00,0x00,0x24,0x24,0x24,0x7e,0x24,0x24,0x7e,0x24,0x24,0x24,0x00,0x00,0x00,0x00,

        8, // 0x24 '$'
        0x00,0x14,0x14,0x3e,0x55,0x54,0x54,0x3e,0x15,0x15,0x55,0x3e,0x14,0x14,0x00,0x00,

        8, // 0x25 '%'
        0x00,0x00,0x32,0x56,0x6c,0x04,0x08,0x08,0x10,0x13,0x25,0x26,0x00,0x00,0x00,0x00,

        8, // 0x26 '&'
        0x00,0x00,0x18,0x24,0x24,0x24,0x18,0x28,0x45,0x46,0x44,0x3b,0x00,0x00,0x00,0x00,

        8, // 0x27 '''
        0x00,0x00,0x08,0x08,0x08,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x28 '('
        0x00,0x04,0x08,0x10,0x10,0x20,0x20,0x20,0x20,0x10,0x10,0x08,0x04,0x00,0x00,0x00,

        8, // 0x29 ')'
        0x00,0x10,0x08,0x04,0x04,0x02,0x02,0x02,0x02,0x04,0x04,0x08,0x10,0x00,0x00,0x00,

        8, // 0x2a '*'
        0x00,0x00,0x00,0x00,0x66,0x24,0x18,0xff,0x18,0x24,0x66,0x00,0x00,0x00,0x00,0x00,

        8, // 0x2b '+'
        0x00,0x00,0x00,0x00,0x08,0x08,0x08,0x7f,0x08,0x08,0x08,0x00,0x00,0x00,0x00,0x00,

        8, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x18,0x30,0x20,0x00,

        8, // 0x2d '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,

        8, // 0x2f '/'
        0x00,0x02,0x02,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,0x00,0x00,

        8, // 0x30 '0'
        0x00,0x00,0x3c,0x42,0x42,0x46,0x4a,0x52,0x62,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x31 '1'
        0x00,0x00,0x08,0x08,0x18,0x38,0x08,0x08,0x08,0x08,0x08,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x32 '2'
        0x00,0x00,0x3c,0x42,0x42,0x02,0x04,0x08,0x10,0x20,0x42,0x7e,0x00,0x00,0x00,0x00,

        8, // 0x33 '3'
        0x00,0x00,0x7e,0x42,0x04,0x08,0x1c,0x02,0x02,0x02,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x34 '4'
        0x00,0x00,0x04,0x08,0x10,0x24,0x44,0x44,0x7e,0x04,0x04,0x0e,0x00,0x00,0x00,0x00,

        8, // 0x35 '5'
        0x00,0x00,0x7e,0x42,0x40,0x40,0x7c,0x02,0x02,0x02,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x36 '6'
        0x00,0x00,0x1c,0x20,0x40,0x40,0x7c,0x42,0x42,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x37 '7'
        0x00,0x00,0x7e,0x42,0x42,0x02,0x04,0x08,0x10,0x10,0x10,0x10,0x00,0x00,0x00,0x00,

        8, // 0x38 '8'
        0x00,0x00,0x3c,0x42,0x42,0x42,0x3c,0x42,0x42,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x39 '9'
        0x00,0x00,0x3c,0x42,0x42,0x42,0x42,0x3e,0x02,0x02,0x04,0x38,0x00,0x00,0x00,0x00,

        8, // 0x3a ':'
        0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x3b ';'
        0x00,0x00,0x00,0x00,0x30,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x60,0x40,0x00,

        8, // 0x3c '<'
        0x00,0x00,0x00,0x02,0x04,0x08,0x10,0x20,0x10,0x08,0x04,0x02,0x00,0x00,0x00,0x00,

        8, // 0x3d '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x3e '>'
        0x00,0x00,0x00,0x20,0x10,0x08,0x04,0x02,0x04,0x08,0x10,0x20,0x00,0x00,0x00,0x00,

        8, // 0x3f '?'
        0x00,0x00,0x3c,0x42,0x42,0x42,0x04,0x08,0x08,0x00,0x08,0x08,0x00,0x00,0x00,0x00,

        8, // 0x40 '@'
        0x00,0x00,0x3c,0x42,0x01,0x39,0x49,0x49,0x49,0x49,0x49,0x36,0x00,0x00,0x00,0x00,

        8, // 0x41 'A'
        0x00,0x00,0x18,0x24,0x42,0x42,0x42,0x7e,0x42,0x42,0x42,0x42,0x00,0x00,0x00,0x00,

        8, // 0x42 'B'
        0x00,0x00,0x7c,0x22,0x22,0x22,0x3c,0x22,0x22,0x22,0x22,0x7c,0x00,0x00,0x00,0x00,

        8, // 0x43 'C'
        0x00,0x00,0x3c,0x42,0x42,0x40,0x40,0x40,0x40,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x44 'D'
        0x00,0x00,0x7c,0x22,0x22,0x22,0x22,0x22,0x22,0x22,0x22,0x7c,0x00,0x00,0x00,0x00,

        8, // 0x45 'E'
        0x00,0x00,0x7e,0x22,0x20,0x28,0x38,0x28,0x20,0x20,0x22,0x7e,0x00,0x00,0x00,0x00,

        8, // 0x46 'F'
        0x00,0x00,0x7e,0x22,0x20,0x28,0x38,0x28,0x20,0x20,0x20,0x70,0x00,0x00,0x00,0x00,

        8, // 0x47 'G'
        0x00,0x00,0x3c,0x42,0x42,0x40,0x40,0x4e,0x42,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x48 'H'
        0x00,0x00,0x42,0x42,0x42,0x42,0x7e,0x42,0x42,0x42,0x42,0x42,0x00,0x00,0x00,0x00,

        8, // 0x49 'I'
        0x00,0x00,0x1c,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x1c,0x00,0x00,0x00,0x00,

        8, // 0x4a 'J'
        0x00,0x00,0x0e,0x04,0x04,0x04,0x04,0x04,0x04,0x44,0x44,0x38,0x00,0x00,0x00,0x00,

        8, // 0x4b 'K'
        0x00,0x00,0x62,0x22,0x24,0x28,0x30,0x28,0x24,0x22,0x22,0x62,0x00,0x00,0x00,0x00,

        8, // 0x4c 'L'
        0x00,0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x22,0x7e,0x00,0x00,0x00,0x00,

        8, // 0x4d 'M'
        0x00,0x00,0x41,0x63,0x55,0x49,0x41,0x41,0x41,0x41,0x41,0x41,0x00,0x00,0x00,0x00,

        8, // 0x4e 'N'
        0x00,0x00,0x42,0x42,0x62,0x52,0x4a,0x46,0x42,0x42,0x42,0x42,0x00,0x00,0x00,0x00,

        8, // 0x4f 'O'
        0x00,0x00,0x3c,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x50 'P'
        0x00,0x00,0x7c,0x22,0x22,0x22,0x22,0x3c,0x20,0x20,0x20,0x70,0x00,0x00,0x00,0x00,

        8, // 0x51 'Q'
        0x00,0x00,0x3c,0x42,0x42,0x42,0x42,0x42,0x42,0x4a,0x44,0x3a,0x02,0x00,0x00,0x00,

        8, // 0x52 'R'
        0x00,0x00,0x7c,0x22,0x22,0x22,0x22,0x3c,0x28,0x24,0x22,0x62,0x00,0x00,0x00,0x00,

        8, // 0x53 'S'
        0x00,0x00,0x3c,0x42,0x42,0x40,0x30,0x0c,0x02,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x54 'T'
        0x00,0x00,0x7f,0x49,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x1c,0x00,0x00,0x00,0x00,

        8, // 0x55 'U'
        0x00,0x00,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x56 'V'
        0x00,0x00,0x41,0x41,0x41,0x41,0x22,0x22,0x14,0x14,0x08,0x08,0x00,0x00,0x00,0x00,

        8, // 0x57 'W'
        0x00,0x00,0x41,0x41,0x41,0x41,0x41,0x49,0x49,0x55,0x63,0x41,0x00,0x00,0x00,0x00,

        8, // 0x58 'X'
        0x00,0x00,0x42,0x42,0x42,0x24,0x18,0x18,0x24,0x42,0x42,0x42,0x00,0x00,0x00,0x00,

        8, // 0x59 'Y'
        0x00,0x00,0x22,0x22,0x22,0x22,0x14,0x08,0x08,0x08,0x08,0x1c,0x00,0x00,0x00,0x00,

        8, // 0x5a 'Z'
        0x00,0x00,0x7e,0x42,0x02,0x04,0x08,0x10,0x20,0x40,0x42,0x7e,0x00,0x00,0x00,0x00,

        8, // 0x5b '['
        0x00,0x1e,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x1e,0x00,0x00,0x00,

        8, // 0x5c '\'
        0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x02,0x02,0x00,0x00,0x00,

        8, // 0x5d ']'
        0x00,0x3c,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x3c,0x00,0x00,0x00,

        8, // 0x5e '^'
        0x00,0x00,0x08,0x14,0x22,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x00,

        8, // 0x60 '`'
        0x00,0x00,0x08,0x08,0x08,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x38,0x44,0x04,0x3c,0x44,0x44,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x62 'b'
        0x00,0x00,0x60,0x20,0x20,0x38,0x24,0x22,0x22,0x22,0x22,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x3c,0x42,0x40,0x40,0x40,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x64 'd'
        0x00,0x00,0x0c,0x04,0x04,0x1c,0x24,0x44,0x44,0x44,0x44,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x3c,0x42,0x42,0x7e,0x40,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x66 'f'
        0x00,0x00,0x0c,0x12,0x10,0x10,0x38,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,0x00,

        8, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x3e,0x44,0x44,0x44,0x44,0x44,0x3c,0x04,0x44,0x38,0x00,

        8, // 0x68 'h'
        0x00,0x00,0x60,0x20,0x20,0x2c,0x32,0x22,0x22,0x22,0x22,0x62,0x00,0x00,0x00,0x00,

        8, // 0x69 'i'
        0x00,0x00,0x08,0x08,0x00,0x18,0x08,0x08,0x08,0x08,0x08,0x1c,0x00,0x00,0x00,0x00,

        8, // 0x6a 'j'
        0x00,0x00,0x04,0x04,0x00,0x0c,0x04,0x04,0x04,0x04,0x04,0x44,0x44,0x38,0x00,0x00,

        8, // 0x6b 'k'
        0x00,0x00,0x60,0x20,0x20,0x22,0x24,0x28,0x38,0x24,0x22,0x62,0x00,0x00,0x00,0x00,

        8, // 0x6c 'l'
        0x00,0x00,0x18,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x1c,0x00,0x00,0x00,0x00,

        8, // 0x6d 'm'
        0x00,0x00,0x00,0x00,0x00,0x76,0x49,0x49,0x49,0x49,0x41,0x41,0x00,0x00,0x00,0x00,

        8, // 0x6e 'n'
        0x00,0x00,0x00,0x00,0x00,0x5c,0x22,0x22,0x22,0x22,0x22,0x22,0x00,0x00,0x00,0x00,

        8, // 0x6f 'o'
        0x00,0x00,0x00,0x00,0x00,0x3c,0x42,0x42,0x42,0x42,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x7c,0x22,0x22,0x22,0x22,0x22,0x3c,0x20,0x20,0x70,0x00,

        8, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x3e,0x44,0x44,0x44,0x44,0x44,0x3c,0x04,0x04,0x0e,0x00,

        8, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x7c,0x22,0x22,0x20,0x20,0x20,0x70,0x00,0x00,0x00,0x00,

        8, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x3c,0x42,0x40,0x3c,0x02,0x42,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x74 't'
        0x00,0x00,0x10,0x10,0x10,0x7c,0x10,0x10,0x10,0x10,0x12,0x0c,0x00,0x00,0x00,0x00,

        8, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x41,0x41,0x41,0x41,0x22,0x14,0x08,0x00,0x00,0x00,0x00,

        8, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x41,0x41,0x41,0x49,0x49,0x55,0x22,0x00,0x00,0x00,0x00,

        8, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x42,0x42,0x24,0x18,0x24,0x42,0x42,0x00,0x00,0x00,0x00,

        8, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x42,0x42,0x42,0x42,0x42,0x42,0x3e,0x02,0x04,0x78,0x00,

        8, // 0x7a 'z'
        0x00,0x00,0x00,0x00,0x00,0x7e,0x44,0x08,0x10,0x20,0x42,0x7e,0x00,0x00,0x00,0x00,

        8, // 0x7b '{'
        0x00,0x06,0x08,0x08,0x08,0x08,0x08,0x30,0x08,0x08,0x08,0x08,0x08,0x06,0x00,0x00,

        8, // 0x7c '|'
        0x00,0x00,0x08,0x08,0x08,0x08,0x08,0x00,0x08,0x08,0x08,0x08,0x08,0x00,0x00,0x00,

        8, // 0x7d '}'
        0x00,0x30,0x08,0x08,0x08,0x08,0x08,0x06,0x08,0x08,0x08,0x08,0x08,0x30,0x00,0x00,

        8, // 0x7e '~'
        0x00,0x00,0x39,0x4e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x7f ''
        0x00,0x00,0x00,0x00,0x00,0x08,0x14,0x22,0x41,0x41,0x7f,0x00,0x00,0x00,0x00,0x00,
        0
    };

    const int8u gse8x16_bold[] = 
    {
        16, 0, 32, 128-32,

        0x00,0x00,0x11,0x00,0x22,0x00,0x33,0x00,0x44,0x00,0x55,0x00,0x66,0x00,0x77,0x00,0x88,0x00,
        0x99,0x00,0xaa,0x00,0xbb,0x00,0xcc,0x00,0xdd,0x00,0xee,0x00,0xff,0x00,0x10,0x01,0x21,0x01,
        0x32,0x01,0x43,0x01,0x54,0x01,0x65,0x01,0x76,0x01,0x87,0x01,0x98,0x01,0xa9,0x01,0xba,0x01,
        0xcb,0x01,0xdc,0x01,0xed,0x01,0xfe,0x01,0x0f,0x02,0x20,0x02,0x31,0x02,0x42,0x02,0x53,0x02,
        0x64,0x02,0x75,0x02,0x86,0x02,0x97,0x02,0xa8,0x02,0xb9,0x02,0xca,0x02,0xdb,0x02,0xec,0x02,
        0xfd,0x02,0x0e,0x03,0x1f,0x03,0x30,0x03,0x41,0x03,0x52,0x03,0x63,0x03,0x74,0x03,0x85,0x03,
        0x96,0x03,0xa7,0x03,0xb8,0x03,0xc9,0x03,0xda,0x03,0xeb,0x03,0xfc,0x03,0x0d,0x04,0x1e,0x04,
        0x2f,0x04,0x40,0x04,0x51,0x04,0x62,0x04,0x73,0x04,0x84,0x04,0x95,0x04,0xa6,0x04,0xb7,0x04,
        0xc8,0x04,0xd9,0x04,0xea,0x04,0xfb,0x04,0x0c,0x05,0x1d,0x05,0x2e,0x05,0x3f,0x05,0x50,0x05,
        0x61,0x05,0x72,0x05,0x83,0x05,0x94,0x05,0xa5,0x05,0xb6,0x05,0xc7,0x05,0xd8,0x05,0xe9,0x05,
        0xfa,0x05,0x0b,0x06,0x1c,0x06,0x2d,0x06,0x3e,0x06,0x4f,0x06,

        8, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x21 '!'
        0x00,0x00,0x18,0x3c,0x3c,0x3c,0x3c,0x18,0x18,0x00,0x18,0x18,0x00,0x00,0x00,0x00,

        8, // 0x22 '"'
        0x00,0x66,0x66,0x66,0x66,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x23 '#'
        0x00,0x00,0x66,0x66,0x66,0xff,0x66,0x66,0xff,0x66,0x66,0x66,0x00,0x00,0x00,0x00,

        8, // 0x24 '$'
        0x00,0x08,0x08,0x3e,0x6b,0x6b,0x68,0x3e,0x0b,0x6b,0x6b,0x3e,0x08,0x08,0x00,0x00,

        8, // 0x25 '%'
        0x00,0x00,0x66,0xbe,0xcc,0x0c,0x18,0x18,0x30,0x33,0x65,0x66,0x00,0x00,0x00,0x00,

        8, // 0x26 '&'
        0x00,0x00,0x1c,0x36,0x36,0x36,0x1c,0x3b,0x6e,0x66,0x66,0x3b,0x00,0x00,0x00,0x00,

        8, // 0x27 '''
        0x00,0x00,0x18,0x18,0x18,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x28 '('
        0x00,0x06,0x0c,0x18,0x18,0x30,0x30,0x30,0x30,0x18,0x18,0x0c,0x06,0x00,0x00,0x00,

        8, // 0x29 ')'
        0x00,0x30,0x18,0x0c,0x0c,0x06,0x06,0x06,0x06,0x0c,0x0c,0x18,0x30,0x00,0x00,0x00,

        8, // 0x2a '*'
        0x00,0x00,0x00,0x00,0x66,0x24,0x18,0xff,0x18,0x24,0x66,0x00,0x00,0x00,0x00,0x00,

        8, // 0x2b '+'
        0x00,0x00,0x00,0x00,0x18,0x18,0x18,0xff,0x18,0x18,0x18,0x00,0x00,0x00,0x00,0x00,

        8, // 0x2c ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x18,0x30,0x20,0x00,

        8, // 0x2d '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x2e '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,

        8, // 0x2f '/'
        0x00,0x03,0x03,0x06,0x06,0x0c,0x0c,0x18,0x18,0x30,0x30,0x60,0x60,0x00,0x00,0x00,

        8, // 0x30 '0'
        0x00,0x00,0x3e,0x63,0x63,0x67,0x6b,0x73,0x63,0x63,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x31 '1'
        0x00,0x00,0x0c,0x0c,0x1c,0x3c,0x0c,0x0c,0x0c,0x0c,0x0c,0x3f,0x00,0x00,0x00,0x00,

        8, // 0x32 '2'
        0x00,0x00,0x3e,0x63,0x63,0x03,0x06,0x0c,0x18,0x30,0x61,0x7f,0x00,0x00,0x00,0x00,

        8, // 0x33 '3'
        0x00,0x00,0x7f,0x43,0x06,0x0c,0x1e,0x03,0x03,0x03,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x34 '4'
        0x00,0x00,0x06,0x0c,0x18,0x32,0x66,0x66,0x7f,0x06,0x06,0x0f,0x00,0x00,0x00,0x00,

        8, // 0x35 '5'
        0x00,0x00,0x7f,0x61,0x60,0x60,0x7e,0x03,0x03,0x03,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x36 '6'
        0x00,0x00,0x1e,0x30,0x60,0x60,0x7e,0x63,0x63,0x63,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x37 '7'
        0x00,0x00,0x7f,0x63,0x63,0x03,0x06,0x0c,0x18,0x18,0x18,0x18,0x00,0x00,0x00,0x00,

        8, // 0x38 '8'
        0x00,0x00,0x3e,0x63,0x63,0x63,0x3e,0x63,0x63,0x63,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x39 '9'
        0x00,0x00,0x3e,0x63,0x63,0x63,0x63,0x3f,0x03,0x03,0x06,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x3a ':'
        0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,0x00,

        8, // 0x3b ';'
        0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x60,0x40,0x00,

        8, // 0x3c '<'
        0x00,0x00,0x00,0x06,0x0c,0x18,0x30,0x60,0x30,0x18,0x0c,0x06,0x00,0x00,0x00,0x00,

        8, // 0x3d '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x3e '>'
        0x00,0x00,0x00,0x30,0x18,0x0c,0x06,0x03,0x06,0x0c,0x18,0x30,0x00,0x00,0x00,0x00,

        8, // 0x3f '?'
        0x00,0x00,0x3e,0x63,0x63,0x63,0x06,0x0c,0x0c,0x00,0x0c,0x0c,0x00,0x00,0x00,0x00,

        8, // 0x40 '@'
        0x00,0x00,0x7c,0x86,0x03,0x73,0xdb,0xdb,0xdb,0xdb,0xdb,0x6e,0x00,0x00,0x00,0x00,

        8, // 0x41 'A'
        0x00,0x00,0x08,0x1c,0x36,0x63,0x63,0x63,0x7f,0x63,0x63,0x63,0x00,0x00,0x00,0x00,

        8, // 0x42 'B'
        0x00,0x00,0x7e,0x33,0x33,0x33,0x3e,0x33,0x33,0x33,0x33,0x7e,0x00,0x00,0x00,0x00,

        8, // 0x43 'C'
        0x00,0x00,0x1e,0x33,0x61,0x60,0x60,0x60,0x60,0x61,0x33,0x1e,0x00,0x00,0x00,0x00,

        8, // 0x44 'D'
        0x00,0x00,0x7c,0x36,0x33,0x33,0x33,0x33,0x33,0x33,0x36,0x7c,0x00,0x00,0x00,0x00,

        8, // 0x45 'E'
        0x00,0x00,0x7f,0x33,0x31,0x34,0x3c,0x34,0x30,0x31,0x33,0x7f,0x00,0x00,0x00,0x00,

        8, // 0x46 'F'
        0x00,0x00,0x7f,0x33,0x31,0x34,0x3c,0x34,0x30,0x30,0x30,0x78,0x00,0x00,0x00,0x00,

        8, // 0x47 'G'
        0x00,0x00,0x1f,0x33,0x61,0x60,0x60,0x6f,0x63,0x63,0x33,0x1e,0x00,0x00,0x00,0x00,

        8, // 0x48 'H'
        0x00,0x00,0x63,0x63,0x63,0x63,0x7f,0x63,0x63,0x63,0x63,0x63,0x00,0x00,0x00,0x00,

        8, // 0x49 'I'
        0x00,0x00,0x1e,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x1e,0x00,0x00,0x00,0x00,

        8, // 0x4a 'J'
        0x00,0x00,0x0f,0x06,0x06,0x06,0x06,0x06,0x06,0x66,0x66,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x4b 'K'
        0x00,0x00,0x73,0x33,0x36,0x36,0x3c,0x36,0x36,0x33,0x33,0x73,0x00,0x00,0x00,0x00,

        8, // 0x4c 'L'
        0x00,0x00,0x78,0x30,0x30,0x30,0x30,0x30,0x30,0x31,0x33,0x7f,0x00,0x00,0x00,0x00,

        8, // 0x4d 'M'
        0x00,0x00,0x63,0x63,0x77,0x77,0x7f,0x6b,0x6b,0x63,0x63,0x63,0x00,0x00,0x00,0x00,

        8, // 0x4e 'N'
        0x00,0x00,0x63,0x63,0x73,0x7b,0x6f,0x67,0x63,0x63,0x63,0x63,0x00,0x00,0x00,0x00,

        8, // 0x4f 'O'
        0x00,0x00,0x1c,0x36,0x63,0x63,0x63,0x63,0x63,0x63,0x36,0x1c,0x00,0x00,0x00,0x00,

        8, // 0x50 'P'
        0x00,0x00,0x7e,0x33,0x33,0x33,0x33,0x3e,0x30,0x30,0x30,0x78,0x00,0x00,0x00,0x00,

        8, // 0x51 'Q'
        0x00,0x00,0x1c,0x36,0x63,0x63,0x63,0x63,0x63,0x6f,0x36,0x1e,0x03,0x00,0x00,0x00,

        8, // 0x52 'R'
        0x00,0x00,0x7e,0x33,0x33,0x33,0x33,0x3e,0x36,0x33,0x33,0x73,0x00,0x00,0x00,0x00,

        8, // 0x53 'S'
        0x00,0x00,0x3e,0x63,0x63,0x30,0x18,0x0c,0x06,0x63,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x54 'T'
        0x00,0x00,0x3f,0x3f,0x2d,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x1e,0x00,0x00,0x00,0x00,

        8, // 0x55 'U'
        0x00,0x00,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x56 'V'
        0x00,0x00,0x63,0x63,0x63,0x63,0x63,0x63,0x63,0x36,0x1c,0x08,0x00,0x00,0x00,0x00,

        8, // 0x57 'W'
        0x00,0x00,0x63,0x63,0x63,0x6b,0x6b,0x7f,0x77,0x77,0x63,0x63,0x00,0x00,0x00,0x00,

        8, // 0x58 'X'
        0x00,0x00,0x63,0x63,0x63,0x36,0x1c,0x1c,0x36,0x63,0x63,0x63,0x00,0x00,0x00,0x00,

        8, // 0x59 'Y'
        0x00,0x00,0x33,0x33,0x33,0x33,0x1e,0x0c,0x0c,0x0c,0x0c,0x1e,0x00,0x00,0x00,0x00,

        8, // 0x5a 'Z'
        0x00,0x00,0x7f,0x63,0x43,0x06,0x0c,0x18,0x30,0x61,0x63,0x7f,0x00,0x00,0x00,0x00,

        8, // 0x5b '['
        0x00,0x1f,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x1f,0x00,0x00,0x00,

        8, // 0x5c '\'
        0x00,0x60,0x60,0x30,0x30,0x18,0x18,0x0c,0x0c,0x06,0x06,0x03,0x03,0x00,0x00,0x00,

        8, // 0x5d ']'
        0x00,0x7c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x7c,0x00,0x00,0x00,

        8, // 0x5e '^'
        0x00,0x00,0x08,0x1c,0x36,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x5f '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x00,

        8, // 0x60 '`'
        0x00,0x00,0x18,0x18,0x18,0x0c,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x3c,0x66,0x06,0x3e,0x66,0x66,0x3b,0x00,0x00,0x00,0x00,

        8, // 0x62 'b'
        0x00,0x00,0x70,0x30,0x30,0x3c,0x36,0x33,0x33,0x33,0x33,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x3e,0x63,0x63,0x60,0x60,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x64 'd'
        0x00,0x00,0x0e,0x06,0x06,0x1e,0x36,0x66,0x66,0x66,0x66,0x3b,0x00,0x00,0x00,0x00,

        8, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x3e,0x63,0x63,0x7f,0x60,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x66 'f'
        0x00,0x00,0x0e,0x1b,0x1b,0x18,0x3c,0x18,0x18,0x18,0x18,0x3c,0x00,0x00,0x00,0x00,

        8, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x3b,0x66,0x66,0x66,0x66,0x66,0x3e,0x06,0x66,0x3c,0x00,

        8, // 0x68 'h'
        0x00,0x00,0x70,0x30,0x30,0x36,0x3b,0x33,0x33,0x33,0x33,0x73,0x00,0x00,0x00,0x00,

        8, // 0x69 'i'
        0x00,0x00,0x0c,0x0c,0x00,0x1c,0x0c,0x0c,0x0c,0x0c,0x0c,0x1e,0x00,0x00,0x00,0x00,

        8, // 0x6a 'j'
        0x00,0x00,0x06,0x06,0x00,0x0e,0x06,0x06,0x06,0x06,0x06,0x66,0x66,0x3c,0x00,0x00,

        8, // 0x6b 'k'
        0x00,0x00,0x70,0x30,0x30,0x33,0x33,0x36,0x3c,0x36,0x33,0x73,0x00,0x00,0x00,0x00,

        8, // 0x6c 'l'
        0x00,0x00,0x1c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x1e,0x00,0x00,0x00,0x00,

        8, // 0x6d 'm'
        0x00,0x00,0x00,0x00,0x00,0x76,0x7f,0x6b,0x6b,0x6b,0x63,0x63,0x00,0x00,0x00,0x00,

        8, // 0x6e 'n'
        0x00,0x00,0x00,0x00,0x00,0x6e,0x33,0x33,0x33,0x33,0x33,0x33,0x00,0x00,0x00,0x00,

        8, // 0x6f 'o'
        0x00,0x00,0x00,0x00,0x00,0x3e,0x63,0x63,0x63,0x63,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x6e,0x33,0x33,0x33,0x33,0x33,0x3e,0x30,0x30,0x78,0x00,

        8, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x3b,0x66,0x66,0x66,0x66,0x66,0x3e,0x06,0x06,0x0f,0x00,

        8, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x6e,0x3b,0x33,0x30,0x30,0x30,0x78,0x00,0x00,0x00,0x00,

        8, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x3e,0x63,0x60,0x3e,0x03,0x63,0x3e,0x00,0x00,0x00,0x00,

        8, // 0x74 't'
        0x00,0x00,0x08,0x18,0x18,0x7e,0x18,0x18,0x18,0x18,0x1b,0x0e,0x00,0x00,0x00,0x00,

        8, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x66,0x66,0x66,0x66,0x3b,0x00,0x00,0x00,0x00,

        8, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x63,0x63,0x63,0x63,0x36,0x1c,0x08,0x00,0x00,0x00,0x00,

        8, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x63,0x63,0x6b,0x6b,0x7f,0x36,0x36,0x00,0x00,0x00,0x00,

        8, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x63,0x63,0x36,0x1c,0x36,0x63,0x63,0x00,0x00,0x00,0x00,

        8, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x63,0x63,0x63,0x63,0x63,0x63,0x3f,0x03,0x06,0x7c,0x00,

        8, // 0x7a 'z'
        0x00,0x00,0x00,0x00,0x00,0x7f,0x63,0x06,0x0c,0x18,0x31,0x7f,0x00,0x00,0x00,0x00,

        8, // 0x7b '{'
        0x00,0x03,0x04,0x0c,0x0c,0x0c,0x08,0x30,0x08,0x0c,0x0c,0x0c,0x04,0x03,0x00,0x00,

        8, // 0x7c '|'
        0x00,0x00,0x0c,0x0c,0x0c,0x0c,0x0c,0x00,0x0c,0x0c,0x0c,0x0c,0x0c,0x00,0x00,0x00,

        8, // 0x7d '}'
        0x00,0x60,0x10,0x18,0x18,0x18,0x08,0x06,0x08,0x18,0x18,0x18,0x10,0x60,0x00,0x00,

        8, // 0x7e '~'
        0x00,0x00,0x3b,0x6e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x7f ''
        0x00,0x00,0x00,0x00,0x00,0x08,0x1c,0x36,0x63,0x63,0x7f,0x00,0x00,0x00,0x00,0x00,
        0
    };

    const int8u mcs11_prop[] = 
    {
        11, 2, 32, 128-32,
        0x00,0x00,0x0C,0x00,0x18,0x00,0x24,0x00,0x30,0x00,0x3C,0x00,0x48,0x00,0x54,0x00,0x60,0x00,
        0x6C,0x00,0x78,0x00,0x84,0x00,0x90,0x00,0x9C,0x00,0xA8,0x00,0xB4,0x00,0xC0,0x00,0xCC,0x00,
        0xD8,0x00,0xE4,0x00,0xF0,0x00,0xFC,0x00,0x08,0x01,0x14,0x01,0x20,0x01,0x2C,0x01,0x38,0x01,
        0x44,0x01,0x50,0x01,0x5C,0x01,0x68,0x01,0x74,0x01,0x80,0x01,0x8C,0x01,0x98,0x01,0xA4,0x01,
        0xB0,0x01,0xBC,0x01,0xC8,0x01,0xD4,0x01,0xE0,0x01,0xEC,0x01,0xF8,0x01,0x04,0x02,0x10,0x02,
        0x1C,0x02,0x28,0x02,0x34,0x02,0x40,0x02,0x4C,0x02,0x58,0x02,0x64,0x02,0x70,0x02,0x7C,0x02,
        0x88,0x02,0x94,0x02,0xA0,0x02,0xAC,0x02,0xB8,0x02,0xC4,0x02,0xD0,0x02,0xDC,0x02,0xE8,0x02,
        0xF4,0x02,0x00,0x03,0x0C,0x03,0x18,0x03,0x24,0x03,0x30,0x03,0x3C,0x03,0x48,0x03,0x54,0x03,
        0x60,0x03,0x6C,0x03,0x78,0x03,0x84,0x03,0x90,0x03,0x9C,0x03,0xA8,0x03,0xB4,0x03,0xC0,0x03,
        0xCC,0x03,0xD8,0x03,0xE4,0x03,0xF0,0x03,0xFC,0x03,0x08,0x04,0x14,0x04,0x20,0x04,0x2C,0x04,
        0x38,0x04,0x44,0x04,0x50,0x04,0x5C,0x04,0x68,0x04,0x74,0x04,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,

        4, // 0x22 '"'
        0x50,0x50,0xA0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x23 '#'
        0x00,0x28,0x28,0x7C,0x28,0x28,0x28,0x7C,0x28,0x28,0x00,

        6, // 0x24 '$'
        0x10,0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x38,0x10,0x10,

        6, // 0x25 '%'
        0x00,0x00,0x68,0xA8,0xD0,0x10,0x20,0x2C,0x54,0x58,0x00,

        6, // 0x26 '&'
        0x00,0x20,0x50,0x50,0x50,0x20,0x54,0x54,0x48,0x34,0x00,

        3, // 0x27 '''
        0x40,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x28 '('
        0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,

        5, // 0x29 ')'
        0x40,0x20,0x20,0x10,0x10,0x10,0x10,0x10,0x20,0x20,0x40,

        6, // 0x2A '*'
        0x00,0x00,0x28,0x7C,0x38,0x7C,0x28,0x00,0x00,0x00,0x00,

        6, // 0x2B '+'
        0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,

        4, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0xC0,

        6, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,

        7, // 0x2F '/'
        0x00,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,

        6, // 0x30 '0'
        0x00,0x38,0x44,0x44,0x54,0x54,0x54,0x44,0x44,0x38,0x00,

        4, // 0x31 '1'
        0x00,0x20,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,

        6, // 0x32 '2'
        0x00,0x38,0x44,0x44,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,

        6, // 0x33 '3'
        0x00,0x38,0x44,0x04,0x04,0x38,0x04,0x04,0x44,0x38,0x00,

        6, // 0x34 '4'
        0x00,0x08,0x18,0x18,0x28,0x28,0x48,0x7C,0x08,0x08,0x00,

        6, // 0x35 '5'
        0x00,0x7C,0x40,0x40,0x78,0x44,0x04,0x04,0x44,0x38,0x00,

        6, // 0x36 '6'
        0x00,0x38,0x44,0x40,0x40,0x78,0x44,0x44,0x44,0x38,0x00,

        6, // 0x37 '7'
        0x00,0x7C,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x20,0x00,

        6, // 0x38 '8'
        0x00,0x38,0x44,0x44,0x44,0x38,0x44,0x44,0x44,0x38,0x00,

        6, // 0x39 '9'
        0x00,0x38,0x44,0x44,0x44,0x3C,0x04,0x04,0x44,0x38,0x00,

        4, // 0x3A ':'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0x00,

        4, // 0x3B ';'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0xC0,

        6, // 0x3C '<'
        0x00,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x00,

        6, // 0x3D '='
        0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x7C,0x00,0x00,0x00,

        6, // 0x3E '>'
        0x00,0x40,0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x40,0x00,

        6, // 0x3F '?'
        0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x10,0x00,0x10,0x00,

        6, // 0x40 '@'
        0x00,0x38,0x44,0x44,0x5C,0x54,0x54,0x4C,0x40,0x38,0x00,

        6, // 0x41 'A'
        0x00,0x38,0x44,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x00,

        6, // 0x42 'B'
        0x00,0x78,0x44,0x44,0x44,0x78,0x44,0x44,0x44,0x78,0x00,

        6, // 0x43 'C'
        0x00,0x38,0x44,0x40,0x40,0x40,0x40,0x40,0x44,0x38,0x00,

        6, // 0x44 'D'
        0x00,0x70,0x48,0x44,0x44,0x44,0x44,0x44,0x48,0x70,0x00,

        6, // 0x45 'E'
        0x00,0x7C,0x40,0x40,0x40,0x78,0x40,0x40,0x40,0x7C,0x00,

        6, // 0x46 'F'
        0x00,0x7C,0x40,0x40,0x40,0x78,0x40,0x40,0x40,0x40,0x00,

        6, // 0x47 'G'
        0x00,0x38,0x44,0x40,0x40,0x5C,0x44,0x44,0x4C,0x34,0x00,

        6, // 0x48 'H'
        0x00,0x44,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,

        4, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,

        6, // 0x4A 'J'
        0x00,0x1C,0x08,0x08,0x08,0x08,0x08,0x08,0x48,0x30,0x00,

        6, // 0x4B 'K'
        0x00,0x44,0x48,0x50,0x60,0x60,0x50,0x48,0x44,0x44,0x00,

        6, // 0x4C 'L'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,

        8, // 0x4D 'M'
        0x00,0x41,0x63,0x55,0x49,0x49,0x41,0x41,0x41,0x41,0x00,

        7, // 0x4E 'N'
        0x00,0x42,0x42,0x62,0x52,0x4A,0x46,0x42,0x42,0x42,0x00,

        6, // 0x4F 'O'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,

        6, // 0x50 'P'
        0x00,0x78,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x40,0x00,

        6, // 0x51 'Q'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x54,0x48,0x34,0x00,

        6, // 0x52 'R'
        0x00,0x78,0x44,0x44,0x44,0x78,0x44,0x44,0x44,0x44,0x00,

        6, // 0x53 'S'
        0x00,0x38,0x44,0x40,0x40,0x38,0x04,0x04,0x44,0x38,0x00,

        6, // 0x54 'T'
        0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,

        6, // 0x55 'U'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,

        6, // 0x56 'V'
        0x00,0x44,0x44,0x44,0x44,0x28,0x28,0x28,0x10,0x10,0x00,

        8, // 0x57 'W'
        0x00,0x41,0x41,0x41,0x41,0x49,0x49,0x49,0x55,0x22,0x00,

        6, // 0x58 'X'
        0x00,0x44,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x00,

        6, // 0x59 'Y'
        0x00,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x10,0x10,0x00,

        6, // 0x5A 'Z'
        0x00,0x7C,0x04,0x04,0x08,0x10,0x20,0x40,0x40,0x7C,0x00,

        5, // 0x5B '['
        0x30,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x30,

        7, // 0x5C '\'
        0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x00,

        4, // 0x5D ']'
        0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x60,

        6, // 0x5E '^'
        0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,

        4, // 0x60 '`'
        0x00,0x40,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x61 'a'
        0x00,0x00,0x00,0x38,0x04,0x3C,0x44,0x44,0x44,0x3C,0x00,

        6, // 0x62 'b'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x40,0x44,0x38,0x00,

        6, // 0x64 'd'
        0x00,0x04,0x04,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x00,

        6, // 0x65 'e'
        0x00,0x00,0x00,0x38,0x44,0x44,0x7C,0x40,0x44,0x38,0x00,

        4, // 0x66 'f'
        0x00,0x10,0x20,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x00,

        6, // 0x67 'g'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x3C,0x04,0x44,0x38,

        6, // 0x68 'h'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x44,0x00,

        2, // 0x69 'i'
        0x00,0x40,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,

        3, // 0x6A 'j'
        0x00,0x20,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0xA0,0x40,

        5, // 0x6B 'k'
        0x00,0x40,0x40,0x48,0x50,0x60,0x60,0x50,0x48,0x48,0x00,

        2, // 0x6C 'l'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,

        8, // 0x6D 'm'
        0x00,0x00,0x00,0x76,0x49,0x49,0x49,0x49,0x41,0x41,0x00,

        6, // 0x6E 'n'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x44,0x44,0x00,

        6, // 0x6F 'o'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x38,0x00,

        6, // 0x70 'p'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,

        6, // 0x71 'q'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x3C,0x04,0x04,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x58,0x24,0x20,0x20,0x20,0x20,0x20,0x00,

        6, // 0x73 's'
        0x00,0x00,0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x38,0x00,

        5, // 0x74 't'
        0x00,0x20,0x20,0x70,0x20,0x20,0x20,0x20,0x28,0x10,0x00,

        6, // 0x75 'u'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x4C,0x34,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x44,0x44,0x44,0x28,0x28,0x10,0x10,0x00,

        8, // 0x77 'w'
        0x00,0x00,0x00,0x41,0x41,0x41,0x41,0x49,0x49,0x36,0x00,

        6, // 0x78 'x'
        0x00,0x00,0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00,

        6, // 0x79 'y'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x3C,0x04,0x08,0x70,

        6, // 0x7A 'z'
        0x00,0x00,0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,

        5, // 0x7B '{'
        0x18,0x20,0x20,0x20,0x20,0xC0,0x20,0x20,0x20,0x20,0x18,

        3, // 0x7C '|'
        0x00,0x40,0x40,0x40,0x40,0x00,0x40,0x40,0x40,0x40,0x00,

        5, // 0x7D '}'
        0xC0,0x20,0x20,0x20,0x20,0x18,0x20,0x20,0x20,0x20,0xC0,

        6, // 0x7E '~'
        0x00,0x24,0x54,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x7F ''
        0x00,0x10,0x38,0x6C,0x44,0x44,0x7C,0x00,0x00,0x00,0x00,

        0
    };

    const int8u mcs11_prop_condensed[] = 
    {
        11, 2, 32, 128-32,
        0x00,0x00,0x0C,0x00,0x18,0x00,0x24,0x00,0x30,0x00,0x3C,0x00,0x48,0x00,0x54,0x00,0x60,0x00,
        0x6C,0x00,0x78,0x00,0x84,0x00,0x90,0x00,0x9C,0x00,0xA8,0x00,0xB4,0x00,0xC0,0x00,0xCC,0x00,
        0xD8,0x00,0xE4,0x00,0xF0,0x00,0xFC,0x00,0x08,0x01,0x14,0x01,0x20,0x01,0x2C,0x01,0x38,0x01,
        0x44,0x01,0x50,0x01,0x5C,0x01,0x68,0x01,0x74,0x01,0x80,0x01,0x8C,0x01,0x98,0x01,0xA4,0x01,
        0xB0,0x01,0xBC,0x01,0xC8,0x01,0xD4,0x01,0xE0,0x01,0xEC,0x01,0xF8,0x01,0x04,0x02,0x10,0x02,
        0x1C,0x02,0x28,0x02,0x34,0x02,0x40,0x02,0x4C,0x02,0x58,0x02,0x64,0x02,0x70,0x02,0x7C,0x02,
        0x88,0x02,0x94,0x02,0xA0,0x02,0xAC,0x02,0xB8,0x02,0xC4,0x02,0xD0,0x02,0xDC,0x02,0xE8,0x02,
        0xF4,0x02,0x00,0x03,0x0C,0x03,0x18,0x03,0x24,0x03,0x30,0x03,0x3C,0x03,0x48,0x03,0x54,0x03,
        0x60,0x03,0x6C,0x03,0x78,0x03,0x84,0x03,0x90,0x03,0x9C,0x03,0xA8,0x03,0xB4,0x03,0xC0,0x03,
        0xCC,0x03,0xD8,0x03,0xE4,0x03,0xF0,0x03,0xFC,0x03,0x08,0x04,0x14,0x04,0x20,0x04,0x2C,0x04,
        0x38,0x04,0x44,0x04,0x50,0x04,0x5C,0x04,0x68,0x04,0x74,0x04,

        3, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        3, // 0x21 '!'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x40,0x00,

        4, // 0x22 '"'
        0x50,0x50,0xA0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x23 '#'
        0x00,0x50,0x50,0xF8,0x50,0x50,0x50,0xF8,0x50,0x50,0x00,

        5, // 0x24 '$'
        0x00,0x40,0x60,0x90,0x80,0x60,0x10,0x90,0x60,0x20,0x00,

        5, // 0x25 '%'
        0x00,0x00,0x90,0x90,0x20,0x20,0x40,0x40,0x90,0x90,0x00,

        5, // 0x26 '&'
        0x00,0x40,0xA0,0xA0,0xA0,0x40,0xA8,0x90,0x90,0x68,0x00,

        5, // 0x27 '''
        0x00,0x00,0x20,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,

        4, // 0x29 ')'
        0x80,0x40,0x40,0x20,0x20,0x20,0x20,0x20,0x40,0x40,0x80,

        5, // 0x2A '*'
        0x00,0x00,0x90,0x60,0xF0,0x60,0x90,0x00,0x00,0x00,0x00,

        5, // 0x2B '+'
        0x00,0x00,0x00,0x20,0x20,0xF8,0x20,0x20,0x00,0x00,0x00,

        4, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0xC0,

        5, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0xF0,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xC0,0x00,

        6, // 0x2F '/'
        0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x80,0x80,0x00,

        5, // 0x30 '0'
        0x00,0x70,0x90,0x90,0x90,0x90,0x90,0x90,0x90,0xE0,0x00,

        3, // 0x31 '1'
        0x00,0x40,0xC0,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,

        5, // 0x32 '2'
        0x00,0x60,0x90,0x90,0x10,0x10,0x20,0x40,0x80,0xF0,0x00,

        5, // 0x33 '3'
        0x00,0x60,0x90,0x10,0x10,0x60,0x10,0x10,0x90,0x60,0x00,

        5, // 0x34 '4'
        0x00,0x10,0x30,0x30,0x50,0x50,0x90,0xF0,0x10,0x10,0x00,

        5, // 0x35 '5'
        0x00,0xF0,0x80,0x80,0xE0,0x90,0x10,0x10,0x90,0x60,0x00,

        5, // 0x36 '6'
        0x00,0x60,0x90,0x80,0x80,0xE0,0x90,0x90,0x90,0x60,0x00,

        5, // 0x37 '7'
        0x00,0xF0,0x10,0x10,0x10,0x20,0x20,0x40,0x40,0x40,0x00,

        5, // 0x38 '8'
        0x00,0x60,0x90,0x90,0x90,0x60,0x90,0x90,0x90,0x60,0x00,

        5, // 0x39 '9'
        0x00,0x60,0x90,0x90,0x90,0x70,0x10,0x10,0x90,0x60,0x00,

        4, // 0x3A ':'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0x00,

        4, // 0x3B ';'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0xC0,

        6, // 0x3C '<'
        0x00,0x08,0x10,0x20,0x40,0x80,0x40,0x20,0x10,0x08,0x00,

        5, // 0x3D '='
        0x00,0x00,0x00,0x00,0xF0,0x00,0x00,0xF0,0x00,0x00,0x00,

        6, // 0x3E '>'
        0x00,0x80,0x40,0x20,0x10,0x08,0x10,0x20,0x40,0x80,0x00,

        5, // 0x3F '?'
        0x00,0x60,0x90,0x10,0x10,0x20,0x40,0x00,0x40,0x00,0x00,

        5, // 0x40 '@'
        0x00,0x60,0x90,0x90,0xB0,0xB0,0xB0,0x80,0x80,0x70,0x00,

        5, // 0x41 'A'
        0x00,0x60,0x90,0x90,0x90,0xF0,0x90,0x90,0x90,0x90,0x00,

        5, // 0x42 'B'
        0x00,0xE0,0x90,0x90,0x90,0xE0,0x90,0x90,0x90,0xE0,0x00,

        5, // 0x43 'C'
        0x00,0x60,0x90,0x80,0x80,0x80,0x80,0x80,0x90,0x60,0x00,

        5, // 0x44 'D'
        0x00,0xE0,0x90,0x90,0x90,0x90,0x90,0x90,0x90,0xE0,0x00,

        5, // 0x45 'E'
        0x00,0xF0,0x80,0x80,0x80,0xF0,0x80,0x80,0x80,0xF0,0x00,

        5, // 0x46 'F'
        0x00,0xF0,0x80,0x80,0x80,0xF0,0x80,0x80,0x80,0x80,0x00,

        5, // 0x47 'G'
        0x00,0x70,0x80,0x80,0x80,0xB0,0x90,0x90,0x90,0x60,0x00,

        5, // 0x48 'H'
        0x00,0x90,0x90,0x90,0x90,0xF0,0x90,0x90,0x90,0x90,0x00,

        4, // 0x49 'I'
        0x00,0xE0,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0xE0,0x00,

        5, // 0x4A 'J'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0xA0,0xA0,0x40,0x00,

        5, // 0x4B 'K'
        0x00,0x90,0x90,0xA0,0xA0,0xC0,0xA0,0xA0,0x90,0x90,0x00,

        5, // 0x4C 'L'
        0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0xF0,0x00,

        6, // 0x4D 'M'
        0x00,0x88,0xD8,0xA8,0xA8,0xA8,0x88,0x88,0x88,0x88,0x00,

        5, // 0x4E 'N'
        0x00,0x90,0x90,0xD0,0xD0,0xB0,0xB0,0x90,0x90,0x90,0x00,

        5, // 0x4F 'O'
        0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x50 'P'
        0x00,0xE0,0x90,0x90,0x90,0x90,0xE0,0x80,0x80,0x80,0x00,

        5, // 0x51 'Q'
        0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x30,

        5, // 0x52 'R'
        0x00,0xE0,0x90,0x90,0x90,0x90,0xE0,0xA0,0x90,0x90,0x00,

        5, // 0x53 'S'
        0x00,0x60,0x90,0x80,0x80,0x60,0x10,0x10,0x90,0x60,0x00,

        6, // 0x54 'T'
        0x00,0xF8,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,

        5, // 0x55 'U'
        0x00,0x90,0x90,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x00,

        6, // 0x56 'V'
        0x00,0x88,0x88,0x88,0x88,0x50,0x50,0x50,0x20,0x20,0x00,

        6, // 0x57 'W'
        0x00,0x88,0x88,0x88,0xA8,0xA8,0xA8,0xA8,0xA8,0x50,0x00,

        5, // 0x58 'X'
        0x00,0x90,0x90,0x90,0x60,0x60,0x90,0x90,0x90,0x90,0x00,

        6, // 0x59 'Y'
        0x00,0x88,0x88,0x88,0x50,0x20,0x20,0x20,0x20,0x20,0x00,

        5, // 0x5A 'Z'
        0x00,0xF0,0x10,0x20,0x20,0x40,0x40,0x80,0x80,0xF0,0x00,

        4, // 0x5B '['
        0x60,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x60,0x00,

        6, // 0x5C '\'
        0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x00,

        4, // 0x5D ']'
        0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x60,0x00,

        5, // 0x5E '^'
        0x00,0x20,0x50,0x88,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0x00,

        5, // 0x60 '`'
        0x00,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x61 'a'
        0x00,0x00,0x00,0x60,0x90,0x10,0x70,0x90,0x90,0x70,0x00,

        5, // 0x62 'b'
        0x00,0x80,0x80,0x80,0xE0,0x90,0x90,0x90,0x90,0xE0,0x00,

        5, // 0x63 'c'
        0x00,0x00,0x00,0x60,0x90,0x80,0x80,0x80,0x90,0x60,0x00,

        5, // 0x64 'd'
        0x00,0x10,0x10,0x10,0x70,0x90,0x90,0x90,0x90,0x70,0x00,

        5, // 0x65 'e'
        0x00,0x00,0x00,0x60,0x90,0x90,0xF0,0x80,0x90,0x60,0x00,

        4, // 0x66 'f'
        0x00,0x20,0x40,0x40,0xE0,0x40,0x40,0x40,0x40,0x40,0x00,

        5, // 0x67 'g'
        0x00,0x00,0x00,0x70,0x90,0x90,0x90,0x70,0x10,0x90,0x60,

        5, // 0x68 'h'
        0x00,0x80,0x80,0x80,0xE0,0x90,0x90,0x90,0x90,0x90,0x00,

        2, // 0x69 'i'
        0x00,0x80,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x00,

        4, // 0x6A 'j'
        0x00,0x20,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0xA0,0x40,

        5, // 0x6B 'k'
        0x00,0x80,0x80,0x90,0x90,0xA0,0xC0,0xA0,0x90,0x90,0x00,

        2, // 0x6C 'l'
        0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x00,

        6, // 0x6D 'm'
        0x00,0x00,0x00,0xD0,0xA8,0xA8,0xA8,0x88,0x88,0x88,0x00,

        5, // 0x6E 'n'
        0x00,0x00,0x00,0xA0,0xD0,0x90,0x90,0x90,0x90,0x90,0x00,

        5, // 0x6F 'o'
        0x00,0x00,0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x70 'p'
        0x00,0x00,0x00,0xE0,0x90,0x90,0x90,0x90,0xE0,0x80,0x80,

        5, // 0x71 'q'
        0x00,0x00,0x00,0x70,0x90,0x90,0x90,0x90,0x70,0x10,0x10,

        6, // 0x72 'r'
        0x00,0x00,0x00,0xB8,0x48,0x40,0x40,0x40,0x40,0x40,0x00,

        5, // 0x73 's'
        0x00,0x00,0x00,0x60,0x90,0x40,0x20,0x10,0x90,0x60,0x00,

        4, // 0x74 't'
        0x00,0x40,0x40,0xE0,0x40,0x40,0x40,0x40,0x40,0x20,0x00,

        5, // 0x75 'u'
        0x00,0x00,0x00,0x90,0x90,0x90,0x90,0x90,0x90,0x70,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x88,0x88,0x88,0x50,0x50,0x20,0x20,0x00,

        6, // 0x77 'w'
        0x00,0x00,0x00,0x88,0x88,0x88,0xA8,0xA8,0xA8,0x50,0x00,

        5, // 0x78 'x'
        0x00,0x00,0x00,0x90,0x90,0x60,0x60,0x90,0x90,0x90,0x00,

        5, // 0x79 'y'
        0x00,0x00,0x00,0x90,0x90,0x90,0x90,0x70,0x10,0x20,0xC0,

        5, // 0x7A 'z'
        0x00,0x00,0x00,0xF0,0x10,0x20,0x40,0x80,0x80,0xF0,0x00,

        5, // 0x7B '{'
        0x30,0x40,0x40,0x40,0x40,0x80,0x40,0x40,0x40,0x40,0x30,

        3, // 0x7C '|'
        0x00,0x40,0x40,0x40,0x40,0x00,0x40,0x40,0x40,0x40,0x00,

        5, // 0x7D '}'
        0xC0,0x20,0x20,0x20,0x20,0x10,0x20,0x20,0x20,0x20,0xC0,

        5, // 0x7E '~'
        0x00,0x40,0xA8,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x7F ''
        0x00,0x20,0x70,0xD8,0x88,0x88,0xF8,0x00,0x00,0x00,0x00,

        0
    };

    const int8u mcs12_prop[] = 
    {
        12, 3, 32, 128-32,
        0x00,0x00,0x0D,0x00,0x1A,0x00,0x27,0x00,0x34,0x00,0x41,0x00,0x4E,0x00,0x5B,0x00,0x68,0x00,
        0x75,0x00,0x82,0x00,0x8F,0x00,0x9C,0x00,0xA9,0x00,0xB6,0x00,0xC3,0x00,0xD0,0x00,0xDD,0x00,
        0xEA,0x00,0xF7,0x00,0x04,0x01,0x11,0x01,0x1E,0x01,0x2B,0x01,0x38,0x01,0x45,0x01,0x52,0x01,
        0x5F,0x01,0x6C,0x01,0x79,0x01,0x86,0x01,0x93,0x01,0xA0,0x01,0xAD,0x01,0xBA,0x01,0xC7,0x01,
        0xD4,0x01,0xE1,0x01,0xEE,0x01,0xFB,0x01,0x08,0x02,0x15,0x02,0x22,0x02,0x2F,0x02,0x3C,0x02,
        0x49,0x02,0x62,0x02,0x6F,0x02,0x7C,0x02,0x89,0x02,0x96,0x02,0xA3,0x02,0xB0,0x02,0xBD,0x02,
        0xCA,0x02,0xD7,0x02,0xF0,0x02,0xFD,0x02,0x0A,0x03,0x17,0x03,0x24,0x03,0x31,0x03,0x3E,0x03,
        0x4B,0x03,0x58,0x03,0x65,0x03,0x72,0x03,0x7F,0x03,0x8C,0x03,0x99,0x03,0xA6,0x03,0xB3,0x03,
        0xC0,0x03,0xCD,0x03,0xDA,0x03,0xE7,0x03,0xF4,0x03,0x01,0x04,0x1A,0x04,0x27,0x04,0x34,0x04,
        0x41,0x04,0x4E,0x04,0x5B,0x04,0x68,0x04,0x75,0x04,0x82,0x04,0x8F,0x04,0xA8,0x04,0xB5,0x04,
        0xC2,0x04,0xCF,0x04,0xDC,0x04,0xE9,0x04,0xF6,0x04,0x03,0x05,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,0x00,

        4, // 0x22 '"'
        0x50,0x50,0xA0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x23 '#'
        0x28,0x28,0x28,0x7C,0x28,0x28,0x28,0x7C,0x28,0x28,0x28,0x00,

        6, // 0x24 '$'
        0x10,0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x38,0x10,0x10,0x00,

        7, // 0x25 '%'
        0x32,0x54,0x64,0x08,0x08,0x10,0x10,0x26,0x2A,0x4C,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x30,0x48,0x48,0x48,0x30,0x4A,0x4A,0x44,0x3A,0x00,0x00,

        3, // 0x27 '''
        0x40,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,0x00,

        5, // 0x29 ')'
        0x40,0x20,0x20,0x10,0x10,0x10,0x10,0x10,0x20,0x20,0x40,0x00,

        6, // 0x2A '*'
        0x00,0x00,0x10,0x54,0x38,0x7C,0x38,0x54,0x10,0x00,0x00,0x00,

        6, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,

        4, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x40,0x80,

        6, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,

        7, // 0x2F '/'
        0x00,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,

        7, // 0x30 '0'
        0x00,0x38,0x44,0x44,0x54,0x54,0x54,0x44,0x44,0x38,0x00,0x00,

        4, // 0x31 '1'
        0x00,0x20,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x38,0x44,0x04,0x04,0x38,0x04,0x04,0x44,0x38,0x00,0x00,

        6, // 0x34 '4'
        0x00,0x08,0x18,0x28,0x28,0x48,0x48,0x7C,0x08,0x08,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x7C,0x40,0x40,0x78,0x44,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x38,0x44,0x40,0x78,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x37 '7'
        0x00,0x7C,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x20,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x38,0x44,0x44,0x44,0x38,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x38,0x44,0x44,0x44,0x3C,0x04,0x04,0x44,0x38,0x00,0x00,

        4, // 0x3A ':'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0x00,0x00,

        4, // 0x3B ';'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0x40,0x80,

        6, // 0x3C '<'
        0x00,0x00,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x00,

        6, // 0x3D '='
        0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,

        6, // 0x3E '>'
        0x00,0x00,0x40,0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x40,0x00,

        6, // 0x3F '?'
        0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x10,0x00,0x10,0x00,0x00,

        7, // 0x40 '@'
        0x00,0x38,0x44,0x44,0x5C,0x54,0x54,0x4C,0x40,0x38,0x00,0x00,

        7, // 0x41 'A'
        0x00,0x38,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x78,0x44,0x44,0x44,0x78,0x44,0x44,0x44,0x78,0x00,0x00,

        6, // 0x43 'C'
        0x00,0x38,0x44,0x40,0x40,0x40,0x40,0x40,0x44,0x38,0x00,0x00,

        7, // 0x44 'D'
        0x00,0x70,0x48,0x44,0x44,0x44,0x44,0x44,0x48,0x70,0x00,0x00,

        6, // 0x45 'E'
        0x00,0x7C,0x40,0x40,0x40,0x78,0x40,0x40,0x40,0x7C,0x00,0x00,

        6, // 0x46 'F'
        0x00,0x7C,0x40,0x40,0x40,0x78,0x40,0x40,0x40,0x40,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x38,0x44,0x40,0x40,0x5C,0x44,0x44,0x4C,0x34,0x00,0x00,

        7, // 0x48 'H'
        0x00,0x44,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        6, // 0x4A 'J'
        0x00,0x1C,0x08,0x08,0x08,0x08,0x08,0x48,0x48,0x30,0x00,0x00,

        6, // 0x4B 'K'
        0x00,0x44,0x48,0x50,0x60,0x60,0x50,0x48,0x44,0x44,0x00,0x00,

        6, // 0x4C 'L'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,0x00,

        9, // 0x4D 'M'
        0x00,0x00,0x41,0x00,0x63,0x00,0x55,0x00,0x49,0x00,0x49,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,

        7, // 0x4E 'N'
        0x00,0x44,0x64,0x64,0x54,0x54,0x4C,0x4C,0x44,0x44,0x00,0x00,

        7, // 0x4F 'O'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x00,0x00,

        7, // 0x51 'Q'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x54,0x48,0x34,0x00,0x00,

        7, // 0x52 'R'
        0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x48,0x44,0x44,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x38,0x44,0x40,0x40,0x38,0x04,0x04,0x44,0x38,0x00,0x00,

        6, // 0x54 'T'
        0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        7, // 0x55 'U'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x56 'V'
        0x00,0x44,0x44,0x44,0x44,0x28,0x28,0x28,0x10,0x10,0x00,0x00,

        9, // 0x57 'W'
        0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x49,0x00,0x49,0x00,0x55,0x00,0x22,0x00,0x00,0x00,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x44,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x44,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x10,0x00,0x00,

        6, // 0x5A 'Z'
        0x00,0x7C,0x04,0x04,0x08,0x10,0x20,0x40,0x40,0x7C,0x00,0x00,

        4, // 0x5B '['
        0x70,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x70,0x00,

        7, // 0x5C '\'
        0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x00,

        4, // 0x5D ']'
        0xE0,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0xE0,0x00,

        6, // 0x5E '^'
        0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,

        4, // 0x60 '`'
        0x00,0x40,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x38,0x04,0x3C,0x44,0x44,0x44,0x3C,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x00,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x40,0x44,0x38,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x04,0x04,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x38,0x44,0x44,0x7C,0x40,0x44,0x38,0x00,0x00,

        4, // 0x66 'f'
        0x00,0x30,0x40,0xE0,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x04,0x78,

        7, // 0x68 'h'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x40,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        5, // 0x6A 'j'
        0x00,0x10,0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x90,0x60,

        6, // 0x6B 'k'
        0x00,0x40,0x40,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        9, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x76,0x00,0x49,0x00,0x49,0x00,0x49,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,

        7, // 0x6E 'n'
        0x00,0x00,0x00,0x58,0x64,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x6F 'o'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x40,0x40,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x04,0x04,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x58,0x24,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x38,0x00,0x00,

        5, // 0x74 't'
        0x00,0x20,0x20,0x70,0x20,0x20,0x20,0x20,0x20,0x18,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x4C,0x34,0x00,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x44,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,

        9, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x49,0x00,0x49,0x00,0x49,0x00,0x36,0x00,0x00,0x00,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x3C,0x08,0x70,

        6, // 0x7A 'z'
        0x00,0x00,0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        5, // 0x7B '{'
        0x18,0x20,0x20,0x20,0x20,0xC0,0x20,0x20,0x20,0x20,0x18,0x00,

        3, // 0x7C '|'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,

        5, // 0x7D '}'
        0xC0,0x20,0x20,0x20,0x20,0x18,0x20,0x20,0x20,0x20,0xC0,0x00,

        7, // 0x7E '~'
        0x00,0x60,0x92,0x92,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x7F ''
        0x00,0x10,0x38,0x6C,0x44,0x44,0x7C,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u mcs13_prop[] = 
    {
        13, 4, 32, 128-32,
        0x00,0x00,0x0E,0x00,0x1C,0x00,0x2A,0x00,0x38,0x00,0x46,0x00,0x54,0x00,0x62,0x00,0x70,0x00,
        0x7E,0x00,0x8C,0x00,0x9A,0x00,0xA8,0x00,0xB6,0x00,0xC4,0x00,0xD2,0x00,0xE0,0x00,0xEE,0x00,
        0xFC,0x00,0x0A,0x01,0x18,0x01,0x26,0x01,0x34,0x01,0x42,0x01,0x50,0x01,0x5E,0x01,0x6C,0x01,
        0x7A,0x01,0x88,0x01,0x96,0x01,0xA4,0x01,0xB2,0x01,0xC0,0x01,0xCE,0x01,0xDC,0x01,0xEA,0x01,
        0xF8,0x01,0x06,0x02,0x14,0x02,0x22,0x02,0x30,0x02,0x3E,0x02,0x4C,0x02,0x5A,0x02,0x68,0x02,
        0x76,0x02,0x91,0x02,0x9F,0x02,0xAD,0x02,0xBB,0x02,0xC9,0x02,0xD7,0x02,0xE5,0x02,0xF3,0x02,
        0x01,0x03,0x0F,0x03,0x2A,0x03,0x38,0x03,0x46,0x03,0x54,0x03,0x62,0x03,0x70,0x03,0x7E,0x03,
        0x8C,0x03,0x9A,0x03,0xA8,0x03,0xB6,0x03,0xC4,0x03,0xD2,0x03,0xE0,0x03,0xEE,0x03,0xFC,0x03,
        0x0A,0x04,0x18,0x04,0x26,0x04,0x34,0x04,0x42,0x04,0x50,0x04,0x6B,0x04,0x79,0x04,0x87,0x04,
        0x95,0x04,0xA3,0x04,0xB1,0x04,0xBF,0x04,0xCD,0x04,0xDB,0x04,0xE9,0x04,0x04,0x05,0x12,0x05,
        0x20,0x05,0x2E,0x05,0x3C,0x05,0x4A,0x05,0x58,0x05,0x66,0x05,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,0x00,

        4, // 0x22 '"'
        0x00,0x50,0x50,0xA0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x23 '#'
        0x00,0x28,0x28,0x28,0x7C,0x28,0x28,0x28,0x7C,0x28,0x28,0x28,0x00,

        6, // 0x24 '$'
        0x00,0x10,0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x38,0x10,0x10,0x00,

        7, // 0x25 '%'
        0x00,0x32,0x54,0x64,0x08,0x08,0x10,0x10,0x26,0x2A,0x4C,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x30,0x48,0x48,0x48,0x30,0x4A,0x4A,0x44,0x3A,0x00,0x00,0x00,

        3, // 0x27 '''
        0x00,0x40,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,0x00,0x00,

        5, // 0x29 ')'
        0x40,0x20,0x20,0x10,0x10,0x10,0x10,0x10,0x20,0x20,0x40,0x00,0x00,

        6, // 0x2A '*'
        0x00,0x00,0x10,0x54,0x38,0x7C,0x38,0x54,0x10,0x00,0x00,0x00,0x00,

        6, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,0x00,

        4, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x20,0x40,0x80,

        6, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,

        7, // 0x2F '/'
        0x00,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,0x00,

        7, // 0x30 '0'
        0x00,0x38,0x44,0x44,0x54,0x54,0x54,0x44,0x44,0x38,0x00,0x00,0x00,

        4, // 0x31 '1'
        0x00,0x20,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x38,0x44,0x04,0x04,0x38,0x04,0x04,0x44,0x38,0x00,0x00,0x00,

        6, // 0x34 '4'
        0x00,0x08,0x18,0x28,0x28,0x48,0x48,0x7C,0x08,0x08,0x00,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x7C,0x40,0x40,0x78,0x44,0x04,0x04,0x44,0x38,0x00,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x38,0x44,0x40,0x78,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        6, // 0x37 '7'
        0x00,0x7C,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x20,0x00,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x38,0x44,0x44,0x44,0x38,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x38,0x44,0x44,0x44,0x3C,0x04,0x04,0x44,0x38,0x00,0x00,0x00,

        4, // 0x3A ':'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,

        4, // 0x3B ';'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0x20,0x40,0x80,

        6, // 0x3C '<'
        0x00,0x00,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x00,0x00,

        6, // 0x3D '='
        0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        6, // 0x3E '>'
        0x00,0x00,0x40,0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x40,0x00,0x00,

        6, // 0x3F '?'
        0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x10,0x00,0x10,0x00,0x00,0x00,

        7, // 0x40 '@'
        0x00,0x38,0x44,0x44,0x5C,0x54,0x54,0x4C,0x40,0x38,0x00,0x00,0x00,

        7, // 0x41 'A'
        0x00,0x38,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x78,0x44,0x44,0x44,0x78,0x44,0x44,0x44,0x78,0x00,0x00,0x00,

        6, // 0x43 'C'
        0x00,0x38,0x44,0x40,0x40,0x40,0x40,0x40,0x44,0x38,0x00,0x00,0x00,

        7, // 0x44 'D'
        0x00,0x70,0x48,0x44,0x44,0x44,0x44,0x44,0x48,0x70,0x00,0x00,0x00,

        6, // 0x45 'E'
        0x00,0x7C,0x40,0x40,0x40,0x78,0x40,0x40,0x40,0x7C,0x00,0x00,0x00,

        6, // 0x46 'F'
        0x00,0x7C,0x40,0x40,0x40,0x78,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x38,0x44,0x40,0x40,0x5C,0x44,0x44,0x4C,0x34,0x00,0x00,0x00,

        7, // 0x48 'H'
        0x00,0x44,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,0x00,

        6, // 0x4A 'J'
        0x00,0x1C,0x08,0x08,0x08,0x08,0x08,0x48,0x48,0x30,0x00,0x00,0x00,

        6, // 0x4B 'K'
        0x00,0x44,0x48,0x50,0x60,0x60,0x50,0x48,0x44,0x44,0x00,0x00,0x00,

        6, // 0x4C 'L'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,0x00,0x00,

        9, // 0x4D 'M'
        0x00,0x00,0x41,0x00,0x63,0x00,0x55,0x00,0x49,0x00,0x49,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x4E 'N'
        0x00,0x44,0x64,0x64,0x54,0x54,0x4C,0x4C,0x44,0x44,0x00,0x00,0x00,

        7, // 0x4F 'O'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x51 'Q'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x54,0x48,0x34,0x00,0x00,0x00,

        7, // 0x52 'R'
        0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x48,0x44,0x44,0x00,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x38,0x44,0x40,0x40,0x38,0x04,0x04,0x44,0x38,0x00,0x00,0x00,

        6, // 0x54 'T'
        0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,0x00,

        7, // 0x55 'U'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        6, // 0x56 'V'
        0x00,0x44,0x44,0x44,0x44,0x28,0x28,0x28,0x10,0x10,0x00,0x00,0x00,

        9, // 0x57 'W'
        0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x49,0x00,0x49,0x00,0x55,0x00,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x44,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x44,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x10,0x00,0x00,0x00,

        6, // 0x5A 'Z'
        0x00,0x7C,0x04,0x04,0x08,0x10,0x20,0x40,0x40,0x7C,0x00,0x00,0x00,

        4, // 0x5B '['
        0x70,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x70,0x00,0x00,

        7, // 0x5C '\'
        0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x00,0x00,

        4, // 0x5D ']'
        0xE0,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0xE0,0x00,0x00,

        6, // 0x5E '^'
        0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,

        4, // 0x60 '`'
        0x00,0x40,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x38,0x04,0x3C,0x44,0x44,0x44,0x3C,0x00,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x00,0x00,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x40,0x44,0x38,0x00,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x04,0x04,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x00,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x38,0x44,0x44,0x7C,0x40,0x44,0x38,0x00,0x00,0x00,

        4, // 0x66 'f'
        0x00,0x30,0x40,0xE0,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x04,0x44,0x38,

        7, // 0x68 'h'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x40,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        5, // 0x6A 'j'
        0x00,0x10,0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x90,0x60,0x00,

        6, // 0x6B 'k'
        0x00,0x40,0x40,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        9, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x76,0x00,0x49,0x00,0x49,0x00,0x49,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x6E 'n'
        0x00,0x00,0x00,0x58,0x64,0x44,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x6F 'o'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x40,0x40,0x40,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x04,0x04,0x04,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x58,0x24,0x20,0x20,0x20,0x20,0x70,0x00,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x38,0x00,0x00,0x00,

        5, // 0x74 't'
        0x00,0x20,0x20,0x70,0x20,0x20,0x20,0x20,0x20,0x18,0x00,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x4C,0x34,0x00,0x00,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x44,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,0x00,

        9, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x49,0x00,0x49,0x00,0x49,0x00,0x36,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x3C,0x04,0x08,0x70,

        6, // 0x7A 'z'
        0x00,0x00,0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,0x00,

        5, // 0x7B '{'
        0x18,0x20,0x20,0x20,0x20,0xC0,0x20,0x20,0x20,0x20,0x18,0x00,0x00,

        3, // 0x7C '|'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        5, // 0x7D '}'
        0xC0,0x20,0x20,0x20,0x20,0x18,0x20,0x20,0x20,0x20,0xC0,0x00,0x00,

        7, // 0x7E '~'
        0x00,0x60,0x92,0x92,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x7F ''
        0x00,0x10,0x38,0x6C,0x44,0x44,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u mcs5x10_mono[] = 
    {
        10, 2, 32, 128-32,
        0x00,0x00,0x0B,0x00,0x16,0x00,0x21,0x00,0x2C,0x00,0x37,0x00,0x42,0x00,0x4D,0x00,0x58,0x00,
        0x63,0x00,0x6E,0x00,0x79,0x00,0x84,0x00,0x8F,0x00,0x9A,0x00,0xA5,0x00,0xB0,0x00,0xBB,0x00,
        0xC6,0x00,0xD1,0x00,0xDC,0x00,0xE7,0x00,0xF2,0x00,0xFD,0x00,0x08,0x01,0x13,0x01,0x1E,0x01,
        0x29,0x01,0x34,0x01,0x3F,0x01,0x4A,0x01,0x55,0x01,0x60,0x01,0x6B,0x01,0x76,0x01,0x81,0x01,
        0x8C,0x01,0x97,0x01,0xA2,0x01,0xAD,0x01,0xB8,0x01,0xC3,0x01,0xCE,0x01,0xD9,0x01,0xE4,0x01,
        0xEF,0x01,0xFA,0x01,0x05,0x02,0x10,0x02,0x1B,0x02,0x26,0x02,0x31,0x02,0x3C,0x02,0x47,0x02,
        0x52,0x02,0x5D,0x02,0x68,0x02,0x73,0x02,0x7E,0x02,0x89,0x02,0x94,0x02,0x9F,0x02,0xAA,0x02,
        0xB5,0x02,0xC0,0x02,0xCB,0x02,0xD6,0x02,0xE1,0x02,0xEC,0x02,0xF7,0x02,0x02,0x03,0x0D,0x03,
        0x18,0x03,0x23,0x03,0x2E,0x03,0x39,0x03,0x44,0x03,0x4F,0x03,0x5A,0x03,0x65,0x03,0x70,0x03,
        0x7B,0x03,0x86,0x03,0x91,0x03,0x9C,0x03,0xA7,0x03,0xB2,0x03,0xBD,0x03,0xC8,0x03,0xD3,0x03,
        0xDE,0x03,0xE9,0x03,0xF4,0x03,0xFF,0x03,0x0A,0x04,0x15,0x04,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,

        5, // 0x22 '"'
        0x00,0x50,0x50,0xA0,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x23 '#'
        0x00,0x50,0x50,0xF8,0x50,0x50,0x50,0xF8,0x50,0x50,

        5, // 0x24 '$'
        0x00,0x40,0x60,0x90,0x80,0x60,0x10,0x90,0x60,0x20,

        5, // 0x25 '%'
        0x00,0x00,0x90,0x90,0x20,0x20,0x40,0x40,0x90,0x90,

        5, // 0x26 '&'
        0x00,0x40,0xA0,0xA0,0xA0,0x40,0xA8,0x90,0x90,0x68,

        5, // 0x27 '''
        0x00,0x20,0x20,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x20,0x20,0x10,

        5, // 0x29 ')'
        0x40,0x20,0x20,0x10,0x10,0x10,0x10,0x20,0x20,0x40,

        5, // 0x2A '*'
        0x00,0x00,0x90,0x60,0xF0,0x60,0x90,0x00,0x00,0x00,

        5, // 0x2B '+'
        0x00,0x00,0x00,0x20,0x20,0xF8,0x20,0x20,0x00,0x00,

        5, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0xC0,

        5, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0xF0,0x00,0x00,0x00,0x00,

        5, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,

        5, // 0x2F '/'
        0x00,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x80,0x00,

        5, // 0x30 '0'
        0x00,0x70,0x90,0x90,0x90,0x90,0x90,0x90,0xE0,0x00,

        5, // 0x31 '1'
        0x00,0x20,0x60,0x20,0x20,0x20,0x20,0x20,0x70,0x00,

        5, // 0x32 '2'
        0x00,0x60,0x90,0x90,0x10,0x20,0x40,0x80,0xF0,0x00,

        5, // 0x33 '3'
        0x00,0x60,0x90,0x10,0x60,0x10,0x10,0x90,0x60,0x00,

        5, // 0x34 '4'
        0x00,0x10,0x30,0x50,0x50,0x90,0xF0,0x10,0x10,0x00,

        5, // 0x35 '5'
        0x00,0xF0,0x80,0x80,0xE0,0x10,0x10,0x90,0x60,0x00,

        5, // 0x36 '6'
        0x00,0x60,0x80,0x80,0xE0,0x90,0x90,0x90,0x60,0x00,

        5, // 0x37 '7'
        0x00,0xF0,0x10,0x10,0x20,0x20,0x40,0x40,0x40,0x00,

        5, // 0x38 '8'
        0x00,0x60,0x90,0x90,0x60,0x90,0x90,0x90,0x60,0x00,

        5, // 0x39 '9'
        0x00,0x60,0x90,0x90,0x90,0x70,0x10,0x10,0x60,0x00,

        5, // 0x3A ':'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x60,0x60,0x00,

        5, // 0x3B ';'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x60,0x60,0xC0,

        5, // 0x3C '<'
        0x00,0x08,0x10,0x20,0x40,0x80,0x40,0x20,0x10,0x08,

        5, // 0x3D '='
        0x00,0x00,0x00,0x00,0xF0,0x00,0xF0,0x00,0x00,0x00,

        5, // 0x3E '>'
        0x00,0x80,0x40,0x20,0x10,0x08,0x10,0x20,0x40,0x80,

        5, // 0x3F '?'
        0x00,0x60,0x90,0x10,0x10,0x20,0x40,0x00,0x40,0x00,

        5, // 0x40 '@'
        0x00,0x60,0x90,0x90,0xB0,0xB0,0x80,0x80,0x70,0x00,

        5, // 0x41 'A'
        0x00,0x60,0x90,0x90,0x90,0xF0,0x90,0x90,0x90,0x00,

        5, // 0x42 'B'
        0x00,0xE0,0x90,0x90,0xE0,0x90,0x90,0x90,0xE0,0x00,

        5, // 0x43 'C'
        0x00,0x60,0x90,0x80,0x80,0x80,0x80,0x90,0x60,0x00,

        5, // 0x44 'D'
        0x00,0xE0,0x90,0x90,0x90,0x90,0x90,0x90,0xE0,0x00,

        5, // 0x45 'E'
        0x00,0xF0,0x80,0x80,0xF0,0x80,0x80,0x80,0xF0,0x00,

        5, // 0x46 'F'
        0x00,0xF0,0x80,0x80,0xF0,0x80,0x80,0x80,0x80,0x00,

        5, // 0x47 'G'
        0x00,0x60,0x90,0x80,0x80,0xB0,0x90,0x90,0x60,0x00,

        5, // 0x48 'H'
        0x00,0x90,0x90,0x90,0x90,0xF0,0x90,0x90,0x90,0x00,

        5, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,

        5, // 0x4A 'J'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0xA0,0x40,0x00,

        5, // 0x4B 'K'
        0x00,0x90,0xA0,0xA0,0xC0,0xC0,0xA0,0xA0,0x90,0x00,

        5, // 0x4C 'L'
        0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0xF0,0x00,

        5, // 0x4D 'M'
        0x00,0x90,0x90,0xF0,0xF0,0x90,0x90,0x90,0x90,0x00,

        5, // 0x4E 'N'
        0x00,0x90,0x90,0xD0,0xD0,0xB0,0xB0,0x90,0x90,0x00,

        5, // 0x4F 'O'
        0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x50 'P'
        0x00,0xE0,0x90,0x90,0x90,0xE0,0x80,0x80,0x80,0x00,

        5, // 0x51 'Q'
        0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x30,

        5, // 0x52 'R'
        0x00,0xE0,0x90,0x90,0x90,0xE0,0xA0,0x90,0x90,0x00,

        5, // 0x53 'S'
        0x00,0x60,0x90,0x80,0x60,0x10,0x90,0x90,0x60,0x00,

        5, // 0x54 'T'
        0x00,0xF8,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,

        5, // 0x55 'U'
        0x00,0x90,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x56 'V'
        0x00,0x90,0x90,0x90,0x50,0x50,0x50,0x20,0x20,0x00,

        5, // 0x57 'W'
        0x00,0x90,0x90,0x90,0x90,0x90,0xF0,0xF0,0x90,0x00,

        5, // 0x58 'X'
        0x00,0x90,0x90,0x90,0x60,0x60,0x90,0x90,0x90,0x00,

        5, // 0x59 'Y'
        0x00,0x88,0x88,0x88,0x50,0x20,0x20,0x20,0x20,0x00,

        5, // 0x5A 'Z'
        0x00,0xF0,0x10,0x20,0x20,0x40,0x40,0x80,0xF0,0x00,

        5, // 0x5B '['
        0x60,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x60,

        5, // 0x5C '\'
        0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,

        5, // 0x5D ']'
        0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x60,

        5, // 0x5E '^'
        0x00,0x20,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0x00,

        5, // 0x60 '`'
        0x00,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x61 'a'
        0x00,0x00,0x00,0x60,0x10,0x70,0x90,0x90,0x70,0x00,

        5, // 0x62 'b'
        0x00,0x80,0x80,0xE0,0x90,0x90,0x90,0x90,0xE0,0x00,

        5, // 0x63 'c'
        0x00,0x00,0x00,0x60,0x90,0x80,0x80,0x90,0x60,0x00,

        5, // 0x64 'd'
        0x00,0x10,0x10,0x70,0x90,0x90,0x90,0x90,0x70,0x00,

        5, // 0x65 'e'
        0x00,0x00,0x00,0x60,0x90,0x90,0xF0,0x80,0x70,0x00,

        5, // 0x66 'f'
        0x00,0x30,0x40,0xE0,0x40,0x40,0x40,0x40,0x40,0x00,

        5, // 0x67 'g'
        0x00,0x00,0x00,0x70,0x90,0x90,0x90,0x70,0x10,0xE0,

        5, // 0x68 'h'
        0x00,0x80,0x80,0xE0,0x90,0x90,0x90,0x90,0x90,0x00,

        5, // 0x69 'i'
        0x00,0x20,0x00,0x60,0x20,0x20,0x20,0x20,0x70,0x00,

        5, // 0x6A 'j'
        0x00,0x20,0x00,0x60,0x20,0x20,0x20,0x20,0x20,0xC0,

        5, // 0x6B 'k'
        0x00,0x80,0x80,0x90,0xA0,0xC0,0xA0,0x90,0x90,0x00,

        5, // 0x6C 'l'
        0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,

        5, // 0x6D 'm'
        0x00,0x00,0x00,0x90,0xF0,0x90,0x90,0x90,0x90,0x00,

        5, // 0x6E 'n'
        0x00,0x00,0x00,0xE0,0x90,0x90,0x90,0x90,0x90,0x00,

        5, // 0x6F 'o'
        0x00,0x00,0x00,0x60,0x90,0x90,0x90,0x90,0x60,0x00,

        5, // 0x70 'p'
        0x00,0x00,0x00,0xE0,0x90,0x90,0x90,0xE0,0x80,0x80,

        5, // 0x71 'q'
        0x00,0x00,0x00,0x70,0x90,0x90,0x90,0x70,0x10,0x10,

        5, // 0x72 'r'
        0x00,0x00,0x00,0xB0,0x50,0x40,0x40,0x40,0xE0,0x00,

        5, // 0x73 's'
        0x00,0x00,0x00,0x60,0x90,0x40,0x20,0x90,0x60,0x00,

        5, // 0x74 't'
        0x00,0x40,0x40,0xE0,0x40,0x40,0x40,0x50,0x20,0x00,

        5, // 0x75 'u'
        0x00,0x00,0x00,0x90,0x90,0x90,0x90,0x90,0x70,0x00,

        5, // 0x76 'v'
        0x00,0x00,0x00,0x90,0x90,0x50,0x50,0x20,0x20,0x00,

        5, // 0x77 'w'
        0x00,0x00,0x00,0x90,0x90,0x90,0x90,0xF0,0x90,0x00,

        5, // 0x78 'x'
        0x00,0x00,0x00,0x90,0x90,0x60,0x60,0x90,0x90,0x00,

        5, // 0x79 'y'
        0x00,0x00,0x00,0x90,0x90,0x90,0x90,0x70,0x10,0xE0,

        5, // 0x7A 'z'
        0x00,0x00,0x00,0xF0,0x10,0x20,0x40,0x80,0xF0,0x00,

        5, // 0x7B '{'
        0x30,0x40,0x40,0x40,0x80,0x40,0x40,0x40,0x40,0x30,

        5, // 0x7C '|'
        0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,

        5, // 0x7D '}'
        0xC0,0x20,0x20,0x20,0x10,0x20,0x20,0x20,0x20,0xC0,

        5, // 0x7E '~'
        0x00,0x40,0xA8,0x10,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x7F ''
        0x00,0x20,0x70,0xD8,0x88,0x88,0xF8,0x00,0x00,0x00,

        0
    };

    const int8u mcs5x11_mono[] = 
    {
        11, 3, 32, 128-32,
        0x00,0x00,0x0C,0x00,0x18,0x00,0x24,0x00,0x30,0x00,0x3C,0x00,0x48,0x00,0x54,0x00,0x60,0x00,
        0x6C,0x00,0x78,0x00,0x84,0x00,0x90,0x00,0x9C,0x00,0xA8,0x00,0xB4,0x00,0xC0,0x00,0xCC,0x00,
        0xD8,0x00,0xE4,0x00,0xF0,0x00,0xFC,0x00,0x08,0x01,0x14,0x01,0x20,0x01,0x2C,0x01,0x38,0x01,
        0x44,0x01,0x50,0x01,0x5C,0x01,0x68,0x01,0x74,0x01,0x80,0x01,0x8C,0x01,0x98,0x01,0xA4,0x01,
        0xB0,0x01,0xBC,0x01,0xC8,0x01,0xD4,0x01,0xE0,0x01,0xEC,0x01,0xF8,0x01,0x04,0x02,0x10,0x02,
        0x1C,0x02,0x28,0x02,0x34,0x02,0x40,0x02,0x4C,0x02,0x58,0x02,0x64,0x02,0x70,0x02,0x7C,0x02,
        0x88,0x02,0x94,0x02,0xA0,0x02,0xAC,0x02,0xB8,0x02,0xC4,0x02,0xD0,0x02,0xDC,0x02,0xE8,0x02,
        0xF4,0x02,0x00,0x03,0x0C,0x03,0x18,0x03,0x24,0x03,0x30,0x03,0x3C,0x03,0x48,0x03,0x54,0x03,
        0x60,0x03,0x6C,0x03,0x78,0x03,0x84,0x03,0x90,0x03,0x9C,0x03,0xA8,0x03,0xB4,0x03,0xC0,0x03,
        0xCC,0x03,0xD8,0x03,0xE4,0x03,0xF0,0x03,0xFC,0x03,0x08,0x04,0x14,0x04,0x20,0x04,0x2C,0x04,
        0x38,0x04,0x44,0x04,0x50,0x04,0x5C,0x04,0x68,0x04,0x74,0x04,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,0x00,

        5, // 0x22 '"'
        0x00,0x50,0x50,0xA0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x23 '#'
        0x00,0x50,0x50,0xF8,0x50,0x50,0x50,0xF8,0x50,0x50,0x00,

        5, // 0x24 '$'
        0x00,0x40,0x60,0x90,0x80,0x60,0x10,0x90,0x60,0x20,0x00,

        5, // 0x25 '%'
        0x00,0x00,0x90,0x90,0x20,0x20,0x40,0x40,0x90,0x90,0x00,

        5, // 0x26 '&'
        0x00,0x40,0xA0,0xA0,0x40,0xA8,0x90,0x90,0x68,0x00,0x00,

        5, // 0x27 '''
        0x00,0x20,0x20,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x00,0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x20,0x20,0x10,

        5, // 0x29 ')'
        0x00,0x40,0x20,0x20,0x10,0x10,0x10,0x10,0x20,0x20,0x40,

        5, // 0x2A '*'
        0x00,0x00,0x90,0x60,0xF0,0x60,0x90,0x00,0x00,0x00,0x00,

        5, // 0x2B '+'
        0x00,0x00,0x00,0x20,0x20,0xF8,0x20,0x20,0x00,0x00,0x00,

        5, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x40,0x80,

        5, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0xF0,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,

        5, // 0x2F '/'
        0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x80,0x80,0x00,

        5, // 0x30 '0'
        0x00,0x70,0x90,0x90,0x90,0x90,0x90,0x90,0xE0,0x00,0x00,

        5, // 0x31 '1'
        0x00,0x20,0x60,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        5, // 0x32 '2'
        0x00,0x60,0x90,0x90,0x10,0x20,0x40,0x80,0xF0,0x00,0x00,

        5, // 0x33 '3'
        0x00,0x60,0x90,0x10,0x60,0x10,0x10,0x90,0x60,0x00,0x00,

        5, // 0x34 '4'
        0x00,0x10,0x30,0x50,0x50,0x90,0xF8,0x10,0x10,0x00,0x00,

        5, // 0x35 '5'
        0x00,0xF0,0x80,0xE0,0x90,0x10,0x10,0x90,0x60,0x00,0x00,

        5, // 0x36 '6'
        0x00,0x60,0x90,0x80,0xE0,0x90,0x90,0x90,0x60,0x00,0x00,

        5, // 0x37 '7'
        0x00,0xF0,0x10,0x10,0x20,0x20,0x40,0x40,0x40,0x00,0x00,

        5, // 0x38 '8'
        0x00,0x60,0x90,0x90,0x60,0x90,0x90,0x90,0x60,0x00,0x00,

        5, // 0x39 '9'
        0x00,0x60,0x90,0x90,0x90,0x70,0x10,0x90,0x60,0x00,0x00,

        5, // 0x3A ':'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x60,0x60,0x00,0x00,

        5, // 0x3B ';'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x60,0x60,0x40,0x80,

        5, // 0x3C '<'
        0x00,0x08,0x10,0x20,0x40,0x80,0x40,0x20,0x10,0x08,0x00,

        5, // 0x3D '='
        0x00,0x00,0x00,0x00,0xF0,0x00,0x00,0xF0,0x00,0x00,0x00,

        5, // 0x3E '>'
        0x00,0x80,0x40,0x20,0x10,0x08,0x10,0x20,0x40,0x80,0x00,

        5, // 0x3F '?'
        0x00,0x60,0x90,0x10,0x10,0x20,0x40,0x00,0x40,0x00,0x00,

        5, // 0x40 '@'
        0x00,0x60,0x90,0x90,0xB0,0xB0,0x80,0x80,0x70,0x00,0x00,

        5, // 0x41 'A'
        0x00,0x60,0x90,0x90,0x90,0xF0,0x90,0x90,0x90,0x00,0x00,

        5, // 0x42 'B'
        0x00,0xE0,0x90,0x90,0xE0,0x90,0x90,0x90,0xE0,0x00,0x00,

        5, // 0x43 'C'
        0x00,0x60,0x90,0x80,0x80,0x80,0x80,0x90,0x60,0x00,0x00,

        5, // 0x44 'D'
        0x00,0xE0,0x90,0x90,0x90,0x90,0x90,0x90,0xE0,0x00,0x00,

        5, // 0x45 'E'
        0x00,0xF0,0x80,0x80,0xE0,0x80,0x80,0x80,0xF0,0x00,0x00,

        5, // 0x46 'F'
        0x00,0xF0,0x80,0x80,0xE0,0x80,0x80,0x80,0x80,0x00,0x00,

        5, // 0x47 'G'
        0x00,0x60,0x90,0x80,0x80,0xB0,0x90,0x90,0x60,0x00,0x00,

        5, // 0x48 'H'
        0x00,0x90,0x90,0x90,0xF0,0x90,0x90,0x90,0x90,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        5, // 0x4A 'J'
        0x00,0x70,0x20,0x20,0x20,0x20,0xA0,0xA0,0x40,0x00,0x00,

        5, // 0x4B 'K'
        0x00,0x90,0xA0,0xA0,0xC0,0xA0,0xA0,0x90,0x90,0x00,0x00,

        5, // 0x4C 'L'
        0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0xF0,0x00,0x00,

        5, // 0x4D 'M'
        0x00,0x90,0xF0,0xF0,0x90,0x90,0x90,0x90,0x90,0x00,0x00,

        5, // 0x4E 'N'
        0x00,0x90,0x90,0xD0,0xD0,0xB0,0xB0,0x90,0x90,0x00,0x00,

        5, // 0x4F 'O'
        0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x00,0x00,

        5, // 0x50 'P'
        0x00,0xE0,0x90,0x90,0x90,0xE0,0x80,0x80,0x80,0x00,0x00,

        5, // 0x51 'Q'
        0x00,0x60,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x30,0x00,

        5, // 0x52 'R'
        0x00,0xE0,0x90,0x90,0x90,0xE0,0xA0,0x90,0x90,0x00,0x00,

        5, // 0x53 'S'
        0x00,0x60,0x90,0x80,0x60,0x10,0x90,0x90,0x60,0x00,0x00,

        5, // 0x54 'T'
        0x00,0xF8,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,

        5, // 0x55 'U'
        0x00,0x90,0x90,0x90,0x90,0x90,0x90,0x90,0x60,0x00,0x00,

        5, // 0x56 'V'
        0x00,0x90,0x90,0x90,0x50,0x50,0x50,0x20,0x20,0x00,0x00,

        5, // 0x57 'W'
        0x00,0x90,0x90,0x90,0x90,0x90,0xF0,0xF0,0x90,0x00,0x00,

        5, // 0x58 'X'
        0x00,0x90,0x90,0x90,0x60,0x60,0x90,0x90,0x90,0x00,0x00,

        5, // 0x59 'Y'
        0x00,0x88,0x88,0x88,0x50,0x20,0x20,0x20,0x20,0x00,0x00,

        5, // 0x5A 'Z'
        0x00,0xF0,0x10,0x20,0x20,0x40,0x40,0x80,0xF0,0x00,0x00,

        5, // 0x5B '['
        0x00,0x60,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x60,

        5, // 0x5C '\'
        0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x00,

        5, // 0x5D ']'
        0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x60,

        5, // 0x5E '^'
        0x00,0x20,0x50,0x88,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0x00,0x00,

        5, // 0x60 '`'
        0x00,0x40,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x61 'a'
        0x00,0x00,0x00,0x60,0x10,0x70,0x90,0x90,0x70,0x00,0x00,

        5, // 0x62 'b'
        0x00,0x80,0x80,0xE0,0x90,0x90,0x90,0x90,0xE0,0x00,0x00,

        5, // 0x63 'c'
        0x00,0x00,0x00,0x60,0x90,0x80,0x80,0x90,0x60,0x00,0x00,

        5, // 0x64 'd'
        0x00,0x10,0x10,0x70,0x90,0x90,0x90,0x90,0x70,0x00,0x00,

        5, // 0x65 'e'
        0x00,0x00,0x00,0x60,0x90,0x90,0xF0,0x80,0x70,0x00,0x00,

        5, // 0x66 'f'
        0x00,0x30,0x40,0xE0,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        5, // 0x67 'g'
        0x00,0x00,0x00,0x70,0x90,0x90,0x90,0x90,0x70,0x10,0xE0,

        5, // 0x68 'h'
        0x00,0x80,0x80,0xE0,0x90,0x90,0x90,0x90,0x90,0x00,0x00,

        5, // 0x69 'i'
        0x00,0x20,0x00,0x60,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        5, // 0x6A 'j'
        0x00,0x20,0x00,0x60,0x20,0x20,0x20,0x20,0x20,0xA0,0x40,

        5, // 0x6B 'k'
        0x00,0x80,0x80,0x90,0xA0,0xC0,0xA0,0x90,0x90,0x00,0x00,

        5, // 0x6C 'l'
        0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        5, // 0x6D 'm'
        0x00,0x00,0x00,0x90,0xF0,0x90,0x90,0x90,0x90,0x00,0x00,

        5, // 0x6E 'n'
        0x00,0x00,0x00,0xE0,0x90,0x90,0x90,0x90,0x90,0x00,0x00,

        5, // 0x6F 'o'
        0x00,0x00,0x00,0x60,0x90,0x90,0x90,0x90,0x60,0x00,0x00,

        5, // 0x70 'p'
        0x00,0x00,0x00,0xE0,0x90,0x90,0x90,0x90,0xE0,0x80,0x80,

        5, // 0x71 'q'
        0x00,0x00,0x00,0x70,0x90,0x90,0x90,0x90,0x70,0x10,0x10,

        5, // 0x72 'r'
        0x00,0x00,0x00,0xA0,0x50,0x40,0x40,0x40,0xE0,0x00,0x00,

        5, // 0x73 's'
        0x00,0x00,0x00,0x60,0x90,0x40,0x20,0x90,0x60,0x00,0x00,

        5, // 0x74 't'
        0x00,0x40,0x40,0xE0,0x40,0x40,0x40,0x40,0x30,0x00,0x00,

        5, // 0x75 'u'
        0x00,0x00,0x00,0x90,0x90,0x90,0x90,0x90,0x70,0x00,0x00,

        5, // 0x76 'v'
        0x00,0x00,0x00,0x90,0x90,0x50,0x50,0x20,0x20,0x00,0x00,

        5, // 0x77 'w'
        0x00,0x00,0x00,0x90,0x90,0x90,0x90,0xF0,0x90,0x00,0x00,

        5, // 0x78 'x'
        0x00,0x00,0x00,0x90,0x90,0x60,0x60,0x90,0x90,0x00,0x00,

        5, // 0x79 'y'
        0x00,0x00,0x00,0x90,0x90,0x90,0x90,0x90,0x70,0x10,0xE0,

        5, // 0x7A 'z'
        0x00,0x00,0x00,0xF0,0x10,0x20,0x40,0x80,0xF0,0x00,0x00,

        5, // 0x7B '{'
        0x30,0x40,0x40,0x40,0x40,0x80,0x40,0x40,0x40,0x40,0x30,

        5, // 0x7C '|'
        0x00,0x20,0x20,0x20,0x20,0x00,0x20,0x20,0x20,0x20,0x00,

        5, // 0x7D '}'
        0xC0,0x20,0x20,0x20,0x20,0x10,0x20,0x20,0x20,0x20,0xC0,

        5, // 0x7E '~'
        0x00,0x40,0xA8,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x7F ''
        0x00,0x20,0x70,0xD8,0x88,0x88,0xF8,0x00,0x00,0x00,0x00,

        0
    };

    const int8u mcs6x10_mono[] = 
    {
        10, 3, 32, 128-32,
        0x00,0x00,0x0B,0x00,0x16,0x00,0x21,0x00,0x2C,0x00,0x37,0x00,0x42,0x00,0x4D,0x00,0x58,0x00,
        0x63,0x00,0x6E,0x00,0x79,0x00,0x84,0x00,0x8F,0x00,0x9A,0x00,0xA5,0x00,0xB0,0x00,0xBB,0x00,
        0xC6,0x00,0xD1,0x00,0xDC,0x00,0xE7,0x00,0xF2,0x00,0xFD,0x00,0x08,0x01,0x13,0x01,0x1E,0x01,
        0x29,0x01,0x34,0x01,0x3F,0x01,0x4A,0x01,0x55,0x01,0x60,0x01,0x6B,0x01,0x76,0x01,0x81,0x01,
        0x8C,0x01,0x97,0x01,0xA2,0x01,0xAD,0x01,0xB8,0x01,0xC3,0x01,0xCE,0x01,0xD9,0x01,0xE4,0x01,
        0xEF,0x01,0xFA,0x01,0x05,0x02,0x10,0x02,0x1B,0x02,0x26,0x02,0x31,0x02,0x3C,0x02,0x47,0x02,
        0x52,0x02,0x5D,0x02,0x68,0x02,0x73,0x02,0x7E,0x02,0x89,0x02,0x94,0x02,0x9F,0x02,0xAA,0x02,
        0xB5,0x02,0xC0,0x02,0xCB,0x02,0xD6,0x02,0xE1,0x02,0xEC,0x02,0xF7,0x02,0x02,0x03,0x0D,0x03,
        0x18,0x03,0x23,0x03,0x2E,0x03,0x39,0x03,0x44,0x03,0x4F,0x03,0x5A,0x03,0x65,0x03,0x70,0x03,
        0x7B,0x03,0x86,0x03,0x91,0x03,0x9C,0x03,0xA7,0x03,0xB2,0x03,0xBD,0x03,0xC8,0x03,0xD3,0x03,
        0xDE,0x03,0xE9,0x03,0xF4,0x03,0xFF,0x03,0x0A,0x04,0x15,0x04,

        6, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x10,0x10,0x10,0x10,0x10,0x00,0x10,0x00,0x00,

        6, // 0x22 '"'
        0x00,0x28,0x28,0x50,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x23 '#'
        0x00,0x28,0x28,0x7C,0x28,0x28,0x7C,0x28,0x28,0x00,

        6, // 0x24 '$'
        0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x38,0x10,0x00,

        6, // 0x25 '%'
        0x00,0x08,0xC8,0xD0,0x10,0x20,0x2C,0x4C,0x40,0x00,

        6, // 0x26 '&'
        0x00,0x20,0x50,0x50,0x24,0x54,0x48,0x34,0x00,0x00,

        6, // 0x27 '''
        0x00,0x10,0x10,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x28 '('
        0x08,0x10,0x10,0x20,0x20,0x20,0x10,0x10,0x08,0x00,

        6, // 0x29 ')'
        0x20,0x10,0x10,0x08,0x08,0x08,0x10,0x10,0x20,0x00,

        6, // 0x2A '*'
        0x00,0x00,0x28,0x7C,0x38,0x7C,0x28,0x00,0x00,0x00,

        6, // 0x2B '+'
        0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,

        6, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x20,0x40,

        6, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        6, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,

        6, // 0x2F '/'
        0x00,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,

        6, // 0x30 '0'
        0x00,0x38,0x44,0x4C,0x54,0x64,0x44,0x38,0x00,0x00,

        6, // 0x31 '1'
        0x00,0x10,0x30,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        6, // 0x32 '2'
        0x00,0x38,0x44,0x04,0x18,0x20,0x40,0x7C,0x00,0x00,

        6, // 0x33 '3'
        0x00,0x38,0x44,0x04,0x38,0x04,0x44,0x38,0x00,0x00,

        6, // 0x34 '4'
        0x00,0x08,0x18,0x28,0x48,0x7C,0x08,0x08,0x00,0x00,

        6, // 0x35 '5'
        0x00,0x7C,0x40,0x40,0x78,0x04,0x44,0x38,0x00,0x00,

        6, // 0x36 '6'
        0x00,0x38,0x40,0x40,0x78,0x44,0x44,0x38,0x00,0x00,

        6, // 0x37 '7'
        0x00,0x7C,0x04,0x08,0x10,0x20,0x20,0x20,0x00,0x00,

        6, // 0x38 '8'
        0x00,0x38,0x44,0x44,0x38,0x44,0x44,0x38,0x00,0x00,

        6, // 0x39 '9'
        0x00,0x38,0x44,0x44,0x3C,0x04,0x04,0x38,0x00,0x00,

        6, // 0x3A ':'
        0x00,0x00,0x30,0x30,0x00,0x00,0x30,0x30,0x00,0x00,

        6, // 0x3B ';'
        0x00,0x00,0x30,0x30,0x00,0x00,0x30,0x30,0x20,0x40,

        6, // 0x3C '<'
        0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x00,

        6, // 0x3D '='
        0x00,0x00,0x00,0x7C,0x00,0x00,0x7C,0x00,0x00,0x00,

        6, // 0x3E '>'
        0x40,0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x40,0x00,

        6, // 0x3F '?'
        0x00,0x38,0x44,0x04,0x18,0x10,0x00,0x10,0x00,0x00,

        6, // 0x40 '@'
        0x00,0x38,0x44,0x5C,0x54,0x5C,0x40,0x38,0x00,0x00,

        6, // 0x41 'A'
        0x00,0x38,0x44,0x44,0x44,0x7C,0x44,0x44,0x00,0x00,

        6, // 0x42 'B'
        0x00,0x78,0x44,0x44,0x78,0x44,0x44,0x78,0x00,0x00,

        6, // 0x43 'C'
        0x00,0x38,0x44,0x40,0x40,0x40,0x44,0x38,0x00,0x00,

        6, // 0x44 'D'
        0x00,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x00,0x00,

        6, // 0x45 'E'
        0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x7C,0x00,0x00,

        6, // 0x46 'F'
        0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x40,0x00,0x00,

        6, // 0x47 'G'
        0x00,0x38,0x44,0x40,0x4C,0x44,0x44,0x3C,0x00,0x00,

        6, // 0x48 'H'
        0x00,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x00,0x00,

        6, // 0x49 'I'
        0x00,0x38,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        6, // 0x4A 'J'
        0x00,0x1C,0x08,0x08,0x08,0x48,0x48,0x30,0x00,0x00,

        6, // 0x4B 'K'
        0x00,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00,0x00,

        6, // 0x4C 'L'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,0x00,

        6, // 0x4D 'M'
        0x00,0x44,0x6C,0x54,0x54,0x44,0x44,0x44,0x00,0x00,

        6, // 0x4E 'N'
        0x00,0x44,0x44,0x64,0x54,0x4C,0x44,0x44,0x00,0x00,

        6, // 0x4F 'O'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x50 'P'
        0x00,0x78,0x44,0x44,0x78,0x40,0x40,0x40,0x00,0x00,

        6, // 0x51 'Q'
        0x00,0x38,0x44,0x44,0x44,0x54,0x48,0x34,0x00,0x00,

        6, // 0x52 'R'
        0x00,0x78,0x44,0x44,0x78,0x48,0x44,0x44,0x00,0x00,

        6, // 0x53 'S'
        0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x38,0x00,0x00,

        6, // 0x54 'T'
        0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        6, // 0x55 'U'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x56 'V'
        0x00,0x44,0x44,0x44,0x44,0x28,0x28,0x10,0x00,0x00,

        6, // 0x57 'W'
        0x00,0x44,0x44,0x54,0x54,0x54,0x54,0x28,0x00,0x00,

        6, // 0x58 'X'
        0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,

        6, // 0x59 'Y'
        0x00,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x00,0x00,

        6, // 0x5A 'Z'
        0x00,0x78,0x08,0x10,0x20,0x40,0x40,0x78,0x00,0x00,

        6, // 0x5B '['
        0x38,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x38,0x00,

        6, // 0x5C '\'
        0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x00,

        6, // 0x5D ']'
        0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x38,0x00,

        6, // 0x5E '^'
        0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,

        6, // 0x60 '`'
        0x00,0x10,0x10,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x61 'a'
        0x00,0x00,0x00,0x38,0x04,0x3C,0x44,0x3C,0x00,0x00,

        6, // 0x62 'b'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x78,0x00,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x3C,0x00,0x00,

        6, // 0x64 'd'
        0x00,0x04,0x04,0x3C,0x44,0x44,0x44,0x3C,0x00,0x00,

        6, // 0x65 'e'
        0x00,0x00,0x00,0x38,0x44,0x78,0x40,0x3C,0x00,0x00,

        6, // 0x66 'f'
        0x00,0x0C,0x10,0x10,0x38,0x10,0x10,0x10,0x00,0x00,

        6, // 0x67 'g'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x3C,0x04,0x38,

        6, // 0x68 'h'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x00,0x00,

        6, // 0x69 'i'
        0x00,0x10,0x00,0x30,0x10,0x10,0x10,0x38,0x00,0x00,

        6, // 0x6A 'j'
        0x00,0x08,0x00,0x18,0x08,0x08,0x08,0x08,0x48,0x30,

        6, // 0x6B 'k'
        0x00,0x40,0x40,0x48,0x50,0x60,0x50,0x48,0x00,0x00,

        6, // 0x6C 'l'
        0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        6, // 0x6D 'm'
        0x00,0x00,0x00,0x68,0x54,0x54,0x44,0x44,0x00,0x00,

        6, // 0x6E 'n'
        0x00,0x00,0x00,0x58,0x64,0x44,0x44,0x44,0x00,0x00,

        6, // 0x6F 'o'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x70 'p'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x78,0x40,0x40,

        6, // 0x71 'q'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x3C,0x04,0x04,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x58,0x24,0x20,0x20,0x70,0x00,0x00,

        6, // 0x73 's'
        0x00,0x00,0x00,0x38,0x40,0x38,0x04,0x78,0x00,0x00,

        6, // 0x74 't'
        0x00,0x10,0x10,0x38,0x10,0x10,0x14,0x08,0x00,0x00,

        6, // 0x75 'u'
        0x00,0x00,0x00,0x44,0x44,0x44,0x4C,0x34,0x00,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x44,0x44,0x44,0x28,0x10,0x00,0x00,

        6, // 0x77 'w'
        0x00,0x00,0x00,0x44,0x44,0x54,0x7C,0x28,0x00,0x00,

        6, // 0x78 'x'
        0x00,0x00,0x00,0x48,0x48,0x30,0x48,0x48,0x00,0x00,

        6, // 0x79 'y'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x3C,0x04,0x38,

        6, // 0x7A 'z'
        0x00,0x00,0x00,0x78,0x08,0x30,0x40,0x78,0x00,0x00,

        6, // 0x7B '{'
        0x18,0x20,0x20,0x20,0xC0,0x20,0x20,0x20,0x18,0x00,

        6, // 0x7C '|'
        0x10,0x10,0x10,0x10,0x00,0x10,0x10,0x10,0x10,0x00,

        6, // 0x7D '}'
        0x60,0x10,0x10,0x10,0x0C,0x10,0x10,0x10,0x60,0x00,

        6, // 0x7E '~'
        0x00,0x48,0xA8,0x90,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x7F ''
        0x00,0x10,0x38,0x6C,0x44,0x44,0x7C,0x00,0x00,0x00,

        0
    };

    const int8u mcs6x11_mono[] = 
    {
        11, 3, 32, 128-32,
        0x00,0x00,0x0C,0x00,0x18,0x00,0x24,0x00,0x30,0x00,0x3C,0x00,0x48,0x00,0x54,0x00,0x60,0x00,
        0x6C,0x00,0x78,0x00,0x84,0x00,0x90,0x00,0x9C,0x00,0xA8,0x00,0xB4,0x00,0xC0,0x00,0xCC,0x00,
        0xD8,0x00,0xE4,0x00,0xF0,0x00,0xFC,0x00,0x08,0x01,0x14,0x01,0x20,0x01,0x2C,0x01,0x38,0x01,
        0x44,0x01,0x50,0x01,0x5C,0x01,0x68,0x01,0x74,0x01,0x80,0x01,0x8C,0x01,0x98,0x01,0xA4,0x01,
        0xB0,0x01,0xBC,0x01,0xC8,0x01,0xD4,0x01,0xE0,0x01,0xEC,0x01,0xF8,0x01,0x04,0x02,0x10,0x02,
        0x1C,0x02,0x28,0x02,0x34,0x02,0x40,0x02,0x4C,0x02,0x58,0x02,0x64,0x02,0x70,0x02,0x7C,0x02,
        0x88,0x02,0x94,0x02,0xA0,0x02,0xAC,0x02,0xB8,0x02,0xC4,0x02,0xD0,0x02,0xDC,0x02,0xE8,0x02,
        0xF4,0x02,0x00,0x03,0x0C,0x03,0x18,0x03,0x24,0x03,0x30,0x03,0x3C,0x03,0x48,0x03,0x54,0x03,
        0x60,0x03,0x6C,0x03,0x78,0x03,0x84,0x03,0x90,0x03,0x9C,0x03,0xA8,0x03,0xB4,0x03,0xC0,0x03,
        0xCC,0x03,0xD8,0x03,0xE4,0x03,0xF0,0x03,0xFC,0x03,0x08,0x04,0x14,0x04,0x20,0x04,0x2C,0x04,
        0x38,0x04,0x44,0x04,0x50,0x04,0x5C,0x04,0x68,0x04,0x74,0x04,

        6, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,0x00,

        6, // 0x22 '"'
        0x00,0x28,0x28,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x23 '#'
        0x00,0x28,0x28,0x7C,0x28,0x28,0x7C,0x28,0x28,0x00,0x00,

        6, // 0x24 '$'
        0x00,0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x38,0x10,0x00,

        6, // 0x25 '%'
        0x00,0x68,0xA8,0xD0,0x10,0x20,0x2C,0x54,0x58,0x00,0x00,

        6, // 0x26 '&'
        0x00,0x20,0x50,0x50,0x20,0x54,0x54,0x48,0x34,0x00,0x00,

        6, // 0x27 '''
        0x00,0x10,0x10,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x28 '('
        0x08,0x10,0x10,0x20,0x20,0x20,0x20,0x10,0x10,0x08,0x00,

        6, // 0x29 ')'
        0x20,0x10,0x10,0x08,0x08,0x08,0x08,0x10,0x10,0x20,0x00,

        6, // 0x2A '*'
        0x00,0x00,0x28,0x7C,0x38,0x7C,0x28,0x00,0x00,0x00,0x00,

        6, // 0x2B '+'
        0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,

        6, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x20,0x40,

        6, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        6, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,

        6, // 0x2F '/'
        0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,

        6, // 0x30 '0'
        0x00,0x38,0x44,0x44,0x54,0x54,0x44,0x44,0x38,0x00,0x00,

        6, // 0x31 '1'
        0x00,0x10,0x30,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        6, // 0x32 '2'
        0x00,0x38,0x44,0x44,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        6, // 0x33 '3'
        0x00,0x38,0x44,0x04,0x38,0x04,0x04,0x44,0x38,0x00,0x00,

        6, // 0x34 '4'
        0x00,0x08,0x18,0x28,0x28,0x48,0x7C,0x08,0x08,0x00,0x00,

        6, // 0x35 '5'
        0x00,0x7C,0x40,0x78,0x44,0x04,0x04,0x44,0x38,0x00,0x00,

        6, // 0x36 '6'
        0x00,0x38,0x44,0x40,0x78,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x37 '7'
        0x00,0x7C,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x00,0x00,

        6, // 0x38 '8'
        0x00,0x38,0x44,0x44,0x38,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x39 '9'
        0x00,0x38,0x44,0x44,0x3C,0x04,0x04,0x44,0x38,0x00,0x00,

        6, // 0x3A ':'
        0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x30,0x30,0x00,0x00,

        6, // 0x3B ';'
        0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x30,0x30,0x20,0x40,

        6, // 0x3C '<'
        0x00,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x00,

        6, // 0x3D '='
        0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x7C,0x00,0x00,0x00,

        6, // 0x3E '>'
        0x00,0x40,0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x40,0x00,

        6, // 0x3F '?'
        0x00,0x38,0x44,0x04,0x08,0x10,0x10,0x00,0x10,0x00,0x00,

        6, // 0x40 '@'
        0x00,0x38,0x44,0x5C,0x54,0x54,0x4C,0x40,0x38,0x00,0x00,

        6, // 0x41 'A'
        0x00,0x38,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x00,0x00,

        6, // 0x42 'B'
        0x00,0x78,0x44,0x44,0x78,0x44,0x44,0x44,0x78,0x00,0x00,

        6, // 0x43 'C'
        0x00,0x38,0x44,0x40,0x40,0x40,0x40,0x44,0x38,0x00,0x00,

        6, // 0x44 'D'
        0x00,0x70,0x48,0x44,0x44,0x44,0x44,0x48,0x70,0x00,0x00,

        6, // 0x45 'E'
        0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x40,0x7C,0x00,0x00,

        6, // 0x46 'F'
        0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x40,0x40,0x00,0x00,

        6, // 0x47 'G'
        0x00,0x38,0x44,0x40,0x40,0x5C,0x44,0x4C,0x34,0x00,0x00,

        6, // 0x48 'H'
        0x00,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,0x00,

        6, // 0x49 'I'
        0x00,0x38,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        6, // 0x4A 'J'
        0x00,0x1C,0x08,0x08,0x08,0x08,0x48,0x48,0x30,0x00,0x00,

        6, // 0x4B 'K'
        0x00,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x44,0x00,0x00,

        6, // 0x4C 'L'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,0x00,

        6, // 0x4D 'M'
        0x00,0x44,0x6C,0x54,0x54,0x54,0x44,0x44,0x44,0x00,0x00,

        6, // 0x4E 'N'
        0x00,0x44,0x64,0x64,0x54,0x54,0x4C,0x4C,0x44,0x00,0x00,

        6, // 0x4F 'O'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x50 'P'
        0x00,0x78,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x00,0x00,

        6, // 0x51 'Q'
        0x00,0x38,0x44,0x44,0x44,0x44,0x54,0x48,0x34,0x00,0x00,

        6, // 0x52 'R'
        0x00,0x78,0x44,0x44,0x44,0x78,0x48,0x44,0x44,0x00,0x00,

        6, // 0x53 'S'
        0x00,0x38,0x44,0x40,0x38,0x04,0x04,0x44,0x38,0x00,0x00,

        6, // 0x54 'T'
        0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        6, // 0x55 'U'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x56 'V'
        0x00,0x44,0x44,0x44,0x28,0x28,0x28,0x10,0x10,0x00,0x00,

        6, // 0x57 'W'
        0x00,0x44,0x44,0x54,0x54,0x54,0x54,0x54,0x28,0x00,0x00,

        6, // 0x58 'X'
        0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x00,0x00,

        6, // 0x59 'Y'
        0x00,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x10,0x00,0x00,

        6, // 0x5A 'Z'
        0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x40,0x7C,0x00,0x00,

        6, // 0x5B '['
        0x38,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x38,0x00,

        6, // 0x5C '\'
        0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x00,

        6, // 0x5D ']'
        0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x38,0x00,

        6, // 0x5E '^'
        0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,

        6, // 0x60 '`'
        0x00,0x20,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x61 'a'
        0x00,0x00,0x00,0x38,0x04,0x3C,0x44,0x44,0x3C,0x00,0x00,

        6, // 0x62 'b'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x78,0x00,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x44,0x38,0x00,0x00,

        6, // 0x64 'd'
        0x00,0x04,0x04,0x3C,0x44,0x44,0x44,0x44,0x3C,0x00,0x00,

        6, // 0x65 'e'
        0x00,0x00,0x00,0x38,0x44,0x7C,0x40,0x44,0x38,0x00,0x00,

        6, // 0x66 'f'
        0x00,0x0C,0x10,0x38,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        6, // 0x67 'g'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x3C,0x04,0x78,

        6, // 0x68 'h'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        6, // 0x69 'i'
        0x00,0x10,0x00,0x30,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        6, // 0x6A 'j'
        0x00,0x10,0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x50,0x20,

        6, // 0x6B 'k'
        0x00,0x40,0x40,0x4C,0x50,0x60,0x50,0x48,0x44,0x00,0x00,

        6, // 0x6C 'l'
        0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        6, // 0x6D 'm'
        0x00,0x00,0x00,0x68,0x54,0x54,0x54,0x44,0x44,0x00,0x00,

        6, // 0x6E 'n'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        6, // 0x6F 'o'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        6, // 0x70 'p'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,

        6, // 0x71 'q'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x3C,0x04,0x04,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x58,0x24,0x20,0x20,0x20,0x70,0x00,0x00,

        6, // 0x73 's'
        0x00,0x00,0x00,0x38,0x44,0x30,0x08,0x44,0x38,0x00,0x00,

        6, // 0x74 't'
        0x00,0x20,0x20,0x70,0x20,0x20,0x20,0x20,0x18,0x00,0x00,

        6, // 0x75 'u'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x4C,0x34,0x00,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,

        6, // 0x77 'w'
        0x00,0x00,0x00,0x44,0x44,0x44,0x54,0x7C,0x28,0x00,0x00,

        6, // 0x78 'x'
        0x00,0x00,0x00,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,

        6, // 0x79 'y'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x3C,0x08,0x70,

        6, // 0x7A 'z'
        0x00,0x00,0x00,0x7C,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        6, // 0x7B '{'
        0x18,0x20,0x20,0x20,0xC0,0xC0,0x20,0x20,0x20,0x18,0x00,

        6, // 0x7C '|'
        0x00,0x10,0x10,0x10,0x10,0x00,0x10,0x10,0x10,0x10,0x00,

        6, // 0x7D '}'
        0x60,0x10,0x10,0x10,0x0C,0x0C,0x10,0x10,0x10,0x60,0x00,

        6, // 0x7E '~'
        0x00,0x24,0x54,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x7F ''
        0x00,0x10,0x38,0x6C,0x44,0x44,0x7C,0x00,0x00,0x00,0x00,

        0
    };

    const int8u mcs7x12_mono_high[] = 
    {
        12, 3, 32, 128-32,
        0x00,0x00,0x0D,0x00,0x1A,0x00,0x27,0x00,0x34,0x00,0x41,0x00,0x4E,0x00,0x5B,0x00,0x68,0x00,
        0x75,0x00,0x82,0x00,0x8F,0x00,0x9C,0x00,0xA9,0x00,0xB6,0x00,0xC3,0x00,0xD0,0x00,0xDD,0x00,
        0xEA,0x00,0xF7,0x00,0x04,0x01,0x11,0x01,0x1E,0x01,0x2B,0x01,0x38,0x01,0x45,0x01,0x52,0x01,
        0x5F,0x01,0x6C,0x01,0x79,0x01,0x86,0x01,0x93,0x01,0xA0,0x01,0xAD,0x01,0xBA,0x01,0xC7,0x01,
        0xD4,0x01,0xE1,0x01,0xEE,0x01,0xFB,0x01,0x08,0x02,0x15,0x02,0x22,0x02,0x2F,0x02,0x3C,0x02,
        0x49,0x02,0x56,0x02,0x63,0x02,0x70,0x02,0x7D,0x02,0x8A,0x02,0x97,0x02,0xA4,0x02,0xB1,0x02,
        0xBE,0x02,0xCB,0x02,0xD8,0x02,0xE5,0x02,0xF2,0x02,0xFF,0x02,0x0C,0x03,0x19,0x03,0x26,0x03,
        0x33,0x03,0x40,0x03,0x4D,0x03,0x5A,0x03,0x67,0x03,0x74,0x03,0x81,0x03,0x8E,0x03,0x9B,0x03,
        0xA8,0x03,0xB5,0x03,0xC2,0x03,0xCF,0x03,0xDC,0x03,0xE9,0x03,0xF6,0x03,0x03,0x04,0x10,0x04,
        0x1D,0x04,0x2A,0x04,0x37,0x04,0x44,0x04,0x51,0x04,0x5E,0x04,0x6B,0x04,0x78,0x04,0x85,0x04,
        0x92,0x04,0x9F,0x04,0xAC,0x04,0xB9,0x04,0xC6,0x04,0xD3,0x04,

        7, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x21 '!'
        0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x10,0x00,0x00,

        7, // 0x22 '"'
        0x28,0x28,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x23 '#'
        0x24,0x24,0x24,0x7E,0x24,0x24,0x24,0x7E,0x24,0x24,0x24,0x00,

        7, // 0x24 '$'
        0x10,0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x38,0x10,0x10,0x00,

        7, // 0x25 '%'
        0x32,0x54,0x64,0x08,0x08,0x10,0x10,0x26,0x2A,0x4C,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x20,0x50,0x50,0x50,0x20,0x54,0x54,0x48,0x34,0x00,0x00,

        7, // 0x27 '''
        0x10,0x10,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x08,0x10,0x10,0x20,0x20,0x20,0x20,0x20,0x10,0x10,0x08,0x00,

        7, // 0x29 ')'
        0x20,0x10,0x10,0x08,0x08,0x08,0x08,0x08,0x10,0x10,0x20,0x00,

        7, // 0x2A '*'
        0x00,0x00,0x10,0x54,0x38,0x7C,0x38,0x54,0x10,0x00,0x00,0x00,

        7, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,

        7, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x20,0x40,

        7, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,

        7, // 0x2F '/'
        0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,0x00,

        7, // 0x30 '0'
        0x00,0x38,0x44,0x44,0x54,0x54,0x54,0x44,0x44,0x38,0x00,0x00,

        7, // 0x31 '1'
        0x00,0x10,0x30,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x38,0x44,0x04,0x04,0x38,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x34 '4'
        0x00,0x08,0x18,0x28,0x28,0x48,0x48,0x7C,0x08,0x08,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x7C,0x40,0x40,0x78,0x44,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x38,0x44,0x40,0x78,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x37 '7'
        0x00,0x7C,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x20,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x38,0x44,0x44,0x44,0x38,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x38,0x44,0x44,0x44,0x3C,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x3A ':'
        0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x30,0x30,0x00,0x00,

        7, // 0x3B ';'
        0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x30,0x30,0x20,0x40,

        7, // 0x3C '<'
        0x00,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x00,0x00,

        7, // 0x3D '='
        0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,

        7, // 0x3E '>'
        0x00,0x40,0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x40,0x00,0x00,

        7, // 0x3F '?'
        0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x10,0x00,0x10,0x00,0x00,

        7, // 0x40 '@'
        0x00,0x38,0x44,0x44,0x5C,0x54,0x54,0x4C,0x40,0x38,0x00,0x00,

        7, // 0x41 'A'
        0x00,0x38,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x78,0x44,0x44,0x44,0x78,0x44,0x44,0x44,0x78,0x00,0x00,

        7, // 0x43 'C'
        0x00,0x38,0x44,0x40,0x40,0x40,0x40,0x40,0x44,0x38,0x00,0x00,

        7, // 0x44 'D'
        0x00,0x70,0x48,0x44,0x44,0x44,0x44,0x44,0x48,0x70,0x00,0x00,

        7, // 0x45 'E'
        0x00,0x7C,0x40,0x40,0x40,0x78,0x40,0x40,0x40,0x7C,0x00,0x00,

        7, // 0x46 'F'
        0x00,0x7C,0x40,0x40,0x40,0x78,0x40,0x40,0x40,0x40,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x38,0x44,0x40,0x40,0x5C,0x44,0x44,0x4C,0x34,0x00,0x00,

        7, // 0x48 'H'
        0x00,0x44,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x49 'I'
        0x00,0x38,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x4A 'J'
        0x00,0x1C,0x08,0x08,0x08,0x08,0x08,0x48,0x48,0x30,0x00,0x00,

        7, // 0x4B 'K'
        0x00,0x44,0x48,0x50,0x60,0x60,0x50,0x48,0x44,0x44,0x00,0x00,

        7, // 0x4C 'L'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,0x00,

        7, // 0x4D 'M'
        0x00,0x44,0x6C,0x6C,0x54,0x54,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x4E 'N'
        0x00,0x44,0x64,0x64,0x54,0x54,0x4C,0x4C,0x44,0x44,0x00,0x00,

        7, // 0x4F 'O'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x00,0x00,

        7, // 0x51 'Q'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x54,0x48,0x34,0x00,0x00,

        7, // 0x52 'R'
        0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x48,0x44,0x44,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x38,0x44,0x40,0x40,0x38,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x54 'T'
        0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        7, // 0x55 'U'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x56 'V'
        0x00,0x44,0x44,0x44,0x44,0x28,0x28,0x28,0x10,0x10,0x00,0x00,

        7, // 0x57 'W'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x54,0x54,0x28,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x44,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x44,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x10,0x00,0x00,

        7, // 0x5A 'Z'
        0x00,0x7C,0x04,0x04,0x08,0x10,0x20,0x40,0x40,0x7C,0x00,0x00,

        7, // 0x5B '['
        0x38,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x38,0x00,

        7, // 0x5C '\'
        0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x00,

        7, // 0x5D ']'
        0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x38,0x00,

        7, // 0x5E '^'
        0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,

        7, // 0x60 '`'
        0x00,0x20,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x38,0x04,0x3C,0x44,0x44,0x44,0x3C,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x40,0x44,0x38,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x04,0x04,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x38,0x44,0x44,0x7C,0x40,0x44,0x38,0x00,0x00,

        7, // 0x66 'f'
        0x00,0x0C,0x10,0x38,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x04,0x78,

        7, // 0x68 'h'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x69 'i'
        0x00,0x10,0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x6A 'j'
        0x00,0x08,0x00,0x18,0x08,0x08,0x08,0x08,0x08,0x08,0x48,0x30,

        7, // 0x6B 'k'
        0x00,0x40,0x40,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00,0x00,

        7, // 0x6C 'l'
        0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x6D 'm'
        0x00,0x00,0x00,0x68,0x54,0x54,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x6E 'n'
        0x00,0x00,0x00,0x58,0x64,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x6F 'o'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x44,0x78,0x40,0x40,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x44,0x3C,0x04,0x04,

        7, // 0x72 'r'
        0x00,0x00,0x00,0x58,0x24,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x38,0x00,0x00,

        7, // 0x74 't'
        0x00,0x20,0x20,0x70,0x20,0x20,0x20,0x20,0x24,0x18,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x4C,0x34,0x00,0x00,

        7, // 0x76 'v'
        0x00,0x00,0x00,0x44,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,

        7, // 0x77 'w'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x54,0x54,0x28,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x3C,0x08,0x70,

        7, // 0x7A 'z'
        0x00,0x00,0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        7, // 0x7B '{'
        0x18,0x20,0x20,0x20,0x20,0xC0,0x20,0x20,0x20,0x20,0x18,0x00,

        7, // 0x7C '|'
        0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,

        7, // 0x7D '}'
        0x60,0x10,0x10,0x10,0x10,0x0C,0x10,0x10,0x10,0x10,0x60,0x00,

        7, // 0x7E '~'
        0x00,0x60,0x92,0x92,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x7F ''
        0x00,0x10,0x38,0x6C,0x44,0x44,0x7C,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u mcs7x12_mono_low[] = 
    {
        12, 4, 32, 128-32,
        0x00,0x00,0x0D,0x00,0x1A,0x00,0x27,0x00,0x34,0x00,0x41,0x00,0x4E,0x00,0x5B,0x00,0x68,0x00,
        0x75,0x00,0x82,0x00,0x8F,0x00,0x9C,0x00,0xA9,0x00,0xB6,0x00,0xC3,0x00,0xD0,0x00,0xDD,0x00,
        0xEA,0x00,0xF7,0x00,0x04,0x01,0x11,0x01,0x1E,0x01,0x2B,0x01,0x38,0x01,0x45,0x01,0x52,0x01,
        0x5F,0x01,0x6C,0x01,0x79,0x01,0x86,0x01,0x93,0x01,0xA0,0x01,0xAD,0x01,0xBA,0x01,0xC7,0x01,
        0xD4,0x01,0xE1,0x01,0xEE,0x01,0xFB,0x01,0x08,0x02,0x15,0x02,0x22,0x02,0x2F,0x02,0x3C,0x02,
        0x49,0x02,0x56,0x02,0x63,0x02,0x70,0x02,0x7D,0x02,0x8A,0x02,0x97,0x02,0xA4,0x02,0xB1,0x02,
        0xBE,0x02,0xCB,0x02,0xD8,0x02,0xE5,0x02,0xF2,0x02,0xFF,0x02,0x0C,0x03,0x19,0x03,0x26,0x03,
        0x33,0x03,0x40,0x03,0x4D,0x03,0x5A,0x03,0x67,0x03,0x74,0x03,0x81,0x03,0x8E,0x03,0x9B,0x03,
        0xA8,0x03,0xB5,0x03,0xC2,0x03,0xCF,0x03,0xDC,0x03,0xE9,0x03,0xF6,0x03,0x03,0x04,0x10,0x04,
        0x1D,0x04,0x2A,0x04,0x37,0x04,0x44,0x04,0x51,0x04,0x5E,0x04,0x6B,0x04,0x78,0x04,0x85,0x04,
        0x92,0x04,0x9F,0x04,0xAC,0x04,0xB9,0x04,0xC6,0x04,0xD3,0x04,

        7, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x21 '!'
        0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x10,0x00,0x00,

        7, // 0x22 '"'
        0x28,0x28,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x23 '#'
        0x00,0x28,0x28,0x7C,0x28,0x28,0x28,0x7C,0x28,0x28,0x00,0x00,

        7, // 0x24 '$'
        0x00,0x10,0x38,0x54,0x50,0x38,0x14,0x54,0x38,0x10,0x00,0x00,

        7, // 0x25 '%'
        0x34,0x54,0x68,0x08,0x10,0x10,0x20,0x2C,0x54,0x58,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x20,0x50,0x50,0x20,0x54,0x54,0x48,0x34,0x00,0x00,0x00,

        7, // 0x27 '''
        0x00,0x10,0x10,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x08,0x10,0x10,0x20,0x20,0x20,0x20,0x20,0x10,0x10,0x08,0x00,

        7, // 0x29 ')'
        0x20,0x10,0x10,0x08,0x08,0x08,0x08,0x08,0x10,0x10,0x20,0x00,

        7, // 0x2A '*'
        0x00,0x00,0x10,0x54,0x38,0x7C,0x38,0x54,0x10,0x00,0x00,0x00,

        7, // 0x2B '+'
        0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,0x00,

        7, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x20,0x40,0x00,

        7, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,

        7, // 0x2F '/'
        0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x00,0x00,

        7, // 0x30 '0'
        0x00,0x38,0x44,0x44,0x54,0x54,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x31 '1'
        0x00,0x10,0x30,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x38,0x44,0x44,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x38,0x44,0x04,0x38,0x04,0x04,0x44,0x38,0x00,0x00,0x00,

        7, // 0x34 '4'
        0x00,0x08,0x18,0x28,0x28,0x48,0x7C,0x08,0x08,0x00,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x7C,0x40,0x78,0x44,0x04,0x04,0x44,0x38,0x00,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x38,0x44,0x40,0x78,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x37 '7'
        0x00,0x7C,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x00,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x38,0x44,0x44,0x38,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x38,0x44,0x44,0x44,0x3C,0x04,0x44,0x38,0x00,0x00,0x00,

        7, // 0x3A ':'
        0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x30,0x30,0x00,0x00,0x00,

        7, // 0x3B ';'
        0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x30,0x30,0x20,0x40,0x00,

        7, // 0x3C '<'
        0x00,0x04,0x08,0x10,0x20,0x40,0x20,0x10,0x08,0x04,0x00,0x00,

        7, // 0x3D '='
        0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,

        7, // 0x3E '>'
        0x00,0x40,0x20,0x10,0x08,0x04,0x08,0x10,0x20,0x40,0x00,0x00,

        7, // 0x3F '?'
        0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x10,0x00,0x10,0x00,0x00,

        7, // 0x40 '@'
        0x00,0x38,0x44,0x44,0x5C,0x54,0x4C,0x40,0x38,0x00,0x00,0x00,

        7, // 0x41 'A'
        0x00,0x38,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x78,0x44,0x44,0x78,0x44,0x44,0x44,0x78,0x00,0x00,0x00,

        7, // 0x43 'C'
        0x00,0x38,0x44,0x40,0x40,0x40,0x40,0x44,0x38,0x00,0x00,0x00,

        7, // 0x44 'D'
        0x00,0x70,0x48,0x44,0x44,0x44,0x44,0x48,0x70,0x00,0x00,0x00,

        7, // 0x45 'E'
        0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x40,0x7C,0x00,0x00,0x00,

        7, // 0x46 'F'
        0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x38,0x44,0x40,0x40,0x4C,0x44,0x4C,0x34,0x00,0x00,0x00,

        7, // 0x48 'H'
        0x00,0x44,0x44,0x44,0x7C,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x49 'I'
        0x00,0x38,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,

        7, // 0x4A 'J'
        0x00,0x1C,0x08,0x08,0x08,0x08,0x48,0x48,0x30,0x00,0x00,0x00,

        7, // 0x4B 'K'
        0x00,0x44,0x48,0x50,0x60,0x60,0x50,0x48,0x44,0x00,0x00,0x00,

        7, // 0x4C 'L'
        0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,0x00,0x00,

        7, // 0x4D 'M'
        0x00,0x44,0x6C,0x54,0x54,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x4E 'N'
        0x00,0x44,0x64,0x64,0x54,0x54,0x4C,0x4C,0x44,0x00,0x00,0x00,

        7, // 0x4F 'O'
        0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x78,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x51 'Q'
        0x00,0x38,0x44,0x44,0x44,0x44,0x54,0x48,0x34,0x00,0x00,0x00,

        7, // 0x52 'R'
        0x00,0x78,0x44,0x44,0x44,0x78,0x48,0x44,0x44,0x00,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x54 'T'
        0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,0x00,

        7, // 0x55 'U'
        0x00,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x56 'V'
        0x00,0x44,0x44,0x44,0x28,0x28,0x28,0x10,0x10,0x00,0x00,0x00,

        7, // 0x57 'W'
        0x00,0x44,0x44,0x44,0x44,0x44,0x54,0x54,0x28,0x00,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x44,0x44,0x44,0x28,0x10,0x10,0x10,0x10,0x00,0x00,0x00,

        7, // 0x5A 'Z'
        0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x40,0x7C,0x00,0x00,0x00,

        7, // 0x5B '['
        0x38,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x38,0x00,

        7, // 0x5C '\'
        0x00,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x00,

        7, // 0x5D ']'
        0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x38,0x00,

        7, // 0x5E '^'
        0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,

        7, // 0x60 '`'
        0x00,0x20,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x38,0x04,0x3C,0x44,0x44,0x3C,0x00,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x78,0x00,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x44,0x38,0x00,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x04,0x04,0x3C,0x44,0x44,0x44,0x44,0x3C,0x00,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x38,0x44,0x7C,0x40,0x44,0x38,0x00,0x00,0x00,

        7, // 0x66 'f'
        0x00,0x0C,0x10,0x38,0x10,0x10,0x10,0x10,0x10,0x00,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x3C,0x04,0x44,0x38,

        7, // 0x68 'h'
        0x00,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x69 'i'
        0x00,0x10,0x00,0x30,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,

        7, // 0x6A 'j'
        0x00,0x08,0x00,0x18,0x08,0x08,0x08,0x08,0x08,0x48,0x48,0x30,

        7, // 0x6B 'k'
        0x00,0x40,0x40,0x4C,0x50,0x60,0x50,0x48,0x44,0x00,0x00,0x00,

        7, // 0x6C 'l'
        0x00,0x30,0x10,0x10,0x10,0x10,0x10,0x10,0x38,0x00,0x00,0x00,

        7, // 0x6D 'm'
        0x00,0x00,0x00,0x68,0x54,0x54,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x6E 'n'
        0x00,0x00,0x00,0x58,0x64,0x44,0x44,0x44,0x44,0x00,0x00,0x00,

        7, // 0x6F 'o'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x38,0x00,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,0x40,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x3C,0x04,0x04,0x04,

        7, // 0x72 'r'
        0x00,0x00,0x00,0x58,0x24,0x20,0x20,0x20,0x70,0x00,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x38,0x44,0x30,0x08,0x44,0x38,0x00,0x00,0x00,

        7, // 0x74 't'
        0x00,0x20,0x20,0x70,0x20,0x20,0x20,0x24,0x18,0x00,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x4C,0x34,0x00,0x00,0x00,

        7, // 0x76 'v'
        0x00,0x00,0x00,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,0x00,

        7, // 0x77 'w'
        0x00,0x00,0x00,0x44,0x44,0x44,0x54,0x54,0x28,0x00,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x3C,0x04,0x08,0x70,

        7, // 0x7A 'z'
        0x00,0x00,0x00,0x7C,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,0x00,

        7, // 0x7B '{'
        0x18,0x20,0x20,0x20,0x20,0xC0,0x20,0x20,0x20,0x20,0x18,0x00,

        7, // 0x7C '|'
        0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,

        7, // 0x7D '}'
        0x60,0x10,0x10,0x10,0x10,0x0C,0x10,0x10,0x10,0x10,0x60,0x00,

        7, // 0x7E '~'
        0x00,0x24,0x54,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x7F ''
        0x00,0x10,0x38,0x6C,0x44,0x44,0x7C,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana12[] = 
    {
        12, 3, 32, 128-32,
        0x00,0x00,0x0D,0x00,0x1A,0x00,0x27,0x00,0x34,0x00,0x41,0x00,0x5A,0x00,0x67,0x00,0x74,0x00,
        0x81,0x00,0x8E,0x00,0x9B,0x00,0xA8,0x00,0xB5,0x00,0xC2,0x00,0xCF,0x00,0xDC,0x00,0xE9,0x00,
        0xF6,0x00,0x03,0x01,0x10,0x01,0x1D,0x01,0x2A,0x01,0x37,0x01,0x44,0x01,0x51,0x01,0x5E,0x01,
        0x6B,0x01,0x78,0x01,0x85,0x01,0x92,0x01,0x9F,0x01,0xAC,0x01,0xC5,0x01,0xD2,0x01,0xDF,0x01,
        0xEC,0x01,0xF9,0x01,0x06,0x02,0x13,0x02,0x20,0x02,0x2D,0x02,0x3A,0x02,0x47,0x02,0x54,0x02,
        0x61,0x02,0x7A,0x02,0x87,0x02,0xA0,0x02,0xAD,0x02,0xC6,0x02,0xD3,0x02,0xE0,0x02,0xED,0x02,
        0xFA,0x02,0x07,0x03,0x20,0x03,0x2D,0x03,0x3A,0x03,0x47,0x03,0x54,0x03,0x61,0x03,0x6E,0x03,
        0x7B,0x03,0x88,0x03,0x95,0x03,0xA2,0x03,0xAF,0x03,0xBC,0x03,0xC9,0x03,0xD6,0x03,0xE3,0x03,
        0xF0,0x03,0xFD,0x03,0x0A,0x04,0x17,0x04,0x24,0x04,0x31,0x04,0x4A,0x04,0x57,0x04,0x64,0x04,
        0x71,0x04,0x7E,0x04,0x8B,0x04,0x98,0x04,0xA5,0x04,0xB2,0x04,0xBF,0x04,0xCC,0x04,0xD9,0x04,
        0xE6,0x04,0xF3,0x04,0x00,0x05,0x0D,0x05,0x1A,0x05,0x27,0x05,

        3, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x21 '!'
        0x00,0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,0x00,

        5, // 0x22 '"'
        0x00,0x00,0x50,0x50,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x28,0x7C,0x28,0x7C,0x28,0x00,0x00,0x00,

        7, // 0x24 '$'
        0x00,0x00,0x10,0x10,0x3C,0x50,0x30,0x18,0x14,0x78,0x10,0x10,

        11, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x31,0x00,0x4A,0x00,0x4A,0x00,0x35,0x80,0x0A,0x40,0x0A,0x40,0x11,0x80,0x00,0x00,0x00,0x00,

        7, // 0x26 '&'
        0x00,0x00,0x00,0x30,0x48,0x48,0x32,0x4A,0x44,0x3A,0x00,0x00,

        3, // 0x27 '''
        0x00,0x00,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x28 '('
        0x00,0x00,0x10,0x20,0x40,0x40,0x40,0x40,0x40,0x40,0x20,0x10,

        4, // 0x29 ')'
        0x00,0x00,0x80,0x40,0x20,0x20,0x20,0x20,0x20,0x20,0x40,0x80,

        7, // 0x2A '*'
        0x00,0x10,0x54,0x38,0x54,0x10,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,

        3, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x80,0x00,

        5, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0x00,0x00,0x00,0x00,

        3, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x00,0x00,

        4, // 0x2F '/'
        0x00,0x00,0x10,0x10,0x20,0x20,0x40,0x40,0x40,0x80,0x80,0x00,

        7, // 0x30 '0'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x31 '1'
        0x00,0x00,0x00,0x10,0x30,0x10,0x10,0x10,0x10,0x38,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x00,0x00,0x38,0x44,0x04,0x08,0x10,0x20,0x7C,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x00,0x00,0x38,0x44,0x04,0x18,0x04,0x44,0x38,0x00,0x00,

        7, // 0x34 '4'
        0x00,0x00,0x00,0x08,0x18,0x28,0x48,0x7C,0x08,0x08,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x00,0x00,0x7C,0x40,0x78,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x00,0x00,0x18,0x20,0x40,0x78,0x44,0x44,0x38,0x00,0x00,

        7, // 0x37 '7'
        0x00,0x00,0x00,0x7C,0x04,0x08,0x08,0x10,0x10,0x10,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x00,0x00,0x38,0x44,0x44,0x38,0x44,0x44,0x38,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x00,0x00,0x38,0x44,0x44,0x3C,0x04,0x08,0x30,0x00,0x00,

        4, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x00,0x40,0x40,0x00,0x00,

        4, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x00,0x40,0x40,0x80,0x00,

        7, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x04,0x18,0x60,0x18,0x04,0x00,0x00,0x00,

        7, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x7C,0x00,0x00,0x00,0x00,

        7, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x40,0x30,0x0C,0x30,0x40,0x00,0x00,0x00,

        6, // 0x3F '?'
        0x00,0x00,0x00,0x70,0x08,0x08,0x10,0x20,0x00,0x20,0x00,0x00,

        10, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x20,0x80,0x4E,0x80,0x52,0x80,0x52,0x80,0x4D,0x00,0x20,0x00,0x1F,0x00,0x00,0x00,

        8, // 0x41 'A'
        0x00,0x00,0x00,0x18,0x18,0x24,0x24,0x7E,0x42,0x42,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x00,0x00,0x70,0x48,0x48,0x78,0x44,0x44,0x78,0x00,0x00,

        8, // 0x43 'C'
        0x00,0x00,0x00,0x1C,0x22,0x40,0x40,0x40,0x22,0x1C,0x00,0x00,

        8, // 0x44 'D'
        0x00,0x00,0x00,0x78,0x44,0x42,0x42,0x42,0x44,0x78,0x00,0x00,

        7, // 0x45 'E'
        0x00,0x00,0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x7C,0x00,0x00,

        6, // 0x46 'F'
        0x00,0x00,0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x40,0x00,0x00,

        8, // 0x47 'G'
        0x00,0x00,0x00,0x1C,0x22,0x40,0x4E,0x42,0x22,0x1C,0x00,0x00,

        8, // 0x48 'H'
        0x00,0x00,0x00,0x42,0x42,0x42,0x7E,0x42,0x42,0x42,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x00,0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        5, // 0x4A 'J'
        0x00,0x00,0x00,0x30,0x10,0x10,0x10,0x10,0x10,0xE0,0x00,0x00,

        7, // 0x4B 'K'
        0x00,0x00,0x00,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00,0x00,

        6, // 0x4C 'L'
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,0x00,

        9, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x55,0x00,0x55,0x00,0x49,0x00,0x49,0x00,0x41,0x00,0x00,0x00,0x00,0x00,

        8, // 0x4E 'N'
        0x00,0x00,0x00,0x42,0x62,0x52,0x4A,0x46,0x42,0x42,0x00,0x00,

        9, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x22,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x78,0x40,0x40,0x00,0x00,

        9, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x22,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x04,0x00,0x03,0x00,

        7, // 0x52 'R'
        0x00,0x00,0x00,0x78,0x44,0x44,0x78,0x50,0x48,0x44,0x00,0x00,

        7, // 0x53 'S'
        0x00,0x00,0x00,0x38,0x44,0x40,0x38,0x04,0x44,0x38,0x00,0x00,

        7, // 0x54 'T'
        0x00,0x00,0x00,0xFE,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        8, // 0x55 'U'
        0x00,0x00,0x00,0x42,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,

        8, // 0x56 'V'
        0x00,0x00,0x00,0x42,0x42,0x42,0x24,0x24,0x18,0x18,0x00,0x00,

        9, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x49,0x00,0x49,0x00,0x55,0x00,0x55,0x00,0x22,0x00,0x22,0x00,0x00,0x00,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x00,0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x00,0x00,0x44,0x44,0x28,0x28,0x10,0x10,0x10,0x00,0x00,

        7, // 0x5A 'Z'
        0x00,0x00,0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        4, // 0x5B '['
        0x00,0x00,0x60,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x60,

        4, // 0x5C '\'
        0x00,0x00,0x80,0x80,0x40,0x40,0x40,0x20,0x20,0x10,0x10,0x00,

        4, // 0x5D ']'
        0x00,0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x60,

        7, // 0x5E '^'
        0x00,0x00,0x00,0x10,0x28,0x44,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,

        6, // 0x60 '`'
        0x00,0x00,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x30,0x08,0x38,0x48,0x38,0x00,0x00,

        6, // 0x62 'b'
        0x00,0x00,0x40,0x40,0x40,0x70,0x48,0x48,0x48,0x70,0x00,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x38,0x40,0x40,0x40,0x38,0x00,0x00,

        6, // 0x64 'd'
        0x00,0x00,0x08,0x08,0x08,0x38,0x48,0x48,0x48,0x38,0x00,0x00,

        6, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x30,0x48,0x78,0x40,0x38,0x00,0x00,

        4, // 0x66 'f'
        0x00,0x00,0x30,0x40,0x40,0xE0,0x40,0x40,0x40,0x40,0x00,0x00,

        6, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x38,0x48,0x48,0x48,0x38,0x08,0x30,

        6, // 0x68 'h'
        0x00,0x00,0x40,0x40,0x40,0x70,0x48,0x48,0x48,0x48,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x00,0x00,0x40,0x00,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        3, // 0x6A 'j'
        0x00,0x00,0x00,0x40,0x00,0xC0,0x40,0x40,0x40,0x40,0x40,0x80,

        6, // 0x6B 'k'
        0x00,0x00,0x40,0x40,0x40,0x48,0x50,0x60,0x50,0x48,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        9, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x76,0x00,0x49,0x00,0x49,0x00,0x49,0x00,0x49,0x00,0x00,0x00,0x00,0x00,

        6, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x70,0x48,0x48,0x48,0x48,0x00,0x00,

        6, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x30,0x48,0x48,0x48,0x30,0x00,0x00,

        6, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x70,0x48,0x48,0x48,0x70,0x40,0x40,

        6, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x38,0x48,0x48,0x48,0x38,0x08,0x08,

        4, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x50,0x60,0x40,0x40,0x40,0x00,0x00,

        6, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x38,0x40,0x30,0x08,0x70,0x00,0x00,

        4, // 0x74 't'
        0x00,0x00,0x00,0x00,0x40,0xF0,0x40,0x40,0x40,0x30,0x00,0x00,

        6, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x48,0x48,0x48,0x48,0x38,0x00,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x48,0x48,0x48,0x30,0x30,0x00,0x00,

        7, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x44,0x54,0x54,0x28,0x28,0x00,0x00,

        6, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x48,0x48,0x30,0x48,0x48,0x00,0x00,

        6, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x48,0x48,0x48,0x30,0x10,0x20,0x20,

        5, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x70,0x10,0x20,0x40,0x70,0x00,0x00,

        6, // 0x7B '{'
        0x00,0x00,0x18,0x20,0x20,0x20,0x20,0xC0,0x20,0x20,0x20,0x18,

        5, // 0x7C '|'
        0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,

        6, // 0x7D '}'
        0x00,0x00,0x60,0x10,0x10,0x10,0x10,0x0C,0x10,0x10,0x10,0x60,

        7, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x34,0x58,0x00,0x00,0x00,0x00,

        9, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana12_bold[] = 
    {
        12, 3, 32, 128-32,
        0x00,0x00,0x0D,0x00,0x1A,0x00,0x27,0x00,0x34,0x00,0x41,0x00,0x5A,0x00,0x67,0x00,0x74,0x00,
        0x81,0x00,0x8E,0x00,0x9B,0x00,0xA8,0x00,0xB5,0x00,0xC2,0x00,0xCF,0x00,0xDC,0x00,0xE9,0x00,
        0xF6,0x00,0x03,0x01,0x10,0x01,0x1D,0x01,0x2A,0x01,0x37,0x01,0x44,0x01,0x51,0x01,0x5E,0x01,
        0x6B,0x01,0x78,0x01,0x85,0x01,0x92,0x01,0x9F,0x01,0xAC,0x01,0xC5,0x01,0xD2,0x01,0xDF,0x01,
        0xEC,0x01,0xF9,0x01,0x06,0x02,0x13,0x02,0x20,0x02,0x2D,0x02,0x3A,0x02,0x47,0x02,0x54,0x02,
        0x61,0x02,0x6E,0x02,0x7B,0x02,0x88,0x02,0x95,0x02,0xA2,0x02,0xAF,0x02,0xBC,0x02,0xC9,0x02,
        0xD6,0x02,0xE3,0x02,0xFC,0x02,0x09,0x03,0x16,0x03,0x23,0x03,0x30,0x03,0x3D,0x03,0x4A,0x03,
        0x57,0x03,0x64,0x03,0x71,0x03,0x7E,0x03,0x8B,0x03,0x98,0x03,0xA5,0x03,0xB2,0x03,0xBF,0x03,
        0xCC,0x03,0xD9,0x03,0xE6,0x03,0xF3,0x03,0x00,0x04,0x0D,0x04,0x26,0x04,0x33,0x04,0x40,0x04,
        0x4D,0x04,0x5A,0x04,0x67,0x04,0x74,0x04,0x81,0x04,0x8E,0x04,0x9B,0x04,0xB4,0x04,0xC1,0x04,
        0xCE,0x04,0xDB,0x04,0xE8,0x04,0xF5,0x04,0x02,0x05,0x0F,0x05,

        3, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x21 '!'
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x00,0x60,0x00,0x00,

        5, // 0x22 '"'
        0x00,0x00,0xD8,0xD8,0xD8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x23 '#'
        0x00,0x00,0x00,0x14,0x14,0x7E,0x28,0xFC,0x50,0x50,0x00,0x00,

        6, // 0x24 '$'
        0x00,0x00,0x20,0x20,0x70,0xE8,0xE0,0x38,0xB8,0x70,0x20,0x20,

        11, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x62,0x00,0x94,0x00,0x94,0x00,0x69,0x80,0x0A,0x40,0x0A,0x40,0x11,0x80,0x00,0x00,0x00,0x00,

        8, // 0x26 '&'
        0x00,0x00,0x00,0x70,0xD8,0xD8,0x76,0xDC,0xCC,0x76,0x00,0x00,

        3, // 0x27 '''
        0x00,0x00,0xC0,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x00,0x00,0x30,0x60,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x60,0x30,

        5, // 0x29 ')'
        0x00,0x00,0xC0,0x60,0x30,0x30,0x30,0x30,0x30,0x30,0x60,0xC0,

        6, // 0x2A '*'
        0x00,0x00,0x20,0xA8,0x70,0xA8,0x20,0x00,0x00,0x00,0x00,0x00,

        8, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x10,0x10,0x7C,0x10,0x10,0x00,0x00,0x00,

        3, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xC0,0x80,0x00,

        4, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0x00,0x00,0x00,0x00,0x00,

        3, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xC0,0x00,0x00,

        6, // 0x2F '/'
        0x00,0x00,0x08,0x08,0x10,0x10,0x20,0x40,0x40,0x80,0x80,0x00,

        6, // 0x30 '0'
        0x00,0x00,0x00,0x70,0xD8,0xD8,0xD8,0xD8,0xD8,0x70,0x00,0x00,

        6, // 0x31 '1'
        0x00,0x00,0x00,0x30,0x70,0x30,0x30,0x30,0x30,0x78,0x00,0x00,

        6, // 0x32 '2'
        0x00,0x00,0x00,0x70,0x98,0x18,0x30,0x60,0xC0,0xF8,0x00,0x00,

        6, // 0x33 '3'
        0x00,0x00,0x00,0x70,0x98,0x18,0x70,0x18,0x98,0x70,0x00,0x00,

        6, // 0x34 '4'
        0x00,0x00,0x00,0x18,0x38,0x58,0x98,0xFC,0x18,0x18,0x00,0x00,

        6, // 0x35 '5'
        0x00,0x00,0x00,0xF8,0xC0,0xF0,0x18,0x18,0x98,0x70,0x00,0x00,

        6, // 0x36 '6'
        0x00,0x00,0x00,0x70,0xC0,0xF0,0xD8,0xD8,0xD8,0x70,0x00,0x00,

        6, // 0x37 '7'
        0x00,0x00,0x00,0xF8,0x18,0x30,0x30,0x60,0x60,0xC0,0x00,0x00,

        6, // 0x38 '8'
        0x00,0x00,0x00,0x70,0xD8,0xD8,0x70,0xD8,0xD8,0x70,0x00,0x00,

        6, // 0x39 '9'
        0x00,0x00,0x00,0x70,0xD8,0xD8,0xD8,0x78,0x18,0x70,0x00,0x00,

        4, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x60,0x60,0x00,0x00,

        4, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x60,0x60,0x40,0x00,

        8, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x04,0x18,0x60,0x60,0x18,0x04,0x00,0x00,

        8, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x7C,0x00,0x00,0x00,0x00,

        8, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x40,0x30,0x0C,0x0C,0x30,0x40,0x00,0x00,

        6, // 0x3F '?'
        0x00,0x00,0x00,0xF0,0x18,0x18,0x30,0x60,0x00,0x60,0x00,0x00,

        9, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x00,0x42,0x00,0x9D,0x00,0xA5,0x00,0xA5,0x00,0x9E,0x00,0x40,0x00,0x3C,0x00,0x00,0x00,

        8, // 0x41 'A'
        0x00,0x00,0x00,0x38,0x38,0x6C,0x6C,0x7C,0xC6,0xC6,0x00,0x00,

        7, // 0x42 'B'
        0x00,0x00,0x00,0xF8,0xCC,0xCC,0xF8,0xCC,0xCC,0xF8,0x00,0x00,

        6, // 0x43 'C'
        0x00,0x00,0x00,0x70,0xC8,0xC0,0xC0,0xC0,0xC8,0x70,0x00,0x00,

        7, // 0x44 'D'
        0x00,0x00,0x00,0xF8,0xCC,0xCC,0xCC,0xCC,0xCC,0xF8,0x00,0x00,

        6, // 0x45 'E'
        0x00,0x00,0x00,0xF8,0xC0,0xC0,0xF8,0xC0,0xC0,0xF8,0x00,0x00,

        6, // 0x46 'F'
        0x00,0x00,0x00,0xF8,0xC0,0xC0,0xF8,0xC0,0xC0,0xC0,0x00,0x00,

        7, // 0x47 'G'
        0x00,0x00,0x00,0x78,0xC4,0xC0,0xC0,0xDC,0xCC,0x7C,0x00,0x00,

        7, // 0x48 'H'
        0x00,0x00,0x00,0xCC,0xCC,0xCC,0xFC,0xCC,0xCC,0xCC,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x00,0x00,0xF0,0x60,0x60,0x60,0x60,0x60,0xF0,0x00,0x00,

        5, // 0x4A 'J'
        0x00,0x00,0x00,0x70,0x30,0x30,0x30,0x30,0x30,0xE0,0x00,0x00,

        7, // 0x4B 'K'
        0x00,0x00,0x00,0xCC,0xD8,0xF0,0xE0,0xF0,0xD8,0xCC,0x00,0x00,

        6, // 0x4C 'L'
        0x00,0x00,0x00,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xF8,0x00,0x00,

        8, // 0x4D 'M'
        0x00,0x00,0x00,0x82,0xC6,0xEE,0xB6,0xB6,0x86,0x86,0x00,0x00,

        7, // 0x4E 'N'
        0x00,0x00,0x00,0x84,0xC4,0xE4,0xB4,0x9C,0x8C,0x84,0x00,0x00,

        8, // 0x4F 'O'
        0x00,0x00,0x00,0x7C,0xC6,0xC6,0xC6,0xC6,0xC6,0x7C,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x00,0x00,0xF8,0xCC,0xCC,0xCC,0xF8,0xC0,0xC0,0x00,0x00,

        8, // 0x51 'Q'
        0x00,0x00,0x00,0x7C,0xC6,0xC6,0xC6,0xC6,0xC6,0x7C,0x18,0x0E,

        7, // 0x52 'R'
        0x00,0x00,0x00,0xF8,0xCC,0xCC,0xF8,0xD8,0xCC,0xC6,0x00,0x00,

        6, // 0x53 'S'
        0x00,0x00,0x00,0x70,0xC8,0xC0,0x70,0x18,0x98,0x70,0x00,0x00,

        6, // 0x54 'T'
        0x00,0x00,0x00,0xFC,0x30,0x30,0x30,0x30,0x30,0x30,0x00,0x00,

        7, // 0x55 'U'
        0x00,0x00,0x00,0xCC,0xCC,0xCC,0xCC,0xCC,0xCC,0x78,0x00,0x00,

        7, // 0x56 'V'
        0x00,0x00,0x00,0xCC,0xCC,0x78,0x78,0x78,0x30,0x30,0x00,0x00,

        11, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0xCC,0xC0,0xCC,0xC0,0x6D,0x80,0x6D,0x80,0x73,0x80,0x33,0x00,0x33,0x00,0x00,0x00,0x00,0x00,

        7, // 0x58 'X'
        0x00,0x00,0x00,0xCC,0xCC,0x78,0x30,0x78,0xCC,0xCC,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x00,0x00,0xCC,0xCC,0x78,0x30,0x30,0x30,0x30,0x00,0x00,

        6, // 0x5A 'Z'
        0x00,0x00,0x00,0xF8,0x18,0x30,0x60,0xC0,0xC0,0xF8,0x00,0x00,

        5, // 0x5B '['
        0x00,0x00,0x70,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x70,

        6, // 0x5C '\'
        0x00,0x00,0x80,0x80,0x40,0x40,0x20,0x10,0x10,0x08,0x08,0x00,

        5, // 0x5D ']'
        0x00,0x00,0x70,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x70,

        8, // 0x5E '^'
        0x00,0x00,0x00,0x18,0x3C,0x66,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,

        6, // 0x60 '`'
        0x00,0x00,0x30,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x70,0x18,0x78,0xD8,0x78,0x00,0x00,

        6, // 0x62 'b'
        0x00,0x00,0xC0,0xC0,0xC0,0xF0,0xD8,0xD8,0xD8,0xF0,0x00,0x00,

        5, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x70,0xC0,0xC0,0xC0,0x70,0x00,0x00,

        6, // 0x64 'd'
        0x00,0x00,0x18,0x18,0x18,0x78,0xD8,0xD8,0xD8,0x78,0x00,0x00,

        6, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x70,0xD8,0xF8,0xC0,0x78,0x00,0x00,

        5, // 0x66 'f'
        0x00,0x00,0x38,0x60,0x60,0xF8,0x60,0x60,0x60,0x60,0x00,0x00,

        6, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x78,0xD8,0xD8,0xD8,0x78,0x18,0x70,

        6, // 0x68 'h'
        0x00,0x00,0xC0,0xC0,0xC0,0xF0,0xD8,0xD8,0xD8,0xD8,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x00,0x00,0xC0,0x00,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,

        4, // 0x6A 'j'
        0x00,0x00,0x00,0x60,0x00,0xE0,0x60,0x60,0x60,0x60,0x60,0xC0,

        6, // 0x6B 'k'
        0x00,0x00,0xC0,0xC0,0xC0,0xD8,0xD8,0xF0,0xD8,0xD8,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x00,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,

        9, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF6,0x00,0xDB,0x00,0xDB,0x00,0xDB,0x00,0xDB,0x00,0x00,0x00,0x00,0x00,

        6, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0xF0,0xD8,0xD8,0xD8,0xD8,0x00,0x00,

        6, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x70,0xD8,0xD8,0xD8,0x70,0x00,0x00,

        6, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0xF0,0xD8,0xD8,0xD8,0xF0,0xC0,0xC0,

        6, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x78,0xD8,0xD8,0xD8,0x78,0x18,0x18,

        4, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0xD0,0xE0,0xC0,0xC0,0xC0,0x00,0x00,

        5, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x70,0xC0,0xF0,0x30,0xE0,0x00,0x00,

        5, // 0x74 't'
        0x00,0x00,0x00,0x60,0x60,0xF8,0x60,0x60,0x60,0x38,0x00,0x00,

        6, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0xD8,0xD8,0xD8,0xD8,0x78,0x00,0x00,

        6, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0xD8,0xD8,0xD8,0x70,0x70,0x00,0x00,

        9, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xDB,0x00,0xDB,0x00,0xDB,0x00,0x66,0x00,0x66,0x00,0x00,0x00,0x00,0x00,

        6, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0xD8,0xD8,0x70,0xD8,0xD8,0x00,0x00,

        6, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0xD8,0xD8,0xD8,0x70,0x70,0x30,0x60,

        5, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0xF0,0x30,0x60,0xC0,0xF0,0x00,0x00,

        6, // 0x7B '{'
        0x00,0x00,0x18,0x30,0x30,0x30,0xE0,0x30,0x30,0x30,0x30,0x18,

        5, // 0x7C '|'
        0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,

        6, // 0x7D '}'
        0x00,0x00,0xC0,0x60,0x60,0x60,0x38,0x60,0x60,0x60,0x60,0xC0,

        8, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x62,0x92,0x8C,0x00,0x00,0x00,

        9, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana13[] = 
    {
        13, 3, 32, 128-32,
        0x00,0x00,0x0E,0x00,0x1C,0x00,0x2A,0x00,0x45,0x00,0x53,0x00,0x6E,0x00,0x7C,0x00,0x8A,0x00,
        0x98,0x00,0xA6,0x00,0xB4,0x00,0xCF,0x00,0xDD,0x00,0xEB,0x00,0xF9,0x00,0x07,0x01,0x15,0x01,
        0x23,0x01,0x31,0x01,0x3F,0x01,0x4D,0x01,0x5B,0x01,0x69,0x01,0x77,0x01,0x85,0x01,0x93,0x01,
        0xA1,0x01,0xAF,0x01,0xCA,0x01,0xE5,0x01,0x00,0x02,0x0E,0x02,0x29,0x02,0x37,0x02,0x45,0x02,
        0x60,0x02,0x7B,0x02,0x89,0x02,0x97,0x02,0xB2,0x02,0xC0,0x02,0xCE,0x02,0xDC,0x02,0xEA,0x02,
        0xF8,0x02,0x13,0x03,0x21,0x03,0x3C,0x03,0x4A,0x03,0x65,0x03,0x73,0x03,0x81,0x03,0x8F,0x03,
        0x9D,0x03,0xAB,0x03,0xC6,0x03,0xD4,0x03,0xE2,0x03,0xF0,0x03,0xFE,0x03,0x0C,0x04,0x1A,0x04,
        0x35,0x04,0x43,0x04,0x51,0x04,0x5F,0x04,0x6D,0x04,0x7B,0x04,0x89,0x04,0x97,0x04,0xA5,0x04,
        0xB3,0x04,0xC1,0x04,0xCF,0x04,0xDD,0x04,0xEB,0x04,0xF9,0x04,0x14,0x05,0x22,0x05,0x30,0x05,
        0x3E,0x05,0x4C,0x05,0x5A,0x05,0x68,0x05,0x76,0x05,0x84,0x05,0x92,0x05,0xAD,0x05,0xBB,0x05,
        0xC9,0x05,0xD7,0x05,0xE5,0x05,0xF3,0x05,0x01,0x06,0x1C,0x06,

        4, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x21 '!'
        0x00,0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,0x00,

        5, // 0x22 '"'
        0x00,0x00,0x50,0x50,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x0A,0x00,0x0A,0x00,0x3F,0x00,0x14,0x00,0x14,0x00,0x7E,0x00,0x28,0x00,0x28,0x00,0x00,0x00,0x00,0x00,

        7, // 0x24 '$'
        0x00,0x00,0x10,0x10,0x3C,0x50,0x50,0x38,0x14,0x14,0x78,0x10,0x10,

        12, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x31,0x00,0x49,0x00,0x4A,0x00,0x32,0x00,0x04,0xC0,0x05,0x20,0x09,0x20,0x08,0xC0,0x00,0x00,0x00,0x00,

        8, // 0x26 '&'
        0x00,0x00,0x00,0x30,0x48,0x48,0x32,0x4A,0x44,0x46,0x39,0x00,0x00,

        3, // 0x27 '''
        0x00,0x00,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x00,0x00,0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,

        5, // 0x29 ')'
        0x00,0x00,0x40,0x20,0x20,0x10,0x10,0x10,0x10,0x10,0x20,0x20,0x40,

        7, // 0x2A '*'
        0x00,0x00,0x10,0x54,0x38,0x54,0x10,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x7F,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x40,

        5, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,

        5, // 0x2F '/'
        0x00,0x00,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x80,0x80,0x00,

        7, // 0x30 '0'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x31 '1'
        0x00,0x00,0x00,0x10,0x70,0x10,0x10,0x10,0x10,0x10,0x7C,0x00,0x00,

        7, // 0x32 '2'
        0x00,0x00,0x00,0x38,0x44,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        7, // 0x33 '3'
        0x00,0x00,0x00,0x38,0x44,0x04,0x18,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x34 '4'
        0x00,0x00,0x00,0x08,0x18,0x28,0x48,0x88,0xFC,0x08,0x08,0x00,0x00,

        7, // 0x35 '5'
        0x00,0x00,0x00,0x7C,0x40,0x40,0x78,0x04,0x04,0x44,0x38,0x00,0x00,

        7, // 0x36 '6'
        0x00,0x00,0x00,0x18,0x20,0x40,0x78,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x37 '7'
        0x00,0x00,0x00,0x7C,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x00,0x00,

        7, // 0x38 '8'
        0x00,0x00,0x00,0x38,0x44,0x44,0x38,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x39 '9'
        0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x3C,0x04,0x08,0x30,0x00,0x00,

        5, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x20,0x20,0x00,0x00,

        5, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x20,0x20,0x20,0x40,

        9, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x18,0x00,0x60,0x00,0x18,0x00,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x0C,0x00,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x3F '?'
        0x00,0x00,0x00,0x70,0x08,0x08,0x10,0x20,0x20,0x00,0x20,0x00,0x00,

        10, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x20,0x80,0x4E,0x80,0x52,0x80,0x52,0x80,0x52,0x80,0x4D,0x00,0x20,0x00,0x1E,0x00,0x00,0x00,

        8, // 0x41 'A'
        0x00,0x00,0x00,0x18,0x18,0x24,0x24,0x24,0x7E,0x42,0x42,0x00,0x00,

        8, // 0x42 'B'
        0x00,0x00,0x00,0x78,0x44,0x44,0x7C,0x42,0x42,0x42,0x7C,0x00,0x00,

        9, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x42,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x42,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        7, // 0x45 'E'
        0x00,0x00,0x00,0x7C,0x40,0x40,0x7C,0x40,0x40,0x40,0x7C,0x00,0x00,

        6, // 0x46 'F'
        0x00,0x00,0x00,0x7C,0x40,0x40,0x78,0x40,0x40,0x40,0x40,0x00,0x00,

        9, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x00,0x40,0x00,0x47,0x00,0x41,0x00,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,

        8, // 0x48 'H'
        0x00,0x00,0x00,0x42,0x42,0x42,0x7E,0x42,0x42,0x42,0x42,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x00,0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        5, // 0x4A 'J'
        0x00,0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0xE0,0x00,0x00,

        8, // 0x4B 'K'
        0x00,0x00,0x00,0x42,0x44,0x48,0x50,0x70,0x48,0x44,0x42,0x00,0x00,

        6, // 0x4C 'L'
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7C,0x00,0x00,

        9, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x55,0x00,0x55,0x00,0x49,0x00,0x49,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,

        8, // 0x4E 'N'
        0x00,0x00,0x00,0x62,0x62,0x52,0x52,0x4A,0x4A,0x46,0x46,0x00,0x00,

        9, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x22,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,

        7, // 0x50 'P'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x78,0x40,0x40,0x40,0x00,0x00,

        9, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x22,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x04,0x00,0x03,0x00,

        8, // 0x52 'R'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x78,0x48,0x44,0x42,0x00,0x00,

        8, // 0x53 'S'
        0x00,0x00,0x00,0x3C,0x42,0x40,0x30,0x0C,0x02,0x42,0x3C,0x00,0x00,

        7, // 0x54 'T'
        0x00,0x00,0x00,0xFE,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        8, // 0x55 'U'
        0x00,0x00,0x00,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,

        8, // 0x56 'V'
        0x00,0x00,0x00,0x42,0x42,0x42,0x24,0x24,0x24,0x18,0x18,0x00,0x00,

        11, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x44,0x40,0x44,0x40,0x2A,0x80,0x2A,0x80,0x2A,0x80,0x2A,0x80,0x11,0x00,0x11,0x00,0x00,0x00,0x00,0x00,

        8, // 0x58 'X'
        0x00,0x00,0x00,0x42,0x42,0x24,0x18,0x18,0x24,0x42,0x42,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x00,0x00,0x82,0x44,0x28,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        8, // 0x5A 'Z'
        0x00,0x00,0x00,0x7E,0x02,0x04,0x08,0x10,0x20,0x40,0x7E,0x00,0x00,

        5, // 0x5B '['
        0x00,0x00,0x70,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x70,

        5, // 0x5C '\'
        0x00,0x00,0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x00,

        5, // 0x5D ']'
        0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x70,

        9, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x14,0x00,0x22,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,

        7, // 0x60 '`'
        0x00,0x00,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x38,0x04,0x3C,0x44,0x44,0x3C,0x00,0x00,

        7, // 0x62 'b'
        0x00,0x00,0x40,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x78,0x00,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x44,0x38,0x00,0x00,

        7, // 0x64 'd'
        0x00,0x00,0x04,0x04,0x04,0x3C,0x44,0x44,0x44,0x44,0x3C,0x00,0x00,

        7, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x38,0x44,0x7C,0x40,0x44,0x38,0x00,0x00,

        4, // 0x66 'f'
        0x00,0x00,0x30,0x40,0x40,0xF0,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        7, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x3C,0x04,0x38,

        7, // 0x68 'h'
        0x00,0x00,0x40,0x40,0x40,0x78,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x00,0x40,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        4, // 0x6A 'j'
        0x00,0x00,0x20,0x00,0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0xC0,

        7, // 0x6B 'k'
        0x00,0x00,0x40,0x40,0x40,0x44,0x48,0x50,0x70,0x48,0x44,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        11, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x80,0x44,0x40,0x44,0x40,0x44,0x40,0x44,0x40,0x44,0x40,0x00,0x00,0x00,0x00,

        7, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x44,0x00,0x00,

        7, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x38,0x44,0x44,0x44,0x44,0x38,0x00,0x00,

        7, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x44,0x78,0x40,0x40,

        7, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x44,0x44,0x44,0x44,0x3C,0x04,0x04,

        5, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x58,0x60,0x40,0x40,0x40,0x40,0x00,0x00,

        6, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x38,0x40,0x60,0x18,0x08,0x70,0x00,0x00,

        4, // 0x74 't'
        0x00,0x00,0x00,0x40,0x40,0xF0,0x40,0x40,0x40,0x40,0x30,0x00,0x00,

        7, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x44,0x44,0x44,0x44,0x44,0x3C,0x00,0x00,

        7, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x44,0x44,0x28,0x28,0x10,0x10,0x00,0x00,

        9, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x49,0x00,0x49,0x00,0x55,0x00,0x55,0x00,0x22,0x00,0x22,0x00,0x00,0x00,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x44,0x28,0x10,0x10,0x28,0x44,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x44,0x28,0x28,0x28,0x10,0x10,0x10,0x20,

        6, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x78,0x08,0x10,0x20,0x40,0x78,0x00,0x00,

        7, // 0x7B '{'
        0x00,0x00,0x0C,0x10,0x10,0x10,0x10,0x60,0x10,0x10,0x10,0x10,0x0C,

        5, // 0x7C '|'
        0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,

        7, // 0x7D '}'
        0x00,0x00,0x60,0x10,0x10,0x10,0x10,0x0C,0x10,0x10,0x10,0x10,0x60,

        9, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0x00,0x49,0x00,0x46,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x7F,0x80,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana13_bold[] = 
    {
        13, 3, 32, 128-32,
        0x00,0x00,0x0E,0x00,0x1C,0x00,0x2A,0x00,0x45,0x00,0x53,0x00,0x6E,0x00,0x89,0x00,0x97,0x00,
        0xA5,0x00,0xB3,0x00,0xC1,0x00,0xDC,0x00,0xEA,0x00,0xF8,0x00,0x06,0x01,0x14,0x01,0x22,0x01,
        0x30,0x01,0x3E,0x01,0x4C,0x01,0x5A,0x01,0x68,0x01,0x76,0x01,0x84,0x01,0x92,0x01,0xA0,0x01,
        0xAE,0x01,0xBC,0x01,0xD7,0x01,0xF2,0x01,0x0D,0x02,0x1B,0x02,0x36,0x02,0x51,0x02,0x5F,0x02,
        0x6D,0x02,0x88,0x02,0x96,0x02,0xA4,0x02,0xBF,0x02,0xDA,0x02,0xE8,0x02,0xF6,0x02,0x04,0x03,
        0x12,0x03,0x2D,0x03,0x48,0x03,0x63,0x03,0x71,0x03,0x8C,0x03,0x9A,0x03,0xA8,0x03,0xB6,0x03,
        0xD1,0x03,0xDF,0x03,0xFA,0x03,0x08,0x04,0x16,0x04,0x24,0x04,0x32,0x04,0x40,0x04,0x4E,0x04,
        0x69,0x04,0x77,0x04,0x85,0x04,0x93,0x04,0xA1,0x04,0xAF,0x04,0xBD,0x04,0xCB,0x04,0xD9,0x04,
        0xE7,0x04,0xF5,0x04,0x03,0x05,0x11,0x05,0x1F,0x05,0x2D,0x05,0x48,0x05,0x56,0x05,0x64,0x05,
        0x72,0x05,0x80,0x05,0x8E,0x05,0x9C,0x05,0xAA,0x05,0xB8,0x05,0xC6,0x05,0xE1,0x05,0xEF,0x05,
        0xFD,0x05,0x0B,0x06,0x19,0x06,0x27,0x06,0x35,0x06,0x50,0x06,

        4, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x21 '!'
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x60,0x00,0x00,

        7, // 0x22 '"'
        0x00,0x00,0x6C,0x6C,0x6C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x0A,0x00,0x0A,0x00,0x3F,0x00,0x14,0x00,0x14,0x00,0x7E,0x00,0x28,0x00,0x28,0x00,0x00,0x00,0x00,0x00,

        8, // 0x24 '$'
        0x00,0x00,0x08,0x08,0x3C,0x6A,0x68,0x3C,0x16,0x56,0x3C,0x10,0x10,

        14, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x80,0x6C,0x80,0x6D,0x00,0x6D,0x70,0x3A,0xD8,0x02,0xD8,0x04,0xD8,0x04,0x70,0x00,0x00,0x00,0x00,

        10, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x6C,0x00,0x6C,0x00,0x39,0x80,0x6D,0x00,0x66,0x00,0x63,0x00,0x3D,0x80,0x00,0x00,0x00,0x00,

        4, // 0x27 '''
        0x00,0x00,0x60,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x28 '('
        0x00,0x00,0x18,0x30,0x30,0x60,0x60,0x60,0x60,0x60,0x30,0x30,0x18,

        6, // 0x29 ')'
        0x00,0x00,0x60,0x30,0x30,0x18,0x18,0x18,0x18,0x18,0x30,0x30,0x60,

        8, // 0x2A '*'
        0x00,0x00,0x10,0x54,0x38,0x54,0x10,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x7F,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x60,0x40,

        6, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,

        8, // 0x2F '/'
        0x00,0x00,0x06,0x06,0x0C,0x0C,0x18,0x18,0x18,0x30,0x30,0x60,0x60,

        8, // 0x30 '0'
        0x00,0x00,0x00,0x3C,0x66,0x66,0x66,0x66,0x66,0x66,0x3C,0x00,0x00,

        8, // 0x31 '1'
        0x00,0x00,0x00,0x18,0x38,0x18,0x18,0x18,0x18,0x18,0x3C,0x00,0x00,

        8, // 0x32 '2'
        0x00,0x00,0x00,0x3C,0x66,0x06,0x0C,0x18,0x30,0x60,0x7E,0x00,0x00,

        8, // 0x33 '3'
        0x00,0x00,0x00,0x3C,0x66,0x06,0x1C,0x06,0x06,0x66,0x3C,0x00,0x00,

        8, // 0x34 '4'
        0x00,0x00,0x00,0x04,0x0C,0x1C,0x2C,0x4C,0x7E,0x0C,0x0C,0x00,0x00,

        8, // 0x35 '5'
        0x00,0x00,0x00,0x3E,0x30,0x30,0x3C,0x06,0x06,0x66,0x3C,0x00,0x00,

        8, // 0x36 '6'
        0x00,0x00,0x00,0x1C,0x30,0x60,0x7C,0x66,0x66,0x66,0x3C,0x00,0x00,

        8, // 0x37 '7'
        0x00,0x00,0x00,0x7E,0x06,0x0C,0x0C,0x18,0x18,0x30,0x30,0x00,0x00,

        8, // 0x38 '8'
        0x00,0x00,0x00,0x3C,0x66,0x66,0x3C,0x66,0x66,0x66,0x3C,0x00,0x00,

        8, // 0x39 '9'
        0x00,0x00,0x00,0x3C,0x66,0x66,0x66,0x3E,0x06,0x0C,0x38,0x00,0x00,

        4, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x60,0x60,0x00,0x00,

        4, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x60,0x60,0x60,0x40,

        9, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x0C,0x00,0x30,0x00,0x40,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x18,0x00,0x06,0x00,0x01,0x00,0x06,0x00,0x18,0x00,0x60,0x00,0x00,0x00,0x00,0x00,

        7, // 0x3F '?'
        0x00,0x00,0x00,0x38,0x4C,0x0C,0x18,0x30,0x30,0x00,0x30,0x00,0x00,

        11, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x80,0x20,0x40,0x4F,0x40,0x5B,0x40,0x5B,0x40,0x5B,0x40,0x4F,0x80,0x20,0x00,0x1F,0x00,0x00,0x00,

        9, // 0x41 'A'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x1C,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0x7F,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,

        8, // 0x42 'B'
        0x00,0x00,0x00,0x7C,0x66,0x66,0x7C,0x66,0x66,0x66,0x7C,0x00,0x00,

        8, // 0x43 'C'
        0x00,0x00,0x00,0x3C,0x62,0x60,0x60,0x60,0x60,0x62,0x3C,0x00,0x00,

        9, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x66,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x66,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        8, // 0x45 'E'
        0x00,0x00,0x00,0x7E,0x60,0x60,0x7E,0x60,0x60,0x60,0x7E,0x00,0x00,

        8, // 0x46 'F'
        0x00,0x00,0x00,0x7E,0x60,0x60,0x7E,0x60,0x60,0x60,0x60,0x00,0x00,

        9, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x61,0x00,0x60,0x00,0x60,0x00,0x67,0x00,0x63,0x00,0x63,0x00,0x3F,0x00,0x00,0x00,0x00,0x00,

        9, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7F,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,

        6, // 0x49 'I'
        0x00,0x00,0x00,0x78,0x30,0x30,0x30,0x30,0x30,0x30,0x78,0x00,0x00,

        6, // 0x4A 'J'
        0x00,0x00,0x00,0x78,0x18,0x18,0x18,0x18,0x18,0x18,0xF0,0x00,0x00,

        8, // 0x4B 'K'
        0x00,0x00,0x00,0x66,0x6C,0x78,0x70,0x70,0x78,0x6C,0x66,0x00,0x00,

        7, // 0x4C 'L'
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x7E,0x00,0x00,

        10, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x80,0x71,0x80,0x7B,0x80,0x5D,0x80,0x49,0x80,0x41,0x80,0x41,0x80,0x41,0x80,0x00,0x00,0x00,0x00,

        9, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x61,0x00,0x71,0x00,0x59,0x00,0x4D,0x00,0x47,0x00,0x43,0x00,0x41,0x00,0x00,0x00,0x00,0x00,

        9, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,

        8, // 0x50 'P'
        0x00,0x00,0x00,0x7C,0x66,0x66,0x66,0x7C,0x60,0x60,0x60,0x00,0x00,

        9, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x0C,0x00,0x07,0x00,

        8, // 0x52 'R'
        0x00,0x00,0x00,0x7C,0x66,0x66,0x66,0x7C,0x6C,0x66,0x63,0x00,0x00,

        8, // 0x53 'S'
        0x00,0x00,0x00,0x3C,0x62,0x60,0x7C,0x3E,0x06,0x46,0x3C,0x00,0x00,

        8, // 0x54 'T'
        0x00,0x00,0x00,0xFF,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,

        9, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,

        8, // 0x56 'V'
        0x00,0x00,0x00,0x66,0x66,0x66,0x3C,0x3C,0x3C,0x18,0x18,0x00,0x00,

        12, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x66,0x60,0x66,0x60,0x66,0x60,0x36,0xC0,0x3F,0xC0,0x39,0xC0,0x19,0x80,0x19,0x80,0x00,0x00,0x00,0x00,

        8, // 0x58 'X'
        0x00,0x00,0x00,0x66,0x66,0x3C,0x18,0x18,0x3C,0x66,0x66,0x00,0x00,

        8, // 0x59 'Y'
        0x00,0x00,0x00,0x66,0x66,0x3C,0x3C,0x18,0x18,0x18,0x18,0x00,0x00,

        8, // 0x5A 'Z'
        0x00,0x00,0x00,0x7E,0x06,0x0E,0x1C,0x38,0x70,0x60,0x7E,0x00,0x00,

        6, // 0x5B '['
        0x00,0x00,0x78,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x78,

        8, // 0x5C '\'
        0x00,0x00,0x60,0x60,0x30,0x30,0x18,0x18,0x18,0x0C,0x0C,0x06,0x06,

        6, // 0x5D ']'
        0x00,0x00,0x78,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x78,

        10, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x1E,0x00,0x33,0x00,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,

        8, // 0x60 '`'
        0x00,0x00,0x30,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x06,0x3E,0x66,0x66,0x3E,0x00,0x00,

        8, // 0x62 'b'
        0x00,0x00,0x60,0x60,0x60,0x7C,0x66,0x66,0x66,0x66,0x7C,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x60,0x60,0x60,0x60,0x3C,0x00,0x00,

        8, // 0x64 'd'
        0x00,0x00,0x06,0x06,0x06,0x3E,0x66,0x66,0x66,0x66,0x3E,0x00,0x00,

        8, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x66,0x7E,0x60,0x62,0x3C,0x00,0x00,

        5, // 0x66 'f'
        0x00,0x00,0x38,0x60,0x60,0xF8,0x60,0x60,0x60,0x60,0x60,0x00,0x00,

        8, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x3E,0x66,0x66,0x66,0x66,0x3E,0x06,0x3C,

        8, // 0x68 'h'
        0x00,0x00,0x60,0x60,0x60,0x7C,0x66,0x66,0x66,0x66,0x66,0x00,0x00,

        4, // 0x69 'i'
        0x00,0x00,0x00,0x60,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,

        5, // 0x6A 'j'
        0x00,0x00,0x00,0x30,0x00,0x70,0x30,0x30,0x30,0x30,0x30,0x30,0xE0,

        8, // 0x6B 'k'
        0x00,0x00,0x60,0x60,0x60,0x66,0x6C,0x78,0x78,0x6C,0x66,0x00,0x00,

        4, // 0x6C 'l'
        0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,

        12, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7D,0xC0,0x66,0x60,0x66,0x60,0x66,0x60,0x66,0x60,0x66,0x60,0x00,0x00,0x00,0x00,

        8, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x66,0x66,0x66,0x66,0x66,0x00,0x00,

        8, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x66,0x66,0x66,0x66,0x3C,0x00,0x00,

        8, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x66,0x66,0x66,0x66,0x7C,0x60,0x60,

        8, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x3E,0x66,0x66,0x66,0x66,0x3E,0x06,0x06,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x6C,0x7C,0x60,0x60,0x60,0x60,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x60,0x78,0x3C,0x0C,0x78,0x00,0x00,

        5, // 0x74 't'
        0x00,0x00,0x00,0x60,0x60,0xF8,0x60,0x60,0x60,0x60,0x38,0x00,0x00,

        8, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x66,0x66,0x66,0x3E,0x00,0x00,

        8, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x66,0x3C,0x3C,0x18,0x00,0x00,

        10, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6D,0x80,0x6D,0x80,0x6D,0x80,0x6D,0x80,0x33,0x00,0x33,0x00,0x00,0x00,0x00,0x00,

        8, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x3C,0x3C,0x66,0x66,0x00,0x00,

        8, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x3C,0x3C,0x18,0x18,0x30,0x30,

        7, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x0C,0x18,0x30,0x60,0x7C,0x00,0x00,

        8, // 0x7B '{'
        0x00,0x00,0x0E,0x18,0x18,0x18,0x18,0x70,0x18,0x18,0x18,0x18,0x0E,

        6, // 0x7C '|'
        0x00,0x00,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,

        8, // 0x7D '}'
        0x00,0x00,0x70,0x18,0x18,0x18,0x18,0x0E,0x18,0x18,0x18,0x18,0x70,

        9, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x31,0x00,0x49,0x00,0x49,0x00,0x46,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x7F,0x80,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana14[] = 
    {
        14, 3, 32, 128-32,
        0x00,0x00,0x0F,0x00,0x1E,0x00,0x2D,0x00,0x4A,0x00,0x59,0x00,0x76,0x00,0x93,0x00,0xA2,0x00,
        0xB1,0x00,0xC0,0x00,0xCF,0x00,0xEC,0x00,0xFB,0x00,0x0A,0x01,0x19,0x01,0x28,0x01,0x37,0x01,
        0x46,0x01,0x55,0x01,0x64,0x01,0x73,0x01,0x82,0x01,0x91,0x01,0xA0,0x01,0xAF,0x01,0xBE,0x01,
        0xCD,0x01,0xDC,0x01,0xF9,0x01,0x16,0x02,0x33,0x02,0x42,0x02,0x5F,0x02,0x6E,0x02,0x7D,0x02,
        0x9A,0x02,0xB7,0x02,0xC6,0x02,0xD5,0x02,0xF2,0x02,0x0F,0x03,0x1E,0x03,0x2D,0x03,0x3C,0x03,
        0x4B,0x03,0x68,0x03,0x85,0x03,0xA2,0x03,0xB1,0x03,0xCE,0x03,0xDD,0x03,0xEC,0x03,0xFB,0x03,
        0x18,0x04,0x27,0x04,0x44,0x04,0x53,0x04,0x62,0x04,0x71,0x04,0x80,0x04,0x8F,0x04,0x9E,0x04,
        0xBB,0x04,0xCA,0x04,0xD9,0x04,0xE8,0x04,0xF7,0x04,0x06,0x05,0x15,0x05,0x24,0x05,0x33,0x05,
        0x42,0x05,0x51,0x05,0x60,0x05,0x6F,0x05,0x7E,0x05,0x8D,0x05,0xAA,0x05,0xB9,0x05,0xC8,0x05,
        0xD7,0x05,0xE6,0x05,0xF5,0x05,0x04,0x06,0x13,0x06,0x22,0x06,0x31,0x06,0x4E,0x06,0x5D,0x06,
        0x6C,0x06,0x7B,0x06,0x8A,0x06,0x99,0x06,0xA8,0x06,0xC5,0x06,

        4, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x21 '!'
        0x00,0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x00,0x00,

        6, // 0x22 '"'
        0x00,0x00,0x48,0x48,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x00,0x09,0x00,0x12,0x00,0x3F,0x80,0x12,0x00,0x12,0x00,0x7F,0x00,0x24,0x00,0x24,0x00,0x00,0x00,0x00,0x00,

        8, // 0x24 '$'
        0x00,0x00,0x10,0x10,0x3E,0x50,0x50,0x30,0x1C,0x12,0x12,0x7C,0x10,0x10,

        13, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x80,0x49,0x00,0x49,0x00,0x4A,0x00,0x32,0x60,0x02,0x90,0x04,0x90,0x04,0x90,0x08,0x60,0x00,0x00,0x00,0x00,

        10, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x44,0x00,0x44,0x00,0x44,0x00,0x39,0x00,0x45,0x00,0x42,0x00,0x43,0x00,0x3C,0x80,0x00,0x00,0x00,0x00,

        3, // 0x27 '''
        0x00,0x00,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x28 '('
        0x00,0x00,0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,

        5, // 0x29 ')'
        0x00,0x00,0x40,0x20,0x20,0x10,0x10,0x10,0x10,0x10,0x10,0x20,0x20,0x40,

        8, // 0x2A '*'
        0x00,0x00,0x10,0x54,0x38,0x54,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x7F,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x40,

        5, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,

        5, // 0x2F '/'
        0x00,0x00,0x08,0x08,0x10,0x10,0x10,0x20,0x20,0x20,0x40,0x40,0x40,0x80,

        8, // 0x30 '0'
        0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,

        8, // 0x31 '1'
        0x00,0x00,0x00,0x08,0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x3E,0x00,0x00,

        8, // 0x32 '2'
        0x00,0x00,0x00,0x3C,0x42,0x42,0x02,0x04,0x18,0x20,0x40,0x7E,0x00,0x00,

        8, // 0x33 '3'
        0x00,0x00,0x00,0x3C,0x42,0x02,0x02,0x1C,0x02,0x02,0x42,0x3C,0x00,0x00,

        8, // 0x34 '4'
        0x00,0x00,0x00,0x04,0x0C,0x14,0x24,0x44,0x7F,0x04,0x04,0x04,0x00,0x00,

        8, // 0x35 '5'
        0x00,0x00,0x00,0x7E,0x40,0x40,0x7C,0x02,0x02,0x02,0x42,0x3C,0x00,0x00,

        8, // 0x36 '6'
        0x00,0x00,0x00,0x1C,0x20,0x40,0x7C,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,

        8, // 0x37 '7'
        0x00,0x00,0x00,0x7E,0x02,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x00,0x00,

        8, // 0x38 '8'
        0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x3C,0x42,0x42,0x42,0x3C,0x00,0x00,

        8, // 0x39 '9'
        0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x42,0x3E,0x02,0x04,0x38,0x00,0x00,

        5, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,0x20,0x20,0x00,0x00,

        5, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,0x20,0x20,0x20,0x40,

        9, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x0C,0x00,0x30,0x00,0x40,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x18,0x00,0x06,0x00,0x01,0x00,0x06,0x00,0x18,0x00,0x60,0x00,0x00,0x00,0x00,0x00,

        7, // 0x3F '?'
        0x00,0x00,0x00,0x38,0x44,0x04,0x04,0x08,0x10,0x10,0x00,0x10,0x00,0x00,

        12, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0xC0,0x27,0x40,0x49,0x20,0x49,0x20,0x49,0x20,0x49,0x20,0x27,0xC0,0x30,0x00,0x0F,0x00,0x00,0x00,

        8, // 0x41 'A'
        0x00,0x00,0x00,0x18,0x18,0x24,0x24,0x42,0x42,0x7E,0x81,0x81,0x00,0x00,

        8, // 0x42 'B'
        0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x7C,0x42,0x42,0x42,0x7C,0x00,0x00,

        9, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x42,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x42,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,

        8, // 0x45 'E'
        0x00,0x00,0x00,0x7E,0x40,0x40,0x40,0x7E,0x40,0x40,0x40,0x7E,0x00,0x00,

        7, // 0x46 'F'
        0x00,0x00,0x00,0x7E,0x40,0x40,0x40,0x7C,0x40,0x40,0x40,0x40,0x00,0x00,

        9, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x00,0x40,0x00,0x47,0x00,0x41,0x00,0x41,0x00,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x7F,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x00,0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,

        5, // 0x4A 'J'
        0x00,0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0xE0,0x00,0x00,

        8, // 0x4B 'K'
        0x00,0x00,0x00,0x42,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x42,0x00,0x00,

        7, // 0x4C 'L'
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7E,0x00,0x00,

        10, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x52,0x80,0x52,0x80,0x52,0x80,0x4C,0x80,0x4C,0x80,0x40,0x80,0x40,0x80,0x00,0x00,0x00,0x00,

        9, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x00,0x61,0x00,0x51,0x00,0x51,0x00,0x49,0x00,0x45,0x00,0x45,0x00,0x43,0x00,0x43,0x00,0x00,0x00,0x00,0x00,

        10, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,

        8, // 0x50 'P'
        0x00,0x00,0x00,0x7C,0x42,0x42,0x42,0x42,0x7C,0x40,0x40,0x40,0x00,0x00,

        10, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x02,0x00,0x01,0x80,

        8, // 0x52 'R'
        0x00,0x00,0x00,0x7C,0x42,0x42,0x42,0x7C,0x48,0x44,0x42,0x41,0x00,0x00,

        8, // 0x53 'S'
        0x00,0x00,0x00,0x3C,0x42,0x40,0x40,0x3C,0x02,0x02,0x42,0x3C,0x00,0x00,

        7, // 0x54 'T'
        0x00,0x00,0x00,0xFE,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        9, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,

        8, // 0x56 'V'
        0x00,0x00,0x00,0x81,0x81,0x42,0x42,0x42,0x24,0x24,0x18,0x18,0x00,0x00,

        13, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x10,0x42,0x10,0x45,0x10,0x45,0x10,0x25,0x20,0x28,0xA0,0x28,0xA0,0x10,0x40,0x10,0x40,0x00,0x00,0x00,0x00,

        8, // 0x58 'X'
        0x00,0x00,0x00,0x42,0x42,0x24,0x18,0x18,0x18,0x24,0x42,0x42,0x00,0x00,

        7, // 0x59 'Y'
        0x00,0x00,0x00,0x82,0x44,0x44,0x28,0x10,0x10,0x10,0x10,0x10,0x00,0x00,

        8, // 0x5A 'Z'
        0x00,0x00,0x00,0x7E,0x02,0x04,0x08,0x10,0x10,0x20,0x40,0x7E,0x00,0x00,

        5, // 0x5B '['
        0x00,0x00,0x70,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x70,

        5, // 0x5C '\'
        0x00,0x00,0x80,0x80,0x40,0x40,0x40,0x20,0x20,0x10,0x10,0x10,0x08,0x08,

        5, // 0x5D ']'
        0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x70,

        10, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x12,0x00,0x21,0x00,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,

        8, // 0x60 '`'
        0x00,0x00,0x20,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x02,0x02,0x3E,0x42,0x42,0x3E,0x00,0x00,

        8, // 0x62 'b'
        0x00,0x00,0x40,0x40,0x40,0x5C,0x62,0x42,0x42,0x42,0x42,0x7C,0x00,0x00,

        6, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x38,0x44,0x40,0x40,0x40,0x44,0x38,0x00,0x00,

        8, // 0x64 'd'
        0x00,0x00,0x02,0x02,0x02,0x3E,0x42,0x42,0x42,0x42,0x46,0x3A,0x00,0x00,

        8, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x7E,0x40,0x42,0x3C,0x00,0x00,

        4, // 0x66 'f'
        0x00,0x00,0x30,0x40,0x40,0xF0,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        8, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x3E,0x42,0x42,0x42,0x42,0x46,0x3A,0x02,0x3C,

        8, // 0x68 'h'
        0x00,0x00,0x40,0x40,0x40,0x5C,0x62,0x42,0x42,0x42,0x42,0x42,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x00,0x40,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        4, // 0x6A 'j'
        0x00,0x00,0x20,0x00,0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0xC0,

        7, // 0x6B 'k'
        0x00,0x00,0x40,0x40,0x40,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        11, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7B,0x80,0x44,0x40,0x44,0x40,0x44,0x40,0x44,0x40,0x44,0x40,0x44,0x40,0x00,0x00,0x00,0x00,

        8, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x5C,0x62,0x42,0x42,0x42,0x42,0x42,0x00,0x00,

        8, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,

        8, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x5C,0x62,0x42,0x42,0x42,0x42,0x7C,0x40,0x40,

        8, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x3E,0x42,0x42,0x42,0x42,0x46,0x3A,0x02,0x02,

        5, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x58,0x60,0x40,0x40,0x40,0x40,0x40,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x40,0x40,0x38,0x04,0x04,0x78,0x00,0x00,

        5, // 0x74 't'
        0x00,0x00,0x00,0x40,0x40,0xF8,0x40,0x40,0x40,0x40,0x40,0x38,0x00,0x00,

        8, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x42,0x42,0x42,0x42,0x42,0x46,0x3A,0x00,0x00,

        7, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x44,0x44,0x28,0x28,0x28,0x10,0x10,0x00,0x00,

        11, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x44,0x40,0x44,0x40,0x2A,0x80,0x2A,0x80,0x2A,0x80,0x11,0x00,0x11,0x00,0x00,0x00,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,

        7, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x44,0x44,0x28,0x28,0x28,0x10,0x10,0x10,0x20,

        7, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,

        8, // 0x7B '{'
        0x00,0x00,0x0C,0x10,0x10,0x10,0x10,0x60,0x10,0x10,0x10,0x10,0x10,0x0C,

        5, // 0x7C '|'
        0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,

        8, // 0x7D '}'
        0x00,0x00,0x30,0x08,0x08,0x08,0x08,0x06,0x08,0x08,0x08,0x08,0x08,0x30,

        10, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x80,0x4C,0x80,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xE0,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x3F,0xE0,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana14_bold[] = 
    {
        14, 3, 32, 128-32,
        0x00,0x00,0x0F,0x00,0x1E,0x00,0x2D,0x00,0x4A,0x00,0x67,0x00,0x84,0x00,0xA1,0x00,0xB0,0x00,
        0xBF,0x00,0xCE,0x00,0xEB,0x00,0x08,0x01,0x17,0x01,0x26,0x01,0x35,0x01,0x44,0x01,0x61,0x01,
        0x7E,0x01,0x9B,0x01,0xB8,0x01,0xD5,0x01,0xF2,0x01,0x0F,0x02,0x2C,0x02,0x49,0x02,0x66,0x02,
        0x75,0x02,0x84,0x02,0xA1,0x02,0xBE,0x02,0xDB,0x02,0xEA,0x02,0x07,0x03,0x24,0x03,0x41,0x03,
        0x5E,0x03,0x7B,0x03,0x8A,0x03,0x99,0x03,0xB6,0x03,0xD3,0x03,0xE2,0x03,0xF1,0x03,0x0E,0x04,
        0x1D,0x04,0x3A,0x04,0x57,0x04,0x74,0x04,0x91,0x04,0xAE,0x04,0xCB,0x04,0xE8,0x04,0xF7,0x04,
        0x14,0x05,0x31,0x05,0x4E,0x05,0x6B,0x05,0x88,0x05,0x97,0x05,0xA6,0x05,0xB5,0x05,0xC4,0x05,
        0xE1,0x05,0xFE,0x05,0x1B,0x06,0x2A,0x06,0x39,0x06,0x48,0x06,0x57,0x06,0x66,0x06,0x75,0x06,
        0x84,0x06,0x93,0x06,0xA2,0x06,0xB1,0x06,0xC0,0x06,0xCF,0x06,0xEC,0x06,0xFB,0x06,0x0A,0x07,
        0x19,0x07,0x28,0x07,0x37,0x07,0x46,0x07,0x55,0x07,0x64,0x07,0x73,0x07,0x90,0x07,0x9F,0x07,
        0xAE,0x07,0xBD,0x07,0xDA,0x07,0xE9,0x07,0x06,0x08,0x23,0x08,

        4, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x21 '!'
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x60,0x60,0x00,0x00,

        7, // 0x22 '"'
        0x00,0x00,0x6C,0x6C,0x6C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x00,0x09,0x00,0x3F,0x80,0x3F,0x80,0x12,0x00,0x7F,0x00,0x7F,0x00,0x24,0x00,0x24,0x00,0x00,0x00,0x00,0x00,

        9, // 0x24 '$'
        0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x3E,0x00,0x69,0x00,0x68,0x00,0x7E,0x00,0x3F,0x00,0x0B,0x00,0x4B,0x00,0x3E,0x00,0x08,0x00,0x08,0x00,

        15, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x40,0x6C,0x40,0x6C,0x80,0x6C,0xB8,0x6D,0x6C,0x3A,0x6C,0x02,0x6C,0x04,0x6C,0x04,0x38,0x00,0x00,0x00,0x00,

        10, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x6C,0x00,0x6C,0x00,0x6C,0x00,0x39,0x80,0x6D,0x00,0x66,0x00,0x63,0x00,0x3D,0x80,0x00,0x00,0x00,0x00,

        4, // 0x27 '''
        0x00,0x00,0x60,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x00,0x00,0x18,0x30,0x30,0x60,0x60,0x60,0x60,0x60,0x60,0x30,0x30,0x18,

        7, // 0x29 ')'
        0x00,0x00,0x30,0x18,0x18,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x18,0x18,0x30,

        9, // 0x2A '*'
        0x00,0x00,0x00,0x00,0x08,0x00,0x2A,0x00,0x1C,0x00,0x1C,0x00,0x2A,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x7F,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x60,0x40,

        6, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,

        8, // 0x2F '/'
        0x00,0x00,0x06,0x06,0x0C,0x0C,0x0C,0x18,0x18,0x30,0x30,0x30,0x60,0x60,

        9, // 0x30 '0'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x31 '1'
        0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x3C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x3F,0x00,0x00,0x00,0x00,0x00,

        9, // 0x32 '2'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x03,0x00,0x03,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x30,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,

        9, // 0x33 '3'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x03,0x00,0x03,0x00,0x1E,0x00,0x03,0x00,0x03,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x34 '4'
        0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x0E,0x00,0x16,0x00,0x16,0x00,0x26,0x00,0x46,0x00,0x7F,0x00,0x06,0x00,0x06,0x00,0x00,0x00,0x00,0x00,

        9, // 0x35 '5'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x30,0x00,0x30,0x00,0x3E,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x36 '6'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x30,0x00,0x60,0x00,0x7E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x37 '7'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x30,0x00,0x00,0x00,0x00,0x00,

        9, // 0x38 '8'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x39 '9'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3F,0x00,0x03,0x00,0x06,0x00,0x3C,0x00,0x00,0x00,0x00,0x00,

        5, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0x00,0x00,

        5, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x60,0x60,0x60,0x40,

        10, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x06,0x00,0x18,0x00,0x60,0x00,0x60,0x00,0x18,0x00,0x06,0x00,0x01,0x00,0x00,0x00,0x00,0x00,

        10, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x03,0x00,0x0C,0x00,0x30,0x00,0x40,0x00,0x00,0x00,0x00,0x00,

        7, // 0x3F '?'
        0x00,0x00,0x00,0x38,0x4C,0x0C,0x18,0x30,0x30,0x00,0x30,0x30,0x00,0x00,

        12, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0xC0,0x2F,0x40,0x5B,0x20,0x5B,0x20,0x5B,0x20,0x5B,0x20,0x2F,0xC0,0x30,0x00,0x0F,0x00,0x00,0x00,

        9, // 0x41 'A'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x1C,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0x7F,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,

        9, // 0x42 'B'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x66,0x00,0x66,0x00,0x66,0x00,0x7E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,

        9, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x31,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x31,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,

        10, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x63,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,

        8, // 0x45 'E'
        0x00,0x00,0x00,0x7E,0x60,0x60,0x60,0x7E,0x60,0x60,0x60,0x7E,0x00,0x00,

        8, // 0x46 'F'
        0x00,0x00,0x00,0x7E,0x60,0x60,0x60,0x7E,0x60,0x60,0x60,0x60,0x00,0x00,

        10, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x30,0x80,0x60,0x00,0x60,0x00,0x60,0x00,0x67,0x80,0x61,0x80,0x31,0x80,0x1F,0x80,0x00,0x00,0x00,0x00,

        10, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x7F,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x00,0x00,0x00,0x00,

        6, // 0x49 'I'
        0x00,0x00,0x00,0x78,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x78,0x00,0x00,

        7, // 0x4A 'J'
        0x00,0x00,0x00,0x7C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0xF8,0x00,0x00,

        9, // 0x4B 'K'
        0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x66,0x00,0x6C,0x00,0x78,0x00,0x70,0x00,0x78,0x00,0x6C,0x00,0x66,0x00,0x63,0x00,0x00,0x00,0x00,0x00,

        8, // 0x4C 'L'
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x7F,0x00,0x00,

        11, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x71,0xC0,0x71,0xC0,0x5A,0xC0,0x5A,0xC0,0x4C,0xC0,0x4C,0xC0,0x40,0xC0,0x40,0xC0,0x00,0x00,0x00,0x00,

        10, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x80,0x70,0x80,0x58,0x80,0x58,0x80,0x4C,0x80,0x46,0x80,0x46,0x80,0x43,0x80,0x41,0x80,0x00,0x00,0x00,0x00,

        11, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,

        9, // 0x50 'P'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7E,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x00,0x00,0x00,0x00,

        11, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x06,0x00,0x03,0xC0,

        9, // 0x52 'R'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7E,0x00,0x6C,0x00,0x66,0x00,0x63,0x00,0x61,0x80,0x00,0x00,0x00,0x00,

        9, // 0x53 'S'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x61,0x00,0x60,0x00,0x70,0x00,0x3E,0x00,0x07,0x00,0x03,0x00,0x43,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,

        8, // 0x54 'T'
        0x00,0x00,0x00,0xFF,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,

        10, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x3F,0x00,0x00,0x00,0x00,0x00,

        9, // 0x56 'V'
        0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0x1C,0x00,0x1C,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,

        14, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x63,0x18,0x63,0x18,0x33,0x30,0x37,0xB0,0x34,0xB0,0x1C,0xE0,0x18,0x60,0x18,0x60,0x00,0x00,0x00,0x00,

        9, // 0x58 'X'
        0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x1C,0x00,0x1C,0x00,0x1C,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,

        10, // 0x59 'Y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x33,0x00,0x1E,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,

        8, // 0x5A 'Z'
        0x00,0x00,0x00,0x7E,0x0C,0x0C,0x18,0x18,0x30,0x30,0x60,0x7E,0x00,0x00,

        6, // 0x5B '['
        0x00,0x00,0x78,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x78,

        8, // 0x5C '\'
        0x00,0x00,0x60,0x60,0x30,0x30,0x30,0x18,0x18,0x0C,0x0C,0x0C,0x06,0x06,

        6, // 0x5D ']'
        0x00,0x00,0x78,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x78,

        10, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x1E,0x00,0x33,0x00,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,

        9, // 0x60 '`'
        0x00,0x00,0x00,0x00,0x30,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x06,0x3E,0x66,0x66,0x66,0x3E,0x00,0x00,

        8, // 0x62 'b'
        0x00,0x00,0x60,0x60,0x60,0x7C,0x66,0x66,0x66,0x66,0x66,0x7C,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x62,0x60,0x60,0x60,0x62,0x3C,0x00,0x00,

        8, // 0x64 'd'
        0x00,0x00,0x06,0x06,0x06,0x3E,0x66,0x66,0x66,0x66,0x66,0x3E,0x00,0x00,

        8, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x66,0x66,0x7E,0x60,0x62,0x3C,0x00,0x00,

        5, // 0x66 'f'
        0x00,0x00,0x38,0x60,0x60,0xF8,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,

        8, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x3E,0x66,0x66,0x66,0x66,0x66,0x3E,0x06,0x3C,

        8, // 0x68 'h'
        0x00,0x00,0x60,0x60,0x60,0x7C,0x66,0x66,0x66,0x66,0x66,0x66,0x00,0x00,

        4, // 0x69 'i'
        0x00,0x00,0x60,0x60,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,

        5, // 0x6A 'j'
        0x00,0x00,0x30,0x30,0x00,0x70,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0xE0,

        8, // 0x6B 'k'
        0x00,0x00,0x60,0x60,0x60,0x66,0x6C,0x78,0x78,0x6C,0x66,0x63,0x00,0x00,

        4, // 0x6C 'l'
        0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,

        12, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6C,0xC0,0x77,0x60,0x66,0x60,0x66,0x60,0x66,0x60,0x66,0x60,0x66,0x60,0x00,0x00,0x00,0x00,

        8, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x66,0x66,0x66,0x66,0x66,0x66,0x00,0x00,

        8, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x66,0x66,0x66,0x66,0x66,0x3C,0x00,0x00,

        8, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x66,0x66,0x66,0x66,0x66,0x7C,0x60,0x60,

        8, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x3E,0x66,0x66,0x66,0x66,0x66,0x3E,0x06,0x06,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x6C,0x7C,0x60,0x60,0x60,0x60,0x60,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x3C,0x60,0x60,0x38,0x0C,0x0C,0x78,0x00,0x00,

        5, // 0x74 't'
        0x00,0x00,0x00,0x60,0x60,0xF8,0x60,0x60,0x60,0x60,0x60,0x38,0x00,0x00,

        8, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x66,0x66,0x66,0x66,0x3E,0x00,0x00,

        8, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x66,0x3C,0x3C,0x3C,0x18,0x00,0x00,

        12, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x66,0x60,0x66,0x60,0x66,0x60,0x69,0x60,0x39,0xC0,0x30,0xC0,0x30,0xC0,0x00,0x00,0x00,0x00,

        8, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x3C,0x18,0x3C,0x66,0x66,0x00,0x00,

        8, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x66,0x66,0x66,0x3C,0x3C,0x3C,0x18,0x18,0x30,

        7, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x7C,0x0C,0x18,0x38,0x30,0x60,0x7C,0x00,0x00,

        9, // 0x7B '{'
        0x00,0x00,0x00,0x00,0x0E,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x70,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x0E,0x00,

        6, // 0x7C '|'
        0x00,0x00,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,

        9, // 0x7D '}'
        0x00,0x00,0x00,0x00,0x38,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x07,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x38,0x00,

        10, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x80,0x48,0x80,0x44,0x80,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xE0,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x3F,0xE0,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana16[] = 
    {
        16, 4, 32, 128-32,
        0x00,0x00,0x11,0x00,0x22,0x00,0x33,0x00,0x54,0x00,0x65,0x00,0x86,0x00,0xA7,0x00,0xB8,0x00,
        0xC9,0x00,0xDA,0x00,0xFB,0x00,0x1C,0x01,0x2D,0x01,0x3E,0x01,0x4F,0x01,0x60,0x01,0x71,0x01,
        0x82,0x01,0x93,0x01,0xA4,0x01,0xB5,0x01,0xC6,0x01,0xD7,0x01,0xE8,0x01,0xF9,0x01,0x0A,0x02,
        0x1B,0x02,0x2C,0x02,0x4D,0x02,0x6E,0x02,0x8F,0x02,0xA0,0x02,0xC1,0x02,0xE2,0x02,0xF3,0x02,
        0x14,0x03,0x35,0x03,0x46,0x03,0x57,0x03,0x78,0x03,0x99,0x03,0xAA,0x03,0xBB,0x03,0xCC,0x03,
        0xDD,0x03,0xFE,0x03,0x1F,0x04,0x40,0x04,0x51,0x04,0x72,0x04,0x93,0x04,0xB4,0x04,0xD5,0x04,
        0xF6,0x04,0x17,0x05,0x38,0x05,0x59,0x05,0x7A,0x05,0x9B,0x05,0xAC,0x05,0xBD,0x05,0xCE,0x05,
        0xEF,0x05,0x00,0x06,0x11,0x06,0x22,0x06,0x33,0x06,0x44,0x06,0x55,0x06,0x66,0x06,0x77,0x06,
        0x88,0x06,0x99,0x06,0xAA,0x06,0xBB,0x06,0xCC,0x06,0xDD,0x06,0xFE,0x06,0x0F,0x07,0x20,0x07,
        0x31,0x07,0x42,0x07,0x53,0x07,0x64,0x07,0x75,0x07,0x86,0x07,0x97,0x07,0xB8,0x07,0xC9,0x07,
        0xDA,0x07,0xEB,0x07,0xFC,0x07,0x0D,0x08,0x1E,0x08,0x3F,0x08,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x21 '!'
        0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x20,0x00,0x00,0x00,

        5, // 0x22 '"'
        0x00,0x00,0x00,0x50,0x50,0x50,0x50,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x00,0x09,0x00,0x3F,0x80,0x12,0x00,0x12,0x00,0x7F,0x00,0x24,0x00,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x24 '$'
        0x00,0x00,0x00,0x10,0x10,0x3E,0x50,0x50,0x30,0x1C,0x12,0x12,0x7C,0x10,0x10,0x00,

        13, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x40,0x44,0x80,0x45,0x00,0x45,0x00,0x3A,0xE0,0x05,0x10,0x05,0x10,0x09,0x10,0x10,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x44,0x00,0x44,0x00,0x44,0x00,0x38,0x80,0x45,0x00,0x42,0x00,0x46,0x00,0x39,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        3, // 0x27 '''
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x28 '('
        0x00,0x00,0x00,0x08,0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,0x08,

        6, // 0x29 ')'
        0x00,0x00,0x00,0x40,0x20,0x10,0x10,0x08,0x08,0x08,0x08,0x08,0x10,0x10,0x20,0x40,

        9, // 0x2A '*'
        0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x2A,0x00,0x1C,0x00,0x2A,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x7F,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x40,0x00,

        7, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,

        6, // 0x2F '/'
        0x00,0x00,0x00,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x80,0x80,0x00,

        8, // 0x30 '0'
        0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,0x00,

        8, // 0x31 '1'
        0x00,0x00,0x00,0x00,0x08,0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x3E,0x00,0x00,0x00,

        8, // 0x32 '2'
        0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x02,0x04,0x18,0x20,0x40,0x7E,0x00,0x00,0x00,

        8, // 0x33 '3'
        0x00,0x00,0x00,0x00,0x3C,0x42,0x02,0x02,0x1C,0x02,0x02,0x42,0x3C,0x00,0x00,0x00,

        8, // 0x34 '4'
        0x00,0x00,0x00,0x00,0x04,0x0C,0x14,0x24,0x44,0x7F,0x04,0x04,0x04,0x00,0x00,0x00,

        8, // 0x35 '5'
        0x00,0x00,0x00,0x00,0x3E,0x20,0x20,0x20,0x3C,0x02,0x02,0x42,0x3C,0x00,0x00,0x00,

        8, // 0x36 '6'
        0x00,0x00,0x00,0x00,0x1C,0x20,0x40,0x7C,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,0x00,

        8, // 0x37 '7'
        0x00,0x00,0x00,0x00,0x7E,0x02,0x04,0x04,0x08,0x08,0x10,0x10,0x10,0x00,0x00,0x00,

        8, // 0x38 '8'
        0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x3C,0x42,0x42,0x42,0x3C,0x00,0x00,0x00,

        8, // 0x39 '9'
        0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x42,0x3E,0x02,0x04,0x38,0x00,0x00,0x00,

        6, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,

        6, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,0x20,0x20,0x20,0x40,0x00,

        9, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x0C,0x00,0x30,0x00,0x40,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x18,0x00,0x06,0x00,0x01,0x00,0x06,0x00,0x18,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x3F '?'
        0x00,0x00,0x00,0x00,0x38,0x44,0x04,0x08,0x10,0x10,0x00,0x10,0x10,0x00,0x00,0x00,

        13, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x10,0x40,0x27,0xA0,0x48,0x90,0x48,0x90,0x48,0x90,0x48,0x90,0x48,0x90,0x27,0xE0,0x10,0x00,0x0F,0x80,0x00,0x00,

        9, // 0x41 'A'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x14,0x00,0x14,0x00,0x22,0x00,0x22,0x00,0x3E,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x42 'B'
        0x00,0x00,0x00,0x00,0x78,0x44,0x44,0x44,0x7C,0x42,0x42,0x42,0x7C,0x00,0x00,0x00,

        9, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x42,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x42,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x45 'E'
        0x00,0x00,0x00,0x00,0x7E,0x40,0x40,0x40,0x7E,0x40,0x40,0x40,0x7E,0x00,0x00,0x00,

        8, // 0x46 'F'
        0x00,0x00,0x00,0x00,0x7E,0x40,0x40,0x40,0x7C,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        9, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x47,0x00,0x41,0x00,0x21,0x00,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x7F,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x00,0x00,0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,0x00,

        6, // 0x4A 'J'
        0x00,0x00,0x00,0x00,0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0xF0,0x00,0x00,0x00,

        8, // 0x4B 'K'
        0x00,0x00,0x00,0x00,0x42,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x42,0x00,0x00,0x00,

        7, // 0x4C 'L'
        0x00,0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7E,0x00,0x00,0x00,

        11, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x60,0xC0,0x51,0x40,0x51,0x40,0x4A,0x40,0x4A,0x40,0x44,0x40,0x44,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x00,0x61,0x00,0x51,0x00,0x51,0x00,0x49,0x00,0x45,0x00,0x45,0x00,0x43,0x00,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x50 'P'
        0x00,0x00,0x00,0x00,0x7C,0x42,0x42,0x42,0x42,0x7C,0x40,0x40,0x40,0x00,0x00,0x00,

        10, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x02,0x00,0x01,0x80,0x00,0x00,

        9, // 0x52 'R'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x42,0x00,0x42,0x00,0x42,0x00,0x44,0x00,0x78,0x00,0x44,0x00,0x42,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x53 'S'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x41,0x00,0x40,0x00,0x40,0x00,0x3E,0x00,0x01,0x00,0x01,0x00,0x41,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x54 'T'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x56 'V'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x22,0x00,0x14,0x00,0x14,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x10,0x42,0x10,0x45,0x10,0x45,0x10,0x25,0x20,0x28,0xA0,0x28,0xA0,0x10,0x40,0x10,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x58 'X'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x14,0x00,0x08,0x00,0x14,0x00,0x22,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x59 'Y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x22,0x00,0x22,0x00,0x14,0x00,0x14,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x5A 'Z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x01,0x00,0x02,0x00,0x04,0x00,0x08,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5B '['
        0x00,0x00,0x00,0x38,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x38,0x00,

        6, // 0x5C '\'
        0x00,0x00,0x00,0x80,0x80,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x00,

        6, // 0x5D ']'
        0x00,0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x70,0x00,

        11, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x0A,0x00,0x11,0x00,0x20,0x80,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x00,

        8, // 0x60 '`'
        0x00,0x00,0x00,0x10,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x02,0x02,0x3E,0x42,0x42,0x3E,0x00,0x00,0x00,

        8, // 0x62 'b'
        0x00,0x00,0x00,0x40,0x40,0x40,0x5C,0x62,0x42,0x42,0x42,0x42,0x7C,0x00,0x00,0x00,

        8, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x40,0x40,0x40,0x42,0x3C,0x00,0x00,0x00,

        8, // 0x64 'd'
        0x00,0x00,0x00,0x02,0x02,0x02,0x3E,0x42,0x42,0x42,0x42,0x46,0x3A,0x00,0x00,0x00,

        8, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x7E,0x40,0x42,0x3C,0x00,0x00,0x00,

        6, // 0x66 'f'
        0x00,0x00,0x00,0x1C,0x20,0x20,0x78,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,

        8, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x42,0x42,0x42,0x42,0x46,0x3A,0x02,0x02,0x3C,

        8, // 0x68 'h'
        0x00,0x00,0x00,0x40,0x40,0x40,0x5C,0x62,0x42,0x42,0x42,0x42,0x42,0x00,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x00,0x00,0x40,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        4, // 0x6A 'j'
        0x00,0x00,0x00,0x20,0x00,0x00,0x60,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0xC0,

        7, // 0x6B 'k'
        0x00,0x00,0x00,0x40,0x40,0x40,0x44,0x48,0x50,0x60,0x50,0x48,0x44,0x00,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        11, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x59,0x80,0x66,0x40,0x44,0x40,0x44,0x40,0x44,0x40,0x44,0x40,0x44,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0x62,0x42,0x42,0x42,0x42,0x42,0x00,0x00,0x00,

        8, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00,0x00,

        8, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0x62,0x42,0x42,0x42,0x42,0x7C,0x40,0x40,0x40,

        8, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x42,0x42,0x42,0x42,0x46,0x3A,0x02,0x02,0x02,

        5, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x00,0x58,0x60,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        7, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x40,0x40,0x38,0x04,0x04,0x78,0x00,0x00,0x00,

        6, // 0x74 't'
        0x00,0x00,0x00,0x00,0x20,0x20,0x78,0x20,0x20,0x20,0x20,0x20,0x18,0x00,0x00,0x00,

        8, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x42,0x42,0x42,0x42,0x46,0x3A,0x00,0x00,0x00,

        8, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x42,0x24,0x24,0x24,0x18,0x18,0x00,0x00,0x00,

        11, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x44,0x40,0x44,0x40,0x2A,0x80,0x2A,0x80,0x2A,0x80,0x11,0x00,0x11,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x00,0x44,0x44,0x28,0x10,0x28,0x44,0x44,0x00,0x00,0x00,

        8, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x42,0x24,0x24,0x24,0x18,0x18,0x10,0x10,0x20,

        7, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x04,0x08,0x10,0x20,0x40,0x7C,0x00,0x00,0x00,

        8, // 0x7B '{'
        0x00,0x00,0x00,0x0C,0x10,0x10,0x10,0x10,0x60,0x10,0x10,0x10,0x10,0x10,0x0C,0x00,

        7, // 0x7C '|'
        0x00,0x00,0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,

        8, // 0x7D '}'
        0x00,0x00,0x00,0x30,0x08,0x08,0x08,0x08,0x06,0x08,0x08,0x08,0x08,0x08,0x30,0x00,

        11, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x80,0x4C,0x80,0x43,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xF0,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x3F,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana16_bold[] = 
    {
        16, 4, 32, 128-32,
        0x00,0x00,0x11,0x00,0x22,0x00,0x33,0x00,0x54,0x00,0x75,0x00,0xA6,0x00,0xC7,0x00,0xD8,0x00,
        0xE9,0x00,0xFA,0x00,0x1B,0x01,0x3C,0x01,0x4D,0x01,0x5E,0x01,0x6F,0x01,0x90,0x01,0xB1,0x01,
        0xD2,0x01,0xF3,0x01,0x14,0x02,0x35,0x02,0x56,0x02,0x77,0x02,0x98,0x02,0xB9,0x02,0xDA,0x02,
        0xEB,0x02,0xFC,0x02,0x1D,0x03,0x3E,0x03,0x5F,0x03,0x70,0x03,0x91,0x03,0xB2,0x03,0xD3,0x03,
        0xF4,0x03,0x15,0x04,0x36,0x04,0x57,0x04,0x78,0x04,0x99,0x04,0xAA,0x04,0xBB,0x04,0xDC,0x04,
        0xED,0x04,0x0E,0x05,0x2F,0x05,0x50,0x05,0x71,0x05,0x92,0x05,0xB3,0x05,0xD4,0x05,0xE5,0x05,
        0x06,0x06,0x27,0x06,0x48,0x06,0x69,0x06,0x8A,0x06,0xAB,0x06,0xBC,0x06,0xDD,0x06,0xEE,0x06,
        0x0F,0x07,0x30,0x07,0x51,0x07,0x72,0x07,0x93,0x07,0xA4,0x07,0xC5,0x07,0xE6,0x07,0xF7,0x07,
        0x18,0x08,0x39,0x08,0x4A,0x08,0x5B,0x08,0x6C,0x08,0x7D,0x08,0x9E,0x08,0xBF,0x08,0xE0,0x08,
        0x01,0x09,0x22,0x09,0x33,0x09,0x44,0x09,0x55,0x09,0x76,0x09,0x97,0x09,0xB8,0x09,0xD9,0x09,
        0xFA,0x09,0x0B,0x0A,0x2C,0x0A,0x3D,0x0A,0x5E,0x0A,0x7F,0x0A,

        4, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x30,0x30,0x30,0x00,0x30,0x30,0x00,0x00,0x00,

        7, // 0x22 '"'
        0x00,0x00,0x00,0x6C,0x6C,0x6C,0x6C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x09,0x00,0x09,0x00,0x3F,0x80,0x3F,0x80,0x12,0x00,0x7F,0x00,0x7F,0x00,0x24,0x00,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x24 '$'
        0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x3E,0x00,0x69,0x00,0x68,0x00,0x78,0x00,0x3E,0x00,0x0F,0x00,0x0B,0x00,0x4B,0x00,0x3E,0x00,0x08,0x00,0x08,0x00,0x00,0x00,

        17, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x20,0x00,0x66,0x20,0x00,0x66,0x40,0x00,0x66,0x5E,0x00,0x66,0xB3,0x00,0x3D,0x33,0x00,0x01,0x33,0x00,0x02,0x33,0x00,0x02,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x00,0x66,0x00,0x66,0x00,0x66,0xC0,0x3C,0xC0,0x66,0x80,0x63,0x00,0x63,0x80,0x3C,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x27 '''
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x00,0x00,0x00,0x0C,0x18,0x30,0x30,0x60,0x60,0x60,0x60,0x60,0x30,0x30,0x18,0x0C,

        7, // 0x29 ')'
        0x00,0x00,0x00,0x60,0x30,0x18,0x18,0x0C,0x0C,0x0C,0x0C,0x0C,0x18,0x18,0x30,0x60,

        9, // 0x2A '*'
        0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x49,0x00,0x2A,0x00,0x1C,0x00,0x2A,0x00,0x49,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x3F,0x80,0x04,0x00,0x04,0x00,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x60,0x60,0xC0,0xC0,

        7, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,

        9, // 0x2F '/'
        0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x30,0x00,0x30,0x00,0x60,0x00,0x60,0x00,0x00,0x00,

        9, // 0x30 '0'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x31 '1'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x3C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x32 '2'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x03,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x30,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x33 '3'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x03,0x00,0x0E,0x00,0x03,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x34 '4'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x0E,0x00,0x16,0x00,0x26,0x00,0x46,0x00,0x7F,0x80,0x06,0x00,0x06,0x00,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x35 '5'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x30,0x00,0x30,0x00,0x3E,0x00,0x03,0x00,0x03,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x36 '6'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x30,0x00,0x60,0x00,0x7E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x37 '7'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x38 '8'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x39 '9'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3F,0x00,0x03,0x00,0x06,0x00,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,

        5, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x38,0x30,0x30,0x60,0x60,

        11, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x03,0x00,0x0C,0x00,0x30,0x00,0x40,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x80,0x00,0x00,0x00,0x00,0x3F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x18,0x00,0x06,0x00,0x01,0x80,0x00,0x40,0x01,0x80,0x06,0x00,0x18,0x00,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x3F '?'
        0x00,0x00,0x00,0x00,0x3C,0x66,0x06,0x0C,0x18,0x18,0x00,0x18,0x18,0x00,0x00,0x00,

        13, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x30,0x60,0x27,0xA0,0x4D,0x90,0x4D,0x90,0x4D,0x90,0x4D,0x90,0x27,0xE0,0x30,0x00,0x0F,0x80,0x00,0x00,0x00,0x00,

        10, // 0x41 'A'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x0C,0x00,0x1E,0x00,0x1E,0x00,0x33,0x00,0x33,0x00,0x7F,0x80,0x61,0x80,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x42 'B'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7F,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x61,0x80,0x60,0x00,0x60,0x00,0x60,0x00,0x61,0x80,0x31,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x63,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x45 'E'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x46 'F'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x61,0x80,0x60,0x00,0x60,0x00,0x63,0x80,0x61,0x80,0x31,0x80,0x1F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x7F,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x49 'I'
        0x00,0x00,0x00,0x00,0x78,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x78,0x00,0x00,0x00,

        7, // 0x4A 'J'
        0x00,0x00,0x00,0x00,0x7C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0xF8,0x00,0x00,0x00,

        9, // 0x4B 'K'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x66,0x00,0x6C,0x00,0x78,0x00,0x78,0x00,0x6C,0x00,0x66,0x00,0x63,0x00,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x4C 'L'
        0x00,0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x7F,0x00,0x00,0x00,

        12, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0xE0,0x70,0xE0,0x59,0x60,0x59,0x60,0x4E,0x60,0x4E,0x60,0x44,0x60,0x44,0x60,0x40,0x60,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x80,0x70,0x80,0x58,0x80,0x58,0x80,0x4C,0x80,0x46,0x80,0x46,0x80,0x43,0x80,0x43,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x50 'P'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7E,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x03,0x00,0x01,0xC0,0x00,0x00,

        9, // 0x52 'R'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7E,0x00,0x6C,0x00,0x66,0x00,0x63,0x00,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x53 'S'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x70,0x00,0x3E,0x00,0x07,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x54 'T'
        0x00,0x00,0x00,0x00,0xFF,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,0x00,

        10, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x56 'V'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x00,0x33,0x00,0x1E,0x00,0x1E,0x00,0x0C,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        14, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x63,0x18,0x63,0x18,0x33,0x30,0x37,0xB0,0x34,0xB0,0x1C,0xE0,0x18,0x60,0x18,0x60,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x58 'X'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x33,0x00,0x33,0x00,0x1E,0x00,0x0C,0x00,0x1E,0x00,0x33,0x00,0x33,0x00,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x59 'Y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x33,0x00,0x1E,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x5A 'Z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x30,0x00,0x30,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5B '['
        0x00,0x00,0x00,0x78,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x78,0x00,

        9, // 0x5C '\'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x30,0x00,0x30,0x00,0x18,0x00,0x18,0x00,0x0C,0x00,0x0C,0x00,0x06,0x00,0x06,0x00,0x03,0x00,0x03,0x00,0x00,0x00,

        6, // 0x5D ']'
        0x00,0x00,0x00,0x78,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x78,0x00,

        10, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x1E,0x00,0x33,0x00,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x00,

        9, // 0x60 '`'
        0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x03,0x00,0x3F,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x62 'b'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x6E,0x00,0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x63,0x60,0x60,0x60,0x63,0x3E,0x00,0x00,0x00,

        9, // 0x64 'd'
        0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x3F,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x7F,0x00,0x60,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x66 'f'
        0x00,0x00,0x00,0x38,0x60,0x60,0xF8,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        9, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3B,0x00,0x03,0x00,0x03,0x00,0x3E,0x00,

        9, // 0x68 'h'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x6E,0x00,0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x69 'i'
        0x00,0x00,0x00,0x60,0x60,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        5, // 0x6A 'j'
        0x00,0x00,0x00,0x30,0x30,0x00,0x70,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0xE0,

        8, // 0x6B 'k'
        0x00,0x00,0x00,0x60,0x60,0x60,0x66,0x6C,0x78,0x78,0x6C,0x66,0x63,0x00,0x00,0x00,

        4, // 0x6C 'l'
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        14, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x70,0x73,0x98,0x63,0x18,0x63,0x18,0x63,0x18,0x63,0x18,0x63,0x18,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x00,0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x00,0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x7E,0x00,0x60,0x00,0x60,0x00,0x60,0x00,

        9, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3B,0x00,0x03,0x00,0x03,0x00,0x03,0x00,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x00,0x6C,0x7C,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        8, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x60,0x70,0x3C,0x0E,0x06,0x7C,0x00,0x00,0x00,

        6, // 0x74 't'
        0x00,0x00,0x00,0x00,0x60,0x60,0xF8,0x60,0x60,0x60,0x60,0x60,0x38,0x00,0x00,0x00,

        9, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x36,0x00,0x1C,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x66,0x60,0x66,0x60,0x66,0x60,0x69,0x60,0x39,0xC0,0x30,0xC0,0x30,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x1C,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x36,0x00,0x1C,0x00,0x1C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,

        8, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x06,0x0C,0x18,0x30,0x60,0x7E,0x00,0x00,0x00,

        9, // 0x7B '{'
        0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x70,0x00,0x18,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x07,0x00,0x00,0x00,

        8, // 0x7C '|'
        0x00,0x00,0x00,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x00,

        9, // 0x7D '}'
        0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x0C,0x00,0x07,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x70,0x00,0x00,0x00,

        11, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x40,0x44,0x40,0x44,0x40,0x43,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xF0,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x20,0x10,0x3F,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana17[] = 
    {
        17, 4, 32, 128-32,
        0x00,0x00,0x12,0x00,0x24,0x00,0x36,0x00,0x59,0x00,0x7C,0x00,0x9F,0x00,0xC2,0x00,0xD4,0x00,
        0xE6,0x00,0xF8,0x00,0x1B,0x01,0x3E,0x01,0x50,0x01,0x62,0x01,0x74,0x01,0x86,0x01,0xA9,0x01,
        0xCC,0x01,0xEF,0x01,0x12,0x02,0x35,0x02,0x58,0x02,0x7B,0x02,0x9E,0x02,0xC1,0x02,0xE4,0x02,
        0xF6,0x02,0x08,0x03,0x2B,0x03,0x4E,0x03,0x71,0x03,0x83,0x03,0xA6,0x03,0xC9,0x03,0xEC,0x03,
        0x0F,0x04,0x32,0x04,0x55,0x04,0x67,0x04,0x8A,0x04,0xAD,0x04,0xBF,0x04,0xD1,0x04,0xF4,0x04,
        0x06,0x05,0x29,0x05,0x4C,0x05,0x6F,0x05,0x81,0x05,0xA4,0x05,0xC7,0x05,0xEA,0x05,0x0D,0x06,
        0x30,0x06,0x53,0x06,0x76,0x06,0x99,0x06,0xBC,0x06,0xDF,0x06,0xF1,0x06,0x03,0x07,0x15,0x07,
        0x38,0x07,0x5B,0x07,0x7E,0x07,0x90,0x07,0xB3,0x07,0xC5,0x07,0xE8,0x07,0xFA,0x07,0x0C,0x08,
        0x2F,0x08,0x52,0x08,0x64,0x08,0x76,0x08,0x88,0x08,0x9A,0x08,0xBD,0x08,0xE0,0x08,0x03,0x09,
        0x26,0x09,0x49,0x09,0x5B,0x09,0x6D,0x09,0x7F,0x09,0xA2,0x09,0xB4,0x09,0xD7,0x09,0xFA,0x09,
        0x0C,0x0A,0x1E,0x0A,0x41,0x0A,0x53,0x0A,0x76,0x0A,0x99,0x0A,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x20,0x00,0x00,0x00,

        6, // 0x22 '"'
        0x00,0x00,0x00,0x48,0x48,0x48,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x80,0x04,0x80,0x09,0x00,0x3F,0xC0,0x09,0x00,0x12,0x00,0x7F,0x80,0x12,0x00,0x24,0x00,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x24 '$'
        0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x3E,0x00,0x49,0x00,0x48,0x00,0x48,0x00,0x3E,0x00,0x09,0x00,0x09,0x00,0x49,0x00,0x3E,0x00,0x08,0x00,0x08,0x00,0x00,0x00,

        15, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x20,0x44,0x40,0x44,0x80,0x44,0x80,0x45,0x38,0x39,0x44,0x02,0x44,0x04,0x44,0x04,0x44,0x08,0x38,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x00,0x42,0x00,0x42,0x00,0x44,0x00,0x38,0x80,0x44,0x80,0x42,0x80,0x41,0x00,0x22,0x80,0x1C,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x27 '''
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x28 '('
        0x00,0x00,0x00,0x08,0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,0x08,

        6, // 0x29 ')'
        0x00,0x00,0x00,0x40,0x20,0x10,0x10,0x08,0x08,0x08,0x08,0x08,0x08,0x10,0x10,0x20,0x40,

        9, // 0x2A '*'
        0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x49,0x00,0x2A,0x00,0x1C,0x00,0x2A,0x00,0x49,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x7F,0xC0,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x40,0x00,

        7, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,

        6, // 0x2F '/'
        0x00,0x00,0x00,0x04,0x08,0x08,0x08,0x10,0x10,0x20,0x20,0x20,0x40,0x40,0x80,0x80,0x00,

        9, // 0x30 '0'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x22,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x31 '1'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x38,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x32 '2'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x41,0x00,0x01,0x00,0x01,0x00,0x02,0x00,0x0C,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x33 '3'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x41,0x00,0x01,0x00,0x02,0x00,0x1C,0x00,0x02,0x00,0x01,0x00,0x01,0x00,0x42,0x00,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x34 '4'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0x00,0x06,0x00,0x0A,0x00,0x12,0x00,0x22,0x00,0x42,0x00,0x7F,0x80,0x02,0x00,0x02,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x35 '5'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x7C,0x00,0x02,0x00,0x01,0x00,0x01,0x00,0x42,0x00,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x36 '6'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x00,0x30,0x00,0x20,0x00,0x40,0x00,0x7C,0x00,0x42,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x37 '7'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x01,0x00,0x02,0x00,0x02,0x00,0x04,0x00,0x04,0x00,0x08,0x00,0x10,0x00,0x10,0x00,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x38 '8'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x3E,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x39 '9'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x22,0x00,0x41,0x00,0x41,0x00,0x21,0x00,0x1F,0x00,0x01,0x00,0x02,0x00,0x06,0x00,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,

        6, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x40,0x00,

        11, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x80,0x06,0x00,0x18,0x00,0x60,0x00,0x60,0x00,0x18,0x00,0x06,0x00,0x01,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xC0,0x00,0x00,0x00,0x00,0x3F,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0xC0,0x00,0xC0,0x03,0x00,0x0C,0x00,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x3F '?'
        0x00,0x00,0x00,0x00,0x3C,0x42,0x02,0x02,0x0C,0x10,0x10,0x00,0x10,0x10,0x00,0x00,0x00,

        14, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xC0,0x18,0x20,0x20,0x10,0x27,0xC8,0x48,0x48,0x48,0x48,0x48,0x48,0x48,0x48,0x27,0xF0,0x20,0x00,0x18,0x00,0x07,0xC0,0x00,0x00,

        10, // 0x41 'A'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x12,0x00,0x12,0x00,0x21,0x00,0x21,0x00,0x21,0x00,0x7F,0x80,0x40,0x80,0x80,0x40,0x80,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x42 'B'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x7E,0x00,0x41,0x00,0x40,0x80,0x40,0x80,0x41,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0x80,0x20,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x20,0x00,0x30,0x80,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x41,0x80,0x40,0x80,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x80,0x41,0x80,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x45 'E'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x7F,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x46 'F'
        0x00,0x00,0x00,0x00,0x7F,0x40,0x40,0x40,0x7E,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        11, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0xC0,0x20,0x40,0x40,0x00,0x40,0x00,0x43,0xC0,0x40,0x40,0x20,0x40,0x30,0x40,0x0F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x7F,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x00,0x00,0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,0x00,

        6, // 0x4A 'J'
        0x00,0x00,0x00,0x00,0x38,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0xF0,0x00,0x00,0x00,

        10, // 0x4B 'K'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x42,0x00,0x44,0x00,0x48,0x00,0x50,0x00,0x68,0x00,0x44,0x00,0x42,0x00,0x41,0x00,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x4C 'L'
        0x00,0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7F,0x00,0x00,0x00,

        11, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x60,0xC0,0x51,0x40,0x51,0x40,0x4A,0x40,0x4A,0x40,0x44,0x40,0x44,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x80,0x60,0x80,0x50,0x80,0x48,0x80,0x48,0x80,0x44,0x80,0x44,0x80,0x42,0x80,0x41,0x80,0x41,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x00,0x31,0x80,0x20,0x80,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x20,0x80,0x31,0x80,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x50 'P'
        0x00,0x00,0x00,0x00,0x7C,0x42,0x41,0x41,0x42,0x7C,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        11, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x00,0x31,0x80,0x20,0x80,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x20,0x80,0x31,0x80,0x0E,0x00,0x02,0x00,0x02,0x00,0x01,0xC0,

        10, // 0x52 'R'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x42,0x00,0x42,0x00,0x42,0x00,0x44,0x00,0x78,0x00,0x44,0x00,0x42,0x00,0x41,0x00,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x53 'S'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x20,0x80,0x40,0x00,0x40,0x00,0x38,0x00,0x07,0x00,0x00,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x54 'T'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x56 'V'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x40,0x80,0x40,0x40,0x80,0x40,0x80,0x21,0x00,0x21,0x00,0x21,0x00,0x12,0x00,0x12,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        15, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x04,0x41,0x04,0x22,0x88,0x22,0x88,0x22,0x88,0x14,0x50,0x14,0x50,0x14,0x50,0x08,0x20,0x08,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x58 'X'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x80,0x21,0x00,0x12,0x00,0x12,0x00,0x0C,0x00,0x0C,0x00,0x12,0x00,0x12,0x00,0x21,0x00,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x59 'Y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0x41,0x00,0x22,0x00,0x22,0x00,0x14,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x5A 'Z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x00,0x80,0x01,0x00,0x02,0x00,0x04,0x00,0x08,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x5B '['
        0x00,0x00,0x00,0x3C,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x3C,

        6, // 0x5C '\'
        0x00,0x00,0x00,0x80,0x40,0x40,0x40,0x20,0x20,0x10,0x10,0x10,0x08,0x08,0x08,0x04,0x00,

        6, // 0x5D ']'
        0x00,0x00,0x00,0x78,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x78,

        11, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x0A,0x00,0x11,0x00,0x20,0x80,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x00,0x00,

        9, // 0x60 '`'
        0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x22,0x02,0x3E,0x42,0x42,0x46,0x3A,0x00,0x00,0x00,

        9, // 0x62 'b'
        0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x5C,0x00,0x62,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x42,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x22,0x40,0x40,0x40,0x40,0x22,0x1C,0x00,0x00,0x00,

        9, // 0x64 'd'
        0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x01,0x00,0x01,0x00,0x1F,0x00,0x21,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x23,0x00,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x24,0x42,0x7E,0x40,0x40,0x22,0x1C,0x00,0x00,0x00,

        6, // 0x66 'f'
        0x00,0x00,0x00,0x1C,0x20,0x20,0x7C,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,

        9, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x21,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x23,0x00,0x1D,0x00,0x01,0x00,0x22,0x00,0x1C,0x00,

        9, // 0x68 'h'
        0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x5E,0x00,0x61,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x00,0x00,0x00,0x40,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        5, // 0x6A 'j'
        0x00,0x00,0x00,0x00,0x10,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0xE0,

        8, // 0x6B 'k'
        0x00,0x00,0x00,0x40,0x40,0x40,0x42,0x44,0x48,0x50,0x70,0x48,0x44,0x42,0x00,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        13, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0xE0,0x63,0x10,0x42,0x10,0x42,0x10,0x42,0x10,0x42,0x10,0x42,0x10,0x42,0x10,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5E,0x00,0x61,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x22,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0x00,0x62,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x42,0x00,0x7C,0x00,0x40,0x00,0x40,0x00,0x40,0x00,

        9, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x21,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x23,0x00,0x1D,0x00,0x01,0x00,0x01,0x00,0x01,0x00,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0x60,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        8, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x40,0x30,0x0C,0x02,0x42,0x3C,0x00,0x00,0x00,

        6, // 0x74 't'
        0x00,0x00,0x00,0x00,0x20,0x20,0x7C,0x20,0x20,0x20,0x20,0x20,0x20,0x1C,0x00,0x00,0x00,

        9, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x43,0x00,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x42,0x24,0x24,0x24,0x18,0x18,0x18,0x00,0x00,0x00,

        11, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x44,0x40,0x44,0x40,0x2A,0x80,0x2A,0x80,0x2A,0x80,0x2A,0x80,0x11,0x00,0x11,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x22,0x00,0x14,0x00,0x08,0x00,0x08,0x00,0x14,0x00,0x22,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x42,0x24,0x24,0x24,0x18,0x18,0x18,0x10,0x10,0x20,

        8, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x02,0x04,0x08,0x10,0x20,0x40,0x7E,0x00,0x00,0x00,

        9, // 0x7B '{'
        0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x60,0x00,0x10,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x07,0x00,

        6, // 0x7C '|'
        0x00,0x00,0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,

        9, // 0x7D '}'
        0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x04,0x00,0x03,0x00,0x04,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x70,0x00,

        11, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x40,0x44,0x40,0x44,0x40,0x43,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        14, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xF8,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x3F,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana17_bold[] = 
    {
        17, 4, 32, 128-32,
        0x00,0x00,0x12,0x00,0x24,0x00,0x36,0x00,0x59,0x00,0x7C,0x00,0xB0,0x00,0xD3,0x00,0xE5,0x00,
        0xF7,0x00,0x09,0x01,0x2C,0x01,0x4F,0x01,0x61,0x01,0x73,0x01,0x85,0x01,0xA8,0x01,0xCB,0x01,
        0xEE,0x01,0x11,0x02,0x34,0x02,0x57,0x02,0x7A,0x02,0x9D,0x02,0xC0,0x02,0xE3,0x02,0x06,0x03,
        0x18,0x03,0x2A,0x03,0x4D,0x03,0x70,0x03,0x93,0x03,0xB6,0x03,0xD9,0x03,0xFC,0x03,0x1F,0x04,
        0x42,0x04,0x65,0x04,0x88,0x04,0xAB,0x04,0xCE,0x04,0xF1,0x04,0x03,0x05,0x15,0x05,0x38,0x05,
        0x5B,0x05,0x7E,0x05,0xA1,0x05,0xC4,0x05,0xE7,0x05,0x0A,0x06,0x2D,0x06,0x50,0x06,0x73,0x06,
        0x96,0x06,0xB9,0x06,0xDC,0x06,0xFF,0x06,0x22,0x07,0x45,0x07,0x57,0x07,0x7A,0x07,0x8C,0x07,
        0xAF,0x07,0xD2,0x07,0xF5,0x07,0x18,0x08,0x3B,0x08,0x4D,0x08,0x70,0x08,0x93,0x08,0xA5,0x08,
        0xC8,0x08,0xEB,0x08,0xFD,0x08,0x0F,0x09,0x32,0x09,0x44,0x09,0x67,0x09,0x8A,0x09,0xAD,0x09,
        0xD0,0x09,0xF3,0x09,0x05,0x0A,0x17,0x0A,0x29,0x0A,0x4C,0x0A,0x6F,0x0A,0x92,0x0A,0xB5,0x0A,
        0xD8,0x0A,0xEA,0x0A,0x0D,0x0B,0x1F,0x0B,0x42,0x0B,0x65,0x0B,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x00,0x30,0x30,0x00,0x00,0x00,

        8, // 0x22 '"'
        0x00,0x00,0x00,0x66,0x66,0x66,0x66,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x40,0x04,0x40,0x3F,0xE0,0x3F,0xE0,0x08,0x80,0x11,0x00,0x7F,0xC0,0x7F,0xC0,0x22,0x00,0x22,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x24 '$'
        0x00,0x00,0x00,0x00,0x04,0x00,0x04,0x00,0x1F,0x00,0x34,0x80,0x64,0x00,0x74,0x00,0x3C,0x00,0x0F,0x00,0x0B,0x80,0x09,0x80,0x4B,0x00,0x3E,0x00,0x08,0x00,0x08,0x00,0x00,0x00,

        18, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x08,0x00,0x66,0x10,0x00,0x66,0x20,0x00,0x66,0x2F,0x00,0x66,0x59,0x80,0x66,0x99,0x80,0x3D,0x19,0x80,0x01,0x19,0x80,0x02,0x19,0x80,0x04,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x33,0x00,0x33,0x00,0x36,0x00,0x1C,0x60,0x36,0x60,0x63,0x60,0x61,0xC0,0x31,0xC0,0x1F,0x60,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x27 '''
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x28 '('
        0x00,0x00,0x00,0x0C,0x18,0x30,0x30,0x60,0x60,0x60,0x60,0x60,0x60,0x30,0x30,0x18,0x0C,

        8, // 0x29 ')'
        0x00,0x00,0x00,0x30,0x18,0x0C,0x0C,0x06,0x06,0x06,0x06,0x06,0x06,0x0C,0x0C,0x18,0x30,

        10, // 0x2A '*'
        0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x49,0x00,0x2A,0x00,0x1C,0x00,0x2A,0x00,0x49,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x7F,0xC0,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x60,0x60,0xC0,0xC0,0x00,

        7, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x00,0x00,0x00,

        10, // 0x2F '/'
        0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x80,0x01,0x80,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x30,0x00,0x30,0x00,0x60,0x00,0x60,0x00,0x00,0x00,

        10, // 0x30 '0'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x31 '1'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x3C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x32 '2'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x61,0x80,0x61,0x80,0x01,0x80,0x03,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x30,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x33 '3'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x61,0x80,0x61,0x80,0x01,0x80,0x0F,0x00,0x03,0x00,0x01,0x80,0x61,0x80,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x34 '4'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x07,0x00,0x0B,0x00,0x13,0x00,0x23,0x00,0x43,0x00,0x7F,0xC0,0x03,0x00,0x03,0x00,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x35 '5'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x80,0x30,0x00,0x30,0x00,0x3E,0x00,0x03,0x00,0x01,0x80,0x01,0x80,0x61,0x80,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x36 '6'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x38,0x00,0x30,0x00,0x6E,0x00,0x73,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x37 '7'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x01,0x80,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x38 '8'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x3F,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x39 '9'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x80,0x1D,0x80,0x03,0x00,0x07,0x00,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x00,0x00,0x30,0x30,0x30,0x00,0x00,0x00,

        6, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x00,0x00,0x38,0x30,0x30,0x60,0x60,0x00,

        12, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x03,0x00,0x0C,0x00,0x30,0x00,0x40,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x18,0x00,0x06,0x00,0x01,0x80,0x00,0x40,0x01,0x80,0x06,0x00,0x18,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3F '?'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x03,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        14, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0xC0,0x18,0x20,0x20,0x10,0x27,0xC8,0x4C,0xC8,0x4C,0xC8,0x4C,0xC8,0x4C,0xC8,0x27,0xF0,0x20,0x00,0x18,0x00,0x07,0xC0,0x00,0x00,

        11, // 0x41 'A'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x00,0x0E,0x00,0x0E,0x00,0x1B,0x00,0x1B,0x00,0x31,0x80,0x3F,0x80,0x31,0x80,0x60,0xC0,0x60,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x42 'B'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x7F,0x00,0x61,0x80,0x60,0xC0,0x60,0xC0,0x61,0x80,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x30,0xC0,0x30,0xC0,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x30,0xC0,0x30,0xC0,0x0F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x61,0x80,0x61,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x61,0x80,0x61,0x80,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x45 'E'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x80,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x46 'F'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x30,0xC0,0x30,0xC0,0x60,0x00,0x60,0x00,0x63,0xC0,0x60,0xC0,0x30,0xC0,0x30,0xC0,0x0F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x7F,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x49 'I'
        0x00,0x00,0x00,0x00,0x7E,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x7E,0x00,0x00,0x00,

        8, // 0x4A 'J'
        0x00,0x00,0x00,0x00,0x3E,0x06,0x06,0x06,0x06,0x06,0x06,0x06,0x0C,0xF8,0x00,0x00,0x00,

        11, // 0x4B 'K'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x61,0x80,0x63,0x00,0x66,0x00,0x6C,0x00,0x7C,0x00,0x76,0x00,0x63,0x00,0x61,0x80,0x60,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x4C 'L'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x70,0x70,0x70,0x70,0xF0,0x58,0xB0,0x59,0xB0,0x4D,0x30,0x4F,0x30,0x46,0x30,0x46,0x30,0x40,0x30,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x40,0x70,0x40,0x58,0x40,0x4C,0x40,0x4C,0x40,0x46,0x40,0x43,0x40,0x43,0x40,0x41,0xC0,0x40,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0xC0,0x30,0xC0,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x30,0xC0,0x30,0xC0,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x50 'P'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x63,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x00,0x7E,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0xC0,0x30,0xC0,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x30,0xC0,0x30,0xC0,0x0F,0x80,0x03,0x00,0x03,0x00,0x01,0xE0,

        11, // 0x52 'R'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x61,0x80,0x60,0xC0,0x60,0xC0,0x61,0x80,0x7F,0x00,0x63,0x00,0x61,0x80,0x60,0xC0,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x53 'S'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x61,0x80,0x60,0x00,0x3E,0x00,0x1F,0x00,0x01,0x80,0x61,0x80,0x63,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x54 'T'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x56 'V'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x31,0x80,0x31,0x80,0x1B,0x00,0x1B,0x00,0x0E,0x00,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        16, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x86,0x61,0x86,0x63,0xC6,0x32,0x4C,0x36,0x6C,0x36,0x6C,0x34,0x2C,0x1C,0x38,0x18,0x18,0x18,0x18,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x58 'X'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x31,0x80,0x31,0x80,0x1B,0x00,0x0E,0x00,0x0E,0x00,0x1B,0x00,0x31,0x80,0x31,0x80,0x60,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x59 'Y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xC0,0x61,0x80,0x61,0x80,0x33,0x00,0x1E,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x5A 'Z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x01,0x80,0x03,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x5B '['
        0x00,0x00,0x00,0x3E,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x3E,

        10, // 0x5C '\'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x30,0x00,0x30,0x00,0x18,0x00,0x18,0x00,0x0C,0x00,0x06,0x00,0x06,0x00,0x03,0x00,0x03,0x00,0x01,0x80,0x01,0x80,0x00,0x00,

        8, // 0x5D ']'
        0x00,0x00,0x00,0x7C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x7C,

        12, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x0E,0x00,0x1B,0x00,0x31,0x80,0x60,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC0,0x00,0x00,

        10, // 0x60 '`'
        0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x03,0x00,0x03,0x00,0x3F,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x62 'b'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x6E,0x00,0x73,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x33,0x60,0x60,0x60,0x60,0x33,0x1E,0x00,0x00,0x00,

        10, // 0x64 'd'
        0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x80,0x01,0x80,0x01,0x80,0x1F,0x80,0x31,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x80,0x1D,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x33,0x00,0x63,0x00,0x7F,0x00,0x60,0x00,0x60,0x00,0x33,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x66 'f'
        0x00,0x00,0x00,0x1C,0x30,0x30,0x7C,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x00,0x00,0x00,

        10, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x80,0x31,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x80,0x1D,0x80,0x01,0x80,0x03,0x00,0x3E,0x00,

        10, // 0x68 'h'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x6F,0x00,0x71,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x69 'i'
        0x00,0x00,0x00,0x60,0x60,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        6, // 0x6A 'j'
        0x00,0x00,0x00,0x18,0x18,0x00,0x38,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0xF0,

        9, // 0x6B 'k'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x63,0x00,0x66,0x00,0x6C,0x00,0x78,0x00,0x7C,0x00,0x66,0x00,0x63,0x00,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x6C 'l'
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        14, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x70,0x73,0x98,0x63,0x18,0x63,0x18,0x63,0x18,0x63,0x18,0x63,0x18,0x63,0x18,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6F,0x00,0x71,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x00,0x73,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x00,0x7E,0x00,0x60,0x00,0x60,0x00,0x60,0x00,

        10, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x80,0x31,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x80,0x1D,0x80,0x01,0x80,0x01,0x80,0x01,0x80,

        7, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x7E,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        8, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x62,0x60,0x7C,0x3E,0x06,0x46,0x3C,0x00,0x00,0x00,

        6, // 0x74 't'
        0x00,0x00,0x00,0x00,0x60,0x60,0xFC,0x60,0x60,0x60,0x60,0x60,0x60,0x3C,0x00,0x00,0x00,

        10, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x80,0x3D,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0x1C,0x00,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        14, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x63,0x18,0x33,0x30,0x37,0xB0,0x34,0xB0,0x1C,0xE0,0x1C,0xE0,0x0C,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x1C,0x00,0x1C,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0x1C,0x00,0x1C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,

        8, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x06,0x0C,0x18,0x18,0x30,0x60,0x7E,0x00,0x00,0x00,

        10, // 0x7B '{'
        0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x70,0x00,0x18,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x07,0x80,

        8, // 0x7C '|'
        0x00,0x00,0x00,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,

        10, // 0x7D '}'
        0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x06,0x00,0x03,0x80,0x06,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x78,0x00,

        12, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x20,0x24,0x20,0x46,0x20,0x42,0x40,0x41,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        14, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xF8,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x3F,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana18[] = 
    {
        18, 4, 32, 128-32,
        0x00,0x00,0x13,0x00,0x26,0x00,0x39,0x00,0x5E,0x00,0x83,0x00,0xA8,0x00,0xCD,0x00,0xE0,0x00,
        0xF3,0x00,0x06,0x01,0x2B,0x01,0x50,0x01,0x63,0x01,0x76,0x01,0x89,0x01,0x9C,0x01,0xC1,0x01,
        0xE6,0x01,0x0B,0x02,0x30,0x02,0x55,0x02,0x7A,0x02,0x9F,0x02,0xC4,0x02,0xE9,0x02,0x0E,0x03,
        0x21,0x03,0x34,0x03,0x59,0x03,0x7E,0x03,0xA3,0x03,0xB6,0x03,0xDB,0x03,0x00,0x04,0x25,0x04,
        0x4A,0x04,0x6F,0x04,0x94,0x04,0xB9,0x04,0xDE,0x04,0x03,0x05,0x16,0x05,0x29,0x05,0x4E,0x05,
        0x61,0x05,0x86,0x05,0xAB,0x05,0xD0,0x05,0xF5,0x05,0x1A,0x06,0x3F,0x06,0x64,0x06,0x89,0x06,
        0xAE,0x06,0xD3,0x06,0xF8,0x06,0x1D,0x07,0x42,0x07,0x67,0x07,0x7A,0x07,0x8D,0x07,0xA0,0x07,
        0xC5,0x07,0xEA,0x07,0x0F,0x08,0x34,0x08,0x59,0x08,0x6C,0x08,0x91,0x08,0xB6,0x08,0xC9,0x08,
        0xEE,0x08,0x13,0x09,0x26,0x09,0x39,0x09,0x5E,0x09,0x71,0x09,0x96,0x09,0xBB,0x09,0xE0,0x09,
        0x05,0x0A,0x2A,0x0A,0x3D,0x0A,0x50,0x0A,0x63,0x0A,0x88,0x0A,0xAD,0x0A,0xD2,0x0A,0xF7,0x0A,
        0x1C,0x0B,0x41,0x0B,0x66,0x0B,0x79,0x0B,0x9E,0x0B,0xC3,0x0B,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x20,0x20,0x00,0x00,0x00,

        7, // 0x22 '"'
        0x00,0x00,0x00,0x48,0x48,0x48,0x48,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x80,0x04,0x80,0x09,0x00,0x3F,0xC0,0x09,0x00,0x11,0x00,0x12,0x00,0x7F,0x80,0x12,0x00,0x24,0x00,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x24 '$'
        0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x08,0x00,0x3E,0x00,0x49,0x00,0x48,0x00,0x48,0x00,0x38,0x00,0x0E,0x00,0x09,0x00,0x09,0x00,0x49,0x00,0x3E,0x00,0x08,0x00,0x08,0x00,0x08,0x00,

        16, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x20,0x44,0x40,0x44,0x40,0x44,0x80,0x44,0x80,0x38,0x9C,0x01,0x22,0x01,0x22,0x02,0x22,0x02,0x22,0x04,0x1C,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x21,0x00,0x21,0x00,0x1E,0x40,0x24,0x40,0x42,0x40,0x41,0x40,0x40,0x80,0x21,0x40,0x1E,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x27 '''
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x28 '('
        0x00,0x00,0x00,0x08,0x10,0x20,0x20,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x20,0x20,0x10,0x08,

        7, // 0x29 ')'
        0x00,0x00,0x00,0x20,0x10,0x08,0x08,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x08,0x08,0x10,0x20,

        10, // 0x2A '*'
        0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x49,0x00,0x2A,0x00,0x1C,0x00,0x2A,0x00,0x49,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0x00,0x02,0x00,0x02,0x00,0x02,0x00,0x3F,0xE0,0x02,0x00,0x02,0x00,0x02,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x20,0x40,0x40,

        7, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x00,0x00,0x00,

        7, // 0x2F '/'
        0x00,0x00,0x00,0x02,0x04,0x04,0x04,0x08,0x08,0x10,0x10,0x20,0x20,0x40,0x40,0x40,0x80,0x00,

        10, // 0x30 '0'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x31 '1'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x04,0x00,0x1C,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x32 '2'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x41,0x00,0x00,0x80,0x00,0x80,0x00,0x80,0x01,0x00,0x02,0x00,0x0C,0x00,0x30,0x00,0x40,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x33 '3'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x40,0x80,0x00,0x80,0x01,0x00,0x0E,0x00,0x01,0x00,0x00,0x80,0x00,0x80,0x00,0x80,0x41,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x34 '4'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x03,0x00,0x05,0x00,0x09,0x00,0x11,0x00,0x21,0x00,0x41,0x00,0x7F,0xC0,0x01,0x00,0x01,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x35 '5'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x80,0x20,0x00,0x20,0x00,0x20,0x00,0x3E,0x00,0x01,0x00,0x00,0x80,0x00,0x80,0x00,0x80,0x41,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x36 '6'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x5E,0x00,0x61,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x37 '7'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x01,0x00,0x02,0x00,0x02,0x00,0x04,0x00,0x04,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x10,0x00,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x38 '8'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x39 '9'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x20,0x80,0x1F,0x80,0x00,0x80,0x01,0x00,0x02,0x00,0x3C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x00,0x00,0x00,0x00,0x10,0x10,0x00,0x00,0x00,

        7, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x20,0x20,

        12, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x03,0x00,0x0C,0x00,0x30,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xE0,0x00,0x00,0x00,0x00,0x3F,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0xC0,0x00,0xC0,0x03,0x00,0x0C,0x00,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x3F '?'
        0x00,0x00,0x00,0x00,0x3C,0x42,0x02,0x02,0x04,0x08,0x10,0x10,0x00,0x10,0x10,0x00,0x00,0x00,

        15, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0x18,0x60,0x20,0x10,0x23,0xD0,0x44,0x48,0x48,0x48,0x48,0x48,0x48,0x48,0x44,0x48,0x23,0xF0,0x20,0x00,0x18,0x00,0x07,0xC0,0x00,0x00,

        10, // 0x41 'A'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x12,0x00,0x12,0x00,0x12,0x00,0x21,0x00,0x21,0x00,0x40,0x80,0x7F,0x80,0x40,0x80,0x80,0x40,0x80,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x42 'B'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x7E,0x00,0x41,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x41,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0xC0,0x20,0x40,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x20,0x40,0x30,0xC0,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x41,0x80,0x40,0x80,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x80,0x41,0x80,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x45 'E'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x7F,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x46 'F'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x40,0x00,0x40,0x00,0x40,0x00,0x7F,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x30,0x60,0x20,0x20,0x40,0x00,0x40,0x00,0x41,0xE0,0x40,0x20,0x40,0x20,0x20,0x20,0x30,0x20,0x0F,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7F,0xC0,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x49 'I'
        0x00,0x00,0x00,0x00,0x70,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x70,0x00,0x00,0x00,

        7, // 0x4A 'J'
        0x00,0x00,0x00,0x00,0x3C,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x08,0xF0,0x00,0x00,0x00,

        10, // 0x4B 'K'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x80,0x41,0x00,0x42,0x00,0x44,0x00,0x48,0x00,0x50,0x00,0x68,0x00,0x44,0x00,0x42,0x00,0x41,0x00,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x4C 'L'
        0x00,0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x7F,0x00,0x00,0x00,

        13, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x30,0x50,0x50,0x50,0x50,0x48,0x90,0x48,0x90,0x45,0x10,0x45,0x10,0x42,0x10,0x42,0x10,0x40,0x10,0x40,0x10,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x60,0x40,0x50,0x40,0x48,0x40,0x48,0x40,0x44,0x40,0x42,0x40,0x42,0x40,0x41,0x40,0x40,0xC0,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0xC0,0x20,0x40,0x40,0x20,0x40,0x20,0x40,0x20,0x40,0x20,0x40,0x20,0x20,0x40,0x30,0xC0,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x50 'P'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x41,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x41,0x00,0x7E,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x30,0xC0,0x20,0x40,0x40,0x20,0x40,0x20,0x40,0x20,0x40,0x20,0x40,0x20,0x20,0x40,0x30,0xC0,0x0F,0x00,0x01,0x00,0x01,0x00,0x00,0xE0,

        10, // 0x52 'R'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x42,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x42,0x00,0x7C,0x00,0x42,0x00,0x41,0x00,0x40,0x80,0x40,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x53 'S'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x20,0x80,0x40,0x00,0x40,0x00,0x20,0x00,0x1E,0x00,0x01,0x00,0x00,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x54 'T'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x80,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x20,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x56 'V'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x40,0x80,0x40,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x21,0x00,0x12,0x00,0x12,0x00,0x12,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        15, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x04,0x41,0x04,0x22,0x88,0x22,0x88,0x22,0x88,0x12,0x90,0x14,0x50,0x14,0x50,0x14,0x50,0x08,0x20,0x08,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x58 'X'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x80,0x21,0x00,0x21,0x00,0x12,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x12,0x00,0x21,0x00,0x21,0x00,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x59 'Y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0x41,0x00,0x22,0x00,0x22,0x00,0x14,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x5A 'Z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x00,0x80,0x01,0x00,0x02,0x00,0x04,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x5B '['
        0x00,0x00,0x00,0x3C,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x3C,

        7, // 0x5C '\'
        0x00,0x00,0x00,0x80,0x40,0x40,0x40,0x20,0x20,0x10,0x10,0x08,0x08,0x04,0x04,0x04,0x02,0x00,

        7, // 0x5D ']'
        0x00,0x00,0x00,0x78,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x78,

        12, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x09,0x00,0x10,0x80,0x20,0x40,0x40,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xC0,0x00,0x00,

        10, // 0x60 '`'
        0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x01,0x00,0x3F,0x00,0x41,0x00,0x41,0x00,0x43,0x00,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x62 'b'
        0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x5C,0x00,0x62,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x42,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x21,0x40,0x40,0x40,0x40,0x21,0x1E,0x00,0x00,0x00,

        9, // 0x64 'd'
        0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x01,0x00,0x01,0x00,0x01,0x00,0x1F,0x00,0x21,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x23,0x00,0x1D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x00,0x22,0x00,0x41,0x00,0x7F,0x00,0x40,0x00,0x40,0x00,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x66 'f'
        0x00,0x00,0x00,0x1C,0x20,0x20,0x20,0x7C,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,

        9, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x21,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x23,0x00,0x1D,0x00,0x01,0x00,0x22,0x00,0x1C,0x00,

        9, // 0x68 'h'
        0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x5E,0x00,0x61,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        3, // 0x69 'i'
        0x00,0x00,0x00,0x00,0x40,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        5, // 0x6A 'j'
        0x00,0x00,0x00,0x00,0x10,0x00,0x00,0x70,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0xE0,

        9, // 0x6B 'k'
        0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x42,0x00,0x44,0x00,0x48,0x00,0x50,0x00,0x68,0x00,0x44,0x00,0x42,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        3, // 0x6C 'l'
        0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        15, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x2E,0x70,0x31,0x88,0x21,0x08,0x21,0x08,0x21,0x08,0x21,0x08,0x21,0x08,0x21,0x08,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5E,0x00,0x61,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x21,0x00,0x40,0x80,0x40,0x80,0x40,0x80,0x40,0x80,0x21,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0x00,0x62,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x42,0x00,0x7C,0x00,0x40,0x00,0x40,0x00,0x40,0x00,

        9, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x21,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x23,0x00,0x1D,0x00,0x01,0x00,0x01,0x00,0x01,0x00,

        6, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x5C,0x60,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,

        8, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x42,0x40,0x30,0x0C,0x02,0x42,0x3C,0x00,0x00,0x00,

        6, // 0x74 't'
        0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x7C,0x20,0x20,0x20,0x20,0x20,0x20,0x1C,0x00,0x00,0x00,

        9, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x41,0x00,0x43,0x00,0x3D,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x22,0x00,0x22,0x00,0x14,0x00,0x14,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x10,0x42,0x10,0x25,0x20,0x25,0x20,0x28,0xA0,0x28,0xA0,0x10,0x40,0x10,0x40,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x22,0x00,0x14,0x00,0x08,0x00,0x08,0x00,0x14,0x00,0x22,0x00,0x41,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x41,0x00,0x41,0x00,0x22,0x00,0x22,0x00,0x22,0x00,0x14,0x00,0x14,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x10,0x00,

        9, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x02,0x00,0x04,0x00,0x08,0x00,0x10,0x00,0x20,0x00,0x40,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x7B '{'
        0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x60,0x00,0x10,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x07,0x00,

        7, // 0x7C '|'
        0x00,0x00,0x00,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,

        10, // 0x7D '}'
        0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x02,0x00,0x01,0x80,0x02,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x04,0x00,0x38,0x00,

        12, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x20,0x24,0x20,0x42,0x40,0x41,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        15, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xF8,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x3F,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,

        0
    };

    const int8u verdana18_bold[] = 
    {
        18, 4, 32, 128-32,
        0x00,0x00,0x13,0x00,0x26,0x00,0x4B,0x00,0x70,0x00,0x95,0x00,0xCC,0x00,0xF1,0x00,0x04,0x01,
        0x17,0x01,0x2A,0x01,0x4F,0x01,0x74,0x01,0x87,0x01,0x9A,0x01,0xAD,0x01,0xD2,0x01,0xF7,0x01,
        0x1C,0x02,0x41,0x02,0x66,0x02,0x8B,0x02,0xB0,0x02,0xD5,0x02,0xFA,0x02,0x1F,0x03,0x44,0x03,
        0x57,0x03,0x6A,0x03,0x8F,0x03,0xB4,0x03,0xD9,0x03,0xFE,0x03,0x23,0x04,0x48,0x04,0x6D,0x04,
        0x92,0x04,0xB7,0x04,0xDC,0x04,0x01,0x05,0x26,0x05,0x4B,0x05,0x5E,0x05,0x71,0x05,0x96,0x05,
        0xBB,0x05,0xE0,0x05,0x05,0x06,0x2A,0x06,0x4F,0x06,0x74,0x06,0x99,0x06,0xBE,0x06,0xE3,0x06,
        0x08,0x07,0x2D,0x07,0x52,0x07,0x77,0x07,0x9C,0x07,0xC1,0x07,0xD4,0x07,0xF9,0x07,0x0C,0x08,
        0x31,0x08,0x56,0x08,0x7B,0x08,0xA0,0x08,0xC5,0x08,0xD8,0x08,0xFD,0x08,0x22,0x09,0x35,0x09,
        0x5A,0x09,0x7F,0x09,0x92,0x09,0xA5,0x09,0xCA,0x09,0xDD,0x09,0x02,0x0A,0x27,0x0A,0x4C,0x0A,
        0x71,0x0A,0x96,0x0A,0xA9,0x0A,0xCE,0x0A,0xE1,0x0A,0x06,0x0B,0x2B,0x0B,0x50,0x0B,0x75,0x0B,
        0x9A,0x0B,0xBF,0x0B,0xE4,0x0B,0xF7,0x0B,0x1C,0x0C,0x41,0x0C,

        5, // 0x20 ' '
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x21 '!'
        0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x00,0x30,0x30,0x00,0x00,0x00,

        9, // 0x22 '"'
        0x00,0x00,0x00,0x00,0x00,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x23 '#'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x20,0x04,0x20,0x08,0x40,0x3F,0xF0,0x3F,0xF0,0x08,0x40,0x10,0x80,0x7F,0xE0,0x7F,0xE0,0x21,0x00,0x21,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x24 '$'
        0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x04,0x00,0x1F,0x80,0x34,0xC0,0x64,0xC0,0x64,0x00,0x3C,0x00,0x07,0x80,0x04,0xC0,0x64,0xC0,0x65,0x80,0x3F,0x00,0x04,0x00,0x04,0x00,0x00,0x00,

        19, // 0x25 '%'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x08,0x00,0x63,0x10,0x00,0x63,0x10,0x00,0x63,0x20,0x00,0x63,0x2F,0x80,0x63,0x58,0xC0,0x3E,0x98,0xC0,0x00,0x98,0xC0,0x01,0x18,0xC0,0x01,0x18,0xC0,0x02,0x0F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x26 '&'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x33,0x00,0x33,0x00,0x33,0x00,0x1E,0x60,0x36,0x60,0x63,0x60,0x61,0xC0,0x60,0xC0,0x30,0xE0,0x1F,0x30,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x27 '''
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x28 '('
        0x00,0x00,0x00,0x06,0x0C,0x18,0x30,0x30,0x60,0x60,0x60,0x60,0x60,0x30,0x30,0x18,0x0C,0x06,

        8, // 0x29 ')'
        0x00,0x00,0x00,0x60,0x30,0x18,0x0C,0x0C,0x06,0x06,0x06,0x06,0x06,0x0C,0x0C,0x18,0x30,0x60,

        11, // 0x2A '*'
        0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x00,0x24,0x80,0x15,0x00,0x0E,0x00,0x15,0x00,0x24,0x80,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x2B '+'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0x00,0x02,0x00,0x02,0x00,0x02,0x00,0x3F,0xE0,0x02,0x00,0x02,0x00,0x02,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2C ','
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x60,0x60,0x60,0xC0,0xC0,

        7, // 0x2D '-'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        5, // 0x2E '.'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x00,0x00,0x00,

        10, // 0x2F '/'
        0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x80,0x01,0x80,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x30,0x00,0x30,0x00,0x60,0x00,0x60,0x00,0x00,0x00,

        11, // 0x30 '0'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x31 '1'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x1E,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x1F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x32 '2'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x61,0x80,0x60,0xC0,0x00,0xC0,0x01,0x80,0x03,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x30,0x00,0x7F,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x33 '3'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x61,0x80,0x60,0xC0,0x00,0xC0,0x01,0x80,0x0F,0x00,0x01,0x80,0x00,0xC0,0x60,0xC0,0x61,0x80,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x34 '4'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x80,0x03,0x80,0x05,0x80,0x09,0x80,0x11,0x80,0x21,0x80,0x41,0x80,0x7F,0xE0,0x01,0x80,0x01,0x80,0x01,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x35 '5'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xC0,0x30,0x00,0x30,0x00,0x30,0x00,0x3F,0x00,0x01,0x80,0x00,0xC0,0x00,0xC0,0x60,0xC0,0x61,0x80,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x36 '6'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x18,0x00,0x30,0x00,0x60,0x00,0x6F,0x00,0x71,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x37 '7'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0xC0,0x00,0xC0,0x01,0x80,0x01,0x80,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x38 '8'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x31,0x80,0x60,0xC0,0x60,0xC0,0x31,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x39 '9'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x00,0x31,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0xC0,0x1E,0xC0,0x00,0xC0,0x01,0x80,0x03,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x3A ':'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x00,0x00,0x30,0x30,0x30,0x00,0x00,0x00,

        6, // 0x3B ';'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x30,0x00,0x00,0x38,0x30,0x30,0x30,0x60,0x60,

        13, // 0x3C '<'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0xC0,0x03,0x00,0x0C,0x00,0x30,0x00,0x30,0x00,0x0C,0x00,0x03,0x00,0x00,0xC0,0x00,0x20,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x3D '='
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x3E '>'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x18,0x00,0x06,0x00,0x01,0x80,0x00,0x60,0x00,0x60,0x01,0x80,0x06,0x00,0x18,0x00,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        9, // 0x3F '?'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x63,0x00,0x03,0x00,0x03,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        14, // 0x40 '@'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0x18,0x60,0x20,0x10,0x27,0xD0,0x4C,0xC8,0x4C,0xC8,0x4C,0xC8,0x4C,0xC8,0x4C,0xC8,0x27,0xF0,0x20,0x00,0x18,0x00,0x07,0xC0,0x00,0x00,

        12, // 0x41 'A'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x06,0x00,0x0F,0x00,0x0F,0x00,0x19,0x80,0x19,0x80,0x30,0xC0,0x3F,0xC0,0x30,0xC0,0x60,0x60,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x42 'B'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x00,0x7F,0x00,0x61,0x80,0x60,0xC0,0x60,0xC0,0x61,0x80,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x43 'C'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x38,0xC0,0x30,0xC0,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x30,0xC0,0x38,0xC0,0x0F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x44 'D'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x61,0xC0,0x60,0xC0,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0xC0,0x61,0xC0,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x45 'E'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x80,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x46 'F'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x80,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x47 'G'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0xC0,0x38,0x60,0x30,0x60,0x60,0x00,0x60,0x00,0x63,0xE0,0x60,0x60,0x60,0x60,0x30,0x60,0x38,0x60,0x0F,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x48 'H'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x7F,0xE0,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x49 'I'
        0x00,0x00,0x00,0x00,0x7E,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x7E,0x00,0x00,0x00,

        8, // 0x4A 'J'
        0x00,0x00,0x00,0x00,0x3E,0x06,0x06,0x06,0x06,0x06,0x06,0x06,0x06,0x0C,0xF8,0x00,0x00,0x00,

        12, // 0x4B 'K'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x60,0xC0,0x61,0x80,0x63,0x00,0x66,0x00,0x6C,0x00,0x7E,0x00,0x73,0x00,0x61,0x80,0x60,0xC0,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x4C 'L'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        14, // 0x4D 'M'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x38,0x70,0x38,0x70,0x78,0x58,0x58,0x58,0xD8,0x4C,0x98,0x4D,0x98,0x47,0x18,0x47,0x18,0x42,0x18,0x40,0x18,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x4E 'N'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x20,0x70,0x20,0x58,0x20,0x4C,0x20,0x4C,0x20,0x46,0x20,0x43,0x20,0x43,0x20,0x41,0xA0,0x40,0xE0,0x40,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x4F 'O'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x38,0xE0,0x30,0x60,0x60,0x30,0x60,0x30,0x60,0x30,0x60,0x30,0x60,0x30,0x30,0x60,0x38,0xE0,0x0F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x50 'P'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x61,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x61,0x80,0x7F,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        13, // 0x51 'Q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x80,0x38,0xE0,0x30,0x60,0x60,0x30,0x60,0x30,0x60,0x30,0x60,0x30,0x60,0x30,0x30,0x60,0x38,0xE0,0x0F,0x80,0x03,0x00,0x03,0x80,0x01,0xF0,

        12, // 0x52 'R'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x61,0x80,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x61,0x80,0x7F,0x00,0x61,0x80,0x60,0xC0,0x60,0x60,0x60,0x30,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x53 'S'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x80,0x30,0xC0,0x60,0xC0,0x60,0x00,0x7C,0x00,0x3F,0x80,0x03,0xC0,0x00,0xC0,0x60,0xC0,0x61,0x80,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x54 'T'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        12, // 0x55 'U'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x30,0xC0,0x1F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x56 'V'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x60,0xC0,0x60,0xC0,0x31,0x80,0x31,0x80,0x31,0x80,0x1B,0x00,0x1B,0x00,0x1B,0x00,0x0E,0x00,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        16, // 0x57 'W'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x86,0x61,0x86,0x63,0xC6,0x33,0xCC,0x32,0x4C,0x32,0x4C,0x1E,0x78,0x1C,0x38,0x1C,0x38,0x0C,0x30,0x0C,0x30,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x58 'X'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0xC0,0x31,0x80,0x31,0x80,0x1B,0x00,0x0E,0x00,0x0E,0x00,0x0E,0x00,0x1B,0x00,0x31,0x80,0x31,0x80,0x60,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x59 'Y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xC0,0x61,0x80,0x61,0x80,0x33,0x00,0x1E,0x00,0x1E,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x5A 'Z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x80,0x01,0x80,0x03,0x00,0x06,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x7F,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x5B '['
        0x00,0x00,0x00,0x3E,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x3E,

        10, // 0x5C '\'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x30,0x00,0x30,0x00,0x18,0x00,0x18,0x00,0x0C,0x00,0x0C,0x00,0x06,0x00,0x06,0x00,0x03,0x00,0x03,0x00,0x01,0x80,0x01,0x80,0x00,0x00,

        8, // 0x5D ']'
        0x00,0x00,0x00,0x7C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x0C,0x7C,

        13, // 0x5E '^'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x0F,0x00,0x19,0x80,0x30,0xC0,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x5F '_'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xE0,0x00,0x00,

        11, // 0x60 '`'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x61 'a'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x00,0x01,0x80,0x01,0x80,0x3F,0x80,0x61,0x80,0x61,0x80,0x63,0x80,0x3D,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x62 'b'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x6E,0x00,0x73,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        8, // 0x63 'c'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x33,0x60,0x60,0x60,0x60,0x33,0x1E,0x00,0x00,0x00,

        10, // 0x64 'd'
        0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x1F,0x80,0x31,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x80,0x1D,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x65 'e'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x33,0x00,0x61,0x80,0x7F,0x80,0x60,0x00,0x60,0x00,0x31,0x80,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        6, // 0x66 'f'
        0x00,0x00,0x00,0x1C,0x30,0x30,0x30,0x7C,0x30,0x30,0x30,0x30,0x30,0x30,0x30,0x00,0x00,0x00,

        10, // 0x67 'g'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x80,0x31,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x80,0x1D,0x80,0x01,0x80,0x03,0x00,0x3E,0x00,

        10, // 0x68 'h'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x6F,0x00,0x71,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x69 'i'
        0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        6, // 0x6A 'j'
        0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x78,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0xF0,

        10, // 0x6B 'k'
        0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x61,0x80,0x63,0x00,0x66,0x00,0x6C,0x00,0x7E,0x00,0x73,0x00,0x61,0x80,0x60,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,

        4, // 0x6C 'l'
        0x00,0x00,0x00,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        16, // 0x6D 'm'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6F,0x3C,0x71,0xC6,0x61,0x86,0x61,0x86,0x61,0x86,0x61,0x86,0x61,0x86,0x61,0x86,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x6E 'n'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6F,0x00,0x71,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x6F 'o'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1E,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x00,0x1E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x70 'p'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x00,0x73,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x00,0x7E,0x00,0x60,0x00,0x60,0x00,0x60,0x00,

        10, // 0x71 'q'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x80,0x31,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x80,0x1D,0x80,0x01,0x80,0x01,0x80,0x01,0x80,

        7, // 0x72 'r'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x6E,0x7E,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,

        9, // 0x73 's'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3E,0x00,0x61,0x00,0x60,0x00,0x7E,0x00,0x3F,0x00,0x03,0x00,0x43,0x00,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        7, // 0x74 't'
        0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x7E,0x30,0x30,0x30,0x30,0x30,0x30,0x1E,0x00,0x00,0x00,

        10, // 0x75 'u'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x63,0x80,0x3D,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x76 'v'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x33,0x00,0x33,0x00,0x33,0x00,0x1E,0x00,0x1E,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        14, // 0x77 'w'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x18,0x63,0x18,0x63,0x18,0x37,0xB0,0x34,0xB0,0x3C,0xF0,0x18,0x60,0x18,0x60,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x78 'x'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x33,0x00,0x33,0x00,0x1E,0x00,0x1E,0x00,0x33,0x00,0x33,0x00,0x61,0x80,0x00,0x00,0x00,0x00,0x00,0x00,

        10, // 0x79 'y'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x61,0x80,0x33,0x00,0x33,0x00,0x33,0x00,0x1E,0x00,0x1E,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x18,0x00,

        9, // 0x7A 'z'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x00,0x03,0x00,0x06,0x00,0x0C,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        11, // 0x7B '{'
        0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x80,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x18,0x00,0x70,0x00,0x18,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x0C,0x00,0x07,0x80,

        8, // 0x7C '|'
        0x00,0x00,0x00,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,

        11, // 0x7D '}'
        0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x03,0x00,0x01,0xC0,0x03,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x06,0x00,0x3C,0x00,

        13, // 0x7E '~'
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x10,0x24,0x10,0x42,0x10,0x41,0x20,0x40,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

        15, // 0x7F ''
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0xF8,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x20,0x08,0x3F,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,

        0
    };

}

