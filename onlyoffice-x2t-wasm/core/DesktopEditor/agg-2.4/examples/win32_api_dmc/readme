This is a makefile to build the demo examples with the Digital Mars C++.
Visit http://digitalmars.com/ for more info and to download the compiler.

The compiler is very easy in use and doesn't require any installation 
procedure. You just download and unzip it. 

Suppose you have unzipped it into the root directory:

\dm\*.*

That is, the full path to the compiler is \dm\bin\dmc.exe

Then you simply run:

\dm\bin\make

That's it, it should build the examples, except for svg_test and freetype_test.
If you have a different directory with the compiler please modify the Makefile
accordingly (that's pertty easy).

