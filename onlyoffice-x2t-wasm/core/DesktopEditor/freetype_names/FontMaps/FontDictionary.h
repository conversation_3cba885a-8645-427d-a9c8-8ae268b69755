﻿/*
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */
#ifndef _FONT_DICTIONARY_H

typedef struct FD_FontMapRec_
{
	const char*		m_name;

	int				m_index_r;
	int				m_index_i;
	int				m_index_b;
	int				m_index_bi;
} FD_FontMapRec;

typedef struct FD_FontMapRecW_
{
	const wchar_t*	m_name;

	int				m_index_r;
	int				m_index_i;
	int				m_index_b;
	int				m_index_bi;
} FD_FontMapRecW;

#define FONTS_DICT_ASCII_NAMES_COUNT 427
static const FD_FontMapRec FD_Ascii_Names[FONTS_DICT_ASCII_NAMES_COUNT] = 
{
	{ "AGA Arabesque", 24, -1, -1, -1 },
	{ "AGA Arabesque Desktop", 27, -1, -1, -1 },
	{ "Agency FB", 1, -1, 0, -1 },
	{ "Aharoni", -1, -1, 3, -1 },
	{ "Akhbar MT", -1, 4, -1, 5 },
	{ "Aldhabi", 6, -1, -1, -1 },
	{ "Algerian", 7, -1, -1, -1 },
	{ "Ami R", 345, -1, -1, -1 },
	{ "Andalus", 8, -1, -1, -1 },
	{ "Angsana New", 9, 11, 10, 16 },
	{ "AngsanaUPC", 12, 14, 13, 15 },
	{ "Aparajita", 20, 23, 21, 22 },
	{ "Arabic Transparent", 40, -1, 39, -1 },
	{ "Arabic Typesetting", 25, -1, -1, -1 },
	{ "Arial", 28, 31, 29, 30 },
	{ "Arial Black", 37, -1, -1, -1 },
	{ "Arial Narrow", 32, 35, 33, 34 },
	{ "Arial Rounded MT Bold", 38, -1, -1, -1 },
	{ "Arial Unicode MS", 36, -1, -1, -1 },
	{ "Arvo", 44, 43, 41, 42 },
	{ "Aston-F1", 45, -1, -1, -1 },
	{ "Baskerville Old Face", 46, -1, -1, -1 },
	{ "Batang", 47, -1, -1, -1 },
	{ "BatangChe", 48, -1, -1, -1 },
	{ "Bauhaus 93", 51, -1, -1, -1 },
	{ "Bell MT", 52, 54, 53, -1 },
	{ "Berlin Sans FB", 78, -1, 76, -1 },
	{ "Berlin Sans FB Demi", -1, -1, 77, -1 },
	{ "Bernard MT Condensed", 55, -1, -1, -1 },
	{ "Bickham Script Pro", -1, -1, 56, -1 },
	{ "Bickham Script Pro Regular", -1, -1, 56, -1 },
	{ "Blackadder ITC", 355, -1, -1, -1 },
	{ "Bodoni MT", 69, 67, 59, 60 },
	{ "Bodoni MT Black", 62, 61, -1, -1 },
	{ "Bodoni MT Condensed", 66, 65, 63, 64 },
	{ "Bodoni MT Poster Compressed", 68, -1, -1, -1 },
	{ "Bold Italic Art", 58, -1, -1, -1 },
	{ "Book Antiqua", 57, 19, 17, 18 },
	{ "Bookman Old Style", 70, 73, 71, 72 },
	{ "Bookshelf Symbol 7", 89, -1, -1, -1 },
	{ "Bradley Hand ITC", 74, -1, -1, -1 },
	{ "Britannic Bold", 75, -1, -1, -1 },
	{ "Broadway", 79, -1, -1, -1 },
	{ "Browallia New", 80, 82, 81, 87 },
	{ "BrowalliaUPC", 83, 85, 84, 86 },
	{ "Brush Script MT", -1, 88, -1, -1 },
	{ "Calibri", 90, 92, 91, 95 },
	{ "Calibri Light", 93, 94, -1, -1 },
	{ "Calibri Light Italic", -1, 94, -1, -1 },
	{ "Californian FB", 98, 97, 96, -1 },
	{ "Calisto MT", 99, 102, 100, 101 },
	{ "Cambria", 103, 106, 105, 107 },
	{ "Cambria Math", 104, -1, -1, -1 },
	{ "Candara", 108, 110, 109, 111 },
	{ "Castellar", 112, -1, -1, -1 },
	{ "Centaur", 114, -1, -1, -1 },
	{ "Century", 115, -1, -1, -1 },
	{ "Century Gothic", 280, 283, 281, 282 },
	{ "Century Schoolbook", 113, 550, 548, 549 },
	{ "Chiller", 116, -1, -1, -1 },
	{ "Colonna MT", 117, -1, -1, -1 },
	{ "Comic Sans MS", 118, 120, 119, 121 },
	{ "Consolas", 122, 124, 123, 125 },
	{ "Constantia", 126, 128, 127, 129 },
	{ "Cooper Black", 130, -1, -1, -1 },
	{ "Copperplate Gothic Bold", 131, -1, -1, -1 },
	{ "Copperplate Gothic Light", 132, -1, -1, -1 },
	{ "Corbel", 133, 135, 134, 136 },
	{ "Cordia New", 137, 139, 138, 144 },
	{ "CordiaUPC", 140, 142, 141, 143 },
	{ "Courier New", 145, 148, 146, 147 },
	{ "Cuprum", 152, 151, 149, 150 },
	{ "Curlz MT", 153, -1, -1, -1 },
	{ "DFKai-SB", 361, -1, -1, -1 },
	{ "Dancing Script", 155, -1, 154, -1 },
	{ "DaunPenh", 156, -1, -1, -1 },
	{ "David", 157, -1, 158, -1 },
	{ "David Transparent", 159, -1, -1, -1 },
	{ "DecoType Naskh", 195, -1, -1, -1 },
	{ "DecoType Naskh Extensions", 198, -1, -1, -1 },
	{ "DecoType Naskh Special", 196, -1, -1, -1 },
	{ "DecoType Naskh Swashes", 199, -1, -1, -1 },
	{ "DecoType Naskh Variants", 197, -1, -1, -1 },
	{ "DecoType Thuluth", 194, -1, -1, -1 },
	{ "DejaVu Sans", 160, 172, 161, 162 },
	{ "DejaVu Sans Condensed", 166, 165, 163, 164 },
	{ "DejaVu Sans Light", 167, -1, -1, -1 },
	{ "DejaVu Sans Mono", 171, 170, 168, 169 },
	{ "DejaVu Serif", 173, 180, 174, 175 },
	{ "DejaVu Serif Condensed", 179, 178, 176, 177 },
	{ "DilleniaUPC", 639, 638, 636, 637 },
	{ "Dingbats", 181, -1, -1, -1 },
	{ "Diwani Bent", 182, -1, -1, -1 },
	{ "Diwani Letter", 183, -1, -1, -1 },
	{ "Diwani Outline Shaded", 200, -1, -1, -1 },
	{ "Diwani Simple Outline", 202, -1, -1, -1 },
	{ "Diwani Simple Outline 2", 201, -1, -1, -1 },
	{ "Diwani Simple Striped", 203, -1, -1, -1 },
	{ "DokChampa", 184, -1, -1, -1 },
	{ "Dotum", 290, -1, -1, -1 },
	{ "DotumChe", 291, -1, -1, -1 },
	{ "Droid Sans", 188, -1, 187, -1 },
	{ "Droid Sans Mono", 189, -1, -1, -1 },
	{ "Droid Serif", 193, 192, 190, 191 },
	{ "Ebrima", 204, -1, 205, -1 },
	{ "Edwardian Script ITC", 356, -1, -1, -1 },
	{ "Elephant", 206, 207, -1, -1 },
	{ "Engravers MT", 208, -1, -1, -1 },
	{ "Eras Bold ITC", 209, -1, -1, -1 },
	{ "Eras Demi ITC", 210, -1, -1, -1 },
	{ "Eras Light ITC", 211, -1, -1, -1 },
	{ "Eras Medium ITC", 212, -1, -1, -1 },
	{ "Estrangelo Edessa", 213, -1, -1, -1 },
	{ "EucrosiaUPC", 643, 642, 640, 641 },
	{ "Euphemia", 214, -1, -1, -1 },
	{ "Expo M", 342, -1, -1, -1 },
	{ "FZShuTi", 236, -1, -1, -1 },
	{ "FZYaoTi", 237, -1, -1, -1 },
	{ "FangSong", 572, -1, -1, -1 },
	{ "Farsi Simple Bold", 233, -1, -1, -1 },
	{ "Farsi Simple Outline", 234, -1, -1, -1 },
	{ "Felix Titling", 215, -1, -1, -1 },
	{ "Fixed Miriam Transparent", 450, -1, -1, -1 },
	{ "FlemishScript BT", 216, -1, -1, -1 },
	{ "Footlight MT Light", 235, -1, -1, -1 },
	{ "Forte", 217, -1, -1, -1 },
	{ "FrankRuehl", 228, -1, -1, -1 },
	{ "Franklin Gothic Book", 218, 219, -1, -1 },
	{ "Franklin Gothic Demi", 220, 222, -1, -1 },
	{ "Franklin Gothic Demi Cond", 221, -1, -1, -1 },
	{ "Franklin Gothic Heavy", 223, 224, -1, -1 },
	{ "Franklin Gothic Medium", 225, 227, -1, -1 },
	{ "Franklin Gothic Medium Cond", 226, -1, -1, -1 },
	{ "FreesiaUPC", 647, 646, 644, 645 },
	{ "Freestyle Script", 230, -1, -1, -1 },
	{ "French Script MT", 232, -1, -1, -1 },
	{ "GOST type A", 278, -1, -1, -1 },
	{ "GOST type B", 279, -1, -1, -1 },
	{ "Gabriola", 238, -1, -1, -1 },
	{ "Gadugi", 239, -1, 240, -1 },
	{ "Garamond", 242, 244, 243, -1 },
	{ "Gautami", 245, -1, 246, -1 },
	{ "Gentium Basic", 250, 249, 247, 248 },
	{ "Gentium Book Basic", 254, 253, 251, 252 },
	{ "Georgia", 255, 257, 256, 258 },
	{ "Gigi", 262, -1, -1, -1 },
	{ "Gill Sans MT", 269, 266, 264, 263 },
	{ "Gill Sans MT Condensed", 265, -1, -1, -1 },
	{ "Gill Sans MT Ext Condensed Bold", 276, -1, -1, -1 },
	{ "Gill Sans Ultra Bold", 268, -1, -1, -1 },
	{ "Gill Sans Ultra Bold Condensed", 267, -1, -1, -1 },
	{ "Gisha", 270, -1, 271, -1 },
	{ "Gloucester MT Extra Condensed", 275, -1, -1, -1 },
	{ "Goudy Old Style", 284, 286, 285, -1 },
	{ "Goudy Stout", 287, -1, -1, -1 },
	{ "Gulim", 288, -1, -1, -1 },
	{ "GulimChe", 289, -1, -1, -1 },
	{ "Gungsuh", 49, -1, -1, -1 },
	{ "GungsuhChe", 50, -1, -1, -1 },
	{ "Guttman Aharoni", 241, -1, -1, -1 },
	{ "Guttman Drogolin", 186, -1, 185, -1 },
	{ "Guttman Frank", 259, -1, 229, -1 },
	{ "Guttman Frnew", 231, -1, -1, -1 },
	{ "Guttman Haim", 260, -1, -1, -1 },
	{ "Guttman Haim-Condensed", 261, -1, -1, -1 },
	{ "Guttman Hatzvi", 630, -1, 629, -1 },
	{ "Guttman Kav", 274, -1, 272, -1 },
	{ "Guttman Kav-Light", 273, -1, -1, -1 },
	{ "Guttman Logo1", 399, -1, -1, -1 },
	{ "Guttman Mantova", 422, -1, 420, -1 },
	{ "Guttman Mantova-Decor", 421, -1, -1, -1 },
	{ "Guttman Miryam", 442, -1, 440, -1 },
	{ "Guttman Myamfix", 277, -1, -1, -1 },
	{ "Guttman Rashi", 534, -1, 535, -1 },
	{ "Guttman Stam", 589, -1, -1, -1 },
	{ "Guttman Stam1", 590, -1, -1, -1 },
	{ "Guttman Vilna", 677, -1, 678, -1 },
	{ "Guttman Yad", 293, -1, -1, -1 },
	{ "Guttman Yad-Brush", 292, -1, -1, -1 },
	{ "Guttman Yad-Light", 294, -1, -1, -1 },
	{ "Guttman-Aharoni", -1, -1, 2, -1 },
	{ "Guttman-Aram", 26, -1, -1, -1 },
	{ "Guttman-CourMir", 441, -1, -1, -1 },
	{ "HGGothicE", 311, -1, -1, -1 },
	{ "HGGothicM", 314, -1, -1, -1 },
	{ "HGGyoshotai", 317, -1, -1, -1 },
	{ "HGKyokashotai", 320, -1, -1, -1 },
	{ "HGMaruGothicMPRO", 339, -1, -1, -1 },
	{ "HGMinchoB", 323, -1, -1, -1 },
	{ "HGMinchoE", 326, -1, -1, -1 },
	{ "HGPGothicE", 312, -1, -1, -1 },
	{ "HGPGothicM", 315, -1, -1, -1 },
	{ "HGPGyoshotai", 318, -1, -1, -1 },
	{ "HGPKyokashotai", 321, -1, -1, -1 },
	{ "HGPMinchoB", 324, -1, -1, -1 },
	{ "HGPMinchoE", 327, -1, -1, -1 },
	{ "HGPSoeiKakugothicUB", 336, -1, -1, -1 },
	{ "HGPSoeiKakupoptai", 330, -1, -1, -1 },
	{ "HGPSoeiPresenceEB", 333, -1, -1, -1 },
	{ "HGSGothicE", 313, -1, -1, -1 },
	{ "HGSGothicM", 316, -1, -1, -1 },
	{ "HGSGyoshotai", 319, -1, -1, -1 },
	{ "HGSKyokashotai", 322, -1, -1, -1 },
	{ "HGSMinchoB", 325, -1, -1, -1 },
	{ "HGSMinchoE", 328, -1, -1, -1 },
	{ "HGSSoeiKakugothicUB", 337, -1, -1, -1 },
	{ "HGSSoeiKakupoptai", 331, -1, -1, -1 },
	{ "HGSSoeiPresenceEB", 334, -1, -1, -1 },
	{ "HGSeikaishotaiPRO", 338, -1, -1, -1 },
	{ "HGSoeiKakugothicUB", 335, -1, -1, -1 },
	{ "HGSoeiKakupoptai", 329, -1, -1, -1 },
	{ "HGSoeiPresenceEB", 332, -1, -1, -1 },
	{ "HYGothic-Extra", 297, -1, -1, -1 },
	{ "HYGothic-Medium", 298, -1, -1, -1 },
	{ "HYGraphic-Medium", 295, -1, -1, -1 },
	{ "HYGungSo-Bold", 296, -1, -1, -1 },
	{ "HYHeadLine-Medium", 299, -1, -1, -1 },
	{ "HYMyeongJo-Extra", 300, -1, -1, -1 },
	{ "HYPMokGak-Bold", 302, -1, -1, -1 },
	{ "HYPost-Light", 303, -1, -1, -1 },
	{ "HYPost-Medium", 304, -1, -1, -1 },
	{ "HYShortSamul-Medium", 305, -1, -1, -1 },
	{ "HYSinMyeongJo-Medium", 301, -1, -1, -1 },
	{ "Haettenschweiler", 308, -1, -1, -1 },
	{ "HanWangMingMedium", 688, -1, -1, -1 },
	{ "Harlow Solid Italic", -1, 306, -1, -1 },
	{ "Harrington", 307, -1, -1, -1 },
	{ "Headline R", 347, -1, -1, -1 },
	{ "High Tower Text", 348, 349, -1, -1 },
	{ "Impact", 350, -1, -1, -1 },
	{ "Imprint MT Shadow", 351, -1, -1, -1 },
	{ "Informal Roman", 352, -1, -1, -1 },
	{ "IrisUPC", 651, 650, 648, 649 },
	{ "Iskoola Pota", 353, -1, 354, -1 },
	{ "Italic Outline Art", 358, -1, -1, -1 },
	{ "JasmineUPC", 655, 654, 652, 653 },
	{ "Jokerman", 359, -1, -1, -1 },
	{ "Juice ITC", 360, -1, -1, -1 },
	{ "KaiTi", 574, -1, -1, -1 },
	{ "Kalinga", 362, -1, 363, -1 },
	{ "Kartika", 364, -1, 365, -1 },
	{ "Khmer UI", 368, -1, 369, -1 },
	{ "KodchiangUPC", 659, 658, 656, 657 },
	{ "Kokila", 370, 373, 371, 372 },
	{ "Kristen ITC", 357, -1, -1, -1 },
	{ "Kufi Extended Outline", 366, -1, -1, -1 },
	{ "Kufi Outline Shaded", 367, -1, -1, -1 },
	{ "Kunstler Script", 375, -1, -1, -1 },
	{ "Lao UI", 376, -1, 377, -1 },
	{ "Latha", 378, -1, 379, -1 },
	{ "Led Italic Font", 386, -1, -1, -1 },
	{ "Leelawadee", 387, -1, 388, -1 },
	{ "Levenim MT", 409, -1, 410, -1 },
	{ "LiSu", 575, -1, -1, -1 },
	{ "LilyUPC", 663, 662, 660, 661 },
	{ "Lobster", 394, -1, -1, -1 },
	{ "Lobster 1.4", 394, -1, -1, -1 },
	{ "Lobster Two", 398, 397, 395, 396 },
	{ "Lucida Bright", 381, 384, 382, 383 },
	{ "Lucida Calligraphy", 385, -1, -1, -1 },
	{ "Lucida Console", 408, -1, -1, -1 },
	{ "Lucida Fax", 389, 392, 390, 391 },
	{ "Lucida Handwriting", 393, -1, -1, -1 },
	{ "Lucida Sans", 400, 403, 401, 402 },
	{ "Lucida Sans Typewriter", 404, 407, 405, 406 },
	{ "Lucida Sans Unicode", 411, -1, -1, -1 },
	{ "MS Gothic", 452, -1, -1, -1 },
	{ "MS Mincho", 459, -1, -1, -1 },
	{ "MS Outlook", 499, -1, -1, -1 },
	{ "MS PGothic", 454, -1, -1, -1 },
	{ "MS PMincho", 460, -1, -1, -1 },
	{ "MS Reference Sans Serif", 537, -1, -1, -1 },
	{ "MS Reference Specialty", 538, -1, -1, -1 },
	{ "MS UI Gothic", 453, -1, -1, -1 },
	{ "MV Boli", 471, -1, -1, -1 },
	{ "Magic R", 346, -1, -1, -1 },
	{ "Magneto", -1, -1, 412, -1 },
	{ "Maiandra GD", 413, -1, -1, -1 },
	{ "Malgun Gothic", 416, -1, 417, -1 },
	{ "Mangal", 418, -1, 419, -1 },
	{ "Marlett", 423, -1, -1, -1 },
	{ "Matura MT Script Capitals", 424, -1, -1, -1 },
	{ "Meiryo", 425, 426, 429, 430 },
	{ "Meiryo UI", 427, 428, 431, 432 },
	{ "Microsoft Himalaya", 340, -1, -1, -1 },
	{ "Microsoft JhengHei", 455, -1, 457, -1 },
	{ "Microsoft JhengHei UI", 456, -1, 458, -1 },
	{ "Microsoft New Tai Lue", 478, -1, 479, -1 },
	{ "Microsoft PhagsPa", 514, -1, 515, -1 },
	{ "Microsoft Sans Serif", 433, -1, -1, -1 },
	{ "Microsoft Tai Le", 606, -1, 607, -1 },
	{ "Microsoft Uighur", 462, -1, 461, -1 },
	{ "Microsoft YaHei", 463, -1, 465, -1 },
	{ "Microsoft YaHei UI", 464, -1, 466, -1 },
	{ "Microsoft Yi Baiti", 467, -1, -1, -1 },
	{ "MingLiU", 434, -1, -1, -1 },
	{ "MingLiU-ExtB", 437, -1, -1, -1 },
	{ "MingLiU_HKSCS", 436, -1, -1, -1 },
	{ "MingLiU_HKSCS-ExtB", 439, -1, -1, -1 },
	{ "Miriam", 448, -1, -1, -1 },
	{ "Miriam Fixed", 449, -1, -1, -1 },
	{ "Miriam Transparent", 451, -1, -1, -1 },
	{ "Mistral", 443, -1, -1, -1 },
	{ "Modern No. 20", 445, -1, -1, -1 },
	{ "MoeumT R", 341, -1, -1, -1 },
	{ "Mongolian Baiti", 446, -1, -1, -1 },
	{ "Monotype Corsiva", -1, 468, -1, -1 },
	{ "Monotype Hadassah", 309, -1, 310, -1 },
	{ "Monotype Koufi", -1, 374, -1, -1 },
	{ "Monotype Sorts", 469, -1, -1, -1 },
	{ "MoolBoran", 447, -1, -1, -1 },
	{ "Mudir MT", -1, 470, -1, -1 },
	{ "Myanmar Text", 444, -1, -1, -1 },
	{ "NSimSun", 580, -1, -1, -1 },
	{ "Narkisim", 477, -1, -1, -1 },
	{ "New Gulim", 472, -1, -1, -1 },
	{ "Niagara Engraved", 473, -1, -1, -1 },
	{ "Niagara Solid", 474, -1, -1, -1 },
	{ "Nirmala UI", 475, -1, 476, -1 },
	{ "Nyala", 480, -1, -1, -1 },
	{ "OCR A Extended", 481, -1, -1, -1 },
	{ "OCRB", 482, -1, -1, -1 },
	{ "Old Antic Bold", 483, -1, -1, -1 },
	{ "Old Antic Decorative", 484, -1, -1, -1 },
	{ "Old Antic Outline", 485, -1, -1, -1 },
	{ "Old Antic Outline Shaded", 487, -1, -1, -1 },
	{ "Old English Text MT", 486, -1, -1, -1 },
	{ "Onyx", 488, -1, -1, -1 },
	{ "Open Sans", 495, 494, 489, 490 },
	{ "Open Sans Condensed", -1, -1, 491, -1 },
	{ "Open Sans Condensed Light", 492, 493, -1, -1 },
	{ "OpenSymbol", 496, -1, -1, -1 },
	{ "Oswald", 498, -1, 497, -1 },
	{ "PMingLiU", 435, -1, -1, -1 },
	{ "PMingLiU-ExtB", 438, -1, -1, -1 },
	{ "PT Bold Arch", 520, -1, -1, -1 },
	{ "PT Bold Broken", 521, -1, -1, -1 },
	{ "PT Bold Dusky", 522, -1, -1, -1 },
	{ "PT Bold Heading", 523, -1, -1, -1 },
	{ "PT Bold Mirror", 524, -1, -1, -1 },
	{ "PT Bold Stars", 525, -1, -1, -1 },
	{ "PT Sans", 530, 529, 527, 528 },
	{ "PT Separated Baloon", 526, -1, -1, -1 },
	{ "PT Simple Bold Ruled", 585, -1, -1, -1 },
	{ "Pacifico", 500, -1, -1, -1 },
	{ "Palace Script MT", -1, 505, -1, -1 },
	{ "Palatino Linotype", 501, 504, 502, 503 },
	{ "Papyrus", 506, -1, -1, -1 },
	{ "Parchment", 507, -1, -1, -1 },
	{ "Perpetua", 513, 510, 509, 508 },
	{ "Perpetua Titling MT", 512, -1, 511, -1 },
	{ "Plantagenet Cherokee", 516, -1, -1, -1 },
	{ "Playbill", 517, -1, -1, -1 },
	{ "Poor Richard", 518, -1, -1, -1 },
	{ "Pristina", 519, -1, -1, -1 },
	{ "Pyunji R", 344, -1, -1, -1 },
	{ "Raavi", 531, -1, 532, -1 },
	{ "Rage Italic", 533, -1, -1, -1 },
	{ "Ravie", 536, -1, -1, -1 },
	{ "Rockwell", 541, 545, 542, 543 },
	{ "Rockwell Condensed", 540, -1, 539, -1 },
	{ "Rockwell Extra Bold", 544, -1, -1, -1 },
	{ "Rod", 546, -1, -1, -1 },
	{ "Rod Transparent", 547, -1, -1, -1 },
	{ "STCaiyun", 591, -1, -1, -1 },
	{ "STFangsong", 593, -1, -1, -1 },
	{ "STHupo", 594, -1, -1, -1 },
	{ "STKaiti", 595, -1, -1, -1 },
	{ "STLiti", 596, -1, -1, -1 },
	{ "STSong", 597, -1, -1, -1 },
	{ "STXihei", 598, -1, -1, -1 },
	{ "STXingkai", 599, -1, -1, -1 },
	{ "STXinwei", 600, -1, -1, -1 },
	{ "STZhongsong", 601, -1, -1, -1 },
	{ "Sakkal Majalla", 414, -1, 415, -1 },
	{ "Script MT Bold", 551, -1, -1, -1 },
	{ "Segoe Print", 552, -1, 553, -1 },
	{ "Segoe Script", 554, -1, 555, -1 },
	{ "Segoe UI", 556, 558, 557, 561 },
	{ "Segoe UI Light", 559, 562, -1, -1 },
	{ "Segoe UI Semibold", 563, 564, -1, -1 },
	{ "Segoe UI Semilight", 560, 565, -1, -1 },
	{ "Segoe UI Symbol", 566, -1, -1, -1 },
	{ "Shonar Bangla", 567, -1, 568, -1 },
	{ "Showcard Gothic", 569, -1, -1, -1 },
	{ "Shruti", 570, -1, 571, -1 },
	{ "SimHei", 573, -1, -1, -1 },
	{ "SimSun", 579, -1, -1, -1 },
	{ "SimSun-ExtB", 581, -1, -1, -1 },
	{ "Simple Bold Jut Out", 584, -1, -1, -1 },
	{ "Simple Indust Outline", 586, -1, -1, -1 },
	{ "Simple Indust Shaded", 587, -1, -1, -1 },
	{ "Simple Outline Pat", 588, -1, -1, -1 },
	{ "Simplified Arabic", 578, -1, 576, -1 },
	{ "Simplified Arabic Fixed", 577, -1, -1, -1 },
	{ "Snap ITC", 583, -1, -1, -1 },
	{ "Stencil", 592, -1, -1, -1 },
	{ "Sylfaen", 602, -1, -1, -1 },
	{ "Symbol", 603, -1, -1, -1 },
	{ "Tahoma", 604, -1, 605, -1 },
	{ "Tall Paul", 608, -1, -1, -1 },
	{ "Tempus Sans ITC", 616, -1, -1, -1 },
	{ "Times New Roman", 617, 620, 618, 619 },
	{ "Traditional Arabic", 622, -1, 621, -1 },
	{ "Trebuchet MS", 623, 626, 624, 625 },
	{ "Tunga", 627, -1, 628, -1 },
	{ "Tw Cen MT", 615, 614, 610, 609 },
	{ "Tw Cen MT Condensed", 613, -1, 611, -1 },
	{ "Tw Cen MT Condensed Extra Bold", 612, -1, -1, -1 },
	{ "Ubuntu", 634, 633, 631, 632 },
	{ "Ubuntu Condensed", 635, -1, -1, -1 },
	{ "Urdu Typesetting", 664, -1, -1, -1 },
	{ "Utsaah", 665, 668, 666, 667 },
	{ "Vani", 669, -1, 670, -1 },
	{ "Verdana", 671, 673, 672, 674 },
	{ "Vijaya", 675, -1, 676, -1 },
	{ "Viner Hand ITC", 679, -1, -1, -1 },
	{ "Vivaldi", -1, 680, -1, -1 },
	{ "Vladimir Script", 681, -1, -1, -1 },
	{ "Vrinda", 682, -1, 683, -1 },
	{ "Webdings", 684, -1, -1, -1 },
	{ "Wide Latin", 380, -1, -1, -1 },
	{ "Wingdings", 685, -1, -1, -1 },
	{ "Wingdings 2", 686, -1, -1, -1 },
	{ "Wingdings 3", 687, -1, -1, -1 },
	{ "Yet R", 343, -1, -1, -1 },
	{ "YouYuan", 582, -1, -1, -1 }
};

static const int FD_Ascii_Names_Offsets[256] =
{
	  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
	  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
	  -1,   0,  21,  46,  73, 104, 116, 136, 183, 229, 235, 238, 248, 266, 313, 320, 333,  -1, 356, 364, 399, 409, 413, 420,  -1, 425,  -1,  -1,  -1,  -1,  -1,  -1,
	  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
	  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
	  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
	  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
	  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1
};

#define FONTS_DICT_UNICODE_NAMES_COUNT 91
static const FD_FontMapRecW FD_Unicode_Names[FONTS_DICT_UNICODE_NAMES_COUNT] = 
{
	{ L"HGP創英角ｺﾞｼｯｸUB", 336, -1, -1, -1 },
	{ L"HGP創英角ﾎﾟｯﾌﾟ体", 330, -1, -1, -1 },
	{ L"HGP創英ﾌﾟﾚｾﾞﾝｽEB", 333, -1, -1, -1 },
	{ L"HGP教科書体", 321, -1, -1, -1 },
	{ L"HGP明朝B", 324, -1, -1, -1 },
	{ L"HGP明朝E", 327, -1, -1, -1 },
	{ L"HGP行書体", 318, -1, -1, -1 },
	{ L"HGPｺﾞｼｯｸE", 312, -1, -1, -1 },
	{ L"HGPｺﾞｼｯｸM", 315, -1, -1, -1 },
	{ L"HGS創英角ｺﾞｼｯｸUB", 337, -1, -1, -1 },
	{ L"HGS創英角ﾎﾟｯﾌﾟ体", 331, -1, -1, -1 },
	{ L"HGS創英ﾌﾟﾚｾﾞﾝｽEB", 334, -1, -1, -1 },
	{ L"HGS教科書体", 322, -1, -1, -1 },
	{ L"HGS明朝B", 325, -1, -1, -1 },
	{ L"HGS明朝E", 328, -1, -1, -1 },
	{ L"HGS行書体", 319, -1, -1, -1 },
	{ L"HGSｺﾞｼｯｸE", 313, -1, -1, -1 },
	{ L"HGSｺﾞｼｯｸM", 316, -1, -1, -1 },
	{ L"HG丸ｺﾞｼｯｸM-PRO", 339, -1, -1, -1 },
	{ L"HG創英角ｺﾞｼｯｸUB", 335, -1, -1, -1 },
	{ L"HG創英角ﾎﾟｯﾌﾟ体", 329, -1, -1, -1 },
	{ L"HG創英ﾌﾟﾚｾﾞﾝｽEB", 332, -1, -1, -1 },
	{ L"HG教科書体", 320, -1, -1, -1 },
	{ L"HG明朝B", 323, -1, -1, -1 },
	{ L"HG明朝E", 326, -1, -1, -1 },
	{ L"HG正楷書体-PRO", 338, -1, -1, -1 },
	{ L"HG行書体", 317, -1, -1, -1 },
	{ L"HGｺﾞｼｯｸE", 311, -1, -1, -1 },
	{ L"HGｺﾞｼｯｸM", 314, -1, -1, -1 },
	{ L"HY견고딕", 297, -1, -1, -1 },
	{ L"HY견명조", 300, -1, -1, -1 },
	{ L"HY궁서B", 296, -1, -1, -1 },
	{ L"HY그래픽M", 295, -1, -1, -1 },
	{ L"HY목각파임B", 302, -1, -1, -1 },
	{ L"HY신명조", 301, -1, -1, -1 },
	{ L"HY얕은샘물M", 305, -1, -1, -1 },
	{ L"HY엽서L", 303, -1, -1, -1 },
	{ L"HY엽서M", 304, -1, -1, -1 },
	{ L"HY중고딕", 298, -1, -1, -1 },
	{ L"HY헤드라인M", 299, -1, -1, -1 },
	{ L"メイリオ", 425, 426, 429, 430 },
	{ L"仿宋", 572, -1, -1, -1 },
	{ L"华文中宋", 601, -1, -1, -1 },
	{ L"华文仿宋", 593, -1, -1, -1 },
	{ L"华文宋体", 597, -1, -1, -1 },
	{ L"华文彩云", 591, -1, -1, -1 },
	{ L"华文新魏", 600, -1, -1, -1 },
	{ L"华文楷体", 595, -1, -1, -1 },
	{ L"华文琥珀", 594, -1, -1, -1 },
	{ L"华文细黑", 598, -1, -1, -1 },
	{ L"华文行楷", 599, -1, -1, -1 },
	{ L"华文隶书", 596, -1, -1, -1 },
	{ L"宋体", 579, -1, -1, -1 },
	{ L"幼圆", 582, -1, -1, -1 },
	{ L"微軟正黑體", 455, -1, 457, -1 },
	{ L"微软雅黑", 463, -1, 465, -1 },
	{ L"新宋体", 580, -1, -1, -1 },
	{ L"新細明體", 435, -1, -1, -1 },
	{ L"新細明體-ExtB", 438, -1, -1, -1 },
	{ L"方正姚体", 237, -1, -1, -1 },
	{ L"方正舒体", 236, -1, -1, -1 },
	{ L"楷体", 574, -1, -1, -1 },
	{ L"標楷體", 361, -1, -1, -1 },
	{ L"王漢宗中明體繁", 688, -1, -1, -1 },
	{ L"細明體", 434, -1, -1, -1 },
	{ L"細明體-ExtB", 437, -1, -1, -1 },
	{ L"細明體_HKSCS", 436, -1, -1, -1 },
	{ L"細明體_HKSCS-ExtB", 439, -1, -1, -1 },
	{ L"隶书", 575, -1, -1, -1 },
	{ L"黑体", 573, -1, -1, -1 },
	{ L"굴림", 288, -1, -1, -1 },
	{ L"굴림체", 289, -1, -1, -1 },
	{ L"궁서", 49, -1, -1, -1 },
	{ L"궁서체", 50, -1, -1, -1 },
	{ L"돋움", 290, -1, -1, -1 },
	{ L"돋움체", 291, -1, -1, -1 },
	{ L"맑은 고딕", 416, -1, 417, -1 },
	{ L"바탕", 47, -1, -1, -1 },
	{ L"바탕체", 48, -1, -1, -1 },
	{ L"새굴림", 472, -1, -1, -1 },
	{ L"휴먼둥근헤드라인", 347, -1, -1, -1 },
	{ L"휴먼매직체", 346, -1, -1, -1 },
	{ L"휴먼모음T", 341, -1, -1, -1 },
	{ L"휴먼아미체", 345, -1, -1, -1 },
	{ L"휴먼엑스포", 342, -1, -1, -1 },
	{ L"휴먼옛체", 343, -1, -1, -1 },
	{ L"휴먼편지체", 344, -1, -1, -1 },
	{ L"ＭＳ ゴシック", 452, -1, -1, -1 },
	{ L"ＭＳ 明朝", 459, -1, -1, -1 },
	{ L"ＭＳ Ｐゴシック", 454, -1, -1, -1 },
	{ L"ＭＳ Ｐ明朝", 460, -1, -1, -1 },
};

typedef struct FD_Font_Rec
{
	const char*		m_name;

	long			m_lIndex;

	unsigned char	m_bBold;
	unsigned char	m_bItalic;
	unsigned char	m_bIsFixed;

	unsigned char	m_aPanose[10];
	unsigned long	m_ulUnicodeRange1;
	unsigned long	m_ulUnicodeRange2;
	unsigned long	m_ulUnicodeRange3;
	unsigned long	m_ulUnicodeRange4;

	unsigned long	m_ulCodePageRange1;
	unsigned long	m_ulCodePageRange2;

	unsigned short	m_usWeigth;
	unsigned short	m_usWidth;

	short			m_sFamilyClass;
	unsigned char	m_eFontFormat;

	short			m_shAvgCharWidth;
	short			m_shAscent;
	short			m_shDescent;
	short			m_shLineGap;
	short			m_shXHeight;
	short			m_shCapHeight;
} FD_Font;

#define FONTS_DICT_ASCII_FONTS_COUNT 689
static const FD_Font FD_Ascii_Files[FONTS_DICT_ASCII_FONTS_COUNT] = 
{
	{"Agency FB", 0, 1, 0, 0, { 2, 11, 8, 4, 2, 2, 2, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 700, 3, 2048, 1, 341, 764, -180, 80, 0, 0 },
	{"Agency FB", 0, 0, 0, 0, { 2, 11, 5, 3, 2, 2, 2, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 3, 2048, 1, 316, 764, -180, 80, 0, 0 },
	{"Guttman-Aharoni", 0, 1, 0, 0, { 2, 1, 7, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 396, 746, -336, 0, 0, 0 },
	{"Aharoni", 0, 1, 0, 0, { 2, 1, 8, 3, 2, 1, 4, 3, 2, 3 }, 2049, 0, 0, 0, 32, 2097152, 700, 5, 0, 1, 477, 734, -265, 0, 0, 0 },
	{"Akhbar MT", 0, 0, 1, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 0, 1, 408, 529, -414, 125, 0, 0 },
	{"Akhbar MT", 0, 1, 1, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 700, 5, 0, 1, 425, 555, -367, 147, 0, 0 },
	{"Aldhabi", 0, 0, 0, 0, { 1, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2684362863, 2415951947, 0, 0, 65, 0, 400, 5, 0, 1, 511, 643, -356, 750, 262, 386 },
	{"Algerian", 0, 0, 0, 0, { 4, 2, 7, 5, 4, 10, 2, 6, 7, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 2, 0, 1, 541, 888, -223, 208, 0, 0 },
	{"Andalus", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 8195, 2147483648, 8, 0, 65, 537395200, 400, 5, 0, 1, 473, 1104, -421, 0, 510, 711 },
	{"Angsana New", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 261, 1, 264, 922, -239, 0, 283, 437 },
	{"Angsana New", 0, 1, 0, 0, { 2, 2, 8, 3, 7, 5, 5, 2, 3, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 261, 1, 281, 888, -239, 0, 283, 437 },
	{"Angsana New", 0, 0, 1, 0, { 2, 2, 5, 3, 5, 4, 5, 9, 3, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 261, 1, 265, 923, -239, 0, 283, 437 },
	{"AngsanaUPC", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 261, 1, 264, 922, -239, 0, 283, 437 },
	{"AngsanaUPC", 0, 1, 0, 0, { 2, 2, 8, 3, 7, 5, 5, 2, 3, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 261, 1, 281, 888, -239, 0, 283, 437 },
	{"AngsanaUPC", 0, 0, 1, 0, { 2, 2, 5, 3, 5, 4, 5, 9, 3, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 261, 1, 265, 923, -239, 0, 283, 437 },
	{"AngsanaUPC", 0, 1, 1, 0, { 2, 2, 7, 3, 6, 5, 5, 9, 3, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 261, 1, 271, 927, -206, 0, 283, 437 },
	{"Angsana New", 0, 1, 1, 0, { 2, 2, 7, 3, 6, 5, 5, 9, 3, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 261, 1, 271, 927, -206, 0, 283, 437 },
	{"Book Antiqua", 0, 1, 0, 0, { 2, 4, 7, 2, 5, 3, 5, 3, 3, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 700, 5, 260, 1, 458, 726, -265, 78, 0, 0 },
	{"Book Antiqua", 0, 1, 1, 0, { 2, 4, 7, 2, 6, 3, 5, 10, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 700, 5, 260, 1, 446, 729, -274, 64, 0, 0 },
	{"Book Antiqua", 0, 0, 1, 0, { 2, 4, 5, 2, 5, 3, 5, 10, 3, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 260, 1, 400, 725, -279, 64, 0, 0 },
	{"Aparajita", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 400, 5, 0, 1, 495, 732, -242, 95, 359, 533 },
	{"Aparajita", 0, 1, 0, 0, { 2, 11, 8, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 700, 5, 0, 1, 522, 732, -242, 95, 367, 533 },
	{"Aparajita", 0, 1, 1, 0, { 2, 11, 8, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 700, 5, 0, 1, 518, 732, -242, 95, 364, 533 },
	{"Aparajita", 0, 0, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 400, 5, 0, 1, 493, 732, -242, 95, 355, 533 },
	{"AGA Arabesque", 0, 0, 0, 0, { 5, 1, 1, 1, 1, 1, 1, 1, 1, 1 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 3072, 1, 551, 770, 205, 23, 0, 0 },
	{"Arabic Typesetting", 0, 0, 0, 0, { 3, 2, 4, 2, 4, 4, 6, 3, 2, 3 }, 2684362863, 3221225472, 8, 0, 536871123, 0, 400, 5, 0, 1, 283, 527, -341, 0, 500, 700 },
	{"Guttman-Aram", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 374, 746, -336, 0, 0, 0 },
	{"AGA Arabesque Desktop", 0, 0, 0, 0, { 5, 1, 1, 1, 1, 1, 1, 1, 1, 1 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 0, 1, 942, 500, 500, 0, 0, 0 },
	{"Arial", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 3758107391, 3221256259, 9, 0, 1073742335, 4294901760, 400, 5, 2053, 1, 441, 728, -210, 149, 518, 716 },
	{"Arial", 0, 1, 0, 0, { 2, 11, 7, 4, 2, 2, 2, 2, 2, 4 }, 3758107391, 3221256259, 9, 0, 1073742335, 4294901760, 700, 5, 2053, 1, 478, 728, -210, 149, 518, 715 },
	{"Arial", 0, 1, 1, 0, { 2, 11, 7, 4, 2, 2, 2, 9, 2, 4 }, 3758099199, 30787, 1, 0, 1073742271, 3757506560, 700, 5, 2053, 1, 478, 728, -210, 149, 518, 715 },
	{"Arial", 0, 0, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 9, 2, 4 }, 3758099199, 30787, 1, 0, 1073742271, 3757506560, 400, 5, 2053, 1, 441, 728, -207, 149, 518, 715 },
	{"Arial Narrow", 0, 0, 0, 0, { 2, 11, 6, 6, 2, 2, 2, 3, 2, 4 }, 647, 2048, 0, 0, 536871071, 3755409408, 400, 3, 2053, 1, 361, 728, -210, 131, 0, 0 },
	{"Arial Narrow", 0, 1, 0, 0, { 2, 11, 7, 6, 2, 2, 2, 3, 2, 4 }, 647, 2048, 0, 0, 536871071, 3755409408, 700, 3, 2053, 1, 392, 728, -210, 131, 0, 0 },
	{"Arial Narrow", 0, 1, 1, 0, { 2, 11, 7, 6, 2, 2, 2, 10, 2, 4 }, 647, 2048, 0, 0, 536871071, 3755409408, 700, 3, 2053, 1, 392, 728, -210, 131, 0, 0 },
	{"Arial Narrow", 0, 0, 1, 0, { 2, 11, 6, 6, 2, 2, 2, 10, 2, 4 }, 647, 2048, 0, 0, 536871071, 3755409408, 400, 3, 2053, 1, 361, 728, -207, 134, 0, 0 },
	{"Arial Unicode MS", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 4294967295, 3925868543, 63, 0, 1614742015, 4294901760, 400, 5, 2053, 1, 441, 728, -209, 131, 0, 0 },
	{"Arial Black", 0, 0, 0, 0, { 2, 11, 10, 4, 2, 1, 2, 2, 2, 4 }, 2684355247, 1073772795, 0, 0, 1610612895, 3755409408, 900, 5, 2053, 1, 552, 715, 211, 142, 518, 715 },
	{"Arial Rounded MT Bold", 0, 0, 0, 0, { 2, 15, 7, 4, 3, 5, 4, 3, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2053, 1, 483, 728, -209, 131, 0, 0 },
	{"Arabic Transparent", 0, 1, 0, 0, { 2, 1, 0, 0, 0, 0, 0, 0, 0, 0 }, 8192, 0, 0, 0, 64, 0, 700, 5, 0, 1, 441, 849, -326, 0, 0, 0 },
	{"Arabic Transparent", 0, 0, 0, 0, { 2, 1, 0, 0, 0, 0, 0, 0, 0, 0 }, 8192, 0, 0, 0, 64, 0, 400, 5, 0, 1, 408, 849, -326, 0, 0, 0 },
	{"Arvo", 0, 1, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2147483687, 134217792, 335544320, 0, 1, 0, 700, 5, 0, 1, 517, 759, -229, 53, 505, 740 },
	{"Arvo", 0, 1, 1, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2147483687, 65, 0, 0, 536871185, 1073741824, 700, 5, 0, 1, 518, 759, -229, 53, 505, 740 },
	{"Arvo", 0, 0, 1, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2147483815, 65, 0, 0, 536871185, 1073741824, 400, 5, 0, 1, 500, 759, -229, 53, 505, 740 },
	{"Arvo", 0, 0, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2147483815, 65, 0, 0, 536871185, 1073741824, 400, 5, 0, 1, 472, 759, -229, 53, 505, 740 },
	{"Aston-F1", 0, 0, 0, 0, { 0, 0, 4, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 0, 0, 400, 5, 0, 1, 90, 469, 0, 0, 0, 0 },
	{"Baskerville Old Face", 0, 0, 0, 0, { 2, 2, 6, 2, 8, 5, 5, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 392, 687, -178, 203, 0, 0 },
	{"Batang", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2952790703, 1775729915, 48, 0, 1074266271, 3755409408, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"BatangChe", 1, 0, 0, 4, { 2, 3, 6, 9, 0, 1, 1, 1, 1, 1 }, 2952790703, 1775729915, 48, 0, 1074266271, 3755409408, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"Gungsuh", 2, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2952790703, 1775729915, 48, 0, 1074266271, 3755409408, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"GungsuhChe", 3, 0, 0, 4, { 2, 3, 6, 9, 0, 1, 1, 1, 1, 1 }, 2952790703, 1775729915, 48, 0, 1074266271, 3755409408, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"Bauhaus 93", 0, 0, 0, 0, { 4, 3, 9, 5, 2, 11, 2, 2, 12, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 443, 898, -232, 328, 0, 0 },
	{"Bell MT", 0, 0, 0, 0, { 2, 2, 5, 3, 6, 3, 5, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 513, 1, 407, 668, -266, 134, 0, 0 },
	{"Bell MT", 0, 1, 0, 0, { 2, 3, 7, 3, 6, 5, 10, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 513, 1, 438, 667, -264, 137, 0, 0 },
	{"Bell MT", 0, 0, 1, 0, { 2, 3, 6, 3, 6, 5, 10, 9, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 513, 1, 366, 669, -266, 134, 0, 0 },
	{"Bernard MT Condensed", 0, 0, 0, 0, { 2, 5, 8, 6, 6, 9, 5, 2, 4, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 3, 1029, 1, 387, 805, -120, 143, 0, 0 },
	{"Bickham Script Pro Regular", 0, 1, 0, 0, { 3, 3, 8, 2, 4, 7, 7, 13, 13, 6 }, 2147483823, 1342185547, 0, 0, 147, 0, 700, 5, 0, 2, 533, 680, -320, 200, 638, 750 },
	{"Book Antiqua", 0, 0, 0, 0, { 2, 4, 6, 2, 5, 3, 5, 3, 3, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 260, 1, 445, 727, -282, 60, 0, 0 },
	{"Bold Italic Art", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 653, 1517, 568, 0, 0, 0 },
	{"Bodoni MT", 0, 1, 0, 0, { 2, 7, 8, 3, 8, 6, 6, 2, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 769, 1, 408, 664, -256, 148, 0, 0 },
	{"Bodoni MT", 0, 1, 1, 0, { 2, 7, 8, 3, 8, 6, 6, 9, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 769, 1, 459, 666, -255, 146, 0, 0 },
	{"Bodoni MT Black", 0, 0, 1, 0, { 2, 7, 10, 3, 8, 6, 6, 9, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 900, 5, 769, 1, 543, 660, -209, 199, 0, 0 },
	{"Bodoni MT Black", 0, 0, 0, 0, { 2, 7, 10, 3, 8, 6, 6, 2, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 900, 5, 769, 1, 509, 669, -228, 171, 0, 0 },
	{"Bodoni MT Condensed", 0, 1, 0, 0, { 2, 7, 8, 6, 8, 6, 6, 2, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 3, 769, 1, 336, 643, -211, 214, 0, 0 },
	{"Bodoni MT Condensed", 0, 1, 1, 0, { 2, 7, 8, 6, 8, 6, 6, 9, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 3, 769, 1, 338, 643, -211, 214, 0, 0 },
	{"Bodoni MT Condensed", 0, 0, 1, 0, { 2, 7, 6, 6, 8, 6, 6, 9, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 3, 769, 1, 287, 643, -211, 214, 0, 0 },
	{"Bodoni MT Condensed", 0, 0, 0, 0, { 2, 7, 6, 6, 8, 6, 6, 2, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 3, 769, 1, 284, 643, -211, 214, 0, 0 },
	{"Bodoni MT", 0, 0, 1, 0, { 2, 7, 6, 3, 8, 6, 6, 9, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 769, 1, 389, 666, -250, 151, 0, 0 },
	{"Bodoni MT Poster Compressed", 0, 0, 0, 0, { 2, 7, 7, 6, 8, 6, 1, 5, 2, 4 }, 3, 0, 0, 0, 536870929, 0, 300, 2, 769, 1, 239, 740, -181, 147, 0, 0 },
	{"Bodoni MT", 0, 0, 0, 0, { 2, 7, 6, 3, 8, 6, 6, 2, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 769, 1, 417, 662, -258, 148, 0, 0 },
	{"Bookman Old Style", 0, 0, 0, 0, { 2, 5, 6, 4, 5, 5, 5, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 300, 5, 261, 1, 492, 716, -225, 128, 0, 0 },
	{"Bookman Old Style", 0, 1, 0, 0, { 2, 5, 8, 4, 4, 5, 5, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 600, 5, 261, 1, 526, 716, -223, 129, 0, 0 },
	{"Bookman Old Style", 0, 1, 1, 0, { 2, 5, 8, 4, 4, 5, 5, 9, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 600, 5, 261, 1, 538, 716, -223, 130, 0, 0 },
	{"Bookman Old Style", 0, 0, 1, 0, { 2, 5, 6, 4, 5, 5, 5, 9, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 300, 5, 261, 1, 482, 716, -230, 123, 0, 0 },
	{"Bradley Hand ITC", 0, 0, 0, 0, { 3, 7, 4, 2, 5, 3, 2, 3, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2566, 1, 418, 698, -269, 102, 0, 0 },
	{"Britannic Bold", 0, 0, 0, 0, { 2, 11, 9, 3, 6, 7, 3, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 446, 676, -166, 256, 0, 0 },
	{"Berlin Sans FB", 0, 1, 0, 0, { 2, 14, 9, 2, 2, 5, 2, 2, 3, 6 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 2050, 1, 476, 906, -331, 104, 0, 0 },
	{"Berlin Sans FB Demi", 0, 1, 0, 0, { 2, 14, 8, 2, 2, 5, 2, 2, 3, 6 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 2050, 1, 444, 924, -204, 96, 0, 0 },
	{"Berlin Sans FB", 0, 0, 0, 0, { 2, 14, 6, 2, 2, 5, 2, 2, 3, 6 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2050, 1, 413, 895, -203, 92, 0, 0 },
	{"Broadway", 0, 0, 0, 0, { 4, 4, 9, 5, 8, 11, 2, 2, 5, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2304, 1, 543, 705, -23, 0, 0, 0 },
	{"Browallia New", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 2053, 1, 294, 840, -289, 0, 338, 467 },
	{"Browallia New", 0, 1, 0, 0, { 2, 11, 7, 4, 2, 2, 2, 2, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 2053, 1, 312, 872, -256, 0, 338, 467 },
	{"Browallia New", 0, 0, 1, 0, { 2, 11, 3, 4, 2, 2, 2, 9, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 2053, 1, 288, 839, -294, 0, 338, 467 },
	{"BrowalliaUPC", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 2053, 1, 294, 840, -289, 0, 338, 467 },
	{"BrowalliaUPC", 0, 1, 0, 0, { 2, 11, 7, 4, 2, 2, 2, 2, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 2053, 1, 312, 872, -256, 0, 338, 467 },
	{"BrowalliaUPC", 0, 0, 1, 0, { 2, 11, 3, 4, 2, 2, 2, 9, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 2053, 1, 288, 839, -294, 0, 338, 467 },
	{"BrowalliaUPC", 0, 1, 1, 0, { 2, 11, 7, 4, 2, 2, 2, 9, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 2053, 1, 319, 872, -256, 0, 338, 467 },
	{"Browallia New", 0, 1, 1, 0, { 2, 11, 7, 4, 2, 2, 2, 9, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 2053, 1, 319, 872, -256, 0, 338, 467 },
	{"Brush Script MT", 0, 0, 1, 0, { 3, 6, 8, 2, 4, 4, 6, 7, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2562, 1, 319, 600, -247, 222, 0, 0 },
	{"Bookshelf Symbol 7", 0, 0, 0, 0, { 5, 1, 1, 1, 1, 1, 1, 1, 1, 1 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 2053, 1, 656, 859, -140, 0, 0, 0 },
	{"Calibri", 0, 0, 0, 0, { 2, 15, 5, 2, 2, 2, 4, 3, 2, 4 }, 3758097151, 1073786111, 1, 0, 536871327, 0, 400, 5, 2048, 1, 520, 750, -250, 220, 464, 631 },
	{"Calibri", 0, 1, 0, 0, { 2, 15, 7, 2, 3, 4, 4, 3, 2, 4 }, 3758097151, 1073786111, 1, 0, 536871327, 0, 700, 5, 2048, 1, 536, 750, -250, 220, 468, 631 },
	{"Calibri", 0, 0, 1, 0, { 2, 15, 5, 2, 2, 2, 4, 10, 2, 4 }, 3758097151, 1073786111, 1, 0, 536871327, 0, 400, 5, 2048, 1, 520, 750, -250, 220, 467, 633 },
	{"Calibri Light", 0, 0, 0, 0, { 2, 15, 3, 2, 2, 2, 4, 3, 2, 4 }, 2684355311, 1073750139, 0, 0, 536871327, 0, 300, 5, 2048, 1, 520, 750, -250, 220, 461, 631 },
	{"Calibri Light", 0, 0, 1, 0, { 2, 15, 3, 2, 2, 2, 4, 3, 2, 4 }, 2684355311, 1073750139, 0, 0, 536871327, 0, 300, 5, 2048, 1, 520, 750, -250, 220, 464, 631 },
	{"Calibri", 0, 1, 1, 0, { 2, 15, 7, 2, 3, 4, 4, 10, 2, 4 }, 3758097151, 1073786111, 1, 0, 536871327, 0, 700, 5, 2048, 1, 536, 750, -250, 220, 468, 631 },
	{"Californian FB", 0, 1, 0, 0, { 2, 7, 6, 3, 6, 8, 11, 3, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 259, 1, 427, 745, -254, 84, 0, 0 },
	{"Californian FB", 0, 0, 1, 0, { 2, 7, 4, 3, 6, 8, 11, 10, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 256, 1, 337, 745, -254, 84, 0, 0 },
	{"Californian FB", 0, 0, 0, 0, { 2, 7, 4, 3, 6, 8, 11, 3, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 259, 1, 404, 745, -254, 84, 0, 0 },
	{"Calisto MT", 0, 0, 0, 0, { 2, 4, 6, 3, 5, 5, 5, 3, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 258, 1, 421, 712, -209, 147, 0, 0 },
	{"Calisto MT", 0, 1, 0, 0, { 2, 4, 7, 3, 6, 5, 5, 2, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 258, 1, 436, 712, -209, 147, 0, 0 },
	{"Calisto MT", 0, 1, 1, 0, { 2, 4, 7, 3, 5, 5, 5, 9, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 258, 1, 393, 712, -209, 147, 0, 0 },
	{"Calisto MT", 0, 0, 1, 0, { 2, 4, 6, 3, 5, 5, 5, 9, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1029, 1, 373, 711, -209, 147, 0, 0 },
	{"Cambria", 0, 0, 0, 0, { 2, 4, 5, 3, 5, 4, 6, 3, 2, 4 }, 3758097151, 1073743103, 0, 0, 536871327, 0, 400, 5, 527, 1, 615, 777, -222, 172, 466, 666 },
	{"Cambria Math", 1, 0, 0, 0, { 2, 4, 5, 3, 5, 4, 6, 3, 2, 4 }, 3758097151, 1107305727, 0, 0, 536871327, 0, 400, 5, 527, 1, 615, 777, -222, 172, 466, 666 },
	{"Cambria", 0, 1, 0, 0, { 2, 4, 8, 3, 5, 4, 6, 3, 2, 4 }, 3758097151, 1073742943, 0, 0, 536871327, 0, 700, 5, 527, 1, 599, 777, -222, 172, 484, 666 },
	{"Cambria", 0, 0, 1, 0, { 2, 4, 5, 3, 5, 4, 6, 10, 2, 4 }, 3758097151, 1073742943, 0, 0, 536871327, 0, 400, 5, 527, 1, 542, 777, -222, 172, 466, 666 },
	{"Cambria", 0, 1, 1, 0, { 2, 4, 8, 3, 5, 4, 6, 10, 2, 4 }, 3758097151, 1073742943, 0, 0, 536871327, 0, 700, 5, 527, 1, 584, 777, -222, 172, 484, 666 },
	{"Candara", 0, 0, 0, 0, { 2, 14, 5, 2, 3, 3, 3, 2, 2, 4 }, 2684355311, 1073783883, 0, 0, 536871327, 0, 400, 5, 2050, 1, 521, 724, -275, 220, 463, 638 },
	{"Candara", 0, 1, 0, 0, { 2, 14, 7, 2, 3, 3, 3, 2, 2, 4 }, 2684355311, 1073783883, 0, 0, 536871327, 0, 700, 5, 2050, 1, 528, 724, -275, 220, 463, 638 },
	{"Candara", 0, 0, 1, 0, { 2, 14, 5, 2, 3, 3, 3, 9, 2, 4 }, 2684355311, 1073783883, 0, 0, 536871327, 0, 400, 5, 2050, 1, 503, 724, -275, 220, 469, 638 },
	{"Candara", 0, 1, 1, 0, { 2, 14, 7, 2, 3, 3, 3, 9, 2, 4 }, 2684355311, 1073783883, 0, 0, 536871327, 0, 700, 5, 2050, 1, 517, 724, -275, 220, 469, 638 },
	{"Castellar", 0, 0, 0, 0, { 2, 10, 4, 2, 6, 4, 6, 1, 3, 1 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2308, 1, 666, 714, -3, 351, 0, 0 },
	{"Century Schoolbook", 0, 0, 0, 0, { 2, 4, 6, 4, 5, 5, 5, 2, 3, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 1026, 1, 464, 740, -194, 134, 0, 0 },
	{"Centaur", 0, 0, 0, 0, { 2, 3, 5, 4, 5, 2, 5, 2, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1026, 1, 362, 672, -277, 119, 0, 0 },
	{"Century", 0, 0, 0, 0, { 2, 4, 6, 4, 5, 5, 5, 2, 3, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 1026, 1, 464, 740, -194, 134, 0, 0 },
	{"Chiller", 0, 0, 0, 0, { 4, 2, 4, 4, 3, 16, 7, 2, 6, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 6, 2575, 1, 293, 848, -305, 0, 0, 0 },
	{"Colonna MT", 0, 0, 0, 0, { 4, 2, 8, 5, 6, 2, 2, 3, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2305, 1, 419, 595, -333, 141, 0, 0 },
	{"Comic Sans MS", 0, 0, 0, 0, { 3, 15, 7, 2, 3, 3, 2, 2, 2, 4 }, 647, 1073741843, 0, 0, 536871071, 0, 400, 5, 2568, 1, 468, 799, -291, 0, 539, 758 },
	{"Comic Sans MS", 0, 1, 0, 0, { 3, 15, 9, 2, 3, 3, 2, 2, 2, 4 }, 647, 1073741843, 0, 0, 536871071, 0, 700, 5, 2568, 1, 495, 799, -275, 0, 539, 758 },
	{"Comic Sans MS", 0, 0, 1, 0, { 3, 15, 7, 2, 3, 3, 2, 6, 2, 4 }, 647, 19, 0, 0, 536871071, 0, 400, 5, 2568, 1, 646, 799, -275, 0, 539, 758 },
	{"Comic Sans MS", 0, 1, 1, 0, { 3, 15, 9, 2, 3, 3, 2, 6, 2, 4 }, 647, 19, 0, 0, 536871071, 0, 700, 5, 2568, 1, 655, 799, -275, 0, 539, 758 },
	{"Consolas", 0, 0, 0, 4, { 2, 11, 6, 9, 2, 2, 4, 3, 2, 4 }, 3774874367, 1073806591, 9, 0, 1610613151, 3755409408, 400, 5, 2057, 1, 549, 742, -257, 170, 490, 638 },
	{"Consolas", 0, 1, 0, 4, { 2, 11, 7, 9, 2, 2, 4, 3, 2, 4 }, 3774874367, 1073806591, 9, 0, 1610613151, 3755409408, 700, 5, 2057, 1, 549, 742, -257, 170, 496, 638 },
	{"Consolas", 0, 0, 1, 4, { 2, 11, 6, 9, 2, 2, 4, 10, 2, 4 }, 3774874367, 1073806591, 9, 0, 1610613151, 3755409408, 400, 5, 2057, 1, 549, 742, -257, 170, 490, 638 },
	{"Consolas", 0, 1, 1, 4, { 2, 11, 7, 9, 2, 2, 4, 10, 2, 4 }, 3774874367, 1073806591, 9, 0, 1610613151, 3755409408, 700, 5, 2057, 1, 549, 742, -257, 170, 496, 638 },
	{"Constantia", 0, 0, 0, 0, { 2, 3, 6, 2, 5, 3, 6, 3, 3, 3 }, 2684355311, 1073750091, 0, 0, 536871327, 0, 400, 5, 0, 1, 541, 750, -249, 220, 453, 686 },
	{"Constantia", 0, 1, 0, 0, { 2, 3, 7, 2, 6, 3, 6, 3, 3, 3 }, 2684355311, 1073750091, 0, 0, 536871327, 0, 700, 5, 0, 1, 574, 750, -249, 220, 456, 686 },
	{"Constantia", 0, 0, 1, 0, { 2, 3, 6, 2, 5, 3, 6, 10, 3, 3 }, 2684355311, 1073750091, 0, 0, 536871327, 0, 400, 5, 0, 1, 533, 750, -249, 220, 458, 686 },
	{"Constantia", 0, 1, 1, 0, { 2, 3, 7, 2, 6, 3, 6, 10, 3, 3 }, 2684355311, 1073750091, 0, 0, 536871327, 0, 700, 5, 0, 1, 569, 750, -249, 220, 464, 686 },
	{"Cooper Black", 0, 0, 0, 0, { 2, 8, 9, 4, 4, 3, 11, 2, 4, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2304, 1, 511, 671, -192, 0, 0, 0 },
	{"Copperplate Gothic Bold", 0, 0, 0, 0, { 2, 14, 7, 5, 2, 2, 6, 2, 4, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2305, 1, 579, 578, -11, 0, 0, 0 },
	{"Copperplate Gothic Light", 0, 0, 0, 0, { 2, 14, 5, 7, 2, 2, 6, 2, 4, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2305, 1, 553, 578, -11, 0, 0, 0 },
	{"Corbel", 0, 0, 0, 0, { 2, 11, 5, 3, 2, 2, 4, 2, 2, 4 }, 2684355311, 1073783883, 0, 0, 536871327, 0, 400, 5, 0, 1, 529, 743, -256, 207, 463, 653 },
	{"Corbel", 0, 1, 0, 0, { 2, 11, 7, 3, 2, 2, 4, 2, 2, 4 }, 2684355311, 1073783883, 0, 0, 536871327, 0, 700, 5, 0, 1, 552, 743, -256, 207, 473, 653 },
	{"Corbel", 0, 0, 1, 0, { 2, 11, 5, 3, 2, 2, 4, 9, 2, 4 }, 2684355311, 1073783883, 0, 0, 536871327, 0, 400, 5, 0, 1, 515, 743, -256, 207, 463, 653 },
	{"Corbel", 0, 1, 1, 0, { 2, 11, 7, 3, 2, 2, 4, 9, 2, 4 }, 2684355311, 1073783883, 0, 0, 536871327, 0, 700, 5, 0, 1, 542, 743, -256, 207, 473, 653 },
	{"Cordia New", 0, 0, 0, 0, { 2, 11, 3, 4, 2, 2, 2, 2, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 2053, 1, 293, 893, -253, 0, 339, 468 },
	{"Cordia New", 0, 1, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 2053, 1, 296, 833, -261, 0, 339, 468 },
	{"Cordia New", 0, 0, 1, 0, { 2, 11, 3, 4, 2, 2, 2, 9, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 2053, 1, 299, 893, -253, 0, 339, 468 },
	{"CordiaUPC", 0, 0, 0, 0, { 2, 11, 3, 4, 2, 2, 2, 2, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 2053, 1, 293, 893, -253, 0, 339, 468 },
	{"CordiaUPC", 0, 1, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 2053, 1, 296, 833, -261, 0, 339, 468 },
	{"CordiaUPC", 0, 0, 1, 0, { 2, 11, 3, 4, 2, 2, 2, 9, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 400, 5, 2053, 1, 299, 893, -253, 0, 339, 468 },
	{"CordiaUPC", 0, 1, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 9, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 2053, 1, 289, 833, -261, 0, 339, 468 },
	{"Cordia New", 0, 1, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 9, 2, 4 }, 2164260867, 0, 0, 0, 65537, 0, 700, 5, 2053, 1, 289, 833, -261, 0, 339, 468 },
	{"Courier New", 0, 0, 0, 4, { 2, 7, 3, 9, 2, 2, 5, 2, 4, 4 }, 3758107391, 3221256259, 9, 0, 1073742335, 4294901760, 400, 5, 1285, 1, 600, 612, -188, 0, 422, 571 },
	{"Courier New", 0, 1, 0, 4, { 2, 7, 6, 9, 2, 2, 5, 2, 4, 4 }, 3758107391, 3221256259, 9, 0, 1073742335, 4294901760, 700, 5, 1285, 1, 600, 633, -208, 0, 443, 591 },
	{"Courier New", 0, 1, 1, 4, { 2, 7, 6, 9, 2, 2, 5, 9, 4, 4 }, 3758099199, 1073772611, 1, 0, 1073742271, 3757506560, 700, 5, 1285, 1, 600, 633, -208, 0, 443, 591 },
	{"Courier New", 0, 0, 1, 4, { 2, 7, 4, 9, 2, 2, 5, 9, 4, 4 }, 3758099199, 1073772611, 1, 0, 1073742271, 3757506560, 400, 5, 1285, 1, 600, 612, -188, 0, 422, 571 },
	{"Cuprum", 0, 1, 0, 0, { 2, 0, 8, 6, 0, 0, 0, 2, 0, 4 }, 2147484207, 10, 0, 0, 149, 0, 700, 5, 0, 1, 463, 895, -260, 0, 500, 710 },
	{"Cuprum", 0, 1, 1, 0, { 2, 0, 8, 6, 0, 0, 0, 9, 0, 4 }, 2147484207, 10, 0, 0, 149, 0, 700, 5, 0, 1, 464, 895, -260, 0, 500, 710 },
	{"Cuprum", 0, 0, 1, 0, { 2, 0, 5, 6, 0, 0, 0, 9, 0, 4 }, 2147484207, 10, 0, 0, 149, 0, 400, 5, 0, 1, 439, 895, -260, 0, 500, 710 },
	{"Cuprum", 0, 0, 0, 0, { 2, 0, 5, 6, 0, 0, 0, 2, 0, 4 }, 2147484207, 10, 0, 0, 149, 0, 400, 5, 0, 1, 437, 895, -260, 0, 500, 710 },
	{"Curlz MT", 0, 0, 0, 0, { 4, 4, 4, 4, 5, 7, 2, 2, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1807, 1, 380, 714, -190, 164, 0, 0 },
	{"Dancing Script", 0, 1, 0, 0, { 3, 8, 8, 0, 4, 5, 7, 0, 13, 0 }, 2147483695, 1073741835, 0, 0, 1, 0, 700, 5, 2562, 1, 494, 920, -280, 0, 332, 720 },
	{"Dancing Script", 0, 0, 0, 0, { 3, 8, 6, 0, 4, 5, 7, 0, 13, 0 }, 2147483695, 1073741835, 0, 0, 1, 0, 400, 5, 2562, 1, 467, 920, -280, 0, 332, 720 },
	{"DaunPenh", 0, 0, 0, 0, { 1, 1, 1, 1, 1, 1, 1, 1, 1, 1 }, 3, 0, 65536, 0, 1, 0, 400, 5, 0, 1, 388, 682, -659, 30, 278, 468 },
	{"David", 0, 0, 0, 0, { 2, 14, 5, 2, 6, 4, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 400, 5, 0, 1, 396, 734, -265, 0, 0, 0 },
	{"David", 0, 1, 0, 0, { 2, 14, 8, 2, 6, 4, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 700, 5, 0, 1, 421, 734, -265, 0, 0, 0 },
	{"David Transparent", 0, 0, 0, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2048, 0, 0, 0, 32, 0, 400, 5, 0, 1, 407, 923, -266, 0, 0, 0 },
	{"DejaVu Sans", 0, 0, 0, 0, { 2, 11, 6, 3, 3, 8, 4, 2, 2, 4 }, 3875548927, 3523280383, 168042537, 0, 3758096895, 3221159936, 400, 5, 0, 1, 506, 759, -240, 200, 0, 0 },
	{"DejaVu Sans", 0, 1, 0, 0, { 2, 11, 8, 3, 3, 6, 4, 2, 2, 4 }, 3875548927, 3523278335, 168042537, 0, 1610613247, 3221159936, 700, 5, 0, 1, 572, 759, -240, 200, 0, 0 },
	{"DejaVu Sans", 0, 1, 1, 0, { 2, 11, 8, 3, 3, 3, 4, 11, 2, 4 }, 3875540735, 1375794687, 168042529, 0, 1610613183, 2683764736, 700, 5, 0, 1, 572, 759, -240, 200, 0, 0 },
	{"DejaVu Sans Condensed", 0, 1, 0, 0, { 2, 11, 8, 6, 3, 6, 4, 2, 2, 4 }, 3875548927, 3523278335, 168058921, 0, 1610613247, 524288, 700, 4, 0, 1, 515, 759, -240, 200, 0, 0 },
	{"DejaVu Sans Condensed", 0, 1, 1, 0, { 2, 11, 8, 6, 3, 3, 4, 11, 2, 4 }, 3875540735, 1375794687, 168042529, 0, 1610613183, 0, 700, 4, 0, 1, 515, 759, -240, 200, 0, 0 },
	{"DejaVu Sans Condensed", 0, 0, 1, 0, { 2, 11, 6, 6, 3, 3, 4, 11, 2, 4 }, 3875540735, 1375796735, 168042529, 0, 1610613183, 3757506560, 400, 4, 0, 1, 456, 759, -240, 200, 0, 0 },
	{"DejaVu Sans Condensed", 0, 0, 0, 0, { 2, 11, 6, 6, 3, 8, 4, 2, 2, 4 }, 3875548927, 3523280383, 168058921, 0, 1610613247, 3758030848, 400, 4, 0, 1, 456, 759, -240, 200, 0, 0 },
	{"DejaVu Sans Light", 0, 0, 0, 0, { 2, 11, 2, 3, 3, 8, 4, 2, 2, 4 }, 3758106367, 1342177403, 134217760, 0, 1610613151, 2681667584, 200, 5, 0, 1, 506, 759, -240, 0, 0, 0 },
	{"DejaVu Sans Mono", 0, 1, 0, 4, { 2, 11, 7, 9, 3, 6, 4, 2, 2, 4 }, 3858768639, 3489722875, 40, 0, 1610613215, 524288, 700, 5, 0, 1, 602, 759, -240, 200, 0, 0 },
	{"DejaVu Sans Mono", 0, 1, 1, 4, { 2, 11, 7, 9, 3, 3, 4, 11, 2, 4 }, 3858760447, 1342206459, 32, 0, 1610613151, 0, 700, 5, 0, 1, 602, 759, -240, 200, 0, 0 },
	{"DejaVu Sans Mono", 0, 0, 1, 4, { 2, 11, 6, 9, 3, 3, 4, 11, 2, 4 }, 3858760447, 1342208507, 32, 0, 1610613151, 3755409408, 400, 5, 0, 1, 602, 759, -240, 200, 0, 0 },
	{"DejaVu Sans Mono", 0, 0, 0, 4, { 2, 11, 6, 9, 3, 8, 4, 2, 2, 4 }, 3858768639, 3523279355, 33554472, 0, 1610613215, 3755933696, 400, 5, 0, 1, 602, 759, -240, 200, 0, 0 },
	{"DejaVu Sans", 0, 0, 1, 0, { 2, 11, 6, 3, 3, 3, 4, 11, 2, 4 }, 3875540735, 1375796735, 168042529, 0, 1610613183, 2683764736, 400, 5, 0, 1, 506, 759, -240, 200, 0, 0 },
	{"DejaVu Serif", 0, 0, 0, 0, { 2, 6, 6, 3, 5, 6, 5, 2, 2, 4 }, 3825206015, 1342208507, 134479904, 0, 1610612895, 2681667584, 400, 5, 0, 1, 512, 759, -240, 200, 0, 0 },
	{"DejaVu Serif", 0, 1, 0, 0, { 2, 6, 8, 3, 5, 6, 5, 2, 2, 4 }, 3825206015, 1342206459, 134479904, 0, 1610612895, 2681667584, 700, 5, 0, 1, 565, 759, -240, 200, 0, 0 },
	{"DejaVu Serif", 0, 1, 1, 0, { 2, 6, 8, 3, 5, 3, 5, 11, 2, 4 }, 3825206015, 1342206459, 134479904, 0, 1610612895, 2681667584, 700, 5, 0, 1, 565, 759, -240, 200, 0, 0 },
	{"DejaVu Serif Condensed", 0, 1, 0, 0, { 2, 6, 8, 6, 5, 6, 5, 2, 2, 4 }, 3825206015, 1375793659, 168034336, 0, 1610612895, 0, 700, 4, 0, 1, 509, 759, -240, 200, 0, 0 },
	{"DejaVu Serif Condensed", 0, 1, 1, 0, { 2, 6, 8, 6, 5, 3, 5, 11, 2, 4 }, 3825206015, 1375793659, 168034336, 0, 1610612895, 0, 700, 4, 0, 1, 509, 759, -240, 200, 0, 0 },
	{"DejaVu Serif Condensed", 0, 0, 1, 0, { 2, 6, 6, 6, 5, 3, 5, 11, 2, 4 }, 3825206015, 1375795707, 168034336, 0, 1610612895, 3755409408, 400, 4, 0, 1, 460, 759, -240, 200, 0, 0 },
	{"DejaVu Serif Condensed", 0, 0, 0, 0, { 2, 6, 6, 6, 5, 6, 5, 2, 2, 4 }, 3825206015, 1375795707, 168034336, 0, 1610612895, 3755409408, 400, 4, 0, 1, 460, 759, -240, 200, 0, 0 },
	{"DejaVu Serif", 0, 0, 1, 0, { 2, 6, 6, 3, 5, 3, 5, 11, 2, 4 }, 3825206015, 1342208507, 134479904, 0, 1610612895, 2681667584, 400, 5, 0, 1, 512, 759, -240, 200, 0, 0 },
	{"Dingbats", 0, 0, 0, 0, { 2, 0, 5, 3, 0, 0, 0, 0, 0, 0 }, 2147483651, 0, 0, 0, 1, 0, 400, 5, 0, 1, 688, 819, -143, 90, 0, 0 },
	{"Diwani Bent", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 369, 1546, -539, 0, 0, 0 },
	{"Diwani Letter", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 360, 1524, -539, 0, 0, 0 },
	{"DokChampa", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 50331651, 0, 0, 0, 1073807361, 0, 400, 5, 2053, 1, 589, 976, -270, 97, 558, 728 },
	{"Guttman Drogolin", 0, 1, 0, 0, { 2, 1, 7, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 391, 746, -336, 0, 0, 0 },
	{"Guttman Drogolin", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 383, 746, -336, 0, 0, 0 },
	{"Droid Sans", 0, 1, 0, 0, { 2, 11, 8, 6, 3, 8, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 700, 5, 0, 1, 548, 765, -240, 64, 545, 713 },
	{"Droid Sans", 0, 0, 0, 0, { 2, 11, 6, 6, 3, 8, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 400, 5, 0, 1, 518, 765, -240, 64, 536, 713 },
	{"Droid Sans Mono", 0, 0, 0, 4, { 2, 11, 6, 9, 3, 8, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 400, 5, 0, 1, 600, 765, -240, 64, 536, 713 },
	{"Droid Serif", 0, 1, 0, 0, { 2, 2, 8, 0, 6, 5, 0, 2, 2, 0 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 700, 5, 512, 1, 579, 769, -240, 60, 536, 713 },
	{"Droid Serif", 0, 1, 1, 0, { 2, 2, 8, 0, 6, 5, 0, 9, 2, 0 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 700, 5, 512, 1, 580, 770, -240, 59, 536, 713 },
	{"Droid Serif", 0, 0, 1, 0, { 2, 2, 6, 0, 6, 5, 0, 9, 2, 0 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 400, 5, 512, 1, 547, 770, -240, 59, 536, 713 },
	{"Droid Serif", 0, 0, 0, 0, { 2, 2, 6, 0, 6, 5, 0, 2, 2, 0 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 400, 5, 512, 1, 552, 770, -240, 59, 536, 713 },
	{"DecoType Thuluth", 0, 0, 0, 0, { 2, 1, 0, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 428, 994, -500, 0, 0, 0 },
	{"DecoType Naskh", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 401, 994, -500, 0, 0, 0 },
	{"DecoType Naskh Special", 0, 0, 0, 0, { 2, 1, 0, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 404, 994, -500, 0, 0, 0 },
	{"DecoType Naskh Variants", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 417, 994, -500, 0, 0, 0 },
	{"DecoType Naskh Extensions", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 465, 994, -500, 0, 0, 0 },
	{"DecoType Naskh Swashes", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 400, 994, -500, 0, 0, 0 },
	{"Diwani Outline Shaded", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 408, 1590, -631, 0, 0, 0 },
	{"Diwani Simple Outline 2", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 376, 1590, -553, 0, 0, 0 },
	{"Diwani Simple Outline", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 376, 1564, -556, 0, 0, 0 },
	{"Diwani Simple Striped", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 369, 1581, -539, 0, 0, 0 },
	{"Ebrima", 0, 0, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2684375135, 33554497, 2048, 1028, 147, 0, 400, 5, 2053, 1, 604, 728, -210, 131, 500, 700 },
	{"Ebrima", 0, 1, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2684375135, 33554497, 2048, 1028, 147, 0, 700, 5, 2053, 1, 654, 740, -229, 57, 500, 700 },
	{"Elephant", 0, 0, 0, 0, { 2, 2, 9, 4, 9, 5, 5, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 488, 962, -265, 0, 0, 0 },
	{"Elephant", 0, 0, 1, 0, { 2, 2, 9, 7, 9, 9, 5, 9, 9, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 495, 967, -264, 0, 0, 0 },
	{"Engravers MT", 0, 0, 0, 0, { 2, 9, 7, 7, 8, 5, 5, 2, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 500, 7, 769, 1, 791, 695, -13, 360, 0, 0 },
	{"Eras Bold ITC", 0, 0, 0, 0, { 2, 11, 9, 7, 3, 5, 4, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2052, 1, 507, 696, -237, 0, 0, 0 },
	{"Eras Demi ITC", 0, 0, 0, 0, { 2, 11, 8, 5, 3, 5, 4, 2, 8, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2052, 1, 474, 685, -236, 0, 0, 0 },
	{"Eras Light ITC", 0, 0, 0, 0, { 2, 11, 4, 2, 3, 5, 4, 2, 8, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2052, 1, 415, 681, -236, 0, 0, 0 },
	{"Eras Medium ITC", 0, 0, 0, 0, { 2, 11, 6, 2, 3, 5, 4, 2, 8, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2053, 1, 443, 685, -236, 0, 0, 0 },
	{"Estrangelo Edessa", 0, 0, 0, 0, { 3, 8, 6, 0, 0, 0, 0, 0, 0, 0 }, 2147491907, 0, 128, 0, 1, 0, 400, 5, 0, 1, 501, 700, -299, 0, 400, 631 },
	{"Euphemia", 0, 0, 0, 0, { 2, 11, 5, 3, 4, 1, 2, 2, 1, 4 }, 2147483759, 74, 8192, 0, 1, 0, 400, 5, 2054, 1, 698, 765, -221, 81, 527, 765 },
	{"Felix Titling", 0, 0, 0, 0, { 4, 6, 5, 5, 6, 2, 2, 2, 10, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 258, 1, 588, 719, 0, 350, 0, 0 },
	{"FlemishScript BT", 0, 0, 0, 0, { 3, 3, 6, 2, 5, 5, 7, 15, 10, 5 }, 0, 0, 0, 0, 0, 0, 400, 5, 2563, 1, 270, 759, -240, 200, 0, 0 },
	{"Forte", 0, 0, 0, 0, { 3, 6, 9, 2, 4, 5, 2, 7, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2566, 1, 426, 672, -213, 183, 0, 0 },
	{"Franklin Gothic Book", 0, 0, 0, 0, { 2, 11, 5, 3, 2, 1, 2, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2054, 1, 421, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Book", 0, 0, 1, 0, { 2, 11, 5, 3, 2, 1, 2, 9, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2054, 1, 423, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Demi", 0, 0, 0, 0, { 2, 11, 7, 3, 2, 1, 2, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2054, 1, 438, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Demi Cond", 0, 0, 0, 0, { 2, 11, 7, 6, 3, 4, 2, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 3, 2054, 1, 375, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Demi", 0, 0, 1, 0, { 2, 11, 7, 3, 2, 1, 2, 9, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2054, 1, 437, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Heavy", 0, 0, 0, 0, { 2, 11, 9, 3, 2, 1, 2, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2054, 1, 473, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Heavy", 0, 0, 1, 0, { 2, 11, 9, 3, 2, 1, 2, 9, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2054, 1, 469, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Medium", 0, 0, 0, 0, { 2, 11, 6, 3, 2, 1, 2, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2054, 1, 428, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Medium Cond", 0, 0, 0, 0, { 2, 11, 6, 6, 3, 4, 2, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 3, 2054, 1, 360, 700, -189, 179, 0, 0 },
	{"Franklin Gothic Medium", 0, 0, 1, 0, { 2, 11, 6, 3, 2, 1, 2, 9, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2054, 1, 427, 700, -189, 179, 0, 0 },
	{"FrankRuehl", 0, 0, 0, 0, { 2, 14, 5, 3, 6, 1, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 400, 5, 0, 1, 394, 734, -265, 0, 0, 0 },
	{"Guttman Frank", 0, 1, 0, 0, { 2, 1, 7, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 378, 746, -336, 0, 0, 0 },
	{"Freestyle Script", 0, 0, 0, 0, { 3, 8, 4, 2, 3, 2, 5, 11, 4, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2564, 1, 258, 682, -317, 0, 0, 0 },
	{"Guttman Frnew", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 365, 746, -336, 0, 0, 0 },
	{"French Script MT", 0, 0, 0, 0, { 3, 2, 4, 2, 4, 6, 7, 4, 6, 5 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2563, 1, 279, 574, -299, 195, 0, 0 },
	{"Farsi Simple Bold", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 448, 1336, -891, 0, 0, 0 },
	{"Farsi Simple Outline", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 472, 1344, -898, 0, 0, 0 },
	{"Footlight MT Light", 0, 0, 0, 0, { 2, 4, 6, 2, 6, 3, 10, 2, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 300, 5, 259, 1, 411, 691, -223, -154, 0, 0 },
	{"FZShuTi", 0, 0, 0, 0, { 2, 1, 6, 1, 3, 1, 1, 1, 1, 1 }, 3, 135135232, 0, 0, 262144, 0, 400, 4, 0, 1, 500, 910, -144, 144, 0, 0 },
	{"FZYaoTi", 0, 0, 0, 0, { 2, 1, 6, 1, 3, 1, 1, 1, 1, 1 }, 3, 135135232, 0, 0, 262144, 0, 400, 4, 0, 1, 500, 988, -148, 148, 0, 0 },
	{"Gabriola", 0, 0, 0, 0, { 4, 4, 6, 5, 5, 16, 2, 2, 13, 2 }, 3758097135, 1342185547, 0, 0, 536871071, 0, 400, 5, 0, 1, 492, 683, -316, 699, 343, 558 },
	{"Gadugi", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 12288, 0, 1, 0, 400, 5, 2053, 1, 564, 728, -210, 131, 500, 700 },
	{"Gadugi", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 12288, 0, 1, 0, 700, 5, 2053, 1, 686, 728, -210, 131, 500, 700 },
	{"Guttman Aharoni", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 399, 746, 336, 0, 0, 0 },
	{"Garamond", 0, 0, 0, 0, { 2, 2, 4, 4, 3, 3, 1, 1, 8, 3 }, 647, 0, 0, 0, 159, 3755409408, 400, 5, 258, 1, 387, 653, -263, 152, 0, 0 },
	{"Garamond", 0, 1, 0, 0, { 2, 2, 8, 4, 3, 3, 7, 1, 8, 3 }, 647, 0, 0, 0, 159, 3755409408, 700, 5, 258, 1, 422, 653, -263, 152, 0, 0 },
	{"Garamond", 0, 0, 1, 0, { 2, 2, 4, 4, 3, 3, 1, 1, 8, 3 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 258, 1, 324, 653, -263, 152, 0, 0 },
	{"Gautami", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 2097155, 0, 0, 0, 1, 0, 400, 5, 0, 1, 569, 923, -812, 169, 478, 660 },
	{"Gautami", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 2097155, 0, 0, 0, 1, 0, 700, 5, 0, 1, 573, 923, -812, 169, 478, 660 },
	{"Gentium Basic", 0, 1, 0, 0, { 2, 0, 5, 3, 6, 0, 0, 2, 0, 4 }, 2684354687, 1073750090, 0, 0, 536870931, 0, 700, 5, 0, 1, 529, 874, -283, 0, 454, 615 },
	{"Gentium Basic", 0, 1, 1, 0, { 2, 0, 6, 6, 8, 0, 0, 2, 0, 4 }, 2684354687, 1073750090, 0, 0, 536870931, 0, 700, 5, 0, 1, 485, 874, -283, 0, 454, 615 },
	{"Gentium Basic", 0, 0, 1, 0, { 2, 0, 6, 6, 8, 0, 0, 2, 0, 4 }, 2684354687, 1073750090, 0, 0, 536870931, 0, 400, 5, 0, 1, 456, 874, -283, 0, 454, 615 },
	{"Gentium Basic", 0, 0, 0, 0, { 2, 0, 5, 3, 6, 0, 0, 2, 0, 4 }, 2684354687, 1073750090, 0, 0, 536870931, 0, 400, 5, 0, 1, 500, 874, -283, 0, 454, 615 },
	{"Gentium Book Basic", 0, 1, 0, 0, { 2, 0, 5, 3, 6, 0, 0, 2, 0, 4 }, 2684354687, 1073750090, 0, 0, 536870931, 0, 700, 5, 0, 1, 538, 874, -283, 0, 454, 615 },
	{"Gentium Book Basic", 0, 1, 1, 0, { 2, 0, 6, 6, 8, 0, 0, 2, 0, 4 }, 2684354687, 1073750090, 0, 0, 536870931, 0, 700, 5, 0, 1, 495, 874, -283, 0, 454, 615 },
	{"Gentium Book Basic", 0, 0, 1, 0, { 2, 0, 6, 6, 8, 0, 0, 2, 0, 4 }, 2684354687, 1073750090, 0, 0, 536870931, 0, 400, 5, 0, 1, 466, 874, -283, 0, 454, 615 },
	{"Gentium Book Basic", 0, 0, 0, 0, { 2, 0, 5, 3, 6, 0, 0, 2, 0, 4 }, 2684354687, 1073750090, 0, 0, 536870931, 0, 400, 5, 0, 1, 510, 874, -283, 0, 454, 615 },
	{"Georgia", 0, 0, 0, 0, { 2, 4, 5, 2, 5, 4, 5, 2, 3, 3 }, 647, 0, 0, 0, 536871071, 0, 400, 5, 1027, 1, 439, 756, -216, 96, 481, 692 },
	{"Georgia", 0, 1, 0, 0, { 2, 4, 8, 2, 5, 4, 5, 2, 2, 3 }, 647, 0, 0, 0, 536871071, 0, 700, 5, 1027, 1, 513, 756, -216, 96, 484, 692 },
	{"Georgia", 0, 0, 1, 0, { 2, 4, 5, 2, 5, 4, 5, 9, 3, 3 }, 647, 0, 0, 0, 536871071, 0, 400, 5, 1027, 1, 449, 756, -216, 96, 488, 692 },
	{"Georgia", 0, 1, 1, 0, { 2, 4, 8, 2, 5, 4, 5, 9, 2, 3 }, 647, 0, 0, 0, 536871071, 0, 700, 5, 1027, 1, 523, 756, -216, 96, 495, 692 },
	{"Guttman Frank", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 379, 746, -336, 0, 0, 0 },
	{"Guttman Haim", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 406, 746, -336, 0, 0, 0 },
	{"Guttman Haim-Condensed", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 335, 746, -336, 0, 0, 0 },
	{"Gigi", 0, 0, 0, 0, { 4, 4, 5, 4, 6, 16, 7, 2, 13, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2575, 1, 394, 795, -388, 0, 0, 0 },
	{"Gill Sans MT", 0, 1, 1, 0, { 2, 11, 8, 2, 2, 1, 4, 9, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 700, 5, 2050, 1, 439, 690, -229, 148, 0, 0 },
	{"Gill Sans MT", 0, 1, 0, 0, { 2, 11, 8, 2, 2, 1, 4, 2, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 700, 5, 2050, 1, 466, 690, -229, 148, 0, 0 },
	{"Gill Sans MT Condensed", 0, 0, 0, 0, { 2, 11, 5, 6, 2, 1, 4, 2, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 400, 3, 2050, 1, 304, 691, -245, 132, 0, 0 },
	{"Gill Sans MT", 0, 0, 1, 0, { 2, 11, 5, 2, 2, 1, 4, 9, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 400, 5, 2050, 1, 375, 690, -229, 148, 0, 0 },
	{"Gill Sans Ultra Bold Condensed", 0, 0, 0, 0, { 2, 11, 10, 6, 2, 1, 4, 2, 2, 3 }, 3, 0, 0, 0, 3, 0, 400, 3, 2050, 1, 445, 753, -164, 151, 0, 0 },
	{"Gill Sans Ultra Bold", 0, 0, 0, 0, { 2, 11, 10, 2, 2, 1, 4, 2, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 400, 5, 2050, 1, 629, 756, -165, 147, 0, 0 },
	{"Gill Sans MT", 0, 0, 0, 0, { 2, 11, 5, 2, 2, 1, 4, 2, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 400, 5, 2050, 1, 407, 690, -229, 148, 0, 0 },
	{"Gisha", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 2147485703, 1073741890, 0, 0, 33, 0, 400, 5, 2053, 1, 510, 750, -235, 83, 500, 700 },
	{"Gisha", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 2147485703, 1073741890, 0, 0, 33, 0, 700, 5, 2053, 1, 550, 751, -234, 83, 500, 700 },
	{"Guttman Kav", 0, 1, 0, 0, { 2, 1, 7, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 397, 746, -336, 0, 0, 0 },
	{"Guttman Kav-Light", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 372, 746, -336, 0, 0, 0 },
	{"Guttman Kav", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 399, 746, -336, 0, 0, 0 },
	{"Gloucester MT Extra Condensed", 0, 0, 0, 0, { 2, 3, 8, 8, 2, 6, 1, 1, 1, 1 }, 3, 0, 0, 0, 536870913, 0, 400, 2, 1029, 1, 276, 769, -160, 140, 0, 0 },
	{"Gill Sans MT Ext Condensed Bold", 0, 0, 0, 0, { 2, 11, 9, 2, 2, 1, 4, 2, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 400, 2, 2050, 1, 229, 802, -116, 150, 0, 0 },
	{"Guttman Myamfix", 0, 0, 0, 4, { 2, 1, 4, 9, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 429, 996, -378, 0, 0, 0 },
	{"GOST type A", 0, 0, 0, 0, { 2, 11, 5, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 0, 0, 400, 5, 0, 1, 348, 687, -218, 0, 0, 0 },
	{"GOST type B", 0, 0, 0, 0, { 2, 11, 5, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 0, 0, 400, 5, 0, 1, 434, 690, -205, 0, 0, 0 },
	{"Century Gothic", 0, 0, 0, 0, { 2, 11, 5, 2, 2, 2, 2, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 516, 1, 485, 750, -208, 111, 0, 0 },
	{"Century Gothic", 0, 1, 0, 0, { 2, 11, 7, 2, 2, 2, 2, 2, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 700, 5, 516, 1, 485, 750, -208, 111, 0, 0 },
	{"Century Gothic", 0, 1, 1, 0, { 2, 11, 6, 2, 2, 2, 2, 9, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 700, 5, 516, 1, 485, 750, -208, 111, 0, 0 },
	{"Century Gothic", 0, 0, 1, 0, { 2, 11, 5, 2, 2, 2, 2, 9, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 516, 1, 485, 750, -208, 111, 0, 0 },
	{"Goudy Old Style", 0, 0, 0, 0, { 2, 2, 5, 2, 5, 3, 5, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 6, 259, 1, 393, 894, -237, 68, 0, 0 },
	{"Goudy Old Style", 0, 1, 0, 0, { 2, 2, 6, 2, 6, 3, 5, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 6, 259, 1, 404, 894, -237, 68, 0, 0 },
	{"Goudy Old Style", 0, 0, 1, 0, { 2, 2, 5, 2, 5, 3, 5, 9, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 259, 1, 350, 894, -237, 68, 0, 0 },
	{"Goudy Stout", 0, 0, 0, 0, { 2, 2, 9, 4, 7, 3, 11, 2, 4, 1 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 1109, 745, -18, 0, 0, 0 },
	{"Gulim", 0, 0, 0, 0, { 2, 11, 6, 0, 0, 1, 1, 1, 1, 1 }, 2952790703, 1775729915, 48, 0, 1074266271, 3755409408, 400, 5, 2053, 1, 500, 858, -141, 148, 0, 0 },
	{"GulimChe", 1, 0, 0, 4, { 2, 11, 6, 9, 0, 1, 1, 1, 1, 1 }, 2952790703, 1775729915, 48, 0, 1074266271, 3755409408, 400, 5, 2053, 1, 500, 858, -141, 148, 0, 0 },
	{"Dotum", 2, 0, 0, 0, { 2, 11, 6, 0, 0, 1, 1, 1, 1, 1 }, 2952790703, 1775729915, 48, 0, 1074266271, 3755409408, 400, 5, 2053, 1, 500, 858, -141, 148, 0, 0 },
	{"DotumChe", 3, 0, 0, 4, { 2, 11, 6, 9, 0, 1, 1, 1, 1, 1 }, 2952790703, 1775729915, 48, 0, 1074266271, 3755409408, 400, 5, 2053, 1, 500, 858, -141, 148, 0, 0 },
	{"Guttman Yad-Brush", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 438, 746, -335, 0, 0, 0 },
	{"Guttman Yad", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 403, 746, -335, 0, 0, 0 },
	{"Guttman Yad-Light", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 405, 746, -336, 0, 0, 0 },
	{"HYGraphic-Medium", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 30899449, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYGungSo-Bold", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 30899449, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYGothic-Extra", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 701988089, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYGothic-Medium", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 701988089, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYHeadLine-Medium", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 30899449, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYMyeongJo-Extra", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 701988089, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYSinMyeongJo-Medium", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 701988089, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYPMokGak-Bold", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 30899449, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYPost-Light", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 30899449, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYPost-Medium", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 30899449, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"HYShortSamul-Medium", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2415919783, 30899449, 16, 0, 524288, 0, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"Harlow Solid Italic", 0, 0, 1, 0, { 4, 3, 6, 4, 2, 15, 2, 2, 13, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 6, 0, 1, 381, 874, -388, 0, 0, 0 },
	{"Harrington", 0, 0, 0, 0, { 4, 4, 5, 5, 5, 10, 2, 2, 7, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1807, 1, 434, 945, -230, 0, 0, 0 },
	{"Haettenschweiler", 0, 0, 0, 0, { 2, 11, 7, 6, 4, 9, 2, 6, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2053, 1, 311, 700, 120, 66, 0, 0 },
	{"Monotype Hadassah", 0, 0, 0, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 0, 1, 502, 856, -212, 0, 0, 0 },
	{"Monotype Hadassah", 0, 1, 0, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 700, 5, 0, 1, 516, 856, -212, 0, 0, 0 },
	{"HGGothicE", 0, 0, 0, 4, { 2, 11, 9, 9, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 500, 859, -140, 0, 562, 781 },
	{"HGPGothicE", 1, 0, 0, 0, { 2, 11, 9, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 500, 859, -140, 0, 562, 781 },
	{"HGSGothicE", 2, 0, 0, 0, { 2, 11, 9, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 500, 859, -140, 0, 562, 781 },
	{"HGGothicM", 0, 0, 0, 0, { 2, 11, 6, 9, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 2049, 1, 500, 859, -140, 0, 0, 0 },
	{"HGPGothicM", 1, 0, 0, 0, { 2, 11, 6, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 2049, 1, 500, 859, -140, 0, 0, 0 },
	{"HGSGothicM", 2, 0, 0, 0, { 2, 11, 6, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 2049, 1, 500, 859, -140, 0, 0, 0 },
	{"HGGyoshotai", 0, 0, 0, 0, { 3, 0, 6, 9, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 2566, 1, 500, 859, -140, 0, 0, 0 },
	{"HGPGyoshotai", 1, 0, 0, 0, { 3, 0, 6, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 2566, 1, 500, 859, -140, 0, 0, 0 },
	{"HGSGyoshotai", 2, 0, 0, 0, { 3, 0, 6, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 2566, 1, 500, 859, -140, 0, 0, 0 },
	{"HGKyokashotai", 0, 0, 0, 0, { 2, 2, 6, 9, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 264, 1, 500, 859, -140, 0, 0, 0 },
	{"HGPKyokashotai", 1, 0, 0, 0, { 2, 2, 6, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 264, 1, 500, 859, -140, 0, 0, 0 },
	{"HGSKyokashotai", 2, 0, 0, 0, { 2, 2, 6, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 264, 1, 500, 859, -140, 0, 0, 0 },
	{"HGMinchoB", 0, 0, 0, 0, { 2, 2, 8, 9, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 261, 1, 500, 859, -140, 0, 0, 0 },
	{"HGPMinchoB", 1, 0, 0, 0, { 2, 2, 8, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 261, 1, 500, 859, -140, 0, 0, 0 },
	{"HGSMinchoB", 2, 0, 0, 0, { 2, 2, 8, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 261, 1, 500, 859, -140, 0, 0, 0 },
	{"HGMinchoE", 0, 0, 0, 4, { 2, 2, 9, 9, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 261, 1, 500, 859, -140, 0, 496, 734 },
	{"HGPMinchoE", 1, 0, 0, 0, { 2, 2, 9, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 261, 1, 500, 859, -140, 0, 492, 726 },
	{"HGSMinchoE", 2, 0, 0, 0, { 2, 2, 9, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 261, 1, 500, 859, -140, 0, 492, 726 },
	{"HGSoeiKakupoptai", 0, 0, 0, 4, { 4, 11, 10, 9, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2307, 1, 500, 859, -140, 0, 550, 800 },
	{"HGPSoeiKakupoptai", 1, 0, 0, 0, { 4, 11, 10, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2307, 1, 500, 859, -140, 0, 589, 808 },
	{"HGSSoeiKakupoptai", 2, 0, 0, 0, { 4, 11, 10, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2307, 1, 500, 859, -140, 0, 589, 808 },
	{"HGSoeiPresenceEB", 0, 0, 0, 0, { 2, 2, 8, 9, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 261, 1, 500, 859, -140, 0, 0, 0 },
	{"HGPSoeiPresenceEB", 1, 0, 0, 0, { 2, 2, 8, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 261, 1, 500, 859, -140, 0, 0, 0 },
	{"HGSSoeiPresenceEB", 2, 0, 0, 0, { 2, 2, 8, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 261, 1, 500, 859, -140, 0, 0, 0 },
	{"HGSoeiKakugothicUB", 0, 0, 0, 4, { 2, 11, 9, 9, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 500, 859, -140, 0, 570, 769 },
	{"HGPSoeiKakugothicUB", 1, 0, 0, 0, { 2, 11, 9, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 500, 859, -140, 0, 570, 773 },
	{"HGSSoeiKakugothicUB", 2, 0, 0, 0, { 2, 11, 9, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 500, 859, -140, 0, 570, 773 },
	{"HGSeikaishotaiPRO", 0, 0, 0, 0, { 3, 0, 6, 0, 0, 0, 0, 0, 0, 0 }, 2147484289, 684158200, 16, 0, 131072, 0, 400, 5, 2567, 1, 500, 859, -140, 0, 0, 0 },
	{"HGMaruGothicMPRO", 0, 0, 0, 0, { 2, 15, 6, 0, 0, 0, 0, 0, 0, 0 }, 3758097151, 1791491579, 18, 0, 1073873055, 3755409408, 400, 5, 2057, 1, 500, 859, -140, 0, 527, 757 },
	{"Microsoft Himalaya", 0, 0, 0, 0, { 1, 1, 1, 0, 1, 1, 1, 1, 1, 1 }, 2147483651, 65536, 64, 0, 1, 0, 400, 5, 2560, 1, 410, 591, -408, 84, 299, 443 },
	{"MoeumT R", 0, 0, 0, 0, { 2, 3, 5, 4, 0, 1, 1, 1, 1, 1 }, 2147484327, 701988091, 16, 0, 524288, 0, 400, 5, 0, 1, 500, 859, -140, 148, 0, 0 },
	{"Expo M", 1, 0, 0, 0, { 2, 3, 5, 4, 0, 1, 1, 1, 1, 1 }, 2147484327, 701988091, 16, 0, 524288, 0, 400, 5, 0, 1, 500, 859, -140, 148, 0, 0 },
	{"Yet R", 0, 0, 0, 0, { 2, 3, 5, 4, 0, 1, 1, 1, 1, 1 }, 2147484327, 701988091, 16, 0, 524288, 0, 400, 5, 0, 1, 500, 859, -140, 148, 0, 0 },
	{"Pyunji R", 0, 0, 0, 0, { 2, 3, 5, 4, 0, 1, 1, 1, 1, 1 }, 2147484327, 701988091, 16, 0, 524288, 0, 400, 5, 0, 1, 500, 859, -140, 148, 0, 0 },
	{"Ami R", 0, 0, 0, 0, { 2, 3, 5, 4, 0, 1, 1, 1, 1, 1 }, 2147484327, 30899451, 16, 0, 524288, 0, 400, 5, 0, 1, 500, 859, -140, 148, 0, 0 },
	{"Magic R", 0, 0, 0, 0, { 2, 3, 5, 4, 0, 1, 1, 1, 1, 1 }, 2147484327, 30899451, 16, 0, 524288, 0, 400, 5, 0, 1, 500, 859, -140, 148, 0, 0 },
	{"Headline R", 0, 0, 0, 0, { 2, 3, 5, 4, 0, 1, 1, 1, 1, 1 }, 2147484327, 30899451, 16, 0, 524288, 0, 400, 5, 0, 1, 500, 859, -140, 148, 0, 0 },
	{"High Tower Text", 0, 0, 0, 0, { 2, 4, 5, 2, 5, 5, 6, 3, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 259, 1, 418, 740, -259, 84, 0, 0 },
	{"High Tower Text", 0, 0, 1, 0, { 2, 4, 5, 2, 5, 5, 6, 10, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 259, 1, 375, 799, -200, 84, 0, 0 },
	{"Impact", 0, 0, 0, 0, { 2, 11, 8, 6, 3, 9, 2, 5, 2, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 3, 2053, 1, 596, 790, -111, 167, 647, 790 },
	{"Imprint MT Shadow", 0, 0, 0, 0, { 4, 2, 6, 5, 6, 3, 3, 3, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1027, 1, 420, 701, -240, 128, 0, 0 },
	{"Informal Roman", 0, 0, 0, 0, { 3, 6, 4, 2, 3, 4, 6, 11, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1807, 1, 361, 750, -250, 0, 0, 0 },
	{"Iskoola Pota", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 512, 0, 536870913, 0, 400, 5, 261, 1, 687, 693, -244, 197, 447, 662 },
	{"Iskoola Pota", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 512, 0, 536870913, 0, 700, 5, 261, 1, 701, 693, -244, 197, 448, 662 },
	{"Blackadder ITC", 0, 0, 0, 0, { 4, 2, 5, 5, 5, 16, 7, 2, 13, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2566, 1, 312, 896, -386, 0, 0, 0 },
	{"Edwardian Script ITC", 0, 0, 0, 0, { 3, 3, 3, 2, 4, 7, 7, 13, 8, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 6, 2563, 1, 254, 848, -328, 0, 0, 0 },
	{"Kristen ITC", 0, 0, 0, 0, { 3, 5, 5, 2, 4, 2, 2, 3, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 6, 2566, 1, 490, 1027, -332, 0, 0, 0 },
	{"Italic Outline Art", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 658, 1517, -588, 0, 0, 0 },
	{"Jokerman", 0, 0, 0, 0, { 4, 9, 6, 5, 6, 13, 6, 2, 7, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2307, 1, 488, 867, -318, 0, 0, 0 },
	{"Juice ITC", 0, 0, 0, 0, { 4, 4, 4, 3, 4, 10, 2, 2, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2568, 1, 291, 759, -240, 200, 0, 0 },
	{"DFKai-SB", 0, 0, 0, 4, { 3, 0, 5, 9, 0, 0, 0, 0, 0, 0 }, 3, 137232384, 22, 0, 1048577, 0, 400, 5, 2567, 1, 500, 800, -199, 199, 0, 0 },
	{"Kalinga", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 524291, 0, 0, 0, 1, 0, 400, 5, 0, 1, 584, 996, -488, 626, 514, 709 },
	{"Kalinga", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 524291, 0, 0, 0, 1, 0, 700, 5, 0, 1, 590, 996, -488, 626, 512, 709 },
	{"Kartika", 0, 0, 0, 0, { 2, 2, 5, 3, 3, 4, 4, 6, 2, 3 }, 8388611, 0, 0, 0, 1, 0, 400, 5, 0, 1, 833, 982, -443, 0, 537, 740 },
	{"Kartika", 0, 1, 0, 0, { 2, 2, 8, 3, 3, 4, 4, 6, 2, 3 }, 8388611, 0, 0, 0, 1, 0, 700, 5, 0, 1, 840, 982, -443, 0, 537, 740 },
	{"Kufi Extended Outline", 0, 0, 0, 0, { 4, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 597, 1366, -554, 0, 0, 0 },
	{"Kufi Outline Shaded", 0, 0, 0, 0, { 4, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 513, 1272, -539, 0, 0, 0 },
	{"Khmer UI", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 2147483695, 8266, 65536, 0, 1, 0, 400, 5, 2053, 1, 661, 728, -210, 131, 500, 700 },
	{"Khmer UI", 0, 1, 0, 0, { 2, 11, 7, 2, 4, 2, 4, 2, 2, 3 }, 2147483695, 8266, 65536, 0, 1, 0, 700, 5, 2053, 1, 704, 728, -210, 131, 500, 700 },
	{"Kokila", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 400, 5, 0, 1, 416, 536, -166, 366, 346, 512 },
	{"Kokila", 0, 1, 0, 0, { 2, 11, 8, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 700, 5, 0, 1, 448, 524, -166, 378, 353, 512 },
	{"Kokila", 0, 1, 1, 0, { 2, 11, 8, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 700, 5, 0, 1, 444, 524, -166, 378, 350, 512 },
	{"Kokila", 0, 0, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 400, 5, 0, 1, 415, 537, -166, 365, 341, 512 },
	{"Monotype Koufi", 0, 0, 1, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 43253760, 66322438, 39976960, 109576199, 2147483648, 0, 700, 5, 0, 1, 424, 0, 0, 1000, 0, 0 },
	{"Kunstler Script", 0, 0, 0, 0, { 3, 3, 4, 2, 2, 6, 7, 13, 13, 6 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2563, 1, 245, 534, -240, 273, 0, 0 },
	{"Lao UI", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 33554435, 0, 0, 0, 1, 0, 400, 5, 2053, 1, 554, 728, -210, 131, 500, 700 },
	{"Lao UI", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 33554435, 0, 0, 0, 1, 0, 700, 5, 2053, 1, 587, 728, -210, 131, 500, 700 },
	{"Latha", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 1048579, 0, 0, 0, 1, 0, 400, 5, 0, 1, 723, 1000, -660, 0, 466, 644 },
	{"Latha", 0, 1, 0, 0, { 2, 11, 7, 4, 2, 2, 2, 2, 2, 4 }, 1048579, 0, 0, 0, 1, 0, 700, 5, 0, 1, 752, 1000, -660, 0, 466, 644 },
	{"Wide Latin", 0, 0, 0, 0, { 2, 10, 10, 7, 5, 5, 5, 2, 4, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 9, 0, 1, 826, 700, -222, 147, 0, 0 },
	{"Lucida Bright", 0, 0, 0, 0, { 2, 4, 6, 2, 5, 5, 5, 2, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 512, 1, 491, 770, -205, 23, 0, 0 },
	{"Lucida Bright", 0, 1, 0, 0, { 2, 4, 7, 2, 6, 5, 5, 2, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 600, 5, 512, 1, 511, 770, -205, 23, 0, 0 },
	{"Lucida Bright", 0, 1, 1, 0, { 2, 4, 7, 2, 5, 5, 5, 9, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 600, 5, 512, 1, 507, 770, -205, 23, 0, 0 },
	{"Lucida Bright", 0, 0, 1, 0, { 2, 4, 6, 2, 5, 5, 5, 9, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 512, 1, 482, 770, -205, 23, 0, 0 },
	{"Lucida Calligraphy", 0, 0, 0, 0, { 3, 1, 1, 1, 1, 1, 1, 1, 1, 1 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2565, 1, 537, 855, -325, -180, 0, 0 },
	{"Led Italic Font", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 711, 1517, 493, 0, 0, 0 },
	{"Leelawadee", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 16777217, 0, 0, 0, 536936449, 0, 400, 5, 2053, 1, 534, 957, -238, 83, 500, 700 },
	{"Leelawadee", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 16777217, 0, 0, 0, 536936449, 0, 700, 5, 2053, 1, 581, 957, -238, 83, 500, 700 },
	{"Lucida Fax", 0, 0, 0, 0, { 2, 6, 6, 2, 5, 5, 5, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1282, 1, 514, 770, 205, 23, 0, 0 },
	{"Lucida Fax", 0, 1, 0, 0, { 2, 6, 7, 2, 5, 5, 5, 3, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 600, 5, 1282, 1, 539, 770, 205, 23, 0, 0 },
	{"Lucida Fax", 0, 1, 1, 0, { 2, 6, 7, 2, 4, 3, 5, 9, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 600, 5, 1282, 1, 530, 770, 205, 23, 0, 0 },
	{"Lucida Fax", 0, 0, 1, 0, { 2, 6, 6, 2, 5, 3, 5, 10, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1282, 1, 505, 770, 205, 23, 0, 0 },
	{"Lucida Handwriting", 0, 0, 0, 0, { 3, 1, 1, 1, 1, 1, 1, 1, 1, 1 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2564, 1, 570, 855, 325, -180, 0, 0 },
	{"Lobster", 0, 0, 0, 0, { 2, 0, 5, 6, 0, 0, 0, 2, 0, 3 }, 2147483695, 1073741898, 0, 0, 5, 0, 400, 5, 2562, 1, 385, 1000, -250, 0, 500, 749 },
	{"Lobster Two", 0, 1, 0, 0, { 2, 0, 5, 6, 0, 0, 0, 2, 0, 3 }, 2147483695, 1073741898, 0, 0, 1, 0, 700, 5, 2562, 1, 546, 1000, -250, 0, 500, 752 },
	{"Lobster Two", 0, 1, 1, 0, { 2, 0, 5, 6, 0, 0, 0, 2, 0, 3 }, 2147483695, 1073741898, 0, 0, 1, 0, 700, 5, 2562, 1, 545, 1000, -250, 0, 500, 752 },
	{"Lobster Two", 0, 0, 1, 0, { 2, 0, 5, 6, 0, 0, 0, 2, 0, 3 }, 2147483695, 1073741898, 0, 0, 1, 0, 400, 5, 2562, 1, 520, 1000, -250, 0, 500, 754 },
	{"Lobster Two", 0, 0, 0, 0, { 2, 0, 5, 6, 0, 0, 0, 2, 0, 3 }, 2147483695, 1073741898, 0, 0, 1, 0, 400, 5, 2562, 1, 520, 1000, -250, 0, 500, 754 },
	{"Guttman Logo1", 0, 0, 0, 0, { 5, 1, 1, 1, 1, 1, 1, 1, 1, 1 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 0, 1, 732, 800, 200, 0, 0, 0 },
	{"Lucida Sans", 0, 0, 0, 0, { 2, 11, 6, 2, 3, 5, 4, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2050, 1, 489, 770, 205, 23, 0, 0 },
	{"Lucida Sans", 0, 1, 0, 0, { 2, 11, 7, 3, 4, 5, 4, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 600, 5, 2050, 1, 521, 770, 205, 23, 0, 0 },
	{"Lucida Sans", 0, 1, 1, 0, { 2, 11, 7, 3, 4, 5, 4, 10, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 600, 5, 2050, 1, 519, 770, 205, 23, 0, 0 },
	{"Lucida Sans", 0, 0, 1, 0, { 2, 11, 6, 2, 3, 5, 4, 9, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2050, 1, 489, 770, 205, 23, 0, 0 },
	{"Lucida Sans Typewriter", 0, 0, 0, 4, { 2, 11, 5, 9, 3, 5, 4, 3, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 4, 2057, 1, 602, 770, 205, 23, 0, 0 },
	{"Lucida Sans Typewriter", 0, 1, 0, 4, { 2, 11, 7, 9, 4, 5, 4, 3, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 600, 4, 2057, 1, 602, 770, 205, 23, 0, 0 },
	{"Lucida Sans Typewriter", 0, 1, 1, 4, { 2, 11, 7, 9, 4, 5, 4, 10, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 600, 4, 2057, 1, 602, 770, 205, 23, 0, 0 },
	{"Lucida Sans Typewriter", 0, 0, 1, 4, { 2, 11, 5, 9, 3, 5, 4, 3, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 4, 2057, 1, 602, 770, 205, 23, 0, 0 },
	{"Lucida Console", 0, 0, 0, 4, { 2, 11, 6, 9, 4, 5, 4, 2, 2, 4 }, 2147484303, 6144, 0, 0, 31, 3621191680, 400, 4, 2057, 1, 602, 783, -205, 81, 0, 0 },
	{"Levenim MT", 0, 0, 0, 0, { 2, 1, 5, 2, 6, 1, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 400, 5, 0, 1, 495, 944, -455, 0, 0, 0 },
	{"Levenim MT", 0, 1, 0, 0, { 2, 1, 8, 2, 6, 1, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 700, 5, 0, 1, 494, 944, -455, 0, 0, 0 },
	{"Lucida Sans Unicode", 0, 0, 0, 0, { 2, 11, 6, 2, 3, 5, 4, 2, 2, 4 }, 2147490559, 14699, 0, 0, 536871103, 3623288832, 400, 5, 0, 1, 489, 783, -204, 82, 0, 0 },
	{"Magneto", 0, 1, 0, 0, { 4, 3, 8, 5, 5, 8, 2, 2, 13, 2 }, 3, 0, 0, 0, 536870913, 0, 700, 6, 2564, 1, 548, 797, -202, 84, 0, 0 },
	{"Maiandra GD", 0, 0, 0, 0, { 2, 14, 5, 2, 3, 3, 8, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2568, 1, 437, 759, 240, 200, 0, 0 },
	{"Sakkal Majalla", 0, 0, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2684362879, 3221233739, 8, 0, 536871123, 0, 400, 5, 0, 1, 507, 681, -318, 347, 342, 502 },
	{"Sakkal Majalla", 0, 1, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2684362879, 3221233739, 8, 0, 536871123, 0, 700, 5, 0, 1, 523, 681, -318, 347, 341, 502 },
	{"Malgun Gothic", 0, 0, 0, 0, { 2, 11, 5, 3, 2, 0, 0, 2, 0, 4 }, 2415919791, 701988091, 18, 0, 524429, 0, 400, 5, 2053, 1, 463, 799, -200, 0, 512, 718 },
	{"Malgun Gothic", 0, 1, 0, 0, { 2, 11, 8, 3, 2, 0, 0, 2, 0, 4 }, 2415919791, 701988091, 18, 0, 524429, 0, 700, 5, 2053, 1, 488, 799, -200, 0, 512, 717 },
	{"Mangal", 0, 0, 0, 0, { 2, 4, 5, 3, 5, 2, 3, 3, 2, 2 }, 32771, 0, 0, 0, 1, 0, 400, 5, 0, 1, 581, 1241, -438, 0, 536, 741 },
	{"Mangal", 0, 1, 0, 0, { 2, 4, 5, 3, 5, 2, 3, 3, 2, 2 }, 32771, 0, 0, 0, 1, 0, 700, 5, 0, 1, 583, 1241, -438, 0, 536, 741 },
	{"Guttman Mantova", 0, 1, 0, 0, { 2, 1, 7, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 384, 746, -336, 0, 0, 0 },
	{"Guttman Mantova-Decor", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 403, 746, -336, 0, 0, 0 },
	{"Guttman Mantova", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 383, 746, -335, 0, 0, 0 },
	{"Marlett", 0, 0, 0, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 500, 5, 0, 1, 958, 1000, 0, 0, 0, 0 },
	{"Matura MT Script Capitals", 0, 0, 0, 0, { 3, 2, 8, 2, 6, 6, 2, 7, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2567, 1, 428, 626, -277, 165, 0, 0 },
	{"Meiryo", 0, 0, 0, 0, { 2, 11, 6, 4, 3, 5, 4, 4, 2, 4 }, 3758097151, 1791492095, 134217746, 0, 1610743967, 3755409408, 400, 5, 2048, 1, 956, 877, -122, 500, 551, 735 },
	{"Meiryo", 1, 0, 1, 0, { 2, 11, 6, 4, 3, 5, 4, 11, 2, 4 }, 3758097151, 1791492095, 134217746, 0, 1610743967, 3755409408, 400, 5, 2048, 1, 956, 877, -122, 500, 551, 735 },
	{"Meiryo UI", 2, 0, 0, 0, { 2, 11, 6, 4, 3, 5, 4, 4, 2, 4 }, 3758097151, 1791492095, 134217746, 0, 1610743967, 3755409408, 400, 5, 2048, 1, 536, 877, -122, 500, 551, 735 },
	{"Meiryo UI", 3, 0, 1, 0, { 2, 11, 6, 4, 3, 5, 4, 11, 2, 4 }, 3758097151, 1791492095, 134217746, 0, 1610743967, 3755409408, 400, 5, 2048, 1, 536, 877, -122, 500, 551, 735 },
	{"Meiryo", 0, 1, 0, 0, { 2, 11, 8, 4, 3, 5, 4, 4, 2, 4 }, 3758097151, 1791492095, 134217746, 0, 1610743967, 3755409408, 700, 5, 2048, 1, 960, 877, -122, 500, 565, 735 },
	{"Meiryo", 1, 1, 1, 0, { 2, 11, 8, 4, 3, 5, 4, 11, 2, 4 }, 3758097151, 1791492095, 134217746, 0, 1610743967, 3755409408, 700, 5, 2048, 1, 960, 877, -122, 500, 565, 735 },
	{"Meiryo UI", 2, 1, 0, 0, { 2, 11, 8, 4, 3, 5, 4, 4, 2, 4 }, 3758097151, 1791492095, 134217746, 0, 1610743967, 3755409408, 700, 5, 2048, 1, 536, 877, -122, 500, 565, 735 },
	{"Meiryo UI", 3, 1, 1, 0, { 2, 11, 8, 4, 3, 5, 4, 11, 2, 4 }, 3758097151, 1791492095, 134217746, 0, 1610743967, 3755409408, 700, 5, 2048, 1, 536, 877, -122, 500, 565, 735 },
	{"Microsoft Sans Serif", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 3774884607, 3221225474, 8, 0, 536936959, 539492352, 400, 5, 2053, 1, 439, 728, -210, 131, 518, 715 },
	{"MingLiU", 0, 0, 0, 4, { 2, 2, 5, 9, 0, 0, 0, 0, 0, 0 }, 2684355327, 684719354, 22, 0, 1048577, 0, 400, 5, 261, 1, 500, 800, -199, 199, 429, 659 },
	{"PMingLiU", 1, 0, 0, 0, { 2, 2, 5, 0, 0, 0, 0, 0, 0, 0 }, 2684355327, 684719354, 22, 0, 1048577, 0, 400, 5, 261, 1, 500, 800, -199, 199, 429, 659 },
	{"MingLiU_HKSCS", 2, 0, 0, 0, { 2, 2, 5, 0, 0, 0, 0, 0, 0, 0 }, 2684355327, 953154810, 22, 0, 1048577, 0, 400, 5, 261, 1, 500, 800, -199, 199, 429, 659 },
	{"MingLiU-ExtB", 0, 0, 0, 0, { 2, 2, 5, 0, 0, 0, 0, 0, 0, 0 }, 2147483695, 33554440, 0, 0, 1048577, 0, 400, 5, 261, 1, 500, 800, -199, 199, 429, 659 },
	{"PMingLiU-ExtB", 1, 0, 0, 0, { 2, 2, 5, 0, 0, 0, 0, 0, 0, 0 }, 2147483695, 33554440, 0, 0, 1048577, 0, 400, 5, 261, 1, 500, 800, -199, 199, 429, 659 },
	{"MingLiU_HKSCS-ExtB", 2, 0, 0, 0, { 2, 2, 5, 0, 0, 0, 0, 0, 0, 0 }, 2147483695, 33554440, 0, 0, 1048577, 0, 400, 5, 261, 1, 500, 800, -199, 199, 429, 659 },
	{"Guttman Miryam", 0, 1, 0, 0, { 2, 1, 7, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 370, 746, -336, 0, 0, 0 },
	{"Guttman-CourMir", 0, 0, 0, 4, { 2, 1, 4, 9, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 516, 800, -378, 0, 0, 0 },
	{"Guttman Miryam", 0, 0, 0, 0, { 2, 1, 3, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 300, 5, 0, 1, 369, 746, -336, 0, 0, 0 },
	{"Mistral", 0, 0, 0, 0, { 3, 9, 7, 2, 3, 4, 7, 2, 4, 3 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 0, 1, 322, 656, -246, 97, 0, 0 },
	{"Myanmar Text", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 1024, 0, 1, 0, 400, 5, 2053, 1, 549, 670, -329, 860, 500, 700 },
	{"Modern No. 20", 0, 0, 0, 0, { 2, 7, 7, 4, 7, 5, 5, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 513, 1, 400, 653, -209, 70, 0, 0 },
	{"Mongolian Baiti", 0, 0, 0, 0, { 3, 0, 5, 0, 0, 0, 0, 0, 0, 0 }, 2147483683, 0, 131072, 0, 1, 0, 400, 5, 2567, 1, 430, 844, -219, 89, 427, 722 },
	{"MoolBoran", 0, 0, 0, 0, { 2, 11, 1, 0, 1, 1, 1, 1, 1, 1 }, 2147483663, 8266, 65536, 0, 1, 0, 400, 5, 2053, 1, 400, 682, -659, 30, 278, 472 },
	{"Miriam", 0, 0, 0, 0, { 2, 11, 5, 2, 5, 1, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 400, 5, 0, 1, 401, 754, -265, 0, 0, 0 },
	{"Miriam Fixed", 0, 0, 0, 4, { 2, 11, 5, 9, 5, 1, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 400, 5, 0, 1, 600, 737, -265, 0, 0, 0 },
	{"Fixed Miriam Transparent", 0, 0, 0, 4, { 0, 0, 0, 9, 0, 0, 0, 0, 0, 0 }, 2048, 0, 0, 0, 32, 0, 400, 5, 0, 1, 600, 832, -300, 0, 0, 0 },
	{"Miriam Transparent", 0, 0, 0, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2048, 0, 0, 0, 32, 0, 400, 5, 0, 1, 413, 905, -211, 0, 0, 0 },
	{"MS Gothic", 0, 0, 0, 4, { 2, 11, 6, 9, 7, 2, 5, 8, 2, 4 }, 3758097151, 1791491579, 134217746, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 500, 859, -140, 0, 449, 679 },
	{"MS UI Gothic", 1, 0, 0, 0, { 2, 11, 6, 0, 7, 2, 5, 8, 2, 4 }, 3758097151, 1791491579, 134217746, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 417, 859, -140, 0, 449, 679 },
	{"MS PGothic", 2, 0, 0, 0, { 2, 11, 6, 0, 7, 2, 5, 8, 2, 4 }, 3758097151, 1791491579, 134217746, 0, 1073873055, 3755409408, 400, 5, 2049, 1, 417, 859, -140, 0, 449, 679 },
	{"Microsoft JhengHei", 0, 0, 0, 0, { 2, 11, 6, 4, 3, 5, 4, 4, 2, 4 }, 135, 682573824, 22, 0, 1048585, 0, 400, 5, 2050, 1, 468, 890, -109, 0, 540, 756 },
	{"Microsoft JhengHei UI", 1, 0, 0, 0, { 2, 11, 6, 4, 3, 5, 4, 4, 2, 4 }, 135, 682573824, 22, 0, 1048585, 0, 400, 5, 2050, 1, 468, 890, -109, 0, 540, 756 },
	{"Microsoft JhengHei", 0, 1, 0, 0, { 2, 11, 8, 3, 2, 5, 4, 4, 2, 4 }, 135, 682573824, 22, 0, 1048585, 0, 700, 5, 2050, 1, 481, 890, -109, 0, 540, 756 },
	{"Microsoft JhengHei UI", 1, 1, 0, 0, { 2, 11, 8, 3, 2, 5, 4, 4, 2, 4 }, 135, 682573824, 22, 0, 1048585, 0, 700, 5, 2050, 1, 481, 890, -109, 0, 540, 756 },
	{"MS Mincho", 0, 0, 0, 4, { 2, 2, 6, 9, 4, 2, 5, 8, 3, 4 }, 3758097151, 1791491579, 134217746, 0, 1073873055, 3755409408, 400, 5, 261, 1, 500, 859, -140, 0, 449, 679 },
	{"MS PMincho", 1, 0, 0, 0, { 2, 2, 6, 0, 4, 2, 5, 8, 3, 4 }, 3758097151, 1791491579, 134217746, 0, 1073873055, 3755409408, 400, 5, 261, 1, 410, 859, -140, 0, 449, 679 },
	{"Microsoft Uighur", 0, 1, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2147491875, 2147483650, 8, 0, 65, 0, 700, 5, 512, 1, 408, 683, -316, 81, 278, 472 },
	{"Microsoft Uighur", 0, 0, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2147491875, 2147483650, 8, 0, 65, 0, 400, 5, 0, 1, 395, 683, -316, 81, 279, 471 },
	{"Microsoft YaHei", 0, 0, 0, 0, { 2, 11, 5, 3, 2, 2, 4, 2, 2, 4 }, 2147484295, 684670034, 22, 0, 262175, 0, 400, 5, 2053, 1, 481, 812, -253, 4, 540, 756 },
	{"Microsoft YaHei UI", 1, 0, 0, 0, { 2, 11, 5, 3, 2, 2, 4, 2, 2, 4 }, 2147484295, 684670034, 22, 0, 262175, 0, 400, 5, 2053, 1, 481, 812, -253, 4, 540, 756 },
	{"Microsoft YaHei", 0, 1, 0, 0, { 2, 11, 7, 3, 2, 2, 4, 2, 2, 1 }, 2147484295, 684670034, 22, 0, 262175, 0, 700, 5, 2053, 1, 507, 812, -253, 4, 540, 756 },
	{"Microsoft YaHei UI", 1, 1, 0, 0, { 2, 11, 7, 3, 2, 2, 4, 2, 2, 1 }, 2147484295, 684670034, 22, 0, 262175, 0, 700, 5, 2053, 1, 507, 812, -253, 4, 540, 756 },
	{"Microsoft Yi Baiti", 0, 0, 0, 0, { 3, 0, 5, 0, 0, 0, 0, 0, 0, 0 }, 2147483651, 66562, 524290, 0, 1, 0, 400, 5, 2567, 1, 646, 859, -141, 50, 380, 539 },
	{"Monotype Corsiva", 0, 0, 1, 0, { 3, 1, 1, 1, 1, 2, 1, 1, 1, 1 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 2566, 1, 350, 688, -258, 122, 0, 0 },
	{"Monotype Sorts", 0, 0, 0, 0, { 5, 1, 6, 1, 1, 1, 1, 1, 1, 1 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 3075, 1, 747, 0, 0, 1069, 0, 0 },
	{"Mudir MT", 0, 0, 1, 0, { 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 600, 5, 0, 1, 576, 921, 485, 0, 0, 0 },
	{"MV Boli", 0, 0, 0, 0, { 2, 0, 5, 0, 3, 2, 0, 9, 0, 0 }, 3, 0, 256, 0, 1, 0, 400, 5, 2566, 1, 562, 714, -226, 128, 402, 714 },
	{"New Gulim", 0, 0, 0, 0, { 2, 3, 6, 0, 0, 1, 1, 1, 1, 1 }, 2952790703, 2144828667, 48, 0, 1074266271, 3755409408, 400, 5, 261, 1, 500, 858, -141, 148, 0, 0 },
	{"Niagara Engraved", 0, 0, 0, 0, { 4, 2, 5, 2, 7, 7, 3, 3, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 3, 512, 1, 237, 799, -200, 84, 0, 0 },
	{"Niagara Solid", 0, 0, 0, 0, { 4, 2, 5, 2, 7, 7, 2, 2, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 3, 512, 1, 237, 799, -200, 84, 0, 0 },
	{"Nirmala UI", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 2164228131, 74, 512, 262144, 1, 0, 400, 5, 2053, 1, 972, 728, -210, 131, 500, 700 },
	{"Nirmala UI", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 2164228131, 74, 512, 262144, 1, 0, 700, 5, 2053, 1, 1063, 728, -210, 131, 500, 700 },
	{"Narkisim", 0, 0, 0, 0, { 2, 14, 5, 2, 5, 1, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 400, 5, 0, 1, 384, 734, -265, 0, 0, 0 },
	{"Microsoft New Tai Lue", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 2147483648, 0, 1, 0, 400, 5, 2053, 1, 584, 750, -235, 83, 500, 700 },
	{"Microsoft New Tai Lue", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 2147483648, 0, 1, 0, 700, 5, 2053, 1, 620, 751, -234, 83, 500, 700 },
	{"Nyala", 0, 0, 0, 0, { 2, 0, 5, 4, 7, 3, 0, 2, 0, 3 }, 2684354671, 0, 2048, 0, 147, 0, 400, 5, 0, 1, 558, 750, -169, 125, 365, 576 },
	{"OCR A Extended", 0, 0, 0, 0, { 2, 1, 5, 9, 2, 1, 2, 1, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2063, 1, 604, 648, -175, 245, 0, 0 },
	{"OCRB", 0, 0, 0, 4, { 2, 11, 6, 9, 2, 2, 2, 2, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 8, 2048, 1, 602, 706, -164, 555, 0, 0 },
	{"Old Antic Bold", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 428, 1448, -539, 0, 0, 0 },
	{"Old Antic Decorative", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 422, 1517, -539, 0, 0, 0 },
	{"Old Antic Outline", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 464, 1546, -539, 0, 0, 0 },
	{"Old English Text MT", 0, 0, 0, 0, { 3, 4, 9, 2, 4, 5, 8, 3, 8, 6 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2306, 1, 389, 699, -138, 231, 0, 0 },
	{"Old Antic Outline Shaded", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 555, 1546, -539, 0, 0, 0 },
	{"Onyx", 0, 0, 0, 0, { 4, 5, 6, 2, 8, 7, 2, 2, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 239, 740, -181, 147, 0, 0 },
	{"Open Sans", 0, 1, 0, 0, { 2, 11, 8, 6, 3, 5, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 700, 5, 2050, 1, 632, 765, -240, 64, 545, 713 },
	{"Open Sans", 0, 1, 1, 0, { 2, 11, 8, 6, 3, 5, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 700, 5, 0, 1, 595, 765, -240, 64, 545, 713 },
	{"Open Sans Condensed", 0, 1, 0, 0, { 2, 11, 8, 6, 3, 5, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 700, 3, 2050, 1, 500, 765, -240, 64, 543, 713 },
	{"Open Sans Condensed Light", 0, 0, 0, 0, { 2, 11, 3, 6, 3, 5, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 300, 3, 256, 1, 415, 765, -240, 64, 529, 713 },
	{"Open Sans Condensed Light", 0, 0, 1, 0, { 2, 11, 3, 6, 3, 5, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 300, 3, 256, 1, 378, 765, -240, 64, 529, 713 },
	{"Open Sans", 0, 0, 1, 0, { 2, 11, 6, 6, 3, 5, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 400, 5, 0, 1, 552, 765, -240, 64, 535, 713 },
	{"Open Sans", 0, 0, 0, 0, { 2, 11, 6, 6, 3, 5, 4, 2, 2, 4 }, 3758097135, 1073750107, 40, 0, 536871327, 0, 400, 5, 2050, 1, 588, 765, -240, 64, 535, 713 },
	{"OpenSymbol", 0, 0, 0, 0, { 5, 1, 0, 0, 0, 0, 0, 0, 0, 0 }, 2147483823, 268561642, 0, 0, 1, 0, 400, 5, 0, 1, 734, 799, 200, 0, 0, 0 },
	{"Oswald", 0, 1, 0, 0, { 2, 0, 8, 3, 0, 0, 0, 0, 0, 0 }, 2684354799, 1073741899, 0, 0, 147, 0, 700, 5, 0, 1, 391, 1193, -288, 0, 0, 0 },
	{"Oswald", 0, 0, 0, 0, { 2, 0, 5, 3, 0, 0, 0, 0, 0, 0 }, 2684354671, 1073741899, 0, 0, 147, 0, 400, 5, 0, 1, 389, 1193, -288, 0, 0, 0 },
	{"MS Outlook", 0, 0, 0, 0, { 5, 1, 1, 0, 1, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 3072, 1, 954, 799, -200, 0, 0, 0 },
	{"Pacifico", 0, 0, 0, 0, { 2, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 2147483695, 1073741899, 0, 0, 1, 0, 400, 5, 0, 1, 569, 1302, -453, 0, 180, 562 },
	{"Palatino Linotype", 0, 0, 0, 0, { 2, 4, 5, 2, 5, 5, 5, 3, 3, 4 }, 3758097031, 1073741843, 0, 0, 536871327, 0, 400, 5, 260, 1, 445, 731, -284, 333, 518, 715 },
	{"Palatino Linotype", 0, 1, 0, 0, { 2, 4, 7, 2, 6, 3, 5, 10, 2, 4 }, 3758097031, 1073741843, 0, 0, 536871327, 0, 700, 5, 260, 1, 458, 731, -284, 333, 518, 715 },
	{"Palatino Linotype", 0, 1, 1, 0, { 2, 4, 7, 2, 6, 3, 5, 10, 2, 4 }, 3758097031, 1073741843, 0, 0, 536871327, 0, 700, 5, 260, 1, 446, 731, -284, 333, 518, 715 },
	{"Palatino Linotype", 0, 0, 1, 0, { 2, 4, 5, 2, 5, 3, 5, 10, 3, 4 }, 3758097031, 1073741843, 0, 0, 536871327, 0, 400, 5, 260, 1, 400, 731, -284, 333, 518, 715 },
	{"Palace Script MT", 0, 0, 1, 0, { 3, 3, 3, 2, 2, 6, 7, 12, 11, 5 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2563, 1, 224, 494, -244, 331, 0, 0 },
	{"Papyrus", 0, 0, 0, 0, { 3, 7, 5, 2, 6, 5, 2, 3, 2, 5 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2565, 1, 412, 815, -414, 0, 0, 0 },
	{"Parchment", 0, 0, 0, 0, { 3, 4, 6, 2, 4, 7, 8, 4, 8, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2306, 1, 173, 406, -140, 79, 0, 0 },
	{"Perpetua", 0, 1, 1, 0, { 2, 2, 8, 2, 6, 4, 1, 9, 3, 3 }, 3, 0, 0, 0, 1, 0, 700, 5, 262, 1, 388, 627, -303, 138, 0, 0 },
	{"Perpetua", 0, 1, 0, 0, { 2, 2, 8, 2, 6, 4, 1, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 262, 1, 426, 627, -303, 138, 0, 0 },
	{"Perpetua", 0, 0, 1, 0, { 2, 2, 5, 2, 6, 4, 1, 9, 3, 3 }, 3, 0, 0, 0, 1, 0, 400, 5, 262, 1, 318, 627, -303, 138, 0, 0 },
	{"Perpetua Titling MT", 0, 1, 0, 0, { 2, 2, 8, 2, 6, 5, 5, 2, 8, 4 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 513, 1, 649, 719, -146, 203, 0, 0 },
	{"Perpetua Titling MT", 0, 0, 0, 0, { 2, 2, 5, 2, 6, 5, 5, 2, 8, 4 }, 3, 0, 0, 0, 536870913, 0, 300, 5, 513, 1, 595, 719, -137, 213, 0, 0 },
	{"Perpetua", 0, 0, 0, 0, { 2, 2, 5, 2, 6, 4, 1, 2, 3, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 262, 1, 359, 627, -303, 138, 0, 0 },
	{"Microsoft PhagsPa", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 3, 2097152, 134217728, 0, 1, 0, 400, 5, 2053, 1, 760, 728, -210, 131, 500, 700 },
	{"Microsoft PhagsPa", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 3, 2097152, 134217728, 0, 1, 0, 700, 5, 2048, 1, 783, 728, -210, 131, 500, 700 },
	{"Plantagenet Cherokee", 0, 0, 0, 0, { 2, 2, 6, 2, 7, 1, 0, 0, 0, 0 }, 3, 0, 4096, 0, 1, 0, 400, 5, 512, 1, 441, 696, -282, 48, 451, 675 },
	{"Playbill", 0, 0, 0, 0, { 4, 5, 6, 3, 10, 6, 2, 2, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 1, 0, 1, 245, 666, -171, 362, 0, 0 },
	{"Poor Richard", 0, 0, 0, 0, { 2, 8, 5, 2, 5, 5, 5, 2, 7, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 260, 1, 367, 696, -200, 79, 0, 0 },
	{"Pristina", 0, 0, 0, 0, { 3, 6, 4, 2, 4, 4, 6, 8, 2, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2566, 1, 305, 813, -440, 0, 0, 0 },
	{"PT Bold Arch", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 553, 1302, -539, 0, 0, 0 },
	{"PT Bold Broken", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 538, 1302, -539, 0, 0, 0 },
	{"PT Bold Dusky", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 544, 1302, -539, 0, 0, 0 },
	{"PT Bold Heading", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 539, 1302, -539, 0, 0, 0 },
	{"PT Bold Mirror", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 538, 1346, -503, 0, 0, 0 },
	{"PT Bold Stars", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 550, 1302, -539, 0, 0, 0 },
	{"PT Separated Baloon", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 595, 1302, -539, 0, 0, 0 },
	{"PT Sans", 0, 1, 0, 0, { 2, 11, 7, 3, 2, 2, 3, 2, 2, 4 }, 2684355311, 1342185547, 0, 0, 536871063, 0, 700, 5, 2050, 1, 537, 1018, -276, 0, 500, 700 },
	{"PT Sans", 0, 1, 1, 0, { 2, 11, 7, 3, 2, 2, 3, 9, 2, 4 }, 2684355311, 1342185547, 0, 0, 536871063, 0, 700, 5, 2050, 1, 515, 1018, -276, 0, 500, 700 },
	{"PT Sans", 0, 0, 1, 0, { 2, 11, 5, 3, 2, 2, 3, 9, 2, 4 }, 2684355311, 1342185547, 0, 0, 536871063, 0, 400, 5, 2050, 1, 503, 1018, -276, 0, 500, 700 },
	{"PT Sans", 0, 0, 0, 0, { 2, 11, 5, 3, 2, 2, 3, 2, 2, 4 }, 2684355311, 1342185547, 0, 0, 536871063, 0, 400, 5, 2050, 1, 527, 1018, -276, 0, 500, 700 },
	{"Raavi", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 131075, 0, 0, 0, 1, 0, 400, 5, 0, 1, 408, 981, -660, 125, 466, 644 },
	{"Raavi", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 131075, 0, 0, 0, 1, 0, 700, 5, 0, 1, 583, 981, -660, 125, 466, 644 },
	{"Rage Italic", 0, 0, 0, 0, { 3, 7, 5, 2, 4, 5, 7, 7, 3, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2562, 1, 352, 643, -265, 0, 0, 0 },
	{"Guttman Rashi", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 357, 746, -336, 0, 0, 0 },
	{"Guttman Rashi", 0, 1, 0, 0, { 2, 1, 7, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 371, 746, -336, 0, 0, 0 },
	{"Ravie", 0, 0, 0, 0, { 4, 4, 8, 5, 5, 8, 9, 2, 6, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 6, 2560, 1, 689, 799, -200, 84, 0, 0 },
	{"MS Reference Sans Serif", 0, 0, 0, 0, { 2, 11, 6, 4, 3, 5, 4, 4, 2, 4 }, 647, 0, 0, 0, 536871327, 0, 400, 5, 2048, 1, 508, 764, -206, 98, 0, 0 },
	{"MS Reference Specialty", 0, 0, 0, 0, { 5, 0, 5, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 2053, 1, 715, 0, 0, 1069, 0, 0 },
	{"Rockwell Condensed", 0, 1, 0, 0, { 2, 6, 9, 2, 2, 1, 5, 2, 4, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 3, 1283, 1, 414, 748, -224, 97, 0, 0 },
	{"Rockwell Condensed", 0, 0, 0, 0, { 2, 6, 6, 3, 5, 4, 5, 2, 1, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 3, 1283, 1, 334, 748, -224, 97, 0, 0 },
	{"Rockwell", 0, 0, 0, 0, { 2, 6, 6, 3, 2, 2, 5, 2, 4, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1283, 1, 460, 692, -222, 154, 0, 0 },
	{"Rockwell", 0, 1, 0, 0, { 2, 6, 8, 3, 3, 5, 5, 2, 4, 3 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 1283, 1, 491, 690, -219, 159, 0, 0 },
	{"Rockwell", 0, 1, 1, 0, { 2, 6, 8, 3, 3, 5, 5, 9, 4, 3 }, 3, 0, 0, 0, 536870915, 0, 700, 5, 1283, 1, 474, 692, -222, 154, 0, 0 },
	{"Rockwell Extra Bold", 0, 0, 0, 0, { 2, 6, 9, 3, 4, 5, 5, 2, 4, 3 }, 3, 0, 0, 0, 536870913, 0, 800, 5, 1283, 1, 602, 692, -214, 162, 0, 0 },
	{"Rockwell", 0, 0, 1, 0, { 2, 6, 6, 3, 3, 5, 5, 9, 4, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 1283, 1, 445, 690, -222, 156, 0, 0 },
	{"Rod", 0, 0, 0, 4, { 2, 3, 5, 9, 5, 1, 1, 1, 1, 1 }, 2049, 0, 0, 0, 32, 2097152, 400, 5, 0, 1, 600, 734, -265, 0, 0, 0 },
	{"Rod Transparent", 0, 0, 0, 4, { 0, 0, 0, 9, 0, 0, 0, 0, 0, 0 }, 2048, 0, 0, 0, 32, 0, 400, 5, 0, 1, 600, 832, -300, 0, 0, 0 },
	{"Century Schoolbook", 0, 1, 0, 0, { 2, 4, 8, 4, 6, 5, 5, 2, 3, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 700, 5, 1026, 1, 523, 740, -195, 133, 0, 0 },
	{"Century Schoolbook", 0, 1, 1, 0, { 2, 4, 8, 4, 6, 5, 5, 9, 3, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 700, 5, 1026, 1, 514, 739, -192, 138, 0, 0 },
	{"Century Schoolbook", 0, 0, 1, 0, { 2, 4, 6, 4, 5, 5, 5, 9, 3, 4 }, 647, 0, 0, 0, 536871071, 3755409408, 400, 5, 1026, 1, 458, 739, -195, 134, 0, 0 },
	{"Script MT Bold", 0, 0, 0, 0, { 3, 4, 6, 2, 4, 6, 7, 8, 9, 4 }, 3, 0, 0, 0, 536870913, 0, 700, 5, 2562, 1, 393, 695, -250, 123, 0, 0 },
	{"Segoe Print", 0, 0, 0, 0, { 2, 0, 6, 0, 0, 0, 0, 0, 0, 0 }, 655, 0, 0, 0, 536871071, 1191247872, 400, 5, 2560, 1, 641, 855, -312, -98, 498, 679 },
	{"Segoe Print", 0, 1, 0, 0, { 2, 0, 8, 0, 0, 0, 0, 0, 0, 0 }, 655, 0, 0, 0, 536871071, 1191247872, 700, 5, 2560, 1, 640, 849, -311, -91, 506, 680 },
	{"Segoe Script", 0, 0, 0, 0, { 2, 11, 5, 4, 2, 0, 0, 0, 0, 3 }, 655, 0, 0, 0, 159, 0, 400, 5, 2560, 1, 680, 745, -241, 83, 512, 673 },
	{"Segoe Script", 0, 1, 0, 0, { 2, 11, 8, 4, 2, 0, 0, 0, 0, 3 }, 655, 0, 0, 0, 159, 0, 700, 5, 2560, 1, 678, 746, -241, 82, 520, 680 },
	{"Segoe UI", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 3825217279, 3221283967, 9, 0, 536871423, 0, 400, 5, 2053, 1, 538, 728, -210, 131, 500, 700 },
	{"Segoe UI", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 3825217279, 3221283967, 9, 0, 536871423, 0, 700, 5, 2053, 1, 589, 728, -210, 131, 500, 700 },
	{"Segoe UI", 0, 0, 1, 0, { 2, 11, 5, 2, 4, 2, 4, 9, 2, 3 }, 3825207039, 1073800315, 1, 0, 536871327, 0, 400, 5, 2050, 1, 543, 728, -210, 131, 500, 700 },
	{"Segoe UI Light", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 3825217279, 3221283967, 9, 0, 536871423, 0, 300, 5, 2053, 1, 527, 728, -210, 131, 500, 700 },
	{"Segoe UI Semilight", 0, 0, 0, 0, { 2, 11, 4, 2, 4, 2, 4, 2, 2, 3 }, 3825217279, 3221283967, 9, 0, 536871423, 0, 350, 5, 2053, 1, 534, 728, -210, 131, 500, 700 },
	{"Segoe UI", 0, 1, 1, 0, { 2, 11, 8, 2, 4, 2, 4, 9, 2, 3 }, 3825207039, 1073800315, 1, 0, 536871327, 0, 700, 5, 2053, 1, 588, 728, -210, 131, 500, 700 },
	{"Segoe UI Light", 0, 0, 1, 0, { 2, 11, 3, 2, 4, 5, 4, 9, 2, 3 }, 3825207039, 1073800315, 1, 0, 536871327, 0, 300, 5, 2053, 1, 526, 728, -210, 131, 500, 700 },
	{"Segoe UI Semibold", 0, 0, 0, 0, { 2, 11, 7, 2, 4, 2, 4, 2, 2, 3 }, 3825217279, 3221283967, 9, 0, 536871423, 0, 600, 5, 2053, 1, 564, 728, -210, 131, 500, 700 },
	{"Segoe UI Semibold", 0, 0, 1, 0, { 2, 11, 7, 2, 4, 2, 4, 9, 2, 3 }, 3825207039, 1073800315, 1, 0, 536871327, 0, 600, 5, 2053, 1, 575, 728, -210, 131, 500, 700 },
	{"Segoe UI Semilight", 0, 0, 1, 0, { 2, 11, 4, 2, 4, 2, 4, 9, 2, 3 }, 3825207039, 1073800315, 1, 0, 536871327, 0, 350, 5, 2053, 1, 537, 728, -210, 131, 500, 700 },
	{"Segoe UI Symbol", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 2147483747, 302055407, 2408448, 67108864, 1, 1073741824, 400, 5, 2053, 1, 705, 728, -210, 131, 500, 700 },
	{"Shonar Bangla", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 65539, 0, 0, 0, 1, 0, 400, 5, 0, 1, 504, 808, -171, 17, 346, 538 },
	{"Shonar Bangla", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 65539, 0, 0, 0, 1, 0, 700, 5, 0, 1, 551, 560, -178, 330, 377, 547 },
	{"Showcard Gothic", 0, 0, 0, 0, { 4, 2, 9, 4, 2, 1, 2, 2, 6, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 553, 797, -202, 84, 0, 0 },
	{"Shruti", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 262147, 0, 0, 0, 1, 0, 400, 5, 0, 1, 430, 1020, -660, 156, 478, 663 },
	{"Shruti", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 262147, 0, 0, 0, 1, 0, 700, 5, 0, 1, 620, 1020, -660, 156, 478, 663 },
	{"FangSong", 0, 0, 0, 0, { 2, 1, 6, 9, 6, 1, 1, 1, 1, 1 }, 2147484351, 953122042, 22, 0, 262145, 0, 400, 5, 0, 1, 500, 859, -140, 140, 441, 667 },
	{"SimHei", 0, 0, 0, 0, { 2, 1, 6, 9, 6, 1, 1, 1, 1, 1 }, 2147484351, 953122042, 22, 0, 262145, 0, 400, 5, 0, 1, 500, 859, -140, 140, 457, 687 },
	{"KaiTi", 0, 0, 0, 0, { 2, 1, 6, 9, 6, 1, 1, 1, 1, 1 }, 2147484351, 953122042, 22, 0, 262145, 0, 400, 5, 0, 1, 500, 859, -140, 140, 460, 687 },
	{"LiSu", 0, 0, 0, 4, { 2, 1, 5, 9, 6, 1, 1, 1, 1, 1 }, 1, 135135232, 0, 0, 262144, 0, 400, 5, 0, 1, 500, 859, -140, 140, 0, 0 },
	{"Simplified Arabic", 0, 1, 0, 0, { 2, 2, 8, 3, 5, 4, 5, 2, 3, 4 }, 8195, 0, 0, 0, 65, 537395200, 700, 5, 0, 1, 480, 1179, -482, 0, 529, 707 },
	{"Simplified Arabic Fixed", 0, 0, 0, 4, { 2, 7, 3, 9, 2, 2, 5, 2, 4, 4 }, 8195, 0, 0, 0, 65, 537395200, 400, 5, 0, 1, 599, 799, -291, 0, 0, 0 },
	{"Simplified Arabic", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 8195, 0, 0, 0, 65, 537395200, 400, 5, 0, 1, 408, 1179, -478, 0, 529, 707 },
	{"SimSun", 0, 0, 0, 0, { 2, 1, 6, 0, 3, 1, 1, 1, 1, 1 }, 3, 680460288, 6, 0, 262145, 0, 400, 5, 0, 1, 500, 859, -140, 140, 453, 683 },
	{"NSimSun", 1, 0, 0, 4, { 2, 1, 6, 9, 3, 1, 1, 1, 1, 1 }, 3, 680460288, 6, 0, 262145, 0, 400, 5, 0, 1, 500, 859, -140, 140, 453, 683 },
	{"SimSun-ExtB", 0, 0, 0, 4, { 2, 1, 6, 9, 6, 1, 1, 1, 1, 1 }, 1, 33554432, 0, 0, 262145, 0, 400, 5, 0, 1, 500, 859, -140, 140, 0, 0 },
	{"YouYuan", 0, 0, 0, 4, { 2, 1, 5, 9, 6, 1, 1, 1, 1, 1 }, 1, 135135232, 0, 0, 262144, 0, 400, 5, 0, 1, 500, 859, -140, 140, 0, 0 },
	{"Snap ITC", 0, 0, 0, 0, { 4, 4, 10, 7, 6, 10, 2, 2, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2568, 1, 582, 759, -240, 200, 0, 0 },
	{"Simple Bold Jut Out", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 590, 994, -500, 0, 0, 0 },
	{"PT Simple Bold Ruled", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 539, 1351, -539, 0, 0, 0 },
	{"Simple Indust Outline", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 562, 1321, -539, 0, 0, 0 },
	{"Simple Indust Shaded", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 670, 1321, -539, 0, 0, 0 },
	{"Simple Outline Pat", 0, 0, 0, 0, { 2, 1, 4, 0, 0, 0, 0, 0, 0, 0 }, 24576, 2147483648, 8, 0, 64, 0, 400, 5, 0, 1, 530, 1204, -539, 0, 0, 0 },
	{"Guttman Stam", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 423, 746, -336, 0, 0, 0 },
	{"Guttman Stam1", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 423, 746, -336, 0, 0, 0 },
	{"STCaiyun", 0, 0, 0, 0, { 2, 1, 8, 0, 4, 1, 1, 1, 1, 1 }, 1, 135200768, 0, 0, 262144, 0, 400, 5, 0, 1, 443, 800, -200, 144, 0, 0 },
	{"Stencil", 0, 0, 0, 0, { 4, 4, 9, 5, 13, 8, 2, 2, 4, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 0, 1, 554, 666, 0, 333, 0, 0 },
	{"STFangsong", 0, 0, 0, 0, { 2, 1, 6, 0, 4, 1, 1, 1, 1, 1 }, 647, 135200768, 0, 0, 262303, 3755409408, 400, 5, 0, 1, 387, 800, -200, 144, 0, 0 },
	{"STHupo", 0, 0, 0, 0, { 2, 1, 8, 0, 4, 1, 1, 1, 1, 1 }, 1, 135200768, 0, 0, 262144, 0, 400, 5, 0, 1, 443, 800, -200, 144, 0, 0 },
	{"STKaiti", 0, 0, 0, 0, { 2, 1, 6, 0, 4, 1, 1, 1, 1, 1 }, 647, 135200768, 0, 0, 262303, 3755409408, 400, 5, 0, 1, 387, 800, -200, 144, 0, 0 },
	{"STLiti", 0, 0, 0, 0, { 2, 1, 8, 0, 4, 1, 1, 1, 1, 1 }, 1, 135200768, 0, 0, 262144, 0, 400, 5, 0, 1, 354, 689, -259, 144, 0, 0 },
	{"STSong", 0, 0, 0, 0, { 2, 1, 6, 0, 4, 1, 1, 1, 1, 1 }, 647, 135200768, 0, 0, 262303, 3755409408, 400, 5, 0, 1, 387, 800, -200, 144, 0, 0 },
	{"STXihei", 0, 0, 0, 0, { 2, 1, 6, 0, 4, 1, 1, 1, 1, 1 }, 647, 135200768, 0, 0, 262303, 3755409408, 400, 5, 0, 1, 481, 800, -200, 144, 0, 0 },
	{"STXingkai", 0, 0, 0, 0, { 2, 1, 8, 0, 4, 1, 1, 1, 1, 1 }, 1, 135200768, 0, 0, 262144, 0, 400, 5, 0, 1, 313, 800, -200, 144, 0, 0 },
	{"STXinwei", 0, 0, 0, 0, { 2, 1, 8, 0, 4, 1, 1, 1, 1, 1 }, 1, 135200768, 0, 0, 262144, 0, 400, 5, 0, 1, 429, 800, -200, 144, 0, 0 },
	{"STZhongsong", 0, 0, 0, 0, { 2, 1, 6, 0, 4, 1, 1, 1, 1, 1 }, 647, 135200768, 0, 0, 262303, 3755409408, 400, 5, 0, 1, 492, 800, -200, 144, 0, 0 },
	{"Sylfaen", 0, 0, 0, 0, { 1, 10, 5, 2, 5, 3, 6, 3, 3, 3 }, 67110535, 0, 0, 0, 536871071, 0, 400, 5, 1282, 1, 419, 737, -281, 298, 434, 672 },
	{"Symbol", 0, 0, 0, 0, { 5, 5, 1, 2, 1, 7, 6, 2, 5, 7 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 3075, 1, 600, 693, -215, 149, 0, 0 },
	{"Tahoma", 0, 0, 0, 0, { 2, 11, 6, 4, 3, 5, 4, 4, 2, 4 }, 3774885631, 3221250139, 41, 0, 536936959, 539492352, 400, 5, 2048, 1, 444, 764, -206, 28, 545, 727 },
	{"Tahoma", 0, 1, 0, 0, { 2, 11, 8, 4, 3, 5, 4, 4, 2, 4 }, 3774885631, 3221250139, 41, 0, 536936959, 539492352, 700, 5, 2048, 1, 505, 764, -206, 28, 548, 727 },
	{"Microsoft Tai Le", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 1073741824, 0, 1, 0, 400, 5, 2053, 1, 586, 750, -235, 83, 500, 700 },
	{"Microsoft Tai Le", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 3, 0, 1073741824, 0, 1, 0, 700, 5, 2053, 1, 619, 751, -234, 83, 500, 700 },
	{"Tall Paul", 0, 0, 0, 0, { 0, 0, 4, 0, 0, 0, 0, 0, 0, 0 }, 3, 0, 0, 0, 1, 0, 400, 5, 0, 1, 283, 619, -356, 0, 0, 0 },
	{"Tw Cen MT", 0, 1, 1, 0, { 2, 11, 8, 2, 2, 1, 4, 9, 6, 3 }, 3, 0, 0, 0, 536870915, 0, 700, 5, 2052, 1, 381, 689, -188, 190, 0, 0 },
	{"Tw Cen MT", 0, 1, 0, 0, { 2, 11, 8, 2, 2, 1, 4, 2, 6, 3 }, 3, 0, 0, 0, 536870915, 0, 700, 5, 2052, 1, 419, 689, -188, 190, 0, 0 },
	{"Tw Cen MT Condensed", 0, 1, 0, 0, { 2, 11, 8, 6, 2, 1, 4, 2, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 700, 5, 2052, 1, 353, 652, -185, 231, 0, 0 },
	{"Tw Cen MT Condensed Extra Bold", 0, 0, 0, 0, { 2, 11, 8, 3, 2, 2, 2, 2, 2, 4 }, 3, 0, 0, 0, 536870915, 0, 400, 5, 2052, 1, 380, 687, -185, 196, 0, 0 },
	{"Tw Cen MT Condensed", 0, 0, 0, 0, { 2, 11, 6, 6, 2, 1, 4, 2, 2, 3 }, 3, 0, 0, 0, 536870915, 0, 400, 5, 2052, 1, 300, 652, -185, 231, 0, 0 },
	{"Tw Cen MT", 0, 0, 1, 0, { 2, 11, 6, 2, 2, 1, 4, 9, 6, 3 }, 3, 0, 0, 0, 536870915, 0, 400, 5, 2052, 1, 383, 689, -188, 190, 0, 0 },
	{"Tw Cen MT", 0, 0, 0, 0, { 2, 11, 6, 2, 2, 1, 4, 2, 6, 3 }, 3, 0, 0, 0, 536870915, 0, 400, 5, 2052, 1, 398, 689, -188, 190, 0, 0 },
	{"Tempus Sans ITC", 0, 0, 0, 0, { 4, 2, 4, 4, 3, 13, 7, 2, 2, 2 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2565, 1, 416, 884, -296, -111, 0, 0 },
	{"Times New Roman", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 3758107391, 3221256259, 9, 0, 1073742335, 4294901760, 400, 5, 261, 1, 400, 693, -215, 149, 447, 662 },
	{"Times New Roman", 0, 1, 0, 0, { 2, 2, 8, 3, 7, 5, 5, 2, 3, 4 }, 3758107391, 3221256259, 9, 0, 1073742335, 4294901760, 700, 5, 261, 1, 426, 677, -215, 149, 456, 662 },
	{"Times New Roman", 0, 1, 1, 0, { 2, 2, 7, 3, 6, 5, 5, 9, 3, 4 }, 3758099199, 1073772611, 1, 0, 1073742271, 3757506560, 700, 5, 261, 1, 412, 677, -215, 149, 438, 662 },
	{"Times New Roman", 0, 0, 1, 0, { 2, 2, 5, 3, 5, 4, 5, 9, 3, 4 }, 3758099199, 1073772611, 1, 0, 1073742271, 3757506560, 400, 5, 261, 1, 401, 694, -215, 149, 430, 662 },
	{"Traditional Arabic", 0, 1, 0, 0, { 2, 2, 8, 3, 7, 5, 5, 2, 3, 4 }, 8195, 2147483648, 8, 0, 65, 537395200, 700, 5, 0, 1, 481, 1022, -509, 0, 471, 700 },
	{"Traditional Arabic", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 8195, 2147483648, 8, 0, 65, 537395200, 400, 5, 0, 1, 459, 994, -500, 0, 458, 693 },
	{"Trebuchet MS", 0, 0, 0, 0, { 2, 11, 6, 3, 2, 2, 2, 2, 2, 4 }, 647, 3, 0, 0, 536871071, 0, 400, 5, 2050, 1, 453, 737, -205, 0, 522, 715 },
	{"Trebuchet MS", 0, 1, 0, 0, { 2, 11, 7, 3, 2, 2, 2, 2, 2, 4 }, 647, 3, 0, 0, 536871071, 0, 700, 5, 2050, 1, 473, 737, -205, 0, 522, 715 },
	{"Trebuchet MS", 0, 1, 1, 0, { 2, 11, 7, 3, 2, 2, 2, 9, 2, 4 }, 647, 3, 0, 0, 536871071, 0, 700, 5, 2050, 1, 481, 737, -205, 0, 522, 715 },
	{"Trebuchet MS", 0, 0, 1, 0, { 2, 11, 6, 3, 2, 2, 2, 9, 2, 4 }, 647, 3, 0, 0, 536871071, 0, 400, 5, 2050, 1, 458, 737, -205, 0, 522, 715 },
	{"Tunga", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 4194307, 0, 0, 0, 1, 0, 400, 5, 0, 1, 548, 805, -662, 109, 402, 558 },
	{"Tunga", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 4194307, 0, 0, 0, 1, 0, 700, 5, 2053, 1, 555, 805, -662, 109, 402, 558 },
	{"Guttman Hatzvi", 0, 1, 0, 0, { 2, 1, 7, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 413, 746, -336, 0, 0, 0 },
	{"Guttman Hatzvi", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 428, 746, -336, 0, 0, 0 },
	{"Ubuntu", 0, 1, 0, 0, { 2, 11, 8, 4, 3, 6, 2, 3, 2, 4 }, 3758097151, 1342185563, 0, 0, 536871071, 1442906112, 700, 5, 0, 1, 643, 776, -185, 56, 526, 693 },
	{"Ubuntu", 0, 1, 1, 0, { 2, 11, 8, 4, 3, 6, 2, 10, 2, 4 }, 3758097151, 1342185563, 0, 0, 536871071, 1442906112, 700, 5, 0, 1, 634, 776, -185, 56, 526, 693 },
	{"Ubuntu", 0, 0, 1, 0, { 2, 11, 5, 4, 3, 6, 2, 10, 2, 4 }, 3758097151, 1342185563, 0, 0, 536871071, 1442906112, 400, 5, 0, 1, 584, 776, -185, 56, 520, 693 },
	{"Ubuntu", 0, 0, 0, 0, { 2, 11, 5, 4, 3, 6, 2, 3, 2, 4 }, 3758097151, 1342185563, 0, 0, 536871071, 1442906112, 400, 5, 0, 1, 602, 776, -185, 56, 520, 693 },
	{"Ubuntu Condensed", 0, 0, 0, 0, { 2, 11, 5, 6, 3, 6, 2, 3, 2, 4 }, 3758097151, 1342185563, 0, 0, 536871071, 1442906112, 400, 5, 0, 1, 484, 776, -185, 56, 520, 693 },
	{"DilleniaUPC", 0, 1, 0, 0, { 2, 2, 8, 3, 7, 5, 5, 2, 3, 4 }, 2164260903, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 290, 888, -252, 0, 319, 464 },
	{"DilleniaUPC", 0, 1, 1, 0, { 2, 2, 7, 3, 6, 5, 5, 9, 3, 4 }, 2164260903, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 290, 886, -252, 0, 319, 464 },
	{"DilleniaUPC", 0, 0, 1, 0, { 2, 2, 5, 3, 5, 4, 5, 9, 3, 4 }, 2164260903, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 284, 876, -252, 0, 319, 464 },
	{"DilleniaUPC", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 2164260903, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 284, 876, -252, 0, 319, 464 },
	{"EucrosiaUPC", 0, 1, 0, 0, { 2, 2, 8, 3, 7, 5, 5, 2, 3, 4 }, 2164260903, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 287, 831, -256, 0, 305, 450 },
	{"EucrosiaUPC", 0, 1, 1, 0, { 2, 2, 7, 3, 6, 5, 5, 9, 3, 4 }, 2164260903, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 287, 831, -256, 0, 305, 450 },
	{"EucrosiaUPC", 0, 0, 1, 0, { 2, 2, 5, 3, 5, 4, 5, 9, 3, 4 }, 2164260903, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 281, 839, -229, 0, 305, 450 },
	{"EucrosiaUPC", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 2164260903, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 281, 839, -232, 0, 305, 450 },
	{"FreesiaUPC", 0, 1, 0, 0, { 2, 11, 7, 4, 2, 2, 2, 2, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 289, 839, -256, 0, 0, 0 },
	{"FreesiaUPC", 0, 1, 1, 0, { 2, 11, 7, 4, 2, 2, 2, 9, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 289, 839, -256, 0, 0, 0 },
	{"FreesiaUPC", 0, 0, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 9, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 291, 816, -210, 0, 0, 0 },
	{"FreesiaUPC", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 291, 816, -210, 0, 0, 0 },
	{"IrisUPC", 0, 1, 0, 0, { 2, 11, 7, 4, 2, 2, 2, 2, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 291, 840, -204, 0, 0, 0 },
	{"IrisUPC", 0, 1, 1, 0, { 2, 11, 7, 4, 2, 2, 2, 9, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 291, 840, -204, 0, 0, 0 },
	{"IrisUPC", 0, 0, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 9, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 283, 815, -229, 0, 0, 0 },
	{"IrisUPC", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 283, 815, -229, 0, 0, 0 },
	{"JasmineUPC", 0, 1, 0, 0, { 2, 2, 8, 3, 7, 5, 5, 2, 3, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 306, 775, -178, 0, 0, 0 },
	{"JasmineUPC", 0, 1, 1, 0, { 2, 2, 7, 3, 6, 5, 5, 9, 3, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 306, 775, -178, 0, 0, 0 },
	{"JasmineUPC", 0, 0, 1, 0, { 2, 2, 5, 3, 5, 4, 5, 9, 3, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 272, 718, -169, 0, 0, 0 },
	{"JasmineUPC", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 272, 718, -169, 0, 0, 0 },
	{"KodchiangUPC", 0, 1, 0, 0, { 2, 2, 8, 3, 7, 5, 5, 2, 3, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 282, 687, -201, 0, 0, 0 },
	{"KodchiangUPC", 0, 1, 1, 0, { 2, 2, 7, 3, 6, 5, 5, 9, 3, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 282, 687, -201, 0, 0, 0 },
	{"KodchiangUPC", 0, 0, 1, 0, { 2, 2, 5, 3, 5, 4, 5, 9, 3, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 273, 690, -194, 0, 0, 0 },
	{"KodchiangUPC", 0, 0, 0, 0, { 2, 2, 6, 3, 5, 4, 5, 2, 3, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 273, 690, -194, 0, 0, 0 },
	{"LilyUPC", 0, 1, 0, 0, { 2, 11, 7, 4, 2, 2, 2, 2, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 322, 738, -178, 0, 0, 0 },
	{"LilyUPC", 0, 1, 1, 0, { 2, 11, 7, 4, 2, 2, 2, 9, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 700, 5, 0, 1, 322, 738, -178, 0, 0, 0 },
	{"LilyUPC", 0, 0, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 9, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 285, 678, -145, 0, 0, 0 },
	{"LilyUPC", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 16777223, 2, 0, 0, 65537, 0, 400, 5, 0, 1, 285, 678, -145, 0, 0, 0 },
	{"Urdu Typesetting", 0, 0, 0, 0, { 3, 2, 4, 2, 4, 4, 6, 3, 2, 3 }, 8195, 2147483648, 8, 0, 536871123, 0, 400, 5, 0, 1, 402, 713, -285, 70, 396, 616 },
	{"Utsaah", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 400, 5, 0, 1, 421, 521, -150, 397, 371, 512 },
	{"Utsaah", 0, 1, 0, 0, { 2, 11, 8, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 700, 5, 0, 1, 441, 521, -150, 397, 371, 512 },
	{"Utsaah", 0, 1, 1, 0, { 2, 11, 8, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 700, 5, 0, 1, 441, 521, -150, 397, 371, 512 },
	{"Utsaah", 0, 0, 1, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 32771, 0, 0, 0, 1, 0, 400, 5, 0, 1, 421, 521, -148, 399, 371, 512 },
	{"Vani", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 2097155, 0, 0, 0, 1, 0, 400, 5, 1027, 1, 720, 756, -216, 96, 481, 692 },
	{"Vani", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 2097155, 0, 0, 0, 1, 0, 700, 5, 1027, 1, 786, 756, -216, 96, 484, 692 },
	{"Verdana", 0, 0, 0, 0, { 2, 11, 6, 4, 3, 5, 4, 4, 2, 4 }, 2701133567, 1073750107, 16, 0, 536871327, 0, 400, 5, 2048, 1, 508, 764, -206, 98, 545, 727 },
	{"Verdana", 0, 1, 0, 0, { 2, 11, 8, 4, 3, 5, 4, 4, 2, 4 }, 2701133567, 1073750107, 16, 0, 536871327, 0, 700, 5, 2048, 1, 567, 764, -206, 98, 548, 727 },
	{"Verdana", 0, 0, 1, 0, { 2, 11, 6, 4, 3, 5, 4, 11, 2, 4 }, 2701133567, 1073750107, 16, 0, 536871327, 0, 400, 5, 2048, 1, 508, 764, -206, 98, 545, 727 },
	{"Verdana", 0, 1, 1, 0, { 2, 11, 8, 4, 3, 5, 4, 11, 2, 4 }, 2701133567, 1073750107, 16, 0, 536871327, 0, 700, 5, 2048, 1, 567, 764, -206, 98, 548, 727 },
	{"Vijaya", 0, 0, 0, 0, { 2, 11, 6, 4, 2, 2, 2, 2, 2, 4 }, 1048579, 0, 0, 0, 1, 0, 400, 5, 0, 1, 607, 559, -173, 336, 355, 533 },
	{"Vijaya", 0, 1, 0, 0, { 2, 11, 8, 4, 2, 2, 2, 2, 2, 4 }, 1048579, 0, 0, 0, 1, 0, 700, 5, 0, 1, 611, 545, -173, 350, 364, 533 },
	{"Guttman Vilna", 0, 0, 0, 0, { 2, 1, 4, 1, 1, 1, 1, 1, 1, 1 }, 6144, 1073741824, 0, 0, 32, 0, 400, 5, 0, 1, 389, 746, -336, 0, 0, 0 },
	{"Guttman Vilna", 0, 1, 0, 0, { 2, 1, 7, 0, 0, 0, 0, 0, 0, 0 }, 6144, 1073741824, 0, 0, 32, 0, 700, 5, 0, 1, 398, 746, -336, 0, 0, 0 },
	{"Viner Hand ITC", 0, 0, 0, 0, { 3, 7, 5, 2, 3, 5, 2, 2, 2, 3 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2565, 1, 456, 777, -615, -323, 0, 0 },
	{"Vivaldi", 0, 0, 1, 0, { 3, 2, 6, 2, 5, 5, 6, 9, 8, 4 }, 3, 0, 0, 0, 536870913, 0, 400, 3, 0, 1, 294, 903, -285, 30, 0, 0 },
	{"Vladimir Script", 0, 0, 0, 0, { 3, 5, 4, 2, 4, 4, 7, 7, 3, 5 }, 3, 0, 0, 0, 536870913, 0, 400, 5, 2563, 1, 325, 602, -298, 99, 0, 0 },
	{"Vrinda", 0, 0, 0, 0, { 2, 11, 5, 2, 4, 2, 4, 2, 2, 3 }, 65539, 0, 0, 0, 1, 0, 400, 5, 0, 1, 634, 984, -379, 41, 464, 642 },
	{"Vrinda", 0, 1, 0, 0, { 2, 11, 8, 2, 4, 2, 4, 2, 2, 3 }, 65539, 0, 0, 0, 1, 0, 700, 5, 0, 1, 686, 984, -379, 41, 464, 644 },
	{"Webdings", 0, 0, 0, 0, { 5, 3, 1, 2, 1, 5, 9, 6, 7, 3 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 3072, 1, 971, 799, -200, 0, 0, 0 },
	{"Wingdings", 0, 0, 0, 0, { 5, 0, 0, 0, 0, 0, 0, 0, 0, 0 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 3072, 1, 889, 770, 205, 23, 0, 0 },
	{"Wingdings 2", 0, 0, 0, 0, { 5, 2, 1, 2, 1, 5, 7, 7, 7, 7 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 3072, 1, 830, 770, 205, 23, 0, 0 },
	{"Wingdings 3", 0, 0, 0, 0, { 5, 4, 1, 2, 1, 8, 7, 7, 7, 7 }, 0, 0, 0, 0, 2147483648, 0, 400, 5, 3072, 1, 773, 770, 205, 23, 0, 0 },
	{"HanWangMingMedium", 0, 0, 0, 0, { 2, 2, 3, 0, 0, 0, 0, 0, 0, 0 }, 2147483875, 952727674, 22, 0, 1048576, 0, 400, 5, 0, 1, 996, 800, -199, 199, 0, 0 }
};

// error : 0

#endif /* _FONT_DICTIONARY_H */