<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<table align=center border=0 cellpadding=0 cellspacing=0>
<tr><td><a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_ATOM</a></td><td><a href="ft2-lcd_filtering.html#FT_LcdFilter">FT_LCD_FILTER_DEFAULT</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_New">FT_Stroker_New</a></td></tr>
<tr><td><a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_CARDINAL</a></td><td><a href="ft2-header_file_macros.html#FT_LCD_FILTER_H">FT_LCD_FILTER_H</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_ParseOutline">FT_Stroker_ParseOutline</a></td></tr>
<tr><td><a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_INTEGER</a></td><td><a href="ft2-lcd_filtering.html#FT_LcdFilter">FT_LCD_FILTER_LEGACY</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_Rewind">FT_Stroker_Rewind</a></td></tr>
<tr><td><a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_NONE</a></td><td><a href="ft2-lcd_filtering.html#FT_LcdFilter">FT_LCD_FILTER_LIGHT</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_Set">FT_Stroker_Set</a></td></tr>
<tr><td><a href="ft2-bdf_fonts.html#BDF_Property">BDF_Property</a></td><td><a href="ft2-lcd_filtering.html#FT_LcdFilter">FT_LCD_FILTER_NONE</a></td><td><a href="ft2-glyph_stroker.html#FT_StrokerBorder">FT_StrokerBorder</a></td></tr>
<tr><td><a href="ft2-bdf_fonts.html#BDF_PropertyRec">BDF_PropertyRec</a></td><td><a href="ft2-lcd_filtering.html#FT_LcdFilter">FT_LcdFilter</a></td><td><a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_2X2</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#CID_FaceDict">CID_FaceDict</a></td><td><a href="ft2-header_file_macros.html#FT_LIST_H">FT_LIST_H</a></td><td><a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_ARGS_ARE_WORDS</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#CID_FaceDictRec">CID_FaceDictRec</a></td><td><a href="ft2-base_interface.html#FT_Library">FT_Library</a></td><td><a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_ARGS_ARE_XY_VALUES</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#CID_FaceInfo">CID_FaceInfo</a></td><td><a href="ft2-lcd_filtering.html#FT_Library_SetLcdFilter">FT_Library_SetLcdFilter</a></td><td><a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_ROUND_XY_TO_GRID</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#CID_FaceInfoRec">CID_FaceInfoRec</a></td><td><a href="ft2-lcd_filtering.html#FT_Library_SetLcdFilterWeights">FT_Library_SetLcdFilterWeights</a></td><td><a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_SCALE</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#CID_Info">CID_Info</a></td><td><a href="ft2-version.html#FT_Library_Version">FT_Library_Version</a></td><td><a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_USE_MY_METRICS</a></td></tr>
<tr><td><a href="ft2-cff_driver.html#darkening-parameters">darkening-parameters</a></td><td><a href="ft2-list_processing.html#FT_List">FT_List</a></td><td><a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_XXX</a></td></tr>
<tr><td><a href="ft2-auto_hinter.html#default-script">default-script</a></td><td><a href="ft2-list_processing.html#FT_List_Add">FT_List_Add</a></td><td><a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_XY_SCALE</a></td></tr>
<tr><td><a href="ft2-version.html#FREETYPE_XXX">FREETYPE_MAJOR</a></td><td><a href="ft2-list_processing.html#FT_List_Destructor">FT_List_Destructor</a></td><td><a href="ft2-base_interface.html#FT_SubGlyph">FT_SubGlyph</a></td></tr>
<tr><td><a href="ft2-version.html#FREETYPE_XXX">FREETYPE_MINOR</a></td><td><a href="ft2-list_processing.html#FT_List_Finalize">FT_List_Finalize</a></td><td><a href="ft2-header_file_macros.html#FT_SYNTHESIS_H">FT_SYNTHESIS_H</a></td></tr>
<tr><td><a href="ft2-version.html#FREETYPE_XXX">FREETYPE_PATCH</a></td><td><a href="ft2-list_processing.html#FT_List_Find">FT_List_Find</a></td><td><a href="ft2-header_file_macros.html#FT_SYSTEM_H">FT_SYSTEM_H</a></td></tr>
<tr><td><a href="ft2-version.html#FREETYPE_XXX">FREETYPE_XXX</a></td><td><a href="ft2-list_processing.html#FT_List_Insert">FT_List_Insert</a></td><td><a href="ft2-basic_types.html#FT_Tag">FT_Tag</a></td></tr>
<tr><td><a href="ft2-sizes_management.html#FT_Activate_Size">FT_Activate_Size</a></td><td><a href="ft2-list_processing.html#FT_List_Iterate">FT_List_Iterate</a></td><td><a href="ft2-computations.html#FT_Tan">FT_Tan</a></td></tr>
<tr><td><a href="ft2-quick_advance.html#FT_ADVANCE_FLAG_FAST_ONLY">FT_ADVANCE_FLAG_FAST_ONLY</a></td><td><a href="ft2-list_processing.html#FT_List_Iterator">FT_List_Iterator</a></td><td><a href="ft2-header_file_macros.html#FT_TRIGONOMETRY_H">FT_TRIGONOMETRY_H</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_ADVANCES_H">FT_ADVANCES_H</a></td><td><a href="ft2-list_processing.html#FT_List_Remove">FT_List_Remove</a></td><td><a href="ft2-header_file_macros.html#FT_TRUETYPE_DRIVER_H">FT_TRUETYPE_DRIVER_H</a></td></tr>
<tr><td><a href="ft2-module_management.html#FT_Add_Default_Modules">FT_Add_Default_Modules</a></td><td><a href="ft2-list_processing.html#FT_List_Up">FT_List_Up</a></td><td><a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TRUETYPE_ENGINE_TYPE_NONE</a></td></tr>
<tr><td><a href="ft2-module_management.html#FT_Add_Module">FT_Add_Module</a></td><td><a href="ft2-list_processing.html#FT_ListNode">FT_ListNode</a></td><td><a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TRUETYPE_ENGINE_TYPE_PATENTED</a></td></tr>
<tr><td><a href="ft2-system_interface.html#FT_Alloc_Func">FT_Alloc_Func</a></td><td><a href="ft2-list_processing.html#FT_ListNodeRec">FT_ListNodeRec</a></td><td><a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TRUETYPE_ENGINE_TYPE_UNPATENTED</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_ANGLE_2PI">FT_ANGLE_2PI</a></td><td><a href="ft2-list_processing.html#FT_ListRec">FT_ListRec</a></td><td><a href="ft2-header_file_macros.html#FT_TRUETYPE_IDS_H">FT_TRUETYPE_IDS_H</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_ANGLE_PI">FT_ANGLE_PI</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_COLOR</a></td><td><a href="ft2-header_file_macros.html#FT_TRUETYPE_TABLES_H">FT_TRUETYPE_TABLES_H</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_ANGLE_PI2">FT_ANGLE_PI2</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_CROP_BITMAP</a></td><td><a href="ft2-header_file_macros.html#FT_TRUETYPE_TAGS_H">FT_TRUETYPE_TAGS_H</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_ANGLE_PI4">FT_ANGLE_PI4</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_DEFAULT</a></td><td><a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TrueTypeEngineType</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_Angle">FT_Angle</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_FORCE_AUTOHINT</a></td><td><a href="ft2-gx_validation.html#FT_TrueTypeGX_Free">FT_TrueTypeGX_Free</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_Angle_Diff">FT_Angle_Diff</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_IGNORE_GLOBAL_ADVANCE_WIDTH</a></td><td><a href="ft2-gx_validation.html#FT_TrueTypeGX_Validate">FT_TrueTypeGX_Validate</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_Atan2">FT_Atan2</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_IGNORE_TRANSFORM</a></td><td><a href="ft2-header_file_macros.html#FT_TYPE1_TABLES_H">FT_TYPE1_TABLES_H</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Attach_File">FT_Attach_File</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_LINEAR_DESIGN</a></td><td><a href="ft2-header_file_macros.html#FT_TYPES_H">FT_TYPES_H</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Attach_Stream">FT_Attach_Stream</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_MONOCHROME</a></td><td><a href="ft2-basic_types.html#FT_UFWord">FT_UFWord</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_AUTOHINTER_H">FT_AUTOHINTER_H</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_AUTOHINT</a></td><td><a href="ft2-basic_types.html#FT_UInt">FT_UInt</a></td></tr>
<tr><td><a href="ft2-auto_hinter.html#FT_AUTOHINTER_SCRIPT_XXX">FT_AUTOHINTER_SCRIPT_CJK</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_BITMAP</a></td><td><a href="ft2-basic_types.html#FT_UInt16">FT_UInt16</a></td></tr>
<tr><td><a href="ft2-auto_hinter.html#FT_AUTOHINTER_SCRIPT_XXX">FT_AUTOHINTER_SCRIPT_INDIC</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_HINTING</a></td><td><a href="ft2-basic_types.html#FT_UInt32">FT_UInt32</a></td></tr>
<tr><td><a href="ft2-auto_hinter.html#FT_AUTOHINTER_SCRIPT_XXX">FT_AUTOHINTER_SCRIPT_LATIN</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_RECURSE</a></td><td><a href="ft2-basic_types.html#FT_UInt64">FT_UInt64</a></td></tr>
<tr><td><a href="ft2-auto_hinter.html#FT_AUTOHINTER_SCRIPT_XXX">FT_AUTOHINTER_SCRIPT_NONE</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a></td><td><a href="ft2-basic_types.html#FT_ULong">FT_ULong</a></td></tr>
<tr><td><a href="ft2-auto_hinter.html#FT_AUTOHINTER_SCRIPT_XXX">FT_AUTOHINTER_SCRIPT_XXX</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_PEDANTIC</a></td><td><a href="ft2-header_file_macros.html#FT_UNPATENTED_HINTING_H">FT_UNPATENTED_HINTING_H</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_BBOX_H">FT_BBOX_H</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_RENDER</a></td><td><a href="ft2-basic_types.html#FT_UnitVector">FT_UnitVector</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_BBox">FT_BBox</a></td><td><a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_LCD</a></td><td><a href="ft2-basic_types.html#FT_UShort">FT_UShort</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_BDF_H">FT_BDF_H</a></td><td><a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_LCD_V</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_CKERNXXX">FT_VALIDATE_APPLE</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_BITMAP_H">FT_BITMAP_H</a></td><td><a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_LIGHT</a></td><td><a href="ft2-ot_validation.html#FT_VALIDATE_OTXXX">FT_VALIDATE_BASE</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Bitmap">FT_Bitmap</a></td><td><a href="ft2-base_interface.html#FT_LOAD_TARGET_MODE">FT_LOAD_TARGET_MODE</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_bsln</a></td></tr>
<tr><td><a href="ft2-bitmap_handling.html#FT_Bitmap_Convert">FT_Bitmap_Convert</a></td><td><a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_MONO</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_CKERNXXX">FT_VALIDATE_CKERN</a></td></tr>
<tr><td><a href="ft2-bitmap_handling.html#FT_Bitmap_Copy">FT_Bitmap_Copy</a></td><td><a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_NORMAL</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_CKERNXXX">FT_VALIDATE_CKERNXXX</a></td></tr>
<tr><td><a href="ft2-bitmap_handling.html#FT_Bitmap_Done">FT_Bitmap_Done</a></td><td><a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_XXX</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_feat</a></td></tr>
<tr><td><a href="ft2-bitmap_handling.html#FT_Bitmap_Embolden">FT_Bitmap_Embolden</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_VERTICAL_LAYOUT</a></td><td><a href="ft2-ot_validation.html#FT_VALIDATE_OTXXX">FT_VALIDATE_GDEF</a></td></tr>
<tr><td><a href="ft2-bitmap_handling.html#FT_Bitmap_New">FT_Bitmap_New</a></td><td><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_XXX</a></td><td><a href="ft2-ot_validation.html#FT_VALIDATE_OTXXX">FT_VALIDATE_GPOS</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Bitmap_Size">FT_Bitmap_Size</a></td><td><a href="ft2-base_interface.html#FT_Load_Char">FT_Load_Char</a></td><td><a href="ft2-ot_validation.html#FT_VALIDATE_OTXXX">FT_VALIDATE_GSUB</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_BitmapGlyph">FT_BitmapGlyph</a></td><td><a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_GX</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_BitmapGlyphRec">FT_BitmapGlyphRec</a></td><td><a href="ft2-truetype_tables.html#FT_Load_Sfnt_Table">FT_Load_Sfnt_Table</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GX_LENGTH">FT_VALIDATE_GX_LENGTH</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Bool">FT_Bool</a></td><td><a href="ft2-basic_types.html#FT_Long">FT_Long</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_GXXXX</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Byte">FT_Byte</a></td><td><a href="ft2-header_file_macros.html#FT_LZW_H">FT_LZW_H</a></td><td><a href="ft2-ot_validation.html#FT_VALIDATE_OTXXX">FT_VALIDATE_JSTF</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Bytes">FT_Bytes</a></td><td><a href="ft2-header_file_macros.html#FT_MAC_H">FT_MAC_H</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_just</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_BZIP2_H">FT_BZIP2_H</a></td><td><a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_kern</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CACHE_CHARMAP_H">FT_CACHE_CHARMAP_H</a></td><td><a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_lcar</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CACHE_H">FT_CACHE_H</a></td><td><a href="ft2-computations.html#FT_Matrix_Invert">FT_Matrix_Invert</a></td><td><a href="ft2-ot_validation.html#FT_VALIDATE_OTXXX">FT_VALIDATE_MATH</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CACHE_IMAGE_H">FT_CACHE_IMAGE_H</a></td><td><a href="ft2-computations.html#FT_Matrix_Multiply">FT_Matrix_Multiply</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_CKERNXXX">FT_VALIDATE_MS</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CACHE_SMALL_BITMAPS_H">FT_CACHE_SMALL_BITMAPS_H</a></td><td><a href="ft2-system_interface.html#FT_Memory">FT_Memory</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_mort</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_CeilFix">FT_CeilFix</a></td><td><a href="ft2-system_interface.html#FT_MemoryRec">FT_MemoryRec</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_morx</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CFF_DRIVER_H">FT_CFF_DRIVER_H</a></td><td><a href="ft2-multiple_masters.html#FT_MM_Axis">FT_MM_Axis</a></td><td><a href="ft2-ot_validation.html#FT_VALIDATE_OTXXX">FT_VALIDATE_OT</a></td></tr>
<tr><td><a href="ft2-cff_driver.html#FT_CFF_HINTING_XXX">FT_CFF_HINTING_ADOBE</a></td><td><a href="ft2-multiple_masters.html#FT_MM_Var">FT_MM_Var</a></td><td><a href="ft2-ot_validation.html#FT_VALIDATE_OTXXX">FT_VALIDATE_OTXXX</a></td></tr>
<tr><td><a href="ft2-cff_driver.html#FT_CFF_HINTING_XXX">FT_CFF_HINTING_FREETYPE</a></td><td><a href="ft2-header_file_macros.html#FT_MODULE_ERRORS_H">FT_MODULE_ERRORS_H</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_opbd</a></td></tr>
<tr><td><a href="ft2-cff_driver.html#FT_CFF_HINTING_XXX">FT_CFF_HINTING_XXX</a></td><td><a href="ft2-header_file_macros.html#FT_MODULE_H">FT_MODULE_H</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_prop</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Char">FT_Char</a></td><td><a href="ft2-base_interface.html#FT_Module">FT_Module</a></td><td><a href="ft2-gx_validation.html#FT_VALIDATE_GXXXX">FT_VALIDATE_trak</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_CharMap">FT_CharMap</a></td><td><a href="ft2-module_management.html#FT_Module_Class">FT_Module_Class</a></td><td><a href="ft2-multiple_masters.html#FT_Var_Axis">FT_Var_Axis</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_CharMapRec">FT_CharMapRec</a></td><td><a href="ft2-module_management.html#FT_Module_Constructor">FT_Module_Constructor</a></td><td><a href="ft2-multiple_masters.html#FT_Var_Named_Style">FT_Var_Named_Style</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CID_H">FT_CID_H</a></td><td><a href="ft2-module_management.html#FT_Module_Destructor">FT_Module_Destructor</a></td><td><a href="ft2-basic_types.html#FT_Vector">FT_Vector</a></td></tr>
<tr><td><a href="ft2-gx_validation.html#FT_ClassicKern_Free">FT_ClassicKern_Free</a></td><td><a href="ft2-module_management.html#FT_Module_Requester">FT_Module_Requester</a></td><td><a href="ft2-computations.html#FT_Vector_From_Polar">FT_Vector_From_Polar</a></td></tr>
<tr><td><a href="ft2-gx_validation.html#FT_ClassicKern_Validate">FT_ClassicKern_Validate</a></td><td><a href="ft2-header_file_macros.html#FT_MULTIPLE_MASTERS_H">FT_MULTIPLE_MASTERS_H</a></td><td><a href="ft2-computations.html#FT_Vector_Length">FT_Vector_Length</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CONFIG_CONFIG_H">FT_CONFIG_CONFIG_H</a></td><td><a href="ft2-computations.html#FT_MulDiv">FT_MulDiv</a></td><td><a href="ft2-computations.html#FT_Vector_Polarize">FT_Vector_Polarize</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CONFIG_MODULES_H">FT_CONFIG_MODULES_H</a></td><td><a href="ft2-computations.html#FT_MulFix">FT_MulFix</a></td><td><a href="ft2-computations.html#FT_Vector_Rotate">FT_Vector_Rotate</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CONFIG_OPTIONS_H">FT_CONFIG_OPTIONS_H</a></td><td><a href="ft2-multiple_masters.html#FT_Multi_Master">FT_Multi_Master</a></td><td><a href="ft2-computations.html#FT_Vector_Transform">FT_Vector_Transform</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_CONFIG_STANDARD_LIBRARY_H">FT_CONFIG_STANDARD_LIBRARY_H</a></td><td><a href="ft2-base_interface.html#FT_New_Face">FT_New_Face</a></td><td><a href="ft2-computations.html#FT_Vector_Unit">FT_Vector_Unit</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_Cos">FT_Cos</a></td><td><a href="ft2-mac_specific.html#FT_New_Face_From_FOND">FT_New_Face_From_FOND</a></td><td><a href="ft2-header_file_macros.html#FT_WINFONTS_H">FT_WINFONTS_H</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Data">FT_Data</a></td><td><a href="ft2-mac_specific.html#FT_New_Face_From_FSRef">FT_New_Face_From_FSRef</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_Header">FT_WinFNT_Header</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_DivFix">FT_DivFix</a></td><td><a href="ft2-mac_specific.html#FT_New_Face_From_FSSpec">FT_New_Face_From_FSSpec</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_HeaderRec">FT_WinFNT_HeaderRec</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a></td><td><a href="ft2-module_management.html#FT_New_Library">FT_New_Library</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1250</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Done_FreeType">FT_Done_FreeType</a></td><td><a href="ft2-base_interface.html#FT_New_Memory_Face">FT_New_Memory_Face</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1251</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Done_Glyph">FT_Done_Glyph</a></td><td><a href="ft2-sizes_management.html#FT_New_Size">FT_New_Size</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1252</a></td></tr>
<tr><td><a href="ft2-module_management.html#FT_Done_Library">FT_Done_Library</a></td><td><a href="ft2-basic_types.html#FT_Offset">FT_Offset</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1253</a></td></tr>
<tr><td><a href="ft2-sizes_management.html#FT_Done_Size">FT_Done_Size</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_DRIVER</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1254</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Driver">FT_Driver</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_MEMORY</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1255</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_PARAMS</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1256</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_CUSTOM</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_PATHNAME</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1257</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_EXPERT</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_STREAM</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1258</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_LATIN_1</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_XXX</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1361</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_STANDARD</a></td><td><a href="ft2-header_file_macros.html#FT_OPENTYPE_VALIDATE_H">FT_OPENTYPE_VALIDATE_H</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP874</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_APPLE_ROMAN</a></td><td><a href="ft2-base_interface.html#FT_Open_Args">FT_Open_Args</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP932</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_BIG5</a></td><td><a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP936</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_GB2312</a></td><td><a href="ft2-ot_validation.html#FT_OpenType_Free">FT_OpenType_Free</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP949</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_JOHAB</a></td><td><a href="ft2-ot_validation.html#FT_OpenType_Validate">FT_OpenType_Validate</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP950</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_BIG5</a></td><td><a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_FILL_LEFT</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_DEFAULT</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_GB2312</a></td><td><a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_FILL_RIGHT</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_MAC</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_JOHAB</a></td><td><a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_NONE</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_OEM</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_SJIS</a></td><td><a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_POSTSCRIPT</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_SYMBOL</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_SYMBOL</a></td><td><a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_TRUETYPE</a></td><td><a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_XXX</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_WANSUNG</a></td><td><a href="ft2-outline_processing.html#FT_Orientation">FT_Orientation</a></td><td><a href="ft2-header_file_macros.html#FT_XFREE86_H">FT_XFREE86_H</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_NONE</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_EVEN_ODD_FILL</a></td><td><a href="ft2-cache_subsystem.html#FTC_CMapCache">FTC_CMapCache</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_OLD_LATIN_2</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_FLAGS</a></td><td><a href="ft2-cache_subsystem.html#FTC_CMapCache_Lookup">FTC_CMapCache_Lookup</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_SJIS</a></td><td><a href="ft2-header_file_macros.html#FT_OUTLINE_H">FT_OUTLINE_H</a></td><td><a href="ft2-cache_subsystem.html#FTC_CMapCache_New">FTC_CMapCache_New</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_UNICODE</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_HIGH_PRECISION</a></td><td><a href="ft2-cache_subsystem.html#FTC_Face_Requester">FTC_Face_Requester</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_WANSUNG</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_IGNORE_DROPOUTS</a></td><td><a href="ft2-cache_subsystem.html#FTC_FaceID">FTC_FaceID</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Encoding">FT_Encoding</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_INCLUDE_STUBS</a></td><td><a href="ft2-cache_subsystem.html#FTC_ImageCache">FTC_ImageCache</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_ERRORS_H">FT_ERRORS_H</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_NONE</a></td><td><a href="ft2-cache_subsystem.html#FTC_ImageCache_Lookup">FTC_ImageCache_Lookup</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Error">FT_Error</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_OWNER</a></td><td><a href="ft2-cache_subsystem.html#FTC_ImageCache_LookupScaler">FTC_ImageCache_LookupScaler</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_F26Dot6">FT_F26Dot6</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_REVERSE_FILL</a></td><td><a href="ft2-cache_subsystem.html#FTC_ImageCache_New">FTC_ImageCache_New</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_F2Dot14">FT_F2Dot14</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_SINGLE_PASS</a></td><td><a href="ft2-cache_subsystem.html#FTC_ImageType">FTC_ImageType</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_CID_KEYED</a></td><td><a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_SMART_DROPOUTS</a></td><td><a href="ft2-cache_subsystem.html#FTC_ImageTypeRec">FTC_ImageTypeRec</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_COLOR</a></td><td><a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a></td><td><a href="ft2-cache_subsystem.html#FTC_Manager">FTC_Manager</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_EXTERNAL_STREAM</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Check">FT_Outline_Check</a></td><td><a href="ft2-cache_subsystem.html#FTC_Manager_Done">FTC_Manager_Done</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FAST_GLYPHS</a></td><td><a href="ft2-outline_processing.html#FT_Outline_ConicToFunc">FT_Outline_ConicToFunc</a></td><td><a href="ft2-cache_subsystem.html#FTC_Manager_LookupFace">FTC_Manager_LookupFace</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FIXED_SIZES</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Copy">FT_Outline_Copy</a></td><td><a href="ft2-cache_subsystem.html#FTC_Manager_LookupSize">FTC_Manager_LookupSize</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FIXED_WIDTH</a></td><td><a href="ft2-outline_processing.html#FT_Outline_CubicToFunc">FT_Outline_CubicToFunc</a></td><td><a href="ft2-cache_subsystem.html#FTC_Manager_New">FTC_Manager_New</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_GLYPH_NAMES</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Decompose">FT_Outline_Decompose</a></td><td><a href="ft2-cache_subsystem.html#FTC_Manager_RemoveFaceID">FTC_Manager_RemoveFaceID</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_HINTER</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Done">FT_Outline_Done</a></td><td><a href="ft2-cache_subsystem.html#FTC_Manager_Reset">FTC_Manager_Reset</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_HORIZONTAL</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Embolden">FT_Outline_Embolden</a></td><td><a href="ft2-cache_subsystem.html#FTC_Node">FTC_Node</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_KERNING</a></td><td><a href="ft2-outline_processing.html#FT_Outline_EmboldenXY">FT_Outline_EmboldenXY</a></td><td><a href="ft2-cache_subsystem.html#FTC_Node_Unref">FTC_Node_Unref</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_MULTIPLE_MASTERS</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Funcs">FT_Outline_Funcs</a></td><td><a href="ft2-cache_subsystem.html#FTC_SBit">FTC_SBit</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_SCALABLE</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Get_BBox">FT_Outline_Get_BBox</a></td><td><a href="ft2-cache_subsystem.html#FTC_SBitCache">FTC_SBitCache</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_SFNT</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Get_Bitmap">FT_Outline_Get_Bitmap</a></td><td><a href="ft2-cache_subsystem.html#FTC_SBitCache_Lookup">FTC_SBitCache_Lookup</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_TRICKY</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Get_CBox">FT_Outline_Get_CBox</a></td><td><a href="ft2-cache_subsystem.html#FTC_SBitCache_LookupScaler">FTC_SBitCache_LookupScaler</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_VERTICAL</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Get_Orientation">FT_Outline_Get_Orientation</a></td><td><a href="ft2-cache_subsystem.html#FTC_SBitCache_New">FTC_SBitCache_New</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_XXX</a></td><td><a href="ft2-glyph_stroker.html#FT_Outline_GetInsideBorder">FT_Outline_GetInsideBorder</a></td><td><a href="ft2-cache_subsystem.html#FTC_SBitRec">FTC_SBitRec</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Face">FT_Face</a></td><td><a href="ft2-glyph_stroker.html#FT_Outline_GetOutsideBorder">FT_Outline_GetOutsideBorder</a></td><td><a href="ft2-cache_subsystem.html#FTC_Scaler">FTC_Scaler</a></td></tr>
<tr><td><a href="ft2-version.html#FT_Face_CheckTrueTypePatents">FT_Face_CheckTrueTypePatents</a></td><td><a href="ft2-outline_processing.html#FT_Outline_LineToFunc">FT_Outline_LineToFunc</a></td><td><a href="ft2-cache_subsystem.html#FTC_ScalerRec">FTC_ScalerRec</a></td></tr>
<tr><td><a href="ft2-glyph_variants.html#FT_Face_GetCharsOfVariant">FT_Face_GetCharsOfVariant</a></td><td><a href="ft2-outline_processing.html#FT_Outline_MoveToFunc">FT_Outline_MoveToFunc</a></td><td><a href="ft2-auto_hinter.html#fallback-script">fallback-script</a></td></tr>
<tr><td><a href="ft2-glyph_variants.html#FT_Face_GetCharVariantIndex">FT_Face_GetCharVariantIndex</a></td><td><a href="ft2-outline_processing.html#FT_Outline_New">FT_Outline_New</a></td><td><a href="ft2-base_interface.html#ft_encoding_xxx">ft_encoding_xxx</a></td></tr>
<tr><td><a href="ft2-glyph_variants.html#FT_Face_GetCharVariantIsDefault">FT_Face_GetCharVariantIsDefault</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Render">FT_Outline_Render</a></td><td><a href="ft2-glyph_management.html#ft_glyph_bbox_xxx">ft_glyph_bbox_gridfit</a></td></tr>
<tr><td><a href="ft2-glyph_variants.html#FT_Face_GetVariantSelectors">FT_Face_GetVariantSelectors</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Reverse">FT_Outline_Reverse</a></td><td><a href="ft2-glyph_management.html#ft_glyph_bbox_xxx">ft_glyph_bbox_pixels</a></td></tr>
<tr><td><a href="ft2-glyph_variants.html#FT_Face_GetVariantsOfChar">FT_Face_GetVariantsOfChar</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Transform">FT_Outline_Transform</a></td><td><a href="ft2-glyph_management.html#ft_glyph_bbox_xxx">ft_glyph_bbox_subpixels</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Face_Internal">FT_Face_Internal</a></td><td><a href="ft2-outline_processing.html#FT_Outline_Translate">FT_Outline_Translate</a></td><td><a href="ft2-glyph_management.html#ft_glyph_bbox_xxx">ft_glyph_bbox_truncate</a></td></tr>
<tr><td><a href="ft2-version.html#FT_Face_SetUnpatentedHinting">FT_Face_SetUnpatentedHinting</a></td><td><a href="ft2-glyph_management.html#FT_OutlineGlyph">FT_OutlineGlyph</a></td><td><a href="ft2-glyph_management.html#ft_glyph_bbox_xxx">ft_glyph_bbox_unscaled</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a></td><td><a href="ft2-glyph_management.html#FT_OutlineGlyphRec">FT_OutlineGlyphRec</a></td><td><a href="ft2-glyph_management.html#ft_glyph_bbox_xxx">ft_glyph_bbox_xxx</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a></td><td><a href="ft2-sfnt_names.html#FT_PARAM_TAG_IGNORE_PREFERRED_FAMILY">FT_PARAM_TAG_IGNORE_PREFERRED_FAMILY</a></td><td><a href="ft2-basic_types.html#ft_glyph_format_xxx">ft_glyph_format_bitmap</a></td></tr>
<tr><td><a href="ft2-computations.html#FT_FloorFix">FT_FloorFix</a></td><td><a href="ft2-sfnt_names.html#FT_PARAM_TAG_IGNORE_PREFERRED_SUBFAMILY">FT_PARAM_TAG_IGNORE_PREFERRED_SUBFAMILY</a></td><td><a href="ft2-basic_types.html#ft_glyph_format_xxx">ft_glyph_format_composite</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_FREETYPE_H">FT_FREETYPE_H</a></td><td><a href="ft2-incremental.html#FT_PARAM_TAG_INCREMENTAL">FT_PARAM_TAG_INCREMENTAL</a></td><td><a href="ft2-basic_types.html#ft_glyph_format_xxx">ft_glyph_format_none</a></td></tr>
<tr><td><a href="ft2-system_interface.html#FT_Free_Func">FT_Free_Func</a></td><td><a href="ft2-truetype_tables.html#FT_PARAM_TAG_UNPATENTED_HINTING">FT_PARAM_TAG_UNPATENTED_HINTING</a></td><td><a href="ft2-basic_types.html#ft_glyph_format_xxx">ft_glyph_format_outline</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_BITMAP_EMBEDDING_ONLY</a></td><td><a href="ft2-basic_types.html#FT_Palette_Mode">FT_Palette_Mode</a></td><td><a href="ft2-basic_types.html#ft_glyph_format_xxx">ft_glyph_format_plotter</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_EDITABLE_EMBEDDING</a></td><td><a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a></td><td><a href="ft2-basic_types.html#ft_glyph_format_xxx">ft_glyph_format_xxx</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_INSTALLABLE_EMBEDDING</a></td><td><a href="ft2-header_file_macros.html#FT_PFR_H">FT_PFR_H</a></td><td><a href="ft2-base_interface.html#ft_kerning_default">ft_kerning_default</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_NO_SUBSETTING</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_BGRA</a></td><td><a href="ft2-base_interface.html#ft_kerning_unfitted">ft_kerning_unfitted</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_PREVIEW_AND_PRINT_EMBEDDING</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_GRAY</a></td><td><a href="ft2-base_interface.html#ft_kerning_unscaled">ft_kerning_unscaled</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_RESTRICTED_LICENSE_EMBEDDING</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_GRAY2</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_driver</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_XXX</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_GRAY4</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_memory</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_FWord">FT_FWord</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_LCD</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_params</a></td></tr>
<tr><td><a href="ft2-gasp_table.html#FT_GASP_XXX">FT_GASP_DO_GRAY</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_LCD_V</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_pathname</a></td></tr>
<tr><td><a href="ft2-gasp_table.html#FT_GASP_XXX">FT_GASP_DO_GRIDFIT</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_MONO</a></td><td><a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_stream</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_GASP_H">FT_GASP_H</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_NONE</a></td><td><a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_even_odd_fill</a></td></tr>
<tr><td><a href="ft2-gasp_table.html#FT_GASP_XXX">FT_GASP_NO_TABLE</a></td><td><a href="ft2-basic_types.html#FT_Pixel_Mode">FT_Pixel_Mode</a></td><td><a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_flags</a></td></tr>
<tr><td><a href="ft2-gasp_table.html#FT_GASP_XXX">FT_GASP_SYMMETRIC_GRIDFIT</a></td><td><a href="ft2-basic_types.html#FT_Pointer">FT_Pointer</a></td><td><a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_high_precision</a></td></tr>
<tr><td><a href="ft2-gasp_table.html#FT_GASP_XXX">FT_GASP_SYMMETRIC_SMOOTHING</a></td><td><a href="ft2-basic_types.html#FT_Pos">FT_Pos</a></td><td><a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_ignore_dropouts</a></td></tr>
<tr><td><a href="ft2-gasp_table.html#FT_GASP_XXX">FT_GASP_XXX</a></td><td><a href="ft2-auto_hinter.html#FT_Prop_GlyphToScriptMap">FT_Prop_GlyphToScriptMap</a></td><td><a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_none</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Generic">FT_Generic</a></td><td><a href="ft2-auto_hinter.html#FT_Prop_IncreaseXHeight">FT_Prop_IncreaseXHeight</a></td><td><a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_owner</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Generic_Finalizer">FT_Generic_Finalizer</a></td><td><a href="ft2-module_management.html#FT_Property_Get">FT_Property_Get</a></td><td><a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_reverse_fill</a></td></tr>
<tr><td><a href="ft2-quick_advance.html#FT_Get_Advance">FT_Get_Advance</a></td><td><a href="ft2-module_management.html#FT_Property_Set">FT_Property_Set</a></td><td><a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_single_pass</a></td></tr>
<tr><td><a href="ft2-quick_advance.html#FT_Get_Advances">FT_Get_Advances</a></td><td><a href="ft2-bdf_fonts.html#FT_PropertyType">FT_PropertyType</a></td><td><a href="ft2-basic_types.html#FT_Palette_Mode">ft_palette_mode_rgb</a></td></tr>
<tr><td><a href="ft2-bdf_fonts.html#FT_Get_BDF_Charset_ID">FT_Get_BDF_Charset_ID</a></td><td><a href="ft2-basic_types.html#FT_PtrDist">FT_PtrDist</a></td><td><a href="ft2-basic_types.html#FT_Palette_Mode">ft_palette_mode_rgba</a></td></tr>
<tr><td><a href="ft2-bdf_fonts.html#FT_Get_BDF_Property">FT_Get_BDF_Property</a></td><td><a href="ft2-raster.html#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_AA</a></td><td><a href="ft2-basic_types.html#ft_pixel_mode_xxx">ft_pixel_mode_grays</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_Char_Index">FT_Get_Char_Index</a></td><td><a href="ft2-raster.html#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_CLIP</a></td><td><a href="ft2-basic_types.html#ft_pixel_mode_xxx">ft_pixel_mode_mono</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_Charmap_Index">FT_Get_Charmap_Index</a></td><td><a href="ft2-raster.html#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_DEFAULT</a></td><td><a href="ft2-basic_types.html#ft_pixel_mode_xxx">ft_pixel_mode_none</a></td></tr>
<tr><td><a href="ft2-cid_fonts.html#FT_Get_CID_From_Glyph_Index">FT_Get_CID_From_Glyph_Index</a></td><td><a href="ft2-raster.html#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_DIRECT</a></td><td><a href="ft2-basic_types.html#ft_pixel_mode_xxx">ft_pixel_mode_pal2</a></td></tr>
<tr><td><a href="ft2-cid_fonts.html#FT_Get_CID_Is_Internally_CID_Keyed">FT_Get_CID_Is_Internally_CID_Keyed</a></td><td><a href="ft2-raster.html#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_XXX</a></td><td><a href="ft2-basic_types.html#ft_pixel_mode_xxx">ft_pixel_mode_pal4</a></td></tr>
<tr><td><a href="ft2-cid_fonts.html#FT_Get_CID_Registry_Ordering_Supplement">FT_Get_CID_Registry_Ordering_Supplement</a></td><td><a href="ft2-raster.html#FT_Raster">FT_Raster</a></td><td><a href="ft2-basic_types.html#ft_pixel_mode_xxx">ft_pixel_mode_xxx</a></td></tr>
<tr><td><a href="ft2-truetype_tables.html#FT_Get_CMap_Format">FT_Get_CMap_Format</a></td><td><a href="ft2-raster.html#FT_Raster_BitSet_Func">FT_Raster_BitSet_Func</a></td><td><a href="ft2-base_interface.html#ft_render_mode_xxx">ft_render_mode_mono</a></td></tr>
<tr><td><a href="ft2-truetype_tables.html#FT_Get_CMap_Language_ID">FT_Get_CMap_Language_ID</a></td><td><a href="ft2-raster.html#FT_Raster_BitTest_Func">FT_Raster_BitTest_Func</a></td><td><a href="ft2-base_interface.html#ft_render_mode_xxx">ft_render_mode_normal</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_First_Char">FT_Get_First_Char</a></td><td><a href="ft2-raster.html#FT_Raster_DoneFunc">FT_Raster_DoneFunc</a></td><td><a href="ft2-base_interface.html#ft_render_mode_xxx">ft_render_mode_xxx</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_FSType_Flags">FT_Get_FSType_Flags</a></td><td><a href="ft2-raster.html#FT_Raster_Funcs">FT_Raster_Funcs</a></td><td><a href="ft2-auto_hinter.html#glyph-to-script-map">glyph-to-script-map</a></td></tr>
<tr><td><a href="ft2-gasp_table.html#FT_Get_Gasp">FT_Get_Gasp</a></td><td><a href="ft2-raster.html#FT_Raster_NewFunc">FT_Raster_NewFunc</a></td><td><a href="ft2-cff_driver.html#hinting-engine">hinting-engine</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Get_Glyph">FT_Get_Glyph</a></td><td><a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a></td><td><a href="ft2-auto_hinter.html#increase-x-height">increase-x-height</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_Glyph_Name">FT_Get_Glyph_Name</a></td><td><a href="ft2-raster.html#FT_Raster_RenderFunc">FT_Raster_RenderFunc</a></td><td><a href="ft2-tt_driver.html#interpreter-version">interpreter-version</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_Kerning">FT_Get_Kerning</a></td><td><a href="ft2-raster.html#FT_Raster_ResetFunc">FT_Raster_ResetFunc</a></td><td><a href="ft2-cff_driver.html#no-stem-darkening">no-stem-darkening</a></td></tr>
<tr><td><a href="ft2-multiple_masters.html#FT_Get_MM_Var">FT_Get_MM_Var</a></td><td><a href="ft2-raster.html#FT_Raster_SetModeFunc">FT_Raster_SetModeFunc</a></td><td><a href="ft2-type1_tables.html#PS_Dict_Keys">PS_Dict_Keys</a></td></tr>
<tr><td><a href="ft2-module_management.html#FT_Get_Module">FT_Get_Module</a></td><td><a href="ft2-header_file_macros.html#FT_RENDER_H">FT_RENDER_H</a></td><td><a href="ft2-type1_tables.html#PS_FontInfo">PS_FontInfo</a></td></tr>
<tr><td><a href="ft2-multiple_masters.html#FT_Get_Multi_Master">FT_Get_Multi_Master</a></td><td><a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LCD</a></td><td><a href="ft2-type1_tables.html#PS_FontInfoRec">PS_FontInfoRec</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_Name_Index">FT_Get_Name_Index</a></td><td><a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LCD_V</a></td><td><a href="ft2-type1_tables.html#PS_Private">PS_Private</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_Next_Char">FT_Get_Next_Char</a></td><td><a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LIGHT</a></td><td><a href="ft2-type1_tables.html#PS_PrivateRec">PS_PrivateRec</a></td></tr>
<tr><td><a href="ft2-pfr_fonts.html#FT_Get_PFR_Advance">FT_Get_PFR_Advance</a></td><td><a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_MONO</a></td><td><a href="ft2-type1_tables.html#T1_Blend_Flags">T1_Blend_Flags</a></td></tr>
<tr><td><a href="ft2-pfr_fonts.html#FT_Get_PFR_Kerning">FT_Get_PFR_Kerning</a></td><td><a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_NORMAL</a></td><td><a href="ft2-type1_tables.html#T1_EncodingType">T1_EncodingType</a></td></tr>
<tr><td><a href="ft2-pfr_fonts.html#FT_Get_PFR_Metrics">FT_Get_PFR_Metrics</a></td><td><a href="ft2-system_interface.html#FT_Realloc_Func">FT_Realloc_Func</a></td><td><a href="ft2-type1_tables.html#T1_FontInfo">T1_FontInfo</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_Postscript_Name">FT_Get_Postscript_Name</a></td><td><a href="ft2-base_interface.html#FT_Reference_Face">FT_Reference_Face</a></td><td><a href="ft2-type1_tables.html#T1_Private">T1_Private</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#FT_Get_PS_Font_Info">FT_Get_PS_Font_Info</a></td><td><a href="ft2-module_management.html#FT_Reference_Library">FT_Reference_Library</a></td><td><a href="ft2-truetype_tables.html#TT_ADOBE_ID_XXX">TT_ADOBE_ID_CUSTOM</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#FT_Get_PS_Font_Private">FT_Get_PS_Font_Private</a></td><td><a href="ft2-module_management.html#FT_Remove_Module">FT_Remove_Module</a></td><td><a href="ft2-truetype_tables.html#TT_ADOBE_ID_XXX">TT_ADOBE_ID_EXPERT</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#FT_Get_PS_Font_Value">FT_Get_PS_Font_Value</a></td><td><a href="ft2-base_interface.html#FT_Render_Glyph">FT_Render_Glyph</a></td><td><a href="ft2-truetype_tables.html#TT_ADOBE_ID_XXX">TT_ADOBE_ID_LATIN_1</a></td></tr>
<tr><td><a href="ft2-module_management.html#FT_Get_Renderer">FT_Get_Renderer</a></td><td><a href="ft2-base_interface.html#FT_Render_Mode">FT_Render_Mode</a></td><td><a href="ft2-truetype_tables.html#TT_ADOBE_ID_XXX">TT_ADOBE_ID_STANDARD</a></td></tr>
<tr><td><a href="ft2-sfnt_names.html#FT_Get_Sfnt_Name">FT_Get_Sfnt_Name</a></td><td><a href="ft2-base_interface.html#FT_Renderer">FT_Renderer</a></td><td><a href="ft2-truetype_tables.html#TT_ADOBE_ID_XXX">TT_ADOBE_ID_XXX</a></td></tr>
<tr><td><a href="ft2-sfnt_names.html#FT_Get_Sfnt_Name_Count">FT_Get_Sfnt_Name_Count</a></td><td><a href="ft2-module_management.html#FT_Renderer_Class">FT_Renderer_Class</a></td><td><a href="ft2-truetype_tables.html#TT_APPLE_ID_XXX">TT_APPLE_ID_DEFAULT</a></td></tr>
<tr><td><a href="ft2-truetype_tables.html#FT_Get_Sfnt_Table">FT_Get_Sfnt_Table</a></td><td><a href="ft2-base_interface.html#FT_Request_Size">FT_Request_Size</a></td><td><a href="ft2-truetype_tables.html#TT_APPLE_ID_XXX">TT_APPLE_ID_ISO_10646</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_SubGlyph_Info">FT_Get_SubGlyph_Info</a></td><td><a href="ft2-computations.html#FT_RoundFix">FT_RoundFix</a></td><td><a href="ft2-truetype_tables.html#TT_APPLE_ID_XXX">TT_APPLE_ID_UNICODE_1_1</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Get_Track_Kerning">FT_Get_Track_Kerning</a></td><td><a href="ft2-base_interface.html#FT_Select_Charmap">FT_Select_Charmap</a></td><td><a href="ft2-truetype_tables.html#TT_APPLE_ID_XXX">TT_APPLE_ID_UNICODE_2_0</a></td></tr>
<tr><td><a href="ft2-truetype_engine.html#FT_Get_TrueType_Engine_Type">FT_Get_TrueType_Engine_Type</a></td><td><a href="ft2-base_interface.html#FT_Select_Size">FT_Select_Size</a></td><td><a href="ft2-truetype_tables.html#TT_APPLE_ID_XXX">TT_APPLE_ID_UNICODE_32</a></td></tr>
<tr><td><a href="ft2-winfnt_fonts.html#FT_Get_WinFNT_Header">FT_Get_WinFNT_Header</a></td><td><a href="ft2-base_interface.html#FT_Set_Char_Size">FT_Set_Char_Size</a></td><td><a href="ft2-truetype_tables.html#TT_APPLE_ID_XXX">TT_APPLE_ID_VARIANT_SELECTOR</a></td></tr>
<tr><td><a href="ft2-font_formats.html#FT_Get_X11_Font_Format">FT_Get_X11_Font_Format</a></td><td><a href="ft2-base_interface.html#FT_Set_Charmap">FT_Set_Charmap</a></td><td><a href="ft2-truetype_tables.html#TT_APPLE_ID_XXX">TT_APPLE_ID_XXX</a></td></tr>
<tr><td><a href="ft2-mac_specific.html#FT_GetFile_From_Mac_ATS_Name">FT_GetFile_From_Mac_ATS_Name</a></td><td><a href="ft2-module_management.html#FT_Set_Debug_Hook">FT_Set_Debug_Hook</a></td><td><a href="ft2-truetype_tables.html#TT_Header">TT_Header</a></td></tr>
<tr><td><a href="ft2-mac_specific.html#FT_GetFile_From_Mac_Name">FT_GetFile_From_Mac_Name</a></td><td><a href="ft2-multiple_masters.html#FT_Set_MM_Blend_Coordinates">FT_Set_MM_Blend_Coordinates</a></td><td><a href="ft2-truetype_tables.html#TT_HoriHeader">TT_HoriHeader</a></td></tr>
<tr><td><a href="ft2-mac_specific.html#FT_GetFilePath_From_Mac_ATS_Name">FT_GetFilePath_From_Mac_ATS_Name</a></td><td><a href="ft2-multiple_masters.html#FT_Set_MM_Design_Coordinates">FT_Set_MM_Design_Coordinates</a></td><td><a href="ft2-tt_driver.html#TT_INTERPRETER_VERSION_XXX">TT_INTERPRETER_VERSION_35</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_GRIDFIT</a></td><td><a href="ft2-base_interface.html#FT_Set_Pixel_Sizes">FT_Set_Pixel_Sizes</a></td><td><a href="ft2-tt_driver.html#TT_INTERPRETER_VERSION_XXX">TT_INTERPRETER_VERSION_38</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_PIXELS</a></td><td><a href="ft2-module_management.html#FT_Set_Renderer">FT_Set_Renderer</a></td><td><a href="ft2-tt_driver.html#TT_INTERPRETER_VERSION_XXX">TT_INTERPRETER_VERSION_XXX</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_SUBPIXELS</a></td><td><a href="ft2-base_interface.html#FT_Set_Transform">FT_Set_Transform</a></td><td><a href="ft2-truetype_tables.html#TT_ISO_ID_XXX">TT_ISO_ID_10646</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_TRUNCATE</a></td><td><a href="ft2-multiple_masters.html#FT_Set_Var_Blend_Coordinates">FT_Set_Var_Blend_Coordinates</a></td><td><a href="ft2-truetype_tables.html#TT_ISO_ID_XXX">TT_ISO_ID_7BIT_ASCII</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_GLYPH_BBOX_UNSCALED</a></td><td><a href="ft2-multiple_masters.html#FT_Set_Var_Design_Coordinates">FT_Set_Var_Design_Coordinates</a></td><td><a href="ft2-truetype_tables.html#TT_ISO_ID_XXX">TT_ISO_ID_8859_1</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_BITMAP</a></td><td><a href="ft2-header_file_macros.html#FT_SFNT_NAMES_H">FT_SFNT_NAMES_H</a></td><td><a href="ft2-truetype_tables.html#TT_ISO_ID_XXX">TT_ISO_ID_XXX</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_COMPOSITE</a></td><td><a href="ft2-truetype_tables.html#FT_Sfnt_Table_Info">FT_Sfnt_Table_Info</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_ARABIC</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_NONE</a></td><td><a href="ft2-truetype_tables.html#FT_Sfnt_Tag">FT_Sfnt_Tag</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_ARMENIAN</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_OUTLINE</a></td><td><a href="ft2-sfnt_names.html#FT_SfntName">FT_SfntName</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_BENGALI</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_PLOTTER</a></td><td><a href="ft2-basic_types.html#FT_Short">FT_Short</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_BURMESE</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_GLYPH_H">FT_GLYPH_H</a></td><td><a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_BBOX</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_DEVANAGARI</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph">FT_Glyph</a></td><td><a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_CELL</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_GEEZ</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_BBox_Mode">FT_Glyph_BBox_Mode</a></td><td><a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_NOMINAL</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_GEORGIAN</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_Copy">FT_Glyph_Copy</a></td><td><a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_REAL_DIM</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_GREEK</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Glyph_Format">FT_Glyph_Format</a></td><td><a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_SCALES</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_GUJARATI</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_Get_CBox">FT_Glyph_Get_CBox</a></td><td><a href="ft2-header_file_macros.html#FT_SIZES_H">FT_SIZES_H</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_GURMUKHI</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Glyph_Metrics">FT_Glyph_Metrics</a></td><td><a href="ft2-computations.html#FT_Sin">FT_Sin</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_HEBREW</a></td></tr>
<tr><td><a href="ft2-glyph_stroker.html#FT_Glyph_Stroke">FT_Glyph_Stroke</a></td><td><a href="ft2-base_interface.html#FT_Size">FT_Size</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_JAPANESE</a></td></tr>
<tr><td><a href="ft2-glyph_stroker.html#FT_Glyph_StrokeBorder">FT_Glyph_StrokeBorder</a></td><td><a href="ft2-base_interface.html#FT_Size_Internal">FT_Size_Internal</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_KANNADA</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_To_Bitmap">FT_Glyph_To_Bitmap</a></td><td><a href="ft2-base_interface.html#FT_Size_Metrics">FT_Size_Metrics</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_KHMER</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_Glyph_Transform">FT_Glyph_Transform</a></td><td><a href="ft2-base_interface.html#FT_Size_Request">FT_Size_Request</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_KOREAN</a></td></tr>
<tr><td><a href="ft2-glyph_management.html#FT_GlyphRec">FT_GlyphRec</a></td><td><a href="ft2-base_interface.html#FT_Size_Request_Type">FT_Size_Request_Type</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_LAOTIAN</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_GlyphSlot">FT_GlyphSlot</a></td><td><a href="ft2-base_interface.html#FT_Size_RequestRec">FT_Size_RequestRec</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_MALAYALAM</a></td></tr>
<tr><td><a href="ft2-bitmap_handling.html#FT_GlyphSlot_Own_Bitmap">FT_GlyphSlot_Own_Bitmap</a></td><td><a href="ft2-base_interface.html#FT_SizeRec">FT_SizeRec</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_MALDIVIAN</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_GlyphSlotRec">FT_GlyphSlotRec</a></td><td><a href="ft2-base_interface.html#FT_Slot_Internal">FT_Slot_Internal</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_MONGOLIAN</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_GX_VALIDATE_H">FT_GX_VALIDATE_H</a></td><td><a href="ft2-raster.html#FT_Span">FT_Span</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_ORIYA</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_GZIP_H">FT_GZIP_H</a></td><td><a href="ft2-raster.html#FT_SpanFunc">FT_SpanFunc</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_ROMAN</a></td></tr>
<tr><td><a href="ft2-gzip.html#FT_Gzip_Uncompress">FT_Gzip_Uncompress</a></td><td><a href="ft2-glyph_stroker.html#FT_StrokerBorder">FT_STROKER_BORDER_LEFT</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_RSYMBOL</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_HAS_COLOR">FT_HAS_COLOR</a></td><td><a href="ft2-glyph_stroker.html#FT_StrokerBorder">FT_STROKER_BORDER_RIGHT</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_RUSSIAN</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_HAS_FAST_GLYPHS">FT_HAS_FAST_GLYPHS</a></td><td><a href="ft2-header_file_macros.html#FT_STROKER_H">FT_STROKER_H</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_SIMPLIFIED_CHINESE</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_HAS_FIXED_SIZES">FT_HAS_FIXED_SIZES</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineCap">FT_STROKER_LINECAP_BUTT</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_SINDHI</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_HAS_GLYPH_NAMES">FT_HAS_GLYPH_NAMES</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineCap">FT_STROKER_LINECAP_ROUND</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_SINHALESE</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_HAS_HORIZONTAL">FT_HAS_HORIZONTAL</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineCap">FT_STROKER_LINECAP_SQUARE</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_SLAVIC</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_HAS_KERNING">FT_HAS_KERNING</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineJoin">FT_STROKER_LINEJOIN_BEVEL</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_TAMIL</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_HAS_MULTIPLE_MASTERS">FT_HAS_MULTIPLE_MASTERS</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineJoin">FT_STROKER_LINEJOIN_MITER</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_TELUGU</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_HAS_VERTICAL">FT_HAS_VERTICAL</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineJoin">FT_STROKER_LINEJOIN_MITER_FIXED</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_THAI</a></td></tr>
<tr><td><a href="ft2-type1_tables.html#FT_Has_PS_Glyph_Names">FT_Has_PS_Glyph_Names</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineJoin">FT_STROKER_LINEJOIN_MITER_VARIABLE</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_TIBETAN</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_IMAGE_H">FT_IMAGE_H</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineJoin">FT_STROKER_LINEJOIN_ROUND</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_TRADITIONAL_CHINESE</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_IMAGE_TAG">FT_IMAGE_TAG</a></td><td><a href="ft2-base_interface.html#FT_STYLE_FLAG_XXX">FT_STYLE_FLAG_BOLD</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_UNINTERP</a></td></tr>
<tr><td><a href="ft2-header_file_macros.html#FT_INCREMENTAL_H">FT_INCREMENTAL_H</a></td><td><a href="ft2-base_interface.html#FT_STYLE_FLAG_XXX">FT_STYLE_FLAG_ITALIC</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_VIETNAMESE</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a></td><td><a href="ft2-base_interface.html#FT_STYLE_FLAG_XXX">FT_STYLE_FLAG_XXX</a></td><td><a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_XXX</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental_FreeGlyphDataFunc">FT_Incremental_FreeGlyphDataFunc</a></td><td><a href="ft2-system_interface.html#FT_Stream">FT_Stream</a></td><td><a href="ft2-truetype_tables.html#TT_MaxProfile">TT_MaxProfile</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental_FuncsRec">FT_Incremental_FuncsRec</a></td><td><a href="ft2-system_interface.html#FT_Stream_CloseFunc">FT_Stream_CloseFunc</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_BIG_5</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental_GetGlyphDataFunc">FT_Incremental_GetGlyphDataFunc</a></td><td><a href="ft2-system_interface.html#FT_Stream_IoFunc">FT_Stream_IoFunc</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_GB2312</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental_GetGlyphMetricsFunc">FT_Incremental_GetGlyphMetricsFunc</a></td><td><a href="ft2-bzip2.html#FT_Stream_OpenBzip2">FT_Stream_OpenBzip2</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_JOHAB</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental_Interface">FT_Incremental_Interface</a></td><td><a href="ft2-gzip.html#FT_Stream_OpenGzip">FT_Stream_OpenGzip</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_SJIS</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental_InterfaceRec">FT_Incremental_InterfaceRec</a></td><td><a href="ft2-lzw.html#FT_Stream_OpenLZW">FT_Stream_OpenLZW</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_SYMBOL_CS</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental_Metrics">FT_Incremental_Metrics</a></td><td><a href="ft2-system_interface.html#FT_StreamDesc">FT_StreamDesc</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_UCS_4</a></td></tr>
<tr><td><a href="ft2-incremental.html#FT_Incremental_MetricsRec">FT_Incremental_MetricsRec</a></td><td><a href="ft2-system_interface.html#FT_StreamRec">FT_StreamRec</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_UNICODE_CS</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Init_FreeType">FT_Init_FreeType</a></td><td><a href="ft2-basic_types.html#FT_String">FT_String</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_WANSUNG</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Int">FT_Int</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker">FT_Stroker</a></td><td><a href="ft2-truetype_tables.html#TT_MS_ID_XXX">TT_MS_ID_XXX</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Int16">FT_Int16</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_BeginSubPath">FT_Stroker_BeginSubPath</a></td><td><a href="ft2-truetype_tables.html#TT_OS2">TT_OS2</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Int32">FT_Int32</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_ConicTo">FT_Stroker_ConicTo</a></td><td><a href="ft2-truetype_tables.html#TT_PCLT">TT_PCLT</a></td></tr>
<tr><td><a href="ft2-basic_types.html#FT_Int64">FT_Int64</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_CubicTo">FT_Stroker_CubicTo</a></td><td><a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_ADOBE</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_IS_CID_KEYED">FT_IS_CID_KEYED</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_Done">FT_Stroker_Done</a></td><td><a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_APPLE_UNICODE</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_IS_FIXED_WIDTH">FT_IS_FIXED_WIDTH</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_EndSubPath">FT_Stroker_EndSubPath</a></td><td><a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_CUSTOM</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_IS_SCALABLE">FT_IS_SCALABLE</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_Export">FT_Stroker_Export</a></td><td><a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_ISO</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_IS_SFNT">FT_IS_SFNT</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_ExportBorder">FT_Stroker_ExportBorder</a></td><td><a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_MACINTOSH</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_IS_TRICKY">FT_IS_TRICKY</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_GetBorderCounts">FT_Stroker_GetBorderCounts</a></td><td><a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_MICROSOFT</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_DEFAULT</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_GetCounts">FT_Stroker_GetCounts</a></td><td><a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_XXX</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_UNFITTED</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineCap">FT_Stroker_LineCap</a></td><td><a href="ft2-truetype_tables.html#TT_Postscript">TT_Postscript</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_UNSCALED</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineJoin">FT_Stroker_LineJoin</a></td><td><a href="ft2-truetype_tables.html#TT_VertHeader">TT_VertHeader</a></td></tr>
<tr><td><a href="ft2-base_interface.html#FT_Kerning_Mode">FT_Kerning_Mode</a></td><td><a href="ft2-glyph_stroker.html#FT_Stroker_LineTo">FT_Stroker_LineTo</a></td><td></td></tr>
</table>
<hr>
<table><tr><td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<center><font size=-2>generated on Thu Mar  6 23:13:44 2014</font></center></body>
</html>
