<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
Incremental Loading
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_Incremental">FT_Incremental</a></td><td></td><td><a href="#FT_Incremental_GetGlyphMetricsFunc">FT_Incremental_GetGlyphMetricsFunc</a></td></tr>
<tr><td></td><td><a href="#FT_Incremental_MetricsRec">FT_Incremental_MetricsRec</a></td><td></td><td><a href="#FT_Incremental_FuncsRec">FT_Incremental_FuncsRec</a></td></tr>
<tr><td></td><td><a href="#FT_Incremental_Metrics">FT_Incremental_Metrics</a></td><td></td><td><a href="#FT_Incremental_InterfaceRec">FT_Incremental_InterfaceRec</a></td></tr>
<tr><td></td><td><a href="#FT_Incremental_GetGlyphDataFunc">FT_Incremental_GetGlyphDataFunc</a></td><td></td><td><a href="#FT_Incremental_Interface">FT_Incremental_Interface</a></td></tr>
<tr><td></td><td><a href="#FT_Incremental_FreeGlyphDataFunc">FT_Incremental_FreeGlyphDataFunc</a></td><td></td><td><a href="#FT_PARAM_TAG_INCREMENTAL">FT_PARAM_TAG_INCREMENTAL</a></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>This section contains various functions used to perform so-called &lsquo;incremental&rsquo; glyph loading. This is a mode where all glyphs loaded from a given <a href="ft2-base_interface.html#FT_Face">FT_Face</a> are provided by the client application,</p>
<p>Apart from that, all other tables are loaded normally from the font file. This mode is useful when FreeType is used within another engine, e.g., a PostScript Imaging Processor.</p>
<p>To enable this mode, you must use <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a>, passing an <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> with the <a href="ft2-incremental.html#FT_PARAM_TAG_INCREMENTAL">FT_PARAM_TAG_INCREMENTAL</a> tag and an <a href="ft2-incremental.html#FT_Incremental_Interface">FT_Incremental_Interface</a> value. See the comments for <a href="ft2-incremental.html#FT_Incremental_InterfaceRec">FT_Incremental_InterfaceRec</a> for an example.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental">FT_Incremental</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_IncrementalRec_*  <b>FT_Incremental</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An opaque type describing a user-provided object used to implement &lsquo;incremental&rsquo; glyph loading within FreeType. This is used to support embedded fonts in certain environments (e.g., PostScript interpreters), where the glyph data isn't in the font file, or must be overridden by different values.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>It is up to client applications to create and implement <a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a> objects, as long as they provide implementations for the methods <a href="ft2-incremental.html#FT_Incremental_GetGlyphDataFunc">FT_Incremental_GetGlyphDataFunc</a>, <a href="ft2-incremental.html#FT_Incremental_FreeGlyphDataFunc">FT_Incremental_FreeGlyphDataFunc</a> and <a href="ft2-incremental.html#FT_Incremental_GetGlyphMetricsFunc">FT_Incremental_GetGlyphMetricsFunc</a>.</p>
<p>See the description of <a href="ft2-incremental.html#FT_Incremental_InterfaceRec">FT_Incremental_InterfaceRec</a> to understand how to use incremental objects with FreeType.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental_MetricsRec">FT_Incremental_MetricsRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Incremental_MetricsRec_
  {
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  bearing_x;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  bearing_y;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  advance;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  advance_v;     /* since 2.3.12 */

  } <b>FT_Incremental_MetricsRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A small structure used to contain the basic glyph metrics returned by the <a href="ft2-incremental.html#FT_Incremental_GetGlyphMetricsFunc">FT_Incremental_GetGlyphMetricsFunc</a> method.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>bearing_x</b></td><td>
<p>Left bearing, in font units.</p>
</td></tr>
<tr valign=top><td><b>bearing_y</b></td><td>
<p>Top bearing, in font units.</p>
</td></tr>
<tr valign=top><td><b>advance</b></td><td>
<p>Horizontal component of glyph advance, in font units.</p>
</td></tr>
<tr valign=top><td><b>advance_v</b></td><td>
<p>Vertical component of glyph advance, in font units.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>These correspond to horizontal or vertical metrics depending on the value of the &lsquo;vertical&rsquo; argument to the function <a href="ft2-incremental.html#FT_Incremental_GetGlyphMetricsFunc">FT_Incremental_GetGlyphMetricsFunc</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental_Metrics">FT_Incremental_Metrics</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

   <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Incremental_MetricsRec_*  <b>FT_Incremental_Metrics</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to an <a href="ft2-incremental.html#FT_Incremental_MetricsRec">FT_Incremental_MetricsRec</a> structure.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental_GetGlyphDataFunc">FT_Incremental_GetGlyphDataFunc</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <a href="ft2-basic_types.html#FT_Error">FT_Error</a>
  (*<b>FT_Incremental_GetGlyphDataFunc</b>)( <a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a>  incremental,
                                      <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>         glyph_index,
                                      <a href="ft2-basic_types.html#FT_Data">FT_Data</a>*        adata );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function called by FreeType to access a given glyph's data bytes during <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a> or <a href="ft2-base_interface.html#FT_Load_Char">FT_Load_Char</a> if incremental loading is enabled.</p>
<p>Note that the format of the glyph's data bytes depends on the font file format. For TrueType, it must correspond to the raw bytes within the &lsquo;glyf&rsquo; table. For PostScript formats, it must correspond to the <b>unencrypted</b> charstring bytes, without any &lsquo;lenIV&rsquo; header. It is undefined for any other format.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>incremental</b></td><td>
<p>Handle to an opaque <a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a> handle provided by the client application.</p>
</td></tr>
<tr valign=top><td><b>glyph_index</b></td><td>
<p>Index of relevant glyph.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>adata</b></td><td>
<p>A structure describing the returned glyph data bytes (which will be accessed as a read-only byte block).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If this function returns successfully the method <a href="ft2-incremental.html#FT_Incremental_FreeGlyphDataFunc">FT_Incremental_FreeGlyphDataFunc</a> will be called later to release the data bytes.</p>
<p>Nested calls to <a href="ft2-incremental.html#FT_Incremental_GetGlyphDataFunc">FT_Incremental_GetGlyphDataFunc</a> can happen for compound glyphs.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental_FreeGlyphDataFunc">FT_Incremental_FreeGlyphDataFunc</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Incremental_FreeGlyphDataFunc</b>)( <a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a>  incremental,
                                       <a href="ft2-basic_types.html#FT_Data">FT_Data</a>*        data );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function used to release the glyph data bytes returned by a successful call to <a href="ft2-incremental.html#FT_Incremental_GetGlyphDataFunc">FT_Incremental_GetGlyphDataFunc</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>incremental</b></td><td>
<p>A handle to an opaque <a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a> handle provided by the client application.</p>
</td></tr>
<tr valign=top><td><b>data</b></td><td>
<p>A structure describing the glyph data bytes (which will be accessed as a read-only byte block).</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental_GetGlyphMetricsFunc">FT_Incremental_GetGlyphMetricsFunc</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <a href="ft2-basic_types.html#FT_Error">FT_Error</a>
  (*<b>FT_Incremental_GetGlyphMetricsFunc</b>)
                      ( <a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a>              incremental,
                        <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>                     glyph_index,
                        <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>                     vertical,
                        <a href="ft2-incremental.html#FT_Incremental_MetricsRec">FT_Incremental_MetricsRec</a>  *ametrics );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function used to retrieve the basic metrics of a given glyph index before accessing its data. This is necessary because, in certain formats like TrueType, the metrics are stored in a different place from the glyph images proper.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>incremental</b></td><td>
<p>A handle to an opaque <a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a> handle provided by the client application.</p>
</td></tr>
<tr valign=top><td><b>glyph_index</b></td><td>
<p>Index of relevant glyph.</p>
</td></tr>
<tr valign=top><td><b>vertical</b></td><td>
<p>If true, return vertical metrics.</p>
</td></tr>
<tr valign=top><td><b>ametrics</b></td><td>
<p>This parameter is used for both input and output. The original glyph metrics, if any, in font units. If metrics are not available all the values must be set to zero.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>ametrics</b></td><td>
<p>The replacement glyph metrics in font units.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental_FuncsRec">FT_Incremental_FuncsRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Incremental_FuncsRec_
  {
    <a href="ft2-incremental.html#FT_Incremental_GetGlyphDataFunc">FT_Incremental_GetGlyphDataFunc</a>     get_glyph_data;
    <a href="ft2-incremental.html#FT_Incremental_FreeGlyphDataFunc">FT_Incremental_FreeGlyphDataFunc</a>    free_glyph_data;
    <a href="ft2-incremental.html#FT_Incremental_GetGlyphMetricsFunc">FT_Incremental_GetGlyphMetricsFunc</a>  get_glyph_metrics;

  } <b>FT_Incremental_FuncsRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A table of functions for accessing fonts that load data incrementally. Used in <a href="ft2-incremental.html#FT_Incremental_InterfaceRec">FT_Incremental_InterfaceRec</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>get_glyph_data</b></td><td>
<p>The function to get glyph data. Must not be null.</p>
</td></tr>
<tr valign=top><td><b>free_glyph_data</b></td><td>
<p>The function to release glyph data. Must not be null.</p>
</td></tr>
<tr valign=top><td><b>get_glyph_metrics</b></td><td>
<p>The function to get glyph metrics. May be null if the font does not provide overriding glyph metrics.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental_InterfaceRec">FT_Incremental_InterfaceRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Incremental_InterfaceRec_
  {
    <span class="keyword">const</span> <a href="ft2-incremental.html#FT_Incremental_FuncsRec">FT_Incremental_FuncsRec</a>*  funcs;
    <a href="ft2-incremental.html#FT_Incremental">FT_Incremental</a>                  object;

  } <b>FT_Incremental_InterfaceRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure to be used with <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> to indicate that the user wants to support incremental glyph loading. You should use it with <a href="ft2-incremental.html#FT_PARAM_TAG_INCREMENTAL">FT_PARAM_TAG_INCREMENTAL</a> as in the following example:</p>
<pre class="colored">
  FT_Incremental_InterfaceRec  inc_int;
  FT_Parameter                 parameter;
  FT_Open_Args                 open_args;


  // set up incremental descriptor
  inc_int.funcs  = my_funcs;
  inc_int.object = my_object;

  // set up optional parameter
  parameter.tag  = FT_PARAM_TAG_INCREMENTAL;
  parameter.data = &amp;inc_int;

  // set up FT_Open_Args structure
  open_args.flags      = FT_OPEN_PATHNAME | FT_OPEN_PARAMS;
  open_args.pathname   = my_font_pathname;
  open_args.num_params = 1;
  open_args.params     = &amp;parameter; // we use one optional argument

  // open the font
  error = FT_Open_Face( library, &amp;open_args, index, &amp;face );
  ...
</pre>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Incremental_Interface">FT_Incremental_Interface</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <a href="ft2-incremental.html#FT_Incremental_InterfaceRec">FT_Incremental_InterfaceRec</a>*   <b>FT_Incremental_Interface</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A pointer to an <a href="ft2-incremental.html#FT_Incremental_InterfaceRec">FT_Incremental_InterfaceRec</a> structure.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_PARAM_TAG_INCREMENTAL">FT_PARAM_TAG_INCREMENTAL</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_INCREMENTAL_H (ftincrem.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_PARAM_TAG_INCREMENTAL</b>  <a href="ft2-basic_types.html#FT_MAKE_TAG">FT_MAKE_TAG</a>( 'i', 'n', 'c', 'r' )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A constant used as the tag of <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a> structures to indicate an incremental loading object to be used by FreeType.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
