<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
Outline Processing
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_Outline">FT_Outline</a></td><td></td><td><a href="#ft_outline_flags">ft_outline_flags</a></td></tr>
<tr><td></td><td><a href="#FT_OUTLINE_FLAGS">FT_OUTLINE_FLAGS</a></td><td></td><td><a href="#FT_Outline_MoveToFunc">FT_Outline_MoveToFunc</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_New">FT_Outline_New</a></td><td></td><td><a href="#FT_Outline_LineToFunc">FT_Outline_LineToFunc</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_Done">FT_Outline_Done</a></td><td></td><td><a href="#FT_Outline_ConicToFunc">FT_Outline_ConicToFunc</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_Copy">FT_Outline_Copy</a></td><td></td><td><a href="#FT_Outline_CubicToFunc">FT_Outline_CubicToFunc</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_Translate">FT_Outline_Translate</a></td><td></td><td><a href="#FT_Outline_Funcs">FT_Outline_Funcs</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_Transform">FT_Outline_Transform</a></td><td></td><td><a href="#FT_Outline_Decompose">FT_Outline_Decompose</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_Embolden">FT_Outline_Embolden</a></td><td></td><td><a href="#FT_Outline_Get_CBox">FT_Outline_Get_CBox</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_EmboldenXY">FT_Outline_EmboldenXY</a></td><td></td><td><a href="#FT_Outline_Get_Bitmap">FT_Outline_Get_Bitmap</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_Reverse">FT_Outline_Reverse</a></td><td></td><td><a href="#FT_Outline_Render">FT_Outline_Render</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_Check">FT_Outline_Check</a></td><td></td><td><a href="#FT_Orientation">FT_Orientation</a></td></tr>
<tr><td></td><td><a href="#FT_Outline_Get_BBox">FT_Outline_Get_BBox</a></td><td></td><td><a href="#FT_Outline_Get_Orientation">FT_Outline_Get_Orientation</a></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>This section contains routines used to create and destroy scalable glyph images known as &lsquo;outlines&rsquo;. These can also be measured, transformed, and converted into bitmaps and pixmaps.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline">FT_Outline</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_IMAGE_H (ftimage.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Outline_
  {
    <span class="keyword">short</span>       n_contours;      /* number of contours in glyph        */
    <span class="keyword">short</span>       n_points;        /* number of points in the glyph      */

    <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  points;          /* the outline's points               */
    <span class="keyword">char</span>*       tags;            /* the points flags                   */
    <span class="keyword">short</span>*      contours;        /* the contour end points             */

    <span class="keyword">int</span>         flags;           /* outline masks                      */

  } <b>FT_Outline</b>;

  /* Following limits must be consistent with */
  /* <b>FT_Outline</b>.{n_contours,n_points}         */
#define FT_OUTLINE_CONTOURS_MAX  SHRT_MAX
#define FT_OUTLINE_POINTS_MAX    SHRT_MAX

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This structure is used to describe an outline to the scan-line converter.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>n_contours</b></td><td>
<p>The number of contours in the outline.</p>
</td></tr>
<tr valign=top><td><b>n_points</b></td><td>
<p>The number of points in the outline.</p>
</td></tr>
<tr valign=top><td><b>points</b></td><td>
<p>A pointer to an array of &lsquo;n_points&rsquo; <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a> elements, giving the outline's point coordinates.</p>
</td></tr>
<tr valign=top><td><b>tags</b></td><td>
<p>A pointer to an array of &lsquo;n_points&rsquo; chars, giving each outline point's type.</p>
<p>If bit&nbsp;0 is unset, the point is &lsquo;off&rsquo; the curve, i.e., a Bézier control point, while it is &lsquo;on&rsquo; if set.</p>
<p>Bit&nbsp;1 is meaningful for &lsquo;off&rsquo; points only. If set, it indicates a third-order Bézier arc control point; and a second-order control point if unset.</p>
<p>If bit&nbsp;2 is set, bits 5-7 contain the drop-out mode (as defined in the OpenType specification; the value is the same as the argument to the SCANMODE instruction).</p>
<p>Bits 3 and&nbsp;4 are reserved for internal purposes.</p>
</td></tr>
<tr valign=top><td><b>contours</b></td><td>
<p>An array of &lsquo;n_contours&rsquo; shorts, giving the end point of each contour within the outline. For example, the first contour is defined by the points &lsquo;0&rsquo; to &lsquo;contours[0]&rsquo;, the second one is defined by the points &lsquo;contours[0]+1&rsquo; to &lsquo;contours[1]&rsquo;, etc.</p>
</td></tr>
<tr valign=top><td><b>flags</b></td><td>
<p>A set of bit flags used to characterize the outline and give hints to the scan-converter and hinter on how to convert/grid-fit it. See <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_FLAGS</a>.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The B/W rasterizer only checks bit&nbsp;2 in the &lsquo;tags&rsquo; array for the first point of each contour. The drop-out mode as given with <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_IGNORE_DROPOUTS</a>, <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_SMART_DROPOUTS</a>, and <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_INCLUDE_STUBS</a> in &lsquo;flags&rsquo; is then overridden.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_OUTLINE_FLAGS">FT_OUTLINE_FLAGS</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_IMAGE_H (ftimage.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_NONE</a>             0x0
#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_OWNER</a>            0x1
#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_EVEN_ODD_FILL</a>    0x2
#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_REVERSE_FILL</a>     0x4
#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_IGNORE_DROPOUTS</a>  0x8
#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_SMART_DROPOUTS</a>   0x10
#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_INCLUDE_STUBS</a>    0x20

#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_HIGH_PRECISION</a>   0x100
#define <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_SINGLE_PASS</a>      0x200

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of bit-field constants use for the flags in an outline's &lsquo;flags&rsquo; field.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_OUTLINE_NONE</b></td><td>
<p>Value&nbsp;0 is reserved.</p>
</td></tr>
<tr valign=top><td><b>FT_OUTLINE_OWNER</b></td><td>
<p>If set, this flag indicates that the outline's field arrays (i.e., &lsquo;points&rsquo;, &lsquo;flags&rsquo;, and &lsquo;contours&rsquo;) are &lsquo;owned&rsquo; by the outline object, and should thus be freed when it is destroyed.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_OUTLINE_EVEN_ODD_FILL</b></td></tr>
<tr valign=top><td></td><td>
<p>By default, outlines are filled using the non-zero winding rule. If set to 1, the outline will be filled using the even-odd fill rule (only works with the smooth rasterizer).</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_OUTLINE_REVERSE_FILL</b></td></tr>
<tr valign=top><td></td><td>
<p>By default, outside contours of an outline are oriented in clock-wise direction, as defined in the TrueType specification. This flag is set if the outline uses the opposite direction (typically for Type&nbsp;1 fonts). This flag is ignored by the scan converter.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_OUTLINE_IGNORE_DROPOUTS</b></td></tr>
<tr valign=top><td></td><td>
<p>By default, the scan converter will try to detect drop-outs in an outline and correct the glyph bitmap to ensure consistent shape continuity. If set, this flag hints the scan-line converter to ignore such cases. See below for more information.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_OUTLINE_SMART_DROPOUTS</b></td></tr>
<tr valign=top><td></td><td>
<p>Select smart dropout control. If unset, use simple dropout control. Ignored if <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_IGNORE_DROPOUTS</a> is set. See below for more information.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_OUTLINE_INCLUDE_STUBS</b></td></tr>
<tr valign=top><td></td><td>
<p>If set, turn pixels on for &lsquo;stubs&rsquo;, otherwise exclude them. Ignored if <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_IGNORE_DROPOUTS</a> is set. See below for more information.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_OUTLINE_HIGH_PRECISION</b></td></tr>
<tr valign=top><td></td><td>
<p>This flag indicates that the scan-line converter should try to convert this outline to bitmaps with the highest possible quality. It is typically set for small character sizes. Note that this is only a hint that might be completely ignored by a given scan-converter.</p>
</td></tr>
<tr valign=top><td><b>FT_OUTLINE_SINGLE_PASS</b></td><td>
<p>This flag is set to force a given scan-converter to only use a single pass over the outline to render a bitmap glyph image. Normally, it is set for very large character sizes. It is only a hint that might be completely ignored by a given scan-converter.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The flags <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_IGNORE_DROPOUTS</a>, <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_SMART_DROPOUTS</a>, and <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_INCLUDE_STUBS</a> are ignored by the smooth rasterizer.</p>
<p>There exists a second mechanism to pass the drop-out mode to the B/W rasterizer; see the &lsquo;tags&rsquo; field in <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>.</p>
<p>Please refer to the description of the &lsquo;SCANTYPE&rsquo; instruction in the OpenType specification (in file &lsquo;ttinst1.doc&rsquo;) how simple drop-outs, smart drop-outs, and stubs are defined.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_New">FT_Outline_New</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_New</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>   library,
                  <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>      numPoints,
                  <a href="ft2-basic_types.html#FT_Int">FT_Int</a>       numContours,
                  <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>  *anoutline );


  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  FT_Outline_New_Internal( <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>    memory,
                           <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>      numPoints,
                           <a href="ft2-basic_types.html#FT_Int">FT_Int</a>       numContours,
                           <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>  *anoutline );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Create a new outline of a given size.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library object from where the outline is allocated. Note however that the new outline will <b>not</b> necessarily be <b>freed</b>, when destroying the library, by <a href="ft2-base_interface.html#FT_Done_FreeType">FT_Done_FreeType</a>.</p>
</td></tr>
<tr valign=top><td><b>numPoints</b></td><td>
<p>The maximum number of points within the outline. Must be smaller than or equal to 0xFFFF (65535).</p>
</td></tr>
<tr valign=top><td><b>numContours</b></td><td>
<p>The maximum number of contours within the outline. This value must be in the range 0 to &lsquo;numPoints&rsquo;.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>anoutline</b></td><td>
<p>A handle to the new outline.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The reason why this function takes a &lsquo;library&rsquo; parameter is simply to use the library's memory allocator.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Done">FT_Outline_Done</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_Done</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>   library,
                   <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline );


  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  FT_Outline_Done_Internal( <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>    memory,
                            <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Destroy an outline created with <a href="ft2-outline_processing.html#FT_Outline_New">FT_Outline_New</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle of the library object used to allocate the outline.</p>
</td></tr>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the outline object to be discarded.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If the outline's &lsquo;owner&rsquo; field is not set, only the outline descriptor will be released.</p>
<p>The reason why this function takes an &lsquo;library&rsquo; parameter is simply to use ft_mem_free().</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Copy">FT_Outline_Copy</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_Copy</b>( <span class="keyword">const</span> <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  source,
                   <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>        *target );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Copy an outline into another one. Both objects must have the same sizes (number of points &amp; number of contours) when this function is called.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>source</b></td><td>
<p>A handle to the source outline.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>target</b></td><td>
<p>A handle to the target outline.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Translate">FT_Outline_Translate</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Outline_Translate</b>( <span class="keyword">const</span> <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline,
                        <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>             xOffset,
                        <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>             yOffset );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Apply a simple translation to the points of an outline.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the target outline descriptor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>xOffset</b></td><td>
<p>The horizontal offset.</p>
</td></tr>
<tr valign=top><td><b>yOffset</b></td><td>
<p>The vertical offset.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Transform">FT_Outline_Transform</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Outline_Transform</b>( <span class="keyword">const</span> <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline,
                        <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>*   matrix );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Apply a simple 2x2 matrix to all of an outline's points. Useful for applying rotations, slanting, flipping, etc.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the target outline descriptor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>matrix</b></td><td>
<p>A pointer to the transformation matrix.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>You can use <a href="ft2-outline_processing.html#FT_Outline_Translate">FT_Outline_Translate</a> if you need to translate the outline's points.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Embolden">FT_Outline_Embolden</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_Embolden</b>( <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline,
                       <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>       strength );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Embolden an outline. The new outline will be at most 4&nbsp;times &lsquo;strength&rsquo; pixels wider and higher. You may think of the left and bottom borders as unchanged.</p>
<p>Negative &lsquo;strength&rsquo; values to reduce the outline thickness are possible also.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A handle to the target outline.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>strength</b></td><td>
<p>How strong the glyph is emboldened. Expressed in 26.6 pixel format.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The used algorithm to increase or decrease the thickness of the glyph doesn't change the number of points; this means that certain situations like acute angles or intersections are sometimes handled incorrectly.</p>
<p>If you need &lsquo;better&rsquo; metrics values you should call <a href="ft2-outline_processing.html#FT_Outline_Get_CBox">FT_Outline_Get_CBox</a> or <a href="ft2-outline_processing.html#FT_Outline_Get_BBox">FT_Outline_Get_BBox</a>.</p>
<p>Example call:</p>
<pre class="colored">
  FT_Load_Glyph( face, index, FT_LOAD_DEFAULT );                   
  if ( face-&gt;slot-&gt;format == FT_GLYPH_FORMAT_OUTLINE )             
    FT_Outline_Embolden( &amp;face-&gt;slot-&gt;outline, strength );         
</pre>
<p>To get meaningful results, font scaling values must be set with functions like <a href="ft2-base_interface.html#FT_Set_Char_Size">FT_Set_Char_Size</a> before calling FT_Render_Glyph.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_EmboldenXY">FT_Outline_EmboldenXY</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_EmboldenXY</b>( <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline,
                         <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>       xstrength,
                         <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>       ystrength );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Embolden an outline. The new outline will be &lsquo;xstrength&rsquo; pixels wider and &lsquo;ystrength&rsquo; pixels higher. Otherwise, it is similar to <a href="ft2-outline_processing.html#FT_Outline_Embolden">FT_Outline_Embolden</a>, which uses the same strength in both directions.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Reverse">FT_Outline_Reverse</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Outline_Reverse</b>( <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Reverse the drawing direction of an outline. This is used to ensure consistent fill conventions for mirrored glyphs.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the target outline descriptor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function toggles the bit flag <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_REVERSE_FILL</a> in the outline's &lsquo;flags&rsquo; field.</p>
<p>It shouldn't be used by a normal client application, unless it knows what it is doing.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Check">FT_Outline_Check</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_Check</b>( <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Check the contents of an outline descriptor.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A handle to a source outline.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Get_BBox">FT_Outline_Get_BBox</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_BBOX_H (ftbbox.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_Get_BBox</b>( <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline,
                       <a href="ft2-basic_types.html#FT_BBox">FT_BBox</a>     *abbox );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Compute the exact bounding box of an outline. This is slower than computing the control box. However, it uses an advanced algorithm that returns <i>very</i> quickly when the two boxes coincide. Otherwise, the outline Bézier arcs are traversed to extract their extrema.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the source outline.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>abbox</b></td><td>
<p>The outline's exact bounding box.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If the font is tricky and the glyph has been loaded with <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a>, the resulting BBox is meaningless. To get reasonable values for the BBox it is necessary to load the glyph at a large ppem value (so that the hinting instructions can properly shift and scale the subglyphs), then extracting the BBox, which can be eventually converted back to font units.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="ft_outline_flags">ft_outline_flags</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_IMAGE_H (ftimage.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_none</a>             <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_NONE</a>
#define <a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_owner</a>            <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_OWNER</a>
#define <a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_even_odd_fill</a>    <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_EVEN_ODD_FILL</a>
#define <a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_reverse_fill</a>     <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_REVERSE_FILL</a>
#define <a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_ignore_dropouts</a>  <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_IGNORE_DROPOUTS</a>
#define <a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_high_precision</a>   <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_HIGH_PRECISION</a>
#define <a href="ft2-outline_processing.html#ft_outline_flags">ft_outline_single_pass</a>      <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_SINGLE_PASS</a>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>These constants are deprecated. Please use the corresponding <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_FLAGS</a> values.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>ft_outline_none</b></td><td>
<p>See <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_NONE</a>.</p>
</td></tr>
<tr valign=top><td><b>ft_outline_owner</b></td><td>
<p>See <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_OWNER</a>.</p>
</td></tr>
<tr valign=top><td colspan=0><b>ft_outline_even_odd_fill</b></td></tr>
<tr valign=top><td></td><td>
<p>See <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_EVEN_ODD_FILL</a>.</p>
</td></tr>
<tr valign=top><td colspan=0><b>ft_outline_reverse_fill</b></td></tr>
<tr valign=top><td></td><td>
<p>See <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_REVERSE_FILL</a>.</p>
</td></tr>
<tr valign=top><td colspan=0><b>ft_outline_ignore_dropouts</b></td></tr>
<tr valign=top><td></td><td>
<p>See <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_IGNORE_DROPOUTS</a>.</p>
</td></tr>
<tr valign=top><td colspan=0><b>ft_outline_high_precision</b></td></tr>
<tr valign=top><td></td><td>
<p>See <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_HIGH_PRECISION</a>.</p>
</td></tr>
<tr valign=top><td><b>ft_outline_single_pass</b></td><td>
<p>See <a href="ft2-outline_processing.html#FT_OUTLINE_FLAGS">FT_OUTLINE_SINGLE_PASS</a>.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_MoveToFunc">FT_Outline_MoveToFunc</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_IMAGE_H (ftimage.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Outline_MoveToFunc</b>)( <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  to,
                            <span class="keyword">void</span>*             user );

#define FT_Outline_MoveTo_Func  <b>FT_Outline_MoveToFunc</b>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function pointer type used to describe the signature of a &lsquo;move to&rsquo; function during outline walking/decomposition.</p>
<p>A &lsquo;move to&rsquo; is emitted to start a new contour in an outline.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>to</b></td><td>
<p>A pointer to the target point of the &lsquo;move to&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>user</b></td><td>
<p>A typeless pointer, which is passed from the caller of the decomposition function.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>Error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_LineToFunc">FT_Outline_LineToFunc</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_IMAGE_H (ftimage.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Outline_LineToFunc</b>)( <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  to,
                            <span class="keyword">void</span>*             user );

#define FT_Outline_LineTo_Func  <b>FT_Outline_LineToFunc</b>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function pointer type used to describe the signature of a &lsquo;line to&rsquo; function during outline walking/decomposition.</p>
<p>A &lsquo;line to&rsquo; is emitted to indicate a segment in the outline.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>to</b></td><td>
<p>A pointer to the target point of the &lsquo;line to&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>user</b></td><td>
<p>A typeless pointer, which is passed from the caller of the decomposition function.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>Error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_ConicToFunc">FT_Outline_ConicToFunc</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_IMAGE_H (ftimage.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Outline_ConicToFunc</b>)( <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  control,
                             <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  to,
                             <span class="keyword">void</span>*             user );

#define FT_Outline_ConicTo_Func  <b>FT_Outline_ConicToFunc</b>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function pointer type used to describe the signature of a &lsquo;conic to&rsquo; function during outline walking or decomposition.</p>
<p>A &lsquo;conic to&rsquo; is emitted to indicate a second-order Bézier arc in the outline.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>control</b></td><td>
<p>An intermediate control point between the last position and the new target in &lsquo;to&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>to</b></td><td>
<p>A pointer to the target end point of the conic arc.</p>
</td></tr>
<tr valign=top><td><b>user</b></td><td>
<p>A typeless pointer, which is passed from the caller of the decomposition function.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>Error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_CubicToFunc">FT_Outline_CubicToFunc</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_IMAGE_H (ftimage.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">int</span>
  (*<b>FT_Outline_CubicToFunc</b>)( <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  control1,
                             <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  control2,
                             <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  to,
                             <span class="keyword">void</span>*             user );

#define FT_Outline_CubicTo_Func  <b>FT_Outline_CubicToFunc</b>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function pointer type used to describe the signature of a &lsquo;cubic to&rsquo; function during outline walking or decomposition.</p>
<p>A &lsquo;cubic to&rsquo; is emitted to indicate a third-order Bézier arc.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>control1</b></td><td>
<p>A pointer to the first Bézier control point.</p>
</td></tr>
<tr valign=top><td><b>control2</b></td><td>
<p>A pointer to the second Bézier control point.</p>
</td></tr>
<tr valign=top><td><b>to</b></td><td>
<p>A pointer to the target end point.</p>
</td></tr>
<tr valign=top><td><b>user</b></td><td>
<p>A typeless pointer, which is passed from the caller of the decomposition function.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>Error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Funcs">FT_Outline_Funcs</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_IMAGE_H (ftimage.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Outline_Funcs_
  {
    <a href="ft2-outline_processing.html#FT_Outline_MoveToFunc">FT_Outline_MoveToFunc</a>   move_to;
    <a href="ft2-outline_processing.html#FT_Outline_LineToFunc">FT_Outline_LineToFunc</a>   line_to;
    <a href="ft2-outline_processing.html#FT_Outline_ConicToFunc">FT_Outline_ConicToFunc</a>  conic_to;
    <a href="ft2-outline_processing.html#FT_Outline_CubicToFunc">FT_Outline_CubicToFunc</a>  cubic_to;

    <span class="keyword">int</span>                     shift;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>                  delta;

  } <b>FT_Outline_Funcs</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure to hold various function pointers used during outline decomposition in order to emit segments, conic, and cubic Béziers.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>move_to</b></td><td>
<p>The &lsquo;move to&rsquo; emitter.</p>
</td></tr>
<tr valign=top><td><b>line_to</b></td><td>
<p>The segment emitter.</p>
</td></tr>
<tr valign=top><td><b>conic_to</b></td><td>
<p>The second-order Bézier arc emitter.</p>
</td></tr>
<tr valign=top><td><b>cubic_to</b></td><td>
<p>The third-order Bézier arc emitter.</p>
</td></tr>
<tr valign=top><td><b>shift</b></td><td>
<p>The shift that is applied to coordinates before they are sent to the emitter.</p>
</td></tr>
<tr valign=top><td><b>delta</b></td><td>
<p>The delta that is applied to coordinates before they are sent to the emitter, but after the shift.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The point coordinates sent to the emitters are the transformed version of the original coordinates (this is important for high accuracy during scan-conversion). The transformation is simple:</p>
<pre class="colored">
  x' = (x &lt;&lt; shift) - delta                                        
  y' = (x &lt;&lt; shift) - delta                                        
</pre>
<p>Set the values of &lsquo;shift&rsquo; and &lsquo;delta&rsquo; to&nbsp;0 to get the original point coordinates.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Decompose">FT_Outline_Decompose</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_Decompose</b>( <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*              outline,
                        <span class="keyword">const</span> <a href="ft2-outline_processing.html#FT_Outline_Funcs">FT_Outline_Funcs</a>*  func_interface,
                        <span class="keyword">void</span>*                    user );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Walk over an outline's structure to decompose it into individual segments and Bézier arcs. This function also emits &lsquo;move to&rsquo; operations to indicate the start of new contours in the outline.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the source target.</p>
</td></tr>
<tr valign=top><td><b>func_interface</b></td><td>
<p>A table of &lsquo;emitters&rsquo;, i.e., function pointers called during decomposition to indicate path operations.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>user</b></td><td>
<p>A typeless pointer that is passed to each emitter during the decomposition. It can be used to store the state during the decomposition.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>A contour that contains a single point only is represented by a &lsquo;move to&rsquo; operation followed by &lsquo;line to&rsquo; to the same point. In most cases, it is best to filter this out before using the outline for stroking purposes (otherwise it would result in a visible dot when round caps are used).</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Get_CBox">FT_Outline_Get_CBox</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Outline_Get_CBox</b>( <span class="keyword">const</span> <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline,
                       <a href="ft2-basic_types.html#FT_BBox">FT_BBox</a>           *acbox );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return an outline's &lsquo;control box&rsquo;. The control box encloses all the outline's points, including Bézier control points. Though it coincides with the exact bounding box for most glyphs, it can be slightly larger in some situations (like when rotating an outline that contains Bézier outside arcs).</p>
<p>Computing the control box is very fast, while getting the bounding box can take much more time as it needs to walk over all segments and arcs in the outline. To get the latter, you can use the &lsquo;ftbbox&rsquo; component, which is dedicated to this single task.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the source outline descriptor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>acbox</b></td><td>
<p>The outline's control box.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>See <a href="ft2-glyph_management.html#FT_Glyph_Get_CBox">FT_Glyph_Get_CBox</a> for a discussion of tricky fonts.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Get_Bitmap">FT_Outline_Get_Bitmap</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_Get_Bitmap</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>        library,
                         <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*       outline,
                         <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Bitmap">FT_Bitmap</a>  *abitmap );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Render an outline within a bitmap. The outline's image is simply OR-ed to the target bitmap.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to a FreeType library object.</p>
</td></tr>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the source outline descriptor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>abitmap</b></td><td>
<p>A pointer to the target bitmap descriptor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function does NOT CREATE the bitmap, it only renders an outline image within the one you pass to it! Consequently, the various fields in &lsquo;abitmap&rsquo; should be set accordingly.</p>
<p>It will use the raster corresponding to the default glyph format.</p>
<p>The value of the &lsquo;num_grays&rsquo; field in &lsquo;abitmap&rsquo; is ignored. If you select the gray-level rasterizer, and you want less than 256 gray levels, you have to use <a href="ft2-outline_processing.html#FT_Outline_Render">FT_Outline_Render</a> directly.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Render">FT_Outline_Render</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Outline_Render</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>         library,
                     <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*        outline,
                     <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a>*  params );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Render an outline within a bitmap using the current scan-convert. This function uses an <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a> structure as an argument, allowing advanced features like direct composition, translucency, etc.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to a FreeType library object.</p>
</td></tr>
<tr valign=top><td><b>outline</b></td><td>
<p>A pointer to the source outline descriptor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>params</b></td><td>
<p>A pointer to an <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a> structure used to describe the rendering operation.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>You should know what you are doing and how <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a> works to use this function.</p>
<p>The field &lsquo;params.source&rsquo; will be set to &lsquo;outline&rsquo; before the scan converter is called, which means that the value you give to it is actually ignored.</p>
<p>The gray-level rasterizer always uses 256 gray levels. If you want less gray levels, you have to provide your own span callback. See the <a href="ft2-raster.html#FT_RASTER_FLAG_XXX">FT_RASTER_FLAG_DIRECT</a> value of the &lsquo;flags&rsquo; field in the <a href="ft2-raster.html#FT_Raster_Params">FT_Raster_Params</a> structure for more details.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Orientation">FT_Orientation</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Orientation_
  {
    <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_TRUETYPE</a>   = 0,
    <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_POSTSCRIPT</a> = 1,
    <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_FILL_RIGHT</a> = <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_TRUETYPE</a>,
    <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_FILL_LEFT</a>  = <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_POSTSCRIPT</a>,
    <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_NONE</a>

  } <b>FT_Orientation</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of values used to describe an outline's contour orientation.</p>
<p>The TrueType and PostScript specifications use different conventions to determine whether outline contours should be filled or unfilled.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td colspan=0><b>FT_ORIENTATION_TRUETYPE</b></td></tr>
<tr valign=top><td></td><td>
<p>According to the TrueType specification, clockwise contours must be filled, and counter-clockwise ones must be unfilled.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ORIENTATION_POSTSCRIPT</b></td></tr>
<tr valign=top><td></td><td>
<p>According to the PostScript specification, counter-clockwise contours must be filled, and clockwise ones must be unfilled.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ORIENTATION_FILL_RIGHT</b></td></tr>
<tr valign=top><td></td><td>
<p>This is identical to <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_TRUETYPE</a>, but is used to remember that in TrueType, everything that is to the right of the drawing direction of a contour must be filled.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ORIENTATION_FILL_LEFT</b></td></tr>
<tr valign=top><td></td><td>
<p>This is identical to <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_POSTSCRIPT</a>, but is used to remember that in PostScript, everything that is to the left of the drawing direction of a contour must be filled.</p>
</td></tr>
<tr valign=top><td><b>FT_ORIENTATION_NONE</b></td><td>
<p>The orientation cannot be determined. That is, different parts of the glyph have different orientation.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Outline_Get_Orientation">FT_Outline_Get_Orientation</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_OUTLINE_H (ftoutln.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-outline_processing.html#FT_Orientation">FT_Orientation</a> )
  <b>FT_Outline_Get_Orientation</b>( <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>*  outline );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function analyzes a glyph outline and tries to compute its fill orientation (see <a href="ft2-outline_processing.html#FT_Orientation">FT_Orientation</a>). This is done by integrating the total area covered by the outline. The positive integral corresponds to the clockwise orientation and <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_POSTSCRIPT</a> is returned. The negative integral corresponds to the counter-clockwise orientation and <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_TRUETYPE</a> is returned.</p>
<p>Note that this will return <a href="ft2-outline_processing.html#FT_Orientation">FT_ORIENTATION_TRUETYPE</a> for empty outlines.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>outline</b></td><td>
<p>A handle to the source outline.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The orientation.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
