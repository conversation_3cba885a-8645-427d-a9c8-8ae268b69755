<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
Type 1 Tables
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#PS_FontInfoRec">PS_FontInfoRec</a></td><td></td><td><a href="#T1_Blend_Flags">T1_Blend_Flags</a></td><td></td><td><a href="#FT_Has_PS_Glyph_Names">FT_Has_PS_Glyph_Names</a></td></tr>
<tr><td></td><td><a href="#PS_FontInfo">PS_FontInfo</a></td><td></td><td><a href="#CID_FaceDictRec">CID_FaceDictRec</a></td><td></td><td><a href="#FT_Get_PS_Font_Info">FT_Get_PS_Font_Info</a></td></tr>
<tr><td></td><td><a href="#T1_FontInfo">T1_FontInfo</a></td><td></td><td><a href="#CID_FaceDict">CID_FaceDict</a></td><td></td><td><a href="#FT_Get_PS_Font_Private">FT_Get_PS_Font_Private</a></td></tr>
<tr><td></td><td><a href="#PS_PrivateRec">PS_PrivateRec</a></td><td></td><td><a href="#CID_FaceInfoRec">CID_FaceInfoRec</a></td><td></td><td><a href="#T1_EncodingType">T1_EncodingType</a></td></tr>
<tr><td></td><td><a href="#PS_Private">PS_Private</a></td><td></td><td><a href="#CID_FaceInfo">CID_FaceInfo</a></td><td></td><td><a href="#PS_Dict_Keys">PS_Dict_Keys</a></td></tr>
<tr><td></td><td><a href="#T1_Private">T1_Private</a></td><td></td><td><a href="#CID_Info">CID_Info</a></td><td></td><td><a href="#FT_Get_PS_Font_Value">FT_Get_PS_Font_Value</a></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>This section contains the definition of Type 1-specific tables, including structures related to other PostScript font formats.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="PS_FontInfoRec">PS_FontInfoRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  PS_FontInfoRec_
  {
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*  version;
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*  notice;
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*  full_name;
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*  family_name;
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*  weight;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>     italic_angle;
    <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>     is_fixed_pitch;
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>    underline_position;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>   underline_thickness;

  } <b>PS_FontInfoRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to model a Type&nbsp;1 or Type&nbsp;2 FontInfo dictionary. Note that for Multiple Master fonts, each instance has its own FontInfo dictionary.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="PS_FontInfo">PS_FontInfo</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> PS_FontInfoRec_*  <b>PS_FontInfo</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a <a href="ft2-type1_tables.html#PS_FontInfoRec">PS_FontInfoRec</a> structure.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="T1_FontInfo">T1_FontInfo</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <a href="ft2-type1_tables.html#PS_FontInfoRec">PS_FontInfoRec</a>  <b>T1_FontInfo</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This type is equivalent to <a href="ft2-type1_tables.html#PS_FontInfoRec">PS_FontInfoRec</a>. It is deprecated but kept to maintain source compatibility between various versions of FreeType.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="PS_PrivateRec">PS_PrivateRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  PS_PrivateRec_
  {
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     unique_id;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     lenIV;

    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    num_blue_values;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    num_other_blues;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    num_family_blues;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    num_family_other_blues;

    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   blue_values[14];
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   other_blues[10];

    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   family_blues      [14];
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   family_other_blues[10];

    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>   blue_scale;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     blue_shift;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     blue_fuzz;

    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  standard_width[1];
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  standard_height[1];

    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    num_snap_widths;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    num_snap_heights;
    <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>    force_bold;
    <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>    round_stem_up;

    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   snap_widths [13];  /* including std width  */
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   snap_heights[13];  /* including std height */

    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>   expansion_factor;

    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>    language_group;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>    password;

    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   min_feature[2];

  } <b>PS_PrivateRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to model a Type&nbsp;1 or Type&nbsp;2 private dictionary. Note that for Multiple Master fonts, each instance has its own Private dictionary.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="PS_Private">PS_Private</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> PS_PrivateRec_*  <b>PS_Private</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a <a href="ft2-type1_tables.html#PS_PrivateRec">PS_PrivateRec</a> structure.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="T1_Private">T1_Private</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <a href="ft2-type1_tables.html#PS_PrivateRec">PS_PrivateRec</a>  <b>T1_Private</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This type is equivalent to <a href="ft2-type1_tables.html#PS_PrivateRec">PS_PrivateRec</a>. It is deprecated but kept to maintain source compatibility between various versions of FreeType.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="T1_Blend_Flags">T1_Blend_Flags</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  T1_Blend_Flags_
  {
    /*# required fields in a FontInfo blend dictionary */
    T1_BLEND_UNDERLINE_POSITION = 0,
    T1_BLEND_UNDERLINE_THICKNESS,
    T1_BLEND_ITALIC_ANGLE,

    /*# required fields in a Private blend dictionary */
    T1_BLEND_BLUE_VALUES,
    T1_BLEND_OTHER_BLUES,
    T1_BLEND_STANDARD_WIDTH,
    T1_BLEND_STANDARD_HEIGHT,
    T1_BLEND_STEM_SNAP_WIDTHS,
    T1_BLEND_STEM_SNAP_HEIGHTS,
    T1_BLEND_BLUE_SCALE,
    T1_BLEND_BLUE_SHIFT,
    T1_BLEND_FAMILY_BLUES,
    T1_BLEND_FAMILY_OTHER_BLUES,
    T1_BLEND_FORCE_BOLD,

    /*# never remove */
    T1_BLEND_MAX

  } <b>T1_Blend_Flags</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A set of flags used to indicate which fields are present in a given blend dictionary (font info or private). Used to support Multiple Masters fonts.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="CID_FaceDictRec">CID_FaceDictRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  CID_FaceDictRec_
  {
    <a href="ft2-type1_tables.html#PS_PrivateRec">PS_PrivateRec</a>  private_dict;

    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>        len_buildchar;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>       forcebold_threshold;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>         stroke_width;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>       expansion_factor;

    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>        paint_type;
    <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>        font_type;
    <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>      font_matrix;
    <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>      font_offset;

    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>        num_subrs;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>       subrmap_offset;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>         sd_bytes;

  } <b>CID_FaceDictRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to represent data in a CID top-level dictionary.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="CID_FaceDict">CID_FaceDict</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> CID_FaceDictRec_*  <b>CID_FaceDict</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a <a href="ft2-type1_tables.html#CID_FaceDictRec">CID_FaceDictRec</a> structure.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="CID_FaceInfoRec">CID_FaceInfoRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  CID_FaceInfoRec_
  {
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*      cid_font_name;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>        cid_version;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          cid_font_type;

    <a href="ft2-basic_types.html#FT_String">FT_String</a>*      registry;
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*      ordering;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          supplement;

    <a href="ft2-type1_tables.html#PS_FontInfoRec">PS_FontInfoRec</a>  font_info;
    <a href="ft2-basic_types.html#FT_BBox">FT_BBox</a>         font_bbox;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>        uid_base;

    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          num_xuid;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>        xuid[16];

    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>        cidmap_offset;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          fd_bytes;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          gd_bytes;
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>        cid_count;

    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          num_dicts;
    <a href="ft2-type1_tables.html#CID_FaceDict">CID_FaceDict</a>    font_dicts;

    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>        data_offset;

  } <b>CID_FaceInfoRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to represent CID Face information.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="CID_FaceInfo">CID_FaceInfo</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> CID_FaceInfoRec_*  <b>CID_FaceInfo</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a <a href="ft2-type1_tables.html#CID_FaceInfoRec">CID_FaceInfoRec</a> structure.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="CID_Info">CID_Info</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <a href="ft2-type1_tables.html#CID_FaceInfoRec">CID_FaceInfoRec</a>  <b>CID_Info</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This type is equivalent to <a href="ft2-type1_tables.html#CID_FaceInfoRec">CID_FaceInfoRec</a>. It is deprecated but kept to maintain source compatibility between various versions of FreeType.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Has_PS_Glyph_Names">FT_Has_PS_Glyph_Names</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Int">FT_Int</a> )
  <b>FT_Has_PS_Glyph_Names</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return true if a given face provides reliable PostScript glyph names. This is similar to using the <a href="ft2-base_interface.html#FT_HAS_GLYPH_NAMES">FT_HAS_GLYPH_NAMES</a> macro, except that certain fonts (mostly TrueType) contain incorrect glyph name tables.</p>
<p>When this function returns true, the caller is sure that the glyph names returned by <a href="ft2-base_interface.html#FT_Get_Glyph_Name">FT_Get_Glyph_Name</a> are reliable.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>face handle</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>Boolean. True if glyph names are reliable.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_PS_Font_Info">FT_Get_PS_Font_Info</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_PS_Font_Info</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>      face,
                       <a href="ft2-type1_tables.html#PS_FontInfo">PS_FontInfo</a>  afont_info );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the <a href="ft2-type1_tables.html#PS_FontInfoRec">PS_FontInfoRec</a> structure corresponding to a given PostScript font.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>PostScript face handle.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>afont_info</b></td><td>
<p>Output font info structure pointer.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The string pointers within the font info structure are owned by the face and don't need to be freed by the caller.</p>
<p>If the font's format is not PostScript-based, this function will return the &lsquo;FT_Err_Invalid_Argument&rsquo; error code.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_PS_Font_Private">FT_Get_PS_Font_Private</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_PS_Font_Private</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>     face,
                          <a href="ft2-type1_tables.html#PS_Private">PS_Private</a>  afont_private );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the <a href="ft2-type1_tables.html#PS_PrivateRec">PS_PrivateRec</a> structure corresponding to a given PostScript font.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>PostScript face handle.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>afont_private</b></td><td>
<p>Output private dictionary structure pointer.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The string pointers within the <a href="ft2-type1_tables.html#PS_PrivateRec">PS_PrivateRec</a> structure are owned by the face and don't need to be freed by the caller.</p>
<p>If the font's format is not PostScript-based, this function returns the &lsquo;FT_Err_Invalid_Argument&rsquo; error code.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="T1_EncodingType">T1_EncodingType</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  T1_EncodingType_
  {
    T1_ENCODING_TYPE_NONE = 0,
    T1_ENCODING_TYPE_ARRAY,
    T1_ENCODING_TYPE_STANDARD,
    T1_ENCODING_TYPE_ISOLATIN1,
    T1_ENCODING_TYPE_EXPERT

  } <b>T1_EncodingType</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An enumeration describing the &lsquo;Encoding&rsquo; entry in a Type 1 dictionary.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="PS_Dict_Keys">PS_Dict_Keys</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  PS_Dict_Keys_
  {
    /* conventionally in the font dictionary */
    PS_DICT_FONT_TYPE,              /* <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>         */
    PS_DICT_FONT_MATRIX,            /* <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>        */
    PS_DICT_FONT_BBOX,              /* <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>        */
    PS_DICT_PAINT_TYPE,             /* <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>         */
    PS_DICT_FONT_NAME,              /* <a href="ft2-basic_types.html#FT_String">FT_String</a>*      */
    PS_DICT_UNIQUE_ID,              /* <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          */
    PS_DICT_NUM_CHAR_STRINGS,       /* <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          */
    PS_DICT_CHAR_STRING_KEY,        /* <a href="ft2-basic_types.html#FT_String">FT_String</a>*      */
    PS_DICT_CHAR_STRING,            /* <a href="ft2-basic_types.html#FT_String">FT_String</a>*      */
    PS_DICT_ENCODING_TYPE,          /* <a href="ft2-type1_tables.html#T1_EncodingType">T1_EncodingType</a> */
    PS_DICT_ENCODING_ENTRY,         /* <a href="ft2-basic_types.html#FT_String">FT_String</a>*      */

    /* conventionally in the font Private dictionary */
    PS_DICT_NUM_SUBRS,              /* <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     */
    PS_DICT_SUBR,                   /* <a href="ft2-basic_types.html#FT_String">FT_String</a>* */
    PS_DICT_STD_HW,                 /* <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  */
    PS_DICT_STD_VW,                 /* <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  */
    PS_DICT_NUM_BLUE_VALUES,        /* <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    */
    PS_DICT_BLUE_VALUE,             /* <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   */
    PS_DICT_BLUE_FUZZ,              /* <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     */
    PS_DICT_NUM_OTHER_BLUES,        /* <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    */
    PS_DICT_OTHER_BLUE,             /* <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   */
    PS_DICT_NUM_FAMILY_BLUES,       /* <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    */
    PS_DICT_FAMILY_BLUE,            /* <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   */
    PS_DICT_NUM_FAMILY_OTHER_BLUES, /* <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    */
    PS_DICT_FAMILY_OTHER_BLUE,      /* <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   */
    PS_DICT_BLUE_SCALE,             /* <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>   */
    PS_DICT_BLUE_SHIFT,             /* <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     */
    PS_DICT_NUM_STEM_SNAP_H,        /* <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    */
    PS_DICT_STEM_SNAP_H,            /* <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   */
    PS_DICT_NUM_STEM_SNAP_V,        /* <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>    */
    PS_DICT_STEM_SNAP_V,            /* <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   */
    PS_DICT_FORCE_BOLD,             /* <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>    */
    PS_DICT_RND_STEM_UP,            /* <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>    */
    PS_DICT_MIN_FEATURE,            /* <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   */
    PS_DICT_LEN_IV,                 /* <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     */
    PS_DICT_PASSWORD,               /* <a href="ft2-basic_types.html#FT_Long">FT_Long</a>    */
    PS_DICT_LANGUAGE_GROUP,         /* <a href="ft2-basic_types.html#FT_Long">FT_Long</a>    */

    /* conventionally in the font FontInfo dictionary */
    PS_DICT_VERSION,                /* <a href="ft2-basic_types.html#FT_String">FT_String</a>* */
    PS_DICT_NOTICE,                 /* <a href="ft2-basic_types.html#FT_String">FT_String</a>* */
    PS_DICT_FULL_NAME,              /* <a href="ft2-basic_types.html#FT_String">FT_String</a>* */
    PS_DICT_FAMILY_NAME,            /* <a href="ft2-basic_types.html#FT_String">FT_String</a>* */
    PS_DICT_WEIGHT,                 /* <a href="ft2-basic_types.html#FT_String">FT_String</a>* */
    PS_DICT_IS_FIXED_PITCH,         /* <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>    */
    PS_DICT_UNDERLINE_POSITION,     /* <a href="ft2-basic_types.html#FT_Short">FT_Short</a>   */
    PS_DICT_UNDERLINE_THICKNESS,    /* <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  */
    PS_DICT_FS_TYPE,                /* <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  */
    PS_DICT_ITALIC_ANGLE,           /* <a href="ft2-basic_types.html#FT_Long">FT_Long</a>    */

    PS_DICT_MAX = PS_DICT_ITALIC_ANGLE

  } <b>PS_Dict_Keys</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An enumeration used in calls to <a href="ft2-type1_tables.html#FT_Get_PS_Font_Value">FT_Get_PS_Font_Value</a> to identify the Type&nbsp;1 dictionary entry to retrieve.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_PS_Font_Value">FT_Get_PS_Font_Value</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TYPE1_TABLES_H (t1tables.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Long">FT_Long</a> )
  <b>FT_Get_PS_Font_Value</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>       face,
                        <a href="ft2-type1_tables.html#PS_Dict_Keys">PS_Dict_Keys</a>  key,
                        <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>       idx,
                        <span class="keyword">void</span>         *value,
                        <a href="ft2-basic_types.html#FT_Long">FT_Long</a>       value_len );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the value for the supplied key from a PostScript font.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>PostScript face handle.</p>
</td></tr>
<tr valign=top><td><b>key</b></td><td>
<p>An enumeration value representing the dictionary key to retrieve.</p>
</td></tr>
<tr valign=top><td><b>idx</b></td><td>
<p>For array values, this specifies the index to be returned.</p>
</td></tr>
<tr valign=top><td><b>value</b></td><td>
<p>A pointer to memory into which to write the value.</p>
</td></tr>
<tr valign=top><td><b>valen_len</b></td><td>
<p>The size, in bytes, of the memory supplied for the value.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>value</b></td><td>
<p>The value matching the above key, if it exists.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The amount of memory (in bytes) required to hold the requested value (if it exists, -1 otherwise).</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The values returned are not pointers into the internal structures of the face, but are &lsquo;fresh&rsquo; copies, so that the memory containing them belongs to the calling application. This also enforces the &lsquo;read-only&rsquo; nature of these values, i.e., this function cannot be used to manipulate the face.</p>
<p>&lsquo;value&rsquo; is a void pointer because the values returned can be of various types.</p>
<p>If either &lsquo;value&rsquo; is NULL or &lsquo;value_len&rsquo; is too small, just the required memory size for the requested entry is returned.</p>
<p>The &lsquo;idx&rsquo; parameter is used, not only to retrieve elements of, for example, the FontMatrix or FontBBox, but also to retrieve name keys from the CharStrings dictionary, and the charstrings themselves. It is ignored for atomic values.</p>
<p>PS_DICT_BLUE_SCALE returns a value that is scaled up by 1000. To get the value as in the font stream, you need to divide by 65536000.0 (to remove the FT_Fixed scale, and the x1000 scale).</p>
<p>IMPORTANT: Only key/value pairs read by the FreeType interpreter can be retrieved. So, for example, PostScript procedures such as NP, ND, and RD are not available. Arbitrary keys are, obviously, not be available either.</p>
<p>If the font's format is not PostScript-based, this function returns the &lsquo;FT_Err_Invalid_Argument&rsquo; error code.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
