<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
Multiple Masters
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_MM_Axis">FT_MM_Axis</a></td><td></td><td><a href="#FT_Get_MM_Var">FT_Get_MM_Var</a></td></tr>
<tr><td></td><td><a href="#FT_Multi_Master">FT_Multi_Master</a></td><td></td><td><a href="#FT_Set_MM_Design_Coordinates">FT_Set_MM_Design_Coordinates</a></td></tr>
<tr><td></td><td><a href="#FT_Var_Axis">FT_Var_Axis</a></td><td></td><td><a href="#FT_Set_Var_Design_Coordinates">FT_Set_Var_Design_Coordinates</a></td></tr>
<tr><td></td><td><a href="#FT_Var_Named_Style">FT_Var_Named_Style</a></td><td></td><td><a href="#FT_Set_MM_Blend_Coordinates">FT_Set_MM_Blend_Coordinates</a></td></tr>
<tr><td></td><td><a href="#FT_MM_Var">FT_MM_Var</a></td><td></td><td><a href="#FT_Set_Var_Blend_Coordinates">FT_Set_Var_Blend_Coordinates</a></td></tr>
<tr><td></td><td><a href="#FT_Get_Multi_Master">FT_Get_Multi_Master</a></td><td></td><td></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>The following types and functions are used to manage Multiple Master fonts, i.e., the selection of specific design instances by setting design axis coordinates.</p>
<p>George Williams has extended this interface to make it work with both Type&nbsp;1 Multiple Masters fonts and GX distortable (var) fonts. Some of these routines only work with MM fonts, others will work with both types. They are similar enough that a consistent interface makes sense.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_MM_Axis">FT_MM_Axis</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_MM_Axis_
  {
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*  name;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>     minimum;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>     maximum;

  } <b>FT_MM_Axis</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A simple structure used to model a given axis in design space for Multiple Masters fonts.</p>
<p>This structure can't be used for GX var fonts.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>name</b></td><td>
<p>The axis's name.</p>
</td></tr>
<tr valign=top><td><b>minimum</b></td><td>
<p>The axis's minimum design coordinate.</p>
</td></tr>
<tr valign=top><td><b>maximum</b></td><td>
<p>The axis's maximum design coordinate.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Multi_Master">FT_Multi_Master</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Multi_Master_
  {
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     num_axis;
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     num_designs;
    <a href="ft2-multiple_masters.html#FT_MM_Axis">FT_MM_Axis</a>  axis[T1_MAX_MM_AXIS];

  } <b>FT_Multi_Master</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to model the axes and space of a Multiple Masters font.</p>
<p>This structure can't be used for GX var fonts.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>num_axis</b></td><td>
<p>Number of axes. Cannot exceed&nbsp;4.</p>
</td></tr>
<tr valign=top><td><b>num_designs</b></td><td>
<p>Number of designs; should be normally 2^num_axis even though the Type&nbsp;1 specification strangely allows for intermediate designs to be present. This number cannot exceed&nbsp;16.</p>
</td></tr>
<tr valign=top><td><b>axis</b></td><td>
<p>A table of axis descriptors.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Var_Axis">FT_Var_Axis</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Var_Axis_
  {
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*  name;

    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>    minimum;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>    def;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>    maximum;

    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>    tag;
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     strid;

  } <b>FT_Var_Axis</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A simple structure used to model a given axis in design space for Multiple Masters and GX var fonts.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>name</b></td><td>
<p>The axis's name. Not always meaningful for GX.</p>
</td></tr>
<tr valign=top><td><b>minimum</b></td><td>
<p>The axis's minimum design coordinate.</p>
</td></tr>
<tr valign=top><td><b>def</b></td><td>
<p>The axis's default design coordinate. FreeType computes meaningful default values for MM; it is then an integer value, not in 16.16 format.</p>
</td></tr>
<tr valign=top><td><b>maximum</b></td><td>
<p>The axis's maximum design coordinate.</p>
</td></tr>
<tr valign=top><td><b>tag</b></td><td>
<p>The axis's tag (the GX equivalent to &lsquo;name&rsquo;). FreeType provides default values for MM if possible.</p>
</td></tr>
<tr valign=top><td><b>strid</b></td><td>
<p>The entry in &lsquo;name&rsquo; table (another GX version of &lsquo;name&rsquo;). Not meaningful for MM.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Var_Named_Style">FT_Var_Named_Style</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Var_Named_Style_
  {
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>*  coords;
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>    strid;

  } <b>FT_Var_Named_Style</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A simple structure used to model a named style in a GX var font.</p>
<p>This structure can't be used for MM fonts.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>coords</b></td><td>
<p>The design coordinates for this style. This is an array with one entry for each axis.</p>
</td></tr>
<tr valign=top><td><b>strid</b></td><td>
<p>The entry in &lsquo;name&rsquo; table identifying this style.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_MM_Var">FT_MM_Var</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_MM_Var_
  {
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>              num_axis;
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>              num_designs;
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>              num_namedstyles;
    <a href="ft2-multiple_masters.html#FT_Var_Axis">FT_Var_Axis</a>*         axis;
    <a href="ft2-multiple_masters.html#FT_Var_Named_Style">FT_Var_Named_Style</a>*  namedstyle;

  } <b>FT_MM_Var</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to model the axes and space of a Multiple Masters or GX var distortable font.</p>
<p>Some fields are specific to one format and not to the other.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>num_axis</b></td><td>
<p>The number of axes. The maximum value is&nbsp;4 for MM; no limit in GX.</p>
</td></tr>
<tr valign=top><td><b>num_designs</b></td><td>
<p>The number of designs; should be normally 2^num_axis for MM fonts. Not meaningful for GX (where every glyph could have a different number of designs).</p>
</td></tr>
<tr valign=top><td><b>num_namedstyles</b></td><td>
<p>The number of named styles; only meaningful for GX that allows certain design coordinates to have a string ID (in the &lsquo;name&rsquo; table) associated with them. The font can tell the user that, for example, Weight=1.5 is &lsquo;Bold&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>axis</b></td><td>
<p>A table of axis descriptors. GX fonts contain slightly more data than MM.</p>
</td></tr>
<tr valign=top><td><b>namedstyles</b></td><td>
<p>A table of named styles. Only meaningful with GX.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Multi_Master">FT_Get_Multi_Master</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_Multi_Master</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>           face,
                       <a href="ft2-multiple_masters.html#FT_Multi_Master">FT_Multi_Master</a>  *amaster );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the Multiple Master descriptor of a given font.</p>
<p>This function can't be used with GX fonts.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>amaster</b></td><td>
<p>The Multiple Masters descriptor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_MM_Var">FT_Get_MM_Var</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_MM_Var</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>      face,
                 <a href="ft2-multiple_masters.html#FT_MM_Var">FT_MM_Var</a>*  *amaster );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the Multiple Master/GX var descriptor of a given font.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>amaster</b></td><td>
<p>The Multiple Masters/GX var descriptor. Allocates a data structure, which the user must free.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_MM_Design_Coordinates">FT_Set_MM_Design_Coordinates</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Set_MM_Design_Coordinates</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>   face,
                                <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>   num_coords,
                                <a href="ft2-basic_types.html#FT_Long">FT_Long</a>*  coords );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>For Multiple Masters fonts, choose an interpolated font design through design coordinates.</p>
<p>This function can't be used with GX fonts.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>num_coords</b></td><td>
<p>The number of design coordinates (must be equal to the number of axes in the font).</p>
</td></tr>
<tr valign=top><td><b>coords</b></td><td>
<p>An array of design coordinates.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_Var_Design_Coordinates">FT_Set_Var_Design_Coordinates</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Set_Var_Design_Coordinates</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>    face,
                                 <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>    num_coords,
                                 <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>*  coords );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>For Multiple Master or GX Var fonts, choose an interpolated font design through design coordinates.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>num_coords</b></td><td>
<p>The number of design coordinates (must be equal to the number of axes in the font).</p>
</td></tr>
<tr valign=top><td><b>coords</b></td><td>
<p>An array of design coordinates.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_MM_Blend_Coordinates">FT_Set_MM_Blend_Coordinates</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Set_MM_Blend_Coordinates</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>    face,
                               <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>    num_coords,
                               <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>*  coords );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>For Multiple Masters and GX var fonts, choose an interpolated font design through normalized blend coordinates.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>num_coords</b></td><td>
<p>The number of design coordinates (must be equal to the number of axes in the font).</p>
</td></tr>
<tr valign=top><td><b>coords</b></td><td>
<p>The design coordinates array (each element must be between 0 and 1.0).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_Var_Blend_Coordinates">FT_Set_Var_Blend_Coordinates</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MULTIPLE_MASTERS_H (ftmm.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Set_Var_Blend_Coordinates</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>    face,
                                <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>    num_coords,
                                <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>*  coords );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This is another name of <a href="ft2-multiple_masters.html#FT_Set_MM_Blend_Coordinates">FT_Set_MM_Blend_Coordinates</a>.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
