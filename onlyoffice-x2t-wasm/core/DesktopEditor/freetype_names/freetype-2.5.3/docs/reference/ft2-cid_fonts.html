<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
CID Fonts
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_Get_CID_Registry_Ordering_Supplement">FT_Get_CID_Registry_Ordering_Supplement</a></td></tr>
<tr><td></td><td><a href="#FT_Get_CID_Is_Internally_CID_Keyed">FT_Get_CID_Is_Internally_CID_Keyed</a></td></tr>
<tr><td></td><td><a href="#FT_Get_CID_From_Glyph_Index">FT_Get_CID_From_Glyph_Index</a></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>This section contains the declaration of CID-keyed font specific functions.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_CID_Registry_Ordering_Supplement">FT_Get_CID_Registry_Ordering_Supplement</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_CID_H (ftcid.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_CID_Registry_Ordering_Supplement</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>       face,
                                           <span class="keyword">const</span> <span class="keyword">char</span>*  *registry,
                                           <span class="keyword">const</span> <span class="keyword">char</span>*  *ordering,
                                           <a href="ft2-basic_types.html#FT_Int">FT_Int</a>       *supplement);

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the Registry/Ordering/Supplement triple (also known as the "R/O/S") from a CID-keyed font.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the input face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>registry</b></td><td>
<p>The registry, as a C&nbsp;string, owned by the face.</p>
</td></tr>
<tr valign=top><td><b>ordering</b></td><td>
<p>The ordering, as a C&nbsp;string, owned by the face.</p>
</td></tr>
<tr valign=top><td><b>supplement</b></td><td>
<p>The supplement.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function only works with CID faces, returning an error otherwise.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.3.6</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_CID_Is_Internally_CID_Keyed">FT_Get_CID_Is_Internally_CID_Keyed</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_CID_H (ftcid.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_CID_Is_Internally_CID_Keyed</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>   face,
                                      <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>  *is_cid );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the type of the input face, CID keyed or not. In constrast to the <a href="ft2-base_interface.html#FT_IS_CID_KEYED">FT_IS_CID_KEYED</a> macro this function returns successfully also for CID-keyed fonts in an SNFT wrapper.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the input face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>is_cid</b></td><td>
<p>The type of the face as an <a href="ft2-basic_types.html#FT_Bool">FT_Bool</a>.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function only works with CID faces and OpenType fonts, returning an error otherwise.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.3.9</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_CID_From_Glyph_Index">FT_Get_CID_From_Glyph_Index</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_CID_H (ftcid.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_CID_From_Glyph_Index</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>   face,
                               <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>   glyph_index,
                               <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>  *cid );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the CID of the input glyph index.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the input face.</p>
</td></tr>
<tr valign=top><td><b>glyph_index</b></td><td>
<p>The input glyph index.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>cid</b></td><td>
<p>The CID as an <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function only works with CID faces and OpenType fonts, returning an error otherwise.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.3.9</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
