<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
The TrueType Engine
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_TrueTypeEngineType">FT_TrueTypeEngineType</a></td><td></td><td><a href="#FT_Get_TrueType_Engine_Type">FT_Get_TrueType_Engine_Type</a></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>This section contains a function used to query the level of TrueType bytecode support compiled in this version of the library.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_TrueTypeEngineType">FT_TrueTypeEngineType</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_TrueTypeEngineType_
  {
    <a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TRUETYPE_ENGINE_TYPE_NONE</a> = 0,
    <a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TRUETYPE_ENGINE_TYPE_UNPATENTED</a>,
    <a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TRUETYPE_ENGINE_TYPE_PATENTED</a>

  } <b>FT_TrueTypeEngineType</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of values describing which kind of TrueType bytecode engine is implemented in a given FT_Library instance. It is used by the <a href="ft2-truetype_engine.html#FT_Get_TrueType_Engine_Type">FT_Get_TrueType_Engine_Type</a> function.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td colspan=0><b>FT_TRUETYPE_ENGINE_TYPE_NONE</b></td></tr>
<tr valign=top><td></td><td>
<p>The library doesn't implement any kind of bytecode interpreter.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_TRUETYPE_ENGINE_TYPE_UNPATENTED</b></td></tr>
<tr valign=top><td></td><td>
<p>The library implements a bytecode interpreter that doesn't support the patented operations of the TrueType virtual machine.</p>
<p>Its main use is to load certain Asian fonts that position and scale glyph components with bytecode instructions. It produces bad output for most other fonts.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_TRUETYPE_ENGINE_TYPE_PATENTED</b></td></tr>
<tr valign=top><td></td><td>
<p>The library implements a bytecode interpreter that covers the full instruction set of the TrueType virtual machine (this was governed by patents until May 2010, hence the name).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.2</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_TrueType_Engine_Type">FT_Get_TrueType_Engine_Type</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TrueTypeEngineType</a> )
  <b>FT_Get_TrueType_Engine_Type</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  library );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return an <a href="ft2-truetype_engine.html#FT_TrueTypeEngineType">FT_TrueTypeEngineType</a> value to indicate which level of the TrueType virtual machine a given library instance supports.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A library instance.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>A value indicating which level is supported.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.2</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
