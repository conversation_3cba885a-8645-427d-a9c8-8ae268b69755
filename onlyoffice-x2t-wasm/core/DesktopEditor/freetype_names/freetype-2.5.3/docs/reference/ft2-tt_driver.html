<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
The TrueType driver
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#interpreter-version">interpreter-version</a></td><td></td><td><a href="#TT_INTERPRETER_VERSION_XXX">TT_INTERPRETER_VERSION_XXX</a></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>While FreeType's TrueType driver doesn't expose API functions by itself, it is possible to control its behaviour with <a href="ft2-module_management.html#FT_Property_Set">FT_Property_Set</a> and <a href="ft2-module_management.html#FT_Property_Get">FT_Property_Get</a>. The following lists the available properties together with the necessary macros and structures.</p>
<p>The TrueType driver's module name is &lsquo;truetype&rsquo;.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="interpreter-version">interpreter-version</a></h4>
<table align=center width="87%"><tr><td>
<p>Currently, two versions are available, representing the bytecode interpreter with and without subpixel hinting support, respectively. The default is subpixel support if TT_CONFIG_OPTION_SUBPIXEL_HINTING is defined, and no subpixel support otherwise (since it isn't available then).</p>
<p>If subpixel hinting is on, many TrueType bytecode instructions behave differently compared to B/W or grayscale rendering. The main idea is to render at a much increased horizontal resolution, then sampling down the created output to subpixel precision. However, many older fonts are not suited to this and must be specially taken care of by applying (hardcoded) font-specific tweaks.</p>
<p>Details on subpixel hinting and some of the necessary tweaks can be found in Greg Hitchcock's whitepaper at &lsquo;<a href="http://www.microsoft.com/typography/cleartype/truetypecleartype.aspx">http://www.microsoft.com/typography/cleartype/truetypecleartype.aspx</a>&rsquo;.</p>
<p>The following example code demonstrates how to activate subpixel hinting (omitting the error handling).</p>
<pre class="colored">
  FT_Library  library;
  FT_Face     face;
  FT_UInt     interpreter_version = TT_INTERPRETER_VERSION_38;


  FT_Init_FreeType( &amp;library );

  FT_Property_Set( library, "truetype",
                            "interpreter-version",
                            &amp;interpreter_version );
</pre>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This property can be used with <a href="ft2-module_management.html#FT_Property_Get">FT_Property_Get</a> also.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="TT_INTERPRETER_VERSION_XXX">TT_INTERPRETER_VERSION_XXX</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRUETYPE_DRIVER_H (ftttdrv.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-tt_driver.html#TT_INTERPRETER_VERSION_XXX">TT_INTERPRETER_VERSION_35</a>  35
#define <a href="ft2-tt_driver.html#TT_INTERPRETER_VERSION_XXX">TT_INTERPRETER_VERSION_38</a>  38

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of constants used for the <a href="ft2-tt_driver.html#interpreter-version">interpreter-version</a> property to select the hinting engine for Truetype fonts.</p>
<p>The numeric value in the constant names represents the version number as returned by the &lsquo;GETINFO&rsquo; bytecode instruction.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td colspan=0><b>TT_INTERPRETER_VERSION_35</b></td></tr>
<tr valign=top><td></td><td>
<p>Version&nbsp;35 corresponds to MS rasterizer v.1.7 as used e.g. in Windows&nbsp;98; only grayscale and B/W rasterizing is supported.</p>
</td></tr>
<tr valign=top><td colspan=0><b>TT_INTERPRETER_VERSION_38</b></td></tr>
<tr valign=top><td></td><td>
<p>Version&nbsp;38 corresponds to MS rasterizer v.1.9; it is roughly equivalent to the hinting provided by DirectWrite ClearType (as can be found, for example, in the Internet Explorer&nbsp;9 running on Windows&nbsp;7).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This property controls the behaviour of the bytecode interpreter and thus how outlines get hinted. It does <b>not</b> control how glyph get rasterized! In particular, it does not control subpixel color filtering.</p>
<p>If FreeType has not been compiled with configuration option FT_CONFIG_OPTION_SUBPIXEL_HINTING, selecting version&nbsp;38 causes an &lsquo;FT_Err_Unimplemented_Feature&rsquo; error.</p>
<p>Depending on the graphics framework, Microsoft uses different bytecode engines. As a consequence, the version numbers returned by a call to the &lsquo;GETINFO[1]&rsquo; bytecode instruction are more convoluted than desired.</p>
<pre class="colored">
   framework   Windows version   result of GETINFO[1]
  ----------------------------------------------------
    GDI         before XP         35
    GDI         XP and later      37
    GDI+ old    before Vista      37
    GDI+ old    Vista, 7          38
    GDI+        after 7           40
    DWrite      before 8          39
    DWrite      8 and later       40
</pre>
<p>Since FreeType doesn't provide all capabilities of DWrite ClearType, using version&nbsp;38 seems justified.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
