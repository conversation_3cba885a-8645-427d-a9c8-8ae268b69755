<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
BDF and PCF Files
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_PropertyType">FT_PropertyType</a></td><td></td><td><a href="#BDF_PropertyRec">BDF_PropertyRec</a></td><td></td><td><a href="#FT_Get_BDF_Property">FT_Get_BDF_Property</a></td></tr>
<tr><td></td><td><a href="#BDF_Property">BDF_Property</a></td><td></td><td><a href="#FT_Get_BDF_Charset_ID">FT_Get_BDF_Charset_ID</a></td><td></td><td></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>This section contains the declaration of functions specific to BDF and PCF fonts.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_PropertyType">FT_PropertyType</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_BDF_H (ftbdf.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  BDF_PropertyType_
  {
    <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_NONE</a>     = 0,
    <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_ATOM</a>     = 1,
    <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_INTEGER</a>  = 2,
    <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_CARDINAL</a> = 3

  } BDF_PropertyType;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of BDF property types.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>BDF_PROPERTY_TYPE_NONE</b></td><td>
<p>Value&nbsp;0 is used to indicate a missing property.</p>
</td></tr>
<tr valign=top><td><b>BDF_PROPERTY_TYPE_ATOM</b></td><td>
<p>Property is a string atom.</p>
</td></tr>
<tr valign=top><td colspan=0><b>BDF_PROPERTY_TYPE_INTEGER</b></td></tr>
<tr valign=top><td></td><td>
<p>Property is a 32-bit signed integer.</p>
</td></tr>
<tr valign=top><td colspan=0><b>BDF_PROPERTY_TYPE_CARDINAL</b></td></tr>
<tr valign=top><td></td><td>
<p>Property is a 32-bit unsigned integer.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="BDF_Property">BDF_Property</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_BDF_H (ftbdf.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> BDF_PropertyRec_*  <b>BDF_Property</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a <a href="ft2-bdf_fonts.html#BDF_PropertyRec">BDF_PropertyRec</a> structure to model a given BDF/PCF property.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="BDF_PropertyRec">BDF_PropertyRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_BDF_H (ftbdf.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  BDF_PropertyRec_
  {
    BDF_PropertyType  type;
    <span class="keyword">union</span> {
      <span class="keyword">const</span> <span class="keyword">char</span>*     atom;
      <a href="ft2-basic_types.html#FT_Int32">FT_Int32</a>        integer;
      <a href="ft2-basic_types.html#FT_UInt32">FT_UInt32</a>       cardinal;

    } u;

  } <b>BDF_PropertyRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This structure models a given BDF/PCF property.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>type</b></td><td>
<p>The property type.</p>
</td></tr>
<tr valign=top><td><b>u.atom</b></td><td>
<p>The atom string, if type is <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_ATOM</a>. May be NULL, indicating an empty string.</p>
</td></tr>
<tr valign=top><td><b>u.integer</b></td><td>
<p>A signed integer, if type is <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_INTEGER</a>.</p>
</td></tr>
<tr valign=top><td><b>u.cardinal</b></td><td>
<p>An unsigned integer, if type is <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_CARDINAL</a>.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_BDF_Charset_ID">FT_Get_BDF_Charset_ID</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_BDF_H (ftbdf.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_BDF_Charset_ID</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>       face,
                         <span class="keyword">const</span> <span class="keyword">char</span>*  *acharset_encoding,
                         <span class="keyword">const</span> <span class="keyword">char</span>*  *acharset_registry );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve a BDF font character set identity, according to the BDF specification.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the input face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>acharset_encoding</b></td><td>
<p>Charset encoding, as a C&nbsp;string, owned by the face.</p>
</td></tr>
<tr valign=top><td><b>acharset_registry</b></td><td>
<p>Charset registry, as a C&nbsp;string, owned by the face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function only works with BDF faces, returning an error otherwise.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_BDF_Property">FT_Get_BDF_Property</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_BDF_H (ftbdf.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_BDF_Property</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>           face,
                       <span class="keyword">const</span> <span class="keyword">char</span>*       prop_name,
                       <a href="ft2-bdf_fonts.html#BDF_PropertyRec">BDF_PropertyRec</a>  *aproperty );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve a BDF property from a BDF or PCF font file.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the input face.</p>
</td></tr>
<tr valign=top><td><b>name</b></td><td>
<p>The property name.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>aproperty</b></td><td>
<p>The property.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function works with BDF <i>and</i> PCF fonts. It returns an error otherwise. It also returns an error if the property is not in the font.</p>
<p>A &lsquo;property&rsquo; is a either key-value pair within the STARTPROPERTIES ... ENDPROPERTIES block of a BDF font or a key-value pair from the &lsquo;info-&gt;props&rsquo; array within a &lsquo;FontRec&rsquo; structure of a PCF font.</p>
<p>Integer properties are always stored as &lsquo;signed&rsquo; within PCF fonts; consequently, <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_CARDINAL</a> is a possible return value for BDF fonts only.</p>
<p>In case of error, &lsquo;aproperty-&gt;type&rsquo; is always set to <a href="ft2-bdf_fonts.html#FT_PropertyType">BDF_PROPERTY_TYPE_NONE</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
