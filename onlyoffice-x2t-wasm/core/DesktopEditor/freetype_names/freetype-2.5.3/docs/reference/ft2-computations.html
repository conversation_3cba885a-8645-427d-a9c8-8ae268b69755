<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
Computations
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_MulDiv">FT_MulDiv</a></td><td></td><td><a href="#FT_Matrix_Invert">FT_Matrix_Invert</a></td><td></td><td><a href="#FT_Tan">FT_Tan</a></td></tr>
<tr><td></td><td><a href="#FT_MulFix">FT_MulFix</a></td><td></td><td><a href="#FT_Angle">FT_Angle</a></td><td></td><td><a href="#FT_Atan2">FT_Atan2</a></td></tr>
<tr><td></td><td><a href="#FT_DivFix">FT_DivFix</a></td><td></td><td><a href="#FT_ANGLE_PI">FT_ANGLE_PI</a></td><td></td><td><a href="#FT_Angle_Diff">FT_Angle_Diff</a></td></tr>
<tr><td></td><td><a href="#FT_RoundFix">FT_RoundFix</a></td><td></td><td><a href="#FT_ANGLE_2PI">FT_ANGLE_2PI</a></td><td></td><td><a href="#FT_Vector_Unit">FT_Vector_Unit</a></td></tr>
<tr><td></td><td><a href="#FT_CeilFix">FT_CeilFix</a></td><td></td><td><a href="#FT_ANGLE_PI2">FT_ANGLE_PI2</a></td><td></td><td><a href="#FT_Vector_Rotate">FT_Vector_Rotate</a></td></tr>
<tr><td></td><td><a href="#FT_FloorFix">FT_FloorFix</a></td><td></td><td><a href="#FT_ANGLE_PI4">FT_ANGLE_PI4</a></td><td></td><td><a href="#FT_Vector_Length">FT_Vector_Length</a></td></tr>
<tr><td></td><td><a href="#FT_Vector_Transform">FT_Vector_Transform</a></td><td></td><td><a href="#FT_Sin">FT_Sin</a></td><td></td><td><a href="#FT_Vector_Polarize">FT_Vector_Polarize</a></td></tr>
<tr><td></td><td><a href="#FT_Matrix_Multiply">FT_Matrix_Multiply</a></td><td></td><td><a href="#FT_Cos">FT_Cos</a></td><td></td><td><a href="#FT_Vector_From_Polar">FT_Vector_From_Polar</a></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>This section contains various functions used to perform computations on 16.16 fixed-float numbers or 2d vectors.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_MulDiv">FT_MulDiv</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Long">FT_Long</a> )
  <b>FT_MulDiv</b>( <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  a,
             <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  b,
             <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  c );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A very simple function used to perform the computation &lsquo;(a*b)/c&rsquo; with maximum accuracy (it uses a 64-bit intermediate integer whenever necessary).</p>
<p>This function isn't necessarily as fast as some processor specific operations, but is at least completely portable.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>a</b></td><td>
<p>The first multiplier.</p>
</td></tr>
<tr valign=top><td><b>b</b></td><td>
<p>The second multiplier.</p>
</td></tr>
<tr valign=top><td><b>c</b></td><td>
<p>The divisor.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The result of &lsquo;(a*b)/c&rsquo;. This function never traps when trying to divide by zero; it simply returns &lsquo;MaxInt&rsquo; or &lsquo;MinInt&rsquo; depending on the signs of &lsquo;a&rsquo; and &lsquo;b&rsquo;.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_MulFix">FT_MulFix</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Long">FT_Long</a> )
  <b>FT_MulFix</b>( <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  a,
             <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  b );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A very simple function used to perform the computation &lsquo;(a*b)/0x10000&rsquo; with maximum accuracy. Most of the time this is used to multiply a given value by a 16.16 fixed-point factor.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>a</b></td><td>
<p>The first multiplier.</p>
</td></tr>
<tr valign=top><td><b>b</b></td><td>
<p>The second multiplier. Use a 16.16 factor here whenever possible (see note below).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The result of &lsquo;(a*b)/0x10000&rsquo;.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function has been optimized for the case where the absolute value of &lsquo;a&rsquo; is less than 2048, and &lsquo;b&rsquo; is a 16.16 scaling factor. As this happens mainly when scaling from notional units to fractional pixels in FreeType, it resulted in noticeable speed improvements between versions 2.x and 1.x.</p>
<p>As a conclusion, always try to place a 16.16 factor as the <i>second</i> argument of this function; this can make a great difference.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_DivFix">FT_DivFix</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Long">FT_Long</a> )
  <b>FT_DivFix</b>( <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  a,
             <a href="ft2-basic_types.html#FT_Long">FT_Long</a>  b );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A very simple function used to perform the computation &lsquo;(a*0x10000)/b&rsquo; with maximum accuracy. Most of the time, this is used to divide a given value by a 16.16 fixed-point factor.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>a</b></td><td>
<p>The first multiplier.</p>
</td></tr>
<tr valign=top><td><b>b</b></td><td>
<p>The second multiplier. Use a 16.16 factor here whenever possible (see note below).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The result of &lsquo;(a*0x10000)/b&rsquo;.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The optimization for FT_DivFix() is simple: If (a&nbsp;&lt;&lt;&nbsp;16) fits in 32&nbsp;bits, then the division is computed directly. Otherwise, we use a specialized version of <a href="ft2-computations.html#FT_MulDiv">FT_MulDiv</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_RoundFix">FT_RoundFix</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a> )
  <b>FT_RoundFix</b>( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>  a );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A very simple function used to round a 16.16 fixed number.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>a</b></td><td>
<p>The number to be rounded.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The result of &lsquo;(a + 0x8000) &amp; -0x10000&rsquo;.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_CeilFix">FT_CeilFix</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a> )
  <b>FT_CeilFix</b>( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>  a );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A very simple function used to compute the ceiling function of a 16.16 fixed number.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>a</b></td><td>
<p>The number for which the ceiling function is to be computed.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The result of &lsquo;(a + 0x10000 - 1) &amp; -0x10000&rsquo;.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_FloorFix">FT_FloorFix</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a> )
  <b>FT_FloorFix</b>( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>  a );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A very simple function used to compute the floor function of a 16.16 fixed number.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>a</b></td><td>
<p>The number for which the floor function is to be computed.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The result of &lsquo;a &amp; -0x10000&rsquo;.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Vector_Transform">FT_Vector_Transform</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Vector_Transform</b>( <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*        vec,
                       <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>*  matrix );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Transform a single vector through a 2x2 matrix.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>vector</b></td><td>
<p>The target vector to transform.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>matrix</b></td><td>
<p>A pointer to the source 2x2 matrix.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The result is undefined if either &lsquo;vector&rsquo; or &lsquo;matrix&rsquo; is invalid.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Matrix_Multiply">FT_Matrix_Multiply</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_GLYPH_H (ftglyph.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Matrix_Multiply</b>( <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>*  a,
                      <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>*        b );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Perform the matrix operation &lsquo;b = a*b&rsquo;.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>a</b></td><td>
<p>A pointer to matrix &lsquo;a&rsquo;.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>b</b></td><td>
<p>A pointer to matrix &lsquo;b&rsquo;.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The result is undefined if either &lsquo;a&rsquo; or &lsquo;b&rsquo; is zero.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Matrix_Invert">FT_Matrix_Invert</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_GLYPH_H (ftglyph.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Matrix_Invert</b>( <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>*  matrix );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Invert a 2x2 matrix. Return an error if it can't be inverted.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>matrix</b></td><td>
<p>A pointer to the target matrix. Remains untouched in case of error.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Angle">FT_Angle</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>  <b>FT_Angle</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This type is used to model angle values in FreeType. Note that the angle is a 16.16 fixed-point value expressed in degrees.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_ANGLE_PI">FT_ANGLE_PI</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_ANGLE_PI</b>  ( 180L &lt;&lt; 16 )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The angle pi expressed in <a href="ft2-computations.html#FT_Angle">FT_Angle</a> units.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_ANGLE_2PI">FT_ANGLE_2PI</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_ANGLE_2PI</b>  ( <a href="ft2-computations.html#FT_ANGLE_PI">FT_ANGLE_PI</a> * 2 )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The angle 2*pi expressed in <a href="ft2-computations.html#FT_Angle">FT_Angle</a> units.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_ANGLE_PI2">FT_ANGLE_PI2</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_ANGLE_PI2</b>  ( <a href="ft2-computations.html#FT_ANGLE_PI">FT_ANGLE_PI</a> / 2 )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The angle pi/2 expressed in <a href="ft2-computations.html#FT_Angle">FT_Angle</a> units.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_ANGLE_PI4">FT_ANGLE_PI4</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_ANGLE_PI4</b>  ( <a href="ft2-computations.html#FT_ANGLE_PI">FT_ANGLE_PI</a> / 4 )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The angle pi/4 expressed in <a href="ft2-computations.html#FT_Angle">FT_Angle</a> units.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Sin">FT_Sin</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a> )
  <b>FT_Sin</b>( <a href="ft2-computations.html#FT_Angle">FT_Angle</a>  angle );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the sinus of a given angle in fixed-point format.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>angle</b></td><td>
<p>The input angle.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The sinus value.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If you need both the sinus and cosinus for a given angle, use the function <a href="ft2-computations.html#FT_Vector_Unit">FT_Vector_Unit</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Cos">FT_Cos</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a> )
  <b>FT_Cos</b>( <a href="ft2-computations.html#FT_Angle">FT_Angle</a>  angle );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the cosinus of a given angle in fixed-point format.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>angle</b></td><td>
<p>The input angle.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The cosinus value.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If you need both the sinus and cosinus for a given angle, use the function <a href="ft2-computations.html#FT_Vector_Unit">FT_Vector_Unit</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Tan">FT_Tan</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a> )
  <b>FT_Tan</b>( <a href="ft2-computations.html#FT_Angle">FT_Angle</a>  angle );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the tangent of a given angle in fixed-point format.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>angle</b></td><td>
<p>The input angle.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The tangent value.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Atan2">FT_Atan2</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-computations.html#FT_Angle">FT_Angle</a> )
  <b>FT_Atan2</b>( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>  x,
            <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>  y );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the arc-tangent corresponding to a given vector (x,y) in the 2d plane.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>x</b></td><td>
<p>The horizontal vector coordinate.</p>
</td></tr>
<tr valign=top><td><b>y</b></td><td>
<p>The vertical vector coordinate.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The arc-tangent value (i.e. angle).</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Angle_Diff">FT_Angle_Diff</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-computations.html#FT_Angle">FT_Angle</a> )
  <b>FT_Angle_Diff</b>( <a href="ft2-computations.html#FT_Angle">FT_Angle</a>  angle1,
                 <a href="ft2-computations.html#FT_Angle">FT_Angle</a>  angle2 );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the difference between two angles. The result is always constrained to the ]-PI..PI] interval.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>angle1</b></td><td>
<p>First angle.</p>
</td></tr>
<tr valign=top><td><b>angle2</b></td><td>
<p>Second angle.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>Constrained value of &lsquo;value2-value1&rsquo;.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Vector_Unit">FT_Vector_Unit</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Vector_Unit</b>( <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  vec,
                  <a href="ft2-computations.html#FT_Angle">FT_Angle</a>    angle );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the unit vector corresponding to a given angle. After the call, the value of &lsquo;vec.x&rsquo; will be &lsquo;sin(angle)&rsquo;, and the value of &lsquo;vec.y&rsquo; will be &lsquo;cos(angle)&rsquo;.</p>
<p>This function is useful to retrieve both the sinus and cosinus of a given angle quickly.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>vec</b></td><td>
<p>The address of target vector.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>angle</b></td><td>
<p>The address of angle.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Vector_Rotate">FT_Vector_Rotate</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Vector_Rotate</b>( <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  vec,
                    <a href="ft2-computations.html#FT_Angle">FT_Angle</a>    angle );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Rotate a vector by a given angle.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>vec</b></td><td>
<p>The address of target vector.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>angle</b></td><td>
<p>The address of angle.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Vector_Length">FT_Vector_Length</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a> )
  <b>FT_Vector_Length</b>( <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  vec );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the length of a given vector.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>vec</b></td><td>
<p>The address of target vector.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The vector length, expressed in the same units that the original vector coordinates.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Vector_Polarize">FT_Vector_Polarize</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Vector_Polarize</b>( <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  vec,
                      <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>   *length,
                      <a href="ft2-computations.html#FT_Angle">FT_Angle</a>   *angle );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Compute both the length and angle of a given vector.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>vec</b></td><td>
<p>The address of source vector.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>length</b></td><td>
<p>The vector length.</p>
</td></tr>
<tr valign=top><td><b>angle</b></td><td>
<p>The vector angle.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Vector_From_Polar">FT_Vector_From_Polar</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_TRIGONOMETRY_H (fttrigon.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Vector_From_Polar</b>( <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  vec,
                        <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>    length,
                        <a href="ft2-computations.html#FT_Angle">FT_Angle</a>    angle );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Compute vector coordinates from a length and angle.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>vec</b></td><td>
<p>The address of source vector.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>length</b></td><td>
<p>The vector length.</p>
</td></tr>
<tr valign=top><td><b>angle</b></td><td>
<p>The vector angle.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
