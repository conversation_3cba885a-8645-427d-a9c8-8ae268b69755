<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
Module Management
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_Module_Constructor">FT_Module_Constructor</a></td><td></td><td><a href="#FT_Remove_Module">FT_Remove_Module</a></td><td></td><td><a href="#FT_Set_Debug_Hook">FT_Set_Debug_Hook</a></td></tr>
<tr><td></td><td><a href="#FT_Module_Destructor">FT_Module_Destructor</a></td><td></td><td><a href="#FT_Property_Set">FT_Property_Set</a></td><td></td><td><a href="#FT_Add_Default_Modules">FT_Add_Default_Modules</a></td></tr>
<tr><td></td><td><a href="#FT_Module_Requester">FT_Module_Requester</a></td><td></td><td><a href="#FT_Property_Get">FT_Property_Get</a></td><td></td><td><a href="#FT_Renderer_Class">FT_Renderer_Class</a></td></tr>
<tr><td></td><td><a href="#FT_Module_Class">FT_Module_Class</a></td><td></td><td><a href="#FT_Reference_Library">FT_Reference_Library</a></td><td></td><td><a href="#FT_Get_Renderer">FT_Get_Renderer</a></td></tr>
<tr><td></td><td><a href="#FT_Add_Module">FT_Add_Module</a></td><td></td><td><a href="#FT_New_Library">FT_New_Library</a></td><td></td><td><a href="#FT_Set_Renderer">FT_Set_Renderer</a></td></tr>
<tr><td></td><td><a href="#FT_Get_Module">FT_Get_Module</a></td><td></td><td><a href="#FT_Done_Library">FT_Done_Library</a></td><td></td><td></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>The definitions below are used to manage modules within FreeType. Modules can be added, upgraded, and removed at runtime. Additionally, some module properties can be controlled also.</p>
<p>Here is a list of possible values of the &lsquo;module_name&rsquo; field in the <a href="ft2-module_management.html#FT_Module_Class">FT_Module_Class</a> structure.</p>
<pre class="colored">
  autofitter                                                       
  bdf                                                              
  cff                                                              
  gxvalid                                                          
  otvalid                                                          
  pcf                                                              
  pfr                                                              
  psaux                                                            
  pshinter                                                         
  psnames                                                          
  raster1, raster5                                                 
  sfnt                                                             
  smooth, smooth-lcd, smooth-lcdv                                  
  truetype                                                         
  type1                                                            
  type42                                                           
  t1cid                                                            
  winfonts                                                         
</pre>
<p>Note that the FreeType Cache sub-system is not a FreeType module.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_Module_Constructor">FT_Module_Constructor</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <a href="ft2-basic_types.html#FT_Error">FT_Error</a>
  (*<b>FT_Module_Constructor</b>)( <a href="ft2-base_interface.html#FT_Module">FT_Module</a>  module );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function used to initialize (not create) a new module object.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>module</b></td><td>
<p>The module to initialize.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Module_Destructor">FT_Module_Destructor</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">void</span>
  (*<b>FT_Module_Destructor</b>)( <a href="ft2-base_interface.html#FT_Module">FT_Module</a>  module );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function used to finalize (not destroy) a given module object.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>module</b></td><td>
<p>The module to finalize.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Module_Requester">FT_Module_Requester</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> FT_Module_Interface
  (*<b>FT_Module_Requester</b>)( <a href="ft2-base_interface.html#FT_Module">FT_Module</a>    module,
                          <span class="keyword">const</span> <span class="keyword">char</span>*  name );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function used to query a given module for a specific interface.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>module</b></td><td>
<p>The module to be searched.</p>
</td></tr>
<tr valign=top><td><b>name</b></td><td>
<p>The name of the interface in the module.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Module_Class">FT_Module_Class</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Module_Class_
  {
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>               module_flags;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>                module_size;
    <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_String">FT_String</a>*       module_name;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>               module_version;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>               module_requires;

    <span class="keyword">const</span> <span class="keyword">void</span>*            module_interface;

    <a href="ft2-module_management.html#FT_Module_Constructor">FT_Module_Constructor</a>  module_init;
    <a href="ft2-module_management.html#FT_Module_Destructor">FT_Module_Destructor</a>   module_done;
    <a href="ft2-module_management.html#FT_Module_Requester">FT_Module_Requester</a>    get_interface;

  } <b>FT_Module_Class</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The module class descriptor.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>module_flags</b></td><td>
<p>Bit flags describing the module.</p>
</td></tr>
<tr valign=top><td><b>module_size</b></td><td>
<p>The size of one module object/instance in bytes.</p>
</td></tr>
<tr valign=top><td><b>module_name</b></td><td>
<p>The name of the module.</p>
</td></tr>
<tr valign=top><td><b>module_version</b></td><td>
<p>The version, as a 16.16 fixed number (major.minor).</p>
</td></tr>
<tr valign=top><td><b>module_requires</b></td><td>
<p>The version of FreeType this module requires, as a 16.16 fixed number (major.minor). Starts at version 2.0, i.e., 0x20000.</p>
</td></tr>
<tr valign=top><td><b>module_init</b></td><td>
<p>The initializing function.</p>
</td></tr>
<tr valign=top><td><b>module_done</b></td><td>
<p>The finalizing function.</p>
</td></tr>
<tr valign=top><td><b>get_interface</b></td><td>
<p>The interface requesting function.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Add_Module">FT_Add_Module</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Add_Module</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>              library,
                 <span class="keyword">const</span> <a href="ft2-module_management.html#FT_Module_Class">FT_Module_Class</a>*  clazz );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Add a new module to a given library instance.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>clazz</b></td><td>
<p>A pointer to class descriptor for the module.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>An error will be returned if a module already exists by that name, or if the module requires a version of FreeType that is too great.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Module">FT_Get_Module</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-base_interface.html#FT_Module">FT_Module</a> )
  <b>FT_Get_Module</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>   library,
                 <span class="keyword">const</span> <span class="keyword">char</span>*  module_name );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Find a module by its name.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library object.</p>
</td></tr>
<tr valign=top><td><b>module_name</b></td><td>
<p>The module's name (as an ASCII string).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>A module handle. 0&nbsp;if none was found.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>FreeType's internal modules aren't documented very well, and you should look up the source code for details.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Remove_Module">FT_Remove_Module</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Remove_Module</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  library,
                    <a href="ft2-base_interface.html#FT_Module">FT_Module</a>   module );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Remove a given module from a library instance.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to a library object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>module</b></td><td>
<p>A handle to a module object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The module object is destroyed by the function in case of success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Property_Set">FT_Property_Set</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Property_Set</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>        library,
                   <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_String">FT_String</a>*  module_name,
                   <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_String">FT_String</a>*  property_name,
                   <span class="keyword">const</span> <span class="keyword">void</span>*       value );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Set a property for a given module.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library the module is part of.</p>
</td></tr>
<tr valign=top><td><b>module_name</b></td><td>
<p>The module name.</p>
</td></tr>
<tr valign=top><td><b>property_name</b></td><td>
<p>The property name. Properties are described in the &lsquo;Synopsis&rsquo; subsection of the module's documentation.</p>
<p>Note that only a few modules have properties.</p>
</td></tr>
<tr valign=top><td><b>value</b></td><td>
<p>A generic pointer to a variable or structure that gives the new value of the property. The exact definition of &lsquo;value&rsquo; is dependent on the property; see the &lsquo;Synopsis&rsquo; subsection of the module's documentation.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If &lsquo;module_name&rsquo; isn't a valid module name, or &lsquo;property_name&rsquo; doesn't specify a valid property, or if &lsquo;value&rsquo; doesn't represent a valid value for the given property, an error is returned.</p>
<p>The following example sets property &lsquo;bar&rsquo; (a simple integer) in module &lsquo;foo&rsquo; to value&nbsp;1.</p>
<pre class="colored">
  FT_UInt  bar;


  bar = 1;
  FT_Property_Set( library, "foo", "bar", &amp;bar );
</pre>
<p>Note that the FreeType Cache sub-system doesn't recognize module property changes. To avoid glyph lookup confusion within the cache you should call <a href="ft2-cache_subsystem.html#FTC_Manager_Reset">FTC_Manager_Reset</a> to completely flush the cache if a module property gets changed after <a href="ft2-cache_subsystem.html#FTC_Manager_New">FTC_Manager_New</a> has been called.</p>
<p>It is not possible to set properties of the FreeType Cache sub-system itself with FT_Property_Set; use ?FTC_Property_Set? instead.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.4.11</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Property_Get">FT_Property_Get</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Property_Get</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>        library,
                   <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_String">FT_String</a>*  module_name,
                   <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_String">FT_String</a>*  property_name,
                   <span class="keyword">void</span>*             value );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Get a module's property value.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library the module is part of.</p>
</td></tr>
<tr valign=top><td><b>module_name</b></td><td>
<p>The module name.</p>
</td></tr>
<tr valign=top><td><b>property_name</b></td><td>
<p>The property name. Properties are described in the &lsquo;Synopsis&rsquo; subsection of the module's documentation.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>value</b></td><td>
<p>A generic pointer to a variable or structure that gives the value of the property. The exact definition of &lsquo;value&rsquo; is dependent on the property; see the &lsquo;Synopsis&rsquo; subsection of the module's documentation.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If &lsquo;module_name&rsquo; isn't a valid module name, or &lsquo;property_name&rsquo; doesn't specify a valid property, or if &lsquo;value&rsquo; doesn't represent a valid value for the given property, an error is returned.</p>
<p>The following example gets property &lsquo;baz&rsquo; (a range) in module &lsquo;foo&rsquo;.</p>
<pre class="colored">
  typedef  range_
  {
    FT_Int32  min;
    FT_Int32  max;

  } range;

  range  baz;


  FT_Property_Get( library, "foo", "baz", &amp;baz );
</pre>
<p>It is not possible to retrieve properties of the FreeType Cache sub-system with FT_Property_Get; use ?FTC_Property_Get? instead.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.4.11</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Reference_Library">FT_Reference_Library</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Reference_Library</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  library );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A counter gets initialized to&nbsp;1 at the time an <a href="ft2-base_interface.html#FT_Library">FT_Library</a> structure is created. This function increments the counter. <a href="ft2-module_management.html#FT_Done_Library">FT_Done_Library</a> then only destroys a library if the counter is&nbsp;1, otherwise it simply decrements the counter.</p>
<p>This function helps in managing life-cycles of structures that reference <a href="ft2-base_interface.html#FT_Library">FT_Library</a> objects.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to a target library object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.4.2</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_New_Library">FT_New_Library</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_New_Library</b>( <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>    memory,
                  <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  *alibrary );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function is used to create a new FreeType library instance from a given memory object. It is thus possible to use libraries with distinct memory allocators within the same program.</p>
<p>Normally, you would call this function (followed by a call to <a href="ft2-module_management.html#FT_Add_Default_Modules">FT_Add_Default_Modules</a> or a series of calls to <a href="ft2-module_management.html#FT_Add_Module">FT_Add_Module</a>) instead of <a href="ft2-base_interface.html#FT_Init_FreeType">FT_Init_FreeType</a> to initialize the FreeType library.</p>
<p>Don't use <a href="ft2-base_interface.html#FT_Done_FreeType">FT_Done_FreeType</a> but <a href="ft2-module_management.html#FT_Done_Library">FT_Done_Library</a> to destroy a library instance.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>memory</b></td><td>
<p>A handle to the original memory object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>alibrary</b></td><td>
<p>A pointer to handle of a new library object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>See the discussion of reference counters in the description of <a href="ft2-module_management.html#FT_Reference_Library">FT_Reference_Library</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Done_Library">FT_Done_Library</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Done_Library</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  library );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Discard a given library object. This closes all drivers and discards all resource objects.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the target library.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>See the discussion of reference counters in the description of <a href="ft2-module_management.html#FT_Reference_Library">FT_Reference_Library</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_Debug_Hook">FT_Set_Debug_Hook</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Set_Debug_Hook</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>         library,
                     <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>            hook_index,
                     FT_DebugHook_Func  debug_hook );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Set a debug hook function for debugging the interpreter of a font format.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>hook_index</b></td><td>
<p>The index of the debug hook. You should use the values defined in &lsquo;ftobjs.h&rsquo;, e.g., &lsquo;FT_DEBUG_HOOK_TRUETYPE&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>debug_hook</b></td><td>
<p>The function used to debug the interpreter.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Currently, four debug hook slots are available, but only two (for the TrueType and the Type&nbsp;1 interpreter) are defined.</p>
<p>Since the internal headers of FreeType are no longer installed, the symbol &lsquo;FT_DEBUG_HOOK_TRUETYPE&rsquo; isn't available publicly. This is a bug and will be fixed in a forthcoming release.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Add_Default_Modules">FT_Add_Default_Modules</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_MODULE_H (ftmodapi.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Add_Default_Modules</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  library );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Add the set of default drivers to a given library object. This is only useful when you create a library object with <a href="ft2-module_management.html#FT_New_Library">FT_New_Library</a> (usually to plug a custom memory manager).</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to a new library object.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Renderer_Class">FT_Renderer_Class</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_RENDER_H (ftrender.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Renderer_Class_
  {
    <a href="ft2-module_management.html#FT_Module_Class">FT_Module_Class</a>            root;

    <a href="ft2-basic_types.html#FT_Glyph_Format">FT_Glyph_Format</a>            glyph_format;

    FT_Renderer_RenderFunc     render_glyph;
    FT_Renderer_TransformFunc  transform_glyph;
    FT_Renderer_GetCBoxFunc    get_glyph_cbox;
    FT_Renderer_SetModeFunc    set_mode;

    <a href="ft2-raster.html#FT_Raster_Funcs">FT_Raster_Funcs</a>*           raster_class;

  } <b>FT_Renderer_Class</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The renderer module class descriptor.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>root</b></td><td>
<p>The root <a href="ft2-module_management.html#FT_Module_Class">FT_Module_Class</a> fields.</p>
</td></tr>
<tr valign=top><td><b>glyph_format</b></td><td>
<p>The glyph image format this renderer handles.</p>
</td></tr>
<tr valign=top><td><b>render_glyph</b></td><td>
<p>A method used to render the image that is in a given glyph slot into a bitmap.</p>
</td></tr>
<tr valign=top><td><b>transform_glyph</b></td><td>
<p>A method used to transform the image that is in a given glyph slot.</p>
</td></tr>
<tr valign=top><td><b>get_glyph_cbox</b></td><td>
<p>A method used to access the glyph's cbox.</p>
</td></tr>
<tr valign=top><td><b>set_mode</b></td><td>
<p>A method used to pass additional parameters.</p>
</td></tr>
<tr valign=top><td><b>raster_class</b></td><td>
<p>For <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_OUTLINE</a> renderers only. This is a pointer to its raster's class.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Renderer">FT_Get_Renderer</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_RENDER_H (ftrender.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-base_interface.html#FT_Renderer">FT_Renderer</a> )
  <b>FT_Get_Renderer</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>       library,
                   <a href="ft2-basic_types.html#FT_Glyph_Format">FT_Glyph_Format</a>  format );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the current renderer for a given glyph format.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library object.</p>
</td></tr>
<tr valign=top><td><b>format</b></td><td>
<p>The glyph format.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>A renderer handle. 0&nbsp;if none found.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>An error will be returned if a module already exists by that name, or if the module requires a version of FreeType that is too great.</p>
<p>To add a new renderer, simply use <a href="ft2-module_management.html#FT_Add_Module">FT_Add_Module</a>. To retrieve a renderer by its name, use <a href="ft2-module_management.html#FT_Get_Module">FT_Get_Module</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_Renderer">FT_Set_Renderer</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_RENDER_H (ftrender.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Set_Renderer</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>     library,
                   <a href="ft2-base_interface.html#FT_Renderer">FT_Renderer</a>    renderer,
                   <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>        num_params,
                   <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a>*  parameters );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Set the current renderer to use, and set additional mode.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>renderer</b></td><td>
<p>A handle to the renderer object.</p>
</td></tr>
<tr valign=top><td><b>num_params</b></td><td>
<p>The number of additional parameters.</p>
</td></tr>
<tr valign=top><td><b>parameters</b></td><td>
<p>Additional parameters.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>In case of success, the renderer will be used to convert glyph images in the renderer's known format into bitmaps.</p>
<p>This doesn't change the current renderer for other formats.</p>
<p>Currently, only the B/W renderer, if compiled with FT_RASTER_OPTION_ANTI_ALIASING (providing a 5-levels anti-aliasing mode; this option must be set directly in &lsquo;ftraster.c&rsquo; and is undefined by default) accepts a single tag &lsquo;pal5&rsquo; to set its gray palette as a character string with 5&nbsp;elements. Consequently, the third and fourth argument are zero normally.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
