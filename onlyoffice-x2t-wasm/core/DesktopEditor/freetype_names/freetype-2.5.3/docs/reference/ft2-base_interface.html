<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>FreeType-2.5.3 API Reference</title>
<style type="text/css">
  body { font-family: Verdana, Geneva, Arial, Helvetica, serif;
         color: #000000;
         background: #FFFFFF; }

  p { text-align: justify; }
  h1 { text-align: center; }
  li { text-align: justify; }
  td { padding: 0 0.5em 0 0.5em; }
  td.left { padding: 0 0.5em 0 0.5em;
            text-align: left; }

  a:link { color: #0000EF; }
  a:visited { color: #51188E; }
  a:hover { color: #FF0000; }

  span.keyword { font-family: monospace;
                 text-align: left;
                 white-space: pre;
                 color: darkblue; }

  pre.colored { color: blue; }

  ul.empty { list-style-type: none; }
</style>
</head>
<body>

<table align=center><tr><td><font size=-1>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-1>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>
<center><h1>FreeType-2.5.3 API Reference</h1></center>

<center><h1>
Base Interface
</h1></center>
<h2>Synopsis</h2>
<table align=center cellspacing=5 cellpadding=0 border=0>
<tr><td></td><td><a href="#FT_Library">FT_Library</a></td><td></td><td><a href="#FT_IS_TRICKY">FT_IS_TRICKY</a></td><td></td><td><a href="#FT_Load_Glyph">FT_Load_Glyph</a></td></tr>
<tr><td></td><td><a href="#FT_Face">FT_Face</a></td><td></td><td><a href="#FT_HAS_COLOR">FT_HAS_COLOR</a></td><td></td><td><a href="#FT_Load_Char">FT_Load_Char</a></td></tr>
<tr><td></td><td><a href="#FT_Size">FT_Size</a></td><td></td><td><a href="#FT_STYLE_FLAG_XXX">FT_STYLE_FLAG_XXX</a></td><td></td><td><a href="#FT_LOAD_XXX">FT_LOAD_XXX</a></td></tr>
<tr><td></td><td><a href="#FT_GlyphSlot">FT_GlyphSlot</a></td><td></td><td><a href="#FT_Size_Internal">FT_Size_Internal</a></td><td></td><td><a href="#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_XXX</a></td></tr>
<tr><td></td><td><a href="#FT_CharMap">FT_CharMap</a></td><td></td><td><a href="#FT_Size_Metrics">FT_Size_Metrics</a></td><td></td><td><a href="#FT_LOAD_TARGET_MODE">FT_LOAD_TARGET_MODE</a></td></tr>
<tr><td></td><td><a href="#FT_Encoding">FT_Encoding</a></td><td></td><td><a href="#FT_SizeRec">FT_SizeRec</a></td><td></td><td><a href="#FT_Set_Transform">FT_Set_Transform</a></td></tr>
<tr><td></td><td><a href="#FT_Glyph_Metrics">FT_Glyph_Metrics</a></td><td></td><td><a href="#FT_SubGlyph">FT_SubGlyph</a></td><td></td><td><a href="#FT_Render_Mode">FT_Render_Mode</a></td></tr>
<tr><td></td><td><a href="#FT_Bitmap_Size">FT_Bitmap_Size</a></td><td></td><td><a href="#FT_Slot_Internal">FT_Slot_Internal</a></td><td></td><td><a href="#ft_render_mode_xxx">ft_render_mode_xxx</a></td></tr>
<tr><td></td><td><a href="#FT_Module">FT_Module</a></td><td></td><td><a href="#FT_GlyphSlotRec">FT_GlyphSlotRec</a></td><td></td><td><a href="#FT_Render_Glyph">FT_Render_Glyph</a></td></tr>
<tr><td></td><td><a href="#FT_Driver">FT_Driver</a></td><td></td><td><a href="#FT_Init_FreeType">FT_Init_FreeType</a></td><td></td><td><a href="#FT_Kerning_Mode">FT_Kerning_Mode</a></td></tr>
<tr><td></td><td><a href="#FT_Renderer">FT_Renderer</a></td><td></td><td><a href="#FT_Done_FreeType">FT_Done_FreeType</a></td><td></td><td><a href="#ft_kerning_default">ft_kerning_default</a></td></tr>
<tr><td></td><td><a href="#FT_ENC_TAG">FT_ENC_TAG</a></td><td></td><td><a href="#FT_OPEN_XXX">FT_OPEN_XXX</a></td><td></td><td><a href="#ft_kerning_unfitted">ft_kerning_unfitted</a></td></tr>
<tr><td></td><td><a href="#ft_encoding_xxx">ft_encoding_xxx</a></td><td></td><td><a href="#FT_Parameter">FT_Parameter</a></td><td></td><td><a href="#ft_kerning_unscaled">ft_kerning_unscaled</a></td></tr>
<tr><td></td><td><a href="#FT_CharMapRec">FT_CharMapRec</a></td><td></td><td><a href="#FT_Open_Args">FT_Open_Args</a></td><td></td><td><a href="#FT_Get_Kerning">FT_Get_Kerning</a></td></tr>
<tr><td></td><td><a href="#FT_Face_Internal">FT_Face_Internal</a></td><td></td><td><a href="#FT_New_Face">FT_New_Face</a></td><td></td><td><a href="#FT_Get_Track_Kerning">FT_Get_Track_Kerning</a></td></tr>
<tr><td></td><td><a href="#FT_FaceRec">FT_FaceRec</a></td><td></td><td><a href="#FT_New_Memory_Face">FT_New_Memory_Face</a></td><td></td><td><a href="#FT_Get_Glyph_Name">FT_Get_Glyph_Name</a></td></tr>
<tr><td></td><td><a href="#FT_FACE_FLAG_XXX">FT_FACE_FLAG_XXX</a></td><td></td><td><a href="#FT_Open_Face">FT_Open_Face</a></td><td></td><td><a href="#FT_Get_Postscript_Name">FT_Get_Postscript_Name</a></td></tr>
<tr><td></td><td><a href="#FT_HAS_HORIZONTAL">FT_HAS_HORIZONTAL</a></td><td></td><td><a href="#FT_Attach_File">FT_Attach_File</a></td><td></td><td><a href="#FT_Select_Charmap">FT_Select_Charmap</a></td></tr>
<tr><td></td><td><a href="#FT_HAS_VERTICAL">FT_HAS_VERTICAL</a></td><td></td><td><a href="#FT_Attach_Stream">FT_Attach_Stream</a></td><td></td><td><a href="#FT_Set_Charmap">FT_Set_Charmap</a></td></tr>
<tr><td></td><td><a href="#FT_HAS_KERNING">FT_HAS_KERNING</a></td><td></td><td><a href="#FT_Reference_Face">FT_Reference_Face</a></td><td></td><td><a href="#FT_Get_Charmap_Index">FT_Get_Charmap_Index</a></td></tr>
<tr><td></td><td><a href="#FT_IS_SCALABLE">FT_IS_SCALABLE</a></td><td></td><td><a href="#FT_Done_Face">FT_Done_Face</a></td><td></td><td><a href="#FT_Get_Char_Index">FT_Get_Char_Index</a></td></tr>
<tr><td></td><td><a href="#FT_IS_SFNT">FT_IS_SFNT</a></td><td></td><td><a href="#FT_Select_Size">FT_Select_Size</a></td><td></td><td><a href="#FT_Get_First_Char">FT_Get_First_Char</a></td></tr>
<tr><td></td><td><a href="#FT_IS_FIXED_WIDTH">FT_IS_FIXED_WIDTH</a></td><td></td><td><a href="#FT_Size_Request_Type">FT_Size_Request_Type</a></td><td></td><td><a href="#FT_Get_Next_Char">FT_Get_Next_Char</a></td></tr>
<tr><td></td><td><a href="#FT_HAS_FIXED_SIZES">FT_HAS_FIXED_SIZES</a></td><td></td><td><a href="#FT_Size_RequestRec">FT_Size_RequestRec</a></td><td></td><td><a href="#FT_Get_Name_Index">FT_Get_Name_Index</a></td></tr>
<tr><td></td><td><a href="#FT_HAS_FAST_GLYPHS">FT_HAS_FAST_GLYPHS</a></td><td></td><td><a href="#FT_Size_Request">FT_Size_Request</a></td><td></td><td><a href="#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_XXX</a></td></tr>
<tr><td></td><td><a href="#FT_HAS_GLYPH_NAMES">FT_HAS_GLYPH_NAMES</a></td><td></td><td><a href="#FT_Request_Size">FT_Request_Size</a></td><td></td><td><a href="#FT_Get_SubGlyph_Info">FT_Get_SubGlyph_Info</a></td></tr>
<tr><td></td><td><a href="#FT_HAS_MULTIPLE_MASTERS">FT_HAS_MULTIPLE_MASTERS</a></td><td></td><td><a href="#FT_Set_Char_Size">FT_Set_Char_Size</a></td><td></td><td><a href="#FT_FSTYPE_XXX">FT_FSTYPE_XXX</a></td></tr>
<tr><td></td><td><a href="#FT_IS_CID_KEYED">FT_IS_CID_KEYED</a></td><td></td><td><a href="#FT_Set_Pixel_Sizes">FT_Set_Pixel_Sizes</a></td><td></td><td><a href="#FT_Get_FSType_Flags">FT_Get_FSType_Flags</a></td></tr>
</table><br><br>

<table align=center width="87%"><tr><td>
<p>This section describes the public high-level API of FreeType&nbsp;2.</p>
</td></tr></table><br>
<table align=center width="75%"><tr><td>
<h4><a name="FT_Library">FT_Library</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_LibraryRec_  *<b>FT_Library</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a FreeType library instance. Each &lsquo;library&rsquo; is completely independent from the others; it is the &lsquo;root&rsquo; of a set of objects like fonts, faces, sizes, etc.</p>
<p>It also embeds a memory manager (see <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>), as well as a scan-line converter object (see <a href="ft2-raster.html#FT_Raster">FT_Raster</a>).</p>
<p>In multi-threaded applications, make sure that the same FT_Library object or any of its children doesn't get accessed in parallel.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Library objects are normally created by <a href="ft2-base_interface.html#FT_Init_FreeType">FT_Init_FreeType</a>, and destroyed with <a href="ft2-base_interface.html#FT_Done_FreeType">FT_Done_FreeType</a>. If you need reference-counting (cf. <a href="ft2-module_management.html#FT_Reference_Library">FT_Reference_Library</a>), use <a href="ft2-module_management.html#FT_New_Library">FT_New_Library</a> and <a href="ft2-module_management.html#FT_Done_Library">FT_Done_Library</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Face">FT_Face</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_FaceRec_*  <b>FT_Face</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a given typographic face object. A face object models a given typeface, in a given style.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Each face object also owns a single <a href="ft2-base_interface.html#FT_GlyphSlot">FT_GlyphSlot</a> object, as well as one or more <a href="ft2-base_interface.html#FT_Size">FT_Size</a> objects.</p>
<p>Use <a href="ft2-base_interface.html#FT_New_Face">FT_New_Face</a> or <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> to create a new face object from a given filepathname or a custom input stream.</p>
<p>Use <a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a> to destroy it (along with its slot and sizes).</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>also</b></em></td></tr><tr><td>
<p>See <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> for the publicly accessible fields of a given face object.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Size">FT_Size</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_SizeRec_*  <b>FT_Size</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to an object used to model a face scaled to a given character size.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Each <a href="ft2-base_interface.html#FT_Face">FT_Face</a> has an <i>active</i> <a href="ft2-base_interface.html#FT_Size">FT_Size</a> object that is used by functions like <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a> to determine the scaling transformation that in turn is used to load and hint glyphs and metrics.</p>
<p>You can use <a href="ft2-base_interface.html#FT_Set_Char_Size">FT_Set_Char_Size</a>, <a href="ft2-base_interface.html#FT_Set_Pixel_Sizes">FT_Set_Pixel_Sizes</a>, <a href="ft2-base_interface.html#FT_Request_Size">FT_Request_Size</a> or even <a href="ft2-base_interface.html#FT_Select_Size">FT_Select_Size</a> to change the content (i.e., the scaling values) of the active <a href="ft2-base_interface.html#FT_Size">FT_Size</a>.</p>
<p>You can use <a href="ft2-sizes_management.html#FT_New_Size">FT_New_Size</a> to create additional size objects for a given <a href="ft2-base_interface.html#FT_Face">FT_Face</a>, but they won't be used by other functions until you activate it through <a href="ft2-sizes_management.html#FT_Activate_Size">FT_Activate_Size</a>. Only one size can be activated at any given time per face.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>also</b></em></td></tr><tr><td>
<p>See <a href="ft2-base_interface.html#FT_SizeRec">FT_SizeRec</a> for the publicly accessible fields of a given size object.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_GlyphSlot">FT_GlyphSlot</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_GlyphSlotRec_*  <b>FT_GlyphSlot</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a given &lsquo;glyph slot&rsquo;. A slot is a container where it is possible to load any of the glyphs contained in its parent face.</p>
<p>In other words, each time you call <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a> or <a href="ft2-base_interface.html#FT_Load_Char">FT_Load_Char</a>, the slot's content is erased by the new glyph data, i.e., the glyph's metrics, its image (bitmap or outline), and other control information.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>also</b></em></td></tr><tr><td>
<p>See <a href="ft2-base_interface.html#FT_GlyphSlotRec">FT_GlyphSlotRec</a> for the publicly accessible glyph fields.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_CharMap">FT_CharMap</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_CharMapRec_*  <b>FT_CharMap</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a given character map. A charmap is used to translate character codes in a given encoding into glyph indexes for its parent's face. Some font formats may provide several charmaps per font.</p>
<p>Each face object owns zero or more charmaps, but only one of them can be &lsquo;active&rsquo; and used by <a href="ft2-base_interface.html#FT_Get_Char_Index">FT_Get_Char_Index</a> or <a href="ft2-base_interface.html#FT_Load_Char">FT_Load_Char</a>.</p>
<p>The list of available charmaps in a face is available through the &lsquo;face-&gt;num_charmaps&rsquo; and &lsquo;face-&gt;charmaps&rsquo; fields of <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a>.</p>
<p>The currently active charmap is available as &lsquo;face-&gt;charmap&rsquo;. You should call <a href="ft2-base_interface.html#FT_Set_Charmap">FT_Set_Charmap</a> to change it.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>When a new face is created (either through <a href="ft2-base_interface.html#FT_New_Face">FT_New_Face</a> or <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a>), the library looks for a Unicode charmap within the list and automatically activates it.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>also</b></em></td></tr><tr><td>
<p>See <a href="ft2-base_interface.html#FT_CharMapRec">FT_CharMapRec</a> for the publicly accessible fields of a given character map.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Encoding">FT_Encoding</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Encoding_
  {
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_NONE</a>, 0, 0, 0, 0 ),

    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_SYMBOL</a>, 's', 'y', 'm', 'b' ),
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_UNICODE</a>,   'u', 'n', 'i', 'c' ),

    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_SJIS</a>,    's', 'j', 'i', 's' ),
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_GB2312</a>,  'g', 'b', ' ', ' ' ),
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_BIG5</a>,    'b', 'i', 'g', '5' ),
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_WANSUNG</a>, 'w', 'a', 'n', 's' ),
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_JOHAB</a>,   'j', 'o', 'h', 'a' ),

    /* for backwards compatibility */
    <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_SJIS</a>    = <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_SJIS</a>,
    <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_GB2312</a>  = <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_GB2312</a>,
    <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_BIG5</a>    = <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_BIG5</a>,
    <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_WANSUNG</a> = <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_WANSUNG</a>,
    <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_JOHAB</a>   = <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_JOHAB</a>,

    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_STANDARD</a>, 'A', 'D', 'O', 'B' ),
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_EXPERT</a>,   'A', 'D', 'B', 'E' ),
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_CUSTOM</a>,   'A', 'D', 'B', 'C' ),
    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_LATIN_1</a>,  'l', 'a', 't', '1' ),

    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_OLD_LATIN_2</a>, 'l', 'a', 't', '2' ),

    <a href="ft2-base_interface.html#FT_ENC_TAG">FT_ENC_TAG</a>( <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_APPLE_ROMAN</a>, 'a', 'r', 'm', 'n' )

  } <b>FT_Encoding</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An enumeration used to specify character sets supported by charmaps. Used in the <a href="ft2-base_interface.html#FT_Select_Charmap">FT_Select_Charmap</a> API function.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Despite the name, this enumeration lists specific character repertories (i.e., charsets), and not text encoding methods (e.g., UTF-8, UTF-16, etc.).</p>
<p>Other encodings might be defined in the future.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_ENCODING_NONE</b></td><td>
<p>The encoding value&nbsp;0 is reserved.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_UNICODE</b></td><td>
<p>Corresponds to the Unicode character set. This value covers all versions of the Unicode repertoire, including ASCII and Latin-1. Most fonts include a Unicode charmap, but not all of them.</p>
<p>For example, if you want to access Unicode value U+1F028 (and the font contains it), use value 0x1F028 as the input value for <a href="ft2-base_interface.html#FT_Get_Char_Index">FT_Get_Char_Index</a>.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_MS_SYMBOL</b></td><td>
<p>Corresponds to the Microsoft Symbol encoding, used to encode mathematical symbols in the 32..255 character code range. For more information, see &lsquo;<a href="http://www.kostis.net/charsets/symbol.htm">http://www.kostis.net/charsets/symbol.htm</a>&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_SJIS</b></td><td>
<p>Corresponds to Japanese SJIS encoding. More info at at &lsquo;<a href="http://en.wikipedia.org/wiki/Shift_JIS">http://en.wikipedia.org/wiki/Shift_JIS</a>&rsquo;. See note on multi-byte encodings below.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_GB2312</b></td><td>
<p>Corresponds to an encoding system for Simplified Chinese as used used in mainland China.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_BIG5</b></td><td>
<p>Corresponds to an encoding system for Traditional Chinese as used in Taiwan and Hong Kong.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_WANSUNG</b></td><td>
<p>Corresponds to the Korean encoding system known as Wansung. For more information see &lsquo;<a href="http://msdn.microsoft.com/en-US/goglobal/cc305154">http://msdn.microsoft.com/en-US/goglobal/cc305154</a>&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_JOHAB</b></td><td>
<p>The Korean standard character set (KS&nbsp;C 5601-1992), which corresponds to MS Windows code page 1361. This character set includes all possible Hangeul character combinations.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ENCODING_ADOBE_LATIN_1</b></td></tr>
<tr valign=top><td></td><td>
<p>Corresponds to a Latin-1 encoding as defined in a Type&nbsp;1 PostScript font. It is limited to 256 character codes.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ENCODING_ADOBE_STANDARD</b></td></tr>
<tr valign=top><td></td><td>
<p>Corresponds to the Adobe Standard encoding, as found in Type&nbsp;1, CFF, and OpenType/CFF fonts. It is limited to 256 character codes.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ENCODING_ADOBE_EXPERT</b></td></tr>
<tr valign=top><td></td><td>
<p>Corresponds to the Adobe Expert encoding, as found in Type&nbsp;1, CFF, and OpenType/CFF fonts. It is limited to 256 character codes.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ENCODING_ADOBE_CUSTOM</b></td></tr>
<tr valign=top><td></td><td>
<p>Corresponds to a custom encoding, as found in Type&nbsp;1, CFF, and OpenType/CFF fonts. It is limited to 256 character codes.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ENCODING_APPLE_ROMAN</b></td></tr>
<tr valign=top><td></td><td>
<p>Corresponds to the 8-bit Apple roman encoding. Many TrueType and OpenType fonts contain a charmap for this encoding, since older versions of Mac OS are able to use it.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_ENCODING_OLD_LATIN_2</b></td></tr>
<tr valign=top><td></td><td>
<p>This value is deprecated and was never used nor reported by FreeType. Don't use or test for it.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_MS_SJIS</b></td><td>
<p>Same as FT_ENCODING_SJIS. Deprecated.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_MS_GB2312</b></td><td>
<p>Same as FT_ENCODING_GB2312. Deprecated.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_MS_BIG5</b></td><td>
<p>Same as FT_ENCODING_BIG5. Deprecated.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_MS_WANSUNG</b></td><td>
<p>Same as FT_ENCODING_WANSUNG. Deprecated.</p>
</td></tr>
<tr valign=top><td><b>FT_ENCODING_MS_JOHAB</b></td><td>
<p>Same as FT_ENCODING_JOHAB. Deprecated.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>By default, FreeType automatically synthesizes a Unicode charmap for PostScript fonts, using their glyph names dictionaries. However, it also reports the encodings defined explicitly in the font file, for the cases when they are needed, with the Adobe values as well.</p>
<p>FT_ENCODING_NONE is set by the BDF and PCF drivers if the charmap is neither Unicode nor ISO-8859-1 (otherwise it is set to FT_ENCODING_UNICODE). Use <a href="ft2-bdf_fonts.html#FT_Get_BDF_Charset_ID">FT_Get_BDF_Charset_ID</a> to find out which encoding is really present. If, for example, the &lsquo;cs_registry&rsquo; field is &lsquo;KOI8&rsquo; and the &lsquo;cs_encoding&rsquo; field is &lsquo;R&rsquo;, the font is encoded in KOI8-R.</p>
<p>FT_ENCODING_NONE is always set (with a single exception) by the winfonts driver. Use <a href="ft2-winfnt_fonts.html#FT_Get_WinFNT_Header">FT_Get_WinFNT_Header</a> and examine the &lsquo;charset&rsquo; field of the <a href="ft2-winfnt_fonts.html#FT_WinFNT_HeaderRec">FT_WinFNT_HeaderRec</a> structure to find out which encoding is really present. For example, <a href="ft2-winfnt_fonts.html#FT_WinFNT_ID_XXX">FT_WinFNT_ID_CP1251</a> (204) means Windows code page 1251 (for Russian).</p>
<p>FT_ENCODING_NONE is set if &lsquo;platform_id&rsquo; is <a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_MACINTOSH</a> and &lsquo;encoding_id&rsquo; is not <a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_ROMAN</a> (otherwise it is set to FT_ENCODING_APPLE_ROMAN).</p>
<p>If &lsquo;platform_id&rsquo; is <a href="ft2-truetype_tables.html#TT_PLATFORM_XXX">TT_PLATFORM_MACINTOSH</a>, use the function <a href="ft2-truetype_tables.html#FT_Get_CMap_Language_ID">FT_Get_CMap_Language_ID</a> to query the Mac language ID that may be needed to be able to distinguish Apple encoding variants. See</p>
<p><a href="http://www.unicode.org/Public/MAPPINGS/VENDORS/APPLE/Readme.txt">http://www.unicode.org/Public/MAPPINGS/VENDORS/APPLE/Readme.txt</a></p>
<p>to get an idea how to do that. Basically, if the language ID is&nbsp;0, don't use it, otherwise subtract 1 from the language ID. Then examine &lsquo;encoding_id&rsquo;. If, for example, &lsquo;encoding_id&rsquo; is <a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_ROMAN</a> and the language ID (minus&nbsp;1) is &lsquo;TT_MAC_LANGID_GREEK&rsquo;, it is the Greek encoding, not Roman. <a href="ft2-truetype_tables.html#TT_MAC_ID_XXX">TT_MAC_ID_ARABIC</a> with &lsquo;TT_MAC_LANGID_FARSI&rsquo; means the Farsi variant the Arabic encoding.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Glyph_Metrics">FT_Glyph_Metrics</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Glyph_Metrics_
  {
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>  width;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>  height;

    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>  horiBearingX;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>  horiBearingY;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>  horiAdvance;

    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>  vertBearingX;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>  vertBearingY;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>  vertAdvance;

  } <b>FT_Glyph_Metrics</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to model the metrics of a single glyph. The values are expressed in 26.6 fractional pixel format; if the flag <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a> has been used while loading the glyph, values are expressed in font units instead.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>width</b></td><td>
<p>The glyph's width.</p>
</td></tr>
<tr valign=top><td><b>height</b></td><td>
<p>The glyph's height.</p>
</td></tr>
<tr valign=top><td><b>horiBearingX</b></td><td>
<p>Left side bearing for horizontal layout.</p>
</td></tr>
<tr valign=top><td><b>horiBearingY</b></td><td>
<p>Top side bearing for horizontal layout.</p>
</td></tr>
<tr valign=top><td><b>horiAdvance</b></td><td>
<p>Advance width for horizontal layout.</p>
</td></tr>
<tr valign=top><td><b>vertBearingX</b></td><td>
<p>Left side bearing for vertical layout.</p>
</td></tr>
<tr valign=top><td><b>vertBearingY</b></td><td>
<p>Top side bearing for vertical layout. Larger positive values mean further below the vertical glyph origin.</p>
</td></tr>
<tr valign=top><td><b>vertAdvance</b></td><td>
<p>Advance height for vertical layout. Positive values mean the glyph has a positive advance downward.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If not disabled with <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_HINTING</a>, the values represent dimensions of the hinted glyph (in case hinting is applicable).</p>
<p>Stroking a glyph with an outside border does not increase &lsquo;horiAdvance&rsquo; or &lsquo;vertAdvance&rsquo;; you have to manually adjust these values to account for the added width and height.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Bitmap_Size">FT_Bitmap_Size</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Bitmap_Size_
  {
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>  height;
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>  width;

    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>    size;

    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>    x_ppem;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>    y_ppem;

  } <b>FT_Bitmap_Size</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This structure models the metrics of a bitmap strike (i.e., a set of glyphs for a given point size and resolution) in a bitmap font. It is used for the &lsquo;available_sizes&rsquo; field of <a href="ft2-base_interface.html#FT_Face">FT_Face</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>height</b></td><td>
<p>The vertical distance, in pixels, between two consecutive baselines. It is always positive.</p>
</td></tr>
<tr valign=top><td><b>width</b></td><td>
<p>The average width, in pixels, of all glyphs in the strike.</p>
</td></tr>
<tr valign=top><td><b>size</b></td><td>
<p>The nominal size of the strike in 26.6 fractional points. This field is not very useful.</p>
</td></tr>
<tr valign=top><td><b>x_ppem</b></td><td>
<p>The horizontal ppem (nominal width) in 26.6 fractional pixels.</p>
</td></tr>
<tr valign=top><td><b>y_ppem</b></td><td>
<p>The vertical ppem (nominal height) in 26.6 fractional pixels.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Windows FNT: The nominal size given in a FNT font is not reliable. Thus when the driver finds it incorrect, it sets &lsquo;size&rsquo; to some calculated values and sets &lsquo;x_ppem&rsquo; and &lsquo;y_ppem&rsquo; to the pixel width and height given in the font, respectively.</p>
<p>TrueType embedded bitmaps: &lsquo;size&rsquo;, &lsquo;width&rsquo;, and &lsquo;height&rsquo; values are not contained in the bitmap strike itself. They are computed from the global font parameters.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Module">FT_Module</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_ModuleRec_*  <b>FT_Module</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a given FreeType module object. Each module can be a font driver, a renderer, or anything else that provides services to the formers.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Driver">FT_Driver</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_DriverRec_*  <b>FT_Driver</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a given FreeType font driver object. Each font driver is a special module capable of creating faces from font files.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Renderer">FT_Renderer</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_RendererRec_*  <b>FT_Renderer</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a given FreeType renderer. A renderer is a special module in charge of converting a glyph image to a bitmap, when necessary. Each renderer supports a given glyph image format, and one or more target surface depths.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_ENC_TAG">FT_ENC_TAG</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#ifndef <b>FT_ENC_TAG</b>
#define <b>FT_ENC_TAG</b>( value, a, b, c, d )         \
          value = ( ( (<a href="ft2-basic_types.html#FT_UInt32">FT_UInt32</a>)(a) &lt;&lt; 24 ) |  \
                    ( (<a href="ft2-basic_types.html#FT_UInt32">FT_UInt32</a>)(b) &lt;&lt; 16 ) |  \
                    ( (<a href="ft2-basic_types.html#FT_UInt32">FT_UInt32</a>)(c) &lt;&lt;  8 ) |  \
                      (<a href="ft2-basic_types.html#FT_UInt32">FT_UInt32</a>)(d)         )

#endif /* <b>FT_ENC_TAG</b> */

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This macro converts four-letter tags into an unsigned long. It is used to define &lsquo;encoding&rsquo; identifiers (see <a href="ft2-base_interface.html#FT_Encoding">FT_Encoding</a>).</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Since many 16-bit compilers don't like 32-bit enumerations, you should redefine this macro in case of problems to something like this:</p>
<pre class="colored">
  #define FT_ENC_TAG( value, a, b, c, d )  value                   
</pre>
<p>to get a simple enumeration without assigning special numbers.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="ft_encoding_xxx">ft_encoding_xxx</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define ft_encoding_none            <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_NONE</a>
#define ft_encoding_unicode         <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_UNICODE</a>
#define ft_encoding_symbol          <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_MS_SYMBOL</a>
#define ft_encoding_latin_1         <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_LATIN_1</a>
#define ft_encoding_latin_2         <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_OLD_LATIN_2</a>
#define ft_encoding_sjis            <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_SJIS</a>
#define ft_encoding_gb2312          <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_GB2312</a>
#define ft_encoding_big5            <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_BIG5</a>
#define ft_encoding_wansung         <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_WANSUNG</a>
#define ft_encoding_johab           <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_JOHAB</a>

#define ft_encoding_adobe_standard  <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_STANDARD</a>
#define ft_encoding_adobe_expert    <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_EXPERT</a>
#define ft_encoding_adobe_custom    <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_ADOBE_CUSTOM</a>
#define ft_encoding_apple_roman     <a href="ft2-base_interface.html#FT_Encoding">FT_ENCODING_APPLE_ROMAN</a>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>These constants are deprecated; use the corresponding <a href="ft2-base_interface.html#FT_Encoding">FT_Encoding</a> values instead.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_CharMapRec">FT_CharMapRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_CharMapRec_
  {
    <a href="ft2-base_interface.html#FT_Face">FT_Face</a>      face;
    <a href="ft2-base_interface.html#FT_Encoding">FT_Encoding</a>  encoding;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>    platform_id;
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>    encoding_id;

  } <b>FT_CharMapRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The base charmap structure.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the parent face object.</p>
</td></tr>
<tr valign=top><td><b>encoding</b></td><td>
<p>An <a href="ft2-base_interface.html#FT_Encoding">FT_Encoding</a> tag identifying the charmap. Use this with <a href="ft2-base_interface.html#FT_Select_Charmap">FT_Select_Charmap</a>.</p>
</td></tr>
<tr valign=top><td><b>platform_id</b></td><td>
<p>An ID number describing the platform for the following encoding ID. This comes directly from the TrueType specification and should be emulated for other formats.</p>
</td></tr>
<tr valign=top><td><b>encoding_id</b></td><td>
<p>A platform specific encoding number. This also comes from the TrueType specification and should be emulated similarly.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Face_Internal">FT_Face_Internal</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Face_InternalRec_*  <b>FT_Face_Internal</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An opaque handle to an &lsquo;FT_Face_InternalRec&rsquo; structure, used to model private data of a given <a href="ft2-base_interface.html#FT_Face">FT_Face</a> object.</p>
<p>This structure might change between releases of FreeType&nbsp;2 and is not generally available to client applications.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_FaceRec">FT_FaceRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_FaceRec_
  {
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>           num_faces;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>           face_index;

    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>           face_flags;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>           style_flags;

    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>           num_glyphs;

    <a href="ft2-basic_types.html#FT_String">FT_String</a>*        family_name;
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*        style_name;

    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>            num_fixed_sizes;
    <a href="ft2-base_interface.html#FT_Bitmap_Size">FT_Bitmap_Size</a>*   available_sizes;

    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>            num_charmaps;
    <a href="ft2-base_interface.html#FT_CharMap">FT_CharMap</a>*       charmaps;

    <a href="ft2-basic_types.html#FT_Generic">FT_Generic</a>        generic;

    /*# The following member variables (down to `underline_thickness') */
    /*# are only relevant to scalable outlines; cf. @<a href="ft2-base_interface.html#FT_Bitmap_Size">FT_Bitmap_Size</a>    */
    /*# for bitmap fonts.                                              */
    <a href="ft2-basic_types.html#FT_BBox">FT_BBox</a>           bbox;

    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>         units_per_EM;
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>          ascender;
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>          descender;
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>          height;

    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>          max_advance_width;
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>          max_advance_height;

    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>          underline_position;
    <a href="ft2-basic_types.html#FT_Short">FT_Short</a>          underline_thickness;

    <a href="ft2-base_interface.html#FT_GlyphSlot">FT_GlyphSlot</a>      glyph;
    <a href="ft2-base_interface.html#FT_Size">FT_Size</a>           size;
    <a href="ft2-base_interface.html#FT_CharMap">FT_CharMap</a>        charmap;

    /*@private begin */

    <a href="ft2-base_interface.html#FT_Driver">FT_Driver</a>         driver;
    <a href="ft2-system_interface.html#FT_Memory">FT_Memory</a>         memory;
    <a href="ft2-system_interface.html#FT_Stream">FT_Stream</a>         stream;

    <a href="ft2-list_processing.html#FT_ListRec">FT_ListRec</a>        sizes_list;

    <a href="ft2-basic_types.html#FT_Generic">FT_Generic</a>        autohint;   /* face-specific auto-hinter data */
    <span class="keyword">void</span>*             extensions; /* unused                         */

    <a href="ft2-base_interface.html#FT_Face_Internal">FT_Face_Internal</a>  internal;

    /*@private end */

  } <b>FT_FaceRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>FreeType root face class structure. A face object models a typeface in a font file.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>num_faces</b></td><td>
<p>The number of faces in the font file. Some font formats can have multiple faces in a font file.</p>
</td></tr>
<tr valign=top><td><b>face_index</b></td><td>
<p>The index of the face in the font file. It is set to&nbsp;0 if there is only one face in the font file.</p>
</td></tr>
<tr valign=top><td><b>face_flags</b></td><td>
<p>A set of bit flags that give important information about the face; see <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_XXX</a> for the details.</p>
</td></tr>
<tr valign=top><td><b>style_flags</b></td><td>
<p>A set of bit flags indicating the style of the face; see <a href="ft2-base_interface.html#FT_STYLE_FLAG_XXX">FT_STYLE_FLAG_XXX</a> for the details.</p>
</td></tr>
<tr valign=top><td><b>num_glyphs</b></td><td>
<p>The number of glyphs in the face. If the face is scalable and has sbits (see &lsquo;num_fixed_sizes&rsquo;), it is set to the number of outline glyphs.</p>
<p>For CID-keyed fonts, this value gives the highest CID used in the font.</p>
</td></tr>
<tr valign=top><td><b>family_name</b></td><td>
<p>The face's family name. This is an ASCII string, usually in English, that describes the typeface's family (like &lsquo;Times New Roman&rsquo;, &lsquo;Bodoni&rsquo;, &lsquo;Garamond&rsquo;, etc). This is a least common denominator used to list fonts. Some formats (TrueType &amp; OpenType) provide localized and Unicode versions of this string. Applications should use the format specific interface to access them. Can be NULL (e.g., in fonts embedded in a PDF file).</p>
</td></tr>
<tr valign=top><td><b>style_name</b></td><td>
<p>The face's style name. This is an ASCII string, usually in English, that describes the typeface's style (like &lsquo;Italic&rsquo;, &lsquo;Bold&rsquo;, &lsquo;Condensed&rsquo;, etc). Not all font formats provide a style name, so this field is optional, and can be set to NULL. As for &lsquo;family_name&rsquo;, some formats provide localized and Unicode versions of this string. Applications should use the format specific interface to access them.</p>
</td></tr>
<tr valign=top><td><b>num_fixed_sizes</b></td><td>
<p>The number of bitmap strikes in the face. Even if the face is scalable, there might still be bitmap strikes, which are called &lsquo;sbits&rsquo; in that case.</p>
</td></tr>
<tr valign=top><td><b>available_sizes</b></td><td>
<p>An array of <a href="ft2-base_interface.html#FT_Bitmap_Size">FT_Bitmap_Size</a> for all bitmap strikes in the face. It is set to NULL if there is no bitmap strike.</p>
</td></tr>
<tr valign=top><td><b>num_charmaps</b></td><td>
<p>The number of charmaps in the face.</p>
</td></tr>
<tr valign=top><td><b>charmaps</b></td><td>
<p>An array of the charmaps of the face.</p>
</td></tr>
<tr valign=top><td><b>generic</b></td><td>
<p>A field reserved for client uses. See the <a href="ft2-basic_types.html#FT_Generic">FT_Generic</a> type description.</p>
</td></tr>
<tr valign=top><td><b>bbox</b></td><td>
<p>The font bounding box. Coordinates are expressed in font units (see &lsquo;units_per_EM&rsquo;). The box is large enough to contain any glyph from the font. Thus, &lsquo;bbox.yMax&rsquo; can be seen as the &lsquo;maximum ascender&rsquo;, and &lsquo;bbox.yMin&rsquo; as the &lsquo;minimum descender&rsquo;. Only relevant for scalable formats.</p>
<p>Note that the bounding box might be off by (at least) one pixel for hinted fonts. See <a href="ft2-base_interface.html#FT_Size_Metrics">FT_Size_Metrics</a> for further discussion.</p>
</td></tr>
<tr valign=top><td><b>units_per_EM</b></td><td>
<p>The number of font units per EM square for this face. This is typically 2048 for TrueType fonts, and 1000 for Type&nbsp;1 fonts. Only relevant for scalable formats.</p>
</td></tr>
<tr valign=top><td><b>ascender</b></td><td>
<p>The typographic ascender of the face, expressed in font units. For font formats not having this information, it is set to &lsquo;bbox.yMax&rsquo;. Only relevant for scalable formats.</p>
</td></tr>
<tr valign=top><td><b>descender</b></td><td>
<p>The typographic descender of the face, expressed in font units. For font formats not having this information, it is set to &lsquo;bbox.yMin&rsquo;. Note that this field is usually negative. Only relevant for scalable formats.</p>
</td></tr>
<tr valign=top><td><b>height</b></td><td>
<p>This value is the vertical distance between two consecutive baselines, expressed in font units. It is always positive. Only relevant for scalable formats.</p>
<p>If you want the global glyph height, use &lsquo;ascender - descender&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>max_advance_width</b></td><td>
<p>The maximum advance width, in font units, for all glyphs in this face. This can be used to make word wrapping computations faster. Only relevant for scalable formats.</p>
</td></tr>
<tr valign=top><td><b>max_advance_height</b></td><td>
<p>The maximum advance height, in font units, for all glyphs in this face. This is only relevant for vertical layouts, and is set to &lsquo;height&rsquo; for fonts that do not provide vertical metrics. Only relevant for scalable formats.</p>
</td></tr>
<tr valign=top><td><b>underline_position</b></td><td>
<p>The position, in font units, of the underline line for this face. It is the center of the underlining stem. Only relevant for scalable formats.</p>
</td></tr>
<tr valign=top><td><b>underline_thickness</b></td><td>
<p>The thickness, in font units, of the underline for this face. Only relevant for scalable formats.</p>
</td></tr>
<tr valign=top><td><b>glyph</b></td><td>
<p>The face's associated glyph slot(s).</p>
</td></tr>
<tr valign=top><td><b>size</b></td><td>
<p>The current active size for this face.</p>
</td></tr>
<tr valign=top><td><b>charmap</b></td><td>
<p>The current active charmap for this face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Fields may be changed after a call to <a href="ft2-base_interface.html#FT_Attach_File">FT_Attach_File</a> or <a href="ft2-base_interface.html#FT_Attach_Stream">FT_Attach_Stream</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_FACE_FLAG_XXX">FT_FACE_FLAG_XXX</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_SCALABLE</a>          ( 1L &lt;&lt;  0 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FIXED_SIZES</a>       ( 1L &lt;&lt;  1 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FIXED_WIDTH</a>       ( 1L &lt;&lt;  2 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_SFNT</a>              ( 1L &lt;&lt;  3 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_HORIZONTAL</a>        ( 1L &lt;&lt;  4 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_VERTICAL</a>          ( 1L &lt;&lt;  5 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_KERNING</a>           ( 1L &lt;&lt;  6 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FAST_GLYPHS</a>       ( 1L &lt;&lt;  7 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_MULTIPLE_MASTERS</a>  ( 1L &lt;&lt;  8 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_GLYPH_NAMES</a>       ( 1L &lt;&lt;  9 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_EXTERNAL_STREAM</a>   ( 1L &lt;&lt; 10 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_HINTER</a>            ( 1L &lt;&lt; 11 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_CID_KEYED</a>         ( 1L &lt;&lt; 12 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_TRICKY</a>            ( 1L &lt;&lt; 13 )
#define <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_COLOR</a>             ( 1L &lt;&lt; 14 )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of bit flags used in the &lsquo;face_flags&rsquo; field of the <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> structure. They inform client applications of properties of the corresponding face.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_FACE_FLAG_SCALABLE</b></td><td>
<p>Indicates that the face contains outline glyphs. This doesn't prevent bitmap strikes, i.e., a face can have both this and and <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FIXED_SIZES</a> set.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FACE_FLAG_FIXED_SIZES</b></td></tr>
<tr valign=top><td></td><td>
<p>Indicates that the face contains bitmap strikes. See also the &lsquo;num_fixed_sizes&rsquo; and &lsquo;available_sizes&rsquo; fields of <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a>.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FACE_FLAG_FIXED_WIDTH</b></td></tr>
<tr valign=top><td></td><td>
<p>Indicates that the face contains fixed-width characters (like Courier, Lucido, MonoType, etc.).</p>
</td></tr>
<tr valign=top><td><b>FT_FACE_FLAG_SFNT</b></td><td>
<p>Indicates that the face uses the &lsquo;sfnt&rsquo; storage scheme. For now, this means TrueType and OpenType.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FACE_FLAG_HORIZONTAL</b></td></tr>
<tr valign=top><td></td><td>
<p>Indicates that the face contains horizontal glyph metrics. This should be set for all common formats.</p>
</td></tr>
<tr valign=top><td><b>FT_FACE_FLAG_VERTICAL</b></td><td>
<p>Indicates that the face contains vertical glyph metrics. This is only available in some formats, not all of them.</p>
</td></tr>
<tr valign=top><td><b>FT_FACE_FLAG_KERNING</b></td><td>
<p>Indicates that the face contains kerning information. If set, the kerning distance can be retrieved through the function <a href="ft2-base_interface.html#FT_Get_Kerning">FT_Get_Kerning</a>. Otherwise the function always return the vector (0,0). Note that FreeType doesn't handle kerning data from the &lsquo;GPOS&rsquo; table (as present in some OpenType fonts).</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FACE_FLAG_FAST_GLYPHS</b></td></tr>
<tr valign=top><td></td><td>
<p>THIS FLAG IS DEPRECATED. DO NOT USE OR TEST IT.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FACE_FLAG_MULTIPLE_MASTERS</b></td></tr>
<tr valign=top><td></td><td>
<p>Indicates that the font contains multiple masters and is capable of interpolating between them. See the multiple-masters specific API for details.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FACE_FLAG_GLYPH_NAMES</b></td></tr>
<tr valign=top><td></td><td>
<p>Indicates that the font contains glyph names that can be retrieved through <a href="ft2-base_interface.html#FT_Get_Glyph_Name">FT_Get_Glyph_Name</a>. Note that some TrueType fonts contain broken glyph name tables. Use the function <a href="ft2-type1_tables.html#FT_Has_PS_Glyph_Names">FT_Has_PS_Glyph_Names</a> when needed.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FACE_FLAG_EXTERNAL_STREAM</b></td></tr>
<tr valign=top><td></td><td>
<p>Used internally by FreeType to indicate that a face's stream was provided by the client application and should not be destroyed when <a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a> is called. Don't read or test this flag.</p>
</td></tr>
<tr valign=top><td><b>FT_FACE_FLAG_HINTER</b></td><td>
<p>Set if the font driver has a hinting machine of its own. For example, with TrueType fonts, it makes sense to use data from the SFNT &lsquo;gasp&rsquo; table only if the native TrueType hinting engine (with the bytecode interpreter) is available and active.</p>
</td></tr>
<tr valign=top><td><b>FT_FACE_FLAG_CID_KEYED</b></td><td>
<p>Set if the font is CID-keyed. In that case, the font is not accessed by glyph indices but by CID values. For subsetted CID-keyed fonts this has the consequence that not all index values are a valid argument to FT_Load_Glyph. Only the CID values for which corresponding glyphs in the subsetted font exist make FT_Load_Glyph return successfully; in all other cases you get an &lsquo;FT_Err_Invalid_Argument&rsquo; error.</p>
<p>Note that CID-keyed fonts that are in an SFNT wrapper don't have this flag set since the glyphs are accessed in the normal way (using contiguous indices); the &lsquo;CID-ness&rsquo; isn't visible to the application.</p>
</td></tr>
<tr valign=top><td><b>FT_FACE_FLAG_TRICKY</b></td><td>
<p>Set if the font is &lsquo;tricky&rsquo;, this is, it always needs the font format's native hinting engine to get a reasonable result. A typical example is the Chinese font &lsquo;mingli.ttf&rsquo; that uses TrueType bytecode instructions to move and scale all of its subglyphs.</p>
<p>It is not possible to autohint such fonts using <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_FORCE_AUTOHINT</a>; it will also ignore <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_HINTING</a>. You have to set both <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_HINTING</a> and <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_AUTOHINT</a> to really disable hinting; however, you probably never want this except for demonstration purposes.</p>
<p>Currently, there are about a dozen TrueType fonts in the list of tricky fonts; they are hard-coded in file &lsquo;ttobjs.c&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>FT_FACE_FLAG_COLOR</b></td><td>
<p>Set if the font has color glyph tables. To access color glyphs use <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_COLOR</a>.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_HAS_HORIZONTAL">FT_HAS_HORIZONTAL</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_HAS_HORIZONTAL</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_HORIZONTAL</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains horizontal metrics (this is true for all font formats though).</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>also</b></em></td></tr><tr><td>
<p><a href="ft2-base_interface.html#FT_HAS_VERTICAL">FT_HAS_VERTICAL</a> can be used to check for vertical metrics.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_HAS_VERTICAL">FT_HAS_VERTICAL</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_HAS_VERTICAL</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_VERTICAL</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains real vertical metrics (and not only synthesized ones).</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_HAS_KERNING">FT_HAS_KERNING</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_HAS_KERNING</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_KERNING</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains kerning data that can be accessed with <a href="ft2-base_interface.html#FT_Get_Kerning">FT_Get_Kerning</a>.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_IS_SCALABLE">FT_IS_SCALABLE</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_IS_SCALABLE</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_SCALABLE</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains a scalable font face (true for TrueType, Type&nbsp;1, Type&nbsp;42, CID, OpenType/CFF, and PFR font formats.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_IS_SFNT">FT_IS_SFNT</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_IS_SFNT</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_SFNT</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains a font whose format is based on the SFNT storage scheme. This usually means: TrueType fonts, OpenType fonts, as well as SFNT-based embedded bitmap fonts.</p>
<p>If this macro is true, all functions defined in <a href="ft2-header_file_macros.html#FT_SFNT_NAMES_H">FT_SFNT_NAMES_H</a> and <a href="ft2-header_file_macros.html#FT_TRUETYPE_TABLES_H">FT_TRUETYPE_TABLES_H</a> are available.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_IS_FIXED_WIDTH">FT_IS_FIXED_WIDTH</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_IS_FIXED_WIDTH</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FIXED_WIDTH</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains a font face that contains fixed-width (or &lsquo;monospace&rsquo;, &lsquo;fixed-pitch&rsquo;, etc.) glyphs.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_HAS_FIXED_SIZES">FT_HAS_FIXED_SIZES</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_HAS_FIXED_SIZES</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_FIXED_SIZES</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains some embedded bitmaps. See the &lsquo;available_sizes&rsquo; field of the <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> structure.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_HAS_FAST_GLYPHS">FT_HAS_FAST_GLYPHS</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_HAS_FAST_GLYPHS</b>( face )  0

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Deprecated.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_HAS_GLYPH_NAMES">FT_HAS_GLYPH_NAMES</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_HAS_GLYPH_NAMES</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_GLYPH_NAMES</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains some glyph names that can be accessed through <a href="ft2-base_interface.html#FT_Get_Glyph_Name">FT_Get_Glyph_Name</a>.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_HAS_MULTIPLE_MASTERS">FT_HAS_MULTIPLE_MASTERS</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_HAS_MULTIPLE_MASTERS</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_MULTIPLE_MASTERS</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains some multiple masters. The functions provided by <a href="ft2-header_file_macros.html#FT_MULTIPLE_MASTERS_H">FT_MULTIPLE_MASTERS_H</a> are then available to choose the exact design you want.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_IS_CID_KEYED">FT_IS_CID_KEYED</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_IS_CID_KEYED</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_CID_KEYED</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains a CID-keyed font. See the discussion of <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_CID_KEYED</a> for more details.</p>
<p>If this macro is true, all functions defined in <a href="ft2-header_file_macros.html#FT_CID_H">FT_CID_H</a> are available.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_IS_TRICKY">FT_IS_TRICKY</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_IS_TRICKY</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_TRICKY</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face represents a &lsquo;tricky&rsquo; font. See the discussion of <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_TRICKY</a> for more details.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_HAS_COLOR">FT_HAS_COLOR</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_HAS_COLOR</b>( face ) \
          ( face-&gt;face_flags &amp; <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_COLOR</a> )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A macro that returns true whenever a face object contains tables for color glyphs.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_STYLE_FLAG_XXX">FT_STYLE_FLAG_XXX</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-base_interface.html#FT_STYLE_FLAG_XXX">FT_STYLE_FLAG_ITALIC</a>  ( 1 &lt;&lt; 0 )
#define <a href="ft2-base_interface.html#FT_STYLE_FLAG_XXX">FT_STYLE_FLAG_BOLD</a>    ( 1 &lt;&lt; 1 )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of bit-flags used to indicate the style of a given face. These are used in the &lsquo;style_flags&rsquo; field of <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_STYLE_FLAG_ITALIC</b></td><td>
<p>Indicates that a given face style is italic or oblique.</p>
</td></tr>
<tr valign=top><td><b>FT_STYLE_FLAG_BOLD</b></td><td>
<p>Indicates that a given face is bold.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The style information as provided by FreeType is very basic. More details are beyond the scope and should be done on a higher level (for example, by analyzing various fields of the &lsquo;OS/2&rsquo; table in SFNT based fonts).</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Size_Internal">FT_Size_Internal</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Size_InternalRec_*  <b>FT_Size_Internal</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An opaque handle to an &lsquo;FT_Size_InternalRec&rsquo; structure, used to model private data of a given <a href="ft2-base_interface.html#FT_Size">FT_Size</a> object.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Size_Metrics">FT_Size_Metrics</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Size_Metrics_
  {
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  x_ppem;      /* horizontal pixels per EM               */
    <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a>  y_ppem;      /* vertical pixels per EM                 */

    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>   x_scale;     /* scaling values used to convert font    */
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>   y_scale;     /* units to 26.6 fractional pixels        */

    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>     ascender;    /* ascender in 26.6 frac. pixels          */
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>     descender;   /* descender in 26.6 frac. pixels         */
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>     height;      /* text height in 26.6 frac. pixels       */
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>     max_advance; /* max horizontal advance, in 26.6 pixels */

  } <b>FT_Size_Metrics</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The size metrics structure gives the metrics of a size object.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>x_ppem</b></td><td>
<p>The width of the scaled EM square in pixels, hence the term &lsquo;ppem&rsquo; (pixels per EM). It is also referred to as &lsquo;nominal width&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>y_ppem</b></td><td>
<p>The height of the scaled EM square in pixels, hence the term &lsquo;ppem&rsquo; (pixels per EM). It is also referred to as &lsquo;nominal height&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>x_scale</b></td><td>
<p>A 16.16 fractional scaling value used to convert horizontal metrics from font units to 26.6 fractional pixels. Only relevant for scalable font formats.</p>
</td></tr>
<tr valign=top><td><b>y_scale</b></td><td>
<p>A 16.16 fractional scaling value used to convert vertical metrics from font units to 26.6 fractional pixels. Only relevant for scalable font formats.</p>
</td></tr>
<tr valign=top><td><b>ascender</b></td><td>
<p>The ascender in 26.6 fractional pixels. See <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> for the details.</p>
</td></tr>
<tr valign=top><td><b>descender</b></td><td>
<p>The descender in 26.6 fractional pixels. See <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> for the details.</p>
</td></tr>
<tr valign=top><td><b>height</b></td><td>
<p>The height in 26.6 fractional pixels. See <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> for the details.</p>
</td></tr>
<tr valign=top><td><b>max_advance</b></td><td>
<p>The maximum advance width in 26.6 fractional pixels. See <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> for the details.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The scaling values, if relevant, are determined first during a size changing operation. The remaining fields are then set by the driver. For scalable formats, they are usually set to scaled values of the corresponding fields in <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a>.</p>
<p>Note that due to glyph hinting, these values might not be exact for certain fonts. Thus they must be treated as unreliable with an error margin of at least one pixel!</p>
<p>Indeed, the only way to get the exact metrics is to render <i>all</i> glyphs. As this would be a definite performance hit, it is up to client applications to perform such computations.</p>
<p>The FT_Size_Metrics structure is valid for bitmap fonts also.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_SizeRec">FT_SizeRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_SizeRec_
  {
    <a href="ft2-base_interface.html#FT_Face">FT_Face</a>           face;      /* parent face object              */
    <a href="ft2-basic_types.html#FT_Generic">FT_Generic</a>        generic;   /* generic pointer for client uses */
    <a href="ft2-base_interface.html#FT_Size_Metrics">FT_Size_Metrics</a>   metrics;   /* size metrics                    */
    <a href="ft2-base_interface.html#FT_Size_Internal">FT_Size_Internal</a>  internal;

  } <b>FT_SizeRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>FreeType root size class structure. A size object models a face object at a given size.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>Handle to the parent face object.</p>
</td></tr>
<tr valign=top><td><b>generic</b></td><td>
<p>A typeless pointer, unused by the FreeType library or any of its drivers. It can be used by client applications to link their own data to each size object.</p>
</td></tr>
<tr valign=top><td><b>metrics</b></td><td>
<p>Metrics for this size object. This field is read-only.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_SubGlyph">FT_SubGlyph</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_SubGlyphRec_*  <b>FT_SubGlyph</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>The subglyph structure is an internal object used to describe subglyphs (for example, in the case of composites).</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The subglyph implementation is not part of the high-level API, hence the forward structure declaration.</p>
<p>You can however retrieve subglyph information with <a href="ft2-base_interface.html#FT_Get_SubGlyph_Info">FT_Get_SubGlyph_Info</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Slot_Internal">FT_Slot_Internal</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Slot_InternalRec_*  <b>FT_Slot_Internal</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An opaque handle to an &lsquo;FT_Slot_InternalRec&rsquo; structure, used to model private data of a given <a href="ft2-base_interface.html#FT_GlyphSlot">FT_GlyphSlot</a> object.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_GlyphSlotRec">FT_GlyphSlotRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_GlyphSlotRec_
  {
    <a href="ft2-base_interface.html#FT_Library">FT_Library</a>        library;
    <a href="ft2-base_interface.html#FT_Face">FT_Face</a>           face;
    <a href="ft2-base_interface.html#FT_GlyphSlot">FT_GlyphSlot</a>      next;
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>           reserved;       /* retained for binary compatibility */
    <a href="ft2-basic_types.html#FT_Generic">FT_Generic</a>        generic;

    <a href="ft2-base_interface.html#FT_Glyph_Metrics">FT_Glyph_Metrics</a>  metrics;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>          linearHoriAdvance;
    <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>          linearVertAdvance;
    <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>         advance;

    <a href="ft2-basic_types.html#FT_Glyph_Format">FT_Glyph_Format</a>   format;

    <a href="ft2-basic_types.html#FT_Bitmap">FT_Bitmap</a>         bitmap;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>            bitmap_left;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>            bitmap_top;

    <a href="ft2-outline_processing.html#FT_Outline">FT_Outline</a>        outline;

    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>           num_subglyphs;
    <a href="ft2-base_interface.html#FT_SubGlyph">FT_SubGlyph</a>       subglyphs;

    <span class="keyword">void</span>*             control_data;
    <span class="keyword">long</span>              control_len;

    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>            lsb_delta;
    <a href="ft2-basic_types.html#FT_Pos">FT_Pos</a>            rsb_delta;

    <span class="keyword">void</span>*             other;

    <a href="ft2-base_interface.html#FT_Slot_Internal">FT_Slot_Internal</a>  internal;

  } <b>FT_GlyphSlotRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>FreeType root glyph slot class structure. A glyph slot is a container where individual glyphs can be loaded, be they in outline or bitmap format.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the FreeType library instance this slot belongs to.</p>
</td></tr>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the parent face object.</p>
</td></tr>
<tr valign=top><td><b>next</b></td><td>
<p>In some cases (like some font tools), several glyph slots per face object can be a good thing. As this is rare, the glyph slots are listed through a direct, single-linked list using its &lsquo;next&rsquo; field.</p>
</td></tr>
<tr valign=top><td><b>generic</b></td><td>
<p>A typeless pointer unused by the FreeType library or any of its drivers. It can be used by client applications to link their own data to each glyph slot object.</p>
</td></tr>
<tr valign=top><td><b>metrics</b></td><td>
<p>The metrics of the last loaded glyph in the slot. The returned values depend on the last load flags (see the <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a> API function) and can be expressed either in 26.6 fractional pixels or font units.</p>
<p>Note that even when the glyph image is transformed, the metrics are not.</p>
</td></tr>
<tr valign=top><td><b>linearHoriAdvance</b></td><td>
<p>The advance width of the unhinted glyph. Its value is expressed in 16.16 fractional pixels, unless <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_LINEAR_DESIGN</a> is set when loading the glyph. This field can be important to perform correct WYSIWYG layout. Only relevant for outline glyphs.</p>
</td></tr>
<tr valign=top><td><b>linearVertAdvance</b></td><td>
<p>The advance height of the unhinted glyph. Its value is expressed in 16.16 fractional pixels, unless <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_LINEAR_DESIGN</a> is set when loading the glyph. This field can be important to perform correct WYSIWYG layout. Only relevant for outline glyphs.</p>
</td></tr>
<tr valign=top><td><b>advance</b></td><td>
<p>This shorthand is, depending on <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_IGNORE_TRANSFORM</a>, the transformed (hinted) advance width for the glyph, in 26.6 fractional pixel format. As specified with <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_VERTICAL_LAYOUT</a>, it uses either the &lsquo;horiAdvance&rsquo; or the &lsquo;vertAdvance&rsquo; value of &lsquo;metrics&rsquo; field.</p>
</td></tr>
<tr valign=top><td><b>format</b></td><td>
<p>This field indicates the format of the image contained in the glyph slot. Typically <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_BITMAP</a>, <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_OUTLINE</a>, or <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_COMPOSITE</a>, but others are possible.</p>
</td></tr>
<tr valign=top><td><b>bitmap</b></td><td>
<p>This field is used as a bitmap descriptor when the slot format is <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_BITMAP</a>. Note that the address and content of the bitmap buffer can change between calls of <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a> and a few other functions.</p>
</td></tr>
<tr valign=top><td><b>bitmap_left</b></td><td>
<p>This is the bitmap's left bearing expressed in integer pixels. Of course, this is only valid if the format is <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_BITMAP</a>.</p>
</td></tr>
<tr valign=top><td><b>bitmap_top</b></td><td>
<p>This is the bitmap's top bearing expressed in integer pixels. Remember that this is the distance from the baseline to the top-most glyph scanline, upwards y&nbsp;coordinates being <b>positive</b>.</p>
</td></tr>
<tr valign=top><td><b>outline</b></td><td>
<p>The outline descriptor for the current glyph image if its format is <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_OUTLINE</a>. Once a glyph is loaded, &lsquo;outline&rsquo; can be transformed, distorted, embolded, etc. However, it must not be freed.</p>
</td></tr>
<tr valign=top><td><b>num_subglyphs</b></td><td>
<p>The number of subglyphs in a composite glyph. This field is only valid for the composite glyph format that should normally only be loaded with the <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_RECURSE</a> flag. For now this is internal to FreeType.</p>
</td></tr>
<tr valign=top><td><b>subglyphs</b></td><td>
<p>An array of subglyph descriptors for composite glyphs. There are &lsquo;num_subglyphs&rsquo; elements in there. Currently internal to FreeType.</p>
</td></tr>
<tr valign=top><td><b>control_data</b></td><td>
<p>Certain font drivers can also return the control data for a given glyph image (e.g. TrueType bytecode, Type&nbsp;1 charstrings, etc.). This field is a pointer to such data.</p>
</td></tr>
<tr valign=top><td><b>control_len</b></td><td>
<p>This is the length in bytes of the control data.</p>
</td></tr>
<tr valign=top><td><b>other</b></td><td>
<p>Really wicked formats can use this pointer to present their own glyph image to client applications. Note that the application needs to know about the image format.</p>
</td></tr>
<tr valign=top><td><b>lsb_delta</b></td><td>
<p>The difference between hinted and unhinted left side bearing while autohinting is active. Zero otherwise.</p>
</td></tr>
<tr valign=top><td><b>rsb_delta</b></td><td>
<p>The difference between hinted and unhinted right side bearing while autohinting is active. Zero otherwise.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a> is called with default flags (see <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_DEFAULT</a>) the glyph image is loaded in the glyph slot in its native format (e.g., an outline glyph for TrueType and Type&nbsp;1 formats).</p>
<p>This image can later be converted into a bitmap by calling <a href="ft2-base_interface.html#FT_Render_Glyph">FT_Render_Glyph</a>. This function finds the current renderer for the native image's format, then invokes it.</p>
<p>The renderer is in charge of transforming the native image through the slot's face transformation fields, then converting it into a bitmap that is returned in &lsquo;slot-&gt;bitmap&rsquo;.</p>
<p>Note that &lsquo;slot-&gt;bitmap_left&rsquo; and &lsquo;slot-&gt;bitmap_top&rsquo; are also used to specify the position of the bitmap relative to the current pen position (e.g., coordinates (0,0) on the baseline). Of course, &lsquo;slot-&gt;format&rsquo; is also changed to <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_BITMAP</a>.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Here a small pseudo code fragment that shows how to use &lsquo;lsb_delta&rsquo; and &lsquo;rsb_delta&rsquo;:</p>
<pre class="colored">
  FT_Pos  origin_x       = 0;                                      
  FT_Pos  prev_rsb_delta = 0;                                      
                                                                   
                                                                   
  for all glyphs do                                                
    &lt;compute kern between current and previous glyph and add it to 
     `origin_x'&gt;                                                   
                                                                   
    &lt;load glyph with `FT_Load_Glyph'&gt;                              
                                                                   
    if ( prev_rsb_delta - face-&gt;glyph-&gt;lsb_delta &gt;= 32 )           
      origin_x -= 64;                                              
    else if ( prev_rsb_delta - face-&gt;glyph-&gt;lsb_delta &lt; -32 )      
      origin_x += 64;                                              
                                                                   
    prev_rsb_delta = face-&gt;glyph-&gt;rsb_delta;                       
                                                                   
    &lt;save glyph image, or render glyph, or ...&gt;                    
                                                                   
    origin_x += face-&gt;glyph-&gt;advance.x;                            
  endfor                                                           
</pre>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Init_FreeType">FT_Init_FreeType</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Init_FreeType</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  *alibrary );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Initialize a new FreeType library object. The set of modules that are registered by this function is determined at build time.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>alibrary</b></td><td>
<p>A handle to a new library object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>In case you want to provide your own memory allocating routines, use <a href="ft2-module_management.html#FT_New_Library">FT_New_Library</a> instead, followed by a call to <a href="ft2-module_management.html#FT_Add_Default_Modules">FT_Add_Default_Modules</a> (or a series of calls to <a href="ft2-module_management.html#FT_Add_Module">FT_Add_Module</a>).</p>
<p>For multi-threading applications each thread should have its own FT_Library object.</p>
<p>If you need reference-counting (cf. <a href="ft2-module_management.html#FT_Reference_Library">FT_Reference_Library</a>), use <a href="ft2-module_management.html#FT_New_Library">FT_New_Library</a> and <a href="ft2-module_management.html#FT_Done_Library">FT_Done_Library</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Done_FreeType">FT_Done_FreeType</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Done_FreeType</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>  library );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Destroy a given FreeType library object and all of its children, including resources, drivers, faces, sizes, etc.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the target library object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_OPEN_XXX">FT_OPEN_XXX</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_MEMORY</a>    0x1
#define <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_STREAM</a>    0x2
#define <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_PATHNAME</a>  0x4
#define <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_DRIVER</a>    0x8
#define <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_PARAMS</a>    0x10

#define <a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_memory</a>    <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_MEMORY</a>     /* deprecated */
#define <a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_stream</a>    <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_STREAM</a>     /* deprecated */
#define <a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_pathname</a>  <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_PATHNAME</a>   /* deprecated */
#define <a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_driver</a>    <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_DRIVER</a>     /* deprecated */
#define <a href="ft2-base_interface.html#FT_OPEN_XXX">ft_open_params</a>    <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_PARAMS</a>     /* deprecated */

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of bit-field constants used within the &lsquo;flags&rsquo; field of the <a href="ft2-base_interface.html#FT_Open_Args">FT_Open_Args</a> structure.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_OPEN_MEMORY</b></td><td>
<p>This is a memory-based stream.</p>
</td></tr>
<tr valign=top><td><b>FT_OPEN_STREAM</b></td><td>
<p>Copy the stream from the &lsquo;stream&rsquo; field.</p>
</td></tr>
<tr valign=top><td><b>FT_OPEN_PATHNAME</b></td><td>
<p>Create a new input stream from a C&nbsp;path name.</p>
</td></tr>
<tr valign=top><td><b>FT_OPEN_DRIVER</b></td><td>
<p>Use the &lsquo;driver&rsquo; field.</p>
</td></tr>
<tr valign=top><td><b>FT_OPEN_PARAMS</b></td><td>
<p>Use the &lsquo;num_params&rsquo; and &lsquo;params&rsquo; fields.</p>
</td></tr>
<tr valign=top><td><b>ft_open_memory</b></td><td>
<p>Deprecated; use <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_MEMORY</a> instead.</p>
</td></tr>
<tr valign=top><td><b>ft_open_stream</b></td><td>
<p>Deprecated; use <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_STREAM</a> instead.</p>
</td></tr>
<tr valign=top><td><b>ft_open_pathname</b></td><td>
<p>Deprecated; use <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_PATHNAME</a> instead.</p>
</td></tr>
<tr valign=top><td><b>ft_open_driver</b></td><td>
<p>Deprecated; use <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_DRIVER</a> instead.</p>
</td></tr>
<tr valign=top><td><b>ft_open_params</b></td><td>
<p>Deprecated; use <a href="ft2-base_interface.html#FT_OPEN_XXX">FT_OPEN_PARAMS</a> instead.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The &lsquo;FT_OPEN_MEMORY&rsquo;, &lsquo;FT_OPEN_STREAM&rsquo;, and &lsquo;FT_OPEN_PATHNAME&rsquo; flags are mutually exclusive.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Parameter">FT_Parameter</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Parameter_
  {
    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>    tag;
    <a href="ft2-basic_types.html#FT_Pointer">FT_Pointer</a>  data;

  } <b>FT_Parameter</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A simple structure used to pass more or less generic parameters to <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>tag</b></td><td>
<p>A four-byte identification tag.</p>
</td></tr>
<tr valign=top><td><b>data</b></td><td>
<p>A pointer to the parameter data.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The ID and function of parameters are driver-specific. See the various FT_PARAM_TAG_XXX flags for more information.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Open_Args">FT_Open_Args</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Open_Args_
  {
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>         flags;
    <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>*  memory_base;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>         memory_size;
    <a href="ft2-basic_types.html#FT_String">FT_String</a>*      pathname;
    <a href="ft2-system_interface.html#FT_Stream">FT_Stream</a>       stream;
    <a href="ft2-base_interface.html#FT_Module">FT_Module</a>       driver;
    <a href="ft2-basic_types.html#FT_Int">FT_Int</a>          num_params;
    <a href="ft2-base_interface.html#FT_Parameter">FT_Parameter</a>*   params;

  } <b>FT_Open_Args</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to indicate how to open a new font file or stream. A pointer to such a structure can be used as a parameter for the functions <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> and <a href="ft2-base_interface.html#FT_Attach_Stream">FT_Attach_Stream</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>flags</b></td><td>
<p>A set of bit flags indicating how to use the structure.</p>
</td></tr>
<tr valign=top><td><b>memory_base</b></td><td>
<p>The first byte of the file in memory.</p>
</td></tr>
<tr valign=top><td><b>memory_size</b></td><td>
<p>The size in bytes of the file in memory.</p>
</td></tr>
<tr valign=top><td><b>pathname</b></td><td>
<p>A pointer to an 8-bit file pathname.</p>
</td></tr>
<tr valign=top><td><b>stream</b></td><td>
<p>A handle to a source stream object.</p>
</td></tr>
<tr valign=top><td><b>driver</b></td><td>
<p>This field is exclusively used by <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a>; it simply specifies the font driver to use to open the face. If set to&nbsp;0, FreeType tries to load the face with each one of the drivers in its list.</p>
</td></tr>
<tr valign=top><td><b>num_params</b></td><td>
<p>The number of extra parameters.</p>
</td></tr>
<tr valign=top><td><b>params</b></td><td>
<p>Extra parameters passed to the font driver when opening a new face.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The stream type is determined by the contents of &lsquo;flags&rsquo; that are tested in the following order by <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a>:</p>
<p>If the &lsquo;FT_OPEN_MEMORY&rsquo; bit is set, assume that this is a memory file of &lsquo;memory_size&rsquo; bytes, located at &lsquo;memory_address&rsquo;. The data are are not copied, and the client is responsible for releasing and destroying them <i>after</i> the corresponding call to <a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a>.</p>
<p>Otherwise, if the &lsquo;FT_OPEN_STREAM&rsquo; bit is set, assume that a custom input stream &lsquo;stream&rsquo; is used.</p>
<p>Otherwise, if the &lsquo;FT_OPEN_PATHNAME&rsquo; bit is set, assume that this is a normal file and use &lsquo;pathname&rsquo; to open it.</p>
<p>If the &lsquo;FT_OPEN_DRIVER&rsquo; bit is set, <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> only tries to open the file with the driver whose handler is in &lsquo;driver&rsquo;.</p>
<p>If the &lsquo;FT_OPEN_PARAMS&rsquo; bit is set, the parameters given by &lsquo;num_params&rsquo; and &lsquo;params&rsquo; is used. They are ignored otherwise.</p>
<p>Ideally, both the &lsquo;pathname&rsquo; and &lsquo;params&rsquo; fields should be tagged as &lsquo;const&rsquo;; this is missing for API backwards compatibility. In other words, applications should treat them as read-only.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_New_Face">FT_New_Face</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_New_Face</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>   library,
               <span class="keyword">const</span> <span class="keyword">char</span>*  filepathname,
               <a href="ft2-basic_types.html#FT_Long">FT_Long</a>      face_index,
               <a href="ft2-base_interface.html#FT_Face">FT_Face</a>     *aface );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function calls <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> to open a font by its pathname.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library resource.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>pathname</b></td><td>
<p>A path to the font file.</p>
</td></tr>
<tr valign=top><td><b>face_index</b></td><td>
<p>The index of the face within the font. The first face has index&nbsp;0.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>aface</b></td><td>
<p>A handle to a new face object. If &lsquo;face_index&rsquo; is greater than or equal to zero, it must be non-NULL. See <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> for more details.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Use <a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a> to destroy the created <a href="ft2-base_interface.html#FT_Face">FT_Face</a> object (along with its slot and sizes).</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_New_Memory_Face">FT_New_Memory_Face</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_New_Memory_Face</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>      library,
                      <span class="keyword">const</span> <a href="ft2-basic_types.html#FT_Byte">FT_Byte</a>*  file_base,
                      <a href="ft2-basic_types.html#FT_Long">FT_Long</a>         file_size,
                      <a href="ft2-basic_types.html#FT_Long">FT_Long</a>         face_index,
                      <a href="ft2-base_interface.html#FT_Face">FT_Face</a>        *aface );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function calls <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> to open a font that has been loaded into memory.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library resource.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>file_base</b></td><td>
<p>A pointer to the beginning of the font data.</p>
</td></tr>
<tr valign=top><td><b>file_size</b></td><td>
<p>The size of the memory chunk used by the font data.</p>
</td></tr>
<tr valign=top><td><b>face_index</b></td><td>
<p>The index of the face within the font. The first face has index&nbsp;0.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>aface</b></td><td>
<p>A handle to a new face object. If &lsquo;face_index&rsquo; is greater than or equal to zero, it must be non-NULL. See <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> for more details.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>You must not deallocate the memory before calling <a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Open_Face">FT_Open_Face</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Open_Face</b>( <a href="ft2-base_interface.html#FT_Library">FT_Library</a>           library,
                <span class="keyword">const</span> <a href="ft2-base_interface.html#FT_Open_Args">FT_Open_Args</a>*  args,
                <a href="ft2-basic_types.html#FT_Long">FT_Long</a>              face_index,
                <a href="ft2-base_interface.html#FT_Face">FT_Face</a>             *aface );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Create a face object from a given resource described by <a href="ft2-base_interface.html#FT_Open_Args">FT_Open_Args</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>library</b></td><td>
<p>A handle to the library resource.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>args</b></td><td>
<p>A pointer to an &lsquo;FT_Open_Args&rsquo; structure that must be filled by the caller.</p>
</td></tr>
<tr valign=top><td><b>face_index</b></td><td>
<p>The index of the face within the font. The first face has index&nbsp;0.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>aface</b></td><td>
<p>A handle to a new face object. If &lsquo;face_index&rsquo; is greater than or equal to zero, it must be non-NULL. See note below.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Unlike FreeType 1.x, this function automatically creates a glyph slot for the face object that can be accessed directly through &lsquo;face-&gt;glyph&rsquo;.</p>
<p>FT_Open_Face can be used to quickly check whether the font format of a given font resource is supported by FreeType. If the &lsquo;face_index&rsquo; field is negative, the function's return value is&nbsp;0 if the font format is recognized, or non-zero otherwise; the function returns a more or less empty face handle in &lsquo;*aface&rsquo; (if &lsquo;aface&rsquo; isn't NULL). The only useful field in this special case is &lsquo;face-&gt;num_faces&rsquo; that gives the number of faces within the font file. After examination, the returned <a href="ft2-base_interface.html#FT_Face">FT_Face</a> structure should be deallocated with a call to <a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a>.</p>
<p>Each new face object created with this function also owns a default <a href="ft2-base_interface.html#FT_Size">FT_Size</a> object, accessible as &lsquo;face-&gt;size&rsquo;.</p>
<p>One <a href="ft2-base_interface.html#FT_Library">FT_Library</a> instance can have multiple face objects, this is, <a href="ft2-base_interface.html#FT_Open_Face">FT_Open_Face</a> and its siblings can be called multiple times using the same &lsquo;library&rsquo; argument.</p>
<p>See the discussion of reference counters in the description of <a href="ft2-base_interface.html#FT_Reference_Face">FT_Reference_Face</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Attach_File">FT_Attach_File</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Attach_File</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>      face,
                  <span class="keyword">const</span> <span class="keyword">char</span>*  filepathname );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function calls <a href="ft2-base_interface.html#FT_Attach_Stream">FT_Attach_Stream</a> to attach a file.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>The target face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>filepathname</b></td><td>
<p>The pathname.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Attach_Stream">FT_Attach_Stream</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Attach_Stream</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>        face,
                    <a href="ft2-base_interface.html#FT_Open_Args">FT_Open_Args</a>*  parameters );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>&lsquo;Attach&rsquo; data to a face object. Normally, this is used to read additional information for the face object. For example, you can attach an AFM file that comes with a Type&nbsp;1 font to get the kerning values and other metrics.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>The target face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>parameters</b></td><td>
<p>A pointer to <a href="ft2-base_interface.html#FT_Open_Args">FT_Open_Args</a> that must be filled by the caller.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The meaning of the &lsquo;attach&rsquo; (i.e., what really happens when the new file is read) is not fixed by FreeType itself. It really depends on the font format (and thus the font driver).</p>
<p>Client applications are expected to know what they are doing when invoking this function. Most drivers simply do not implement file attachments.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Reference_Face">FT_Reference_Face</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Reference_Face</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A counter gets initialized to&nbsp;1 at the time an <a href="ft2-base_interface.html#FT_Face">FT_Face</a> structure is created. This function increments the counter. <a href="ft2-base_interface.html#FT_Done_Face">FT_Done_Face</a> then only destroys a face if the counter is&nbsp;1, otherwise it simply decrements the counter.</p>
<p>This function helps in managing life-cycles of structures that reference <a href="ft2-base_interface.html#FT_Face">FT_Face</a> objects.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a target face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.4.2</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Done_Face">FT_Done_Face</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Done_Face</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Discard a given face object, as well as all of its child slots and sizes.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a target face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>See the discussion of reference counters in the description of <a href="ft2-base_interface.html#FT_Reference_Face">FT_Reference_Face</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Select_Size">FT_Select_Size</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Select_Size</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face,
                  <a href="ft2-basic_types.html#FT_Int">FT_Int</a>   strike_index );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Select a bitmap strike.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a target face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>strike_index</b></td><td>
<p>The index of the bitmap strike in the &lsquo;available_sizes&rsquo; field of <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> structure.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Size_Request_Type">FT_Size_Request_Type</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Size_Request_Type_
  {
    <a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_NOMINAL</a>,
    <a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_REAL_DIM</a>,
    <a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_BBOX</a>,
    <a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_CELL</a>,
    <a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_SCALES</a>,

    FT_SIZE_REQUEST_TYPE_MAX

  } <b>FT_Size_Request_Type</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An enumeration type that lists the supported size request types.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td colspan=0><b>FT_SIZE_REQUEST_TYPE_NOMINAL</b></td></tr>
<tr valign=top><td></td><td>
<p>The nominal size. The &lsquo;units_per_EM&rsquo; field of <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> is used to determine both scaling values.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_SIZE_REQUEST_TYPE_REAL_DIM</b></td></tr>
<tr valign=top><td></td><td>
<p>The real dimension. The sum of the the &lsquo;ascender&rsquo; and (minus of) the &lsquo;descender&rsquo; fields of <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> are used to determine both scaling values.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_SIZE_REQUEST_TYPE_BBOX</b></td></tr>
<tr valign=top><td></td><td>
<p>The font bounding box. The width and height of the &lsquo;bbox&rsquo; field of <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> are used to determine the horizontal and vertical scaling value, respectively.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_SIZE_REQUEST_TYPE_CELL</b></td></tr>
<tr valign=top><td></td><td>
<p>The &lsquo;max_advance_width&rsquo; field of <a href="ft2-base_interface.html#FT_FaceRec">FT_FaceRec</a> is used to determine the horizontal scaling value; the vertical scaling value is determined the same way as <a href="ft2-base_interface.html#FT_Size_Request_Type">FT_SIZE_REQUEST_TYPE_REAL_DIM</a> does. Finally, both scaling values are set to the smaller one. This type is useful if you want to specify the font size for, say, a window of a given dimension and 80x24 cells.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_SIZE_REQUEST_TYPE_SCALES</b></td></tr>
<tr valign=top><td></td><td>
<p>Specify the scaling values directly.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The above descriptions only apply to scalable formats. For bitmap formats, the behaviour is up to the driver.</p>
<p>See the note section of <a href="ft2-base_interface.html#FT_Size_Metrics">FT_Size_Metrics</a> if you wonder how size requesting relates to scaling values.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Size_RequestRec">FT_Size_RequestRec</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span>  FT_Size_RequestRec_
  {
    <a href="ft2-base_interface.html#FT_Size_Request_Type">FT_Size_Request_Type</a>  type;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>               width;
    <a href="ft2-basic_types.html#FT_Long">FT_Long</a>               height;
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>               horiResolution;
    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>               vertResolution;

  } <b>FT_Size_RequestRec</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A structure used to model a size request.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>fields</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>type</b></td><td>
<p>See <a href="ft2-base_interface.html#FT_Size_Request_Type">FT_Size_Request_Type</a>.</p>
</td></tr>
<tr valign=top><td><b>width</b></td><td>
<p>The desired width.</p>
</td></tr>
<tr valign=top><td><b>height</b></td><td>
<p>The desired height.</p>
</td></tr>
<tr valign=top><td><b>horiResolution</b></td><td>
<p>The horizontal resolution. If set to zero, &lsquo;width&rsquo; is treated as a 26.6 fractional pixel value.</p>
</td></tr>
<tr valign=top><td><b>vertResolution</b></td><td>
<p>The vertical resolution. If set to zero, &lsquo;height&rsquo; is treated as a 26.6 fractional pixel value.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If &lsquo;width&rsquo; is zero, then the horizontal scaling value is set equal to the vertical scaling value, and vice versa.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Size_Request">FT_Size_Request</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">struct</span> FT_Size_RequestRec_  *<b>FT_Size_Request</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A handle to a size request structure.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Request_Size">FT_Request_Size</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Request_Size</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>          face,
                   <a href="ft2-base_interface.html#FT_Size_Request">FT_Size_Request</a>  req );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Resize the scale of the active <a href="ft2-base_interface.html#FT_Size">FT_Size</a> object in a face.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a target face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>req</b></td><td>
<p>A pointer to a <a href="ft2-base_interface.html#FT_Size_RequestRec">FT_Size_RequestRec</a>.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Although drivers may select the bitmap strike matching the request, you should not rely on this if you intend to select a particular bitmap strike. Use <a href="ft2-base_interface.html#FT_Select_Size">FT_Select_Size</a> instead in that case.</p>
<p>The relation between the requested size and the resulting glyph size is dependent entirely on how the size is defined in the source face. The font designer chooses the final size of each glyph relative to this size. For more information refer to &lsquo;<a href="http://www.freetype.org/freetype2/docs/glyphs/glyphs-2.html">http://www.freetype.org/freetype2/docs/glyphs/glyphs-2.html</a>&rsquo;</p>
<p>Don't use this function if you are using the FreeType cache API.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_Char_Size">FT_Set_Char_Size</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Set_Char_Size</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>     face,
                    <a href="ft2-basic_types.html#FT_F26Dot6">FT_F26Dot6</a>  char_width,
                    <a href="ft2-basic_types.html#FT_F26Dot6">FT_F26Dot6</a>  char_height,
                    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     horz_resolution,
                    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     vert_resolution );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function calls <a href="ft2-base_interface.html#FT_Request_Size">FT_Request_Size</a> to request the nominal size (in points).</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a target face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>char_width</b></td><td>
<p>The nominal width, in 26.6 fractional points.</p>
</td></tr>
<tr valign=top><td><b>char_height</b></td><td>
<p>The nominal height, in 26.6 fractional points.</p>
</td></tr>
<tr valign=top><td><b>horz_resolution</b></td><td>
<p>The horizontal resolution in dpi.</p>
</td></tr>
<tr valign=top><td><b>vert_resolution</b></td><td>
<p>The vertical resolution in dpi.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If either the character width or height is zero, it is set equal to the other value.</p>
<p>If either the horizontal or vertical resolution is zero, it is set equal to the other value.</p>
<p>A character width or height smaller than 1pt is set to 1pt; if both resolution values are zero, they are set to 72dpi.</p>
<p>Don't use this function if you are using the FreeType cache API.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_Pixel_Sizes">FT_Set_Pixel_Sizes</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Set_Pixel_Sizes</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face,
                      <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>  pixel_width,
                      <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>  pixel_height );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function calls <a href="ft2-base_interface.html#FT_Request_Size">FT_Request_Size</a> to request the nominal size (in pixels).</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the target face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>pixel_width</b></td><td>
<p>The nominal width, in pixels.</p>
</td></tr>
<tr valign=top><td><b>pixel_height</b></td><td>
<p>The nominal height, in pixels.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>You should not rely on the resulting glyphs matching, or being constrained, to this pixel size. Refer to <a href="ft2-base_interface.html#FT_Request_Size">FT_Request_Size</a> to understand how requested sizes relate to actual sizes.</p>
<p>Don't use this function if you are using the FreeType cache API.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Load_Glyph">FT_Load_Glyph</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Load_Glyph</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>   face,
                 <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>   glyph_index,
                 <a href="ft2-basic_types.html#FT_Int32">FT_Int32</a>  load_flags );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function used to load a single glyph into the glyph slot of a face object.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the target face object where the glyph is loaded.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>glyph_index</b></td><td>
<p>The index of the glyph in the font file. For CID-keyed fonts (either in PS or in CFF format) this argument specifies the CID value.</p>
</td></tr>
<tr valign=top><td><b>load_flags</b></td><td>
<p>A flag indicating what to load for this glyph. The <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_XXX</a> constants can be used to control the glyph loading process (e.g., whether the outline should be scaled, whether to load bitmaps or not, whether to hint the outline, etc).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The loaded glyph may be transformed. See <a href="ft2-base_interface.html#FT_Set_Transform">FT_Set_Transform</a> for the details.</p>
<p>For subsetted CID-keyed fonts, &lsquo;FT_Err_Invalid_Argument&rsquo; is returned for invalid CID values (this is, for CID values that don't have a corresponding glyph in the font). See the discussion of the <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_CID_KEYED</a> flag for more details.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Load_Char">FT_Load_Char</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Load_Char</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>   face,
                <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>  char_code,
                <a href="ft2-basic_types.html#FT_Int32">FT_Int32</a>  load_flags );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function used to load a single glyph into the glyph slot of a face object, according to its character code.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a target face object where the glyph is loaded.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>char_code</b></td><td>
<p>The glyph's character code, according to the current charmap used in the face.</p>
</td></tr>
<tr valign=top><td><b>load_flags</b></td><td>
<p>A flag indicating what to load for this glyph. The <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_XXX</a> constants can be used to control the glyph loading process (e.g., whether the outline should be scaled, whether to load bitmaps or not, whether to hint the outline, etc).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function simply calls <a href="ft2-base_interface.html#FT_Get_Char_Index">FT_Get_Char_Index</a> and <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a>.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_LOAD_XXX">FT_LOAD_XXX</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_DEFAULT</a>                      0x0
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a>                     ( 1L &lt;&lt; 0 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_HINTING</a>                   ( 1L &lt;&lt; 1 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_RENDER</a>                       ( 1L &lt;&lt; 2 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_BITMAP</a>                    ( 1L &lt;&lt; 3 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_VERTICAL_LAYOUT</a>              ( 1L &lt;&lt; 4 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_FORCE_AUTOHINT</a>               ( 1L &lt;&lt; 5 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_CROP_BITMAP</a>                  ( 1L &lt;&lt; 6 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_PEDANTIC</a>                     ( 1L &lt;&lt; 7 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_IGNORE_GLOBAL_ADVANCE_WIDTH</a>  ( 1L &lt;&lt; 9 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_RECURSE</a>                   ( 1L &lt;&lt; 10 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_IGNORE_TRANSFORM</a>             ( 1L &lt;&lt; 11 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_MONOCHROME</a>                   ( 1L &lt;&lt; 12 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_LINEAR_DESIGN</a>                ( 1L &lt;&lt; 13 )
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_AUTOHINT</a>                  ( 1L &lt;&lt; 15 )
  /* Bits 16..19 are used by `FT_LOAD_TARGET_' */
#define <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_COLOR</a>                        ( 1L &lt;&lt; 20 )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of bit-field constants used with <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a> to indicate what kind of operations to perform during glyph loading.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_LOAD_DEFAULT</b></td><td>
<p>Corresponding to&nbsp;0, this value is used as the default glyph load operation. In this case, the following happens:</p>
<p>1. FreeType looks for a bitmap for the glyph corresponding to the face's current size. If one is found, the function returns. The bitmap data can be accessed from the glyph slot (see note below).</p>
<p>2. If no embedded bitmap is searched or found, FreeType looks for a scalable outline. If one is found, it is loaded from the font file, scaled to device pixels, then &lsquo;hinted&rsquo; to the pixel grid in order to optimize it. The outline data can be accessed from the glyph slot (see note below).</p>
<p>Note that by default, the glyph loader doesn't render outlines into bitmaps. The following flags are used to modify this default behaviour to more specific and useful cases.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_NO_SCALE</b></td><td>
<p>Don't scale the loaded outline glyph but keep it in font units.</p>
<p>This flag implies <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_HINTING</a> and <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_BITMAP</a>, and unsets <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_RENDER</a>.</p>
<p>If the font is &lsquo;tricky&rsquo; (see <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_TRICKY</a> for more), using FT_LOAD_NO_SCALE usually yields meaningless outlines because the subglyphs must be scaled and positioned with hinting instructions. This can be solved by loading the font without FT_LOAD_NO_SCALE and setting the character size to &lsquo;font-&gt;units_per_EM&rsquo;.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_NO_HINTING</b></td><td>
<p>Disable hinting. This generally generates &lsquo;blurrier&rsquo; bitmap glyphs when the glyph are rendered in any of the anti-aliased modes. See also the note below.</p>
<p>This flag is implied by <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a>.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_RENDER</b></td><td>
<p>Call <a href="ft2-base_interface.html#FT_Render_Glyph">FT_Render_Glyph</a> after the glyph is loaded. By default, the glyph is rendered in <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_NORMAL</a> mode. This can be overridden by <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_XXX</a> or <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_MONOCHROME</a>.</p>
<p>This flag is unset by <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a>.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_NO_BITMAP</b></td><td>
<p>Ignore bitmap strikes when loading. Bitmap-only fonts ignore this flag.</p>
<p><a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a> always sets this flag.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_LOAD_VERTICAL_LAYOUT</b></td></tr>
<tr valign=top><td></td><td>
<p>Load the glyph for vertical text layout. In particular, the &lsquo;advance&rsquo; value in the <a href="ft2-base_interface.html#FT_GlyphSlotRec">FT_GlyphSlotRec</a> structure is set to the &lsquo;vertAdvance&rsquo; value of the &lsquo;metrics&rsquo; field.</p>
<p>In case <a href="ft2-base_interface.html#FT_HAS_VERTICAL">FT_HAS_VERTICAL</a> doesn't return true, you shouldn't use this flag currently. Reason is that in this case vertical metrics get synthesized, and those values are not always consistent across various font formats.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_FORCE_AUTOHINT</b></td><td>
<p>Indicates that the auto-hinter is preferred over the font's native hinter. See also the note below.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_CROP_BITMAP</b></td><td>
<p>Indicates that the font driver should crop the loaded bitmap glyph (i.e., remove all space around its black bits). Not all drivers implement this.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_PEDANTIC</b></td><td>
<p>Indicates that the font driver should perform pedantic verifications during glyph loading. This is mostly used to detect broken glyphs in fonts. By default, FreeType tries to handle broken fonts also.</p>
<p>In particular, errors from the TrueType bytecode engine are not passed to the application if this flag is not set; this might result in partially hinted or distorted glyphs in case a glyph's bytecode is buggy.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_LOAD_IGNORE_GLOBAL_ADVANCE_WIDTH</b></td></tr>
<tr valign=top><td></td><td>
<p>Ignored. Deprecated.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_NO_RECURSE</b></td><td>
<p>This flag is only used internally. It merely indicates that the font driver should not load composite glyphs recursively. Instead, it should set the &lsquo;num_subglyph&rsquo; and &lsquo;subglyphs&rsquo; values of the glyph slot accordingly, and set &lsquo;glyph-&gt;format&rsquo; to <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_COMPOSITE</a>.</p>
<p>The description of sub-glyphs is not available to client applications for now.</p>
<p>This flag implies <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_SCALE</a> and <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_IGNORE_TRANSFORM</a>.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_LOAD_IGNORE_TRANSFORM</b></td></tr>
<tr valign=top><td></td><td>
<p>Indicates that the transform matrix set by <a href="ft2-base_interface.html#FT_Set_Transform">FT_Set_Transform</a> should be ignored.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_MONOCHROME</b></td><td>
<p>This flag is used with <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_RENDER</a> to indicate that you want to render an outline glyph to a 1-bit monochrome bitmap glyph, with 8&nbsp;pixels packed into each byte of the bitmap data.</p>
<p>Note that this has no effect on the hinting algorithm used. You should rather use <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_MONO</a> so that the monochrome-optimized hinting algorithm is used.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_LINEAR_DESIGN</b></td><td>
<p>Indicates that the &lsquo;linearHoriAdvance&rsquo; and &lsquo;linearVertAdvance&rsquo; fields of <a href="ft2-base_interface.html#FT_GlyphSlotRec">FT_GlyphSlotRec</a> should be kept in font units. See <a href="ft2-base_interface.html#FT_GlyphSlotRec">FT_GlyphSlotRec</a> for details.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_NO_AUTOHINT</b></td><td>
<p>Disable auto-hinter. See also the note below.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_COLOR</b></td><td>
<p>This flag is used to request loading of color embedded-bitmap images. The resulting color bitmaps, if available, will have the <a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_BGRA</a> format. When the flag is not used and color bitmaps are found, they will be converted to 256-level gray bitmaps transparently. Those bitmaps will be in the <a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_GRAY</a> format.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>By default, hinting is enabled and the font's native hinter (see <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_HINTER</a>) is preferred over the auto-hinter. You can disable hinting by setting <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_HINTING</a> or change the precedence by setting <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_FORCE_AUTOHINT</a>. You can also set <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_NO_AUTOHINT</a> in case you don't want the auto-hinter to be used at all.</p>
<p>See the description of <a href="ft2-base_interface.html#FT_FACE_FLAG_XXX">FT_FACE_FLAG_TRICKY</a> for a special exception (affecting only a handful of Asian fonts).</p>
<p>Besides deciding which hinter to use, you can also decide which hinting algorithm to use. See <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_XXX</a> for details.</p>
<p>Note that the auto-hinter needs a valid Unicode cmap (either a native one or synthesized by FreeType) for producing correct results. If a font provides an incorrect mapping (for example, assigning the character code U+005A, LATIN CAPITAL LETTER Z, to a glyph depicting a mathematical integral sign), the auto-hinter might produce useless results.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_XXX</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define FT_LOAD_TARGET_( x )   ( (<a href="ft2-basic_types.html#FT_Int32">FT_Int32</a>)( (x) &amp; 15 ) &lt;&lt; 16 )

#define <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_NORMAL</a>  FT_LOAD_TARGET_( <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_NORMAL</a> )
#define <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_LIGHT</a>   FT_LOAD_TARGET_( <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LIGHT</a>  )
#define <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_MONO</a>    FT_LOAD_TARGET_( <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_MONO</a>   )
#define <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_LCD</a>     FT_LOAD_TARGET_( <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LCD</a>    )
#define <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_LCD_V</a>   FT_LOAD_TARGET_( <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LCD_V</a>  )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of values that are used to select a specific hinting algorithm to use by the hinter. You should OR one of these values to your &lsquo;load_flags&rsquo; when calling <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a>.</p>
<p>Note that font's native hinters may ignore the hinting algorithm you have specified (e.g., the TrueType bytecode interpreter). You can set <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_FORCE_AUTOHINT</a> to ensure that the auto-hinter is used.</p>
<p>Also note that <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_LIGHT</a> is an exception, in that it always implies <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_FORCE_AUTOHINT</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_LOAD_TARGET_NORMAL</b></td><td>
<p>This corresponds to the default hinting algorithm, optimized for standard gray-level rendering. For monochrome output, use <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_MONO</a> instead.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_TARGET_LIGHT</b></td><td>
<p>A lighter hinting algorithm for non-monochrome modes. Many generated glyphs are more fuzzy but better resemble its original shape. A bit like rendering on Mac OS&nbsp;X.</p>
<p>As a special exception, this target implies <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_FORCE_AUTOHINT</a>.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_TARGET_MONO</b></td><td>
<p>Strong hinting algorithm that should only be used for monochrome output. The result is probably unpleasant if the glyph is rendered in non-monochrome modes.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_TARGET_LCD</b></td><td>
<p>A variant of <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_NORMAL</a> optimized for horizontally decimated LCD displays.</p>
</td></tr>
<tr valign=top><td><b>FT_LOAD_TARGET_LCD_V</b></td><td>
<p>A variant of <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_NORMAL</a> optimized for vertically decimated LCD displays.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>You should use only <i>one</i> of the FT_LOAD_TARGET_XXX values in your &lsquo;load_flags&rsquo;. They can't be ORed.</p>
<p>If <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_RENDER</a> is also set, the glyph is rendered in the corresponding mode (i.e., the mode that matches the used algorithm best). An exeption is FT_LOAD_TARGET_MONO since it implies <a href="ft2-base_interface.html#FT_LOAD_XXX">FT_LOAD_MONOCHROME</a>.</p>
<p>You can use a hinting algorithm that doesn't correspond to the same rendering mode. As an example, it is possible to use the &lsquo;light&rsquo; hinting algorithm and have the results rendered in horizontal LCD pixel mode, with code like</p>
<pre class="colored">
  FT_Load_Glyph( face, glyph_index,
                 load_flags | FT_LOAD_TARGET_LIGHT );

  FT_Render_Glyph( face-&gt;glyph, FT_RENDER_MODE_LCD );
</pre>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_LOAD_TARGET_MODE">FT_LOAD_TARGET_MODE</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>FT_LOAD_TARGET_MODE</b>( x )  ( (<a href="ft2-base_interface.html#FT_Render_Mode">FT_Render_Mode</a>)( ( (x) &gt;&gt; 16 ) &amp; 15 ) )

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the <a href="ft2-base_interface.html#FT_Render_Mode">FT_Render_Mode</a> corresponding to a given <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_XXX</a> value.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_Transform">FT_Set_Transform</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">void</span> )
  <b>FT_Set_Transform</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>     face,
                    <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>*  matrix,
                    <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>*  delta );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A function used to set the transformation that is applied to glyph images when they are loaded into a glyph slot through <a href="ft2-base_interface.html#FT_Load_Glyph">FT_Load_Glyph</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>matrix</b></td><td>
<p>A pointer to the transformation's 2x2 matrix. Use&nbsp;0 for the identity matrix.</p>
</td></tr>
<tr valign=top><td><b>delta</b></td><td>
<p>A pointer to the translation vector. Use&nbsp;0 for the null vector.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The transformation is only applied to scalable image formats after the glyph has been loaded. It means that hinting is unaltered by the transformation and is performed on the character size given in the last call to <a href="ft2-base_interface.html#FT_Set_Char_Size">FT_Set_Char_Size</a> or <a href="ft2-base_interface.html#FT_Set_Pixel_Sizes">FT_Set_Pixel_Sizes</a>.</p>
<p>Note that this also transforms the &lsquo;face.glyph.advance&rsquo; field, but <b>not</b> the values in &lsquo;face.glyph.metrics&rsquo;.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Render_Mode">FT_Render_Mode</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Render_Mode_
  {
    <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_NORMAL</a> = 0,
    <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LIGHT</a>,
    <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_MONO</a>,
    <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LCD</a>,
    <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_LCD_V</a>,

    FT_RENDER_MODE_MAX

  } <b>FT_Render_Mode</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An enumeration type that lists the render modes supported by FreeType&nbsp;2. Each mode corresponds to a specific type of scanline conversion performed on the outline.</p>
<p>For bitmap fonts and embedded bitmaps the &lsquo;bitmap-&gt;pixel_mode&rsquo; field in the <a href="ft2-base_interface.html#FT_GlyphSlotRec">FT_GlyphSlotRec</a> structure gives the format of the returned bitmap.</p>
<p>All modes except <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_MONO</a> use 256 levels of opacity.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_RENDER_MODE_NORMAL</b></td><td>
<p>This is the default render mode; it corresponds to 8-bit anti-aliased bitmaps.</p>
</td></tr>
<tr valign=top><td><b>FT_RENDER_MODE_LIGHT</b></td><td>
<p>This is equivalent to <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_NORMAL</a>. It is only defined as a separate value because render modes are also used indirectly to define hinting algorithm selectors. See <a href="ft2-base_interface.html#FT_LOAD_TARGET_XXX">FT_LOAD_TARGET_XXX</a> for details.</p>
</td></tr>
<tr valign=top><td><b>FT_RENDER_MODE_MONO</b></td><td>
<p>This mode corresponds to 1-bit bitmaps (with 2&nbsp;levels of opacity).</p>
</td></tr>
<tr valign=top><td><b>FT_RENDER_MODE_LCD</b></td><td>
<p>This mode corresponds to horizontal RGB and BGR sub-pixel displays like LCD screens. It produces 8-bit bitmaps that are 3&nbsp;times the width of the original glyph outline in pixels, and which use the <a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_LCD</a> mode.</p>
</td></tr>
<tr valign=top><td><b>FT_RENDER_MODE_LCD_V</b></td><td>
<p>This mode corresponds to vertical RGB and BGR sub-pixel displays (like PDA screens, rotated LCD displays, etc.). It produces 8-bit bitmaps that are 3&nbsp;times the height of the original glyph outline in pixels and use the <a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_LCD_V</a> mode.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The LCD-optimized glyph bitmaps produced by FT_Render_Glyph can be filtered to reduce color-fringes by using <a href="ft2-lcd_filtering.html#FT_Library_SetLcdFilter">FT_Library_SetLcdFilter</a> (not active in the default builds). It is up to the caller to either call <a href="ft2-lcd_filtering.html#FT_Library_SetLcdFilter">FT_Library_SetLcdFilter</a> (if available) or do the filtering itself.</p>
<p>The selected render mode only affects vector glyphs of a font. Embedded bitmaps often have a different pixel mode like <a href="ft2-basic_types.html#FT_Pixel_Mode">FT_PIXEL_MODE_MONO</a>. You can use <a href="ft2-bitmap_handling.html#FT_Bitmap_Convert">FT_Bitmap_Convert</a> to transform them into 8-bit pixmaps.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="ft_render_mode_xxx">ft_render_mode_xxx</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-base_interface.html#ft_render_mode_xxx">ft_render_mode_normal</a>  <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_NORMAL</a>
#define <a href="ft2-base_interface.html#ft_render_mode_xxx">ft_render_mode_mono</a>    <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_MONO</a>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>These constants are deprecated. Use the corresponding <a href="ft2-base_interface.html#FT_Render_Mode">FT_Render_Mode</a> values instead.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>ft_render_mode_normal</b></td><td>
<p>see <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_NORMAL</a></p>
</td></tr>
<tr valign=top><td><b>ft_render_mode_mono</b></td><td>
<p>see <a href="ft2-base_interface.html#FT_Render_Mode">FT_RENDER_MODE_MONO</a></p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Render_Glyph">FT_Render_Glyph</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Render_Glyph</b>( <a href="ft2-base_interface.html#FT_GlyphSlot">FT_GlyphSlot</a>    slot,
                   <a href="ft2-base_interface.html#FT_Render_Mode">FT_Render_Mode</a>  render_mode );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Convert a given glyph image to a bitmap. It does so by inspecting the glyph image format, finding the relevant renderer, and invoking it.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>slot</b></td><td>
<p>A handle to the glyph slot containing the image to convert.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>render_mode</b></td><td>
<p>This is the render mode used to render the glyph image into a bitmap. See <a href="ft2-base_interface.html#FT_Render_Mode">FT_Render_Mode</a> for a list of possible values.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>To get meaningful results, font scaling values must be set with functions like <a href="ft2-base_interface.html#FT_Set_Char_Size">FT_Set_Char_Size</a> before calling FT_Render_Glyph.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Kerning_Mode">FT_Kerning_Mode</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  <span class="keyword">typedef</span> <span class="keyword">enum</span>  FT_Kerning_Mode_
  {
    <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_DEFAULT</a>  = 0,
    <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_UNFITTED</a>,
    <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_UNSCALED</a>

  } <b>FT_Kerning_Mode</b>;

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>An enumeration used to specify which kerning values to return in <a href="ft2-base_interface.html#FT_Get_Kerning">FT_Get_Kerning</a>.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>FT_KERNING_DEFAULT</b></td><td>
<p>Return scaled and grid-fitted kerning distances (value is&nbsp;0).</p>
</td></tr>
<tr valign=top><td><b>FT_KERNING_UNFITTED</b></td><td>
<p>Return scaled but un-grid-fitted kerning distances.</p>
</td></tr>
<tr valign=top><td><b>FT_KERNING_UNSCALED</b></td><td>
<p>Return the kerning vector in original font units.</p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="ft_kerning_default">ft_kerning_default</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>ft_kerning_default</b>   <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_DEFAULT</a>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This constant is deprecated. Please use <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_DEFAULT</a> instead.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="ft_kerning_unfitted">ft_kerning_unfitted</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>ft_kerning_unfitted</b>  <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_UNFITTED</a>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This constant is deprecated. Please use <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_UNFITTED</a> instead.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="ft_kerning_unscaled">ft_kerning_unscaled</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <b>ft_kerning_unscaled</b>  <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_UNSCALED</a>

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This constant is deprecated. Please use <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_KERNING_UNSCALED</a> instead.</p>
</td></tr></table><br>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Kerning">FT_Get_Kerning</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_Kerning</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>     face,
                  <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     left_glyph,
                  <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     right_glyph,
                  <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     kern_mode,
                  <a href="ft2-basic_types.html#FT_Vector">FT_Vector</a>  *akerning );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the kerning vector between two glyphs of a same face.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a source face object.</p>
</td></tr>
<tr valign=top><td><b>left_glyph</b></td><td>
<p>The index of the left glyph in the kern pair.</p>
</td></tr>
<tr valign=top><td><b>right_glyph</b></td><td>
<p>The index of the right glyph in the kern pair.</p>
</td></tr>
<tr valign=top><td><b>kern_mode</b></td><td>
<p>See <a href="ft2-base_interface.html#FT_Kerning_Mode">FT_Kerning_Mode</a> for more information. Determines the scale and dimension of the returned kerning vector.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>akerning</b></td><td>
<p>The kerning vector. This is either in font units or in pixels (26.6 format) for scalable formats, and in pixels for fixed-sizes formats.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Only horizontal layouts (left-to-right &amp; right-to-left) are supported by this method. Other layouts, or more sophisticated kernings, are out of the scope of this API function -- they can be implemented through format-specific interfaces.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Track_Kerning">FT_Get_Track_Kerning</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_Track_Kerning</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>    face,
                        <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>   point_size,
                        <a href="ft2-basic_types.html#FT_Int">FT_Int</a>     degree,
                        <a href="ft2-basic_types.html#FT_Fixed">FT_Fixed</a>*  akerning );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the track kerning for a given face object at a given size.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a source face object.</p>
</td></tr>
<tr valign=top><td><b>point_size</b></td><td>
<p>The point size in 16.16 fractional points.</p>
</td></tr>
<tr valign=top><td><b>degree</b></td><td>
<p>The degree of tightness. Increasingly negative values represent tighter track kerning, while increasingly positive values represent looser track kerning. Value zero means no track kerning.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>akerning</b></td><td>
<p>The kerning in 16.16 fractional points, to be uniformly applied between all glyphs.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Currently, only the Type&nbsp;1 font driver supports track kerning, using data from AFM files (if attached with <a href="ft2-base_interface.html#FT_Attach_File">FT_Attach_File</a> or <a href="ft2-base_interface.html#FT_Attach_Stream">FT_Attach_Stream</a>).</p>
<p>Only very few AFM files come with track kerning data; please refer to the Adobe's AFM specification for more details.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Glyph_Name">FT_Get_Glyph_Name</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_Glyph_Name</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>     face,
                     <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     glyph_index,
                     <a href="ft2-basic_types.html#FT_Pointer">FT_Pointer</a>  buffer,
                     <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>     buffer_max );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the ASCII name of a given glyph in a face. This only works for those faces where <a href="ft2-base_interface.html#FT_HAS_GLYPH_NAMES">FT_HAS_GLYPH_NAMES</a>(face) returns&nbsp;1.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to a source face object.</p>
</td></tr>
<tr valign=top><td><b>glyph_index</b></td><td>
<p>The glyph index.</p>
</td></tr>
<tr valign=top><td><b>buffer_max</b></td><td>
<p>The maximum number of bytes available in the buffer.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>buffer</b></td><td>
<p>A pointer to a target buffer where the name is copied to.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>An error is returned if the face doesn't provide glyph names or if the glyph index is invalid. In all cases of failure, the first byte of &lsquo;buffer&rsquo; is set to&nbsp;0 to indicate an empty name.</p>
<p>The glyph name is truncated to fit within the buffer if it is too long. The returned string is always zero-terminated.</p>
<p>Be aware that FreeType reorders glyph indices internally so that glyph index&nbsp;0 always corresponds to the &lsquo;missing glyph&rsquo; (called &lsquo;.notdef&rsquo;).</p>
<p>This function is not compiled within the library if the config macro &lsquo;FT_CONFIG_OPTION_NO_GLYPH_NAMES&rsquo; is defined in &lsquo;ftoptions.h&rsquo;.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Postscript_Name">FT_Get_Postscript_Name</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <span class="keyword">const</span> <span class="keyword">char</span>* )
  <b>FT_Get_Postscript_Name</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve the ASCII PostScript name of a given face, if available. This only works with PostScript and TrueType fonts.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>A pointer to the face's PostScript name. NULL if unavailable.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The returned pointer is owned by the face and is destroyed with it.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Select_Charmap">FT_Select_Charmap</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Select_Charmap</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>      face,
                     <a href="ft2-base_interface.html#FT_Encoding">FT_Encoding</a>  encoding );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Select a given charmap by its encoding tag (as listed in &lsquo;freetype.h&rsquo;).</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>encoding</b></td><td>
<p>A handle to the selected encoding.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function returns an error if no charmap in the face corresponds to the encoding queried here.</p>
<p>Because many fonts contain more than a single cmap for Unicode encoding, this function has some special code to select the one that covers Unicode best (&lsquo;best&rsquo; in the sense that a UCS-4 cmap is preferred to a UCS-2 cmap). It is thus preferable to <a href="ft2-base_interface.html#FT_Set_Charmap">FT_Set_Charmap</a> in this case.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Set_Charmap">FT_Set_Charmap</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Set_Charmap</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>     face,
                  <a href="ft2-base_interface.html#FT_CharMap">FT_CharMap</a>  charmap );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Select a given charmap for character code to glyph index mapping.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>inout</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>charmap</b></td><td>
<p>A handle to the selected charmap.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>This function returns an error if the charmap is not part of the face (i.e., if it is not listed in the &lsquo;face-&gt;charmaps&rsquo; table).</p>
<p>It also fails if a type&nbsp;14 charmap is selected.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Charmap_Index">FT_Get_Charmap_Index</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Int">FT_Int</a> )
  <b>FT_Get_Charmap_Index</b>( <a href="ft2-base_interface.html#FT_CharMap">FT_CharMap</a>  charmap );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve index of a given charmap.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>charmap</b></td><td>
<p>A handle to a charmap.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The index into the array of character maps within the face to which &lsquo;charmap&rsquo; belongs. If an error occurs, -1 is returned.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Char_Index">FT_Get_Char_Index</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a> )
  <b>FT_Get_Char_Index</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>   face,
                     <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>  charcode );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the glyph index of a given character code. This function uses a charmap object to do the mapping.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
<tr valign=top><td><b>charcode</b></td><td>
<p>The character code.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The glyph index. 0&nbsp;means &lsquo;undefined character code&rsquo;.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>If you use FreeType to manipulate the contents of font files directly, be aware that the glyph index returned by this function doesn't always correspond to the internal indices used within the file. This is done to ensure that value&nbsp;0 always corresponds to the &lsquo;missing glyph&rsquo;. If the first glyph is not named &lsquo;.notdef&rsquo;, then for Type&nbsp;1 and Type&nbsp;42 fonts, &lsquo;.notdef&rsquo; will be moved into the glyph ID&nbsp;0 position, and whatever was there will be moved to the position &lsquo;.notdef&rsquo; had. For Type&nbsp;1 fonts, if there is no &lsquo;.notdef&rsquo; glyph at all, then one will be created at index&nbsp;0 and whatever was there will be moved to the last index -- Type&nbsp;42 fonts are considered invalid under this condition.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_First_Char">FT_Get_First_Char</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a> )
  <b>FT_Get_First_Char</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>   face,
                     <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>  *agindex );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function is used to return the first character code in the current charmap of a given face. It also returns the corresponding glyph index.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>agindex</b></td><td>
<p>Glyph index of first character code. 0&nbsp;if charmap is empty.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The charmap's first character code.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>You should use this function with <a href="ft2-base_interface.html#FT_Get_Next_Char">FT_Get_Next_Char</a> to be able to parse all character codes available in a given charmap. The code should look like this:</p>
<pre class="colored">
  FT_ULong  charcode;                                              
  FT_UInt   gindex;                                                
                                                                   
                                                                   
  charcode = FT_Get_First_Char( face, &amp;gindex );                   
  while ( gindex != 0 )                                            
  {                                                                
    ... do something with (charcode,gindex) pair ...               
                                                                   
    charcode = FT_Get_Next_Char( face, charcode, &amp;gindex );        
  }                                                                
</pre>
<p>Note that &lsquo;*agindex&rsquo; is set to&nbsp;0 if the charmap is empty. The result itself can be&nbsp;0 in two cases: if the charmap is empty or if the value&nbsp;0 is the first valid character code.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Next_Char">FT_Get_Next_Char</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a> )
  <b>FT_Get_Next_Char</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>    face,
                    <a href="ft2-basic_types.html#FT_ULong">FT_ULong</a>   char_code,
                    <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>   *agindex );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>This function is used to return the next character code in the current charmap of a given face following the value &lsquo;char_code&rsquo;, as well as the corresponding glyph index.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
<tr valign=top><td><b>char_code</b></td><td>
<p>The starting character code.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>agindex</b></td><td>
<p>Glyph index of next character code. 0&nbsp;if charmap is empty.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The charmap's next character code.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>You should use this function with <a href="ft2-base_interface.html#FT_Get_First_Char">FT_Get_First_Char</a> to walk over all character codes available in a given charmap. See the note for this function for a simple code example.</p>
<p>Note that &lsquo;*agindex&rsquo; is set to&nbsp;0 when there are no more codes in the charmap.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_Name_Index">FT_Get_Name_Index</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a> )
  <b>FT_Get_Name_Index</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>     face,
                     <a href="ft2-basic_types.html#FT_String">FT_String</a>*  glyph_name );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the glyph index of a given glyph name. This function uses driver specific objects to do the translation.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
<tr valign=top><td><b>glyph_name</b></td><td>
<p>The glyph name.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The glyph index. 0&nbsp;means &lsquo;undefined character code&rsquo;.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_XXX</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_ARGS_ARE_WORDS</a>          1
#define <a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_ARGS_ARE_XY_VALUES</a>      2
#define <a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_ROUND_XY_TO_GRID</a>        4
#define <a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_SCALE</a>                   8
#define <a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_XY_SCALE</a>             0x40
#define <a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_2X2</a>                  0x80
#define <a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_USE_MY_METRICS</a>      0x200

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of constants used to describe subglyphs. Please refer to the TrueType specification for the meaning of the various flags.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td colspan=0><b>FT_SUBGLYPH_FLAG_ARGS_ARE_WORDS</b></td></tr>
<tr valign=top><td></td><td>
<p></p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_SUBGLYPH_FLAG_ARGS_ARE_XY_VALUES</b></td></tr>
<tr valign=top><td></td><td>
<p></p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_SUBGLYPH_FLAG_ROUND_XY_TO_GRID</b></td></tr>
<tr valign=top><td></td><td>
<p></p>
</td></tr>
<tr valign=top><td><b>FT_SUBGLYPH_FLAG_SCALE</b></td><td>
<p></p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_SUBGLYPH_FLAG_XY_SCALE</b></td></tr>
<tr valign=top><td></td><td>
<p></p>
</td></tr>
<tr valign=top><td><b>FT_SUBGLYPH_FLAG_2X2</b></td><td>
<p></p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_SUBGLYPH_FLAG_USE_MY_METRICS</b></td></tr>
<tr valign=top><td></td><td>
<p></p>
</td></tr>
</table>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_SubGlyph_Info">FT_Get_SubGlyph_Info</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_Error">FT_Error</a> )
  <b>FT_Get_SubGlyph_Info</b>( <a href="ft2-base_interface.html#FT_GlyphSlot">FT_GlyphSlot</a>  glyph,
                        <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>       sub_index,
                        <a href="ft2-basic_types.html#FT_Int">FT_Int</a>       *p_index,
                        <a href="ft2-basic_types.html#FT_UInt">FT_UInt</a>      *p_flags,
                        <a href="ft2-basic_types.html#FT_Int">FT_Int</a>       *p_arg1,
                        <a href="ft2-basic_types.html#FT_Int">FT_Int</a>       *p_arg2,
                        <a href="ft2-basic_types.html#FT_Matrix">FT_Matrix</a>    *p_transform );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Retrieve a description of a given subglyph. Only use it if &lsquo;glyph-&gt;format&rsquo; is <a href="ft2-basic_types.html#FT_Glyph_Format">FT_GLYPH_FORMAT_COMPOSITE</a>; an error is returned otherwise.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>glyph</b></td><td>
<p>The source glyph slot.</p>
</td></tr>
<tr valign=top><td><b>sub_index</b></td><td>
<p>The index of the subglyph. Must be less than &lsquo;glyph-&gt;num_subglyphs&rsquo;.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>output</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>p_index</b></td><td>
<p>The glyph index of the subglyph.</p>
</td></tr>
<tr valign=top><td><b>p_flags</b></td><td>
<p>The subglyph flags, see <a href="ft2-base_interface.html#FT_SUBGLYPH_FLAG_XXX">FT_SUBGLYPH_FLAG_XXX</a>.</p>
</td></tr>
<tr valign=top><td><b>p_arg1</b></td><td>
<p>The subglyph's first argument (if any).</p>
</td></tr>
<tr valign=top><td><b>p_arg2</b></td><td>
<p>The subglyph's second argument (if any).</p>
</td></tr>
<tr valign=top><td><b>p_transform</b></td><td>
<p>The subglyph transformation (if any).</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>FreeType error code. 0&nbsp;means success.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>The values of &lsquo;*p_arg1&rsquo;, &lsquo;*p_arg2&rsquo;, and &lsquo;*p_transform&rsquo; must be interpreted depending on the flags returned in &lsquo;*p_flags&rsquo;. See the TrueType specification for details.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_FSTYPE_XXX">FT_FSTYPE_XXX</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

#define <a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_INSTALLABLE_EMBEDDING</a>         0x0000
#define <a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_RESTRICTED_LICENSE_EMBEDDING</a>  0x0002
#define <a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_PREVIEW_AND_PRINT_EMBEDDING</a>   0x0004
#define <a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_EDITABLE_EMBEDDING</a>            0x0008
#define <a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_NO_SUBSETTING</a>                 0x0100
#define <a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_BITMAP_EMBEDDING_ONLY</a>         0x0200

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>A list of bit flags used in the &lsquo;fsType&rsquo; field of the OS/2 table in a TrueType or OpenType font and the &lsquo;FSType&rsquo; entry in a PostScript font. These bit flags are returned by <a href="ft2-base_interface.html#FT_Get_FSType_Flags">FT_Get_FSType_Flags</a>; they inform client applications of embedding and subsetting restrictions associated with a font.</p>
<p>See <a href="http://www.adobe.com/devnet/acrobat/pdfs/FontPolicies.pdf">http://www.adobe.com/devnet/acrobat/pdfs/FontPolicies.pdf</a> for more details.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>values</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td colspan=0><b>FT_FSTYPE_INSTALLABLE_EMBEDDING</b></td></tr>
<tr valign=top><td></td><td>
<p>Fonts with no fsType bit set may be embedded and permanently installed on the remote system by an application.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FSTYPE_RESTRICTED_LICENSE_EMBEDDING</b></td></tr>
<tr valign=top><td></td><td>
<p>Fonts that have only this bit set must not be modified, embedded or exchanged in any manner without first obtaining permission of the font software copyright owner.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FSTYPE_PREVIEW_AND_PRINT_EMBEDDING</b></td></tr>
<tr valign=top><td></td><td>
<p>If this bit is set, the font may be embedded and temporarily loaded on the remote system. Documents containing Preview &amp; Print fonts must be opened &lsquo;read-only&rsquo;; no edits can be applied to the document.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FSTYPE_EDITABLE_EMBEDDING</b></td></tr>
<tr valign=top><td></td><td>
<p>If this bit is set, the font may be embedded but must only be installed temporarily on other systems. In contrast to Preview &amp; Print fonts, documents containing editable fonts may be opened for reading, editing is permitted, and changes may be saved.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FSTYPE_NO_SUBSETTING</b></td></tr>
<tr valign=top><td></td><td>
<p>If this bit is set, the font may not be subsetted prior to embedding.</p>
</td></tr>
<tr valign=top><td colspan=0><b>FT_FSTYPE_BITMAP_EMBEDDING_ONLY</b></td></tr>
<tr valign=top><td></td><td>
<p>If this bit is set, only bitmaps contained in the font may be embedded; no outline data may be embedded. If there are no bitmaps available in the font, then the font is unembeddable.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>While the fsType flags can indicate that a font may be embedded, a license with the font vendor may be separately required to use the font in this way.</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

<table align=center width="75%"><tr><td>
<h4><a name="FT_Get_FSType_Flags">FT_Get_FSType_Flags</a></h4>
<table align=center width="87%"><tr><td>
Defined in FT_FREETYPE_H (freetype.h).
</td></tr></table><br>
<table align=center width="87%"><tr bgcolor="#D6E8FF"><td><pre>

  FT_EXPORT( <a href="ft2-basic_types.html#FT_UShort">FT_UShort</a> )
  <b>FT_Get_FSType_Flags</b>( <a href="ft2-base_interface.html#FT_Face">FT_Face</a>  face );

</pre></table><br>
<table align=center width="87%"><tr><td>
<p>Return the fsType flags for a font.</p>
</td></tr></table><br>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>input</b></em></td></tr><tr><td>
<p></p>
<table cellpadding=3 border=0>
<tr valign=top><td><b>face</b></td><td>
<p>A handle to the source face object.</p>
</td></tr>
</table>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>return</b></em></td></tr><tr><td>
<p>The fsType flags, <a href="ft2-base_interface.html#FT_FSTYPE_XXX">FT_FSTYPE_XXX</a>.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>note</b></em></td></tr><tr><td>
<p>Use this function rather than directly reading the &lsquo;fs_type&rsquo; field in the <a href="ft2-type1_tables.html#PS_FontInfoRec">PS_FontInfoRec</a> structure, which is only guaranteed to return the correct results for Type&nbsp;1 fonts.</p>
</td></tr></table>
<table align=center width="87%" cellpadding=5><tr bgcolor="#EEEEFF"><td><em><b>since</b></em></td></tr><tr><td>
<p>2.3.8</p>
</td></tr></table>
</td></tr></table>
<hr width="75%">
<table align=center width="75%"><tr><td><font size=-2>[<a href="ft2-index.html">Index</a>]</font></td>
<td width="100%"></td>
<td><font size=-2>[<a href="ft2-toc.html">TOC</a>]</font></td></tr></table>

</body>
</html>
