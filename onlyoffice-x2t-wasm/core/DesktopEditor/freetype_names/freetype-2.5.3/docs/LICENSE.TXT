
The  FreeType 2  font  engine is  copyrighted  work and  cannot be  used
legally  without a  software license.   In  order to  make this  project
usable  to a vast  majority of  developers, we  distribute it  under two
mutually exclusive open-source licenses.

This means  that *you* must choose  *one* of the  two licenses described
below, then obey  all its terms and conditions when  using FreeType 2 in
any of your projects or products.

  - The FreeType License, found in  the file `FTL.TXT', which is similar
    to the original BSD license *with* an advertising clause that forces
    you  to  explicitly cite  the  FreeType  project  in your  product's
    documentation.  All  details are in the license  file.  This license
    is  suited  to products  which  don't  use  the GNU  General  Public
    License.

    Note that  this license  is  compatible  to the  GNU General  Public
    License version 3, but not version 2.

  - The GNU General Public License version 2, found in  `GPLv2.TXT' (any
    later version can be used  also), for programs which already use the
    GPL.  Note  that the  FTL is  incompatible  with  GPLv2 due  to  its
    advertisement clause.

The contributed BDF and PCF drivers come with a license similar  to that
of the X Window System.  It is compatible to the above two licenses (see
file src/bdf/README and src/pcf/README).

The gzip module uses the zlib license (see src/gzip/zlib.h) which too is
compatible to the above two licenses.

The MD5 checksum support (only used for debugging in development builds)
is in the public domain.


--- end of LICENSE.TXT ---
