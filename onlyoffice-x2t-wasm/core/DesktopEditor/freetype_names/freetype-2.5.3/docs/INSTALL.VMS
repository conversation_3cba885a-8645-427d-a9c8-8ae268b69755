How to build the freetype2 library on VMS
-----------------------------------------

It is actually very  straightforward to install the Freetype2 library.
Just  execute vms_make.com from  the toplevel  directory to  build the
library.  This procedure currently accepts the following options:

DEBUG
  Build the library with debug information and without optimization.

lopts=<value>
  Options to pass to the link command e.g. lopts=/traceback

ccopt=<value>
  Options to pass to the C compiler e.g. ccopt=/float=ieee

In case you did download the demos, place them in a separate directory
sharing the same toplevel as the directory of Freetype2 and follow the
same  instructions as  above  for  the demos  from  there.  The  build
process relies on this to figure the location of the Freetype2 include
files.


To rebuild  the  sources it is necessary to  have MMS/MMK installed on
the system.

The library is available in the directory

  [.LIB]

To  compile applications  using  FreeType  2 you  have  to define  the
logical FREETYPE pointing to the directory

  [.INCLUDE.FREETYPE]

i.e., if  the directory in which  this INSTALL.VMS file  is located is
$disk:[freetype] then define the logical with

  define freetype $disk:[freetype.include.freetype]

This version has  been tested with Compaq C  V6.2-006 on OpenVMS Alpha
V7.2-1.


  Any problems can be reported to

    <PERSON><PERSON> <<EMAIL>> or
    <PERSON> P.J. <PERSON>inser <<EMAIL>>

------------------------------------------------------------------------

Copyright 2000, 2004 by
David Turner, Robert Wilhelm, and Werner Lemberg.

This  file  is  part  of the  FreeType  project, and may  only be  used,
modified,  and  distributed  under  the  terms of  the FreeType  project
license, LICENSE.TXT.   By continuing to use, modify, or distribute this
file you  indicate that  you have  read the  license and understand  and
accept it fully.


--- end of INSTALL.VMS ---
