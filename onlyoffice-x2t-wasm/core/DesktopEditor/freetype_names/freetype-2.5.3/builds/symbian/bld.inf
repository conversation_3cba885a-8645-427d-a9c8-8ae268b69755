//
// FreeType 2 project for the symbian platform
//

// Copyright 2008, 2009, 2013 by
// <PERSON>, <PERSON>, and <PERSON>.
//
// This file is part of the FreeType project, and may only be used, modified,
// and distributed under the terms of the FreeType project license,
// LICENSE.TXT.  By continuing to use, modify, or distribute this file you
// indicate that you have read the license and understand and accept it
// fully.

PRJ_PLATFORMS
DEFAULT

PRJ_MMPFILES
freetype.mmp

PRJ_EXPORTS
../../include/ft2build.h
../../include/config/ftconfig.h	config/ftconfig.h
../../include/config/ftheader.h	config/ftheader.h
../../include/config/ftmodule.h	config/ftmodule.h
../../include/config/ftoption.h	config/ftoption.h
../../include/config/ftstdlib.h	config/ftstdlib.h
../../include/freetype.h	freetype.h
../../include/ftbbox.h		ftbbox.h
../../include/ftbdf.h		ftbdf.h
../../include/ftbitmap.h	ftbitmap.h
../../include/ftcache.h		ftcache.h
../../include/ftcid.h		ftcid.h
../../include/fterrdef.h	fterrdef.h
../../include/fterrors.h	fterrors.h
../../include/ftgasp.h		ftgasp.h
../../include/ftglyph.h		ftglyph.h
../../include/ftgxval.h		ftgxval.h
../../include/ftgzip.h		ftgzip.h
../../include/ftbzip2.h		ftbzip2.h
../../include/ftimage.h		ftimage.h
../../include/ftincrem.h	ftincrem.h
../../include/ftlcdfil.h	ftlcdfil.h
../../include/ftlist.h		ftlist.h
../../include/ftlzw.h		ftlzw.h
../../include/ftmac.h		ftmac.h
../../include/ftmm.h		ftmm.h
../../include/ftmodapi.h	ftmodapi.h
../../include/ftmoderr.h	ftmoderr.h
../../include/ftotval.h		ftotval.h
../../include/ftoutln.h		ftoutln.h
../../include/ftpfr.h		ftpfr.h
../../include/ftrender.h	ftrender.h
../../include/ftsizes.h		ftsizes.h
../../include/ftsnames.h	ftsnames.h
../../include/ftstroke.h	ftstroke.h
../../include/ftsynth.h		ftsynth.h
../../include/ftsystem.h	ftsystem.h
../../include/fttrigon.h	fttrigon.h
../../include/fttypes.h		fttypes.h
../../include/ftwinfnt.h	ftwinfnt.h
../../include/ftxf86.h		ftxf86.h
../../include/t1tables.h	t1tables.h
../../include/ttnameid.h	ttnameid.h
../../include/tttables.h	tttables.h
../../include/tttags.h		tttags.h
../../include/ttunpat.h		ttunpat.h
