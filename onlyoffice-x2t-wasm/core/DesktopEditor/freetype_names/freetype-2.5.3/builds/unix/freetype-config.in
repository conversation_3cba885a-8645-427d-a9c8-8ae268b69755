#! /bin/sh
#
# Copyright 2000-2005, 2008, 2009, 2013, 2014 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

LC_ALL=C
export LC_ALL

prefix="%prefix%"
exec_prefix="%exec_prefix%"
exec_prefix_set="no"
includedir="%includedir%"
libdir="%libdir%"
enable_shared="%build_libtool_libs%"

usage()
{
  cat <<EOF
Usage: freetype-config [OPTION]...
Get FreeType compilation and linking information.

Options:
  --prefix               display \`--prefix' value used for building the
                         FreeType library
  --prefix=PREFIX        override \`--prefix' value with PREFIX
  --exec-prefix          display \`--exec-prefix' value used for building
                         the FreeType library
  --exec-prefix=EPREFIX  override \`--exec-prefix' value with EPREFIX
  --version              display libtool version of the FreeType library
  --ftversion            display FreeType version number
  --libs                 display flags for linking with the FreeType library
  --libtool              display library name for linking with libtool
  --cflags               display flags for compiling with the FreeType
                         library
  --static               make command line options display flags
                         for static linking
EOF
  exit $1
}

if test $# -eq 0 ; then
  usage 1 1>&2
fi

while test $# -gt 0 ; do
  case "$1" in
  -*=*)
    optarg=`echo "$1" | sed 's/[-_a-zA-Z0-9]*=//'`
    ;;
  *)
    optarg=
    ;;
  esac

  case $1 in
  --prefix=*)
    prefix=$optarg
    local_prefix=yes
    ;;
  --prefix)
    echo_prefix=yes
    ;;
  --exec-prefix=*)
    exec_prefix=$optarg
    exec_prefix_set=yes
    local_prefix=yes
    ;;
  --exec-prefix)
    echo_exec_prefix=yes
    ;;
  --version)
    echo %ft_version%
    exit 0
    ;;
  --ftversion)
    echo_ft_version=yes
    ;;
  --cflags)
    echo_cflags=yes
    ;;
  --libs)
    echo_libs=yes
    ;;
  --libtool)
    echo_libtool=yes
    ;;
  --static)
    show_static=yes
    ;;
  *)
    usage 1 1>&2
    ;;
  esac
  shift
done

if test "$local_prefix" = "yes" ; then
  if test "$exec_prefix_set" != "yes" ; then
    exec_prefix=$prefix
  fi
fi

if test "$echo_prefix" = "yes" ; then
  echo ${SYSROOT}$prefix
fi

if test "$echo_exec_prefix" = "yes" ; then
  echo ${SYSROOT}$exec_prefix
fi

if test "$exec_prefix_set" = "yes" ; then
  libdir=$exec_prefix/lib
else
  if test "$local_prefix" = "yes" ; then
    includedir=$prefix/include
    libdir=$prefix/lib
  fi
fi

if test "$echo_ft_version" = "yes" ; then
  major=`grep define ${SYSROOT}$includedir/freetype2/freetype.h \
         | grep FREETYPE_MAJOR \
         | sed 's/.*[ 	]\([0-9][0-9]*\).*/\1/'`
  minor=`grep define ${SYSROOT}$includedir/freetype2/freetype.h \
         | grep FREETYPE_MINOR \
         | sed 's/.*[ 	]\([0-9][0-9]*\).*/\1/'`
  patch=`grep define ${SYSROOT}$includedir/freetype2/freetype.h \
         | grep FREETYPE_PATCH \
         | sed 's/.*[ 	]\([0-9][0-9]*\).*/\1/'`
  echo $major.$minor.$patch
fi

if test "$echo_cflags" = "yes" ; then
  cflags="-I${SYSROOT}$includedir/freetype2"
  echo $cflags
fi

if test "$echo_libs" = "yes" ; then
  libs="%LIBS_CONFIG%"
  staticlibs="%LIBSSTATIC_CONFIG%"
  if test "$show_static" = "yes" ; then
    libs="$staticlibs"
  fi
  if test "${SYSROOT}$libdir" != "/usr/lib"  &&
     test "${SYSROOT}$libdir" != "/usr/lib64"; then
    echo -L${SYSROOT}$libdir $libs
  else
    echo $libs
  fi
fi

if test "$echo_libtool" = "yes" ; then
  convlib="libfreetype.la"
  echo ${SYSROOT}$libdir/$convlib
fi

# EOF
