<html>
<header>
<title>
  FreeType&nbsp;2 Project Files for Visual&nbsp;C++ and VS.NET&nbsp;2005
</title>

<body>
<h1>
  FreeType&nbsp;2 Project Files for Visual&nbsp;C++ and VS.NET&nbsp;2005
</h1>

<p>This directory contains project files for Visual C++, named
<tt>freetype.dsp</tt>, and Visual Studio, called <tt>freetype.sln</tt>.  It
compiles the following libraries from the FreeType 2.5.3 sources:</p>

<ul>
  <pre>
    freetype253.lib     - release build; single threaded
    freetype253_D.lib   - debug build;   single threaded
    freetype253MT.lib   - release build; multi-threaded
    freetype253MT_D.lib - debug build;   multi-threaded</pre>
</ul>

<p>Be sure to extract the files with the Windows (CR+LF) line endings.  ZIP
archives are already stored this way, so no further action is required.  If
you use some <tt>.tar.*z</tt> archives, be sure to configure your extracting
tool to convert the line endings.  For example, with <a
href="http://www.winzip.com">WinZip</a>, you should activate the <it>TAR
file smart CR/LF Conversion</it> option.  Alternatively, you may consider
using the <tt>unix2dos</tt> or <tt>u2d</tt> utilities that are floating
around, which specifically deal with this particular problem.

<p>Build directories are placed in the top-level <tt>objs</tt>
directory.</p>

</body>
</html>
