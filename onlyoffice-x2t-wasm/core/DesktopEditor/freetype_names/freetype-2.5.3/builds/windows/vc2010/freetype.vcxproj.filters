﻿<?xml version="1.0" encoding="utf-8"?>

<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <ItemGroup>

    <Filter Include="Source Files">

      <UniqueIdentifier>{b4c15893-ec11-491d-9507-0ac184f9cc78}</UniqueIdentifier>

      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>

    </Filter>

    <Filter Include="Source Files\FT_MODULES">

      <UniqueIdentifier>{4d3e4eff-3fbc-4b20-b413-2743b23b7109}</UniqueIdentifier>

    </Filter>

    <Filter Include="Header Files">

      <UniqueIdentifier>{e6cf6a0f-0404-4024-8bf8-ff5b29f35657}</UniqueIdentifier>

      <Extensions>h;hpp;hxx;hm;inl</Extensions>

    </Filter>

  </ItemGroup>

  <ItemGroup>

    <ClCompile Include="..\..\..\src\autofit\autofit.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\bdf\bdf.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\cff\cff.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftbase.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftbitmap.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\cache\ftcache.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\ftdebug.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftfstype.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftgasp.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftglyph.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\gzip\ftgzip.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftinit.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\lzw\ftlzw.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftstroke.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftsystem.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\smooth\smooth.c">

      <Filter>Source Files</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftbbox.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftmm.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftpfr.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftsynth.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\fttype1.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftwinfnt.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftxf86.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftlcdfil.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftgxval.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftotval.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\base\ftpatent.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\pcf\pcf.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\pfr\pfr.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\psaux\psaux.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\pshinter\pshinter.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\psnames\psmodule.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\raster\raster.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\sfnt\sfnt.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\truetype\truetype.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\type1\type1.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\cid\type1cid.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\type42\type42.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

    <ClCompile Include="..\..\..\src\winfonts\winfnt.c">

      <Filter>Source Files\FT_MODULES</Filter>

    </ClCompile>

  </ItemGroup>

  <ItemGroup>

    <ClInclude Include="..\..\..\include\ft2build.h">

      <Filter>Header Files</Filter>

    </ClInclude>

    <ClInclude Include="..\..\..\include\config\ftconfig.h">

      <Filter>Header Files</Filter>

    </ClInclude>

    <ClInclude Include="..\..\..\include\config\ftheader.h">

      <Filter>Header Files</Filter>

    </ClInclude>

    <ClInclude Include="..\..\..\include\config\ftmodule.h">

      <Filter>Header Files</Filter>

    </ClInclude>

    <ClInclude Include="..\..\..\include\config\ftoption.h">

      <Filter>Header Files</Filter>

    </ClInclude>

    <ClInclude Include="..\..\..\include\config\ftstdlib.h">

      <Filter>Header Files</Filter>

    </ClInclude>

  </ItemGroup>

</Project>

