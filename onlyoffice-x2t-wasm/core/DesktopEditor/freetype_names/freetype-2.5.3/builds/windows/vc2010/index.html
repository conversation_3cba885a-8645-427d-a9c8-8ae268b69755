<html>
<header>
<title>
  FreeType&nbsp;2 Project Files for VS.NET&nbsp;2010 or newer
</title>

<body>
<h1>
  FreeType&nbsp;2 Project Files for VS.NET&nbsp;2010 or newer
</h1>

<p>This directory contains a project file for Visual C++ (VS.NET&nbsp;2010
or newer), named <tt>freetype.vcxproj</tt>, and Visual Studio, called
<tt>freetype.sln</tt>.  It compiles the following libraries from the
FreeType 2.5.3 sources:</p>

<ul>
  <pre>
freetype253.lib     - release build
freetype253_D.lib   - debug build
freetype253ST.lib   - release build; single threaded
freetype253ST_D.lib - debug build;   single threaded
freetype253MT.lib   - release build; multi-threaded
freetype253MT_D.lib - debug build;   multi-threaded</pre>
</ul>

<p>Both Win32 and x64 builds are supported.</p>

<p>Be sure to extract the files with the Windows (CR+LF) line endings.  ZIP
archives are already stored this way, so no further action is required.  If
you use some <tt>.tar.*z</tt> archives, be sure to configure your extracting
tool to convert the line endings.  For example, with <a
href="http://www.winzip.com">WinZip</a>, you should activate the <it>TAR
file smart CR/LF Conversion</it> option.  Alternatively, you may consider
using the <tt>unix2dos</tt> or <tt>u2d</tt> utilities that are floating
around, which specifically deal with this particular problem.

<p>Build directories are placed in the top-level <tt>objs</tt>
directory.</p>

</body>
</html>
