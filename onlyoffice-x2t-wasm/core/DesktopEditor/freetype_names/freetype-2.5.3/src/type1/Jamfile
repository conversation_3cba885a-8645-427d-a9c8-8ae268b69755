# FreeType 2 src/type1 Jamfile
#
# Copyright 2001 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) type1 ;

{
  local  _sources ;

  if $(FT2_MULTI)
  {
    _sources = t1afm t1driver t1objs t1load t1gload t1parse ;
  }
  else
  {
    _sources = type1 ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# end of src/type1 Jamfile
