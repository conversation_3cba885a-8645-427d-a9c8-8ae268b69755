# FreeType 2 src Jamfile
#
# Copyright 2001, 2002, 2013 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) ;

# The file <internal/internal.h> is used to define macros that are
# later used in #include statements.  It needs to be parsed in order to
# record these definitions.
#
HDRMACRO  [ FT2_SubDir $(FT2_INCLUDE_DIR) internal internal.h ] ;

for xx in $(FT2_COMPONENTS)
{
  SubInclude FT2_TOP $(FT2_SRC_DIR) $(xx) ;
}

# end of src Jamfile
