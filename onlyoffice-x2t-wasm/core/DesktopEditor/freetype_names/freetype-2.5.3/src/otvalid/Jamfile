# FreeType 2 src/otvalid Jamfile
#
# Copyright 2004 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) otvalid ;

{
  local  _sources ;

  if $(FT2_MULTI)
  {
    _sources = otvbase otvcommn otvgdef otvgpos otvgsub otvjstf otvmod otvmath ;
  }
  else
  {
    _sources = otvalid ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# end of src/otvalid Jamfile
