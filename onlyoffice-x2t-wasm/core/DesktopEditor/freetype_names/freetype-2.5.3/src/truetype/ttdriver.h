﻿/***************************************************************************/
/*                                                                         */
/*  ttdriver.h                                                             */
/*                                                                         */
/*    High-level TrueType driver interface (specification).                */
/*                                                                         */
/*  Copyright 1996-2001, 2002 by                                           */
/*  <PERSON>, <PERSON>, and <PERSON>.                      */
/*                                                                         */
/*  This file is part of the FreeType project, and may only be used,       */
/*  modified, and distributed under the terms of the FreeType project      */
/*  license, LICENSE.TXT.  By continuing to use, modify, or distribute     */
/*  this file you indicate that you have read the license and              */
/*  understand and accept it fully.                                        */
/*                                                                         */
/***************************************************************************/


#ifndef __TTDRIVER_H__
#define __TTDRIVER_H__


#include <ft2build.h>
#include FT_INTERNAL_DRIVER_H


FT_BEGIN_HEADER


  FT_DECLARE_DRIVER( tt_driver_class )


FT_END_HEADER

#endif /* __TTDRIVER_H__ */


/* END */
