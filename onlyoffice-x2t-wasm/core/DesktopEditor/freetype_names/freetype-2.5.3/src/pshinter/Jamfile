# FreeType 2 src/pshinter Jamfile
#
# Copyright 2001, 2003 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) pshinter ;

{
  local  _sources ;

  if $(FT2_MULTI)
  {
    _sources = pshrec pshglob pshalgo pshmod pshpic ;
  }
  else
  {
    _sources = pshinter ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# end of src/pshinter Jamfile
