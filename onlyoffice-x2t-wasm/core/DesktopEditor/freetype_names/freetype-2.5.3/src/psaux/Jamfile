# FreeType 2 src/psaux Jamfile
#
# Copyright 2001, 2002 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) psaux ;

{
  local  _sources ;

  if $(FT2_MULTI)
  {
    _sources = psauxmod psobjs   t1decode t1cmap
               psconv   afmparse
               ;
  }
  else
  {
    _sources = psaux ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# end of src/psaux Jamfile
