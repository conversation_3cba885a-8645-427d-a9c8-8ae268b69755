# FreeType 2 src/sfnt Jamfile
#
# Copyright 2001, 2002, 2004, 2005 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) sfnt ;

{
  local  _sources ;

  if $(FT2_MULTI)
  {
    _sources = sfobjs sfdriver ttcmap ttmtx ttpost ttload ttsbit ttkern ttbdf sfntpic ;
  }
  else
  {
    _sources = sfnt ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# end of src/sfnt Jamfile
