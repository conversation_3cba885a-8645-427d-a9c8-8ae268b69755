﻿/***************************************************************************/
/*                                                                         */
/*  ttcmapc.h                                                              */
/*                                                                         */
/*    TT CMAP classes definitions (specification only).                    */
/*                                                                         */
/*  Copyright 2009 by                                                      */
/*  <PERSON><PERSON> and <PERSON>.                                            */
/*                                                                         */
/*  This file is part of the FreeType project, and may only be used,       */
/*  modified, and distributed under the terms of the FreeType project      */
/*  license, LICENSE.TXT.  By continuing to use, modify, or distribute     */
/*  this file you indicate that you have read the license and              */
/*  understand and accept it fully.                                        */
/*                                                                         */
/***************************************************************************/


#ifdef TT_CONFIG_CMAP_FORMAT_0
  TTCMAPCITEM( tt_cmap0_class_rec )
#endif

#ifdef TT_CONFIG_CMAP_FORMAT_2
  TTCMAPCITEM( tt_cmap2_class_rec )
#endif

#ifdef TT_CONFIG_CMAP_FORMAT_4
  TTCMAPCITEM( tt_cmap4_class_rec )
#endif

#ifdef TT_CONFIG_CMAP_FORMAT_6
  TTCMAPCITEM( tt_cmap6_class_rec )
#endif

#ifdef TT_CONFIG_CMAP_FORMAT_8
  TTCMAPCITEM( tt_cmap8_class_rec )
#endif

#ifdef TT_CONFIG_CMAP_FORMAT_10
  TTCMAPCITEM( tt_cmap10_class_rec )
#endif

#ifdef TT_CONFIG_CMAP_FORMAT_12
  TTCMAPCITEM( tt_cmap12_class_rec )
#endif

#ifdef TT_CONFIG_CMAP_FORMAT_13
  TTCMAPCITEM( tt_cmap13_class_rec )
#endif

#ifdef TT_CONFIG_CMAP_FORMAT_14
  TTCMAPCITEM( tt_cmap14_class_rec )
#endif


  /* END */
