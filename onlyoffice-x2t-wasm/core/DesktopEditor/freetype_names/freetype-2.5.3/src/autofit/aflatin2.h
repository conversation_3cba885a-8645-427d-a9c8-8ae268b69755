﻿/***************************************************************************/
/*                                                                         */
/*  aflatin2.h                                                             */
/*                                                                         */
/*    Auto-fitter hinting routines for latin writing system                */
/*    (specification).                                                     */
/*                                                                         */
/*  Copyright 2003-2007, 2012, 2013 by                                     */
/*  <PERSON>, <PERSON>, and <PERSON>.                      */
/*                                                                         */
/*  This file is part of the FreeType project, and may only be used,       */
/*  modified, and distributed under the terms of the FreeType project      */
/*  license, LICENSE.TXT.  By continuing to use, modify, or distribute     */
/*  this file you indicate that you have read the license and              */
/*  understand and accept it fully.                                        */
/*                                                                         */
/***************************************************************************/


#ifndef __AFLATIN2_H__
#define __AFLATIN2_H__

#include "afhints.h"


FT_BEGIN_HEADER


  /* the `latin' writing system */

  AF_DECLARE_WRITING_SYSTEM_CLASS( af_latin2_writing_system_class )


/* */

FT_END_HEADER

#endif /* __AFLATIN_H__ */


/* END */
