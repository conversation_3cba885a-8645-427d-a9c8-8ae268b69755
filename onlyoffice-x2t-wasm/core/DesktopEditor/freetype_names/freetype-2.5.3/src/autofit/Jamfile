# FreeType 2 src/autofit Jamfile
#
# Copyright 2003, 2004, 2005, 2006, 2007, 2009 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir FT2_TOP src autofit ;

{
  local  _sources ;

  # define FT2_AUTOFIT2 to enable experimental latin hinter replacement
  if $(FT2_AUTOFIT2)
  {
    DEFINES += FT_OPTION_AUTOFIT2 ;
  }
  if $(FT2_MULTI)
  {
    _sources = afangles afglobal afhints aflatin afcjk afindic afloader afmodule afdummy afwarp afpic ;

    if $(FT2_AUTOFIT2)
    {
      _sources += aflatin2 ;
    }
  }
  else
  {
    _sources = autofit ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# end of src/autofit Jamfile
