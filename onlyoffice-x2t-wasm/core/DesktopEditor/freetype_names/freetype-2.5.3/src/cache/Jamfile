# FreeType 2 src/cache Jamfile
#
# Copyright 2001, 2003, 2004, 2013 by
# <PERSON>, <PERSON>, and <PERSON>.
#
# This file is part of the FreeType project, and may only be used, modified,
# and distributed under the terms of the FreeType project license,
# LICENSE.TXT.  By continuing to use, modify, or distribute this file you
# indicate that you have read the license and understand and accept it
# fully.

SubDir  FT2_TOP $(FT2_SRC_DIR) cache ;

# The file <ftcache.h> contains some macro definitions that are
# later used in #include statements related to the cache sub-system.  It
# needs to be parsed through a HDRMACRO rule for macro definitions.
#
HDRMACRO  [ FT2_SubDir  include ftcache.h ] ;

{
  local  _sources ;

  if $(FT2_MULTI)
  {
    _sources = ftcmru
               ftcmanag
               ftccache
               ftcglyph
               ftcsbits
               ftcimage
               ftcbasic
               ftccmap
               ;
  }
  else
  {
    _sources = ftcache ;
  }

  Library  $(FT2_LIB) : $(_sources).c ;
}

# end of src/cache Jamfile
