﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{90df7276-7e29-4f1f-afb4-745641209005}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{276275e1-898d-4705-a7af-d8babcf6c3ad}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="tif_aux.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_close.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_codec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_color.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_compress.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_dir.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_dirinfo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_dirread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_dirwrite.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_dumpmode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_error.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_fax3.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_fax3sm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_flush.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_getimage.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_jpeg.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_luv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_lzw.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_next.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_ojpeg.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_open.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_packbits.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_pixarlog.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_predict.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_print.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_read.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_strip.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_swab.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_thunder.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_tile.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_version.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_warning.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_write.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tif_zip.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="t4.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tif_dir.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tif_fax3.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tif_predict.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tiff.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tiffcomp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tiffconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tiffio.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tiffiop.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="uvcode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>