<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="tiff"
	ProjectGUID="{075B4445-2F3F-4B31-864C-A333778D1D00}"
	RootNamespace="tiff"
	Keyword="MFCProj"
	>
	<Platforms>
		<Platform
			Name="Smartphone 2003 (ARMV4)"
		/>
		<Platform
			Name="Pocket PC 2003 (ARMV4)"
		/>
		<Platform
			Name="Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Smartphone 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/tiff.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				AdditionalIncludeDirectories="..\zlib"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/tiff.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\tiff.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/tiff.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Pocket PC 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/tiff.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/tiff.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\tiff.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/tiff.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Smartphone 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/tiff.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\zlib"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/tiff.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\tiff.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/tiff.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Pocket PC 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/tiff.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/tiff.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\tiff.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/tiff.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/tiff.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/tiff.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\tiff.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/tiff.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/tiff.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/tiff.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\tiff.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/tiff.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="tif_aux.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_close.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_codec.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\tif_color.c"
				>
			</File>
			<File
				RelativePath="tif_compress.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_dir.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_dirinfo.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_dirread.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_dirwrite.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_dumpmode.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_error.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_fax3.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\tif_fax3sm.c"
				>
			</File>
			<File
				RelativePath="tif_flush.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_getimage.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_jpeg.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_luv.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_lzw.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_next.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_ojpeg.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_open.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_packbits.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_pixarlog.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_predict.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_print.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_read.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_strip.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_swab.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_thunder.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_tile.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_version.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_warning.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_write.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="tif_zip.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="t4.h"
				>
			</File>
			<File
				RelativePath="tif_dir.h"
				>
			</File>
			<File
				RelativePath="tif_fax3.h"
				>
			</File>
			<File
				RelativePath="tif_predict.h"
				>
			</File>
			<File
				RelativePath="tiff.h"
				>
			</File>
			<File
				RelativePath="tiffcomp.h"
				>
			</File>
			<File
				RelativePath="tiffconf.h"
				>
			</File>
			<File
				RelativePath="tiffio.h"
				>
			</File>
			<File
				RelativePath="tiffiop.h"
				>
			</File>
			<File
				RelativePath="uvcode.h"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
