<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8,00"
	Name="jbig"
	ProjectGUID="{AE867A34-37BF-4216-8174-0E5895427D94}"
	>
	<Platforms>
		<Platform
			Name="Smartphone 2003 (ARMV4)"
		/>
		<Platform
			Name="Pocket PC 2003 (ARMV4)"
		/>
		<Platform
			Name="Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Smartphone 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/jbig.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/jbig.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\jbig.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/jbig.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Pocket PC 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/jbig.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/jbig.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\jbig.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/jbig.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/jbig.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/jbig.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\jbig.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/jbig.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Smartphone 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/jbig.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/jbig.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\jbig.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/jbig.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Pocket PC 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/jbig.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/jbig.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\jbig.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/jbig.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/jbig.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/jbig.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\jbig.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/jbig.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="jbig.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jbig_tab.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="jbig.h"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
