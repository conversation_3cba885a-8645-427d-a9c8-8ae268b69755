﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{e41a2215-ebdc-43c1-bfa9-74ead25861b0}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{384a3681-31d0-4a4f-8bed-dd4bb4675f3d}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="tif_xfile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximabmp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximadsp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximaenc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximaexif.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="xImage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximagif.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximahist.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximaico.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximainfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximaint.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximajas.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximajbg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximajpg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximalpha.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximalyr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximamng.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximapal.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximapcx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximapng.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximapsd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximaraw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximasel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximaska.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximatga.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximatif.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximatran.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximawbmp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximawmf.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ximawnd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="xmemfile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="stdint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="xfile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximabmp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximacfg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximadef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximagif.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximaico.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximaiter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximajas.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximajbg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximajpg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximamng.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximapcx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximapng.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximapsd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximaraw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximaska.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximatga.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximath.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximatif.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximawbmp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ximawmf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="xiofile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="xmemfile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>