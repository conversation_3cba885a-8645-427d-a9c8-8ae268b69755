﻿/*
 * File:	ximamng.h
 * Purpose:	Declaration of the MNG Image Class
 * Author:	<PERSON><PERSON> - www.xdp.it
 * Created:	2001
 */
/* ==========================================================
 * CxImageMNG (c) 07/Aug/2001 <PERSON><PERSON> - www.xdp.it
 * For conditions of distribution and use, see copyright notice in ximage.h
 *
 * Special thanks to <PERSON> <f.haug(at)jdm(dot)de> for suggestions and code.
 *
 * original mng.cpp code created by <PERSON><PERSON> B<PERSON>, November 14th, 2000. <virtualnik(at)nol(dot)at>
 *
 * LIBMNG Copyright (c) 2000,2001 <PERSON> (<EMAIL>)
 * ==========================================================
 */

#if !defined(__ximaMNG_h)
#define __ximaMNG_h

#include "ximage.h"

#if CXIMAGE_SUPPORT_MNG

//#define MNG_NO_CMS
#define MNG_SUPPORT_DISPLAY
#define MNG_SUPPORT_READ
#define	MNG_SUPPORT_WRITE
#define MNG_ACCESS_CHUNKS
#define MNG_STORE_CHUNKS

extern "C" {
#include "../mng/libmng.h"
#include "../mng/libmng_data.h"
#include "../mng/libmng_error.h"
}

//uint32_t _stdcall RunMNGThread(void *lpParam);

typedef struct tagmngstuff 
{
	CxFile		*file;
	uint8_t		*image;
	uint8_t		*alpha;
	HANDLE		thread;
	mng_uint32	delay;
	mng_uint32  width;
	mng_uint32  height;
	mng_uint32  effwdt;
	mng_int16	bpp;
	mng_bool	animation;
	mng_bool	animation_enabled;
	float		speed;
	int32_t		nBkgndIndex;
	RGBQUAD		nBkgndColor;
} mngstuff;

class CxImageMNG: public CxImage
{
public:
	CxImageMNG();
	~CxImageMNG();

	bool Load(const TCHAR * imageFileName);

	bool Decode(CxFile * hFile);
	bool Decode(FILE *hFile) { CxIOFile file(hFile); return Decode(&file); }

#if CXIMAGE_SUPPORT_ENCODE
	bool Encode(CxFile * hFile);
	bool Encode(FILE *hFile) { CxIOFile file(hFile); return Encode(&file); }
	bool Save(const TCHAR * imageFileName){ return CxImage::Save(imageFileName,CXIMAGE_FORMAT_MNG);}
#endif // CXIMAGE_SUPPORT_ENCODE

	int32_t Resume();
	void SetSpeed(float speed);
	
	mng_handle hmng;
	mngstuff mnginfo;
protected:
	void WritePNG(mng_handle hMNG, int32_t Frame, int32_t FrameCount );
	void SetCallbacks(mng_handle mng);
};

#endif

#endif
