CMakeLists.txt  cmake build file
ChangeLog       history of changes
FAQ             Frequently Asked Questions about zlib
INDEX           this file
Makefile        dummy Makefile that tells you to ./configure
Makefile.in     template for Unix Makefile
README          guess what
configure       configure script for Unix
make_vms.com    makefile for VMS
treebuild.xml   XML description of source file dependencies
zconf.h.cmakein zconf.h template for cmake
zconf.h.in      zconf.h template for configure
zlib.3          Man page for zlib
zlib.3.pdf      Man page in PDF format
zlib.map        Linux symbol information
zlib.pc.in      Template for pkg-config descriptor
zlib2ansi       perl script to convert source files for C++ compilation

amiga/          makefiles for Amiga SAS C
doc/            documentation for formats and algorithms
msdos/          makefiles for MSDOS
nintendods/     makefile for Nintendo DS
old/            makefiles for various architectures and zlib documentation
                files that have not yet been updated for zlib 1.2.x
qnx/            makefiles for QNX
watcom/         makefiles for OpenWatcom
win32/          makefiles for Windows

                zlib public header files (required for library use):
zconf.h
zlib.h

                private source files used to build the zlib library:
adler32.c
compress.c
crc32.c
crc32.h
deflate.c
deflate.h
gzclose.c
gzguts.h
gzlib.c
gzread.c
gzwrite.c
infback.c
inffast.c
inffast.h
inffixed.h
inflate.c
inflate.h
inftrees.c
inftrees.h
trees.c
trees.h
uncompr.c
zutil.c
zutil.h

                source files for sample programs:
example.c
minigzip.c
See examples/README.examples for more

                unsupported contribution by third parties
See contrib/README.contrib
