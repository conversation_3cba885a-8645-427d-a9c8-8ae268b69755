<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="zlib"
	ProjectGUID="{0B0813D9-95B0-4172-A933-5BB7AE1498DD}"
	RootNamespace="zlib"
	>
	<Platforms>
		<Platform
			Name="Smartphone 2003 (ARMV4)"
		/>
		<Platform
			Name="Pocket PC 2003 (ARMV4)"
		/>
		<Platform
			Name="Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release|Smartphone 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/zlib.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\wcecompat\include"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/zlib.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\zlib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/zlib.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Pocket PC 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/zlib.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\wcecompat\include"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB;NO_ERRNO_H"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/zlib.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\zlib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/zlib.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Smartphone 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/zlib.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/zlib.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\zlib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/zlib.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Pocket PC 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/zlib.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				AdditionalIncludeDirectories="..\wcecompat\include"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB;NO_ERRNO_H"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/zlib.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\zlib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/zlib.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/zlib.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\wcecompat\include"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/zlib.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\zlib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/zlib.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/zlib.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				AdditionalIncludeDirectories="..\wcecompat\include"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/zlib.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\zlib.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/zlib.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="adler32.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="compress.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="crc32.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="deflate.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="infback.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="inffast.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="inflate.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="inftrees.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="trees.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="uncompr.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="zutil.c"
				>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="crc32.h"
				>
			</File>
			<File
				RelativePath="deflate.h"
				>
			</File>
			<File
				RelativePath="inffast.h"
				>
			</File>
			<File
				RelativePath="inffixed.h"
				>
			</File>
			<File
				RelativePath="inflate.h"
				>
			</File>
			<File
				RelativePath="inftrees.h"
				>
			</File>
			<File
				RelativePath="trees.h"
				>
			</File>
			<File
				RelativePath="zconf.h"
				>
			</File>
			<File
				RelativePath="zconf.in.h"
				>
			</File>
			<File
				RelativePath="zlib.h"
				>
			</File>
			<File
				RelativePath="zutil.h"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
