
                Frequently Asked Questions about zlib


If your question is not there, please check the zlib home page
http://zlib.net/ which may have more recent information.
The lastest zlib FAQ is at http://zlib.net/zlib_faq.html


 1. Is zlib Y2K-compliant?

    Yes. zlib doesn't handle dates.

 2. Where can I get a Windows DLL version?

    The zlib sources can be compiled without change to produce a DLL.  See the
    file win32/DLL_FAQ.txt in the zlib distribution.  Pointers to the
    precompiled DLL are found in the zlib web site at http://zlib.net/ .

 3. Where can I get a Visual Basic interface to zlib?

    See
        * http://marknelson.us/1997/01/01/zlib-engine/
        * win32/DLL_FAQ.txt in the zlib distribution

 4. compress() returns Z_BUF_ERROR.

    Make sure that before the call of compress(), the length of the compressed
    buffer is equal to the available size of the compressed buffer and not
    zero.  For Visual Basic, check that this parameter is passed by reference
    ("as any"), not by value ("as long").

 5. deflate() or inflate() returns Z_BUF_ERROR.

    Before making the call, make sure that avail_in and avail_out are not zero.
    When setting the parameter flush equal to Z_FINISH, also make sure that
    avail_out is big enough to allow processing all pending input.  Note that a
    Z_BUF_ERROR is not fatal--another call to deflate() or inflate() can be
    made with more input or output space.  A Z_BUF_ERROR may in fact be
    unavoidable depending on how the functions are used, since it is not
    possible to tell whether or not there is more output pending when
    strm.avail_out returns with zero.  See http://zlib.net/zlib_how.html for a
    heavily annotated example.

 6. Where's the zlib documentation (man pages, etc.)?

    It's in zlib.h .  Examples of zlib usage are in the files example.c and
    minigzip.c, with more in examples/ .

 7. Why don't you use GNU autoconf or libtool or ...?

    Because we would like to keep zlib as a very small and simple package.
    zlib is rather portable and doesn't need much configuration.

 8. I found a bug in zlib.

    Most of the time, such problems are due to an incorrect usage of zlib.
    Please try to reproduce the problem with a small program and send the
    corresponding source to <NAME_EMAIL> .  Do not send multi-megabyte
    data files without prior agreement.

 9. Why do I get "undefined reference to gzputc"?

    If "make test" produces something like

       example.o(.text+0x154): undefined reference to `gzputc'

    check that you don't have old files libz.* in /usr/lib, /usr/local/lib or
    /usr/X11R6/lib. Remove any old versions, then do "make install".

10. I need a Delphi interface to zlib.

    See the contrib/delphi directory in the zlib distribution.

11. Can zlib handle .zip archives?

    Not by itself, no.  See the directory contrib/minizip in the zlib
    distribution.

12. Can zlib handle .Z files?

    No, sorry.  You have to spawn an uncompress or gunzip subprocess, or adapt
    the code of uncompress on your own.

13. How can I make a Unix shared library?

    make clean
    ./configure -s
    make

14. How do I install a shared zlib library on Unix?

    After the above, then:

    make install

    However, many flavors of Unix come with a shared zlib already installed.
    Before going to the trouble of compiling a shared version of zlib and
    trying to install it, you may want to check if it's already there!  If you
    can #include <zlib.h>, it's there.  The -lz option will probably link to
    it.  You can check the version at the top of zlib.h or with the
    ZLIB_VERSION symbol defined in zlib.h .

15. I have a question about OttoPDF.

    We are not the authors of OttoPDF. The real author is on the OttoPDF web
    site: Joel Hainley, <EMAIL>.

16. Can zlib decode Flate data in an Adobe PDF file?

    Yes. See http://www.pdflib.com/ . To modify PDF forms, see
    http://sourceforge.net/projects/acroformtool/ .

17. Why am I getting this "register_frame_info not found" error on Solaris?

    After installing zlib 1.1.4 on Solaris 2.6, running applications using zlib
    generates an error such as:

        ld.so.1: rpm: fatal: relocation error: file /usr/local/lib/libz.so:
        symbol __register_frame_info: referenced symbol not found

    The symbol __register_frame_info is not part of zlib, it is generated by
    the C compiler (cc or gcc).  You must recompile applications using zlib
    which have this problem.  This problem is specific to Solaris.  See
    http://www.sunfreeware.com for Solaris versions of zlib and applications
    using zlib.

18. Why does gzip give an error on a file I make with compress/deflate?

    The compress and deflate functions produce data in the zlib format, which
    is different and incompatible with the gzip format.  The gz* functions in
    zlib on the other hand use the gzip format.  Both the zlib and gzip formats
    use the same compressed data format internally, but have different headers
    and trailers around the compressed data.

19. Ok, so why are there two different formats?

    The gzip format was designed to retain the directory information about a
    single file, such as the name and last modification date.  The zlib format
    on the other hand was designed for in-memory and communication channel
    applications, and has a much more compact header and trailer and uses a
    faster integrity check than gzip.

20. Well that's nice, but how do I make a gzip file in memory?

    You can request that deflate write the gzip format instead of the zlib
    format using deflateInit2().  You can also request that inflate decode the
    gzip format using inflateInit2().  Read zlib.h for more details.

21. Is zlib thread-safe?

    Yes.  However any library routines that zlib uses and any application-
    provided memory allocation routines must also be thread-safe.  zlib's gz*
    functions use stdio library routines, and most of zlib's functions use the
    library memory allocation routines by default.  zlib's *Init* functions
    allow for the application to provide custom memory allocation routines.

    Of course, you should only operate on any given zlib or gzip stream from a
    single thread at a time.

22. Can I use zlib in my commercial application?

    Yes.  Please read the license in zlib.h.

23. Is zlib under the GNU license?

    No.  Please read the license in zlib.h.

24. The license says that altered source versions must be "plainly marked". So
    what exactly do I need to do to meet that requirement?

    You need to change the ZLIB_VERSION and ZLIB_VERNUM #defines in zlib.h.  In
    particular, the final version number needs to be changed to "f", and an
    identification string should be appended to ZLIB_VERSION.  Version numbers
    x.x.x.f are reserved for modifications to zlib by others than the zlib
    maintainers.  For example, if the version of the base zlib you are altering
    is "*******", then in zlib.h you should change ZLIB_VERNUM to 0x123f, and
    ZLIB_VERSION to something like "1.2.3.f-zachary-mods-v3".  You can also
    update the version strings in deflate.c and inftrees.c.

    For altered source distributions, you should also note the origin and
    nature of the changes in zlib.h, as well as in ChangeLog and README, along
    with the dates of the alterations.  The origin should include at least your
    name (or your company's name), and an email address to contact for help or
    issues with the library.

    Note that distributing a compiled zlib library along with zlib.h and
    zconf.h is also a source distribution, and so you should change
    ZLIB_VERSION and ZLIB_VERNUM and note the origin and nature of the changes
    in zlib.h as you would for a full source distribution.

25. Will zlib work on a big-endian or little-endian architecture, and can I
    exchange compressed data between them?

    Yes and yes.

26. Will zlib work on a 64-bit machine?

    Yes.  It has been tested on 64-bit machines, and has no dependence on any
    data types being limited to 32-bits in length.  If you have any
    difficulties, please provide a complete problem <NAME_EMAIL>

27. Will zlib decompress data from the PKWare Data Compression Library?

    No.  The PKWare DCL uses a completely different compressed data format than
    does PKZIP and zlib.  However, you can look in zlib's contrib/blast
    directory for a possible solution to your problem.

28. Can I access data randomly in a compressed stream?

    No, not without some preparation.  If when compressing you periodically use
    Z_FULL_FLUSH, carefully write all the pending data at those points, and
    keep an index of those locations, then you can start decompression at those
    points.  You have to be careful to not use Z_FULL_FLUSH too often, since it
    can significantly degrade compression.  Alternatively, you can scan a
    deflate stream once to generate an index, and then use that index for
    random access.  See examples/zran.c .

29. Does zlib work on MVS, OS/390, CICS, etc.?

    It has in the past, but we have not heard of any recent evidence.  There
    were working ports of zlib 1.1.4 to MVS, but those links no longer work.
    If you know of recent, successful applications of zlib on these operating
    systems, please let us know.  Thanks.

30. Is there some simpler, easier to read version of inflate I can look at to
    understand the deflate format?

    First off, you should read RFC 1951.  Second, yes.  Look in zlib's
    contrib/puff directory.

31. Does zlib infringe on any patents?

    As far as we know, no.  In fact, that was originally the whole point behind
    zlib.  Look here for some more information:

    http://www.gzip.org/#faq11

32. Can zlib work with greater than 4 GB of data?

    Yes.  inflate() and deflate() will process any amount of data correctly.
    Each call of inflate() or deflate() is limited to input and output chunks
    of the maximum value that can be stored in the compiler's "unsigned int"
    type, but there is no limit to the number of chunks.  Note however that the
    strm.total_in and strm_total_out counters may be limited to 4 GB.  These
    counters are provided as a convenience and are not used internally by
    inflate() or deflate().  The application can easily set up its own counters
    updated after each call of inflate() or deflate() to count beyond 4 GB.
    compress() and uncompress() may be limited to 4 GB, since they operate in a
    single call.  gzseek() and gztell() may be limited to 4 GB depending on how
    zlib is compiled.  See the zlibCompileFlags() function in zlib.h.

    The word "may" appears several times above since there is a 4 GB limit only
    if the compiler's "long" type is 32 bits.  If the compiler's "long" type is
    64 bits, then the limit is 16 exabytes.

33. Does zlib have any security vulnerabilities?

    The only one that we are aware of is potentially in gzprintf().  If zlib is
    compiled to use sprintf() or vsprintf(), then there is no protection
    against a buffer overflow of an 8K string space (or other value as set by
    gzbuffer()), other than the caller of gzprintf() assuring that the output
    will not exceed 8K.  On the other hand, if zlib is compiled to use
    snprintf() or vsnprintf(), which should normally be the case, then there is
    no vulnerability.  The ./configure script will display warnings if an
    insecure variation of sprintf() will be used by gzprintf().  Also the
    zlibCompileFlags() function will return information on what variant of
    sprintf() is used by gzprintf().

    If you don't have snprintf() or vsnprintf() and would like one, you can
    find a portable implementation here:

        http://www.ijs.si/software/snprintf/

    Note that you should be using the most recent version of zlib.  Versions
    1.1.3 and before were subject to a double-free vulnerability, and versions
    1.2.1 and 1.2.2 were subject to an access exception when decompressing
    invalid compressed data.

34. Is there a Java version of zlib?

    Probably what you want is to use zlib in Java. zlib is already included
    as part of the Java SDK in the java.util.zip package. If you really want
    a version of zlib written in the Java language, look on the zlib home
    page for links: http://zlib.net/ .

35. I get this or that compiler or source-code scanner warning when I crank it
    up to maximally-pedantic. Can't you guys write proper code?

    Many years ago, we gave up attempting to avoid warnings on every compiler
    in the universe.  It just got to be a waste of time, and some compilers
    were downright silly as well as contradicted each other.  So now, we simply
    make sure that the code always works.

36. Valgrind (or some similar memory access checker) says that deflate is
    performing a conditional jump that depends on an uninitialized value.
    Isn't that a bug?

    No.  That is intentional for performance reasons, and the output of deflate
    is not affected.  This only started showing up recently since zlib 1.2.x
    uses malloc() by default for allocations, whereas earlier versions used
    calloc(), which zeros out the allocated memory.  Even though the code was
    correct, versions 1.2.4 and later was changed to not stimulate these
    checkers.

37. Will zlib read the (insert any ancient or arcane format here) compressed
    data format?

    Probably not. Look in the comp.compression FAQ for pointers to various
    formats and associated software.

38. How can I encrypt/decrypt zip files with zlib?

    zlib doesn't support encryption.  The original PKZIP encryption is very
    weak and can be broken with freely available programs.  To get strong
    encryption, use GnuPG, http://www.gnupg.org/ , which already includes zlib
    compression.  For PKZIP compatible "encryption", look at
    http://www.info-zip.org/

39. What's the difference between the "gzip" and "deflate" HTTP 1.1 encodings?

    "gzip" is the gzip format, and "deflate" is the zlib format.  They should
    probably have called the second one "zlib" instead to avoid confusion with
    the raw deflate compressed data format.  While the HTTP 1.1 RFC 2616
    correctly points to the zlib specification in RFC 1950 for the "deflate"
    transfer encoding, there have been reports of servers and browsers that
    incorrectly produce or expect raw deflate data per the deflate
    specficiation in RFC 1951, most notably Microsoft.  So even though the
    "deflate" transfer encoding using the zlib format would be the more
    efficient approach (and in fact exactly what the zlib format was designed
    for), using the "gzip" transfer encoding is probably more reliable due to
    an unfortunate choice of name on the part of the HTTP 1.1 authors.

    Bottom line: use the gzip format for HTTP 1.1 encoding.

40. Does zlib support the new "Deflate64" format introduced by PKWare?

    No.  PKWare has apparently decided to keep that format proprietary, since
    they have not documented it as they have previous compression formats.  In
    any case, the compression improvements are so modest compared to other more
    modern approaches, that it's not worth the effort to implement.

41. I'm having a problem with the zip functions in zlib, can you help?

    There are no zip functions in zlib.  You are probably using minizip by
    Giles Vollant, which is found in the contrib directory of zlib.  It is not
    part of zlib.  In fact none of the stuff in contrib is part of zlib.  The
    files in there are not supported by the zlib authors.  You need to contact
    the authors of the respective contribution for help.

42. The match.asm code in contrib is under the GNU General Public License.
    Since it's part of zlib, doesn't that mean that all of zlib falls under the
    GNU GPL?

    No.  The files in contrib are not part of zlib.  They were contributed by
    other authors and are provided as a convenience to the user within the zlib
    distribution.  Each item in contrib has its own license.

43. Is zlib subject to export controls?  What is its ECCN?

    zlib is not subject to export controls, and so is classified as EAR99.

44. Can you please sign these lengthy legal documents and fax them back to us
    so that we can use your software in our product?

    No. Go away. Shoo.
