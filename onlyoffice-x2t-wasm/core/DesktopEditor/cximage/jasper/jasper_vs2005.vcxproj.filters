﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{97f1db21-7dc3-458e-93e9-637df677f4e1}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{ba992748-5ff0-4a76-8c04-8d6be4b69521}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="bmp\bmp_cod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="bmp\bmp_dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="bmp\bmp_enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_cm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_debug.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_getopt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_icc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_iccdata.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_image.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_malloc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_seq.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_stream.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_string.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_tvp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="base\jas_version.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jp2\jp2_cod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jp2\jp2_dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jp2\jp2_enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_bs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_cs.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_math.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_mct.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_mqcod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_mqdec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_mqenc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_qmfb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_t1cod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_t1dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_t1enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_t2cod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_t2dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_t2enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_tagtree.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_tsfb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpc\jpc_util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpg\jpg_dummy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="jpg\jpg_val.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mif\mif_cod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pgx\pgx_cod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pgx\pgx_dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pgx\pgx_enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pnm\pnm_cod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pnm\pnm_dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pnm\pnm_enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ras\ras_cod.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ras\ras_dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ras\ras_enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="bmp\bmp_cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\jasper\jas_cm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\jasper\jas_icc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\jasper\jas_image.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jp2\jp2_cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jp2\jp2_dec.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_bs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_cs.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_dec.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_enc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_fix.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_flt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_math.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_mct.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_mqcod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_mqdec.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_mqenc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_qmfb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_t1cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_t1dec.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_t1enc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_t2cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_t2dec.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_t2enc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_tagtree.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_tsfb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpc\jpc_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="jpg\jpg_cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mif\mif_cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pgx\pgx_cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pnm\pnm_cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ras\ras_cod.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>