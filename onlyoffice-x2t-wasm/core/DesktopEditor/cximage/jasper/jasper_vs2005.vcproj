<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8,00"
	Name="jasper"
	ProjectGUID="{FFDA5DA1-BB65-4695-B678-BE59B4A1355D}"
	RootNamespace="jasper"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			UseOfATL="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\include"
				PreprocessorDefinitions="_DEBUG;WIN32;_LIB;JAS_WIN_MSVC_BUILD;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				EnableEnhancedInstructionSet="0"
				PrecompiledHeaderFile=""
				AssemblerListingLocation="$(ConfigurationName)\"
				ObjectFile="$(IntDir)\"
				ProgramDataBaseFileName="$(IntDir)\vc80.pdb"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
				DisableSpecificWarnings="4005;4311;4312"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\jasper.lib"
				SuppressStartupBanner="true"
				IgnoreAllDefaultLibraries="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Debug/jasper.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\include"
				PreprocessorDefinitions="_DEBUG;WIN32;_LIB;JAS_WIN_MSVC_BUILD;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				PrecompiledHeaderFile=".\Debug/jasper.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)\"
				ObjectFile="$(PlatformName)\$(ConfigurationName)\"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)\"
				WarningLevel="2"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\jasper.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Debug/jasper.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories=".\include"
				PreprocessorDefinitions="NDEBUG;WIN32;_LIB;JAS_WIN_MSVC_BUILD;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Release/jasper.pch"
				AssemblerListingLocation="$(ConfigurationName)\"
				ObjectFile="$(ConfigurationName)\"
				ProgramDataBaseFileName="$(ConfigurationName)\"
				WarningLevel="2"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\jasper.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Release/jasper.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories=".\include"
				PreprocessorDefinitions="NDEBUG;WIN32;_LIB;JAS_WIN_MSVC_BUILD;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Release/jasper.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)\"
				ObjectFile="$(PlatformName)\$(ConfigurationName)\"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)\"
				WarningLevel="2"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\jasper.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Release/jasper.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Unicode Release|Win32"
			OutputDirectory=".\Unicode_Release"
			IntermediateDirectory=".\Unicode_Release"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories=".\include"
				PreprocessorDefinitions="_LIB;JAS_WIN_MSVC_BUILD;WIN32;NDEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Unicode_Release/jasper.pch"
				AssemblerListingLocation=".\Unicode_Release/"
				ObjectFile=".\Unicode_Release/"
				ProgramDataBaseFileName=".\Unicode_Release/"
				WarningLevel="2"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile=".\Unicode_Release\jasper.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Unicode_Release/jasper.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Unicode Release|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories=".\include"
				PreprocessorDefinitions="_LIB;JAS_WIN_MSVC_BUILD;WIN32;NDEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Unicode_Release/jasper.pch"
				AssemblerListingLocation=".\Unicode_Release/"
				ObjectFile=".\Unicode_Release/"
				ProgramDataBaseFileName=".\Unicode_Release/"
				WarningLevel="2"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile=".\Unicode_Release\jasper.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Unicode_Release/jasper.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Unicode Debug|Win32"
			OutputDirectory=".\Unicode_Debug"
			IntermediateDirectory=".\Unicode_Debug"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\include"
				PreprocessorDefinitions="_LIB;JAS_WIN_MSVC_BUILD;WIN32;_DEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				PrecompiledHeaderFile=".\Unicode_Debug/jasper.pch"
				AssemblerListingLocation=".\Unicode_Debug/"
				ObjectFile=".\Unicode_Debug/"
				ProgramDataBaseFileName=".\Unicode_Debug/"
				WarningLevel="2"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile=".\Unicode_Debug\jasper.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Unicode_Debug/jasper.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Unicode Debug|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\include"
				PreprocessorDefinitions="_LIB;JAS_WIN_MSVC_BUILD;WIN32;_DEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				PrecompiledHeaderFile=".\Unicode_Debug/jasper.pch"
				AssemblerListingLocation=".\Unicode_Debug/"
				ObjectFile=".\Unicode_Debug/"
				ProgramDataBaseFileName=".\Unicode_Debug/"
				WarningLevel="2"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile=".\Unicode_Debug\jasper.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Unicode_Debug/jasper.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="bmp\bmp_cod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="bmp\bmp_dec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="bmp\bmp_enc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_cm.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_debug.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_getopt.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_icc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_iccdata.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_image.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_init.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_malloc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_seq.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_stream.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_string.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_tvp.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="base\jas_version.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jp2\jp2_cod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jp2\jp2_dec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jp2\jp2_enc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_bs.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_cs.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_dec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_enc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_math.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_mct.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_mqcod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_mqdec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_mqenc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_qmfb.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_t1cod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_t1dec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_t1enc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_t2cod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_t2dec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_t2enc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_tagtree.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_tsfb.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpc\jpc_util.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpg\jpg_dummy.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="jpg\jpg_val.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="mif\mif_cod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pgx\pgx_cod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pgx\pgx_dec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pgx\pgx_enc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pnm\pnm_cod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pnm\pnm_dec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pnm\pnm_enc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="ras\ras_cod.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="ras\ras_dec.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="ras\ras_enc.c"
				>
				<FileConfiguration
					Name="Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Release|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|Win32"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Unicode Debug|x64"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="bmp\bmp_cod.h"
				>
			</File>
			<File
				RelativePath="include\jasper\jas_cm.h"
				>
			</File>
			<File
				RelativePath="include\jasper\jas_icc.h"
				>
			</File>
			<File
				RelativePath="include\jasper\jas_image.h"
				>
			</File>
			<File
				RelativePath="jp2\jp2_cod.h"
				>
			</File>
			<File
				RelativePath="jp2\jp2_dec.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_bs.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_cod.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_cs.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_dec.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_enc.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_fix.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_flt.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_math.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_mct.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_mqcod.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_mqdec.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_mqenc.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_qmfb.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_t1cod.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_t1dec.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_t1enc.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_t2cod.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_t2dec.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_t2enc.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_tagtree.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_tsfb.h"
				>
			</File>
			<File
				RelativePath="jpc\jpc_util.h"
				>
			</File>
			<File
				RelativePath="jpg\jpg_cod.h"
				>
			</File>
			<File
				RelativePath="mif\mif_cod.h"
				>
			</File>
			<File
				RelativePath="pgx\pgx_cod.h"
				>
			</File>
			<File
				RelativePath="pnm\pnm_cod.h"
				>
			</File>
			<File
				RelativePath="ras\ras_cod.h"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
