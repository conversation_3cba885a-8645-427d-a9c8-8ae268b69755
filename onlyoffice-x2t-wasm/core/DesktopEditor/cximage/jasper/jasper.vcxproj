﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="bmp\bmp_cod.h" />
    <ClInclude Include="include\jasper\jas_cm.h" />
    <ClInclude Include="include\jasper\jas_icc.h" />
    <ClInclude Include="include\jasper\jas_image.h" />
    <ClInclude Include="jp2\jp2_cod.h" />
    <ClInclude Include="jp2\jp2_dec.h" />
    <ClInclude Include="jpc\jpc_bs.h" />
    <ClInclude Include="jpc\jpc_cod.h" />
    <ClInclude Include="jpc\jpc_cs.h" />
    <ClInclude Include="jpc\jpc_dec.h" />
    <ClInclude Include="jpc\jpc_enc.h" />
    <ClInclude Include="jpc\jpc_fix.h" />
    <ClInclude Include="jpc\jpc_flt.h" />
    <ClInclude Include="jpc\jpc_math.h" />
    <ClInclude Include="jpc\jpc_mct.h" />
    <ClInclude Include="jpc\jpc_mqcod.h" />
    <ClInclude Include="jpc\jpc_mqdec.h" />
    <ClInclude Include="jpc\jpc_mqenc.h" />
    <ClInclude Include="jpc\jpc_qmfb.h" />
    <ClInclude Include="jpc\jpc_t1cod.h" />
    <ClInclude Include="jpc\jpc_t1dec.h" />
    <ClInclude Include="jpc\jpc_t1enc.h" />
    <ClInclude Include="jpc\jpc_t2cod.h" />
    <ClInclude Include="jpc\jpc_t2dec.h" />
    <ClInclude Include="jpc\jpc_t2enc.h" />
    <ClInclude Include="jpc\jpc_tagtree.h" />
    <ClInclude Include="jpc\jpc_tsfb.h" />
    <ClInclude Include="jpc\jpc_util.h" />
    <ClInclude Include="mif\mif_cod.h" />
    <ClInclude Include="pgx\pgx_cod.h" />
    <ClInclude Include="pnm\pnm_cod.h" />
    <ClInclude Include="ras\ras_cod.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="base\jas_cm.c" />
    <ClCompile Include="base\jas_debug.c" />
    <ClCompile Include="base\jas_getopt.c" />
    <ClCompile Include="base\jas_icc.c" />
    <ClCompile Include="base\jas_iccdata.c" />
    <ClCompile Include="base\jas_image.c" />
    <ClCompile Include="base\jas_init.c" />
    <ClCompile Include="base\jas_malloc.c" />
    <ClCompile Include="base\jas_seq.c" />
    <ClCompile Include="base\jas_stream.c" />
    <ClCompile Include="base\jas_string.c" />
    <ClCompile Include="base\jas_tvp.c" />
    <ClCompile Include="base\jas_version.c" />
    <ClCompile Include="bmp\bmp_cod.c" />
    <ClCompile Include="bmp\bmp_dec.c" />
    <ClCompile Include="bmp\bmp_enc.c" />
    <ClCompile Include="jp2\jp2_cod.c" />
    <ClCompile Include="jp2\jp2_dec.c" />
    <ClCompile Include="jp2\jp2_enc.c" />
    <ClCompile Include="jpc\jpc_bs.c" />
    <ClCompile Include="jpc\jpc_cs.c" />
    <ClCompile Include="jpc\jpc_dec.c" />
    <ClCompile Include="jpc\jpc_enc.c" />
    <ClCompile Include="jpc\jpc_math.c" />
    <ClCompile Include="jpc\jpc_mct.c" />
    <ClCompile Include="jpc\jpc_mqcod.c" />
    <ClCompile Include="jpc\jpc_mqdec.c" />
    <ClCompile Include="jpc\jpc_mqenc.c" />
    <ClCompile Include="jpc\jpc_qmfb.c" />
    <ClCompile Include="jpc\jpc_t1cod.c" />
    <ClCompile Include="jpc\jpc_t1dec.c" />
    <ClCompile Include="jpc\jpc_t1enc.c" />
    <ClCompile Include="jpc\jpc_t2cod.c" />
    <ClCompile Include="jpc\jpc_t2dec.c" />
    <ClCompile Include="jpc\jpc_t2enc.c" />
    <ClCompile Include="jpc\jpc_tagtree.c" />
    <ClCompile Include="jpc\jpc_tsfb.c" />
    <ClCompile Include="jpc\jpc_util.c" />
    <ClCompile Include="jpg\jpg_dummy.c" />
    <ClCompile Include="jpg\jpg_val.c" />
    <ClCompile Include="mif\mif_cod.c" />
    <ClCompile Include="pgx\pgx_cod.c" />
    <ClCompile Include="pgx\pgx_dec.c" />
    <ClCompile Include="pgx\pgx_enc.c" />
    <ClCompile Include="pnm\pnm_cod.c" />
    <ClCompile Include="pnm\pnm_dec.c" />
    <ClCompile Include="pnm\pnm_enc.c" />
    <ClCompile Include="ras\ras_cod.c" />
    <ClCompile Include="ras\ras_dec.c" />
    <ClCompile Include="ras\ras_enc.c" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{81C530E5-D74C-4A3E-B313-7EBD75698949}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>jasper</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup />
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;JAS_WIN_MSVC_BUILD;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.\include</AdditionalIncludeDirectories>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;JAS_WIN_MSVC_BUILD;_CRT_SECURE_NO_DEPRECATE;EXCLUDE_JPG_SUPPORT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.\include</AdditionalIncludeDirectories>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;JAS_WIN_MSVC_BUILD;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>.\include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;JAS_WIN_MSVC_BUILD;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile>
      </PrecompiledHeaderFile>
      <AdditionalIncludeDirectories>.\include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>