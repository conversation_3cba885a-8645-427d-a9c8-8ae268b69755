<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="libpng"
	RootNamespace="libpng">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="DLL Release|Win32"
			OutputDirectory=".\Win32_DLL_Release"
			IntermediateDirectory=".\Win32_DLL_Release"
			ConfigurationType="2">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;NDEBUG;PNG_BUILD_DLL;ZLIB_DLL;_CRT_SECURE_NO_WARNINGS"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="3"
				PrecompiledHeaderThrough="pngpriv.h"
				WarningLevel="3"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/libpng15.dll"
				LinkIncremental="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="copy ..\..\scripts\pnglibconf.h.prebuilt ..\..\pnglibconf.h"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="..\..;..\..\..\zlib;$(IntDir)"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="DLL Debug|Win32"
			OutputDirectory=".\Win32_DLL_Debug"
			IntermediateDirectory=".\Win32_DLL_Debug"
			ConfigurationType="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;_DEBUG;DEBUG;PNG_DEBUG=1;PNG_BUILD_DLL;ZLIB_DLL;_CRT_SECURE_NO_WARNINGS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="3"
				PrecompiledHeaderThrough="pngpriv.h"
				WarningLevel="3"
				DebugInformationFormat="4"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/libpng15d.dll"
				GenerateDebugInformation="TRUE"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="copy ..\..\scripts\pnglibconf.h.prebuilt ..\..\pnglibconf.h"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG,PNG_DEBUG=1"
				Culture="1033"
				AdditionalIncludeDirectories="..\..;..\..\..\zlib;$(IntDir)"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="DLL VB|Win32"
			OutputDirectory=".\Win32_DLL_VB"
			IntermediateDirectory=".\Win32_DLL_VB"
			ConfigurationType="2">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;NDEBUG;PNG_BUILD_DLL;ZLIB_DLL;PNGAPI=__stdcall;PNG_NO_MODULEDEF;PNG_LIBPNG_SPECIALBUILD;PNG_USER_PRIVATEBUILD;_CRT_SECURE_NO_WARNINGS"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="3"
				PrecompiledHeaderThrough="pngpriv.h"
				WarningLevel="3"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)/libpng15vb.dll"
				LinkIncremental="1"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="copy ..\..\scripts\pnglibconf.h.prebuilt ..\..\pnglibconf.h"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				AdditionalOptions="/d PNG_NO_PEDANTIC_WARNINGS /d PNG_LIBPNG_DLLFNAME_POSTFIX=&quot;&quot;&quot;&quot;VB&quot;&quot;&quot;&quot; /d PNG_LIBPNG_SPECIALBUILD=&quot;&quot;&quot;&quot;__stdcall calling convention used for exported functions&quot;&quot;&quot;&quot;"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"
				AdditionalIncludeDirectories="..\..;..\..\..\zlib;$(IntDir)"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="LIB Release|Win32"
			OutputDirectory=".\Win32_LIB_Release"
			IntermediateDirectory=".\Win32_LIB_Release"
			ConfigurationType="4">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;NDEBUG;_CRT_SECURE_NO_WARNINGS"
				StringPooling="TRUE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="3"
				PrecompiledHeaderThrough="pngpriv.h"
				WarningLevel="3"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/libpng.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="copy ..\..\scripts\pnglibconf.h.prebuilt ..\..\pnglibconf.h"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="LIB Debug|Win32"
			OutputDirectory=".\Win32_LIB_Debug"
			IntermediateDirectory=".\Win32_LIB_Debug"
			ConfigurationType="4">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..;..\..\..\zlib"
				PreprocessorDefinitions="WIN32;_DEBUG;DEBUG;PNG_DEBUG=1;_CRT_SECURE_NO_WARNINGS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				UsePrecompiledHeader="3"
				PrecompiledHeaderThrough="pngpriv.h"
				WarningLevel="3"
				DebugInformationFormat="4"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)/libpngd.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"
				CommandLine="copy ..\..\scripts\pnglibconf.h.prebuilt ..\..\pnglibconf.h"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat">
			<File
				RelativePath="..\..\png.c">
				<FileConfiguration
					Name="DLL Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"/>
				</FileConfiguration>
				<FileConfiguration
					Name="DLL Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"/>
				</FileConfiguration>
				<FileConfiguration
					Name="DLL VB|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="0"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\..\pngerror.c">
				<FileConfiguration
					Name="DLL Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="DLL Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="DLL VB|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\..\pngget.c">
			</File>
			<File
				RelativePath="..\..\pngmem.c">
			</File>
			<File
				RelativePath="..\..\pngpread.c">
			</File>
			<File
				RelativePath="..\..\pngread.c">
			</File>
			<File
				RelativePath="..\..\pngrio.c">
			</File>
			<File
				RelativePath="..\..\pngrtran.c">
			</File>
			<File
				RelativePath="..\..\pngrutil.c">
			</File>
			<File
				RelativePath="..\..\pngset.c">
			</File>
			<File
				RelativePath="..\..\pngtrans.c">
			</File>
			<File
				RelativePath="..\..\pngwio.c">
			</File>
			<File
				RelativePath="..\..\pngwrite.c">
			</File>
			<File
				RelativePath="..\..\pngwtran.c">
			</File>
			<File
				RelativePath="..\..\pngwutil.c">
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl">
			<File
				RelativePath="..\..\png.h">
			</File>
			<File
				RelativePath="..\..\pngconf.h">
			</File>
			<File
				RelativePath="..\..\pngpriv.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe">
			<File
				RelativePath="..\..\scripts\pngwin.rc">
				<FileConfiguration
					Name="LIB Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCResourceCompilerTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCResourceCompilerTool"/>
				</FileConfiguration>
			</File>
		</Filter>
		<File
			RelativePath=".\PRJ0041.mak">
			<FileConfiguration
				Name="DLL Release|Win32">
				<Tool
					Name="VCCustomBuildTool"
					Description="Create dummy include files to prevent &quot;Cannot find missing dependency...&quot; warnings."
					CommandLine="nmake -f PRJ0041.mak IntDir=$(IntDir)"
					Outputs="$(IntDir)\alloc.h;$(IntDir)\fp.h;$(IntDir)\m68881.h;$(IntDir)\mem.h;$(IntDir)\pngusr.h;$(IntDir)\strings.h;$(IntDir)\unistd.h;$(IntDir)\unixio.h"/>
			</FileConfiguration>
			<FileConfiguration
				Name="DLL Debug|Win32">
				<Tool
					Name="VCCustomBuildTool"
					Description="Create dummy include files to prevent &quot;Cannot find missing dependency...&quot; warnings."
					CommandLine="nmake -f PRJ0041.mak IntDir=$(IntDir)"
					Outputs="$(IntDir)\alloc.h;$(IntDir)\fp.h;$(IntDir)\m68881.h;$(IntDir)\mem.h;$(IntDir)\pngusr.h;$(IntDir)\strings.h;$(IntDir)\unistd.h;$(IntDir)\unixio.h"/>
			</FileConfiguration>
			<FileConfiguration
				Name="DLL VB|Win32">
				<Tool
					Name="VCCustomBuildTool"
					Description="Create dummy include files to prevent &quot;Cannot find missing dependency...&quot; warnings."
					CommandLine="nmake -f PRJ0041.mak IntDir=$(IntDir)"
					Outputs="$(IntDir)\alloc.h;$(IntDir)\fp.h;$(IntDir)\m68881.h;$(IntDir)\mem.h;$(IntDir)\pngusr.h;$(IntDir)\strings.h;$(IntDir)\unistd.h;$(IntDir)\unixio.h"/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="README.txt">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
