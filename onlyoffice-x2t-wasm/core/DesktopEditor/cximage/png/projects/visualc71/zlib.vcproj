<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="zlib">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="DLL Release|Win32"
			OutputDirectory=".\Win32_DLL_Release\ZLib"
			IntermediateDirectory=".\Win32_DLL_Release\ZLib"
			ConfigurationType="2">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\..\zlib"
				PreprocessorDefinitions="WIN32;NDEBUG;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				WarningLevel="3"
				CompileAs="1"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)\zlib1.dll"
				LinkIncremental="1"
				ModuleDefinitionFile="..\..\..\zlib\win32\zlib.def"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="DLL Debug|Win32"
			OutputDirectory=".\Win32_DLL_Debug\ZLib"
			IntermediateDirectory=".\Win32_DLL_Debug\ZLib"
			ConfigurationType="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\..\zlib"
				PreprocessorDefinitions="WIN32;_DEBUG;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
				CompileAs="1"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(OutDir)\zlib1d.dll"
				ModuleDefinitionFile="..\..\..\zlib\win32\zlib.def"
				GenerateDebugInformation="TRUE"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="LIB Release|Win32"
			OutputDirectory=".\Win32_LIB_Release\ZLib"
			IntermediateDirectory=".\Win32_LIB_Release\ZLib"
			ConfigurationType="4">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\..\zlib"
				PreprocessorDefinitions="WIN32;NDEBUG;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS"
				StringPooling="TRUE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				WarningLevel="3"
				CompileAs="1"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\zlib.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="LIB Debug|Win32"
			OutputDirectory=".\Win32_LIB_Debug\ZLib"
			IntermediateDirectory=".\Win32_LIB_Debug\ZLib"
			ConfigurationType="4">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\..\zlib"
				PreprocessorDefinitions="WIN32;_DEBUG;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				WarningLevel="3"
				DebugInformationFormat="4"
				CompileAs="1"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\zlibd.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat">
			<File
				RelativePath="..\..\..\zlib\adler32.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\compress.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\crc32.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\deflate.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\gzlib.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\gzclose.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\gzread.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\gzwrite.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\infback.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\inffast.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\inflate.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\inftrees.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\trees.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\uncompr.c">
			</File>
			<File
				RelativePath="..\..\..\zlib\win32\zlib.def">
				<FileConfiguration
					Name="LIB Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\..\..\zlib\zutil.c">
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl">
			<File
				RelativePath="..\..\..\zlib\crc32.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\deflate.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\inffast.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\inffixed.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\inflate.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\inftrees.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\trees.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\zconf.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\zlib.h">
			</File>
			<File
				RelativePath="..\..\..\zlib\zutil.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe">
			<File
				RelativePath="..\..\..\zlib\win32\zlib1.rc">
				<FileConfiguration
					Name="LIB Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCResourceCompilerTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCResourceCompilerTool"/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Assembler Files (Unsupported)"
			Filter="asm;obj;c;cpp;cxx;h;hpp;hxx">
			<File
				RelativePath="..\..\..\zlib\contrib\masmx86\gvmat32.asm">
				<FileConfiguration
					Name="DLL Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="DLL Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\..\..\zlib\contrib\masmx86\gvmat32c.c">
				<FileConfiguration
					Name="DLL Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCLCompilerTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="DLL Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCLCompilerTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCLCompilerTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCLCompilerTool"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\..\..\zlib\contrib\masmx86\inffas32.asm">
				<FileConfiguration
					Name="DLL Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="DLL Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Release|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
				<FileConfiguration
					Name="LIB Debug|Win32"
					ExcludedFromBuild="TRUE">
					<Tool
						Name="VCCustomBuildTool"/>
				</FileConfiguration>
			</File>
		</Filter>
		<File
			RelativePath="README.txt">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
