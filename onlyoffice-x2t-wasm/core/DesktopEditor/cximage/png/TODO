/*
TODO - list of things to do for libpng:

Final bug fixes.
Better C++ wrapper/full C++ implementation?
Fix problem with C++ and EXTERN "C".
cHRM transformation.
Remove setjmp/longjmp usage in favor of returning error codes.
Palette creation.
Add "grayscale->palette" transformation and "palette->grayscale" detection.
Improved dithering.
Multi-lingual error and warning message support.
Complete sRGB transformation (presently it simply uses gamma=0.45455).
Man pages for function calls.
Better documentation.
Better filter selection
   (counting huffman bits/precompression?  filter inertia?  filter costs?).
Histogram creation.
Text conversion between different code pages (Latin-1 -> Mac and DOS).
Avoid building gamma tables whenever possible.
Use greater precision when changing to linear gamma for compositing against
  background and doing rgb-to-gray transformation.
Investigate pre-incremented loop counters and other loop constructions.
Add interpolated method of handling interlacing.
Switch to the simpler zlib (zlib/libpng) license if legally possible.
Extend pngvalid.c to validate more of the libpng transformations.

*/
