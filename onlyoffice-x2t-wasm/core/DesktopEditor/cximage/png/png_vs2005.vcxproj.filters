﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{f725c659-1cac-40da-ac61-294d31f328a2}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;hpj;bat;for;f90</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{d9d674f5-2448-4e56-9ba9-27faa0be8513}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;fi;fd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{00b64795-02b5-4a30-b0ea-ab4ff177429e}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;cnt;rtf;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="png.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngerror.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngget.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngmem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngpread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngread.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngrio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngrtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngrutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngset.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngtrans.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngwio.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngwrite.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngwtran.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pngwutil.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="png.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pngconf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>