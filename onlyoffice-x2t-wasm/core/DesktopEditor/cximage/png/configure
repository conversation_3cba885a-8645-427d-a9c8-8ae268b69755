
echo "
  There is no \"configure\" script in this distribution (*.zip or *.7z) of
  libpng-1.5.26.

  Instead, please copy the appropriate makefile for your system from the
  \"scripts\" directory.  Read the INSTALL file for more details.

  Update, July 2004: you can get a \"configure\" based distribution
  from the libpng distribution sites.  Download the file
  libpng-1.5.26.tar.gz or libpng-1.5.26.tar.xz

  If the line endings in the files look funny, which is likely to be the
  case if you were trying to run \"configure\" on a Linux machine, you may
  wish to get the other distribution of libpng.  It is available in both
  tar.gz/tar.xz (UNIX style line endings, with \"configure\") and .7z/.zip
  (DOS style line endings, without \"configure\") formats.
"

