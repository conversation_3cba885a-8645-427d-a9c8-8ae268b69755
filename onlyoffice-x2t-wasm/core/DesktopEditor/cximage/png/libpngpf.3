.TH LIBPNGPF 3 "December 17, 2015"
.SH NAME
libpng \- Portable Network Graphics (PNG) Reference Library 1.5.26
(private functions)
.SH SYNOPSIS
\fB#include \fI"pngpriv.h"

\fBAs of libpng version \fP\fI1.5.1\fP\fB, this section is no longer \fP\fImaintained\fP\fB, now that the private function prototypes are hidden in pngpriv.h and not accessible to applications. Look in pngpriv.h for the prototypes and a short description of each \fIfunction.

.SH DESCRIPTION
The functions previously listed here are used privately by libpng
and are not recommended for use by applications.  They are
not "exported" to applications using shared libraries.

.SH SEE ALSO
.BR "png"(5), " libpng"(3), " zlib"(3), " deflate"(5), " " and " zlib"(5)
.SH AUTHOR
<PERSON>
