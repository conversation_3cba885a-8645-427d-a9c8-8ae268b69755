# makefile for libpng using gcc (generic, static library)
# Copyright (C) 2002, 2006-2009, 2014 <PERSON>-<PERSON><PERSON>rson
# Copyright (C) 2000 Cosmin Truta
# Copyright (C) 2000 <PERSON> (AIX support added, from makefile.gcc)
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Location of the zlib library and include files
ZLIBINC = ../zlib
ZLIBLIB = ../zlib

# Compiler, linker, lib and other tools
CC = gcc
LD = $(CC)
AR_RC = ar rcs
MKDIR_P = mkdir -p
RANLIB = ranlib
RM_F = rm -f
LN_SF = ln -f -s

LIBNAME = libpng15
PNGMAJ = 15

prefix=/usr/local
INCPATH=$(prefix)/include
LIBPATH=$(prefix)/lib

# override DESTDIR= on the make install command line to easily support
# installing into a temporary location.  Example:
#
#    make install DESTDIR=/tmp/build/libpng
#
# If you're going to install into a temporary location
# via DESTDIR, $(DESTDIR)$(prefix) must already exist before
# you execute make install.
DESTDIR=

DI=$(DESTDIR)$(INCPATH)
DL=$(DESTDIR)$(LIBPATH)

WARNMORE =
CPPFLAGS = -I$(ZLIBINC) # -DPNG_DEBUG=5
CFLAGS = -W -Wall -O2 # $(WARNMORE) -g
LDFLAGS = -L. -L$(ZLIBLIB) -lpng15 -lz -lm

# Variables
OBJS =  png.o pngerror.o pngget.o pngmem.o pngpread.o \
	pngread.o pngrio.o pngrtran.o pngrutil.o pngset.o \
	pngtrans.o pngwio.o pngwrite.o pngwtran.o pngwutil.o

# Targets
.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) -o $@ $<

all: $(LIBNAME).a pngtest$(E)

include scripts/pnglibconf.mak
REMOVE = $(RM_F)
DFNFLAGS = $(DEFS) $(CPPFLAGS)

$(LIBNAME).a: $(OBJS)
	$(AR_RC) $@ $(OBJS)
	$(RANLIB) $@

test: pngtest$(E)
	./pngtest$(E)

pngtest$(E): pngtest.o $(LIBNAME).a
	$(LD) -o $@ pngtest.o $(LDFLAGS)

install: $(LIBNAME).a
	-@if [ ! -d $(DI)  ]; then $(MKDIR_P) $(DI); fi
	-@if [ ! -d $(DI)/$(LIBNAME)  ]; then $(MKDIR_P) $(DI)/$(LIBNAME); fi
	-@if [ ! -d $(DL) ]; then $(MKDIR_P) $(DL); fi
	-@$(RM_F) $(DI)/$(LIBNAME)/png.h
	-@$(RM_F) $(DI)/$(LIBNAME)/pngconf.h
	-@$(RM_F) $(DI)/$(LIBNAME)/pnglibconf.h
	-@$(RM_F) $(DI)/png.h
	-@$(RM_F) $(DI)/pngconf.h
	-@$(RM_F) $(DI)/pnglibconf.h
	cp png.h pngconf.h pnglibconf.h $(DI)/$(LIBNAME)
	chmod 644 $(DI)/$(LIBNAME)/png.h \
	$(DI)/$(LIBNAME)/pngconf.h \
	$(DI)/$(LIBNAME)/pnglibconf.h
	-@$(RM_F) -r $(DI)/libpng
	(cd $(DI); $(LN_SF) $(LIBNAME) libpng; $(LN_SF) $(LIBNAME)/* .)
	-@$(RM_F) $(DL)/$(LIBNAME).a
	-@$(RM_F) $(DL)/libpng.a
	cp $(LIBNAME).a $(DL)/$(LIBNAME).a
	chmod 644 $(DL)/$(LIBNAME).a
	(cd $(DL); $(LN_SF) $(LIBNAME).a libpng.a)
	(cd $(DI); $(LN_SF) libpng/* .;)

clean:
	$(RM_F) *.o $(LIBNAME).a pngtest pngout.png pnglibconf.h

png.o:      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngget.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngmem.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngread.o:  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngset.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwio.o:   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h pnginfo.h pngdebug.h

pngtest.o:  png.h pngconf.h pnglibconf.h
