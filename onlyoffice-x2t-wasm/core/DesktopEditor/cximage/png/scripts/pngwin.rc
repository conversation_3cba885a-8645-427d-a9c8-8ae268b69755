#define PNG_VERSION_INFO_ONLY

#include <windows.h>
#include "../png.h"

#define _QUOTE(x) # x
#define QUOTE(x) _QUOTE(x)

#define PNG_LIBPNG_DLLFNAME "LIBPNG"

/* Support deprecated PRIVATEBUILD macro */
#if defined(PR<PERSON><PERSON><PERSON>BUILD) && !defined(PNG_USER_PRIVATEBUILD)
#  define PNG_USER_PRIVATEBUILD PRIVATEBUILD
#endif

#if defined(PNG_USER_DLLFNAME_POSTFIX) && !defined(PNG_USER_PRIVATEBUILD)
#  error "PNG_USER_PRIVATEBUILD must be defined as a string describing the\
 custom changes made to the library."
#endif

/* Prioritize PNG_USER_x over PNG_LIBPNG_x */
#ifdef PNG_USER_DLLFNAME_POSTFIX
#  undef PNG_LIBPNG_DLLFNAME_POSTFIX
#  define PNG_LIBPNG_DLLFNAME_POSTFIX PNG_USER_DLLFNAME_POSTFIX
#endif

#ifdef PNG_USER_VERSIONINFO_COMMENTS
#  undef PNG_LIBPNG_VERSIONINFO_COMMENTS
#  define PNG_LIBPNG_VERSIONINFO_COMMENTS PNG_USER_VERSIONINFO_COMMENTS
#endif

#if defined(PNG_DEBUG) && (PNG_DEBUG > 0)
#  define VS_DEBUG VS_FF_DEBUG
#  ifndef PNG_LIBPNG_DLLFNAME_POSTFIX
#    define PNG_LIBPNG_DLLFNAME_POSTFIX "D"
#  endif /* PNG_LIBPNG_DLLFNAME_POSTFIX */
#  ifndef PNG_LIBPNG_VERSIONINFO_COMMENTS
#    define PNG_LIBPNG_VERSIONINFO_COMMENTS "PNG_DEBUG=" QUOTE(PNG_DEBUG)
#  endif /* PNG_LIBPNG_VERSIONINFO_COMMENTS */
#else
#  define VS_DEBUG 0
#  ifndef PNG_LIBPNG_DLLFNAME_POSTFIX
#     define PNG_LIBPNG_DLLFNAME_POSTFIX
#  endif /* PNG_LIBPNG_DLLFNAME_POSTFIX */
#endif /* defined(DEBUG)... */

#ifdef PNG_USER_PRIVATEBUILD
#  define VS_PRIVATEBUILD VS_FF_PRIVATEBUILD
#else
#  define VS_PRIVATEBUILD 0
#endif /* PNG_USER_PRIVATEBUILD */

#ifdef PNG_LIBPNG_SPECIALBUILD
#  define VS_SPECIALBUILD VS_FF_SPECIALBUILD
#else
#  define VS_SPECIALBUILD 0
#endif /* PNG_LIBPNG_BUILD_SPECIAL */

#if ((PNG_LIBPNG_BUILD_BASE_TYPE & PNG_LIBPNG_RELEASE_STATUS_MASK) !=\
      PNG_LIBPNG_BUILD_STABLE)
#  define VS_PRERELEASE VS_FF_PRERELEASE
#  define VS_PATCHED 0
#else
#  define VS_PRERELEASE 0
#  if (PNG_LIBPNG_BUILD_BASE_TYPE & PNG_LIBPNG_BUILD_PATCHED)
#    define VS_PATCHED VS_FF_PATCHED
#  else
#    define VS_PATCHED 0
#  endif
#endif

VS_VERSION_INFO VERSIONINFO
FILEVERSION PNG_LIBPNG_VER_MAJOR, PNG_LIBPNG_VER_MINOR, PNG_LIBPNG_VER_RELEASE, PNG_LIBPNG_VER_BUILD
PRODUCTVERSION PNG_LIBPNG_VER_MAJOR, PNG_LIBPNG_VER_MINOR, PNG_LIBPNG_VER_RELEASE, PNG_LIBPNG_VER_BUILD
FILEFLAGSMASK VS_FFI_FILEFLAGSMASK
FILEFLAGS VS_DEBUG | VS_PRIVATEBUILD | VS_SPECIALBUILD | VS_PRERELEASE | VS_PATCHED
FILEOS VOS__WINDOWS32
FILETYPE VFT_DLL
FILESUBTYPE VFT2_UNKNOWN
BEGIN
  BLOCK "StringFileInfo"
  BEGIN BLOCK "040904E4" /* Language type = U.S English(0x0409) and Character Set = Windows, Multilingual(0x04E4) */
    BEGIN
#ifdef PNG_LIBPNG_VERSIONINFO_COMMENTS
      VALUE "Comments", PNG_LIBPNG_VERSIONINFO_COMMENTS "\000"
#endif /* PNG_LIBPNG_VERSIONINFO_COMMENTS */
#ifdef PNG_USER_VERSIONINFO_COMPANYNAME
      VALUE "CompanyName", PNG_USER_VERSIONINFO_COMPANYNAME "\000"
#endif /* PNG_USER_VERSIONINFO_COMPANYNAME */
      VALUE "FileDescription", "PNG image compression library\000"
      VALUE "FileVersion", PNG_LIBPNG_VER_STRING "\000"
      VALUE "InternalName", PNG_LIBPNG_DLLFNAME QUOTE(PNG_LIBPNG_VER_DLLNUM) PNG_LIBPNG_DLLFNAME_POSTFIX " (Windows 32 bit)\000"
      VALUE "LegalCopyright", "\251 1998-2009 Glenn Randers-Pehrson et al.\000"
#ifdef PNG_USER_VERSIONINFO_LEGALTRADEMARKS
      VALUE "LegalTrademarks", PNG_USER_VERSIONINFO_LEGALTRADEMARKS "\000"
#endif /* PNG_USER_VERSIONINFO_LEGALTRADEMARKS */
      VALUE "OriginalFilename", PNG_LIBPNG_DLLFNAME QUOTE(PNG_LIBPNG_VER_DLLNUM) PNG_LIBPNG_DLLFNAME_POSTFIX ".DLL\000"
#ifdef PNG_USER_PRIVATEBUILD
      VALUE "PrivateBuild", PNG_USER_PRIVATEBUILD "\000"
#endif /* PNG_USER_PRIVATEBUILD */
      VALUE "ProductName", "LibPNG\000"
      VALUE "ProductVersion", "1\000"
#ifdef PNG_LIBPNG_SPECIALBUILD
      VALUE "SpecialBuild", PNG_LIBPNG_SPECIALBUILD "\000"
#endif /* PNG_LIBPNG_SPECIALBUILD */
    END
  END
  BLOCK "VarFileInfo"
  BEGIN
    VALUE "Translation", 0x0409, 0x04E4
  END
END
