# makefile for libpng
# Copyright (C) 2002, 2014 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h
#
# Modified for LC56/ATARI assumes libz.lib is in same dir and uses default
# rules for library management
#
CPPFLAGS = -I..\zlib
CFLAGS = -O
LBR = png.lib
LDFLAGS = -L. -L..\zlib -lpng -lz -lm

# where make install puts libpng.a and png.h
prefix=/usr/local
INCPATH=$(prefix)/include
LIBPATH=$(prefix)/lib

# override DESTDIR= on the make install command line to easily support
# installing into a temporary location.  Example:
#
#    make install DESTDIR=/tmp/build/libpng
#
# If you're going to install into a temporary location
# via DESTDIR, $(DESTDIR)$(prefix) must already exist before
# you execute make install.
DESTDIR=

# Pre-built configuration
# See scripts/pnglibconf.mak for more options
PNGLIBCONF_H_PREBUILT = scripts/pnglibconf.h.prebuilt

OBJS = $(LBR)(png.o) $(LBR)(pngset.o) $(LBR)(pngget.o) $(LBR)(pngrutil.o)\
	$(LBR)(pngtrans.o) $(LBR)(pngwutil.o)\
	$(LBR)(pngread.o) $(LBR)(pngerror.o) $(LBR)(pngwrite.o)\
	$(LBR)(pngrtran.o) $(LBR)(pngwtran.o)\
	$(LBR)(pngmem.o) $(LBR)(pngrio.o) $(LBR)(pngwio.o) $(LBR)(pngpread.o)

all: $(LBR) pngtest.ttp

.c.o:
	$(CC) -c $(CPPFLAGS) $(CFLAGS) $<

$(LBR): $(OBJS)

$(OBJS): pngpriv.h png.h pngconf.h pnglibconf.h pnginfo.h pngstruct.h pngdebug.h

pnglibconf.h: $(PNGLIBCONF_H_PREBUILT)
	cp $< $@

pngtest.ttp: pngtest.o $(LBR)
	$(CC) $(CFLAGS) $(LDFLAGS) -o$@ pngtest.o

install: libpng.a
	-@mkdir $(DESTDIR)$(INCPATH)
	-@mkdir $(DESTDIR)$(INCPATH)/libpng
	-@mkdir $(DESTDIR)$(LIBPATH)
	-@rm -f $(DESTDIR)$(INCPATH)/png.h
	-@rm -f $(DESTDIR)$(INCPATH)/pngconf.h
	-@rm -f $(DESTDIR)$(INCPATH)/pnglibconf.h
	cp png.h $(DESTDIR)$(INCPATH)/libpng
	cp pngconf.h $(DESTDIR)$(INCPATH)/libpng
	cp pnglibconf.h $(DESTDIR)$(INCPATH)/libpng
	chmod 644 $(DESTDIR)$(INCPATH)/libpng/png.h
	chmod 644 $(DESTDIR)$(INCPATH)/libpng/pngconf.h
	chmod 644 $(DESTDIR)$(INCPATH)/libpng/pnglibconf.h
	(cd $(DESTDIR)$(INCPATH); ln -f -s $(LIBNAME) libpng; \
	ln -f -s $(LIBNAME)/* .)
