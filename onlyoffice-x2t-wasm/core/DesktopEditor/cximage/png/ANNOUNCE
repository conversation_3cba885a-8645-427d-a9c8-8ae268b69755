
Libpng 1.5.26 - December 17, 2015

This is a public release of libpng, intended for use in production codes.

Files available for download:

Source files with LF line endings (for Unix/Linux) and with a
"configure" script

   libpng-1.5.26.tar.xz (LZMA-compressed, recommended)
   libpng-1.5.26.tar.gz

Source files with CRLF line endings (for Windows), without the
"configure" script

   lpng1526.7z  (LZMA-compressed, recommended)
   lpng1526.zip

Other information:

   libpng-1.5.26-README.txt
   libpng-1.5.26-LICENSE.txt
   libpng-1.5.26-*.asc (armored detached GPG signatures)

Changes since the last public release (1.5.25):
  Fixed an out-of-range read in png_check_keyword() (Bug report from
    Qixue Xiao, CVE-2015-8540).
  Corrected copyright dates in source files.
  Moved png_check_keyword() from pngwutil.c to pngset.c
  Added keyword checks to pngset.c (<PERSON>).

Send comments/corrections/commendations to png-mng-implement at lists.sf.net
(subscription required; visit
https://lists.sourceforge.net/lists/listinfo/png-mng-implement
to subscribe)
or to glennrp at users.sourceforge.net

Glenn R-P
