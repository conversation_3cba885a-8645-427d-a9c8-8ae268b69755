<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="png"
	ProjectGUID="{65818D16-53A8-4EBD-B5B2-2CA493E1DF1A}"
	Keyword="MFCProj"
	>
	<Platforms>
		<Platform
			Name="Smartphone 2003 (ARMV4)"
		/>
		<Platform
			Name="Pocket PC 2003 (ARMV4)"
		/>
		<Platform
			Name="Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Smartphone 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/png.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/png.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\png.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/png.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Pocket PC 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/png.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				AdditionalIncludeDirectories="..\zlib;..\wcecompat\include"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/png.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\png.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/png.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Smartphone 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/png.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/png.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\png.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/png.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Pocket PC 2003 (ARMV4)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/png.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/png.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\png.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/png.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/png.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="0"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="DEBUG;_WIN32_WCE=$(CEVER);$(CePlatform);ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				RuntimeLibrary="1"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/png.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\png.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/png.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
		<Configuration
			Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC60.vsprops"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TypeLibraryName="$(PlatformName)\$(ConfigurationName)/png.tlb"
				HeaderFileName=""
			/>
			<Tool
				Name="VCCLCompilerTool"
				ExecutionBucket="7"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\zlib,..\wcecompat\include"
				PreprocessorDefinitions="_WIN32_WCE=$(CEVER);$(CePlatform);NDEBUG;ARM;_ARM_;ARMV4;UNDER_CE=$(CEVER);UNICODE;_LIB"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderFile="$(PlatformName)\$(ConfigurationName)/png.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)/"
				ObjectFile="$(PlatformName)\$(ConfigurationName)/"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1036"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(PlatformName)\$(ConfigurationName)\png.lib"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile="$(PlatformName)\$(ConfigurationName)/png.bsc"
			/>
			<Tool
				Name="VCCodeSignTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
			<DeploymentTool
				ForceDirty="-1"
				RemoteDirectory=""
				RegisterOutput="0"
				AdditionalFiles=""
			/>
			<DebuggerTool
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath="png.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngerror.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngget.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngmem.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngpread.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngread.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngrio.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngrtran.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngrutil.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngset.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngtrans.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngwio.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngwrite.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngwtran.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pngwutil.c"
				>
				<FileConfiguration
					Name="Debug|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Smartphone 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Pocket PC 2003 (ARMV4)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Windows Mobile 5.0 Pocket PC SDK (ARMV4I)"
					>
					<Tool
						Name="VCCLCompilerTool"
						AdditionalIncludeDirectories=""
						UsePrecompiledHeader="0"
					/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath="png.h"
				>
			</File>
			<File
				RelativePath="pngconf.h"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
