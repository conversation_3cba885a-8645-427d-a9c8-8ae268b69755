This demonstrates the use of PNG_USER_CONFIG and pngusr.h

The makefile builds a minimal read-only progressive decoder with
embedded libpng, zlib and your system's X library.

Specify the location of the zlib source (1.2.1 or later) as ZLIBSRC
on the make command line.

Edit makefile if required, to find your X library and include files,
then

    make ZLIBSRC=directory

If you prefer to use the shared libraries, go to contrib/gregbook
and build the rpng2-x application there.
