# Makefile for PngMinus (png2pnm and pnm2png)
# TurboC++ 3.0

CC=tcc -Ic:\tc3\inc
LD=tcc -Lc:\tc3\lib
LB=tlib
RM=del
CP=copy
MODEL=l
CCFLAGS=-O -m$(MODEL) -I..\libpng -I..\zlib
LDFLAGS=-m$(MODEL) -L..\libpng -L..\zlib
C=.c
O=.obj
L=.lib
E=.exe

# dependencies

all: png2pnm$(E) pnm2png$(E)

png2pnm$(O): png2pnm$(C)
        $(CC) -c $(CCFLAGS) png2pnm$(C)

png2pnm$(E): png2pnm$(O)
        $(LD) $(LDFLAGS) png2pnm$(O) libpng$(L) zlib$(L)

pnm2png$(O): pnm2png$(C)
        $(CC) -c $(CCFLAGS) pnm2png$(C)

pnm2png$(E): pnm2png$(O)
        $(LD) $(LDFLAGS) pnm2png$(O) libpng$(L) zlib$(L)

clean:
        $(RM) *$(O)
        $(RM) *$(E)

# End of makefile for png2pnm / pnm2png

