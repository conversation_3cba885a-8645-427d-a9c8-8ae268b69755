﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{bfaa71c1-da25-4ec5-a132-92b1648fcf06}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{217b36a3-68bd-4cd4-8d07-58876e6333b8}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="adjustment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="bevel_emboss.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="bitmap.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="blend.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="boundary.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="brightness_contrast.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="channel_image.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="channel_mixer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="color.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="color_balance.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="color_mode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="color_overlay.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="curves.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="descriptor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="drop_shadow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="effects.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="file_header.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fixed.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gaussian_blur.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gradient_blend.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gradient_fill.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gradient_map.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gradient_overlay.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hue_saturation.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="image_data.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="image_resource.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="inner_glow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="inner_shadow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="invert.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="layer_mask.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="levels.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="outer_glow.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="path.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pattern.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pattern_fill.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pattern_overlay.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="photo_filter.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="posterize.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="psd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="psd_system.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="psd_zip.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rect.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="satin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="selective_color.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="solid_color.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="stream.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="stroke.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="threshold.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="thumbnail.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="type_tool.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="libpsd.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_bitmap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_blend.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_color.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_descriptor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_fixed.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_gradient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_math.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_rect.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_stream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_system.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="psd_types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>