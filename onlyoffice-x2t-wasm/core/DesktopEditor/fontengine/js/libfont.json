{"name": "fonts", "res_folder": "./deploy", "wasm": true, "asm": true, "run_before": "before.py", "run_after": "after.py", "embed_mem_file": true, "base_js_content": "./engine/module_js.js", "compiler_flags": ["-O3", "-fexceptions", "-Wno-unused-command-line-argument", "-s ALLOW_MEMORY_GROWTH=1", "-s FILESYSTEM=0", "-s ENVIRONMENT='web'", "-s LLD_REPORT_UNDEFINED"], "exported_functions": ["_malloc", "_free", "_ASC_FT_Malloc", "_ASC_FT_Free", "_ASC_FT_Init", "_ASC_FT_Done_FreeType", "_ASC_FT_Set_TrueType_HintProp", "_ASC_FT_Open_Face", "_ASC_FT_Done_Face", "_ASC_FT_SetCMapForCharCode", "_ASC_FT_GetFaceInfo", "_ASC_FT_GetFaceMaxAdvanceX", "_ASC_FT_GetKerningX", "_ASC_FT_Set_Transform", "_ASC_FT_Set_Char_Size", "_ASC_FT_Load_Glyph", "_ASC_FT_Glyph_Get_CBox", "_ASC_FT_Get_Glyph_Measure_Params", "_ASC_FT_Get_Glyph_Render_Params", "_ASC_FT_Get_Glyph_Render_Buffer", "_ASC_HB_LanguageFromString", "_ASC_HB_ShapeText", "_ASC_HB_FontFree"], "include_path": ["./../../graphics/pro/js/freetype-2.10.4/include", "./../../graphics/pro/js/freetype-2.10.4/include/freetype", "./../../graphics/pro/js/freetype-2.10.4/include/freetype/internal", "./../../../Common/3dParty/harfbuzz/harfbuzz/src", "./../../graphics/pro/js/wasm/src/lib", "./../../../OfficeUtils/src", "./../../../OfficeUtils/src/zlib-1.2.11"], "define": ["__linux__", "_LINUX", "UNIX", "NDEBUG", "_LIB", "_CRT_SECURE_NO_WARNINGS", "FT2_BUILD_LIBRARY", "HAVE_FREETYPE", "FT_CONFIG_OPTION_SYSTEM_ZLIB"], "compile_files_array": [{"name": "f", "folder": "./../../graphics/pro/js/freetype-2.10.4/src", "files": ["base/ftdebug.c", "autofit/autofit.c", "bdf/bdf.c", "cff/cff.c", "base/ftbase.c", "base/ftbitmap.c", "base/ftfstype.c", "base/ftgasp.c", "cache/ftcache.c", "base/ftglyph.c", "gzip/ftgzip.c", "base/ftinit.c", "lzw/ftlzw.c", "base/ftstroke.c", "base/ftsystem.c", "smooth/smooth.cpp", "base/ftbbox.c", "base/ftbdf.c", "base/ftcid.c", "base/ftmm.c", "base/ftpfr.c", "base/ftsynth.c", "base/fttype1.c", "base/ftwinfnt.c", "base/ftgxval.c", "base/ftotval.c", "base/ftpatent.c", "pcf/pcf.c", "pfr/pfr.c", "psaux/psaux.c", "pshinter/pshinter.c", "psnames/psmodule.c", "raster/raster.c", "sfnt/sfnt.cpp", "truetype/truetype.c", "type1/type1.c", "cid/type1cid.c", "type42/type42.c", "winfonts/winfnt.c"]}, {"name": "h", "folder": "./../../../Common/3dParty/harfbuzz/harfbuzz/src", "files": ["hb-aat-layout.cc", "hb-aat-map.cc", "hb-blob.cc", "hb-buffer-serialize.cc", "hb-buffer-verify.cc", "hb-buffer.cc", "hb-common.cc", "hb-coretext.cc", "hb-directwrite.cc", "hb-draw.cc", "hb-face.cc", "hb-fallback-shape.cc", "hb-font.cc", "hb-ft.cc", "hb-gdi.cc", "hb-glib.cc", "hb-gobject-structs.cc", "hb-graphite2.cc", "hb-icu.cc", "hb-map.cc", "hb-number.cc", "hb-ot-cff1-table.cc", "hb-ot-cff2-table.cc", "hb-ot-color.cc", "hb-ot-face.cc", "hb-ot-font.cc", "hb-ot-layout.cc", "hb-ot-map.cc", "hb-ot-math.cc", "hb-ot-meta.cc", "hb-ot-metrics.cc", "hb-ot-name.cc", "hb-ot-shape-complex-arabic.cc", "hb-ot-shape-complex-default.cc", "hb-ot-shape-complex-hangul.cc", "hb-ot-shape-complex-hebrew.cc", "hb-ot-shape-complex-indic-table.cc", "hb-ot-shape-complex-indic.cc", "hb-ot-shape-complex-khmer.cc", "hb-ot-shape-complex-myanmar.cc", "hb-ot-shape-complex-syllabic.cc", "hb-ot-shape-complex-thai.cc", "hb-ot-shape-complex-use.cc", "hb-ot-shape-complex-vowel-constraints.cc", "hb-ot-shape-fallback.cc", "hb-ot-shape-normalize.cc", "hb-ot-shape.cc", "hb-ot-tag.cc", "hb-ot-var.cc", "hb-set.cc", "hb-shape-plan.cc", "hb-shape.cc", "hb-shaper.cc", "hb-static.cc", "hb-style.cc", "hb-subset-cff-common.cc", "hb-subset-cff1.cc", "hb-subset-cff2.cc", "hb-subset-input.cc", "hb-subset-plan.cc", "hb-subset.cc", "hb-ucd.cc", "hb-unicode.cc", "hb-uniscribe.cc"]}, {"name": "z", "folder": "../../../OfficeUtils/src/zlib-1.2.11/", "files": ["adler32.c", "crc32.c", "deflate.c", "infback.c", "inffast.c", "inflate.c", "inftrees.c", "trees.c", "zutil.c", "compress.c"]}, {"name": "own", "folder": "cpp", "files": ["text.cpp"]}, {"name": "w", "folder": "./../../graphics/pro/js/wasm/src/lib", "files": ["wasm_jmp.cpp"]}]}