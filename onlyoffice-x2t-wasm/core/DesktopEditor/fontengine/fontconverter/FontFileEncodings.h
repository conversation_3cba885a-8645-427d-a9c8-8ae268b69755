﻿/*
 * (c) Copyright Ascensio System SIA 2010-2019
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-12 Ernesta Birznieka-Upisha
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */
#ifndef _ASC_FONTCONVERTER_FONNT_FILE_ENCODINGS_H
#define _ASC_FONTCONVERTER_FONNT_FILE_ENCODINGS_H

#include "../../common/Types.h"
#include <string>

namespace NSFontConverter
{
    class CWCharWrapper
    {
    public:

        CWCharWrapper():m_cwsString(NULL)
        {
        }

        CWCharWrapper(const wchar_t* cwsString):m_cwsString(cwsString)
        {
        }

        CWCharWrapper(const std::wstring& cwsString):m_cwsString(cwsString.c_str())
        {
        }

        inline const CWCharWrapper operator=(const wchar_t* cwsOther)
        {
            m_cwsString = cwsOther;
            return *this;
        }

        inline const bool operator==(const wchar_t* cwsOther)
        {
            if ( 0 == WStrCmp( m_cwsString, cwsOther ) )
                return true;

            return false;
        }

        inline const bool operator!=(const wchar_t* cwsOther)
        {
            if ( 0 != WStrCmp( m_cwsString, cwsOther ) )
                return true;

            return false;
        }

        inline const wchar_t operator[](const int nIndex) const
        {
            int nLen = GetLength();

            if ( nIndex >= nLen )
                return '\0';

            return m_cwsString[nIndex];
        }
        inline const bool IsNull() const
        {
            return (m_cwsString == NULL);
        }


        inline const int  GetLength() const
        {
            if ( NULL == m_cwsString )
                return 0;

            return (const int)wcslen( m_cwsString );
        }
    public:
        static inline int WStrCmp(const wchar_t* cwsStr1, const wchar_t* cwsStr2)
        {
            if ( NULL == cwsStr1 && NULL == cwsStr2 )
                return 0;
            else if ( NULL == cwsStr1 || NULL == cwsStr2 )
                return -1;

            return wcscmp( cwsStr1, cwsStr2 );
        }

    public:

        const wchar_t* m_cwsString;
    };

    inline const bool operator==(const wchar_t* cwsStr1, const CWCharWrapper& cwsStr2)
    {
        if ( 0 == CWCharWrapper::WStrCmp( cwsStr2.m_cwsString, cwsStr1 ) )
            return true;

        return false;
    }

    inline const bool operator!=(const wchar_t* cwsStr1, const CWCharWrapper& cwsStr2)
    {
        if ( 0 != CWCharWrapper::WStrCmp( cwsStr2.m_cwsString, cwsStr1 ) )
            return true;

        return false;
    }
    //------------------------------------------------------------------------
    // Type 1 and 1C font data
    //------------------------------------------------------------------------

    static char *c_arrsFontFileType1StandardEncoding[256] =
    {
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        "space",
        "exclam",
        "quotedbl",
        "numbersign",
        "dollar",
        "percent",
        "ampersand",
        "quoteright",
        "parenleft",
        "parenright",
        "asterisk",
        "plus",
        "comma",
        "hyphen",
        "period",
        "slash",
        "zero",
        "one",
        "two",
        "three",
        "four",
        "five",
        "six",
        "seven",
        "eight",
        "nine",
        "colon",
        "semicolon",
        "less",
        "equal",
        "greater",
        "question",
        "at",
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z",
        "bracketleft",
        "backslash",
        "bracketright",
        "asciicircum",
        "underscore",
        "quoteleft",
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n",
        "o",
        "p",
        "q",
        "r",
        "s",
        "t",
        "u",
        "v",
        "w",
        "x",
        "y",
        "z",
        "braceleft",
        "bar",
        "braceright",
        "asciitilde",
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        "exclamdown",
        "cent",
        "sterling",
        "fraction",
        "yen",
        "florin",
        "section",
        "currency",
        "quotesingle",
        "quotedblleft",
        "guillemotleft",
        "guilsinglleft",
        "guilsinglright",
        "fi",
        "fl",
        NULL,
        "endash",
        "dagger",
        "daggerdbl",
        "periodcentered",
        NULL,
        "paragraph",
        "bullet",
        "quotesinglbase",
        "quotedblbase",
        "quotedblright",
        "guillemotright",
        "ellipsis",
        "perthousand",
        NULL,
        "questiondown",
        NULL,
        "grave",
        "acute",
        "circumflex",
        "tilde",
        "macron",
        "breve",
        "dotaccent",
        "dieresis",
        NULL,
        "ring",
        "cedilla",
        NULL,
        "hungarumlaut",
        "ogonek",
        "caron",
        "emdash",
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        "AE",
        NULL,
        "ordfeminine",
        NULL,
        NULL,
        NULL,
        NULL,
        "Lslash",
        "Oslash",
        "OE",
        "ordmasculine",
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        "ae",
        NULL,
        NULL,
        NULL,
        "dotlessi",
        NULL,
        NULL,
        "lslash",
        "oslash",
        "oe",
        "germandbls",
        NULL,
        NULL,
        NULL,
        NULL
    };

    static char *c_arrsFontFileType1ExpertEncoding[256] =
    {
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        "space",
        "exclamsmall",
        "Hungarumlautsmall",
        NULL,
        "dollaroldstyle",
        "dollarsuperior",
        "ampersandsmall",
        "Acutesmall",
        "parenleftsuperior",
        "parenrightsuperior",
        "twodotenleader",
        "onedotenleader",
        "comma",
        "hyphen",
        "period",
        "fraction",
        "zerooldstyle",
        "oneoldstyle",
        "twooldstyle",
        "threeoldstyle",
        "fouroldstyle",
        "fiveoldstyle",
        "sixoldstyle",
        "sevenoldstyle",
        "eightoldstyle",
        "nineoldstyle",
        "colon",
        "semicolon",
        "commasuperior",
        "threequartersemdash",
        "periodsuperior",
        "questionsmall",
        NULL,
        "asuperior",
        "bsuperior",
        "centsuperior",
        "dsuperior",
        "esuperior",
        NULL,
        NULL,
        NULL,
        "isuperior",
        NULL,
        NULL,
        "lsuperior",
        "msuperior",
        "nsuperior",
        "osuperior",
        NULL,
        NULL,
        "rsuperior",
        "ssuperior",
        "tsuperior",
        NULL,
        "ff",
        "fi",
        "fl",
        "ffi",
        "ffl",
        "parenleftinferior",
        NULL,
        "parenrightinferior",
        "Circumflexsmall",
        "hyphensuperior",
        "Gravesmall",
        "Asmall",
        "Bsmall",
        "Csmall",
        "Dsmall",
        "Esmall",
        "Fsmall",
        "Gsmall",
        "Hsmall",
        "Ismall",
        "Jsmall",
        "Ksmall",
        "Lsmall",
        "Msmall",
        "Nsmall",
        "Osmall",
        "Psmall",
        "Qsmall",
        "Rsmall",
        "Ssmall",
        "Tsmall",
        "Usmall",
        "Vsmall",
        "Wsmall",
        "Xsmall",
        "Ysmall",
        "Zsmall",
        "colonmonetary",
        "onefitted",
        "rupiah",
        "Tildesmall",
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        "exclamdownsmall",
        "centoldstyle",
        "Lslashsmall",
        NULL,
        NULL,
        "Scaronsmall",
        "Zcaronsmall",
        "Dieresissmall",
        "Brevesmall",
        "Caronsmall",
        NULL,
        "Dotaccentsmall",
        NULL,
        NULL,
        "Macronsmall",
        NULL,
        NULL,
        "figuredash",
        "hypheninferior",
        NULL,
        NULL,
        "Ogoneksmall",
        "Ringsmall",
        "Cedillasmall",
        NULL,
        NULL,
        NULL,
        "onequarter",
        "onehalf",
        "threequarters",
        "questiondownsmall",
        "oneeighth",
        "threeeighths",
        "fiveeighths",
        "seveneighths",
        "onethird",
        "twothirds",
        NULL,
        NULL,
        "zerosuperior",
        "onesuperior",
        "twosuperior",
        "threesuperior",
        "foursuperior",
        "fivesuperior",
        "sixsuperior",
        "sevensuperior",
        "eightsuperior",
        "ninesuperior",
        "zeroinferior",
        "oneinferior",
        "twoinferior",
        "threeinferior",
        "fourinferior",
        "fiveinferior",
        "sixinferior",
        "seveninferior",
        "eightinferior",
        "nineinferior",
        "centinferior",
        "dollarinferior",
        "periodinferior",
        "commainferior",
        "Agravesmall",
        "Aacutesmall",
        "Acircumflexsmall",
        "Atildesmall",
        "Adieresissmall",
        "Aringsmall",
        "AEsmall",
        "Ccedillasmall",
        "Egravesmall",
        "Eacutesmall",
        "Ecircumflexsmall",
        "Edieresissmall",
        "Igravesmall",
        "Iacutesmall",
        "Icircumflexsmall",
        "Idieresissmall",
        "Ethsmall",
        "Ntildesmall",
        "Ogravesmall",
        "Oacutesmall",
        "Ocircumflexsmall",
        "Otildesmall",
        "Odieresissmall",
        "OEsmall",
        "Oslashsmall",
        "Ugravesmall",
        "Uacutesmall",
        "Ucircumflexsmall",
        "Udieresissmall",
        "Yacutesmall",
        "Thornsmall",
        "Ydieresissmall"
    };


    //------------------------------------------------------------------------
    // Type 1C font data
    //------------------------------------------------------------------------

    static char *c_arrsFontFileType1CStandardStrings[391] =
    {
        ".notdef",
        "space",
        "exclam",
        "quotedbl",
        "numbersign",
        "dollar",
        "percent",
        "ampersand",
        "quoteright",
        "parenleft",
        "parenright",
        "asterisk",
        "plus",
        "comma",
        "hyphen",
        "period",
        "slash",
        "zero",
        "one",
        "two",
        "three",
        "four",
        "five",
        "six",
        "seven",
        "eight",
        "nine",
        "colon",
        "semicolon",
        "less",
        "equal",
        "greater",
        "question",
        "at",
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z",
        "bracketleft",
        "backslash",
        "bracketright",
        "asciicircum",
        "underscore",
        "quoteleft",
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n",
        "o",
        "p",
        "q",
        "r",
        "s",
        "t",
        "u",
        "v",
        "w",
        "x",
        "y",
        "z",
        "braceleft",
        "bar",
        "braceright",
        "asciitilde",
        "exclamdown",
        "cent",
        "sterling",
        "fraction",
        "yen",
        "florin",
        "section",
        "currency",
        "quotesingle",
        "quotedblleft",
        "guillemotleft",
        "guilsinglleft",
        "guilsinglright",
        "fi",
        "fl",
        "endash",
        "dagger",
        "daggerdbl",
        "periodcentered",
        "paragraph",
        "bullet",
        "quotesinglbase",
        "quotedblbase",
        "quotedblright",
        "guillemotright",
        "ellipsis",
        "perthousand",
        "questiondown",
        "grave",
        "acute",
        "circumflex",
        "tilde",
        "macron",
        "breve",
        "dotaccent",
        "dieresis",
        "ring",
        "cedilla",
        "hungarumlaut",
        "ogonek",
        "caron",
        "emdash",
        "AE",
        "ordfeminine",
        "Lslash",
        "Oslash",
        "OE",
        "ordmasculine",
        "ae",
        "dotlessi",
        "lslash",
        "oslash",
        "oe",
        "germandbls",
        "onesuperior",
        "logicalnot",
        "mu",
        "trademark",
        "Eth",
        "onehalf",
        "plusminus",
        "Thorn",
        "onequarter",
        "divide",
        "brokenbar",
        "degree",
        "thorn",
        "threequarters",
        "twosuperior",
        "registered",
        "minus",
        "eth",
        "multiply",
        "threesuperior",
        "copyright",
        "Aacute",
        "Acircumflex",
        "Adieresis",
        "Agrave",
        "Aring",
        "Atilde",
        "Ccedilla",
        "Eacute",
        "Ecircumflex",
        "Edieresis",
        "Egrave",
        "Iacute",
        "Icircumflex",
        "Idieresis",
        "Igrave",
        "Ntilde",
        "Oacute",
        "Ocircumflex",
        "Odieresis",
        "Ograve",
        "Otilde",
        "Scaron",
        "Uacute",
        "Ucircumflex",
        "Udieresis",
        "Ugrave",
        "Yacute",
        "Ydieresis",
        "Zcaron",
        "aacute",
        "acircumflex",
        "adieresis",
        "agrave",
        "aring",
        "atilde",
        "ccedilla",
        "eacute",
        "ecircumflex",
        "edieresis",
        "egrave",
        "iacute",
        "icircumflex",
        "idieresis",
        "igrave",
        "ntilde",
        "oacute",
        "ocircumflex",
        "odieresis",
        "ograve",
        "otilde",
        "scaron",
        "uacute",
        "ucircumflex",
        "udieresis",
        "ugrave",
        "yacute",
        "ydieresis",
        "zcaron",
        "exclamsmall",
        "Hungarumlautsmall",
        "dollaroldstyle",
        "dollarsuperior",
        "ampersandsmall",
        "Acutesmall",
        "parenleftsuperior",
        "parenrightsuperior",
        "twodotenleader",
        "onedotenleader",
        "zerooldstyle",
        "oneoldstyle",
        "twooldstyle",
        "threeoldstyle",
        "fouroldstyle",
        "fiveoldstyle",
        "sixoldstyle",
        "sevenoldstyle",
        "eightoldstyle",
        "nineoldstyle",
        "commasuperior",
        "threequartersemdash",
        "periodsuperior",
        "questionsmall",
        "asuperior",
        "bsuperior",
        "centsuperior",
        "dsuperior",
        "esuperior",
        "isuperior",
        "lsuperior",
        "msuperior",
        "nsuperior",
        "osuperior",
        "rsuperior",
        "ssuperior",
        "tsuperior",
        "ff",
        "ffi",
        "ffl",
        "parenleftinferior",
        "parenrightinferior",
        "Circumflexsmall",
        "hyphensuperior",
        "Gravesmall",
        "Asmall",
        "Bsmall",
        "Csmall",
        "Dsmall",
        "Esmall",
        "Fsmall",
        "Gsmall",
        "Hsmall",
        "Ismall",
        "Jsmall",
        "Ksmall",
        "Lsmall",
        "Msmall",
        "Nsmall",
        "Osmall",
        "Psmall",
        "Qsmall",
        "Rsmall",
        "Ssmall",
        "Tsmall",
        "Usmall",
        "Vsmall",
        "Wsmall",
        "Xsmall",
        "Ysmall",
        "Zsmall",
        "colonmonetary",
        "onefitted",
        "rupiah",
        "Tildesmall",
        "exclamdownsmall",
        "centoldstyle",
        "Lslashsmall",
        "Scaronsmall",
        "Zcaronsmall",
        "Dieresissmall",
        "Brevesmall",
        "Caronsmall",
        "Dotaccentsmall",
        "Macronsmall",
        "figuredash",
        "hypheninferior",
        "Ogoneksmall",
        "Ringsmall",
        "Cedillasmall",
        "questiondownsmall",
        "oneeighth",
        "threeeighths",
        "fiveeighths",
        "seveneighths",
        "onethird",
        "twothirds",
        "zerosuperior",
        "foursuperior",
        "fivesuperior",
        "sixsuperior",
        "sevensuperior",
        "eightsuperior",
        "ninesuperior",
        "zeroinferior",
        "oneinferior",
        "twoinferior",
        "threeinferior",
        "fourinferior",
        "fiveinferior",
        "sixinferior",
        "seveninferior",
        "eightinferior",
        "nineinferior",
        "centinferior",
        "dollarinferior",
        "periodinferior",
        "commainferior",
        "Agravesmall",
        "Aacutesmall",
        "Acircumflexsmall",
        "Atildesmall",
        "Adieresissmall",
        "Aringsmall",
        "AEsmall",
        "Ccedillasmall",
        "Egravesmall",
        "Eacutesmall",
        "Ecircumflexsmall",
        "Edieresissmall",
        "Igravesmall",
        "Iacutesmall",
        "Icircumflexsmall",
        "Idieresissmall",
        "Ethsmall",
        "Ntildesmall",
        "Ogravesmall",
        "Oacutesmall",
        "Ocircumflexsmall",
        "Otildesmall",
        "Odieresissmall",
        "OEsmall",
        "Oslashsmall",
        "Ugravesmall",
        "Uacutesmall",
        "Ucircumflexsmall",
        "Udieresissmall",
        "Yacutesmall",
        "Thornsmall",
        "Ydieresissmall",
        "001.000",
        "001.001",
        "001.002",
        "001.003",
        "Black",
        "Bold",
        "Book",
        "Light",
        "Medium",
        "Regular",
        "Roman",
        "Semibold"
    };

    static unsigned short c_arrnFontFileType1CISOAdobeCharset[229] =
    {
        0,   1,   2,   3,   4,   5,   6,   7,   8,   9,
        10,  11,  12,  13,  14,  15,  16,  17,  18,  19,
        20,  21,  22,  23,  24,  25,  26,  27,  28,  29,
        30,  31,  32,  33,  34,  35,  36,  37,  38,  39,
        40,  41,  42,  43,  44,  45,  46,  47,  48,  49,
        50,  51,  52,  53,  54,  55,  56,  57,  58,  59,
        60,  61,  62,  63,  64,  65,  66,  67,  68,  69,
        70,  71,  72,  73,  74,  75,  76,  77,  78,  79,
        80,  81,  82,  83,  84,  85,  86,  87,  88,  89,
        90,  91,  92,  93,  94,  95,  96,  97,  98,  99,
        100, 101, 102, 103, 104, 105, 106, 107, 108, 109,
        110, 111, 112, 113, 114, 115, 116, 117, 118, 119,
        120, 121, 122, 123, 124, 125, 126, 127, 128, 129,
        130, 131, 132, 133, 134, 135, 136, 137, 138, 139,
        140, 141, 142, 143, 144, 145, 146, 147, 148, 149,
        150, 151, 152, 153, 154, 155, 156, 157, 158, 159,
        160, 161, 162, 163, 164, 165, 166, 167, 168, 169,
        170, 171, 172, 173, 174, 175, 176, 177, 178, 179,
        180, 181, 182, 183, 184, 185, 186, 187, 188, 189,
        190, 191, 192, 193, 194, 195, 196, 197, 198, 199,
        200, 201, 202, 203, 204, 205, 206, 207, 208, 209,
        210, 211, 212, 213, 214, 215, 216, 217, 218, 219,
        220, 221, 222, 223, 224, 225, 226, 227, 228
    };

    static unsigned short c_arrnFontFileType1CExpertCharset[166] =
    {
        0,   1,   229, 230, 231, 232, 233, 234, 235, 236,
        237, 238, 13,  14,  15,  99,  239, 240, 241, 242,
        243, 244, 245, 246, 247, 248, 27,  28,  249, 250,
        251, 252, 253, 254, 255, 256, 257, 258, 259, 260,
        261, 262, 263, 264, 265, 266, 109, 110, 267, 268,
        269, 270, 271, 272, 273, 274, 275, 276, 277, 278,
        279, 280, 281, 282, 283, 284, 285, 286, 287, 288,
        289, 290, 291, 292, 293, 294, 295, 296, 297, 298,
        299, 300, 301, 302, 303, 304, 305, 306, 307, 308,
        309, 310, 311, 312, 313, 314, 315, 316, 317, 318,
        158, 155, 163, 319, 320, 321, 322, 323, 324, 325,
        326, 150, 164, 169, 327, 328, 329, 330, 331, 332,
        333, 334, 335, 336, 337, 338, 339, 340, 341, 342,
        343, 344, 345, 346, 347, 348, 349, 350, 351, 352,
        353, 354, 355, 356, 357, 358, 359, 360, 361, 362,
        363, 364, 365, 366, 367, 368, 369, 370, 371, 372,
        373, 374, 375, 376, 377, 378
    };

    static unsigned short c_arrnFontFileType1CExpertSubsetCharset[87] =
    {
        0,   1,   231, 232, 235, 236, 237, 238, 13,  14,
        15,  99,  239, 240, 241, 242, 243, 244, 245, 246,
        247, 248, 27,  28,  249, 250, 251, 253, 254, 255,
        256, 257, 258, 259, 260, 261, 262, 263, 264, 265,
        266, 109, 110, 267, 268, 269, 270, 272, 300, 301,
        302, 305, 314, 315, 158, 155, 163, 320, 321, 322,
        323, 324, 325, 326, 150, 164, 169, 327, 328, 329,
        330, 331, 332, 333, 334, 335, 336, 337, 338, 339,
        340, 341, 342, 343, 344, 345, 346
    };

    int Type1NameToUnicodeW(CWCharWrapper wsName);
    int Type1NameToUnicodeA(const char *sName);

    #define CFF_STANDARD_STRINGS_COUNT 391 // 0..390

    static const wchar_t* c_arrwsCFFStrings[] =
    {
        L".notdef", L"space", L"exclam", L"quotedbl", L"numbersign", L"dollar", L"percent",
        L"ampersand", L"quoteright", L"parenleft", L"parenright", L"asterisk", L"plus",
        L"comma", L"hyphen", L"period", L"slash", L"zero", L"one", L"two", L"three", L"four",
        L"five", L"six", L"seven", L"eight", L"nine", L"colon", L"semicolon", L"less",
        L"equal", L"greater", L"question", L"at", L"A", L"B", L"C", L"D", L"E", L"F", L"G", L"H",
        L"I", L"J", L"K", L"L", L"M", L"N", L"O", L"P", L"Q", L"R", L"S", L"T", L"U", L"V", L"W",
        L"X", L"Y", L"Z", L"bracketleft", L"backslash", L"bracketright", L"asciicircum",
        L"underscore", L"quoteleft", L"a", L"b", L"c", L"d", L"e", L"f", L"g", L"h", L"i", L"j",
        L"k", L"l", L"m", L"n", L"o", L"p", L"q", L"r", L"s", L"t", L"u", L"v", L"w", L"x", L"y",
        L"z", L"braceleft", L"bar", L"braceright", L"asciitilde", L"exclamdown", L"cent",
        L"sterling", L"fraction", L"yen", L"florin", L"section", L"currency",
        L"quotesingle", L"quotedblleft", L"guillemotleft", L"guilsinglleft",
        L"guilsinglright", L"fi", L"fl", L"endash", L"dagger", L"daggerdbl",
        L"periodcentered", L"paragraph", L"bullet", L"quotesinglbase", L"quotedblbase",
        L"quotedblright", L"guillemotright", L"ellipsis", L"perthousand", L"questiondown",
        L"grave", L"acute", L"circumflex", L"tilde", L"macron", L"breve", L"dotaccent",
        L"dieresis", L"ring", L"cedilla", L"hungarumlaut", L"ogonek", L"caron", L"emdash",
        L"AE", L"ordfeminine", L"Lslash", L"Oslash", L"OE", L"ordmasculine", L"ae",
        L"dotlessi", L"lslash", L"oslash", L"oe", L"germandbls", L"onesuperior",
        L"logicalnot", L"mu", L"trademark", L"Eth", L"onehalf", L"plusminus", L"Thorn",
        L"onequarter", L"divide", L"brokenbar", L"degree", L"thorn", L"threequarters",
        L"twosuperior", L"registered", L"minus", L"eth", L"multiply", L"threesuperior",
        L"copyright", L"Aacute", L"Acircumflex", L"Adieresis", L"Agrave", L"Aring",
        L"Atilde", L"Ccedilla", L"Eacute", L"Ecircumflex", L"Edieresis", L"Egrave",
        L"Iacute", L"Icircumflex", L"Idieresis", L"Igrave", L"Ntilde", L"Oacute",
        L"Ocircumflex", L"Odieresis", L"Ograve", L"Otilde", L"Scaron", L"Uacute",
        L"Ucircumflex", L"Udieresis", L"Ugrave", L"Yacute", L"Ydieresis", L"Zcaron",
        L"aacute", L"acircumflex", L"adieresis", L"agrave", L"aring", L"atilde",
        L"ccedilla", L"eacute", L"ecircumflex", L"edieresis", L"egrave", L"iacute",
        L"icircumflex", L"idieresis", L"igrave", L"ntilde", L"oacute", L"ocircumflex",
        L"odieresis", L"ograve", L"otilde", L"scaron", L"uacute", L"ucircumflex",
        L"udieresis", L"ugrave", L"yacute", L"ydieresis", L"zcaron", L"exclamsmall",
        L"Hungarumlautsmall", L"dollaroldstyle", L"dollarsuperior", L"ampersandsmall",
        L"Acutesmall", L"parenleftsuperior", L"parenrightsuperior", L"266 ff",
        L"onedotenleader", L"zerooldstyle", L"oneoldstyle", L"twooldstyle",
        L"threeoldstyle", L"fouroldstyle", L"fiveoldstyle", L"sixoldstyle",
        L"sevenoldstyle", L"eightoldstyle", L"nineoldstyle", L"commasuperior",
        L"threequartersemdash", L"periodsuperior", L"questionsmall", L"asuperior",
        L"bsuperior", L"centsuperior", L"dsuperior", L"esuperior", L"isuperior",
        L"lsuperior", L"msuperior", L"nsuperior", L"osuperior", L"rsuperior", L"ssuperior",
        L"tsuperior", L"ff", L"ffi", L"ffl", L"parenleftinferior", L"parenrightinferior",
        L"Circumflexsmall", L"hyphensuperior", L"Gravesmall", L"Asmall", L"Bsmall",
        L"Csmall", L"Dsmall", L"Esmall", L"Fsmall", L"Gsmall", L"Hsmall", L"Ismall",
        L"Jsmall", L"Ksmall", L"Lsmall", L"Msmall", L"Nsmall", L"Osmall", L"Psmall",
        L"Qsmall", L"Rsmall", L"Ssmall", L"Tsmall", L"Usmall", L"Vsmall", L"Wsmall",
        L"Xsmall", L"Ysmall", L"Zsmall", L"colonmonetary", L"onefitted", L"rupiah",
        L"Tildesmall", L"exclamdownsmall", L"centoldstyle", L"Lslashsmall",
        L"Scaronsmall", L"Zcaronsmall", L"Dieresissmall", L"Brevesmall", L"Caronsmall",
        L"Dotaccentsmall", L"Macronsmall", L"figuredash", L"hypheninferior",
        L"Ogoneksmall", L"Ringsmall", L"Cedillasmall", L"questiondownsmall", L"oneeighth",
        L"threeeighths", L"fiveeighths", L"seveneighths", L"onethird", L"twothirds",
        L"zerosuperior", L"foursuperior", L"fivesuperior", L"sixsuperior",
        L"sevensuperior", L"eightsuperior", L"ninesuperior", L"zeroinferior",
        L"oneinferior", L"twoinferior", L"threeinferior", L"fourinferior",
        L"fiveinferior", L"sixinferior", L"seveninferior", L"eightinferior",
        L"nineinferior", L"centinferior", L"dollarinferior", L"periodinferior",
        L"commainferior", L"Agravesmall", L"Aacutesmall", L"Acircumflexsmall",
        L"Atildesmall", L"Adieresissmall", L"Aringsmall", L"AEsmall", L"Ccedillasmall",
        L"Egravesmall", L"Eacutesmall", L"Ecircumflexsmall", L"Edieresissmall",
        L"Igravesmall", L"Iacutesmall", L"Icircumflexsmall", L"Idieresissmall",
        L"Ethsmall", L"Ntildesmall", L"Ogravesmall", L"Oacutesmall", L"Ocircumflexsmall",
        L"Otildesmall", L"Odieresissmall", L"OEsmall", L"Oslashsmall", L"Ugravesmall",
        L"Uacutesmall", L"Ucircumflexsmall", L"Udieresissmall", L"Yacutesmall",
        L"Thornsmall", L"Ydieresissmall", L"001.000", L"001.001", L"001.002", L"001.003",
        L"Black", L"Bold", L"Book", L"Light", L"Medium", L"Regular", L"Roman", L"Semibold", NULL
    };

    static int GetCFFStringIndex(CWCharWrapper wsString)
    {
        int nPos = 0;
        while ( NULL != c_arrwsCFFStrings[nPos] )
        {
            if ( wsString == c_arrwsCFFStrings[nPos] )
                return nPos;

            nPos++;
        }

        return -1;
    }
}

#endif /* _ASC_FONTCONVERTER_FONNT_FILE_ENCODINGS_H */
