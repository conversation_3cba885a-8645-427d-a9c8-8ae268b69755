﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{7eb751f0-d082-4683-8373-c9b51c720f65}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{8169025b-faf9-43b2-98f3-39a195296214}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Common">
      <UniqueIdentifier>{99808c44-bc8b-4694-a986-8faa3a9de832}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ApplicationFonts.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FontFile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FontManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FontPath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GlyphString.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ApplicationFontsWorker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FontsAssistant.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fontconverter\FontConverter.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fontconverter\FontFileEncodings.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fontconverter\FontFileTrueType.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fontconverter\FontFileType1.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fontconverter\FontFileType1C.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fontconverter\Hash.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fontconverter\StringExt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\graphics\pro\pro_Fonts.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ApplicationFonts.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FontFile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FontManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FontPath.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GlyphString.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\common\Directory.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="..\common\File.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="application_generate_fonts.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="application_generate_fonts_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ApplicationFontsWorker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FontConverter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontdictionaryworker.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FontsAssistant.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MemoryStream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\Consts.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\FontConverter.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\FontFileBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\FontFileEncodings.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\FontFileTrueType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\FontFileType1.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\FontFileType1C.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\Hash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\MemoryUtils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\StringExt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fontconverter\Utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>