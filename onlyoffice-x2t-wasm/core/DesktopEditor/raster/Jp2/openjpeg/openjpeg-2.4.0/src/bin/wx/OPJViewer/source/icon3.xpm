/* XPM */
static const char *icon3_xpm[] = {
/* columns rows colors chars-per-pixel */
"32 32 41 1",
"6 c #EDF2FB",
"- c #AAC1E8",
": c #B9CDED",
"X c #295193",
", c #C6D6F0",
"a c #4A7CCE",
"u c #779DDB",
"y c #7FA2DD",
"$ c #3263B4",
"5 c #EAF0FA",
". c #2D59A3",
"o c #6E96D8",
"* c #356AC1",
"r c #F7F9FD",
"> c #BED0EE",
"3 c #E1E9F7",
"7 c #F0F5FC",
"< c #CBD9F1",
"2 c #DAE5F6",
"# c #3161B1",
"  c None",
"0 c #FDFEFF",
"= c #9FB9E5",
"e c #AEC5EA",
"t c #89A9DF",
"q c #98B5E4",
"p c #5584D1",
"d c #3A70CA",
"@ c #305FAC",
"i c #5D89D3",
"1 c #D2DFF4",
"% c #3366B9",
"9 c #FAFCFE",
"8 c #F5F8FD",
"s c #4075CC",
"O c #638ED5",
"w c #90AFE2",
"& c #3467BC",
"+ c #2F5DA9",
"; c #B3C8EB",
"4 c #E5EDF9",
/* pixels */
"                                ",
"                                ",
"                                ",
"                                ",
"                                ",
"                                ",
"      ......X                   ",
"      .oooooO+                  ",
"      .ooooooo.                 ",
"      .+@@@##$%%&&&&&****.      ",
"      .=-;:>,<12345678900.      ",
"      .q=-;:>,<1234567890.      ",
"      .wq=-e:>,<12345678r.      ",
"      .twq=-e:>,<12345678.      ",
"      .ytwq=-e:>,<1234567.      ",
"      .uytwq=-e:>,<123456.      ",
"      .ouytwq=-e:>,<12345.      ",
"      .Oouytwq=-e;>,<1234.      ",
"      .iOouytwq=-e;>,<123.      ",
"      .piOouytwq=-e;>,<12.      ",
"      .apiOouytwq=-e;>,<1.      ",
"      .sapiOouytwq=-e;>,<.      ",
"      .dsapiOouytwq=-e;>,.      ",
"      ...................#      ",
"                                ",
"                                ",
"                                ",
"                                ",
"                                ",
"                                ",
"                                ",
"                                "
};
