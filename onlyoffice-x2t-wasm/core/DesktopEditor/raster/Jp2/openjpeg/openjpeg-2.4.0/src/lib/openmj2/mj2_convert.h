/*
* Copyright (c) 2003-2004, <PERSON><PERSON><PERSON><PERSON>
* Copyright (c) 2002-2004,  Communications and remote sensing Laboratory, Universite catholique de Louvain, Belgium
* All rights reserved.
*
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions
* are met:
* 1. Redistributions of source code must retain the above copyright
*    notice, this list of conditions and the following disclaimer.
* 2. Redistributions in binary form must reproduce the above copyright
*    notice, this list of conditions and the following disclaimer in the
*    documentation and/or other materials provided with the distribution.
*
* THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS `AS IS'
* AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
* IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
* ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
* LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
* CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
* SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
* INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
* CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
* ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
* POSSIBILITY OF SUCH DAMAGE.
*/


#include "mj2.h"

#ifndef __MJ2_CONVERT_H
#define __MJ2_CONVERT_H

OPJ_API int OPJ_CALLCONV imagetoyuv(opj_image_t * img, char *outfile);

OPJ_API int OPJ_CALLCONV imagetobmp(opj_image_t * img, char *outfile);

OPJ_API opj_image_t * OPJ_CALLCONV mj2_image_create(mj2_tk_t * tk,
        opj_cparameters_t *parameters);

OPJ_API char OPJ_CALLCONV yuvtoimage(mj2_tk_t * tk, opj_image_t * img,
                                     int frame_num, opj_cparameters_t *parameters, char* infile);

OPJ_API unsigned int OPJ_CALLCONV yuv_num_frames(mj2_tk_t * tk, char *infile);


#endif
