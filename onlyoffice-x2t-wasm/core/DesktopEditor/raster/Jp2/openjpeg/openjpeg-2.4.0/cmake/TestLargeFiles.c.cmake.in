#cmakedefine _LARGEFILE_SOURCE
#cmakedefine _LARGE_FILES
#cmakedefine _FILE_OFFSET_BITS @_FILE_OFFSET_BITS@

#include <sys/types.h>
#include <stdio.h>
#include <stdlib.h>

int main(int argc, char **argv)
{
  /* Cause a compile-time error if off_t is smaller than 64 bits,
   * and make sure we have ftello / fseeko.
   */
#define LARGE_OFF_T (((off_t) 1 << 62) - 1 + ((off_t) 1 << 62))
  int off_t_is_large[ (LARGE_OFF_T % 2147483629 == 721 && LARGE_OFF_T % 2147483647 == 1) ? 1 : -1 ];
  FILE *fp = fopen(argv[0],"r");
  off_t offset = ftello( fp );

  fseeko( fp, offset, SEEK_CUR );
  fclose(fp);
  return 0;
}

