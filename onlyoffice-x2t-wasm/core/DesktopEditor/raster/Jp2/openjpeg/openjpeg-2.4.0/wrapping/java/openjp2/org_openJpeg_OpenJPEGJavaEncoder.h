/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class org_openJpeg_OpenJPEGJavaEncoder */

#ifndef _Included_org_openJpeg_OpenJPEGJavaEncoder
#define _Included_org_openJpeg_OpenJPEGJavaEncoder
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     org_openJpeg_OpenJPEGJavaEncoder
 * Method:    internalEncodeImageToJ2K
 * Signature: ([Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_org_openJpeg_OpenJPEGJavaEncoder_internalEncodeImageToJ2K
  (JNIEnv *, jobject, jobjectArray);

#ifdef __cplusplus
}
#endif
#endif
