/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class org_openJpeg_OpenJPEGJavaDecoder */

#ifndef _Included_org_openJpeg_OpenJPEGJavaDecoder
#define _Included_org_openJpeg_OpenJPEGJavaDecoder
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     org_openJpeg_OpenJPEGJavaDecoder
 * Method:    internalDecodeJ2KtoImage
 * Signature: ([Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_org_openJpeg_OpenJPEGJavaDecoder_internalDecodeJ2KtoImage
  (JNIEnv *, jobject, jobjectArray);

#ifdef __cplusplus
}
#endif
#endif
