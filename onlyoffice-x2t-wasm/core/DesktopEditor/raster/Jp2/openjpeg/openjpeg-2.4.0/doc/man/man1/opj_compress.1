'\" t
'\" The line above instructs most `man' programs to invoke tbl
'\"
'\" Separate paragraphs; not the same as PP which resets indent level.
.de SP
.if t .sp .5
.if n .sp
..
'\"
'\" Replacement em-dash for nroff (default is too short).
.ie n .ds m " -
.el .ds m \(em
'\"
'\" Placeholder macro for if longer nroff arrow is needed.
.ds RA \(->
'\"
'\" Decimal point set slightly raised
.if t .ds d \v'-.15m'.\v'+.15m'
.if n .ds d .
'\"
'\" Enclosure macro for examples
.de EX
.SP
.nf
.ft CW
..
.de EE
.ft R
.SP
.fi
..
.TH opj_compress 1 "Version 2.1.1" "opj_compress" "converts to jpeg2000 files"
.P
.SH NAME
opj_compress \- 
This program reads in an image of a certain type and converts it to a
jpeg2000 file. It is part of the OpenJPEG library.
.SP
Valid input image extensions are
.B .bmp, .pgm, .pgx, .png, .pnm, .ppm, .raw, .tga, .tif \fR. For PNG resp. TIF it needs libpng resp. libtiff .
.SP
Valid output image extensions are
.B .j2k, .jp2
.SH SYNOPSIS
.P
.B opj_compress \-i \fRinfile.bmp \fB-o \fRoutfile.j2k
.P
.B opj_compress \-ImgDir \fRdirectory_name \fB-OutFor \fRjp2
.P
.B opj_compress \-h \fRPrint a help message and exit.
.P
See JPWL OPTIONS for special options
.SH OPTIONS
.TP
.B \-\^b " n,n"
(Size of code block (e.g. \-b 32,32). Default: 64 x 64)
.TP
.B \-\^c " n"
(Size of precinct (e.g. \-c 128,128). Default: 2^15 x 2^15)
.TP
.B \-\^cinema2K " fps"
Digital Cinema 2K profile compliant codestream. Valid \fBfps\fR values are 24 or 48.
.TP
.B \-\^cinema4K
Digital Cinema 4K profile compliant codestream. Does not need an fps: default is 24 fps.
.TP
.B \-\^d " X,Y"
(Offset of image origin (e.g. \-d 150,300))
.TP
.B \-\^h
Print a help message and exit.
.TP
.B \-\^i " name"
(input file name)
.TP
.B \-\^n " n"
(Number of resolutions. Default: 6)
.TP
.B \-\^o " name"
(output file name)
.TP
.B \-\^p " name"
Progression order. \fBname\fR can be one out of:LRCP, RLCP, RPCL, PCRL, CPRL. Default: LRCP.
.TP
.B \-\^q " n"
different psnr for successive layers
.br
.B Note: \fR(options \-r and \-q cannot be used together)
.TP
.B \-\^r " n"
different compression ratio(s) for successive layers. The rate specified for each quality level is the desired compression factor.
.br
.B Note: \fR(options \-r and \-q cannot be used together)
.TP
.B \-\^s " X,Y"
sub-sampling factor (e.g. \-s 2,2). Default: No sub-sampling in x or y direction.
.br
.B Remark: \fRsub-sampling bigger than 2 can produce errors.
.TP
.B \-\^t " W,H"
(Size of tile (e.g. \-t 512,512) )
.TP
.B \-\^x " name"
(Create index file and fill it. Default: no index file)
.TP
.B \-\^EPH
(Write EPH marker after each header packet. Default:no EPH)
.TP
.B \-\^F " rawWidth,rawHeight,rawComp,rawBitDepth,s_or_u"
characteristics of the raw input image
.TP
.B \-\^I
(Use the irreversible DWT 9-7. Default: Reversible DWT 5-3)
.TP
.B \-\^ImgDir " directory_name"
(directory containing input files)
.TP
.B \-\^M " n"
mode switch with values: 1, 2, 4, 8, 16, 32. Default:No mode switch activated. 
.br
\fIMeaning:\fR
.br
BYPASS(1) 
.br
RESET(2) 
.br
RESTART(4) 
.br
VSC(8) 
.br
ERTERM(16) 
.br
SEGMARK(32)
.br
Values can be added: RESTART(4) + RESET(2) + SEGMARK(32) = \-M 38
.TP
.B \-\^OutFor "ext"
(extension for output files)
.TP
.B \-\^POC "TtileNr=resolutionStart, componentStart, layerEnd, resolutionEnd, componentEnd, progressionOrder"
(see Examples)
.TP
.B \-\^ROI "c=n,U=n"
quantization indices upshifted for component c (0 or 1 or 2) with a value of U (>= 0 and <= 37)
.br
e.g. \fB-ROI c=0,U=25\fR
.TP
.B \-\^SOP
(Write SOP marker before each packet. Default: No SOP marker in the codestream.)
.TP
.B \-\^T "X,Y"
(Offset of the origin of the tiles (e.g. \-T 100,75) )
.TP
.B \-\^W
(see JPWL OPTIONS)
.P
.SH JPWL OPTIONS
Options usable only if the library has been compiled with \fB-DUSE_JPWL\fR
.P
.B      \-W h<tilepart><=type>, s<tilepart><=method>, a=<addr>, z=<size>, g=<range>, p<tilepart:pack><=type>
.P
.B h\fR selects the header error protection (EPB): \fBtype\fR can be
   [0=none 1,absent=predefined 16=CRC-16 32=CRC-32 37-128=RS]
   if \fBtilepart\fR is absent, it is for main and tile headers
   if \fBtilepart\fR is present, it applies from that tile
     onwards, up to the next h<> spec, or to the last tilepart
     in the codestream (max. 16 specs)
.P
.B p \fRselects the packet error protection (EEP/UEP with EPBs)
  to be applied to raw data: \fBtype\fR can be
   [0=none 1,absent=predefined 16=CRC-16 32=CRC-32 37-128=RS]
   if \fBtilepart:pack\fR is absent, it is from tile 0, packet 0
   if \fBtilepart:pack\fR is present, it applies from that tile
     and that packet onwards, up to the next packet spec
     or to the last packet in the last tilepart in the stream
     (max. 16 specs)
.P
.B s \fRenables sensitivity data insertion (ESD): \fBmethod\fR can be
   [\-1=NO ESD 0=RELATIVE ERROR 1=MSE 2=MSE REDUCTION 3=PSNR
    4=PSNR INCREMENT 5=MAXERR 6=TSE 7=RESERVED]
   if \fBtilepart\fR is absent, it is for main header only
   if \fBtilepart\fR is present, it applies from that tile
     onwards, up to the next s<> spec, or to the last tilepart
     in the codestream (max. 16 specs)
.P
.B g \fRdetermines the addressing mode: \fBrange\fR can be
   [0=PACKET 1=BYTE RANGE 2=PACKET RANGE]
.P
.B a \fRdetermines the size of data addressing: \fBaddr\fR can be
   2/4 bytes (small/large codestreams). If not set, auto-mode
.P
.B z \fRdetermines the size of sensitivity values: \fBsize\fR can be
   1/2 bytes, for the transformed pseudo-floating point value
.P
.SH EXAMPLES
.P
.B opj_compress \-i \fRfile.bmp \fB-o \fRfile.j2k \fB-r \fR20,10,1 (compress 20x, then 10x, then lossless).
.P
.B opj_compress \-i \fRfile.ppm \fB-o \fRfile.j2k \fB-q \fR30,40,50
.P
.B opj_compress \-i \fRfile.pgx \fB-o \fRfile.j2k \fB-POC \fRT1=0,0,1,5,3,CPRL
.P
.B opj_compress \-i \fRlena.raw \fB-o \fRlena.j2k \fB-F \fR512,512,3,8,u
.P
.SH AUTHORS
Copyright (c) 2002-2014, Universite catholique de Louvain (UCL), Belgium
.br
Copyright (c) 2002-2014, Professor Benoit Macq
.br
Copyright (c) 2001-2003, David Janssens
.br
Copyright (c) 2002-2003, Yannick Verschueren
.br
Copyright (c) 2003-2007, Francois-Olivier Devaux and Antonin Descampe
.br
Copyright (c) 2005, Herve Drolon, FreeImage Team
.br
Copyright (c) 2006-2007, Parvatha Elangovan
.P
.SH "SEE ALSO"
opj_decompress(1) opj_dump(1)

