/*
 * $Id$
 *
 * Copyright (c) 2002-2014, Universite catholique de Louvain (UCL), Belgium
 * Copyright (c) 2002-2014, Professor <PERSON><PERSON>
 * Copyright (c) 2010-2011, <PERSON><PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS `AS IS'
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENT<PERSON>, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

/*! \page openjpippage OpenJPIP v@OPENJPEG_VERSION@ Documentation
 *
 * \section Introduction
 * This manual documents the low-level OpenJPIP C API.\n
 * OpenJPIP software is an implementation of JPEG 2000 Part9: Interactivity tools, APIs and protocols (JPIP).\n
 * ( For more info about JPIP, check the website: http://www.jpeg.org/jpeg2000/j2kpart9.html)\n
 *
 * This whole documents covers the following six programs.\n
 * - opj_server.c          JPIP server supporting HTTP connection and JPT/JPP-stream
 * - opj_dec_server.c      Server to decode JPT/JPP-stream and communicate locally with JPIP client, which is coded in java
 * - opj_jpip_addxml.c     To Embed metadata into JP2 file
 * - opj_jpip_transcode.c  To Convert JPT/JPP-stream to JP2 or J2K
 * - opj_jpip_test.c       To test index code format of a JP2 file
 *
 * \section License
 * This software is released under the BSD license, anybody can use or modify the library, even for commercial applications.\n
 * The only restriction is to retain the copyright in the sources or the binaries documentation.\n
 * Neither the author, nor the university accept any responsibility for any kind of error or data loss which may occur during usage.
 *
 *
 * \section reqlibs Required libraries
 *  - OpenJPEG library
 *  - FastCGI development kit (C libraries) at server (http://www.fastcgi.com)
 *  - libcURL library
 *
 *  We tested this software with a virtual server running on the same Linux machine as the clients.
 *
 *
 * \section compilenotes Compiling Notes
 * When you are making opj_server, set anything (e.g. yes) to the parameter jpipserver to define itself in the Makefile, which enables to make it in server mode.\n
 * Otherwise do not define (or do not set to) the parameter jpipserver.\n
 * Be sure that any object files and library file libopenjpip.a are not reused to compile in the two different mode (server mode and non server mode).\n
 * In other words, do make clean before making new targets which are in different modes as previous make.\n
 *
 * 
 * \section sysarchtect System Architecture
 * JPIP protocol is implemented between the JPIP server program (opj_server) and the JPIP client java program (opj_viewer).\n
 * Figure below represents the overview of our system architecture.\n
 * The JPIP server parses JPIP query and sends corresponding JPT/JPP-stream.
 * The JPIP client viewer is an image viewer with GUI to publish JPIP requests and receive JPT/JPP-stream.\n
 * Particularly, our system has the image decoding module implemented on a server (opj_dec_server, Image decoding Server). 
 * Image decoding Server and JPIP client viewer communicate closely. 
 * This specific architecture enables sharing cache of image codestream data among all viewers connected to the same Image decoding Server not only locally but also remotely.
 *
 *  \image html jpip_architect.png "OpenJPIP system architecture"
 *
 * JPIP server follows up the client cache during a session. \n
 * Concretely, the JPIP server models cache in each session, to which Channel IDs are associated. 
 * A Channel ID identifies a JPIP client viewer. 
 * And, new viewers can belong to a session by referring to one of its channel ID.
 * The Image decoding Server maintains the association between channel IDs and targets, and provides a reference channel ID to a Viewer on demand.\n
 * 
 * Typical requests and replies among JPIP server, JPIP client, and Image decoding server is presented below.\n
 * The JPIP server parses HTTP query and sends corresponding JPT/JPP-stream back to the JPIP client (Viewer).
 * JPT/JPP-stream is unreadable by JPIP client, and it is directly passed to Image decoding Server, and which provides the image in raw format (PGM or PPM) to the JPIP client.
 * The Image decoding Server handles the decoding and caching of JPT/JPP-stream.
 * JPIP client can read PGM and PPM images natively.
 * Before connecting to the JPIP server, every JPIP client checks local cache data of the requesting image with the image decoding server.
 * If its cache exists, the image decoding server provides ChannelID (CID), which identifies the image and its cache model on the JPIP server, and the whole system can continue the session using the CID.
 *
 *  \image html jpip_protocol.png "Message Sequence Chart of OpenJPIP implementation"
 *
 * \author Kaori Hagihara UCL/SST/ICTM/ELEN
 */
