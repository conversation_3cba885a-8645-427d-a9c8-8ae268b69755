<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8,00"
	Name="raster"
	ProjectGUID="{9CAA294E-58C3-4CEB-ABA0-CB9786CA5540}"
	RootNamespace="raster"
	Keyword="MFCProj"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="&quot;../freetype-2.5.2/include&quot;;&quot;../agg-2.4/include&quot;"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=""
				AssemblerListingLocation="$(ConfigurationName)\"
				ObjectFile="$(ConfigurationName)\"
				ProgramDataBaseFileName="$(ConfigurationName)\"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\raster.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Release/raster.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="&quot;../freetype-2.5.2/include&quot;;&quot;../agg-2.4/include&quot;"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Release/raster.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)\"
				ObjectFile="$(PlatformName)\$(ConfigurationName)\"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)\"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\raster.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Release/raster.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Unicode Debug|Win32"
			OutputDirectory=".\Unicode_Debug"
			IntermediateDirectory=".\Unicode_Debug"
			ConfigurationType="4"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=""
				PreprocessorDefinitions="_LIB;WIN32;_DEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;_CRT_NON_CONFORMING_SWPRINTFS"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				PrecompiledHeaderFile=".\Unicode_Debug/raster.pch"
				AssemblerListingLocation=".\Unicode_Debug/"
				ObjectFile=".\Unicode_Debug/"
				ProgramDataBaseFileName=".\Unicode_Debug/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile=".\Unicode_Debug\raster.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Unicode_Debug/raster.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Unicode Debug|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;../freetype-2.5.2/include&quot;;&quot;../agg-2.4/include&quot;"
				PreprocessorDefinitions="_LIB;WIN32;_DEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;_CRT_NON_CONFORMING_SWPRINTFS"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				PrecompiledHeaderFile=".\Unicode_Debug/raster.pch"
				AssemblerListingLocation=".\Unicode_Debug/"
				ObjectFile=".\Unicode_Debug/"
				ProgramDataBaseFileName=".\Unicode_Debug/"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile=".\Unicode_Debug\raster.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Unicode_Debug/raster.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Unicode Release|Win32"
			OutputDirectory=".\Unicode_Release"
			IntermediateDirectory=".\Unicode_Release"
			ConfigurationType="4"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories=""
				PreprocessorDefinitions="_LIB;WIN32;NDEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;_CRT_NON_CONFORMING_SWPRINTFS"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Unicode_Release/raster.pch"
				AssemblerListingLocation=".\Unicode_Release/"
				ObjectFile=".\Unicode_Release/"
				ProgramDataBaseFileName=".\Unicode_Release/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile=".\Unicode_Release\raster.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Unicode_Release/raster.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Unicode Release|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="&quot;../freetype-2.5.2/include&quot;;&quot;../agg-2.4/include&quot;"
				PreprocessorDefinitions="_LIB;WIN32;NDEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;_CRT_NON_CONFORMING_SWPRINTFS"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				PrecompiledHeaderFile=".\Unicode_Release/raster.pch"
				AssemblerListingLocation=".\Unicode_Release/"
				ObjectFile=".\Unicode_Release/"
				ProgramDataBaseFileName=".\Unicode_Release/"
				WarningLevel="3"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile=".\Unicode_Release\raster.lib"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Unicode_Release/raster.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			UseOfATL="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;../freetype-2.5.2/include&quot;;&quot;../agg-2.4/include&quot;"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE;$(NOINHERIT)"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				EnableEnhancedInstructionSet="0"
				PrecompiledHeaderFile=""
				AssemblerListingLocation="$(ConfigurationName)\"
				ObjectFile="$(IntDir)\"
				ProgramDataBaseFileName="$(IntDir)\vc80.pdb"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="4"
				DisableSpecificWarnings="4005;4311;4312"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\raster.lib"
				SuppressStartupBanner="true"
				IgnoreAllDefaultLibraries="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Debug/raster.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			UseOfATL="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;../freetype-2.5.2/include&quot;;&quot;../agg-2.4/include&quot;"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE;$(NOINHERIT)"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				PrecompiledHeaderFile=".\Debug/raster.pch"
				AssemblerListingLocation="$(PlatformName)\$(ConfigurationName)\"
				ObjectFile="$(PlatformName)\$(ConfigurationName)\"
				ProgramDataBaseFileName="$(PlatformName)\$(ConfigurationName)\"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="2057"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="$(OutDir)\raster.lib"
				SuppressStartupBanner="true"
				IgnoreAllDefaultLibraries="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
				SuppressStartupBanner="true"
				OutputFile=".\Debug/raster.bsc"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
			>
			<File
				RelativePath=".\BgraFrame.cpp"
				>
			</File>
			<File
				RelativePath=".\ImageFileFormatChecker.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl"
			>
			<File
				RelativePath=".\BgraFrame.h"
				>
			</File>
			<File
				RelativePath=".\ImageFileFormatChecker.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Common"
			>
			<File
				RelativePath="..\common\File.h"
				>
			</File>
		</Filter>
		<Filter
			Name="MetaFile"
			>
			<File
				RelativePath=".\Metafile\MetaFile.cpp"
				>
			</File>
			<File
				RelativePath=".\Metafile\MetaFile.h"
				>
			</File>
			<Filter
				Name="Wmf"
				>
				<File
					RelativePath=".\Metafile\Wmf\WmfClip.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Wmf\WmfClip.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Wmf\WmfFile.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Wmf\WmfObjects.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Wmf\WmfObjects.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Wmf\WmfPlayer.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Wmf\WmfPlayer.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Wmf\WmfTypes.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Emf"
				>
				<File
					RelativePath=".\Metafile\Emf\EmfClip.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfClip.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfFile.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfFile.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfObjects.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfObjects.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfPath.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfPath.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfPlayer.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfPlayer.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Emf\EmfTypes.h"
					>
				</File>
			</Filter>
			<Filter
				Name="Common"
				>
				<File
					RelativePath=".\Metafile\Common\IOutputDevice.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Common\MetaFile.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Common\MetaFileClip.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Common\MetaFileObjects.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Common\MetaFileRenderer.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Common\MetaFileTypes.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Common\MetaFileTypes.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\Common\MetaFileUtils.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\Common\MetaFileUtils.h"
					>
				</File>
			</Filter>
			<Filter
				Name="StarView"
				>
				<File
					RelativePath=".\Metafile\StarView\SvmEnums.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\StarView\SvmFile.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\StarView\SvmFile.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\StarView\SvmObjects.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\StarView\SvmObjects.h"
					>
				</File>
				<File
					RelativePath=".\Metafile\StarView\SvmPlayer.cpp"
					>
				</File>
				<File
					RelativePath=".\Metafile\StarView\SvmPlayer.h"
					>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Jp2"
			>
			<File
				RelativePath=".\Jp2\ArithmeticCoder.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\DWT.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Image.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\J2k.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\J2kFile.cpp"
				>
			</File>
			<File
				RelativePath=".\Jp2\J2kFile.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\J2kIncludes.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Jp2.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\JpgEvent.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Jpt.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Mj2.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\PacketIterator.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Raw.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Reader.cpp"
				>
			</File>
			<File
				RelativePath=".\Jp2\Reader.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Stream.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\TagTree.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Tier1.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Tier2.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Tile.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Types.h"
				>
			</File>
			<File
				RelativePath=".\Jp2\Utils.h"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
