<?xml version="1.0" encoding="windows-1251"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8,00"
	Name="jbig2"
	ProjectGUID="{EE1B576A-07C5-4ACC-920F-81C41DD0C8C1}"
	RootNamespace="jbig2"
	Keyword="Win32Proj"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				EnableEnhancedInstructionSet="0"
				UsePrecompiledHeader="0"
				ObjectFile="$(IntDir)\"
				ProgramDataBaseFileName="$(IntDir)\vc80.pdb"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="4"
				DisableSpecificWarnings="4005;4311;4312"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(SolutionDir)$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(SolutionDir)$(PlatformName)\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ConfigurationName)"
			ConfigurationType="4"
			CharacterSet="1"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
				TargetEnvironment="3"
			/>
			<Tool
				Name="VCCLCompilerTool"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="LeptonLib"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath="..\source\LeptonLib\adaptmap.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\additionaltypes.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\affine.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\affinecompose.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\allheaders.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\alltypes.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\arithlow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\array.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\arrayaccess.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\arrayaccess.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bardecode.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\baseline.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bbuffer.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bbuffer.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bilinear.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\binarize.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\binexpand.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\binexpandlow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\binreduce.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\binreducelow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\blend1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bmf.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bmf.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bmp.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bmpio.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\bmpiostub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\boxbasic.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\boxfunc1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\boxfunc2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\boxfunc3.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\ccbord.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\ccbord.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\ccthin.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\classapp.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\colorcontent.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\colormap.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\colormorph.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\colorquant1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\colorquant2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\colorseg.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\compare.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\conncomp.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\convertfiles.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\convolve.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\convolvelow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\correlscore.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\dewarp.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\dewarp.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\dwacomb.2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\dwacomblow.2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\edge.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\endianness.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\enhance.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\environ.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fhmtauto.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fhmtgen.1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fhmtgenlow.1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\finditalic.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\flipdetect.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fliphmtgen.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fmorphauto.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fmorphgen.1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fmorphgenlow.1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fpix1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\fpix2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\freetype.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\freetype.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\funcprotos.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\gifio.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\gifiostub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\gplot.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\gplot.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\graphics1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\graymorph.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\graymorphlow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\grayquant.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\grayquantlow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\heap.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\heap.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\imageio.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\jbclass.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\jbclass.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\jpegio.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\jpegiostub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\kernel.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\lepton_utils.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\list.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\list.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\maze.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\morph.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\morph.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\morphapp.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\morphdwa.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\morphseq.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\numabasic.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\numafunc1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\numafunc2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pageseg.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\paintcmap.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\parseprotos.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\partition.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pix.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pix1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pix2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pix3.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pix4.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pix5.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixabasic.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixacc.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixafunc1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixafunc2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixalloc.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixarith.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixcomp.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixconv.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pixtiling.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pngio.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pngiostub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pnmio.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\pnmiostub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\projective.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\psio1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\psio1stub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\psio2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\psio2stub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\ptabasic.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\ptafunc1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\ptra.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\ptra.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\queue.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\queue.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\rank.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\readbarcode.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\readbarcode.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\readfile.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\regutils.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\regutils.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\rop.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\ropiplow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\roplow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\rotate.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\rotateam.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\rotateamlow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\rotateorth.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\rotateorthlow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\rotateshear.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\runlength.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\sarray.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\scale.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\scalelow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\seedfill.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\seedfilllow.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\sel1.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\sel2.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\selgen.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\shear.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\skew.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\spixio.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\stack.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\stack.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\sudoku.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\sudoku.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\textops.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\tiffio.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\tiffiostub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\warper.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\watershed.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\watershed.h"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\webpio.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\webpiostub.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\writefile.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\zlibmem.cpp"
				>
			</File>
			<File
				RelativePath="..\source\LeptonLib\zlibmemstub.cpp"
				>
			</File>
		</Filter>
		<Filter
			Name="Encoder"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath="..\source\Encoder\jbig2arith.cpp"
				>
			</File>
			<File
				RelativePath="..\source\Encoder\jbig2arith.h"
				>
			</File>
			<File
				RelativePath="..\source\Encoder\jbig2enc.cpp"
				>
			</File>
			<File
				RelativePath="..\source\Encoder\jbig2enc.h"
				>
			</File>
			<File
				RelativePath="..\source\Encoder\jbig2encoder.h"
				>
			</File>
			<File
				RelativePath="..\source\Encoder\jbig2segments.h"
				>
			</File>
			<File
				RelativePath="..\source\Encoder\jbig2structs.h"
				>
			</File>
			<File
				RelativePath="..\source\Encoder\jbig2sym.cpp"
				>
			</File>
			<File
				RelativePath="..\source\Encoder\jbig2sym.h"
				>
			</File>
		</Filter>
		<File
			RelativePath="..\source\JBig2File.cpp"
			>
		</File>
		<File
			RelativePath="..\source\JBig2File.h"
			>
		</File>
		<File
			RelativePath="..\source\Utils.h"
			>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
