﻿/*====================================================================*
 -  Copyright (C) 2001 Leptonica.  All rights reserved.
 -  This software is distributed in the hope that it will be
 -  useful, but with NO WARRANTY OF ANY KIND.
 -  No author or distributor accepts responsibility to anyone for the
 -  consequences of using this software, or for whether it serves any
 -  particular purpose or works at all, unless he or she says so in
 -  writing.  Everyone is granted permission to copy, modify and
 -  redistribute this source code, for commercial or non-commercial
 -  purposes, with the following restrictions: (1) the origin of this
 -  source code must not be misrepresented; (2) modified versions must
 -  be plainly marked as such; and (3) this notice may not be removed
 -  or altered from any source or modified source distribution.
 *====================================================================*/

#ifndef  LEPTONICA_ALLHEADERS_H
#define  LEPTONICA_ALLHEADERS_H


#define LIBLEPT_MAJOR_VERSION   1
#define LIBLEPT_MINOR_VERSION   67

#pragma warning(disable: 4305 4244 4996 4101 4018 4309)

#include "alltypes.h"

#ifndef NO_PROTOS
#include  "funcprotos.h"
#endif  /* NO_PROTOS */


#endif /* LEPTONICA_ALLHEADERS_H */


