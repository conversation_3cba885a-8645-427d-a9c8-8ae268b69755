﻿/*====================================================================*
 -  Copyright (C) 2001 Leptonica.  All rights reserved.
 -  This software is distributed in the hope that it will be
 -  useful, but with NO WARRANTY OF ANY KIND.
 -  No author or distributor accepts responsibility to anyone for the
 -  consequences of using this software, or for whether it serves any
 -  particular purpose or works at all, unless he or she says so in
 -  writing.  Everyone is granted permission to copy, modify and
 -  redistribute this source code, for commercial or non-commercial
 -  purposes, with the following restrictions: (1) the origin of this
 -  source code must not be misrepresented; (2) modified versions must
 -  be plainly marked as such; and (3) this notice may not be removed
 -  or altered from any source or modified source distribution.
 *====================================================================*/

#ifndef  LEPTONICA_ALLTYPES_H
#define  LEPTONICA_ALLTYPES_H

    /* Standard */
#include <stdio.h>
#include <stdlib.h>

    /* General and configuration defs */
#include "environ.h"

    /* Imaging */
#include "array.h"
#include "arrayaccess.h"
#include "bbuffer.h"
#include "bmf.h"
#include "ccbord.h"
#include "dewarp.h"
#include "gplot.h"
#include "heap.h"
#include "imageio.h"
#include "jbclass.h"
#include "list.h"
#include "morph.h"
#include "pix.h"
#include "ptra.h"
#include "queue.h"
#include "regutils.h"
#include "sudoku.h"
#include "stack.h"
#include "watershed.h"


#endif /* LEPTONICA_ALLTYPES_H */

