<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1139</width>
    <height>656</height>
   </rect>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::DefaultContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="layoutDirection">
   <enum>Qt::LeftToRight</enum>
  </property>
  <property name="inputMethodHints">
   <set>Qt::ImhHiddenText|Qt::ImhNoAutoUppercase|Qt::ImhPreferLowercase|Qt::ImhPreferNumbers|Qt::ImhPreferUppercase|Qt::ImhSensitiveData</set>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QWidget" name="horizontalLayoutWidget">
    <property name="geometry">
     <rect>
      <x>9</x>
      <y>9</y>
      <width>1121</width>
      <height>621</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="CCustomView" name="customView"/>
       </item>
       <item>
        <widget class="CCustomView" name="graphicsView"/>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="CMetafileTreeWidget" name="treeView">
         <property name="inputMethodHints">
          <set>Qt::ImhNone</set>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Plain</enum>
         </property>
         <property name="selectionBehavior">
          <enum>QAbstractItemView::SelectItems</enum>
         </property>
         <property name="autoExpandDelay">
          <number>-1</number>
         </property>
         <property name="rootIsDecorated">
          <bool>true</bool>
         </property>
         <property name="animated">
          <bool>false</bool>
         </property>
         <attribute name="headerCascadingSectionResizes">
          <bool>false</bool>
         </attribute>
         <attribute name="headerHighlightSections">
          <bool>false</bool>
         </attribute>
         <attribute name="headerShowSortIndicator" stdset="0">
          <bool>false</bool>
         </attribute>
         <attribute name="headerStretchLastSection">
          <bool>true</bool>
         </attribute>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>2</number>
         </property>
         <item>
          <widget class="QPushButton" name="expandButton">
           <property name="text">
            <string>Expand All</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="ModButton">
           <property name="enabled">
            <bool>true</bool>
           </property>
           <property name="text">
            <string>Use a light Mod</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1139</width>
     <height>21</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionChange_File"/>
    <addaction name="actionStatistics"/>
    <addaction name="separator"/>
    <addaction name="actionSave_XML_as"/>
    <addaction name="actionSave_EMF_as"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
    <addaction name="separator"/>
   </widget>
   <addaction name="menuFile"/>
  </widget>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
  </action>
  <action name="actionChange_File">
   <property name="text">
    <string>Change File</string>
   </property>
  </action>
  <action name="actionStatistics">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Statistics</string>
   </property>
  </action>
  <action name="actionSave_XML_as">
   <property name="checkable">
    <bool>false</bool>
   </property>
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Save XML as...</string>
   </property>
  </action>
  <action name="actionSave_EMF_as">
   <property name="enabled">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Save EMF as...</string>
   </property>
  </action>
 </widget>
 <customwidgets>
  <customwidget>
   <class>CCustomView</class>
   <extends>QGraphicsView</extends>
   <header location="global">CCustomView.h</header>
  </customwidget>
  <customwidget>
   <class>CMetafileTreeWidget</class>
   <extends>QTreeView</extends>
   <header location="global">CMetafileTreeWidget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
