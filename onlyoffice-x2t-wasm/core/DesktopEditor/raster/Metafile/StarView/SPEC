                          StarView Metafile

                            Specification

                             Inge Wallin
                           <PERSON>


1. Introduction
===============

This file describes the format of a StarView Metafile, SVM, the vector
file format that is used by Star Office, OpenOffice.org, and
LibreOffice to create object replacement images.

As far as we know, there is no formal specification of this format.
This documentation has been created by reading the source code of
LibreOffice, in the vcl subdirectory.  The most important files are:

  vcl/inc/vcl/gdimtf.hxx
  vcl/inc/vcl/metaact.hxx
  vcl/source/gdimtf.cxx
  vcl/source/metaact.cxx

The format of this document is modeled after the documentations of
Windows MetaFile ([MS-WMF].pdf) and the Extended MetaFile
([MS-EMF].pdf) from Microsoft.


1.1 Glossary
------------
FIXME


1.2 Structure Overview
----------------------

An SVM file always starts with a signature of 6 bytes that identifies
the file as an SVM.  This signature is always the string "VCLMTF"
(excluding the quotes).

Second, there is a header object describing the rest of the file.

Third, the header is followed by a number of actions as specified in
the header.  Each action or object, including the header, starts with
a VersionCompat object that specifies the version and size of the
action or object in question.  The size in the VersionCompat object is
*excluding* the VersionCompat itself.


2. Structures
=============

This section specifies the structures used in a StarView
Metafile. This includes:

 * enumerations of SVM constants: enums and flags

 * definitions of SVM objects, i.e. primitive data types and
   composite structs and objects

 * specifications of SVM actions.


2.1 SVM Constants
-----------------

Constants come in two different forms: enumerations and flags.


2.1.1 SVM Enumerations
- - - - - - - - - - - -

The following enumerations should be used in programs that handle SVM
files. 


******* ActionType Enumeration

The ActionType Enumeration defines the type of actions that can be
used in an SVM file.

enum ActionType {
    META_NULL_ACTION                  = 0,
    META_PIXEL_ACTION                 = 100,
    META_POINT_ACTION                 = 101,
    META_LINE_ACTION                  = 102,
    META_RECT_ACTION                  = 103,
    META_ROUNDRECT_ACTION             = 104,
    META_ELLIPSE_ACTION               = 105
    META_ARC_ACTION                   = 106,
    META_PIE_ACTION                   = 107,
    META_CHORD_ACTION                 = 108,
    META_POLYLINE_ACTION              = 109,
    META_POLYGON_ACTION               = 110,
    META_POLYPOLYGON_ACTION           = 111,
    META_TEXT_ACTION                  = 112,
    META_TEXTARRAY_ACTION             = 113,
    META_STRETCHTEXT_ACTION           = 114,
    META_TEXTRECT_ACTION              = 115,
    META_BMP_ACTION                   = 116,
    META_BMPSCALE_ACTION              = 117,
    META_BMPSCALEPART_ACTION          = 118,
    META_BMPEX_ACTION                 = 119,
    META_BMPEXSCALE_ACTION            = 120,
    META_BMPEXSCALEPART_ACTION        = 121,
    META_MASK_ACTION                  = 122,
    META_MASKSCALE_ACTION             = 123,
    META_MASKSCALEPART_ACTION         = 124,
    META_GRADIENT_ACTION              = 125,
    META_HATCH_ACTION                 = 126,
    META_WALLPAPER_ACTION             = 127,
    META_CLIPREGION_ACTION            = 128,
    META_ISECTRECTCLIPREGION_ACTION   = 129,
    META_ISECTREGIONCLIPREGION_ACTION = 130,
    META_MOVECLIPREGION_ACTION        = 131,
    META_LINECOLOR_ACTION             = 132,
    META_FILLCOLOR_ACTION             = 133,
    META_TEXTCOLOR_ACTION             = 134,
    META_TEXTFILLCOLOR_ACTION         = 135,
    META_TEXTALIGN_ACTION             = 136,
    META_MAPMODE_ACTION               = 137,
    META_FONT_ACTION                  = 138,
    META_PUSH_ACTION                  = 139,
    META_POP_ACTION                   = 140,
    META_RASTEROP_ACTION              = 141,
    META_TRANSPARENT_ACTION           = 142,
    META_EPS_ACTION                   = 143,
    META_REFPOINT_ACTION              = 144,
    META_TEXTLINECOLOR_ACTION         = 145,
    META_TEXTLINE_ACTION              = 146,
    META_FLOATTRANSPARENT_ACTION      = 147,
    META_GRADIENTEX_ACTION            = 148,
    META_LAYOUTMODE_ACTION            = 149,
    META_TEXTLANGUAGE_ACTION          = 150,
    META_OVERLINECOLOR_ACTION         = 151,
    META_COMMENT_ACTION               = 512
};


******* Mtf Enumeration

The Mtf Enumeration defines ...    FIXME

enum MtfType {
    MTF_CONVERSION_NONE           = 0,
    MTF_CONVERSION_1BIT_THRESHOLD = 1,
    MTF_CONVERSION_8BIT_GREYS     = 2
};


2.1.1.3 LineStyle Enumeration

enum LineStyle
{
    LINE_NONE  = 0,
    LINE_SOLID = 1,
    LINE_DASH  = 2,
};


2.1.1.4 ... Enumeration
FIXME


2.1.2 SVM Flags
- - - - - - - -

A variable can only containing one value from an SVM enumeration at
the same time.  In contrast, a variable containing SVN flags can
combine as many values from the respective definition of flags as the
enumeration of the flag itself.


2.1.2.1 Mirror Flags

MTF_MIRROR_NONE    0x00000000
MTF_MIRROR_HORZ    0x00000001
MTF_MIRROR_VERT    0x00000002

FIXME: Check the details here.
MTF_MIRROR_NONE    The picture is not mirrored in any direction. 

MTF_MIRROR_HORZ    The picture should be drawn mirrored horizontally,
                   i.e. mirrored in the center line of the picture
                   parallel to the Y axis.

MTF_MIRROR_VERT    The picture should be drawn mirrored vertically,
                   i.e. mirrored in the center line of the picture
                   parallel to the X axis.

In all of the cases above, the space occupied by the picture is the
same. 


2.2 SVM Objects
---------------

This section specifies the objects used in SVM files. 


2.2.1 Primitive Data Types
- - - - - - - - - - - - - -

The following primitive data types are used in the objects or actions.

uint8	an unsigned 8 bit integer, taking up 1 byte in the file.
int8	a signed 8 bit integer, taking up 1 byte in the file.
uint16	an unsigned 16 bit integer, taking up 2 bytes in the file.
int16	a signed 16 bit integer, taking up 2 bytes in the file.
uint32	an unsigned 32 bit integer, taking up 4 bytes in the file.
int32	a signed 32 bit integer, taking up 4 bytes in the file.
bool    an unsigned 8 bit value, taking up 1 byte in the file

Floating point values are not used in SVM.


2.2.1 Basic Objects
- - - - - - - - - -

These object types are mostly used as primitives in the larger and
more complex object types and actions.


2.2.1.1 VersionCompat Object

The VersionCompat object is always the first object of any bigger
object and any action. This object describes which version the object
or action has.

struct VersionCompat {
    uint16 version;
    uint32 length;
}

version		defines the version of the object. Depending on the
                version, the object will have a different number of
                components, thus changing the parser behaviour.

length          defines the length of the object, excluding the
                VersionCompat object.


2.2.1.2 Point

struct Point {
    uint32 x;
    uint32 y;
}


2.2.1.3 Polygon

struct Polygon {
    uint16  nPoints;
    Point   points[nPoints];
}


2.2.1.4 PolyPolygon

struct PolyPolygon {
    uint16  nPolygons;
    Polygon polygons[nPolygons];
}


2.2.1.5 String

struct String {
    uint16 size;
    char   data[size];
}

A string object is built in a Pascal style, not a C style.  There is
no trailing \0 at the end of the string.


2.2.1.6 Fraction

struct Fraction {
    uint32  numerator;
    unit32  denominator;
}

The value of a Fraction is numerator/denominator.


2.2.2 Header Object
- - - - - - - - - -

The header object is a special object in an SVM. It is always the
first object in the file. There is only one header object.

VersionCompat 	versionCompat
uint32          compressionMode 
MapMode         mapMode
uint32          width
uint32          height
uint32          actionCount 

versionCompat   Version and size of the header record, excluding the
                VersionCompat object. 

compressionMode ... FIXME

mapMode         Default MapMode for the file.  FIXME: Check if true

width           Width of the picture in logical coordinates.

height          Height of the picture in logical coordinates.

actionCount     Number of Actions in the file.


2.2.3 Graphics Objects
- - - - - - - - - - - -

The SVM Graphics Objects specify parameters for graphics output. A
particular graphics object becomes part of the drawing context when it
is selected by an appropriate object record, and it is reused in
subsequent graphics operations until a different object is selected.


2.2.3.1 MapMode

This object describes some properties of the current drawing.

VersionCompat   version
uint16          unit
Point           origin
Fraction        scaleX
Fraction        scaleY
bool            isSimple

....FIXME: how do they alter the rendering context ?
.... Can the scale, the origin change ?
.... Find the values for the unit...


2.2.3.2 Font

This object describes the properties of a font face to be used when rendering text.

VersionCompat	version
String          family
String          style
uint32          fontWidth
uint32          fontHeight
uint16          charset
uint16          family
uint16          pitch
uint16          weight
uint16          underline
uint16          strikeout
uint16          italic
uint16          language
uint16          width
uint16          orientation
bool            wordline
bool            outline
bool            shadow
int8            kerning
    If version is greater than 1:
int8            relief
uint16          language
bool            vertical
uint16          emphasis
    If version is greater than 2:
uint16          overline

version         Version and size of the font record, excluding the
                VersionCompat object. 

FIXME: Describe the following parts in detail
family
style
fontWidth
fontHeight
charset
family
pitch
weight
underline
strikeout
italic
language
width
orientation
wordline
outline
shadow
kerning
    If version is greater than 1:
relief
language
vertical
emphasis
    If version is greater than 2:
overline



2.2.4 Structure Objects
- - - - - - - - - - - -

The SVM Structure Objects specify data structures that are embedded in
SVM objects and actions.  Structure objects, unlike graphics objects,
are not explicitly created or deleted; they are components of more
complex structures.


2.2.4.1 Color

FIXME


2.2.4.2 PenInformations

This object describes the pen properties for drawing lines.  Its
contents depends on its version.

    For all values of version:
VersionCompat   version
uint16          lineStyle
uint32          lineWidth
    If version is greater than 1:
uint16          dashCount
uint32          dashLength
uint16          dotCount
uint32          dotLength
uint32          distance
    If version is greater than 2:
uint16          lineJoin

lineStyle       describes the line style of the pen. It can take any
                value of the LineStyle enumeration.

lineWidth       width of the pen in <unit> (FIXME)

....FIXME: how to interpret the next fields values ?


2.3 SVM Actions
---------------

The action type is specified using a uint16 at the beginning of each
action following the file header.  Every action except the special
META_NULL_ACTION begins with a VersionCompat object.


2.3.1 Control Action Types
- - - - - - - - - - - - - - 

2.3.1.1 META_NULL_ACTION (0)

This action should never occur, ever.
It contains a uint16 that is never used.


2.3.1.2 META_COMMENT_ACTION (512)

A comment action has no effect on the rendering.

string          comment
uint32          value
string          comment2


comment         The comment text

value           ... FIXME

comment2        ... FIXME


2.3.2 State Action Types
- - - - - - - - - - - - -

These actions change the state of the drawing context.  


2.3.2.1 META_LINECOLOR_ACTION (132)

A line color sets the pen color in the drawing context.

uint32          colorData
bool            doSet

colorData       The color of the pen, in RGB format
                (... QColor::fromRgb format)

doSet           Should the color be applied (true), or should the pen
                be hidden (false) 


2.3.2.2 META_FILLCOLOR_ACTION (133)

A fill color sets the brush color in the current context.
It contains the following parts
uint32          colorData
bool            isSet

colorData       The color of the brush, in RGB format
                (... QColor::fromRgb format) 

isSet           Specifies wether the color should be be applied
                (true), or if the brush should be hidden (false) 


2.3.2.3 META_MAPMODE_ACTION (137)

This action loads a new MapMode object into the drawing context.

MapMode		mapMode


2.3.2.4 META_FONT_ACTION (138)

This action sets the current font in the graphics context.

Font 		font

font            The new font that will be used when writing text.


2.3.2.5 META_TEXTLANGUAGE_ACTION (150)

This action sets the current text language in the graphics context.

uint16          languageType

languageType    It is a decimal code that specifies the language. 
                It is described in i18npool/inc/i18npool/lang.h
                FIXME: Add these language codes to the enums in 2.1.1


2.3.3 Drawing Action Types
- - - - - - - - - - - - - - 

These actions all cause some form of output to the picture.  The
operations described by these actions are influenced by the current
drawing context.


2.3.3.1 META_POLYLINE_ACTION (109)

A polyline action draws a polygon without filling it.

It contains the following parts
Polygon         polygon
    If the version of the action is greater than 1:
PenInformations penInfos
    If the version of the action is greater than 2:
bool            polygonFlags
    If polygonFlags is true:
VersionCompat   flagsVersionCompat
Polygon         polygon (replaces the previous polygon)
bool            polygonFlagsEnabled
    If polygonFlagsEnabled
....... FIXME!

polygon         The polygon to draw

penInfos        The pen style to apply

polygonFlags    Indicate the presence of extra polygon informations
....... FIXME: next part is complicated...


2.3.3.2 META_POLYGON_ACTION (110)

A polygon action draws a polygon and filling it.

// FIXME: Check if the following is true.  Right now it is just copied
// from META_POLYLINE_ACTION
Polygon         polygon
    If the version of the action is greater than 1:
PenInformations penInfos
    If the version of the action is greater than 2:
bool            polygonFlags
    If polygonFlags is true:
VersionCompat   flagsVersionCompat
Polygon         polygon (replaces the previous polygon)
bool            polygonFlagsEnabled
    If polygonFlagsEnabled
....... FIXME!

polygon         The polygon to draw

penInfos        The pen style to apply

polygonFlags    Indicate the presence of extra polygon informations
....... FIXME: next part is complicated...


2.3.3.3 META_POLYPOLYGON_ACTION (111)

A polypolygon action draws a group of polygons.

It contains the following parts
PolyPolygon     polygons
    If the version of the action is greater than 1:
uint16          numberOfComplexPolygons
For each complexPolygon :
uint16          complexPolygonIndex
Polygon         complexPolygon

The complexPolygonIndex is used to replace a polygon from the polygons list.


2.3.4 Unsorted Action Types
- - - - - - - - - - - - - -

These action types have so far not being sorted into any other type. 

 META_PIXEL_ACTION
 META_POINT_ACTION
 META_LINE_ACTION
 META_RECT_ACTION
 META_ROUNDRECT_ACTION
 META_ELLIPSE_ACTION
 META_ARC_ACTION
 META_PIE_ACTION
 META_CHORD_ACTION
 META_POLYGON_ACTION
 META_TEXT_ACTION
 META_TEXTARRAY_ACTION
 META_STRETCHTEXT_ACTION
 META_TEXTRECT_ACTION
 META_BMP_ACTION
 META_BMPSCALE_ACTION
 META_BMPSCALEPART_ACTION
 META_BMPEX_ACTION
 META_BMPEXSCALE_ACTION
 META_BMPEXSCALEPART_ACTION
 META_MASK_ACTION
 META_MASKSCALE_ACTION
 META_MASKSCALEPART_ACTION
 META_GRADIENT_ACTION
 META_HATCH_ACTION
 META_WALLPAPER_ACTION
 META_CLIPREGION_ACTION
 META_ISECTRECTCLIPREGION_ACTION
 META_ISECTREGIONCLIPREGION_ACTION
 META_MOVECLIPREGION_ACTION
 META_TEXTCOLOR_ACTION
 META_TEXTFILLCOLOR_ACTION
 META_TEXTALIGN_ACTION
 META_FONT_ACTION
 META_PUSH_ACTION
 META_POP_ACTION
 META_RASTEROP_ACTION
 META_TRANSPARENT_ACTION
 META_EPS_ACTION
 META_REFPOINT_ACTION
 META_TEXTLINECOLOR_ACTION
 META_TEXTLINE_ACTION
 META_FLOATTRANSPARENT_ACTION
 META_GRADIENTEX_ACTION
 META_LAYOUTMODE_ACTION
 META_OVERLINECOLOR_ACTION

