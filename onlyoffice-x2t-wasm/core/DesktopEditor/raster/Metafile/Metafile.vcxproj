﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E0341CD3-CFC0-4544-9DBA-31781C259DBF}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>Metafile</RootNamespace>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
    <ProjectName>MetafileTest</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>D:\_Work\core\DesktopEditor\freetype-2.5.2;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\agg-2.4\include;..\..\freetype-2.5.2\include;..\..\cximage\jasper\include;..\..\cximage\jpeg;..\..\cximage\png;..\..\cximage\zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4005;4018</DisableSpecificWarnings>
      <UndefinePreprocessorDefinitions>HAVE_UNISTD_H;HAVE_SYS_TIME_H</UndefinePreprocessorDefinitions>
      <PreprocessorDefinitions>GRAPHICS_NO_USE_DYNAMIC_LIBRARY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <PrecompiledHeaderFile />
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;_LIB;%(PreprocessorDefinitions);_CRT_SECURE_NO_WARNINGS</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\agg-2.4\include;..\..\freetype-2.5.2\include;..\..\cximage\jasper\include;..\..\cximage\jpeg;..\..\cximage\png;..\..\cximage\zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4005;4018</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_LIB;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\agg-2.4\include;..\..\freetype-2.5.2\include;..\..\cximage\jasper\include;..\..\cximage\jpeg;..\..\cximage\png;..\..\cximage\zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4005;4018</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_LIB;%(PreprocessorDefinitions);_CRT_SECURE_NO_WARNINGS</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\agg-2.4\include;..\..\freetype-2.5.2\include;..\..\cximage\jasper\include;..\..\cximage\jpeg;..\..\cximage\png;..\..\cximage\zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4005;4018;4267</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="TestMain.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\agg-2.4\agg_vs2005.vcxproj">
      <Project>{617f9069-5e37-4b80-9a3a-e77afc4cc7ad}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\CxImage\cximage_vs2005.vcxproj">
      <Project>{bc52a07c-a797-423d-8c4f-8678805bbb36}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\jasper\jasper_vs2005.vcxproj">
      <Project>{ffda5da1-bb65-4695-b678-be59b4a1355d}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\jbig\jbig_vs2005.vcxproj">
      <Project>{764c3a2d-fb0f-428e-b1c7-62d1dd2ce239}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\jpeg\Jpeg_vs2005.vcxproj">
      <Project>{818753f2-dbb9-4d3b-898a-a604309be470}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\libpsd\libpsd_vs2005.vcxproj">
      <Project>{9a037a69-d1df-4505-ab2a-6cb3641c476e}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\mng\mng_vs2005.vcxproj">
      <Project>{40a69f40-063e-43fd-8543-455495d8733e}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\png\png_vs2005.vcxproj">
      <Project>{43a0e60e-5c4a-4c09-a29b-7683f503bbd7}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\raw\libdcr_vs2005.vcxproj">
      <Project>{df861d33-9bc1-418c-82b1-581f590fe169}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\tiff\Tiff_vs2005.vcxproj">
      <Project>{0588563c-f05c-428c-b21a-dd74756628b3}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\cximage\zlib\zlib_vs2005.vcxproj">
      <Project>{7b53d2c7-1b4a-4a53-a7d3-e25b92470b81}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\fontengine\font_engine_vs2005.vcxproj">
      <Project>{c739151f-5384-41df-a1a6-f089e2c1ad56}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\freetype-2.5.2\builds\windows\vc2005\freetype.vcxproj">
      <Project>{78b079bd-9fc7-4b9e-b4a6-96da0f00248b}</Project>
    </ProjectReference>
    <ProjectReference Include="..\JBig2\win32\jbig2.vcxproj">
      <Project>{ee1b576a-07c5-4acc-920f-81c41dd0c8c1}</Project>
    </ProjectReference>
    <ProjectReference Include="..\raster_vs2005.vcxproj">
      <Project>{9caa294e-58c3-4ceb-aba0-cb9786ca5540}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>