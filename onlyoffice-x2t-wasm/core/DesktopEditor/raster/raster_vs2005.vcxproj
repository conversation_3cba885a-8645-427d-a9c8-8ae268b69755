﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Unicode Debug|Win32">
      <Configuration>Unicode Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Unicode Debug|x64">
      <Configuration>Unicode Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Unicode Release|Win32">
      <Configuration>Unicode Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Unicode Release|x64">
      <Configuration>Unicode Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectName>raster</ProjectName>
    <ProjectGuid>{9CAA294E-58C3-4CEB-ABA0-CB9786CA5540}</ProjectGuid>
    <RootNamespace>raster</RootNamespace>
    <Keyword>MFCProj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <UseOfAtl>false</UseOfAtl>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
    <UseOfAtl>false</UseOfAtl>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
    <UseOfMfc>false</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>14.0.25431.1</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Debug|Win32'">
    <OutDir>.\Unicode_Debug\</OutDir>
    <IntDir>.\Unicode_Debug\</IntDir>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Debug|x64'">
    <OutDir>$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Release|Win32'">
    <OutDir>.\Unicode_Release\</OutDir>
    <IntDir>.\Unicode_Release\</IntDir>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Release|x64'">
    <OutDir>$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
    <IncludePath>D:\_Work\core\DesktopEditor\freetype-2.5.2;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\</IntDir>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRules />
    <CodeAnalysisRuleAssemblies />
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>../freetype-2.5.2/include;../agg-2.4/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile />
      <AssemblerListingLocation>$(Configuration)\</AssemblerListingLocation>
      <ObjectFileName>$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(Configuration)\</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0809</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)raster.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/raster.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>../freetype-2.5.2/include;../agg-2.4/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\Release/raster.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>$(Platform)\$(Configuration)\</AssemblerListingLocation>
      <ObjectFileName>$(Platform)\$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(Platform)\$(Configuration)\</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0809</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)raster.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Release/raster.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_LIB;WIN32;_DEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;_CRT_NON_CONFORMING_SWPRINTFS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeaderOutputFile>.\Unicode_Debug/raster.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Unicode_Debug/</AssemblerListingLocation>
      <ObjectFileName>.\Unicode_Debug/</ObjectFileName>
      <ProgramDataBaseFileName>.\Unicode_Debug/</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0809</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>.\Unicode_Debug\raster.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Unicode_Debug/raster.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Debug|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>../freetype-2.5.2/include;../agg-2.4/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LIB;WIN32;_DEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;_CRT_NON_CONFORMING_SWPRINTFS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeaderOutputFile>.\Unicode_Debug/raster.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Unicode_Debug/</AssemblerListingLocation>
      <ObjectFileName>.\Unicode_Debug/</ObjectFileName>
      <ProgramDataBaseFileName>.\Unicode_Debug/</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0809</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>.\Unicode_Debug\raster.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Unicode_Debug/raster.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <PreprocessorDefinitions>_LIB;WIN32;NDEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;_CRT_NON_CONFORMING_SWPRINTFS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\Unicode_Release/raster.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Unicode_Release/</AssemblerListingLocation>
      <ObjectFileName>.\Unicode_Release/</ObjectFileName>
      <ProgramDataBaseFileName>.\Unicode_Release/</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0809</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>.\Unicode_Release\raster.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Unicode_Release/raster.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Unicode Release|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <AdditionalIncludeDirectories>../freetype-2.5.2/include;../agg-2.4/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_LIB;WIN32;NDEBUG;UNICODE;_CRT_SECURE_NO_DEPRECATE;_CRT_NON_CONFORMING_SWPRINTFS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeaderOutputFile>.\Unicode_Release/raster.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>.\Unicode_Release/</AssemblerListingLocation>
      <ObjectFileName>.\Unicode_Release/</ObjectFileName>
      <ProgramDataBaseFileName>.\Unicode_Release/</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0809</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>.\Unicode_Release\raster.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
    </Lib>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Unicode_Release/raster.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>../freetype-2.5.2/include;../agg-2.4/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE; GRAPHICS_NO_USE_DYNAMIC_LIBRARY</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <PrecompiledHeaderOutputFile />
      <AssemblerListingLocation>$(Configuration)\</AssemblerListingLocation>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ProgramDataBaseFileName>$(IntDir)vc80.pdb</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <DisableSpecificWarnings>4005;4311;4312;%(DisableSpecificWarnings)</DisableSpecificWarnings>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0809</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)raster.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>true</IgnoreAllDefaultLibraries>
    </Lib>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/raster.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <TargetEnvironment>X64</TargetEnvironment>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>../freetype-2.5.2/include;../agg-2.4/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;_CRT_SECURE_NO_DEPRECATE</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeaderOutputFile>.\Debug/raster.pch</PrecompiledHeaderOutputFile>
      <AssemblerListingLocation>$(Platform)\$(Configuration)\</AssemblerListingLocation>
      <ObjectFileName>$(Platform)\$(Configuration)\</ObjectFileName>
      <ProgramDataBaseFileName>$(Platform)\$(Configuration)\</ProgramDataBaseFileName>
      <WarningLevel>Level3</WarningLevel>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0809</Culture>
    </ResourceCompile>
    <Lib>
      <OutputFile>$(OutDir)raster.lib</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <IgnoreAllDefaultLibraries>true</IgnoreAllDefaultLibraries>
    </Lib>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\Debug/raster.bsc</OutputFile>
    </Bscmake>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\graphics\ArrowHead.cpp" />
    <ClCompile Include="..\graphics\BaseThread.cpp" />
    <ClCompile Include="..\graphics\Brush.cpp" />
    <ClCompile Include="..\graphics\Clip.cpp" />
    <ClCompile Include="..\graphics\Graphics.cpp" />
    <ClCompile Include="..\graphics\GraphicsPath.cpp" />
    <ClCompile Include="..\graphics\GraphicsRenderer.cpp" />
    <ClCompile Include="..\graphics\Image.cpp" />
    <ClCompile Include="..\graphics\Matrix.cpp" />
    <ClCompile Include="..\graphics\pro\pro_Graphics.cpp" />
    <ClCompile Include="..\graphics\pro\pro_Image.cpp" />
    <ClCompile Include="BgraFrame.cpp" />
    <ClCompile Include="ImageFileFormatChecker.cpp" />
    <ClCompile Include="Jp2\J2kFile.cpp" />
    <ClCompile Include="Jp2\Reader.cpp" />
    <ClCompile Include="Metafile\Common\MetaFileTypes.cpp" />
    <ClCompile Include="Metafile\Common\MetaFileUtils.cpp" />
    <ClCompile Include="Metafile\Emf\EmfClip.cpp" />
    <ClCompile Include="Metafile\Emf\EmfFile.cpp" />
    <ClCompile Include="Metafile\Emf\EmfObjects.cpp" />
    <ClCompile Include="Metafile\Emf\EmfPath.cpp" />
    <ClCompile Include="Metafile\Emf\EmfPlayer.cpp" />
    <ClCompile Include="Metafile\MetaFile.cpp" />
    <ClCompile Include="Metafile\StarView\SvmFile.cpp" />
    <ClCompile Include="Metafile\StarView\SvmObjects.cpp" />
    <ClCompile Include="Metafile\StarView\SvmPlayer.cpp" />
    <ClCompile Include="Metafile\svg\SVGFramework.cpp" />
    <ClCompile Include="Metafile\svg\SVGTransformer.cpp" />
    <ClCompile Include="Metafile\Wmf\WmfClip.cpp" />
    <ClCompile Include="Metafile\Wmf\WmfObjects.cpp" />
    <ClCompile Include="Metafile\Wmf\WmfPlayer.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\common\File.h" />
    <ClInclude Include="..\graphics\AggPlusEnums.h" />
    <ClInclude Include="..\graphics\aggplustypes.h" />
    <ClInclude Include="..\graphics\ArrowHead.h" />
    <ClInclude Include="..\graphics\BaseThread.h" />
    <ClInclude Include="..\graphics\Brush.h" />
    <ClInclude Include="..\graphics\Clip.h" />
    <ClInclude Include="..\graphics\Color.h" />
    <ClInclude Include="..\graphics\config.h" />
    <ClInclude Include="..\graphics\Defines.h" />
    <ClInclude Include="..\graphics\Graphics.h" />
    <ClInclude Include="..\graphics\GraphicsPath.h" />
    <ClInclude Include="..\graphics\GraphicsPath_private.h" />
    <ClInclude Include="..\graphics\GraphicsRenderer.h" />
    <ClInclude Include="..\graphics\Image.h" />
    <ClInclude Include="..\graphics\ImageFilesCache.h" />
    <ClInclude Include="..\graphics\IRenderer.h" />
    <ClInclude Include="..\graphics\Matrix.h" />
    <ClInclude Include="..\graphics\Matrix_private.h" />
    <ClInclude Include="BgraFrame.h" />
    <ClInclude Include="ImageFileFormatChecker.h" />
    <ClInclude Include="Jp2\ArithmeticCoder.h" />
    <ClInclude Include="Jp2\DWT.h" />
    <ClInclude Include="Jp2\Image.h" />
    <ClInclude Include="Jp2\J2k.h" />
    <ClInclude Include="Jp2\J2kFile.h" />
    <ClInclude Include="Jp2\J2kIncludes.h" />
    <ClInclude Include="Jp2\Jp2.h" />
    <ClInclude Include="Jp2\JpgEvent.h" />
    <ClInclude Include="Jp2\Jpt.h" />
    <ClInclude Include="Jp2\Mj2.h" />
    <ClInclude Include="Jp2\PacketIterator.h" />
    <ClInclude Include="Jp2\Raw.h" />
    <ClInclude Include="Jp2\Reader.h" />
    <ClInclude Include="Jp2\Stream.h" />
    <ClInclude Include="Jp2\TagTree.h" />
    <ClInclude Include="Jp2\Tier1.h" />
    <ClInclude Include="Jp2\Tier2.h" />
    <ClInclude Include="Jp2\Tile.h" />
    <ClInclude Include="Jp2\Types.h" />
    <ClInclude Include="Jp2\Utils.h" />
    <ClInclude Include="Metafile\Common\IOutputDevice.h" />
    <ClInclude Include="Metafile\Common\MetaFile.h" />
    <ClInclude Include="Metafile\Common\MetaFileClip.h" />
    <ClInclude Include="Metafile\Common\MetaFileObjects.h" />
    <ClInclude Include="Metafile\Common\MetaFileRenderer.h" />
    <ClInclude Include="Metafile\Common\MetaFileTypes.h" />
    <ClInclude Include="Metafile\Common\MetaFileUtils.h" />
    <ClInclude Include="Metafile\Emf\EmfClip.h" />
    <ClInclude Include="Metafile\Emf\EmfFile.h" />
    <ClInclude Include="Metafile\Emf\EmfObjects.h" />
    <ClInclude Include="Metafile\Emf\EmfPath.h" />
    <ClInclude Include="Metafile\Emf\EmfPlayer.h" />
    <ClInclude Include="Metafile\Emf\EmfTypes.h" />
    <ClInclude Include="Metafile\MetaFile.h" />
    <ClInclude Include="Metafile\StarView\SvmEnums.h" />
    <ClInclude Include="Metafile\StarView\SvmFile.h" />
    <ClInclude Include="Metafile\StarView\SvmObjects.h" />
    <ClInclude Include="Metafile\StarView\SvmPlayer.h" />
    <ClInclude Include="Metafile\svg\SVGFramework.h" />
    <ClInclude Include="Metafile\svg\SVGTransformer.h" />
    <ClInclude Include="Metafile\Wmf\WmfClip.h" />
    <ClInclude Include="Metafile\Wmf\WmfFile.h" />
    <ClInclude Include="Metafile\Wmf\WmfObjects.h" />
    <ClInclude Include="Metafile\Wmf\WmfPlayer.h" />
    <ClInclude Include="Metafile\Wmf\WmfTypes.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\agg-2.4\agg_vs2005.vcxproj">
      <Project>{617f9069-5e37-4b80-9a3a-e77afc4cc7ad}</Project>
    </ProjectReference>
    <ProjectReference Include="..\cximage\CxImage\cximage_vs2005.vcxproj">
      <Project>{bc52a07c-a797-423d-8c4f-8678805bbb36}</Project>
    </ProjectReference>
    <ProjectReference Include="..\fontengine\font_engine_vs2005.vcxproj">
      <Project>{c739151f-5384-41df-a1a6-f089e2c1ad56}</Project>
    </ProjectReference>
    <ProjectReference Include="..\freetype-2.5.2\builds\windows\vc2005\freetype.vcxproj">
      <Project>{78b079bd-9fc7-4b9e-b4a6-96da0f00248b}</Project>
    </ProjectReference>
    <ProjectReference Include="JBig2\win32\jbig2.vcxproj">
      <Project>{ee1b576a-07c5-4acc-920f-81c41dd0c8c1}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>