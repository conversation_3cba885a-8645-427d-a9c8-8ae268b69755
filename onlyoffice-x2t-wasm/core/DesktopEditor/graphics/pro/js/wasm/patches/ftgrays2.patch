<<<<<<<
  static int
  gray_convert_glyph_inner( RAS_ARG,
                            int  continued )
  {
    int  error;


    if ( ft_setjmp( ras.jump_buffer ) == 0 )
    {
      if ( continued )
        FT_Trace_Disable();
      error = FT_Outline_Decompose( &ras.outline, &func_interface, &ras );
      if ( continued )
        FT_Trace_Enable();

      if ( !ras.invalid )
        gray_record_cell( RAS_VAR );

      FT_TRACE7(( "band [%d..%d]: %ld cell%s\n",
                  ras.min_ey,
                  ras.max_ey,
                  ras.num_cells,
                  ras.num_cells == 1 ? "" : "s" ));
    }
    else
    {
      error = FT_THROW( Memory_Overflow );

      FT_TRACE7(( "band [%d..%d]: to be bisected\n",
                  ras.min_ey, ras.max_ey ));
    }

    return error;
  }
=======
  static int
  gray_convert_glyph_inner( RAS_ARG,
                            int  continued )
  {
    int  error;


    try
    {
      if ( continued )
        FT_Trace_Disable();
      error = FT_Outline_Decompose( &ras.outline, &func_interface, &ras );
      if ( continued )
        FT_Trace_Enable();

      if ( !ras.invalid )
        gray_record_cell( RAS_VAR );

      FT_TRACE7(( "band [%d..%d]: %ld cell%s\n",
                  ras.min_ey,
                  ras.max_ey,
                  ras.num_cells,
                  ras.num_cells == 1 ? "" : "s" ));
    }
    catch(int jmp)
    {
      error = FT_THROW( Memory_Overflow );

      FT_TRACE7(( "band [%d..%d]: to be bisected\n",
                  ras.min_ey, ras.max_ey ));
    }

    return error;
  }
>>>>>>>
