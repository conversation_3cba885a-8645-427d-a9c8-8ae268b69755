#include "../../cximage/libpsd/adjustment.c"
#include "../../cximage/libpsd/bevel_emboss.c"
#include "../../cximage/libpsd/bitmap.c"
#include "../../cximage/libpsd/blend.c"
#include "../../cximage/libpsd/boundary.c"
#include "../../cximage/libpsd/brightness_contrast.c"
#include "../../cximage/libpsd/channel_image.c"
#include "../../cximage/libpsd/channel_mixer.c"
#include "../../cximage/libpsd/color_balance.c"
#include "../../cximage/libpsd/color_mode.c"
#include "../../cximage/libpsd/color_overlay.c"
#include "../../cximage/libpsd/color.c"
#include "../../cximage/libpsd/curves.c"
#include "../../cximage/libpsd/drop_shadow.c"
#include "../../cximage/libpsd/effects.c"
#include "../../cximage/libpsd/file_header.c"
#include "../../cximage/libpsd/fixed.c"
#include "../../cximage/libpsd/gaussian_blur.c"
#include "../../cximage/libpsd/gradient_blend.c"
#include "../../cximage/libpsd/gradient_fill.c"
#include "../../cximage/libpsd/gradient_map.c"
#include "../../cximage/libpsd/gradient_overlay.c"
#include "../../cximage/libpsd/hue_saturation.c"
