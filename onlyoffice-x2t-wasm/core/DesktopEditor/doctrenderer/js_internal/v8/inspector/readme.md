Классы v8-инспектора - это CInspectorClient (client) и CInspectorChannel (channel).
Channel нужен для передачи сообщений на фронтенд и не представляет собой ничего интересного.
Базовые функции клиента заключаются в том, чтобы синхронно принимать сообщения с фронтенда,
когда JS стоит на паузе (runMessageLoopOnPause), а также сниматься с паузы (quitMessageLoopOnPause).
Вся логика создания v8 debug session реализована на клиенте в функции setUpDebuggingSession, потому что так удобнее.
Там создаётся channel, далее создаётся v8-inspector, для этого ему нужен isolate и client.
Далее создаётся V8InspectorSession. Контекст, в котором создан client, регистрируется на инспекторе.
Кроме всего этого, нужно отправлять входящие с сервера сообщения на v8 (V8InspectorSession::dispatchProtocolMessage).
Этим также занимается client, раз уж V8InspectorSession лежит в нём.
Класс CInspectorImpl - это обёртка вокруг клиента, в которой реализована дополнительная логика.
Во-первых, там лежит клиент, в котором лежит всё, что напрямую касается v8-инспектора.
Во-вторых, там лежит сервер, к которому подключается Chrome Developer Tools и через который происходит обмен сообщениями.
Сервер умеет отправлять сообщения, ждать одного сообщения, а также входить и выходить из message loop (последнее на практике почти не используется).
CInspectorPool - это синглтон, который хранит в себе CInspectorImpl - по одному на v8::Context.
inspector_interface.h - это просто обёртка вокруг CInspectorPool без лишних #include, 
что позволяет гарантированно избегать циклических зависомостей в заголовках.
Также он умеет удалять их.
Singlethreadutils - это примитивы, необходимые для:
 - парсинга файлов (пережиток тестовых запусков);
 - логгирования сообщений;
 - преобразований между разными строками: std::string, v8::String и v8_inspector::StringView 
   (это необходимо для взаимодействия между сервером, принимающим std::string, и v8, использующим свои строковые классы);
 - парсинга жсонов - это нужно, чтобы доставать из сообщений с фронтенда пересылаемые методы.
Зачем нужно последнее, станет ясно чуть позже.

Работает это следующим образом.
В самом начале, когда инспектор создаётся, на нём должен быть вызван prepareServer, 
который ждёт подключения Chrome Developer Tools (CDT) и после этого запускает message loop на сервере.
Клиент ждёт, когда с сервера придёт сообщение Runtime.runIfWaitingForDebugger. Это свидетельствует о готовности CDT. 
После этого сервер выходит из message loop.
Перед запуском скрипта или функции вызывается CInspectorImpl::beforeLaunch, 
который вызывает CInspectorClient::pauseOnNextStatement, который вызывает V8InspectorSession::schedulePauseOnNextStatement. 
Эта функция ставит галочку, что на следующем стейтменте нужно остановиться, и сразу возвращается.
Пауза на первом стейтменте скрипта или функции нужна, чтобы они появились в CDT.
Кроме того, клиент на этом моменте ставит флаг, что паузу он вызвал сам - это нужно в дальнейшем.
Далее при запуске функции или скрипта он ставится на паузу, вызывается CInspectorClient::runMessageLoopOnPause, 
и начинается синхронный обмен сообщениями.
Этот обмен нужно закончить, когда скрипт или функция появятся в CDT. 
В случае со скриптом индикатором этого является метод Debugger.getScriptSource, приходящий с сервера.
В случае с функцией однозначного индикатора нет, используется метод Runtime.getProperties. 
Также возможно использование метода Overlay.setPausedInDebuggerMessage - обычно он приходит раньше, чем Runtime.getProperties,
но этот вариант не протестирован.
Проверка сообщений с сервера на наличие этих методов производится после их отправки на v8.
В случае, если не стоит флаг о том, что пауза была вызвана с клиента, сообщения не проверяются вообще - 
это значит, что пауза вызвана пользователем (чаще всего это breakpoint или step into/over/out после брейкпойнта)
и с неё не нужно сниматься автоматически.
В случае, если пауза была вызвана клиентом, он мониторит входящие сообщения на предмет вышеописанных методов 
и вызывает V8InspectorSession::resume, если один из них приходит.
После этого на CDT отправляется сообщение Debugger.resume, и JS-код дальше исполняется внутри v8.
Если там есть где остановиться, v8 снова входит в runMessageLoopOnPause и отправляет на CDT Debugger.paused.
Поскольку это пользовательская пауза, клиент не снимается с неё автоматически, 
а ждёт, пока пользователь снимется с неё через CDT.
Когда JS-код полностью исполнен, продолжается выполнение C++-кода.
Элементы v8-инспектора, которые лежат внутри клиента, нужно уничтожать до выхода из isolate scope и context scope.
Если их уничтожать на выходе из программы, то она падает.
Чтобы этого избежать, в CJSContext::dispose вызывается CInspectorPool::disposeContext, который находит CInspectorImpl, 
созданный для уничтожаемого контекста, и удаляет его.
Каждый сервер, а следовательно, каждый CInspectorImpl, требует порт. 
CInspectorImpl создаётся на каждый контекст (хотя было бы правильнее на isolate, но здесь это не принципиально,
потому что на isolate всегда только один контекст).
Следовательно, на каждый контекст выделяется порт.
Порты начинаются с 8080.