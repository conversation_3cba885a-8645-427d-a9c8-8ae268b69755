//Microsoft Visual C++ generated resource script.
//
#include "resource.h"
#define COMPONENT_NAME "docbuilder.com"
#include "version.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "winres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_RUS)
LANGUAGE 25, 1
#pragma code_page(1251)
#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE  
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE  
BEGIN
    "#include ""winres.h""\r\n"
    "\0"
END


#endif    // APSTUDIO_INVOKED

#ifndef _MAC
/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION INTVER
 PRODUCTVERSION INTVER
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904e4"
        BEGIN
            VALUE "CompanyName", "Ascensio System SIA 2019"
            VALUE "FileDescription", "ONLYOFFICE docbuilder ActiveX DLL"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "docbuilder.com.dll"
            VALUE "LegalCopyright", "Copyright (C) Ascensio System SIA 2019. All rights reserved."
            VALUE "OriginalFilename", "docbuilder.com.dll"
            VALUE "ProductName", "docbuilder.com"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
		VALUE "Translation", 0x0409, 1252
    END
END

#endif    // !_MAC

/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE  
BEGIN
	IDS_PROJNAME					"docbuilder.com"
END

IDR_ONLYOFFICEDOCBUILDER REGISTRY "docbuilder.com.rgs"
////////////////////////////////////////////////////////////////////////////


#endif

#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

