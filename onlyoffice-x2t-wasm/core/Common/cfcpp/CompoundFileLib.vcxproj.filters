﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="RBTree">
      <UniqueIdentifier>{2e4c5c99-c3a8-43af-8b70-e1527104287a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Stream">
      <UniqueIdentifier>{2486cb73-0ee3-4336-80d0-0b9b06b62860}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="cfexception.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cfitem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cfstorage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cfstream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="compoundfile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="compoundfile_impl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="directoryentry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="event.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="guid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="header.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="idirectoryentry.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="sector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="sectorcollection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="slist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="streamrw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="streamview.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="svector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RBTree\irbnode.h">
      <Filter>RBTree</Filter>
    </ClInclude>
    <ClInclude Include="RBTree\rbtree.h">
      <Filter>RBTree</Filter>
    </ClInclude>
    <ClInclude Include="RBTree\rbtreeexception.h">
      <Filter>RBTree</Filter>
    </ClInclude>
    <ClInclude Include="RBTree\action.h">
      <Filter>RBTree</Filter>
    </ClInclude>
    <ClInclude Include="Stream\fstream_utils.h">
      <Filter>Stream</Filter>
    </ClInclude>
    <ClInclude Include="Stream\fstream_wrapper.h">
      <Filter>Stream</Filter>
    </ClInclude>
    <ClInclude Include="Stream\stream.h">
      <Filter>Stream</Filter>
    </ClInclude>
    <ClInclude Include="Stream\stream_utils.h">
      <Filter>Stream</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="cfitem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cfstorage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cfstream.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="compoundfile.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="directoryentry.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="header.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sectorcollection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="streamrw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="streamview.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RBTree\rbtree.cpp">
      <Filter>RBTree</Filter>
    </ClCompile>
    <ClCompile Include="Stream\fstream_utils.cpp">
      <Filter>Stream</Filter>
    </ClCompile>
    <ClCompile Include="Stream\stream_utils.cpp">
      <Filter>Stream</Filter>
    </ClCompile>
  </ItemGroup>
</Project>