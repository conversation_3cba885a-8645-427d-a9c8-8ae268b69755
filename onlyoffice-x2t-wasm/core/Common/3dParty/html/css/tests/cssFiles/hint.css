/*! Hint.css - v2.6.0 - 2019-04-27
* http://kushagragour.in/lab/hint/
* Copyright (c) 2019 Kushagra Gour */

/*-------------------------------------*\
	HINT.css - A CSS tooltip library
\*-------------------------------------*/
/**
 * HINT.css is a tooltip library made in pure CSS.
 *
 * Source: https://github.com/chinchang/hint.css
 * Demo: http://kushagragour.in/lab/hint/
 *
 */
/**
 * source: hint-core.scss
 *
 * Defines the basic styling for the tooltip.
 * Each tooltip is made of 2 parts:
 * 	1) body (:after)
 * 	2) arrow (:before)
 *
 * Classes added:
 * 	1) hint
 */
[class*="hint--"] {
  position: relative;
  display: inline-block;
  /**
	 * tooltip arrow
	 */
  /**
	 * tooltip body
	 */ }
  [class*="hint--"]:before, [class*="hint--"]:after {
    position: absolute;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    visibility: hidden;
    opacity: 0;
    z-index: 1000000;
    pointer-events: none;
    -webkit-transition: 0.3s ease;
    -moz-transition: 0.3s ease;
    transition: 0.3s ease;
    -webkit-transition-delay: 0ms;
    -moz-transition-delay: 0ms;
    transition-delay: 0ms; }
  [class*="hint--"]:hover:before, [class*="hint--"]:hover:after {
    visibility: visible;
    opacity: 1; }
  [class*="hint--"]:hover:before, [class*="hint--"]:hover:after {
    -webkit-transition-delay: 100ms;
    -moz-transition-delay: 100ms;
    transition-delay: 100ms; }
  [class*="hint--"]:before {
    content: '';
    position: absolute;
    background: transparent;
    border: 6px solid transparent;
    z-index: 1000001; }
  [class*="hint--"]:after {
    background: #383838;
    color: white;
    padding: 8px 10px;
    font-size: 12px;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    line-height: 12px;
    white-space: nowrap; }
  [class*="hint--"][aria-label]:after {
    content: attr(aria-label); }
  [class*="hint--"][data-hint]:after {
    content: attr(data-hint); }

[aria-label='']:before, [aria-label='']:after,
[data-hint='']:before,
[data-hint='']:after {
  display: none !important; }

/**
 * source: hint-position.scss
 *
 * Defines the positoning logic for the tooltips.
 *
 * Classes added:
 * 	1) hint--top
 * 	2) hint--bottom
 * 	3) hint--left
 * 	4) hint--right
 */
/**
 * set default color for tooltip arrows
 */
.hint--top-left:before {
  border-top-color: #383838; }

.hint--top-right:before {
  border-top-color: #383838; }

.hint--top:before {
  border-top-color: #383838; }

.hint--bottom-left:before {
  border-bottom-color: #383838; }

.hint--bottom-right:before {
  border-bottom-color: #383838; }

.hint--bottom:before {
  border-bottom-color: #383838; }

.hint--left:before {
  border-left-color: #383838; }

.hint--right:before {
  border-right-color: #383838; }

/**
 * top tooltip
 */
.hint--top:before {
  margin-bottom: -11px; }

.hint--top:before, .hint--top:after {
  bottom: 100%;
  left: 50%; }

.hint--top:before {
  left: calc(50% - 6px); }

.hint--top:after {
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  transform: translateX(-50%); }

.hint--top:hover:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px); }

.hint--top:hover:after {
  -webkit-transform: translateX(-50%) translateY(-8px);
  -moz-transform: translateX(-50%) translateY(-8px);
  transform: translateX(-50%) translateY(-8px); }

/**
 * bottom tooltip
 */
.hint--bottom:before {
  margin-top: -11px; }

.hint--bottom:before, .hint--bottom:after {
  top: 100%;
  left: 50%; }

.hint--bottom:before {
  left: calc(50% - 6px); }

.hint--bottom:after {
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  transform: translateX(-50%); }

.hint--bottom:hover:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px); }

.hint--bottom:hover:after {
  -webkit-transform: translateX(-50%) translateY(8px);
  -moz-transform: translateX(-50%) translateY(8px);
  transform: translateX(-50%) translateY(8px); }

/**
 * right tooltip
 */
.hint--right:before {
  margin-left: -11px;
  margin-bottom: -6px; }

.hint--right:after {
  margin-bottom: -14px; }

.hint--right:before, .hint--right:after {
  left: 100%;
  bottom: 50%; }

.hint--right:hover:before {
  -webkit-transform: translateX(8px);
  -moz-transform: translateX(8px);
  transform: translateX(8px); }

.hint--right:hover:after {
  -webkit-transform: translateX(8px);
  -moz-transform: translateX(8px);
  transform: translateX(8px); }

/**
 * left tooltip
 */
.hint--left:before {
  margin-right: -11px;
  margin-bottom: -6px; }

.hint--left:after {
  margin-bottom: -14px; }

.hint--left:before, .hint--left:after {
  right: 100%;
  bottom: 50%; }

.hint--left:hover:before {
  -webkit-transform: translateX(-8px);
  -moz-transform: translateX(-8px);
  transform: translateX(-8px); }

.hint--left:hover:after {
  -webkit-transform: translateX(-8px);
  -moz-transform: translateX(-8px);
  transform: translateX(-8px); }

/**
 * top-left tooltip
 */
.hint--top-left:before {
  margin-bottom: -11px; }

.hint--top-left:before, .hint--top-left:after {
  bottom: 100%;
  left: 50%; }

.hint--top-left:before {
  left: calc(50% - 6px); }

.hint--top-left:after {
  -webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  transform: translateX(-100%); }

.hint--top-left:after {
  margin-left: 12px; }

.hint--top-left:hover:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px); }

.hint--top-left:hover:after {
  -webkit-transform: translateX(-100%) translateY(-8px);
  -moz-transform: translateX(-100%) translateY(-8px);
  transform: translateX(-100%) translateY(-8px); }

/**
 * top-right tooltip
 */
.hint--top-right:before {
  margin-bottom: -11px; }

.hint--top-right:before, .hint--top-right:after {
  bottom: 100%;
  left: 50%; }

.hint--top-right:before {
  left: calc(50% - 6px); }

.hint--top-right:after {
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  transform: translateX(0); }

.hint--top-right:after {
  margin-left: -12px; }

.hint--top-right:hover:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px); }

.hint--top-right:hover:after {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px); }

/**
 * bottom-left tooltip
 */
.hint--bottom-left:before {
  margin-top: -11px; }

.hint--bottom-left:before, .hint--bottom-left:after {
  top: 100%;
  left: 50%; }

.hint--bottom-left:before {
  left: calc(50% - 6px); }

.hint--bottom-left:after {
  -webkit-transform: translateX(-100%);
  -moz-transform: translateX(-100%);
  transform: translateX(-100%); }

.hint--bottom-left:after {
  margin-left: 12px; }

.hint--bottom-left:hover:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px); }

.hint--bottom-left:hover:after {
  -webkit-transform: translateX(-100%) translateY(8px);
  -moz-transform: translateX(-100%) translateY(8px);
  transform: translateX(-100%) translateY(8px); }

/**
 * bottom-right tooltip
 */
.hint--bottom-right:before {
  margin-top: -11px; }

.hint--bottom-right:before, .hint--bottom-right:after {
  top: 100%;
  left: 50%; }

.hint--bottom-right:before {
  left: calc(50% - 6px); }

.hint--bottom-right:after {
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  transform: translateX(0); }

.hint--bottom-right:after {
  margin-left: -12px; }

.hint--bottom-right:hover:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px); }

.hint--bottom-right:hover:after {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px); }

/**
 * source: hint-sizes.scss
 *
 * Defines width restricted tooltips that can span
 * across multiple lines.
 *
 * Classes added:
 * 	1) hint--small
 * 	2) hint--medium
 * 	3) hint--large
 *
 */
.hint--small:after,
.hint--medium:after,
.hint--large:after {
  white-space: normal;
  line-height: 1.4em;
  word-wrap: break-word; }

.hint--small:after {
  width: 80px; }

.hint--medium:after {
  width: 150px; }

.hint--large:after {
  width: 300px; }

/**
 * source: hint-theme.scss
 *
 * Defines basic theme for tooltips.
 *
 */
[class*="hint--"] {
  /**
	 * tooltip body
	 */ }
  [class*="hint--"]:after {
    text-shadow: 0 -1px 0px black;
    box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3); }

/**
 * source: hint-color-types.scss
 *
 * Contains tooltips of various types based on color differences.
 *
 * Classes added:
 * 	1) hint--error
 * 	2) hint--warning
 * 	3) hint--info
 * 	4) hint--success
 *
 */
/**
 * Error
 */
.hint--error:after {
  background-color: #b34e4d;
  text-shadow: 0 -1px 0px #592726; }

.hint--error.hint--top-left:before {
  border-top-color: #b34e4d; }

.hint--error.hint--top-right:before {
  border-top-color: #b34e4d; }

.hint--error.hint--top:before {
  border-top-color: #b34e4d; }

.hint--error.hint--bottom-left:before {
  border-bottom-color: #b34e4d; }

.hint--error.hint--bottom-right:before {
  border-bottom-color: #b34e4d; }

.hint--error.hint--bottom:before {
  border-bottom-color: #b34e4d; }

.hint--error.hint--left:before {
  border-left-color: #b34e4d; }

.hint--error.hint--right:before {
  border-right-color: #b34e4d; }

/**
 * Warning
 */
.hint--warning:after {
  background-color: #c09854;
  text-shadow: 0 -1px 0px #6c5328; }

.hint--warning.hint--top-left:before {
  border-top-color: #c09854; }

.hint--warning.hint--top-right:before {
  border-top-color: #c09854; }

.hint--warning.hint--top:before {
  border-top-color: #c09854; }

.hint--warning.hint--bottom-left:before {
  border-bottom-color: #c09854; }

.hint--warning.hint--bottom-right:before {
  border-bottom-color: #c09854; }

.hint--warning.hint--bottom:before {
  border-bottom-color: #c09854; }

.hint--warning.hint--left:before {
  border-left-color: #c09854; }

.hint--warning.hint--right:before {
  border-right-color: #c09854; }

/**
 * Info
 */
.hint--info:after {
  background-color: #3986ac;
  text-shadow: 0 -1px 0px #1a3c4d; }

.hint--info.hint--top-left:before {
  border-top-color: #3986ac; }

.hint--info.hint--top-right:before {
  border-top-color: #3986ac; }

.hint--info.hint--top:before {
  border-top-color: #3986ac; }

.hint--info.hint--bottom-left:before {
  border-bottom-color: #3986ac; }

.hint--info.hint--bottom-right:before {
  border-bottom-color: #3986ac; }

.hint--info.hint--bottom:before {
  border-bottom-color: #3986ac; }

.hint--info.hint--left:before {
  border-left-color: #3986ac; }

.hint--info.hint--right:before {
  border-right-color: #3986ac; }

/**
 * Success
 */
.hint--success:after {
  background-color: #458746;
  text-shadow: 0 -1px 0px #1a321a; }

.hint--success.hint--top-left:before {
  border-top-color: #458746; }

.hint--success.hint--top-right:before {
  border-top-color: #458746; }

.hint--success.hint--top:before {
  border-top-color: #458746; }

.hint--success.hint--bottom-left:before {
  border-bottom-color: #458746; }

.hint--success.hint--bottom-right:before {
  border-bottom-color: #458746; }

.hint--success.hint--bottom:before {
  border-bottom-color: #458746; }

.hint--success.hint--left:before {
  border-left-color: #458746; }

.hint--success.hint--right:before {
  border-right-color: #458746; }

/**
 * source: hint-always.scss
 *
 * Defines a persisted tooltip which shows always.
 *
 * Classes added:
 * 	1) hint--always
 *
 */
.hint--always:after, .hint--always:before {
  opacity: 1;
  visibility: visible; }

.hint--always.hint--top:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px); }

.hint--always.hint--top:after {
  -webkit-transform: translateX(-50%) translateY(-8px);
  -moz-transform: translateX(-50%) translateY(-8px);
  transform: translateX(-50%) translateY(-8px); }

.hint--always.hint--top-left:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px); }

.hint--always.hint--top-left:after {
  -webkit-transform: translateX(-100%) translateY(-8px);
  -moz-transform: translateX(-100%) translateY(-8px);
  transform: translateX(-100%) translateY(-8px); }

.hint--always.hint--top-right:before {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px); }

.hint--always.hint--top-right:after {
  -webkit-transform: translateY(-8px);
  -moz-transform: translateY(-8px);
  transform: translateY(-8px); }

.hint--always.hint--bottom:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px); }

.hint--always.hint--bottom:after {
  -webkit-transform: translateX(-50%) translateY(8px);
  -moz-transform: translateX(-50%) translateY(8px);
  transform: translateX(-50%) translateY(8px); }

.hint--always.hint--bottom-left:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px); }

.hint--always.hint--bottom-left:after {
  -webkit-transform: translateX(-100%) translateY(8px);
  -moz-transform: translateX(-100%) translateY(8px);
  transform: translateX(-100%) translateY(8px); }

.hint--always.hint--bottom-right:before {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px); }

.hint--always.hint--bottom-right:after {
  -webkit-transform: translateY(8px);
  -moz-transform: translateY(8px);
  transform: translateY(8px); }

.hint--always.hint--left:before {
  -webkit-transform: translateX(-8px);
  -moz-transform: translateX(-8px);
  transform: translateX(-8px); }

.hint--always.hint--left:after {
  -webkit-transform: translateX(-8px);
  -moz-transform: translateX(-8px);
  transform: translateX(-8px); }

.hint--always.hint--right:before {
  -webkit-transform: translateX(8px);
  -moz-transform: translateX(8px);
  transform: translateX(8px); }

.hint--always.hint--right:after {
  -webkit-transform: translateX(8px);
  -moz-transform: translateX(8px);
  transform: translateX(8px); }

/**
 * source: hint-rounded.scss
 *
 * Defines rounded corner tooltips.
 *
 * Classes added:
 * 	1) hint--rounded
 *
 */
.hint--rounded:after {
  border-radius: 4px; }

/**
 * source: hint-effects.scss
 *
 * Defines various transition effects for the tooltips.
 *
 * Classes added:
 * 	1) hint--no-animate
 * 	2) hint--bounce
 *
 */
.hint--no-animate:before, .hint--no-animate:after {
  -webkit-transition-duration: 0ms;
  -moz-transition-duration: 0ms;
  transition-duration: 0ms; }

.hint--bounce:before, .hint--bounce:after {
  -webkit-transition: opacity 0.3s ease, visibility 0.3s ease, -webkit-transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
  -moz-transition: opacity 0.3s ease, visibility 0.3s ease, -moz-transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24);
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s cubic-bezier(0.71, 1.7, 0.77, 1.24); }

.hint--no-shadow:before, .hint--no-shadow:after {
  text-shadow: initial;
  box-shadow: initial; }
