<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Microsoft documentation for VCXPROJ file format is located at -->
  <!-- the following URL. The documentation leaves a lot to be desired. -->
  <!-- https://msdn.microsoft.com/en-us/library/2208a1f2.aspx -->
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DLL-Import Debug|Win32">
      <Configuration>DLL-Import Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DLL-Import Debug|x64">
      <Configuration>DLL-Import Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DLL-Import Release|Win32">
      <Configuration>DLL-Import Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="DLL-Import Release|x64">
      <Configuration>DLL-Import Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <!-- Microsoft documentation clearly shows the Global property group -->
  <!-- preceeds the import of Cpp.Default.props and Cpp.props -->
  <!-- https://msdn.microsoft.com/en-us/library/2208a1f2.aspx -->
  <PropertyGroup Label="Globals">
    <ProjectGuid>{09cdac08-e6ae-48a9-8de7-0fbc779eebde}</ProjectGuid>
    <RootNamespace>cryptest</RootNamespace>
    <ConfigurationType>Application</ConfigurationType>
  </PropertyGroup>
  <!-- Use DefaultPlatformToolset after Microsoft.Cpp.Default.props -->
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <!-- Set DefaultPlatformToolset to v100 (VS2010) if not defined -->
  <PropertyGroup Label="EmptyDefaultPlatformToolset">
    <DefaultPlatformToolset Condition=" '$(DefaultPlatformToolset)' == '' ">v100</DefaultPlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="PlatformToolset">
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <!-- End of Visual Studio boilerplate -->
  <!-- All Configurations -->
  <PropertyGroup Label="All Configurations">
    <ConfigurationType>Application</ConfigurationType>
    <TargetName>cryptest</TargetName>
    <TargetExt>.exe</TargetExt>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <!-- Intermediate and Output directories -->
  <!-- Cryptlib and Cryptest need extra care due to Non-DLL/DLL configs -->
  <PropertyGroup Condition="'$(Configuration)'=='Debug' Or '$(Configuration)'=='Release'" Label="Non-DLL Directories">
    <OutDir>$(Platform)\Output\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='DLL-Import Debug'" Label="DLL Directories">
    <OutDir>$(Platform)\DLL_Output\Debug\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='DLL-Import Release'" Label="DLL Directories">
    <OutDir>$(Platform)\DLL_Output\Release\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <!-- All Configurations -->
  <ItemDefinitionGroup Label="All Configurations">
    <ClCompile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <ErrorReporting>None</ErrorReporting>
      <WarningLevel>Level4</WarningLevel>
      <DisableSpecificWarnings>4231; 4251; 4275; 4355; 4505</DisableSpecificWarnings>
      <PrecompiledHeader />
      <PrecompiledHeadeFile />
    </ClCompile>
    <Link>
      <ErrorReporting>NoErrorReport</ErrorReporting>
      <AdditionalDependencies>cryptlib.lib;Ws2_32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(Platform)\Output\$(Configuration)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)\cryptlib.pdb</ProgramDatabaseFile>
      <OutputFile>$(OutDir)\cryptest.exe</OutputFile>
      <SubSystem>Console</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <!-- Debug Configurations -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug' Or '$(Configuration)'=='DLL-Import Debug'" Label="Debug Configuration">
    <ClCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization>Disabled</Optimization>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <!-- Release Configurations -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release' Or '$(Configuration)'=='DLL-Import Release'" Label="Release Configuration">
    <ClCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
    </Link>
  </ItemDefinitionGroup>
  <!-- DLL-Import Overrides for Preprocessor and Libraries -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='DLL-Import Debug'" Label="DLL Debug Configuration">
    <ClCompile>
      <PreprocessorDefinitions>CRYPTOPP_IMPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalLibraryDirectories>$(Platform)\DLL_Output\Debug</AdditionalLibraryDirectories>
      <AdditionalDependencies>cryptopp.lib;cryptlib.lib;Ws2_32.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='DLL-Import Release'" Label="DLL Debug Configuration">
    <ClCompile>
      <PreprocessorDefinitions>NDEBUG;CRYPTOPP_IMPORTS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <AdditionalLibraryDirectories>$(Platform)\DLL_Output\Release</AdditionalLibraryDirectories>
      <AdditionalDependencies>cryptopp.lib;cryptlib.lib;Ws2_32.lib</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <!-- X86 Configurations -->
  <ItemDefinitionGroup Condition="'$(Platform)'=='Win32'" Label="X86 Configuration">
    <ClCompile>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>true</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <!-- X64 Configurations -->
  <ItemDefinitionGroup Condition="'$(Platform)'=='x64'" Label="X64 Configuration">
    <Link>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <!-- FIPS Validated Warning -->
  <ItemDefinitionGroup Label="FIPS Warning">
    <PreBuildEvent Condition="'$(Configuration)'=='DLL-Import Debug' Or '$(Configuration)'=='DLL-Import Release'">
      <Command>
        echo *************************************************************************
        echo This configuration requires cryptopp.dll. You can build it yourself using
        echo the cryptdll project, or obtain a pre-built, FIPS 140-2 validated DLL. If
        echo you build it yourself the resulting DLL will not be considered FIPS
        echo validated unless it undergoes the FIPS validation process.
        echo *************************************************************************
      </Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <!-- Target for Appvoyer                           -->
  <!-- The rule copies cryptest.exe to the project   -->
  <!-- root directory so it can be executed in place -->
  <Target Name="CopyCryptestToRoot">
    <Exec Command="copy $(Platform)\Output\$(Configuration)\cryptest.exe $(SolutionDir)" />
  </Target>
  <!-- Source Files -->
  <ItemGroup>
    <ClCompile Include="adhoc.cpp" />
    <ClCompile Include="bench1.cpp" />
    <ClCompile Include="bench2.cpp" />
    <ClCompile Include="datatest.cpp" />
    <ClCompile Include="dlltest.cpp" />
    <ClCompile Include="fipsalgt.cpp" />
    <ClCompile Include="regtest1.cpp" />
    <ClCompile Include="regtest2.cpp" />
    <ClCompile Include="regtest3.cpp" />
    <ClCompile Include="test.cpp" />
    <ClCompile Include="validat0.cpp" />
    <ClCompile Include="validat1.cpp" />
    <ClCompile Include="validat2.cpp" />
    <ClCompile Include="validat3.cpp" />
    <ClCompile Include="validat4.cpp" />
  </ItemGroup>
  <!-- Header Files -->
  <ItemGroup>
    <ClInclude Include="bench.h" />
    <ClInclude Include="factory.h" />
    <ClInclude Include="validate.h" />
  </ItemGroup>
  <!-- Test Files -->
  <ItemGroup>
    <None Include="TestVectors\aes.txt" />
    <None Include="TestVectors\all.txt" />
    <None Include="TestVectors\blake2.txt" />
    <None Include="TestVectors\blake2b.txt" />
    <None Include="TestVectors\blake2s.txt" />
    <None Include="TestVectors\aria.txt" />
    <None Include="TestVectors\camellia.txt" />
    <None Include="TestVectors\ccm.txt" />
    <None Include="TestVectors\chacha.txt" />
    <None Include="TestVectors\cmac.txt" />
    <None Include="TestVectors\dlies.txt" />
    <None Include="TestVectors\dsa.txt" />
    <None Include="TestVectors\dsa_1363.txt" />
    <None Include="TestVectors\dsa_rfc6979.txt" />
    <None Include="TestVectors\eax.txt" />
    <None Include="TestVectors\esign.txt" />
    <None Include="TestVectors\gcm.txt" />
    <None Include="TestVectors\hkdf.txt" />
    <None Include="TestVectors\hmac.txt" />
    <None Include="TestVectors\kalyna.txt" />
    <None Include="TestVectors\mars.txt" />
    <None Include="TestVectors\nr.txt" />
    <None Include="TestVectors\panama.txt" />
    <None Include="TestVectors\Readme.txt" />
    <None Include="TestVectors\rsa_oaep.txt" />
    <None Include="TestVectors\rsa_pkcs1_1_5.txt" />
    <None Include="TestVectors\rsa_pss.txt" />
    <None Include="TestVectors\rw.txt" />
    <None Include="TestVectors\salsa.txt" />
    <None Include="TestVectors\seal.txt" />
    <None Include="TestVectors\seed.txt" />
    <None Include="TestVectors\sha.txt" />
    <None Include="TestVectors\sha2.txt" />
    <None Include="TestVectors\sha3.txt" />
    <None Include="TestVectors\shacal2.txt" />
    <None Include="TestVectors\simon.txt" />
    <None Include="TestVectors\siphash.txt" />
    <Text Include="TestVectors\sm3.txt" />
    <None Include="TestVectors\sm4.txt" />
    <None Include="TestVectors\sosemanuk.txt" />
    <None Include="TestVectors\speck.txt" />
    <None Include="TestVectors\tea.txt" />
    <None Include="TestVectors\threefish.txt" />
    <None Include="TestVectors\ttmac.txt" />
    <None Include="TestVectors\vmac.txt" />
    <None Include="TestVectors\wake.txt" />
    <None Include="TestVectors\whrlpool.txt" />
    <None Include="TestData\3desval.dat" />
    <None Include="TestData\3wayval.dat" />
    <None Include="TestData\aria.dat" />
    <None Include="TestData\camellia.dat" />
    <None Include="TestData\cast128v.dat" />
    <None Include="TestData\cast256v.dat" />
    <None Include="TestData\descert.dat" />
    <None Include="TestData\dh1024.dat" />
    <None Include="TestData\dh2048.dat" />
    <None Include="TestData\dlie1024.dat" />
    <None Include="TestData\dlie2048.dat" />
    <None Include="TestData\dsa1024.dat" />
    <None Include="TestData\dsa1024b.dat" />
    <None Include="TestData\dsa512.dat" />
    <None Include="TestData\elgc1024.dat" />
    <None Include="TestData\esig1023.dat" />
    <None Include="TestData\esig1536.dat" />
    <None Include="TestData\esig2046.dat" />
    <None Include="TestData\fhmqv160.dat" />
    <None Include="TestData\fhmqv256.dat" />
    <None Include="TestData\fhmqv384.dat" />
    <None Include="TestData\fhmqv512.dat" />
    <None Include="TestData\gostval.dat" />
    <None Include="TestData\hmqv160.dat" />
    <None Include="TestData\hmqv256.dat" />
    <None Include="TestData\hmqv384.dat" />
    <None Include="TestData\hmqv512.dat" />
    <None Include="TestData\ideaval.dat" />
    <None Include="TestData\luc1024.dat" />
    <None Include="TestData\luc2048.dat" />
    <None Include="TestData\lucc1024.dat" />
    <None Include="TestData\lucc512.dat" />
    <None Include="TestData\lucd1024.dat" />
    <None Include="TestData\lucd512.dat" />
    <None Include="TestData\lucs1024.dat" />
    <None Include="TestData\lucs512.dat" />
    <None Include="TestData\marsval.dat" />
    <None Include="TestData\mqv1024.dat" />
    <None Include="TestData\mqv2048.dat" />
    <None Include="TestData\nr1024.dat" />
    <None Include="TestData\nr2048.dat" />
    <None Include="TestData\rabi1024.dat" />
    <None Include="TestData\rabi2048.dat" />
    <None Include="TestData\rc2val.dat" />
    <None Include="TestData\rc5val.dat" />
    <None Include="TestData\rc6val.dat" />
    <None Include="TestData\rijndael.dat" />
    <None Include="TestData\rsa1024.dat" />
    <None Include="TestData\rsa2048.dat" />
    <None Include="TestData\rsa400pb.dat" />
    <None Include="TestData\rsa400pv.dat" />
    <None Include="TestData\rsa512a.dat" />
    <None Include="TestData\rw1024.dat" />
    <None Include="TestData\rw2048.dat" />
    <None Include="TestData\saferval.dat" />
    <None Include="TestData\serpentv.dat" />
    <None Include="TestData\shacal2v.dat" />
    <None Include="TestData\sharkval.dat" />
    <None Include="TestData\skipjack.dat" />
    <None Include="TestData\squareva.dat" />
    <None Include="TestData\twofishv.dat" />
    <None Include="TestData\usage.dat" />
    <None Include="TestData\xtrdh171.dat" />
    <None Include="TestData\xtrdh342.dat" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>