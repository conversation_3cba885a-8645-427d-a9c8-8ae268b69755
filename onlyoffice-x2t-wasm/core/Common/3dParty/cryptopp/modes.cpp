// modes.cpp - originally written and placed in the public domain by <PERSON>

#include "pch.h"

#ifndef CRYPTOPP_IMPORTS

#include "modes.h"
#include "misc.h"

#if defined(CRYPTOPP_DEBUG)
#include "des.h"
#endif

NAMESPACE_BEGIN(CryptoPP)

#if defined(CRYPTOPP_DEBUG) && !defined(CRYPTOPP_DOXYGEN_PROCESSING)
void Modes_TestInstantiations()
{
	CFB_Mode<DES>::Encryption m0;
	CFB_Mode<DES>::Decryption m1;
	OFB_Mode<DES>::Encryption m2;
	CTR_Mode<DES>::Encryption m3;
	ECB_Mode<DES>::Encryption m4;
	CBC_Mode<DES>::Encryption m5;
}
#endif

void CipherModeBase::ResizeBuffers()
{
	m_register.New(m_cipher->BlockSize());
}

void CFB_ModePolicy::Iterate(byte *output, const byte *input, CipherDir dir, size_t iterationCount)
{
	CRYPTOPP_ASSERT(input);
	CRYPTOPP_ASSERT(output);
	CRYPTOPP_ASSERT(m_cipher->IsForwardTransformation());	// CFB mode needs the "encrypt" direction of the underlying block cipher, even to decrypt
	CRYPTOPP_ASSERT(m_feedbackSize == BlockSize());

	const unsigned int s = BlockSize();
	if (dir == ENCRYPTION)
	{
		m_cipher->ProcessAndXorBlock(m_register, input, output);
		if (iterationCount > 1)
			m_cipher->AdvancedProcessBlocks(output, input+s, output+s, (iterationCount-1)*s, 0);
		memcpy(m_register, output+(iterationCount-1)*s, s);
	}
	else
	{
		memcpy(m_temp, input+(iterationCount-1)*s, s);	// make copy first in case of in-place decryption
		if (iterationCount > 1)
			m_cipher->AdvancedProcessBlocks(input, input+s, output+s, (iterationCount-1)*s, BlockTransformation::BT_ReverseDirection);
		m_cipher->ProcessAndXorBlock(m_register, input, output);
		memcpy(m_register, m_temp, s);
	}
}

void CFB_ModePolicy::TransformRegister()
{
	CRYPTOPP_ASSERT(m_cipher->IsForwardTransformation());	// CFB mode needs the "encrypt" direction of the underlying block cipher, even to decrypt
	m_cipher->ProcessBlock(m_register, m_temp);
	unsigned int updateSize = BlockSize()-m_feedbackSize;
	memmove_s(m_register, m_register.size(), m_register+m_feedbackSize, updateSize);
	memcpy_s(m_register+updateSize, m_register.size()-updateSize, m_temp, m_feedbackSize);
}

void CFB_ModePolicy::CipherResynchronize(const byte *iv, size_t length)
{
	CRYPTOPP_ASSERT(length == BlockSize());
	CopyOrZero(m_register, m_register.size(), iv, length);
	TransformRegister();
}

void CFB_ModePolicy::SetFeedbackSize(unsigned int feedbackSize)
{
	if (feedbackSize > BlockSize())
		throw InvalidArgument("CFB_Mode: invalid feedback size");
	m_feedbackSize = feedbackSize ? feedbackSize : BlockSize();
}

void CFB_ModePolicy::ResizeBuffers()
{
	CipherModeBase::ResizeBuffers();
	m_temp.New(BlockSize());
}

void OFB_ModePolicy::WriteKeystream(byte *keystreamBuffer, size_t iterationCount)
{
	CRYPTOPP_ASSERT(m_cipher->IsForwardTransformation());	// OFB mode needs the "encrypt" direction of the underlying block cipher, even to decrypt
	unsigned int s = BlockSize();
	m_cipher->ProcessBlock(m_register, keystreamBuffer);
	if (iterationCount > 1)
		m_cipher->AdvancedProcessBlocks(keystreamBuffer, NULLPTR, keystreamBuffer+s, s*(iterationCount-1), 0);
	memcpy(m_register, keystreamBuffer+s*(iterationCount-1), s);
}

void OFB_ModePolicy::CipherResynchronize(byte *keystreamBuffer, const byte *iv, size_t length)
{
	CRYPTOPP_UNUSED(keystreamBuffer), CRYPTOPP_UNUSED(length);
	CRYPTOPP_ASSERT(length == BlockSize());

	CopyOrZero(m_register, m_register.size(), iv, length);
}

void CTR_ModePolicy::SeekToIteration(lword iterationCount)
{
	int carry=0;
	for (int i=BlockSize()-1; i>=0; i--)
	{
		unsigned int sum = m_register[i] + byte(iterationCount) + carry;
		m_counterArray[i] = (byte) sum;
		carry = sum >> 8;
		iterationCount >>= 8;
	}
}

void CTR_ModePolicy::IncrementCounterBy256()
{
	IncrementCounterByOne(m_counterArray, BlockSize()-1);
}

void CTR_ModePolicy::OperateKeystream(KeystreamOperation /*operation*/, byte *output, const byte *input, size_t iterationCount)
{
	CRYPTOPP_ASSERT(m_cipher->IsForwardTransformation());	// CTR mode needs the "encrypt" direction of the underlying block cipher, even to decrypt
	unsigned int s = BlockSize();
	unsigned int inputIncrement = input ? s : 0;

	while (iterationCount)
	{
		byte lsb = m_counterArray[s-1];
		size_t blocks = UnsignedMin(iterationCount, 256U-lsb);
		m_cipher->AdvancedProcessBlocks(m_counterArray, input, output, blocks*s, BlockTransformation::BT_InBlockIsCounter|BlockTransformation::BT_AllowParallel);
		if ((m_counterArray[s-1] = lsb + (byte)blocks) == 0)
			IncrementCounterBy256();

		output += blocks*s;
		input += blocks*inputIncrement;
		iterationCount -= blocks;
	}
}

void CTR_ModePolicy::CipherResynchronize(byte *keystreamBuffer, const byte *iv, size_t length)
{
	CRYPTOPP_UNUSED(keystreamBuffer), CRYPTOPP_UNUSED(length);
	CRYPTOPP_ASSERT(length == BlockSize());

	CopyOrZero(m_register, m_register.size(), iv, length);
	m_counterArray = m_register;
}

void BlockOrientedCipherModeBase::UncheckedSetKey(const byte *key, unsigned int length, const NameValuePairs &params)
{
	m_cipher->SetKey(key, length, params);
	ResizeBuffers();
	if (IsResynchronizable())
	{
		size_t ivLength;
		const byte *iv = GetIVAndThrowIfInvalid(params, ivLength);
		Resynchronize(iv, (int)ivLength);
	}
}

void BlockOrientedCipherModeBase::ResizeBuffers()
{
	CipherModeBase::ResizeBuffers();
	m_buffer.New(BlockSize());
}

void ECB_OneWay::ProcessData(byte *outString, const byte *inString, size_t length)
{
	CRYPTOPP_ASSERT(length%BlockSize()==0);
	m_cipher->AdvancedProcessBlocks(inString, NULLPTR, outString, length, BlockTransformation::BT_AllowParallel);
}

void CBC_Encryption::ProcessData(byte *outString, const byte *inString, size_t length)
{
	if (!length) return;
	CRYPTOPP_ASSERT(length%BlockSize()==0);

	const unsigned int blockSize = BlockSize();
	m_cipher->AdvancedProcessBlocks(inString, m_register, outString, blockSize, BlockTransformation::BT_XorInput);
	if (length > blockSize)
		m_cipher->AdvancedProcessBlocks(inString+blockSize, outString, outString+blockSize, length-blockSize, BlockTransformation::BT_XorInput);
	memcpy(m_register, outString + length - blockSize, blockSize);
}

size_t CBC_CTS_Encryption::ProcessLastBlock(byte *outString, size_t outLength, const byte *inString, size_t inLength)
{
	CRYPTOPP_UNUSED(outLength);
	size_t used = inLength;
	if (inLength <= BlockSize())
	{
		if (!m_stolenIV)
			throw InvalidArgument("CBC_Encryption: message is too short for ciphertext stealing");

		// steal from IV
		memcpy(outString, m_register, inLength);
		outString = m_stolenIV;
	}
	else
	{
		// steal from next to last block
		xorbuf(m_register, inString, BlockSize());
		m_cipher->ProcessBlock(m_register);
		inString += BlockSize();
		inLength -= BlockSize();
		memcpy(outString+BlockSize(), m_register, inLength);
	}

	// output last full ciphertext block
	xorbuf(m_register, inString, inLength);
	m_cipher->ProcessBlock(m_register);
	memcpy(outString, m_register, BlockSize());

	return used;
}

void CBC_Decryption::ResizeBuffers()
{
	BlockOrientedCipherModeBase::ResizeBuffers();
	m_temp.New(BlockSize());
}

void CBC_Decryption::ProcessData(byte *outString, const byte *inString, size_t length)
{
	if (!length)
		return;
	CRYPTOPP_ASSERT(length%BlockSize()==0);

	const unsigned int blockSize = BlockSize();
	memcpy(m_temp, inString+length-blockSize, blockSize);	// save copy now in case of in-place decryption
	if (length > blockSize)
		m_cipher->AdvancedProcessBlocks(inString+blockSize, inString, outString+blockSize, length-blockSize, BlockTransformation::BT_ReverseDirection|BlockTransformation::BT_AllowParallel);
	m_cipher->ProcessAndXorBlock(inString, m_register, outString);
	m_register.swap(m_temp);
}

size_t CBC_CTS_Decryption::ProcessLastBlock(byte *outString, size_t outLength, const byte *inString, size_t inLength)
{
	CRYPTOPP_UNUSED(outLength);
	const byte *pn1, *pn2;
	bool stealIV = inLength <= BlockSize();
	size_t used = inLength;

	if (stealIV)
	{
		pn1 = inString;
		pn2 = m_register;
	}
	else
	{
		pn1 = inString + BlockSize();
		pn2 = inString;
		inLength -= BlockSize();
	}

	// decrypt last partial plaintext block
	memcpy(m_temp, pn2, BlockSize());
	m_cipher->ProcessBlock(m_temp);
	xorbuf(m_temp, pn1, inLength);

	if (stealIV)
	{
		memcpy(outString, m_temp, inLength);
	}
	else
	{
		memcpy(outString+BlockSize(), m_temp, inLength);
		// decrypt next to last plaintext block
		memcpy(m_temp, pn1, inLength);
		m_cipher->ProcessBlock(m_temp);
		xorbuf(outString, m_temp, m_register, BlockSize());
	}

	return used;
}

NAMESPACE_END

#endif
