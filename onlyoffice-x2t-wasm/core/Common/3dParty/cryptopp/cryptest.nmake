# cryptest.nmake - written and placed in public domain by <PERSON>.
#                  Copyright assigned to the Crypto++ project.

# This makefile is used for testing and building cryptlib.lib and cryptest.exe under nmake. Open a
#   Visual Studio Developer Prompt and then run "nmake /f cryptest.nmake". The build procedure will
#   reveal not-so-readily-apparent problems under Microsoft ARM and Metro UI apps.

# The makefile is not intended for production use, though it may be used as a starting point.
#   For example, you can add switches like /MT and /MTd for dynamic runtime linking against
#   the Microsoft C++ Runtime libraries. If you are building for Windows Phone or Windows Store, then
#   you probably want to remove /D_MBCS. The resulting cryptlib.lib may be suitable as a starting
#   point for a DLL project using Crypto++.

# You must also add /DCRYPTOPP_DEBUG or /DDEBUG if you want a debug build with the library's assert.
#   The library moved from Posix NDEBUG and assert() to CRYPTOPP_ASSERT at 5.6.5 due to CVE-2016-7420.
#   CRYPTOPP_ASSERT has the additional benefit of using DebugBreak(), and the  program does not crash
#   while you are debugging it like would happen with Posix assert().

# The list of LIB_SRCS and TEST_SRCS was generated under Linux with "make sources". The list of
#   LIB_OBJS and TEST_OBJS was generated under Linux with "make sources | sed 's|.cpp|.obj|g'".
#   The order of the firt three object files are significant. See C++ Static Initialization Order
#   Fisaco on the Crypto++ wiki for details.

# You are free to add and remove files to the list. For example, you can remove rdrand.asm
#   build it using NASM, and then include the NASM object file rdrand_x86.obj or rdrand_x64.obj.

###########################################################################################

# To test debug builds, use the following CXXFLAGS:
#   - /DDEBUG /D_DEBUG /Oi /Oy- /Od
# To test release builds, use the following CXXFLAGS:
#   - /DNDEBUG /D_NDEBUG /Oi /Oy /O2
# To test with static C++ runtime linking, use the following CXXFLAGS (default below):
#   - /MT (release) or /MTd (debug)
# To test with dynamic C++ runtime linking, use the following CXXFLAGS:
#   - /MD (release) or /MDd (debug)
# To test Desktop app, use the following CXXFLAGS:
#   - /DWINAPI_FAMILY=WINAPI_FAMILY_DESKTOP_APP
# To test Windows Store app, use the following CXXFLAGS:
#   - /DWINAPI_FAMILY=WINAPI_FAMILY_APP
# To test Windows Phone, use the following CXXFLAGS:
#   - /DWINAPI_FAMILY=WINAPI_FAMILY_PHONE_APP
# To test Surface RT (ARM tablet), use the following CXXFLAGS:
#   - /D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE=1 /DWINAPI_FAMILY=WINAPI_FAMILY_DESKTOP_APP

###########################################################################################

# If you use 'make sources' from Linux makefile, then add 'winpipes.cpp' to the list below.

LIB_SRCS = cryptlib.cpp cpu.cpp integer.cpp 3way.cpp adler32.cpp algebra.cpp algparam.cpp arc4.cpp aria-simd.cpp aria.cpp ariatab.cpp asn.cpp authenc.cpp base32.cpp base64.cpp basecode.cpp bfinit.cpp blake2-simd.cpp blake2.cpp blowfish.cpp blumshub.cpp camellia.cpp cast.cpp casts.cpp cbcmac.cpp ccm.cpp chacha.cpp channels.cpp cmac.cpp crc-simd.cpp crc.cpp default.cpp des.cpp dessp.cpp dh.cpp dh2.cpp dll.cpp dsa.cpp eax.cpp ec2n.cpp eccrypto.cpp ecp.cpp elgamal.cpp emsa2.cpp eprecomp.cpp esign.cpp files.cpp filters.cpp fips140.cpp fipstest.cpp gcm-simd.cpp gcm.cpp gf256.cpp gf2_32.cpp gf2n.cpp gfpcrypt.cpp gost.cpp gzip.cpp hex.cpp hmac.cpp hrtimer.cpp ida.cpp idea.cpp iterhash.cpp kalyna.cpp kalynatab.cpp keccak.cpp luc.cpp mars.cpp marss.cpp md2.cpp md4.cpp md5.cpp misc.cpp modes.cpp mqueue.cpp mqv.cpp nbtheory.cpp neon-simd.cpp network.cpp oaep.cpp osrng.cpp padlkrng.cpp panama.cpp pkcspad.cpp poly1305.cpp polynomi.cpp pssr.cpp pubkey.cpp queue.cpp rabin.cpp randpool.cpp rc2.cpp rc5.cpp rc6.cpp rdrand.cpp rdtables.cpp rijndael-simd.cpp rijndael.cpp ripemd.cpp rng.cpp rsa.cpp rw.cpp safer.cpp salsa.cpp scrypt.cpp seal.cpp seed.cpp serpent.cpp sha-simd.cpp sha.cpp sha3.cpp shacal2-simd.cpp shacal2.cpp shark.cpp sharkbox.cpp simon.cpp simon-simd.cpp skipjack.cpp sm3.cpp sm4.cpp socketft.cpp sosemanuk.cpp speck.cpp speck-simd.cpp square.cpp squaretb.cpp sse-simd.cpp strciphr.cpp tea.cpp tftables.cpp threefish.cpp tiger.cpp tigertab.cpp trdlocal.cpp ttmac.cpp tweetnacl.cpp twofish.cpp vmac.cpp wait.cpp wake.cpp whrlpool.cpp winpipes.cpp xtr.cpp xtrcrypt.cpp zdeflate.cpp zinflate.cpp zlib.cpp

LIB_OBJS = cryptlib.obj cpu.obj integer.obj 3way.obj adler32.obj algebra.obj algparam.obj arc4.obj aria-simd.obj aria.obj ariatab.obj asn.obj authenc.obj base32.obj base64.obj basecode.obj bfinit.obj blake2-simd.obj blake2.obj blowfish.obj blumshub.obj camellia.obj cast.obj casts.obj cbcmac.obj ccm.obj chacha.obj channels.obj cmac.obj crc-simd.obj crc.obj default.obj des.obj dessp.obj dh.obj dh2.obj dll.obj dsa.obj eax.obj ec2n.obj eccrypto.obj ecp.obj elgamal.obj emsa2.obj eprecomp.obj esign.obj files.obj filters.obj fips140.obj fipstest.obj gcm-simd.obj gcm.obj gf256.obj gf2_32.obj gf2n.obj gfpcrypt.obj gost.obj gzip.obj hex.obj hmac.obj hrtimer.obj ida.obj idea.obj iterhash.obj kalyna.obj kalynatab.obj keccak.obj luc.obj mars.obj marss.obj md2.obj md4.obj md5.obj misc.obj modes.obj mqueue.obj mqv.obj nbtheory.obj neon-simd.obj network.obj oaep.obj osrng.obj padlkrng.obj panama.obj pkcspad.obj poly1305.obj polynomi.obj pssr.obj pubkey.obj queue.obj rabin.obj randpool.obj rc2.obj rc5.obj rc6.obj rdrand.obj rdtables.obj rijndael-simd.obj rijndael.obj ripemd.obj rng.obj rsa.obj rw.obj safer.obj salsa.obj scrypt.obj seal.obj seed.obj serpent.obj sha-simd.obj sha.obj sha3.obj shacal2-simd.obj shacal2.obj shark.obj sharkbox.obj simon.obj simon-simd.obj skipjack.obj sm3.obj sm4.obj socketft.obj sosemanuk.obj speck.obj speck-simd.obj square.obj squaretb.obj sse-simd.obj strciphr.obj tea.obj tftables.obj threefish.obj tiger.obj tigertab.obj trdlocal.obj ttmac.obj tweetnacl.obj twofish.obj vmac.obj wait.obj wake.obj whrlpool.obj winpipes.obj xtr.obj xtrcrypt.obj zdeflate.obj zinflate.obj zlib.obj

TEST_SRCS = bench1.cpp bench2.cpp test.cpp validat0.cpp validat1.cpp validat2.cpp validat3.cpp validat4.cpp datatest.cpp regtest1.cpp regtest2.cpp regtest3.cpp fipsalgt.cpp dlltest.cpp fipstest.cpp

TEST_OBJS = bench1.obj bench2.obj test.obj validat0.obj validat1.obj validat2.obj validat3.obj validat4.obj datatest.obj regtest1.obj regtest2.obj regtest3.obj fipsalgt.obj dlltest.obj fipstest.obj

CXX = cl.exe
LD = link.exe
AR = lib.exe
RM = del.exe

# C4231 is needed for VS2008 and below. Lots of noise...
CXXFLAGS = /nologo /W4 /wd4231 /wd4511 /wd4156 /D_MBCS /Zi /TP /GR /EHsc
LDFLAGS = /nologo /SUBSYSTEM:CONSOLE
ARFLAGS = /nologo
LDLIBS =

# Debug build
# CXXFLAGS = $(CXXFLAGS) /DDEBUG /D_DEBUG /Oi /Oy- /Od /MTd
# Release build
CXXFLAGS = $(CXXFLAGS) /DNDEBUG /D_NDEBUG /Oi /Oy /O2 /MT

# Attempt to detect when <sdkddkver.h> and <winapifamily.h> are available
#   http://stackoverflow.com/q/40577415 ?
!IF "$(WINDOWSSDKDIR)" != "" || "$(WINDOWSSDKLIBVERSION)" != ""
CXXFLAGS = $(CXXFLAGS) /FI sdkddkver.h
!ENDIF
!IF "$(WINDOWSPHONEKITDIR)" != "" || "$(UNIVERSALCRTSDKDIR)" != "" || "$(UCRTVERSION)" != ""
CXXFLAGS = $(CXXFLAGS) /FI winapifamily.h
!ELSEIF "$(PLATFORM)" == "ARM" || "$(PLATFORM)" == "arm" || "$(PLATFORM)" == "ARM64" || "$(PLATFORM)" == "arm64"
CXXFLAGS = $(CXXFLAGS) /FI winapifamily.h
!ENDIF

# Check for empty Platform and Processor
!IF "$(PLATFORM)" == ""
!IF "$(PROCESSOR_ARCHITECTURE)" == "x86"
PLATFORM = x86
!ELSEIF "$(PROCESSOR_ARCHITECTURE)" == "x64" || "$(PROCESSOR_ARCHITECTURE)" == "AMD64"
PLATFORM = x64
!ELSE
!ERROR "Unknown platform"
!ENDIF
!ENDIF

!MESSAGE
!MESSAGE ******************************
!MESSAGE Platform is $(PLATFORM)
!MESSAGE ******************************
!MESSAGE

!IF "$(PLATFORM)" == "x86" || "$(PLATFORM)" == "X86"
# CXXFLAGS = $(CXXFLAGS) /arch:SSE2
# CXXFLAGS = $(CXXFLAGS) /DWINAPI_FAMILY=WINAPI_FAMILY_DESKTOP_APP
# CXXFLAGS = $(CXXFLAGS) /DWINAPI_FAMILY=WINAPI_FAMILY_APP
AS = ml.exe
ASFLAGS = /nologo /D_M_X86 /W3 /Cx /Zi /safeseh
LIB_SRCS = $(LIB_SRCS) rdrand.cpp rdrand.asm
LIB_OBJS = $(LIB_OBJS) rdrand.obj rdrand-x86.obj
LDFLAGS = $(LDFLAGS) /MACHINE:X86
LDLIBS = $(LDLIBS) ws2_32.lib kernel32.lib
!ENDIF

# May need $(VCINSTALLDIR)\bin\amd64\ml64.exe
!IF "$(PLATFORM)" == "x64" || "$(PLATFORM)" == "X64" || "$(PLATFORM)" == "amd64"
# CXXFLAGS = $(CXXFLAGS) /arch:AVX2
# CXXFLAGS = $(CXXFLAGS) /DWINAPI_FAMILY=WINAPI_FAMILY_DESKTOP_APP
# CXXFLAGS = $(CXXFLAGS) /DWINAPI_FAMILY=WINAPI_FAMILY_APP
AS = ml64.exe
ASFLAGS = /nologo /D_M_X64 /W3 /Cx /Zi
LIB_SRCS = $(LIB_SRCS) rdrand.cpp rdrand.asm
LIB_OBJS = $(LIB_OBJS) rdrand.obj rdrand-x64.obj x64masm.obj x64dll.obj
LDFLAGS = $(LDFLAGS) /MACHINE:X64
LDLIBS = $(LDLIBS) ws2_32.lib kernel32.lib
!ENDIF

# We still don't know what we need for ARM64 on Windows. ARM64 and arm64 may be incorrect
!IF "$(PLATFORM)" == "ARM" || "$(PLATFORM)" == "arm" || "$(PLATFORM)" == "ARM64" || "$(PLATFORM)" == "arm64"
# CXXFLAGS = $(CXXFLAGS) /D_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE=1 /DWINAPI_FAMILY=WINAPI_FAMILY_DESKTOP_APP
CXXFLAGS = $(CXXFLAGS) /DWINAPI_FAMILY=WINAPI_FAMILY_PHONE_APP
LIB_SRCS = $(LIB_SRCS) neon-simd.cpp
LIB_OBJS = $(LIB_OBJS) neon-simd.obj
# CXXFLAGS = $(CXXFLAGS) /DWINAPI_FAMILY=WINAPI_FAMILY_APP
# LDLIBS = $(LDLIBS) ws2_32.lib
!ENDIF

all: cryptest.exe

cryptest.exe: pch.pch cryptlib.lib $(TEST_OBJS)
	$(LD) $(LDFLAGS) $(TEST_OBJS) cryptlib.lib $(LDLIBS) /out:$@

cryptlib.lib: $(LIB_OBJS)
	$(AR) $(ARFLAGS) $(LIB_OBJS) /out:$@

clean:
	$(RM) /F /Q pch.pch $(LIB_OBJS) pch.obj rdrand-x86.obj rdrand-x64.obj x64masm.obj x64dll.obj cryptlib.lib $(TEST_OBJS) cryptest.exe *.pdb

# Precompiled header
pch.pch: pch.h pch.cpp
	$(CXX) $(CXXFLAGS) /Yc"pch.h" /Fp"pch.pch" /c pch.cpp

# No precompiled headers
iterhash.obj:
	$(CXX) $(CXXFLAGS) /Y- /c iterhash.cpp
dll.obj:
	$(CXX) $(CXXFLAGS) /Y- /c dll.cpp
rdrand.obj:
	$(CXX) $(CXXFLAGS) /c rdrand.cpp

# Built for x86/x64
rdrand-x86.obj:
	$(AS) $(ASFLAGS) /Fo rdrand-x86.obj /c rdrand.asm
rdrand-x64.obj:
	$(AS) $(ASFLAGS) /Fo rdrand-x64.obj /c rdrand.asm
x64masm.obj:
	$(AS) $(ASFLAGS) /Fo x64masm.obj /c x64masm.asm
x64dll.obj:
	$(AS) $(ASFLAGS) /Fo x64dll.obj /c x64dll.asm

.cpp.obj:
	$(CXX) $(CXXFLAGS) /c $<

.asm.obj:
	$(AS) $(ASFLAGS) /c $<
