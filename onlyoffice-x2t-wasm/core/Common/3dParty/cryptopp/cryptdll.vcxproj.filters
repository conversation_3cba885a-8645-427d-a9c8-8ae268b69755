<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{82666edd-7baf-4a5a-922c-a06edc2198bd}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{2029b271-c489-4b4c-9ce5-261b4cfe2d78}</UniqueIdentifier>
      <Extensions>.h</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="algebra.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="algparam.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="asn.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="authenc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="basecode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cbcmac.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ccm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="channels.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cmac.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cpu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cryptlib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="des.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="dessp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="dh.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="dll.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="dsa.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ec2n.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="eccrypto.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ecp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="emsa2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="eprecomp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="files.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="filters.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fips140.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fipstest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gcm.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gcm-simd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gf2n.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gfpcrypt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hex.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hmac.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hrtimer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="integer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="iterhash.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="misc.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="modes.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mqueue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="nbtheory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="oaep.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="osrng.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pkcspad.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pssr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pubkey.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="queue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="randpool.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rdtables.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rijndael.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rijndael-simd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rng.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rsa.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rw.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sha.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sha-simd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="simple.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="skipjack.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sse-simd.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="strciphr.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="trdlocal.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="aes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="algebra.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="algparam.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="argnames.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="asn.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="authenc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="basecode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cbcmac.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ccm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="channels.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cmac.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cpu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cryptlib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="des.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="dh.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="dll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="dsa.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ec2n.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="eccrypto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ecp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ecpoint.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="emsa2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="eprecomp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="files.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="filters.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fips140.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fltrimpl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="gcm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="gf2n.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="gfpcrypt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hex.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hmac.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="integer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="iterhash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mdc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="misc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="modarith.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="modes.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="modexppc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mqueue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mqv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="nbtheory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="oaep.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="oids.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="osrng.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pch.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pkcspad.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pssr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pubkey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="queue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="randpool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="rijndael.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="rng.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="rsa.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="rw.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="secblock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="seckey.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="sha.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="simple.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="skipjack.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="smartptr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="stdcpp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="strciphr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="trap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="trdlocal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="words.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="cryptopp.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="x64dll.asm">
      <Filter>Source Files</Filter>
    </CustomBuild>
  </ItemGroup>
</Project>
