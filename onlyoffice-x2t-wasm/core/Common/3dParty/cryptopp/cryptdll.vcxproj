<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Microsoft documentation for VCXPROJ file format is located at -->
  <!-- the following URL. The documentation leaves a lot to be desired. -->
  <!-- https://msdn.microsoft.com/en-us/library/2208a1f2.aspx -->
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <!-- Microsoft documentation clearly shows the Global property group -->
  <!-- preceeds the import of Cpp.Default.props and Cpp.props -->
  <!-- https://msdn.microsoft.com/en-us/library/2208a1f2.aspx -->
  <PropertyGroup Label="Globals">
    <ProjectGuid>{94a428a1-9ba8-4db2-b76e-bd2e3c08f257}</ProjectGuid>
    <RootNamespace>cryptdll</RootNamespace>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
  </PropertyGroup>
  <!-- Use DefaultPlatformToolset after Microsoft.Cpp.Default.props -->
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <!-- Set DefaultPlatformToolset to v100 (VS2010) if not defined -->
  <PropertyGroup Label="EmptyDefaultPlatformToolset">
    <DefaultPlatformToolset Condition=" '$(DefaultPlatformToolset)' == '' ">v100</DefaultPlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="PlatformToolset">
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <!-- End of Visual Studio boilerplate -->
  <!-- All Configurations -->
  <PropertyGroup Label="All Configurations">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <TargetName>cryptopp</TargetName>
    <TargetExt>.dll</TargetExt>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <OutDir>$(Platform)\DLL_Output\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <!-- All Configurations -->
  <ItemDefinitionGroup Label="All Configurations">
    <ClCompile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <ErrorReporting>None</ErrorReporting>
      <WarningLevel>Level4</WarningLevel>
      <DisableSpecificWarnings>4231; 4251; 4275; 4355; 4505</DisableSpecificWarnings>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
    </ClCompile>
    <Link>
      <ErrorReporting>NoErrorReport</ErrorReporting>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ProgramDatabaseFile>$(OutDir)\cryptopp.pdb</ProgramDatabaseFile>
      <BaseAddress>0x42900000</BaseAddress>
      <RandomizedBaseAddress>false</RandomizedBaseAddress>
      <PreventDllBinding>true</PreventDllBinding>
      <OutputFile>$(OutDir)\cryptopp.dll</OutputFile>
      <ImportLibrary>$(TargetDir)\cryptopp.lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <!-- Debug Configurations -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'" Label="Debug Configuration">
    <ClCompile>
      <PreprocessorDefinitions>CRYPTOPP_EXPORTS;CRYPTOPP_ENABLE_COMPLIANCE_WITH_FIPS_140_2=1;USE_PRECOMPILED_HEADERS</PreprocessorDefinitions>
      <Optimization>Disabled</Optimization>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <!-- Release Configurations -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'" Label="Release Configuration">
    <ClCompile>
      <PreprocessorDefinitions>NDEBUG;CRYPTOPP_EXPORTS;CRYPTOPP_ENABLE_COMPLIANCE_WITH_FIPS_140_2=1;USE_PRECOMPILED_HEADERS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
    </Link>
  </ItemDefinitionGroup>
  <!-- X86 Configurations -->
  <ItemDefinitionGroup Condition="'$(Platform)'=='Win32'" Label="X86 Configuration">
    <ClCompile>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>true</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <!-- X64 Configurations -->
  <ItemDefinitionGroup Condition="'$(Platform)'=='x64'" Label="X64 Configuration">
    <Link>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <!-- Win32/Debug cryptest.exe for DLL MAC'ing -->
  <!-- Broken at the moment; see http://stackoverflow.com/q/39900437 -->
  <!--   and http://stackoverflow.com/q/39929817/608639              -->
  <!--
  <Target Condition="!Exists('Win32\Output\Debug\cryptest.exe')" Name="MAC tool" Label="MAC tool">
    <Message
	  Text="Creating Win32/Debug cryptest.exe for MAC computation" />
    <MSbuild
      Projects="cryptlib.vcxproj"
      Properties="Configuration=Debug;Platform=Win32;"/>
    <MSbuild
      Projects="cryptest.vcxproj"
      Properties="Configuration=Debug;Platform=Win32;"/>
  </Target>
  -->
  <ItemDefinitionGroup Condition="!Exists('Win32\Output\Debug\cryptest.exe')" Label="MAC tool">
    <PreBuildEvent>
      <Message>Creating Win32/Debug cryptest.exe for MAC computation</Message>
      <Command>
        msbuild /t:Build /p:Configuration=Debug;Platform=Win32 cryptlib.vcxproj
        msbuild /t:Build /p:Configuration=Debug;Platform=Win32 cryptest.vcxproj
      </Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <!-- DLL MAC'ing performed by cryptest.exe -->
  <ItemDefinitionGroup Label="DLL MAC">
    <PostBuildEvent>
      <Message>Adding MAC to DLL</Message>
      <Command>
        Win32\output\debug\cryptest.exe mac_dll "$(TargetPath)"
        IF %ERRORLEVEL% EQU 0 (echo mac done &gt; "$(OutDir)"\cryptopp.mac.done)
      </Command>
      <Inputs>%(Inputs)</Inputs>
      <Outputs>$(OutDir)cryptopp.mac.done;%(Outputs)</Outputs>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <!-- Original File with special treatment -->
  <ItemGroup>
    <CustomBuild Condition="'$(Platform)'=='x64' AND ('$(Configuration)'=='Debug' Or '$(Configuration)'=='Release')" Include="x64dll.asm">
      <Message>Building and assembling x64dll.asm</Message>
      <Command>ml64.exe /c /nologo /D_M_X64 /W3 /Zi /Fo"$(IntDir)x64dll.obj" "%(FullPath)"</Command>
      <Outputs>$(IntDir)x64dll.obj;%(Outputs)</Outputs>
    </CustomBuild>
  </ItemGroup>
  <!-- Source Files -->
  <ItemGroup>
    <ClCompile Include="pch.cpp">
      <PrecompiledHeader>Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="dll.cpp">
      <PrecompiledHeader />
    </ClCompile>
    <ClCompile Include="iterhash.cpp">
      <PrecompiledHeader />
    </ClCompile>
    <ClCompile Include="algebra.cpp" />
    <ClCompile Include="algparam.cpp" />
    <ClCompile Include="asn.cpp" />
    <ClCompile Include="authenc.cpp" />
    <ClCompile Include="basecode.cpp" />
    <ClCompile Include="cbcmac.cpp" />
    <ClCompile Include="ccm.cpp" />
    <ClCompile Include="channels.cpp" />
    <ClCompile Include="cmac.cpp" />
    <ClCompile Include="cpu.cpp" />
    <ClCompile Include="cryptlib.cpp" />
    <ClCompile Include="des.cpp" />
    <ClCompile Include="dessp.cpp" />
    <ClCompile Include="dh.cpp" />
    <ClCompile Include="dsa.cpp" />
    <ClCompile Include="ec2n.cpp" />
    <ClCompile Include="eccrypto.cpp" />
    <ClCompile Include="ecp.cpp" />
    <ClCompile Include="emsa2.cpp" />
    <ClCompile Include="eprecomp.cpp" />
    <ClCompile Include="files.cpp" />
    <ClCompile Include="filters.cpp" />
    <ClCompile Include="fips140.cpp" />
    <ClCompile Include="fipstest.cpp" />
    <ClCompile Include="gcm.cpp" />
    <ClCompile Include="gcm-simd.cpp" />
    <ClCompile Include="gf2n.cpp" />
    <ClCompile Include="gfpcrypt.cpp" />
    <ClCompile Include="hex.cpp" />
    <ClCompile Include="hmac.cpp" />
    <ClCompile Include="hrtimer.cpp" />
    <ClCompile Include="integer.cpp" />
    <ClCompile Include="misc.cpp" />
    <ClCompile Include="modes.cpp" />
    <ClCompile Include="mqueue.cpp" />
    <ClCompile Include="nbtheory.cpp" />
    <ClCompile Include="oaep.cpp" />
    <ClCompile Include="osrng.cpp" />
    <ClCompile Include="pkcspad.cpp" />
    <ClCompile Include="pssr.cpp" />
    <ClCompile Include="pubkey.cpp" />
    <ClCompile Include="queue.cpp" />
    <ClCompile Include="randpool.cpp" />
    <ClCompile Include="rdtables.cpp" />
    <ClCompile Include="rijndael.cpp" />
    <ClCompile Include="rijndael-simd.cpp" />
    <ClCompile Include="rng.cpp" />
    <ClCompile Include="rsa.cpp" />
    <ClCompile Include="rw.cpp" />
    <ClCompile Include="sha.cpp" />
    <ClCompile Include="sha-simd.cpp" />
    <ClCompile Include="simple.cpp" />
    <ClCompile Include="skipjack.cpp" />
    <ClCompile Include="sse-simd.cpp" />
    <ClCompile Include="strciphr.cpp" />
    <ClCompile Include="trdlocal.cpp" />
  </ItemGroup>
  <!-- Header Files -->
  <ItemGroup>
    <ClInclude Include="aes.h" />
    <ClInclude Include="algebra.h" />
    <ClInclude Include="algparam.h" />
    <ClInclude Include="argnames.h" />
    <ClInclude Include="asn.h" />
    <ClInclude Include="authenc.h" />
    <ClInclude Include="basecode.h" />
    <ClInclude Include="cbcmac.h" />
    <ClInclude Include="ccm.h" />
    <ClInclude Include="channels.h" />
    <ClInclude Include="cmac.h" />
    <ClInclude Include="config.h" />
    <ClInclude Include="cpu.h" />
    <ClInclude Include="cryptlib.h" />
    <ClInclude Include="des.h" />
    <ClInclude Include="dh.h" />
    <ClInclude Include="dll.h" />
    <ClInclude Include="dsa.h" />
    <ClInclude Include="ec2n.h" />
    <ClInclude Include="eccrypto.h" />
    <ClInclude Include="ecp.h" />
    <ClInclude Include="ecpoint.h" />
    <ClInclude Include="emsa2.h" />
    <ClInclude Include="eprecomp.h" />
    <ClInclude Include="files.h" />
    <ClInclude Include="filters.h" />
    <ClInclude Include="fips140.h" />
    <ClInclude Include="fltrimpl.h" />
    <ClInclude Include="gcm.h" />
    <ClInclude Include="gf2n.h" />
    <ClInclude Include="gfpcrypt.h" />
    <ClInclude Include="hex.h" />
    <ClInclude Include="hmac.h" />
    <ClInclude Include="integer.h" />
    <ClInclude Include="iterhash.h" />
    <ClInclude Include="mdc.h" />
    <ClInclude Include="misc.h" />
    <ClInclude Include="modarith.h" />
    <ClInclude Include="modes.h" />
    <ClInclude Include="modexppc.h" />
    <ClInclude Include="mqueue.h" />
    <ClInclude Include="mqv.h" />
    <ClInclude Include="nbtheory.h" />
    <ClInclude Include="oaep.h" />
    <ClInclude Include="oids.h" />
    <ClInclude Include="osrng.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="pkcspad.h" />
    <ClInclude Include="pssr.h" />
    <ClInclude Include="pubkey.h" />
    <ClInclude Include="queue.h" />
    <ClInclude Include="randpool.h" />
    <ClInclude Include="rijndael.h" />
    <ClInclude Include="rng.h" />
    <ClInclude Include="rsa.h" />
    <ClInclude Include="rw.h" />
    <ClInclude Include="secblock.h" />
    <ClInclude Include="seckey.h" />
    <ClInclude Include="sha.h" />
    <ClInclude Include="simple.h" />
    <ClInclude Include="skipjack.h" />
    <ClInclude Include="smartptr.h" />
    <ClInclude Include="stdcpp.h" />
    <ClInclude Include="strciphr.h" />
    <ClInclude Include="trap.h" />
    <ClInclude Include="trdlocal.h" />
    <ClInclude Include="words.h" />
  </ItemGroup>
  <!-- Back to Visual Studio boilerplate -->
  <ItemGroup>
    <ResourceCompile Include="cryptopp.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets" />
  </ImportGroup>
</Project>