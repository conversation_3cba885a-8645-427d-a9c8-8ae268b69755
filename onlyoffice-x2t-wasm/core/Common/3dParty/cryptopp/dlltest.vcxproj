<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Microsoft documentation for VCXPROJ file format is located at -->
  <!-- the following URL. The documentation leaves a lot to be desired. -->
  <!-- https://msdn.microsoft.com/en-us/library/2208a1f2.aspx -->
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <!-- Microsoft documentation clearly shows the Global property group -->
  <!-- preceeds the import of Cpp.Default.props and Cpp.props -->
  <!-- https://msdn.microsoft.com/en-us/library/2208a1f2.aspx -->
  <PropertyGroup Label="Globals">
    <ProjectGuid>{1974a53a-9863-41c9-886d-b2b8c2fc3c8b}</ProjectGuid>
    <RootNamespace>dlltest</RootNamespace>
    <ConfigurationType>Application</ConfigurationType>
  </PropertyGroup>
  <!-- Use DefaultPlatformToolset after Microsoft.Cpp.Default.props -->
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <!-- Set DefaultPlatformToolset to v100 (VS2010) if not defined -->
  <PropertyGroup Label="EmptyDefaultPlatformToolset">
    <DefaultPlatformToolset Condition=" '$(DefaultPlatformToolset)' == '' ">v100</DefaultPlatformToolset>
  </PropertyGroup>
  <PropertyGroup Label="PlatformToolset">
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings" />
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <!-- End of Visual Studio boilerplate -->
  <!-- All Configurations -->
  <PropertyGroup Label="All Configurations">
    <ConfigurationType>Application</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <OutDir>$(Platform)\DLL_Output\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <!-- All Configurations -->
  <ItemDefinitionGroup Label="All Configurations">
    <ClCompile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <ErrorReporting>None</ErrorReporting>
      <WarningLevel>Level4</WarningLevel>
      <DisableSpecificWarnings>4231; 4251; 4275; 4355; 4505</DisableSpecificWarnings>
      <CallingConvention>StdCall</CallingConvention>
      <PrecompiledHeader />
    </ClCompile>
    <Link>
      <ErrorReporting>NoErrorReport</ErrorReporting>
      <AdditionalDependencies>cryptopp.lib;Ws2_32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(Platform)\DLL_Output\$(Configuration)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <!-- Debug Configurations -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'" Label="Debug Configuration">
    <ClCompile>
      <PreprocessorDefinitions>CRYPTOPP_DLL_ONLY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Optimization>Disabled</Optimization>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <!-- Release Configurations -->
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'" Label="Release Configuration">
    <ClCompile>
      <PreprocessorDefinitions>NDEBUG;CRYPTOPP_DLL_ONLY;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <StringPooling>true</StringPooling>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0409</Culture>
    </ResourceCompile>
    <Link>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
    </Link>
  </ItemDefinitionGroup>
  <!-- X86 Configurations -->
  <ItemDefinitionGroup Condition="'$(Platform)'=='Win32'" Label="X86 Configuration">
    <ClCompile>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>true</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <!-- X64 Configurations -->
  <ItemDefinitionGroup Condition="'$(Platform)'=='x64'" Label="X64 Configuration">
    <Link>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <!-- Source Files -->
  <ItemGroup>
    <ClCompile Include="dlltest.cpp" />
  </ItemGroup>
  <!-- Back to Visual Studio boilerplate -->
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>