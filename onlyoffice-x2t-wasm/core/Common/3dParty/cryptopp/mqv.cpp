// mqv.cpp - originally written and placed in the public domain by <PERSON>
//           HMQV provided by <PERSON>, <PERSON> and <PERSON><PERSON>.
//           FHMQV provided by <PERSON><PERSON>.

#include "pch.h"
#include "config.h"
#include "mqv.h"
#include "hmqv.h"
#include "fhmqv.h"

NAMESPACE_BEGIN(CryptoPP)

#if defined(CRYPTOPP_DEBUG) && !defined(CRYPTOPP_DOXYGEN_PROCESSING)
void TestInstantiations_MQV()
{
	MQV mqv;
}

void TestInstantiations_HMQV()
{
    HMQV hmqv;
}

void TestInstantiations_FHMQV()
{
    FHMQV fhmqv;
}
#endif

NAMESPACE_END
