<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8,00"
	Name="cryptlib"
	ProjectGUID="{3423EC9A-52E4-4A4D-9753-EDEBC38785EF}"
	RootNamespace="cryptlib"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
		<Platform
			Name="x64"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(PlatformName)\Output\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="2"
				EnableIntrinsicFunctions="true"
				OmitFramePointers="true"
				PreprocessorDefinitions="NDEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32"
				StringPooling="true"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="0"
				PrecompiledHeaderThrough=""
				ProgramDataBaseFileName="$(OutDir)\vc80.pdb"
				WarningLevel="4"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|x64"
			OutputDirectory="$(PlatformName)\Output\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="2"
				EnableIntrinsicFunctions="true"
				OmitFramePointers="true"
				PreprocessorDefinitions="NDEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="pch.h"
				ProgramDataBaseFileName="$(OutDir)\vc80.pdb"
				WarningLevel="4"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="DLL-Import Release|Win32"
			OutputDirectory="$(PlatformName)\DLL_Output\Release"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="2"
				EnableIntrinsicFunctions="true"
				OmitFramePointers="true"
				PreprocessorDefinitions="NDEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_IMPORTS"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="pch.h"
				ProgramDataBaseFileName="$(OutDir)\vc80.pdb"
				WarningLevel="4"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="DLL-Import Release|x64"
			OutputDirectory="$(PlatformName)\DLL_Output\Release"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="2"
				EnableIntrinsicFunctions="true"
				OmitFramePointers="true"
				PreprocessorDefinitions="NDEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_IMPORTS"
				StringPooling="true"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="true"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="pch.h"
				ProgramDataBaseFileName="$(OutDir)\vc80.pdb"
				WarningLevel="4"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="$(PlatformName)\Output\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				EnableIntrinsicFunctions="true"
				PreprocessorDefinitions="_DEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				EnableEnhancedInstructionSet="0"
				UsePrecompiledHeader="0"
				PrecompiledHeaderThrough="pch.h"
				ObjectFile="$(IntDir)\"
				ProgramDataBaseFileName="$(IntDir)\vc80.pdb"
				WarningLevel="3"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
				DisableSpecificWarnings="4005;4311;4312"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Debug|x64"
			OutputDirectory="$(PlatformName)\Output\$(ConfigurationName)"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				EnableIntrinsicFunctions="true"
				PreprocessorDefinitions="_DEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="pch.h"
				ProgramDataBaseFileName="$(OutDir)\vc80.pdb"
				WarningLevel="4"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="DLL-Import Debug|Win32"
			OutputDirectory="$(PlatformName)\DLL_Output\Debug"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				EnableIntrinsicFunctions="true"
				PreprocessorDefinitions="_DEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_IMPORTS"
				RuntimeLibrary="1"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="pch.h"
				ProgramDataBaseFileName="$(OutDir)\vc80.pdb"
				WarningLevel="4"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="DLL-Import Debug|x64"
			OutputDirectory="$(PlatformName)\DLL_Output\Debug"
			IntermediateDirectory="$(PlatformName)\$(ProjectName)\$(ConfigurationName)"
			ConfigurationType="4"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="false"
			CharacterSet="2"
			WholeProgramOptimization="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				EnableIntrinsicFunctions="true"
				PreprocessorDefinitions="_DEBUG;_WINDOWS;USE_PRECOMPILED_HEADERS;WIN32;CRYPTOPP_IMPORTS"
				RuntimeLibrary="1"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="pch.h"
				ProgramDataBaseFileName="$(OutDir)\vc80.pdb"
				WarningLevel="4"
				SuppressStartupBanner="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
				Culture="1033"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
				SuppressStartupBanner="true"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<File
			RelativePath=".\3way.cpp"
			>
		</File>
		<File
			RelativePath=".\3way.h"
			>
		</File>
		<File
			RelativePath=".\adler32.cpp"
			>
		</File>
		<File
			RelativePath=".\adler32.h"
			>
		</File>
		<File
			RelativePath=".\adv-simd.h"
			>
		</File>
		<File
			RelativePath=".\aes.h"
			>
		</File>
		<File
			RelativePath=".\algebra.cpp"
			>
		</File>
		<File
			RelativePath=".\algebra.h"
			>
		</File>
		<File
			RelativePath=".\algparam.cpp"
			>
		</File>
		<File
			RelativePath=".\algparam.h"
			>
		</File>
		<File
			RelativePath=".\arc4.cpp"
			>
		</File>
		<File
			RelativePath=".\arc4.h"
			>
		</File>
		<File
			RelativePath=".\argnames.h"
			>
		</File>
		<File
			RelativePath=".\aria-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\aria.cpp"
			>
		</File>
		<File
			RelativePath=".\aria.h"
			>
		</File>
		<File
			RelativePath=".\ariatab.cpp"
			>
		</File>
		<File
			RelativePath=".\asn.cpp"
			>
		</File>
		<File
			RelativePath=".\asn.h"
			>
		</File>
		<File
			RelativePath=".\authenc.cpp"
			>
		</File>
		<File
			RelativePath=".\authenc.h"
			>
		</File>
		<File
			RelativePath=".\base32.cpp"
			>
		</File>
		<File
			RelativePath=".\base32.h"
			>
		</File>
		<File
			RelativePath=".\base64.cpp"
			>
		</File>
		<File
			RelativePath=".\base64.h"
			>
		</File>
		<File
			RelativePath=".\basecode.cpp"
			>
		</File>
		<File
			RelativePath=".\basecode.h"
			>
		</File>
		<File
			RelativePath=".\bench.h"
			>
		</File>
		<File
			RelativePath=".\bench1.cpp"
			>
		</File>
		<File
			RelativePath=".\bench2.cpp"
			>
		</File>
		<File
			RelativePath=".\bfinit.cpp"
			>
		</File>
		<File
			RelativePath=".\blake2-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\blake2.cpp"
			>
		</File>
		<File
			RelativePath=".\blake2.h"
			>
		</File>
		<File
			RelativePath=".\blowfish.cpp"
			>
		</File>
		<File
			RelativePath=".\blowfish.h"
			>
		</File>
		<File
			RelativePath=".\blumshub.cpp"
			>
		</File>
		<File
			RelativePath=".\blumshub.h"
			>
		</File>
		<File
			RelativePath=".\camellia.cpp"
			>
		</File>
		<File
			RelativePath=".\camellia.h"
			>
		</File>
		<File
			RelativePath=".\cast.cpp"
			>
		</File>
		<File
			RelativePath=".\cast.h"
			>
		</File>
		<File
			RelativePath=".\casts.cpp"
			>
		</File>
		<File
			RelativePath=".\cbcmac.cpp"
			>
		</File>
		<File
			RelativePath=".\cbcmac.h"
			>
		</File>
		<File
			RelativePath=".\ccm.cpp"
			>
		</File>
		<File
			RelativePath=".\ccm.h"
			>
		</File>
		<File
			RelativePath=".\chacha.cpp"
			>
		</File>
		<File
			RelativePath=".\chacha.h"
			>
		</File>
		<File
			RelativePath=".\channels.cpp"
			>
		</File>
		<File
			RelativePath=".\channels.h"
			>
		</File>
		<File
			RelativePath=".\cmac.cpp"
			>
		</File>
		<File
			RelativePath=".\cmac.h"
			>
		</File>
		<File
			RelativePath=".\config.h"
			>
		</File>
		<File
			RelativePath=".\cpu.cpp"
			>
		</File>
		<File
			RelativePath=".\cpu.h"
			>
		</File>
		<File
			RelativePath=".\crc-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\crc.cpp"
			>
		</File>
		<File
			RelativePath=".\crc.h"
			>
		</File>
		<File
			RelativePath=".\cryptlib.cpp"
			>
		</File>
		<File
			RelativePath=".\cryptlib.h"
			>
		</File>
		<File
			RelativePath=".\cryptopp.rc"
			>
		</File>
		<File
			RelativePath=".\datatest.cpp"
			>
		</File>
		<File
			RelativePath=".\default.cpp"
			>
		</File>
		<File
			RelativePath=".\default.h"
			>
		</File>
		<File
			RelativePath=".\des.cpp"
			>
		</File>
		<File
			RelativePath=".\des.h"
			>
		</File>
		<File
			RelativePath=".\dessp.cpp"
			>
		</File>
		<File
			RelativePath=".\dh.cpp"
			>
		</File>
		<File
			RelativePath=".\dh.h"
			>
		</File>
		<File
			RelativePath=".\dh2.cpp"
			>
		</File>
		<File
			RelativePath=".\dh2.h"
			>
		</File>
		<File
			RelativePath=".\dll.cpp"
			>
		</File>
		<File
			RelativePath=".\dll.h"
			>
		</File>
		<File
			RelativePath=".\dlltest.cpp"
			>
		</File>
		<File
			RelativePath=".\dmac.h"
			>
		</File>
		<File
			RelativePath=".\drbg.h"
			>
		</File>
		<File
			RelativePath=".\dsa.cpp"
			>
		</File>
		<File
			RelativePath=".\dsa.h"
			>
		</File>
		<File
			RelativePath=".\eax.cpp"
			>
		</File>
		<File
			RelativePath=".\eax.h"
			>
		</File>
		<File
			RelativePath=".\ec2n.cpp"
			>
		</File>
		<File
			RelativePath=".\ec2n.h"
			>
		</File>
		<File
			RelativePath=".\eccrypto.cpp"
			>
		</File>
		<File
			RelativePath=".\eccrypto.h"
			>
		</File>
		<File
			RelativePath=".\ecp.cpp"
			>
		</File>
		<File
			RelativePath=".\ecp.h"
			>
		</File>
		<File
			RelativePath=".\ecpoint.h"
			>
		</File>
		<File
			RelativePath=".\elgamal.cpp"
			>
		</File>
		<File
			RelativePath=".\elgamal.h"
			>
		</File>
		<File
			RelativePath=".\emsa2.cpp"
			>
		</File>
		<File
			RelativePath=".\emsa2.h"
			>
		</File>
		<File
			RelativePath=".\eprecomp.cpp"
			>
		</File>
		<File
			RelativePath=".\eprecomp.h"
			>
		</File>
		<File
			RelativePath=".\esign.cpp"
			>
		</File>
		<File
			RelativePath=".\esign.h"
			>
		</File>
		<File
			RelativePath=".\factory.h"
			>
		</File>
		<File
			RelativePath=".\fhmqv.h"
			>
		</File>
		<File
			RelativePath=".\files.cpp"
			>
		</File>
		<File
			RelativePath=".\files.h"
			>
		</File>
		<File
			RelativePath=".\filters.cpp"
			>
		</File>
		<File
			RelativePath=".\filters.h"
			>
		</File>
		<File
			RelativePath=".\fips140.cpp"
			>
		</File>
		<File
			RelativePath=".\fips140.h"
			>
		</File>
		<File
			RelativePath=".\fipsalgt.cpp"
			>
		</File>
		<File
			RelativePath=".\fipstest.cpp"
			>
		</File>
		<File
			RelativePath=".\fltrimpl.h"
			>
		</File>
		<File
			RelativePath=".\gcm-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\gcm.cpp"
			>
		</File>
		<File
			RelativePath=".\gcm.h"
			>
		</File>
		<File
			RelativePath=".\gf256.cpp"
			>
		</File>
		<File
			RelativePath=".\gf256.h"
			>
		</File>
		<File
			RelativePath=".\gf2_32.cpp"
			>
		</File>
		<File
			RelativePath=".\gf2_32.h"
			>
		</File>
		<File
			RelativePath=".\gf2n.cpp"
			>
		</File>
		<File
			RelativePath=".\gf2n.h"
			>
		</File>
		<File
			RelativePath=".\gfpcrypt.cpp"
			>
		</File>
		<File
			RelativePath=".\gfpcrypt.h"
			>
		</File>
		<File
			RelativePath=".\gost.cpp"
			>
		</File>
		<File
			RelativePath=".\gost.h"
			>
		</File>
		<File
			RelativePath=".\gzip.cpp"
			>
		</File>
		<File
			RelativePath=".\gzip.h"
			>
		</File>
		<File
			RelativePath=".\hashfwd.h"
			>
		</File>
		<File
			RelativePath=".\hex.cpp"
			>
		</File>
		<File
			RelativePath=".\hex.h"
			>
		</File>
		<File
			RelativePath=".\hkdf.h"
			>
		</File>
		<File
			RelativePath=".\hmac.cpp"
			>
		</File>
		<File
			RelativePath=".\hmac.h"
			>
		</File>
		<File
			RelativePath=".\hmqv.h"
			>
		</File>
		<File
			RelativePath=".\hrtimer.cpp"
			>
		</File>
		<File
			RelativePath=".\hrtimer.h"
			>
		</File>
		<File
			RelativePath=".\ida.cpp"
			>
		</File>
		<File
			RelativePath=".\ida.h"
			>
		</File>
		<File
			RelativePath=".\idea.cpp"
			>
		</File>
		<File
			RelativePath=".\idea.h"
			>
		</File>
		<File
			RelativePath=".\integer.cpp"
			>
		</File>
		<File
			RelativePath=".\integer.h"
			>
		</File>
		<File
			RelativePath=".\iterhash.cpp"
			>
		</File>
		<File
			RelativePath=".\iterhash.h"
			>
		</File>
		<File
			RelativePath=".\kalyna.cpp"
			>
		</File>
		<File
			RelativePath=".\kalyna.h"
			>
		</File>
		<File
			RelativePath=".\kalynatab.cpp"
			>
		</File>
		<File
			RelativePath=".\keccak.cpp"
			>
		</File>
		<File
			RelativePath=".\keccak.h"
			>
		</File>
		<File
			RelativePath=".\lubyrack.h"
			>
		</File>
		<File
			RelativePath=".\luc.cpp"
			>
		</File>
		<File
			RelativePath=".\luc.h"
			>
		</File>
		<File
			RelativePath=".\mars.cpp"
			>
		</File>
		<File
			RelativePath=".\mars.h"
			>
		</File>
		<File
			RelativePath=".\marss.cpp"
			>
		</File>
		<File
			RelativePath=".\md2.cpp"
			>
		</File>
		<File
			RelativePath=".\md2.h"
			>
		</File>
		<File
			RelativePath=".\md4.cpp"
			>
		</File>
		<File
			RelativePath=".\md4.h"
			>
		</File>
		<File
			RelativePath=".\md5.cpp"
			>
		</File>
		<File
			RelativePath=".\md5.h"
			>
		</File>
		<File
			RelativePath=".\mdc.h"
			>
		</File>
		<File
			RelativePath=".\mersenne.h"
			>
		</File>
		<File
			RelativePath=".\misc.cpp"
			>
		</File>
		<File
			RelativePath=".\misc.h"
			>
		</File>
		<File
			RelativePath=".\modarith.h"
			>
		</File>
		<File
			RelativePath=".\modes.cpp"
			>
		</File>
		<File
			RelativePath=".\modes.h"
			>
		</File>
		<File
			RelativePath=".\modexppc.h"
			>
		</File>
		<File
			RelativePath=".\mqueue.cpp"
			>
		</File>
		<File
			RelativePath=".\mqueue.h"
			>
		</File>
		<File
			RelativePath=".\mqv.cpp"
			>
		</File>
		<File
			RelativePath=".\mqv.h"
			>
		</File>
		<File
			RelativePath=".\naclite.h"
			>
		</File>
		<File
			RelativePath=".\nbtheory.cpp"
			>
		</File>
		<File
			RelativePath=".\nbtheory.h"
			>
		</File>
		<File
			RelativePath=".\neon-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\network.cpp"
			>
		</File>
		<File
			RelativePath=".\network.h"
			>
		</File>
		<File
			RelativePath=".\nr.h"
			>
		</File>
		<File
			RelativePath=".\oaep.cpp"
			>
		</File>
		<File
			RelativePath=".\oaep.h"
			>
		</File>
		<File
			RelativePath=".\oids.h"
			>
		</File>
		<File
			RelativePath=".\osrng.cpp"
			>
		</File>
		<File
			RelativePath=".\osrng.h"
			>
		</File>
		<File
			RelativePath=".\ossig.h"
			>
		</File>
		<File
			RelativePath=".\padlkrng.cpp"
			>
		</File>
		<File
			RelativePath=".\padlkrng.h"
			>
		</File>
		<File
			RelativePath=".\panama.cpp"
			>
		</File>
		<File
			RelativePath=".\panama.h"
			>
		</File>
		<File
			RelativePath=".\pch.cpp"
			>
		</File>
		<File
			RelativePath=".\pch.h"
			>
		</File>
		<File
			RelativePath=".\pkcspad.cpp"
			>
		</File>
		<File
			RelativePath=".\pkcspad.h"
			>
		</File>
		<File
			RelativePath=".\poly1305.cpp"
			>
		</File>
		<File
			RelativePath=".\poly1305.h"
			>
		</File>
		<File
			RelativePath=".\polynomi.cpp"
			>
		</File>
		<File
			RelativePath=".\polynomi.h"
			>
		</File>
		<File
			RelativePath=".\ppc-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\ppc-simd.h"
			>
		</File>
		<File
			RelativePath=".\pssr.cpp"
			>
		</File>
		<File
			RelativePath=".\pssr.h"
			>
		</File>
		<File
			RelativePath=".\pubkey.cpp"
			>
		</File>
		<File
			RelativePath=".\pubkey.h"
			>
		</File>
		<File
			RelativePath=".\pwdbased.h"
			>
		</File>
		<File
			RelativePath=".\queue.cpp"
			>
		</File>
		<File
			RelativePath=".\queue.h"
			>
		</File>
		<File
			RelativePath=".\rabin.cpp"
			>
		</File>
		<File
			RelativePath=".\rabin.h"
			>
		</File>
		<File
			RelativePath=".\randpool.cpp"
			>
		</File>
		<File
			RelativePath=".\randpool.h"
			>
		</File>
		<File
			RelativePath=".\rc2.cpp"
			>
		</File>
		<File
			RelativePath=".\rc2.h"
			>
		</File>
		<File
			RelativePath=".\rc5.cpp"
			>
		</File>
		<File
			RelativePath=".\rc5.h"
			>
		</File>
		<File
			RelativePath=".\rc6.cpp"
			>
		</File>
		<File
			RelativePath=".\rc6.h"
			>
		</File>
		<File
			RelativePath=".\rdrand.asm"
			>
		</File>
		<File
			RelativePath=".\rdrand.cpp"
			>
		</File>
		<File
			RelativePath=".\rdrand.h"
			>
		</File>
		<File
			RelativePath=".\rdtables.cpp"
			>
		</File>
		<File
			RelativePath=".\regtest1.cpp"
			>
		</File>
		<File
			RelativePath=".\regtest2.cpp"
			>
		</File>
		<File
			RelativePath=".\regtest3.cpp"
			>
		</File>
		<File
			RelativePath=".\resource.h"
			>
		</File>
		<File
			RelativePath=".\rijndael-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\rijndael.cpp"
			>
		</File>
		<File
			RelativePath=".\rijndael.h"
			>
		</File>
		<File
			RelativePath=".\ripemd.cpp"
			>
		</File>
		<File
			RelativePath=".\ripemd.h"
			>
		</File>
		<File
			RelativePath=".\rng.cpp"
			>
		</File>
		<File
			RelativePath=".\rng.h"
			>
		</File>
		<File
			RelativePath=".\rsa.cpp"
			>
		</File>
		<File
			RelativePath=".\rsa.h"
			>
		</File>
		<File
			RelativePath=".\rw.cpp"
			>
		</File>
		<File
			RelativePath=".\rw.h"
			>
		</File>
		<File
			RelativePath=".\safer.cpp"
			>
		</File>
		<File
			RelativePath=".\safer.h"
			>
		</File>
		<File
			RelativePath=".\salsa.cpp"
			>
		</File>
		<File
			RelativePath=".\salsa.h"
			>
		</File>
		<File
			RelativePath=".\scrypt.cpp"
			>
		</File>
		<File
			RelativePath=".\scrypt.h"
			>
		</File>
		<File
			RelativePath=".\seal.cpp"
			>
		</File>
		<File
			RelativePath=".\seal.h"
			>
		</File>
		<File
			RelativePath=".\secblock.h"
			>
		</File>
		<File
			RelativePath=".\seckey.h"
			>
		</File>
		<File
			RelativePath=".\seed.cpp"
			>
		</File>
		<File
			RelativePath=".\seed.h"
			>
		</File>
		<File
			RelativePath=".\serpent.cpp"
			>
		</File>
		<File
			RelativePath=".\serpent.h"
			>
		</File>
		<File
			RelativePath=".\serpentp.h"
			>
		</File>
		<File
			RelativePath=".\sha-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\sha.cpp"
			>
		</File>
		<File
			RelativePath=".\sha.h"
			>
		</File>
		<File
			RelativePath=".\sha3.cpp"
			>
		</File>
		<File
			RelativePath=".\sha3.h"
			>
		</File>
		<File
			RelativePath=".\shacal2-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\shacal2.cpp"
			>
		</File>
		<File
			RelativePath=".\shacal2.h"
			>
		</File>
		<File
			RelativePath=".\shark.cpp"
			>
		</File>
		<File
			RelativePath=".\shark.h"
			>
		</File>
		<File
			RelativePath=".\sharkbox.cpp"
			>
		</File>
		<File
			RelativePath=".\simon-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\simon.cpp"
			>
		</File>
		<File
			RelativePath=".\simon.h"
			>
		</File>
		<File
			RelativePath=".\simple.cpp"
			>
		</File>
		<File
			RelativePath=".\simple.h"
			>
		</File>
		<File
			RelativePath=".\siphash.h"
			>
		</File>
		<File
			RelativePath=".\skipjack.cpp"
			>
		</File>
		<File
			RelativePath=".\skipjack.h"
			>
		</File>
		<File
			RelativePath=".\sm3.cpp"
			>
		</File>
		<File
			RelativePath=".\sm3.h"
			>
		</File>
		<File
			RelativePath=".\sm4.cpp"
			>
		</File>
		<File
			RelativePath=".\sm4.h"
			>
		</File>
		<File
			RelativePath=".\smartptr.h"
			>
		</File>
		<File
			RelativePath=".\socketft.cpp"
			>
		</File>
		<File
			RelativePath=".\socketft.h"
			>
		</File>
		<File
			RelativePath=".\sosemanuk.cpp"
			>
		</File>
		<File
			RelativePath=".\sosemanuk.h"
			>
		</File>
		<File
			RelativePath=".\speck-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\speck.cpp"
			>
		</File>
		<File
			RelativePath=".\speck.h"
			>
		</File>
		<File
			RelativePath=".\square.cpp"
			>
		</File>
		<File
			RelativePath=".\square.h"
			>
		</File>
		<File
			RelativePath=".\squaretb.cpp"
			>
		</File>
		<File
			RelativePath=".\sse-simd.cpp"
			>
		</File>
		<File
			RelativePath=".\stdcpp.h"
			>
		</File>
		<File
			RelativePath=".\strciphr.cpp"
			>
		</File>
		<File
			RelativePath=".\strciphr.h"
			>
		</File>
		<File
			RelativePath=".\tea.cpp"
			>
		</File>
		<File
			RelativePath=".\tea.h"
			>
		</File>
		<File
			RelativePath=".\test.cpp"
			>
		</File>
		<File
			RelativePath=".\tftables.cpp"
			>
		</File>
		<File
			RelativePath=".\threefish.cpp"
			>
		</File>
		<File
			RelativePath=".\threefish.h"
			>
		</File>
		<File
			RelativePath=".\tiger.cpp"
			>
		</File>
		<File
			RelativePath=".\tiger.h"
			>
		</File>
		<File
			RelativePath=".\tigertab.cpp"
			>
		</File>
		<File
			RelativePath=".\trap.h"
			>
		</File>
		<File
			RelativePath=".\trdlocal.cpp"
			>
		</File>
		<File
			RelativePath=".\trdlocal.h"
			>
		</File>
		<File
			RelativePath=".\trunhash.h"
			>
		</File>
		<File
			RelativePath=".\ttmac.cpp"
			>
		</File>
		<File
			RelativePath=".\ttmac.h"
			>
		</File>
		<File
			RelativePath=".\tweetnacl.cpp"
			>
		</File>
		<File
			RelativePath=".\tweetnacl.h"
			>
		</File>
		<File
			RelativePath=".\twofish.cpp"
			>
		</File>
		<File
			RelativePath=".\twofish.h"
			>
		</File>
		<File
			RelativePath=".\validat0.cpp"
			>
		</File>
		<File
			RelativePath=".\validat1.cpp"
			>
		</File>
		<File
			RelativePath=".\validat2.cpp"
			>
		</File>
		<File
			RelativePath=".\validat3.cpp"
			>
		</File>
		<File
			RelativePath=".\validat4.cpp"
			>
		</File>
		<File
			RelativePath=".\validate.h"
			>
		</File>
		<File
			RelativePath=".\vmac.cpp"
			>
		</File>
		<File
			RelativePath=".\vmac.h"
			>
		</File>
		<File
			RelativePath=".\wait.cpp"
			>
		</File>
		<File
			RelativePath=".\wait.h"
			>
		</File>
		<File
			RelativePath=".\wake.cpp"
			>
		</File>
		<File
			RelativePath=".\wake.h"
			>
		</File>
		<File
			RelativePath=".\whrlpool.cpp"
			>
		</File>
		<File
			RelativePath=".\whrlpool.h"
			>
		</File>
		<File
			RelativePath=".\winpipes.cpp"
			>
		</File>
		<File
			RelativePath=".\winpipes.h"
			>
		</File>
		<File
			RelativePath=".\words.h"
			>
		</File>
		<File
			RelativePath=".\x64dll.asm"
			>
		</File>
		<File
			RelativePath=".\x64masm.asm"
			>
		</File>
		<File
			RelativePath=".\xtr.cpp"
			>
		</File>
		<File
			RelativePath=".\xtr.h"
			>
		</File>
		<File
			RelativePath=".\xtrcrypt.cpp"
			>
		</File>
		<File
			RelativePath=".\xtrcrypt.h"
			>
		</File>
		<File
			RelativePath=".\zdeflate.cpp"
			>
		</File>
		<File
			RelativePath=".\zdeflate.h"
			>
		</File>
		<File
			RelativePath=".\zinflate.cpp"
			>
		</File>
		<File
			RelativePath=".\zinflate.h"
			>
		</File>
		<File
			RelativePath=".\zlib.cpp"
			>
		</File>
		<File
			RelativePath=".\zlib.h"
			>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
