<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{1f4eac20-7b40-40db-a264-4a9256229c5a}</UniqueIdentifier>
      <Extensions>.h;.hpp</Extensions>
    </Filter>
    <Filter Include="Source Code">
      <UniqueIdentifier>{4c6077b5-a2d6-498c-bc42-10af523a06cb}</UniqueIdentifier>
      <Extensions>.cpp</Extensions>
    </Filter>
    <Filter Include="TestData">
      <UniqueIdentifier>{a634d4f4-ddc0-44b4-9c37-d9ffdddc7b06}</UniqueIdentifier>
      <Extensions>.dat</Extensions>
    </Filter>
    <Filter Include="TestVectors">
      <UniqueIdentifier>{2e247f14-f75a-4e15-9804-dccce165306f}</UniqueIdentifier>
      <Extensions>.txt</Extensions>
    </Filter>
    <Filter Include="Miscellaneous">
      <UniqueIdentifier>{5e447502-2b0f-49c8-9df5-56ea9e7a8fbd}</UniqueIdentifier>
      <Extensions>.proto</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="TestVectors\aes.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\all.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\blake2.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\blake2b.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\blake2s.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\aria.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\camellia.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\ccm.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\chacha.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\cmac.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\dlies.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\dsa.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\dsa_1363.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\dsa_rfc6979.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\eax.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\esign.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\gcm.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\hkdf.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\hmac.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\kalyna.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\mars.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\nr.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\panama.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\Readme.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\rsa_oaep.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\rsa_pkcs1_1_5.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\rsa_pss.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\rw.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\salsa.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\seal.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\seed.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\sha.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\sha2.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\sha3.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\shacal2.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\siphash.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\simon.txt">
      <Filter>TestVectors</Filter>
    </None>
    <Text Include="TestVectors\sm3.txt">
      <Filter>TestVectors</Filter>
    </Text>
    <None Include="TestVectors\sm4.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\sosemanuk.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\speck.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\tea.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\threefish.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\ttmac.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\vmac.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\wake.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestVectors\whrlpool.txt">
      <Filter>TestVectors</Filter>
    </None>
    <None Include="TestData\3desval.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\3wayval.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\aria.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\camellia.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\cast128v.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\cast256v.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\descert.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\dh1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\dh2048.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\dlie1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\dlie2048.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\dsa1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\dsa1024b.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\dsa512.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\elgc1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\esig1023.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\esig1536.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\esig2046.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\fhmqv160.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\fhmqv256.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\fhmqv384.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\fhmqv512.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\gostval.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\hmqv160.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\hmqv256.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\hmqv384.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\hmqv512.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\ideaval.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\luc1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\luc2048.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\lucc1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\lucc512.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\lucd1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\lucd512.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\lucs1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\lucs512.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\marsval.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\mqv1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\mqv2048.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\nr1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\nr2048.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rabi1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rabi2048.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rc2val.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rc5val.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rc6val.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rijndael.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rsa1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rsa2048.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rsa400pb.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rsa400pv.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rsa512a.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rw1024.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\rw2048.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\saferval.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\serpentv.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\shacal2v.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\sharkval.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\skipjack.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\squareva.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\twofishv.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\usage.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\xtrdh171.dat">
      <Filter>TestData</Filter>
    </None>
    <None Include="TestData\xtrdh342.dat">
      <Filter>TestData</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="adhoc.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="bench1.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="bench2.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="datatest.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="dlltest.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="regtest1.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="regtest2.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="regtest3.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="test.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="validat0.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="validat1.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="validat2.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="validat3.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="validat4.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
    <ClCompile Include="fipsalgt.cpp">
      <Filter>Source Code</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="bench.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="factory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="validate.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>