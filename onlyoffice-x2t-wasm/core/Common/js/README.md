### Состав .json файла для wasm/asm модуля
- `name` — имя с которым будет собран модуль
- `res_folder` — путь, относительный .json файла, до папки куда будет собран модуль
- `wasm` — требуется ли собрать wasm модуль (*.js и *.wasm)
- `asm` — требуется ли собрать asm модуль (*_ie.js и *.js.mem)
- `run_before` — путь, относительный .json файла, до .py файла который требуется выполнить перед сборкой модуля. Или python in-line код.
- `run_after` — путь, относительный .json файла, до .py файла который требуется выполнить после сборки модуля. Или python in-line код.
- `base_js_content` — путь, относительный .json файла, до .js файла содержащего //module вместо которого будет записан собранный модуль
- `compiler_flags` — массив из флагов и опций компиляции
- `exported_functions` — массив из имён функций, которые будут вызываться из модуля
- `include_path` — массив из путей, относительных .json файла, include-ов для подключаемых файлов
- `define` — массив из дефайнов
- `compile_files_array` — массив из объектов, содержащих:
    - `name` — уникальное, относительно других name в массиве compile_files_array, имя
	- `folder` — путь, относительный .json файла, до папки с подключаемыми файлами
	- `files` — массив из имён подключаемых файлов, расположенных в папке folder (иерархия ../ и ./*/ разрешена)
	- `include_path` — необязательный массив из путей, относительных .json файла, include-ов для подключаемых файлов
	- `define` — необязательный массив из дефайнов
- `sources` — необязательный массив из путей, относительных .json файла, до файлов, которым не требуется прекомпиляция. Например, .a или .o файлы.
